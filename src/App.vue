<!--
 * @Description: 
 * @Author: bruce
 * @Date: 2021-03-17 11:25:29
 * @LastEditTime: 2022-05-30 11:20:35
 * @LastEditors: xuxiang
 * @Reference: 
-->
<template>
  <div id="amf-fe">
    <router-view />
  </div>
</template>

<script>
  import { buttonPermissionMode } from '@/config/setting.config.js';
  import { utils } from '@access/core';
  import { mapGetters } from 'vuex';
  // import { utils } from '@access/core';
  // const { monitor } = utils;
  // monitor.monitor_register({
  //   env: 'dev',
  //   service: 'Access-ms-frame-template',
  // });
  export default {
    name: 'App',
    //用户信息
    computed: {
      ...mapGetters({
        userInfo: 'user/userInfo',
      }),
    },
    watch: {
      $route: {
        handler: function (val, oldVal) {
          if (buttonPermissionMode !== 'none') {
            this.$store.dispatch('button/onGetButtonList', val);
          }
          this.$log(
            // 页面
            'browse',
            {
              business: {},
            },
            val,
          );
          this.$log(
            // 性能
            'performance',
            {
              business: {},
            },
            val,
          );
        },
        deep: true, //true 深度监听
      },
      userInfo(val) {
        if (val) {
          utils.watermark.init({
            watermark_txt: `${
              this.userInfo.realName + ' ' + this.userInfo.jobNumber
            }`, //水印的内容
            watermark_rows: 0, //水印行数
            watermark_cols: 10, //水印列数
            watermark_y_space: 20, //水印y轴间隔
            watermark_alpha: 0.08, //水印透明度
            watermark_fontsize: '0.8', //水印字体大小, 以rem为单位
            watermark_width: 100, //水印宽度
          });
        }
      },
    },
    created() {},
  };
</script>
<style>
  @import './assets/css/main.css';
</style>
