/**
 * @description 登录、获取用户信息、退出登录、清除token逻辑，不建议修改
 */
import Vue from 'vue';
import { getUserInfo, login, logout, socialLogin } from '@/api/mock/user';
import { checkToken } from '@/api/user';
import {
  getAccessToken,
  removeAccessToken,
  setAccessToken,
} from '@/utils/accessToken';
import { resetRouter } from '@/router';
import { isArray, isString } from '@/utils/validate';
import { title, tokenName } from '@/config';
import { ssoPermission, applyPermission } from '@/config/setting.config.js';
import { getCookie, jumpToSSO } from '@/utils/auth.js';
import { MessageBox, Message } from 'element-ui';
import * as Sentry from '@sentry/browser';
import { sentry } from '@/config';

const state = () => ({
  token: getAccessToken(),
  username: '游客',
  avatar: 'http://api.btstu.cn/sjtx/api.php?lx=c1&format=images',
  isLogin: false,
  userInfo: {},
});
const getters = {
  token: state => state.token,
  username: state => state.username,
  avatar: state => state.avatar,
  isLogin: state => state.isLogin,
  userInfo: state => state.userInfo,
};
const mutations = {
  /**
   * @description 设置token
   * @param {*} state
   * @param {*} token
   */
  setToken(state, token) {
    state.token = token;
    setAccessToken(token);
  },
  /**
   * @description 设置用户名
   * @param {*} state
   * @param {*} username
   */
  setUsername(state, username) {
    state.username = username;
  },
  /**
   * @description 设置头像
   * @param {*} state
   * @param {*} avatar
   */
  setAvatar(state, avatar) {
    state.avatar =
      avatar ||
      'https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1605072748151&di=3f67462bdd725270b411832bf2414874&imgtype=0&src=http%3A%2F%2Fqiniuimg.qingmang.mobi%2Fimage%2Forion%2F78c9cd9a4b9414aaedbb5a6ea7081641_1000_1000.gif';
  },
  setLogin(state, isLogin) {
    state.isLogin = isLogin;
  },
  setUserInfo(state, userInfo) {
    state.userInfo = userInfo;
    // 处理SentryScope
    if (sentry.enabled && process.env.NODE_ENV === 'production') {
      // build模式启用
      const env = process.env.VUE_APP_BUILD_MODE;
      if (
        (env === 'test' && sentry.enabledTest) ||
        (env === 'pre' && sentry.enabledPre) ||
        env === 'pro'
      ) {
        try {
          Sentry.configureScope(scope => {
            scope.setUser({
              id: userInfo?.id,
              username: userInfo?.nickname,
              jobNumber: userInfo?.jobNumber,
              email: userInfo?.email,
              phoneNum: userInfo?.phoneNum,
            });
          });
        } catch (e) {
          console.log('sentry: init sentry user info error');
        }
      }
    }
  },
};
const actions = {
  /**
   * @description 登录拦截放行时，设置虚拟角色
   * @param {*} { commit, dispatch }
   */
  setVirtualRoles({ commit, dispatch }) {
    dispatch('acl/setFull', true, { root: true });
    commit('setAvatar', 'http://api.btstu.cn/sjtx/api.php?lx=c1&format=images');
    commit('setUsername', 'admin(未开启登录拦截)');
  },
  /**
   * @description 登录
   * @param {*} { commit }
   * @param {*} userInfo
   */
  async login({ commit }, userInfo) {
    const { data } = await login(userInfo);
    const token = data[tokenName];
    // const token = 'token'
    if (token) {
      commit('setToken', token);
      const hour = new Date().getHours();
      const thisTime =
        hour < 8
          ? '早上好'
          : hour <= 11
          ? '上午好'
          : hour <= 13
          ? '中午好'
          : hour < 18
          ? '下午好'
          : '晚上好';
      Vue.prototype.$baseNotify(`欢迎登录${title}`, `${thisTime}！`);
    } else {
      Vue.prototype.$baseMessage(
        `登录接口异常，未正确返回${tokenName}...`,
        'error',
      );
      return Promise.reject();
    }
  },
  /**
   * @description 第三方登录
   * @param {*} {}
   * @param {*} tokenData
   */
  async socialLogin({}, tokenData) {
    const { data } = await socialLogin(tokenData);
    const token = data[tokenName];
    if (token) {
      const hour = new Date().getHours();
      const thisTime =
        hour < 8
          ? '早上好'
          : hour <= 11
          ? '上午好'
          : hour <= 13
          ? '中午好'
          : hour < 18
          ? '下午好'
          : '晚上好';
      Vue.prototype.$baseNotify(`欢迎登录${title}`, `${thisTime}！`);
    } else {
      Vue.prototype.$baseMessage(
        `login核心接口异常，请检查返回JSON格式是否正确，是否正确返回${tokenName}...`,
        'error',
      );
      return Promise.reject();
    }
  },
  /**
   * @description 获取用户信息接口 这个接口非常非常重要，如果没有明确底层前逻辑禁止修改此方法，错误的修改可能造成整个框架无法正常使用
   * @param {*} { commit, dispatch, state }
   * @returns
   */
  async getUserInfo({ commit, dispatch }) {
    // const { data } = await getUserInfo();

    // const { username, avatar, roles, ability } = data
    /** 以下为假数据，根据实际情况获取用户数据 **/
    const { username, avatar, roles, ability } = {
      roles: ['admin'],
      ability: ['READ', 'WRITE', 'DELETE'],
      username: 'admin',
    };
    /**
     * 检验返回数据是否正常，无对应参数，将使用默认用户名,头像,Roles和Ability
     * username {String}
     * avatar {String}
     * roles {List}
     * ability {List}
     */
    if (
      (username && !isString(username)) ||
      (avatar && !isString(avatar)) ||
      (roles && !isArray(roles)) ||
      (ability && !isArray(ability))
    ) {
      Vue.prototype.$baseMessage(
        'getUserInfo核心接口异常，请检查返回JSON格式是否正确',
        'error',
      );
      return Promise.reject();
    }

    // 如不使用username用户名,可删除以下代码
    if (username) commit('setUsername', username);
    // 如不使用avatar头像,可删除以下代码
    if (avatar) commit('setAvatar', avatar);
    // 如不使用roles权限控制,可删除以下代码
    if (roles) dispatch('acl/setRole', roles, { root: true });
    // 如不使用ability权限控制,可删除以下代码
    if (ability) dispatch('acl/setAbility', ability, { root: true });

    commit('setLogin', true);
  },

  /**
   * @description 退出登录
   * @param {*} { dispatch }
   */
  async logout({ dispatch }) {
    await logout();
    await dispatch('resetAll');
  },
  /**
   * @description 重置token、roles、ability、router、tabsBar等
   * @param {*} { commit, dispatch }
   */
  async resetAll({ commit, dispatch }) {
    commit('setUsername', '游客');
    commit('setAvatar', 'http://api.btstu.cn/sjtx/api.php?lx=c1&format=images');
    commit('setLogin', false);
    await dispatch('setToken', '');
    await dispatch('acl/setFull', false, { root: true });
    await dispatch('acl/setRole', [], { root: true });
    await dispatch('acl/setAbility', [], { root: true });
    await dispatch('tabs/delAllVisitedRoutes', [], { root: true });
    await resetRouter();
    removeAccessToken();
  },
  /**
   * @description 设置token
   * @param {*} { commit }
   * @param {*} token
   */
  setToken({ commit }, token) {
    commit('setToken', token);
  },
  /**
   * @description 设置头像
   * @param {*} { commit }
   * @param {*} avatar
   */
  setAvatar({ commit }, avatar) {
    commit('setAvatar', avatar);
  },
  /**
   * @description 设置用户信息
   * @param {*} { commit }
   * @param {*} avatar
   */
  setUserInfo({ commit }, userInfo) {
    commit('setUserInfo', userInfo);
    commit('setAvatar', userInfo.avatar);
    commit('setUsername', userInfo.realName);
  },
  /**
   * @description 校验token有效性
   * @param {*} { commit }
   */
  async checkToken({ commit, rootState }, userInfo) {
    let _token = getCookie();
    // console.log(rootState.user, 'ssoPermission');
    if (ssoPermission) {
      let params = {
        token: _token,
      };
      const { res } = await Vue.prototype.$req.declaredApi.checkUserToken(
        params,
      );
      // console.log(this.getters, '_token');
      if (res && res.claims && res.claims.id) {
        let userInfo = res.claims;
        commit('setUserInfo', userInfo);
        commit('setAvatar', userInfo.avatar);
        commit('setUsername', userInfo.realName);
        commit('setLogin', true);
        return res;
      } else {
        jumpToSSO();
      }
    } else {
      return { claims: { id: 1 } };
    }
  },

  /**
   * @description 查询有无应用权限
   * @param {*} { commit }
   */
  async getAppAccess({ commit, rootState }, userInfo) {
    if (applyPermission) {
      let data = {
        userId: rootState.user.userInfo.id,
        appCode: process.env.VUE_APP_LOGIN_APP_CODE,
      };
      const { res, err } = await Vue.prototype.$req.declaredApi.getAppAccess(
        data,
      );
      if (res) {
        return res;
      } else {
        MessageBox.alert(`当前应用没有权限,请申请`, '提醒', {
          confirmButtonText: '确定',
          callback: () => {
            window.open(
              `//${window.ACCESS_HOSTS.ssoHost}/#/system/permissionApply?appCode=${process.env.VUE_APP_LOGIN_APP_CODE}`,
              '_blank',
            );
          },
        });
      }
    } else {
      return true;
    }
  },
};
export default { state, getters, mutations, actions };
