/*
 * @Description:
 * @Author: 刘攀
 * @Date: 2021-07-08 14:05:32
 * @LastEditTime: 2022-11-18 16:30:56
 * @LastEditors: dddd
 * @Reference:
 */
import {
  getListChannels,
  getListStore,
  getMapSaleCompanyMain,
  getStatusList,
  getListDept,
  getUserLevelList,
  queryVirtualWarehouseCoverageCountry,
} from '@/api/invoiceCenter';

const state = {
  channelList: [],
  storeList: [],
  allStoreList: [], // 全部店铺数据
  statusList: [],
  mapSaleList: [],
  deptList: [],
  userLevelList: [],
  nationList: [], // 国家列表
  warehouseCode: '',
};

const getters = {
  channelList: state => state.channelList,
  storeList: state => state.storeList,
  allStoreList: state => state.allStoreList,
  statusList: state => state.statusList,
  mapSaleList: state => state.mapSaleList,
  deptList: state => state.deptList,
  userLevelList: state => state.userLevelList,
  nationList: state => state.nationList,
  warehouseCode: state => state.warehouseCode,
};

const mutations = {
  setChannelList(state, channelList) {
    state.channelList = channelList;
  },
  setStoreList(state, storeList) {
    state.storeList = storeList;
  },
  setAllStoreList(state, allStoreList) {
    state.allStoreList = allStoreList;
  },
  setStatusList(state, statusList) {
    state.statusList = statusList;
  },
  setMapSaleList(state, mapSaleList) {
    state.mapSaleList = mapSaleList;
  },
  setDeptList(state, deptList) {
    state.deptList = deptList;
  },
  setUserLevelList(state, userLevelList) {
    state.userLevelList = userLevelList;
  },
  setNationList(state, nationList) {
    state.nationList = nationList;
  },
  setWarehouseCode(state, warehouseCode) {
    state.warehouseCode = warehouseCode;
  },

  resetData(state) {
    state.nationList = [];
    state.storeList = [];
  },
  resetNationListData(state) {
    state.nationList = [];
  },
};

const actions = {
  async setChannelList({ commit, state }) {
    const channel = await getListChannels();
    const status = await getStatusList();
    const store = await getListStore({ channelCode: '' });
    const sale = await getMapSaleCompanyMain();
    const dept = await getListDept();
    const lever = await getUserLevelList();
    commit('setChannelList', channel);
    commit('setAllStoreList', store);
    commit('setStatusList', status);
    commit('setMapSaleList', sale);
    commit('setDeptList', dept);
    commit('setUserLevelList', lever);
  },
  async setNationList({ commit, state }, info) {
    const list = await queryVirtualWarehouseCoverageCountry(info);
    let arr = [];
    let code = '';
    if (Array.isArray(list)) {
      list.forEach(item => {
        // if (item.warehouseCode === info['virtualWarehouseCodeList'][0]) {
        //   arr = item.baseAddressDTOList.map(item => {
        //     return {
        //       ...item,
        //       id: Number(item.id) ? Number(item.id) : item.id,
        //     };
        //   });
        //   code = item.warehouseCode;
        // }
        const itemList = item.baseAddressDTOList.map(item => {
          return {
            ...item,
            id: Number(item.id) ? Number(item.id) : item.id,
          };
        });
        arr.push(itemList);
        // code = item.warehouseCode;
      });
    }

    commit('setNationList', arr.flat(), code);
    commit('setWarehouseCode', code);
  },
  async setStoreList({ commit, state }, info) {
    const store = await getListStore(info);
    commit('setStoreList', store);
  },
  async setAllStoreList({ commit, state }, info) {
    const store = await getListStore(info);
    commit('setAllStoreList', store);
  },

  resetData({ commit, state }, info) {
    commit('resetData');
  },
  resetNationListData({ commit, state }, info) {
    commit('resetNationListData');
  },
};

export default {
  state,
  getters,
  mutations,
  actions,
};
