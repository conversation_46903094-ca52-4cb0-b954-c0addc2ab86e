/**
 * @description 路由拦截状态管理，目前两种模式：all模式与intelligence模式，其中partialRoutes是菜单暂未使用
 */
import Vue from 'vue';
import { asyncRoutes, constantRoutes, resetRouter } from '@/router';
import { getRouterList } from '@/api/user';
import { getRouterList1 } from '@/api/mock/router';
import { convertRouter, filterRoutes, convertRouter1 } from '@/utils/routes';
import { authentication, rolesControl } from '@/config';
import { isArray } from '@/utils/validate';

const state = () => ({
  routes: [],
  cachedRoutes: [],
  currentRouteInfo: {}, //含有menuId
});
const getters = {
  routes: state => state.routes,
  cachedRoutes: state => state.cachedRoutes,
  currentRouteInfo: state => state.currentRouteInfo,
};
const mutations = {
  /**
   * @description 多模式设置路由
   * @param {*} state
   * @param {*} routes
   */
  setRoutes(state, routes) {
    state.routes = routes;
  },
  /**
   * @description 设置缓存Name数组
   * @param {*} state
   * @param {*} routes
   */
  setCachedRoutes(state, routes) {
    state.cachedRoutes = routes;
  },
  /**
   * @description 修改Meta
   * @param {*} state
   * @param options
   */
  changeMenuMeta(state, options) {
    function handleRoutes(routes) {
      return routes.map(route => {
        if (route.name === options.name)
          Object.assign(route.meta, options.meta);
        if (route.children && route.children.length)
          route.children = handleRoutes(route.children);
        return route;
      });
    }

    state.routes = handleRoutes(state.routes);
  },
  setCurrentRouteInfo(state, currentRouteInfo) {
    state.currentRouteInfo = currentRouteInfo;
  },
};
const actions = {
  /**
   * @description 多模式设置路由
   * @param {*} { commit }
   * @param mode
   * @returns
   */
  async setRoutes({ commit }, mode = 'none') {
    // 默认前端路由
    let routes = [...asyncRoutes];
    // 设置游客路由关闭路由拦截(不需要可以删除)
    const control = mode === 'visit' ? false : rolesControl;
    // 设置后端路由(不需要可以删除)
    if (authentication === 'all') {
      const res = await getRouterList();
      const data = res;
      // if (!isArray(data))
      // Vue.prototype.$baseMessage(
      //   '路由格式返回有误！',
      //   'error',
      //   false,
      //   'amf-hey-message-error',
      // );
      routes = convertRouter(data);
      if (routes[routes.length - 1]?.path !== '*')
        routes.push({ path: '*', redirect: '/404', hidden: true });
    }
    // 根据权限和rolesControl过滤路由
    const finallyRoutes = filterRoutes([...constantRoutes, ...routes], control);
    // 设置菜单所需路由
    commit('setRoutes', finallyRoutes);
    // 根据可访问路由重置Vue Router
    await resetRouter(finallyRoutes);
  },
  /**
   * @description 多模式设置路由
   * @param {*} { commit }
   * @param mode
   * @returns
   */
  async setRoutesForTest({ commit }, mode = 'none') {
    // 默认前端路由
    let routes = [...asyncRoutes];
    // 设置游客路由关闭路由拦截(不需要可以删除)
    const control = mode === 'visit' ? false : rolesControl;
    // 设置后端路由(不需要可以删除)
    if (authentication === 'all') {
      const { data } = await getRouterList1();
      if (!isArray(data))
        if (data[data.length - 1].path !== '*')
          // Vue.prototype.$baseMessage(
          //   '路由格式返回有误！',
          //   'error',
          //   false,
          //   'amf-hey-message-error',
          // );
          data.push({ path: '*', redirect: '/404', hidden: true });
      routes = convertRouter1(data);
    }
    // 根据权限和rolesControl过滤路由
    const finallyRoutes = filterRoutes([...constantRoutes, ...routes], control);
    // 设置菜单所需路由
    commit('setRoutes', finallyRoutes);
    // 根据可访问路由重置Vue Router
    await resetRouter(finallyRoutes);
  },

  /**
   * @description 设置缓存Name数组
   * @param {*} { commit }
   * @param {*} routes
   */
  setCachedRoutes({ commit }, routes) {
    commit('setCachedRoutes', routes);
  },
  /**
   * @description 修改Route Meta
   * @param {*} { commit }
   * @param options
   */
  changeMenuMeta({ commit }, options = {}) {
    commit('changeMenuMeta', options);
  },
  setCurrentRouteInfo({ commit }, route) {
    commit('setCurrentRouteInfo', route);
  },
};
export default { state, getters, mutations, actions };
