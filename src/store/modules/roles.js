/*
 * @Description:
 * @Author: bruce
 * @Date: 2021-03-19 10:44:06
 * @LastEditTime: 2021-04-14 14:52:08
 * @LastEditors: bruce
 * @Reference:
 */
import Vue from 'vue';

const state = () => ({
  rolesList: [],
});

const getters = {
  rolesList: state => state.rolesList,
};

const mutations = {
  setRolesList(state, rolesList) {
    state.rolesList = rolesList;
  },
};

const actions = {
  async setRolesList({ commit, rootState }) {
    let params = {
      userId: rootState.user?.userInfo?.id,
    };
    const res = {};
    // const { res } = await Vue.prototype.$req.declaredApi.getUserRolesList(
    //   params,
    // );
    commit('setRolesList', res);
  },
};

export default { state, getters, mutations, actions };
