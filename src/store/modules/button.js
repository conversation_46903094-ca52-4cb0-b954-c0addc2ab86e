/*
 * @Description: 按钮权限分为隐藏与审批
 * @Author: bruce
 * @Date: 2021-03-08 18:31:22
 * @LastEditTime: 2022-11-18 16:30:52
 * @LastEditors: dddd
 * @Reference:
 */
import { Message, MessageBox } from 'element-ui';
import Vue from 'vue';
import { getObjByPropty } from '@/utils/index.js';
import { buttonPermissionMode } from '@/config/setting.config.js';
import { getPermissionBtnList } from '@/api/customize.js';

const state = { buttonList: [], permissionButtonList: [] };
const getters = {
  buttonList: state => state.buttonList,
  permissionButtonList: state => state.permissionButtonList,
};
const mutations = {
  setButtonList(state, buttonList) {
    state.buttonList = buttonList;
  },
  setPermissionButtonList(state, permissionButtonList) {
    state.permissionButtonList = permissionButtonList;
  },
};
const actions = {
  /**
   * @description: 查询按钮权限列表,当前菜单下
   * @param  {*}
   * @return {*}
   * @param {*} commit
   * @param {*} rootState
   * @param {*} buttonParams
   */
  async setButtonList({ commit, rootState }, buttonParams) {
    let params = {
      menuId: buttonParams.menuId,
      userId: rootState.user.userInfo.id,
      appCode: process.env.VUE_APP_LOGIN_APP_CODE,
    };
    const { res } = await Vue.prototype.$req.declaredApi.getBtnList(params);
    if (res) {
      commit('setButtonList', res);
    }
  },

  /**
   * @description: 查询当前按钮有无权限
   * @param  {*}
   * @return {*}
   * @param {*} commit
   * @param {*} rootState
   * @param {*} buttonParams
   */
  async onGetButtonPermission({ commit, rootState }, buttonParams) {
    let { buttonCode } = buttonParams;
    let buttonFilterArr = rootState.button.buttonList.filter(
      item => item['buttonCode'] === buttonCode,
    );
    console.log(
      rootState.button.buttonList,
      buttonFilterArr,
      'buttonFilterArr',
    );
    if (!buttonFilterArr.length) {
      // Message({
      //   type: 'error',
      //   message: '网络异常,请刷新浏览器',
      // });
      // return;
      // 没有配置按钮权限的还可以使用
      return Promise.resolve(true);
    }
    let params = {
      permissionId: buttonFilterArr[0].id, //权限id
      userId: rootState.user.userInfo.id,
      appCode: process.env.VUE_APP_LOGIN_APP_CODE,
    };
    const { res, err } = await Vue.prototype.$req.declaredApi.permissionAccess(
      params,
    );
    if (res) {
      return res;
    } else {
      // 权限申请页
      MessageBox.confirm('当前按钮没有权限,请申请在使用', '提醒', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          let url =
            `//${window.ACCESS_HOSTS.ssoHost}/#/system/permissionApply` +
            `?id=${buttonFilterArr[0].id}&appCode=${process.env.VUE_APP_LOGIN_APP_CODE}&title=${buttonFilterArr[0].title}&parentName=${rootState.routes?.currentRouteInfo?.meta?.title}`;
          window.open(url, '_blank');
        })
        .catch(() => {});
    }
  },

  /**
   * @description: 查询当前菜单下的权限按钮
   * @param  {*}
   * @return {*}
   * @param {*} commit
   * @param {*} rootState
   * @param {*} buttonParams
   */
  async setPermissionButtonList({ commit, rootState }, buttonParams) {
    let params = {
      menuId: buttonParams.menuId,
      userId: rootState.user.userInfo.id,
      appCode: process.env.VUE_APP_LOGIN_APP_CODE,
    };
    const { res } = await Vue.prototype.$req.declaredApi.getPermissionBtnList(
      params,
    );
    if (res) {
      commit('setPermissionButtonList', res);
    }
  },

  onGetButtonList({ commit, dispatch, rootState }, route) {
    let currentRouteInfo = getObjByPropty(
      rootState.routes.routes,
      route.path,
      'path',
    );
    if (currentRouteInfo?.menuId) {
      if (buttonPermissionMode === 'approve') {
        this.dispatch('button/setButtonList', {
          menuId: currentRouteInfo.menuId,
        });
      } else if (buttonPermissionMode === 'display') {
        this.dispatch('button/setPermissionButtonList', {
          menuId: currentRouteInfo.menuId,
        });
      }
    }

    this.dispatch('routes/setCurrentRouteInfo', {
      ...currentRouteInfo,
      ...route,
    });
  },
};

export default { state, getters, mutations, actions };
