const state = () => ({
  ledgerOrderMgtParams: {},
});

const getters = {
  ledgerOrderMgtParams: state => state.ledgerOrderMgtParams,
};

const mutations = {
  setLedgerOrderMgtParams: (state, info) => {
    state.ledgerOrderMgtParams = info;
  },
};

const actions = {
  setLedgerOrderMgtParams: ({ commit }, info) => {
    commit('setLedgerOrderMgtParams', info);
  },
};

export default {
  state,
  getters,
  mutations,
  actions,
};
