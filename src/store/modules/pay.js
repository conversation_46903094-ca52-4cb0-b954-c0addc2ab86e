import { getParamDictionaries } from '@/api/liquidation';

const constant = {
  SET_PAY_CHANNEL_LIST: 'SET_PAY_CHANNEL_LIST',
};

const state = {
  payChannelList: [],
};

const getters = {
  payChannelList: state => state.payChannelList,
};

const mutations = {
  [constant.SET_PAY_CHANNEL_LIST]: (state, list) => {
    state.payChannelList = list;
  },
};

const actions = {
  async getPayChannelList({ commit, state }) {
    if (state.payChannelList.length === 0) {
      // 获取数据
      const { err, res } = await getParamDictionaries(3);
      if (!err) {
        commit(constant.SET_PAY_CHANNEL_LIST, res);
        return res;
      }
      return [];
    }
    return state.payChannelList;
  },
};

export default {
  state,
  getters,
  mutations,
  actions,
};
