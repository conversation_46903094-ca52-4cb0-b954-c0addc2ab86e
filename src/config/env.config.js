/*
 * <AUTHOR> 任洪建
 * @Date         : 2021-03-11 18:17:29
 * @LastEditTime : 2021-05-07 15:42:46
 * @LastEditors  : 任洪建
 * @FilePath     : /frontend-framework-template-next/src/config/env.config.js
 * @Description  : 本文件中的变量会根据环境不同注入到progress.env.xxx中,基于progress.env.BUILD_MODE 变量进行匹配
 */
module.exports = {
  dev: {
    VUE_APP_LOGIN_APP_CODE: 'APP_YRVOYR',
    VUE_APP_BUILD_MODE: 'dev',
  },
  test: {
    VUE_APP_LOGIN_APP_CODE: 'APP_NIMYQL',
    VUE_APP_BUILD_MODE: 'test',
  },
  pre: {
    VUE_APP_LOGIN_APP_CODE: 'APP_HEPEUB',
    VUE_APP_BUILD_MODE: 'pre',
  },
  pro: {
    VUE_APP_LOGIN_APP_CODE: 'APP_HEPEUB',
    VUE_APP_BUILD_MODE: 'pro',
  },
};
