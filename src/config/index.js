/*
 * @Description:
 * @Author: bruce
 * @Date: 2021-03-22 14:11:43
 * @LastEditTime : 2021-04-01 16:25:04
 * @LastEditors  : 任洪建
 * @Reference:
 */
/**
 * @description 4个子配置，vue/cli配置|通用配置|主题配置|网络配置导出
 */
const cli = require('./cli.config');
const setting = require('./setting.config');
const theme = require('./theme.config');
const network = require('./net.config');
const env = require('./env.config');
const cdn = require('./cdn.config');
const packages = require('../../package.json');
const sentry = require('./sentry.config');
module.exports = {
  version: packages.version,
  ...cli,
  ...setting,
  ...theme,
  ...network,
  ...cdn,
  sentry,
  env: env, // 别动
};
