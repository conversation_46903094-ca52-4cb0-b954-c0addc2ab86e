const packages = require('../../package.json');

module.exports = {
  enabledCDN: true, // 开关
  cdnConfig: {
    oss: {
      enabled: false,
      refreshCDN: false, // 上传链接是否刷CDN
      domain: 'frontend.danchuangglobal.com', // 加速域名 当refreshCDN为true时必填
    },
    obs: {
      enabled: true,
      refreshCDN: false, // 上传链接是否刷CDN
      domain: 'frontend.danchuangglobal.com', // 加速域名 当refreshCDN为true时必填
    },
    retry: 3, // 重试次数: number(>=0)
    existCheck: false, // true: 直接上传、false: 先检测,若已存在则不重新上传(不报错)

    baseDir: 'frontend', // 一级目录
    project: packages.name, // 项目名 即二级目录
    env: process.env.BUILD_MODE, // 当前构建环境  即三级目录
    version: packages.version, // 版本号 最终prefix为 `${baseDir}/${project}/${env}/${version}`

    prefix: `frontend/${packages.name}/${process.env.BUILD_MODE}/${packages.version}`,

    exclude: /.*\.(html|map)$/, // 跳过map & html
    enableLog: true,
    ignoreError: false,
    removeMode: true, // 上传完毕是否删除源文件 建议开启减小容器大小
    enabledEnv: {
      // 是否开启对应环境的CDN
      test: true,
      dev: true,
      pre: true,
    },
  },
  cdnPublicPath: 'https://w2.danchuangglobal.com/',
};
