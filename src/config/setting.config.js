/*
 * @Description:
 * @Author: bruce
 * @Date: 2021-03-17 11:25:29
 * @LastEditTime: 2025-04-15 18:38:24
 * @LastEditors: 王赛军
 * @Reference:
 */
/**
 * @description 导出通用配置
 */
module.exports = {
  // 标题，此项修改后需要重启项目 (包括初次加载雪花屏的标题 页面的标题 浏览器的标题)
  title: '财务管理系统',
  // 标题分隔符
  titleSeparator: ' - ',
  // 标题是否反转
  // 如果为false: "page - title"
  // 如果为ture : "title - page"
  titleReverse: false,
  // 简写
  abbreviation: 'access-fmis-web',
  // copyright
  copyright: 'Access Frontend Team',
  // 缓存路由的最大数量
  keepAliveMaxNum: 20,
  // 路由模式，可选值为 history 或 hash
  routerMode: 'hash',
  // 不经过token校验的路由，白名单路由建议配置到与login页面同级，如果需要放行带传参的页面，请使用query传参，配置时只配置path即可
  routesWhiteList: ['/login', '/register', '/callback', '/404', '/403', '/401'],
  // 加载时显示文字
  loadingText: '正在加载中...',
  // token名称
  tokenName: 'token',
  // token在localStorage、sessionStorage、cookie存储的key的名称
  tokenTableName: 'Admin-Token',
  // token存储位置localStorage sessionStorage cookie
  storage: 'cookie',
  // token失效回退到登录页时是否记录本次的路由
  recordRoute: true,
  // 是否开启logo，不显示时设置false，请填写src/icon路径下的图标名称
  // 如需使用内置RemixIcon图标，请自行去logo组件切换注释代码(内置svg雪碧图较大，对性能有一定影响)
  logo: 'vuejs-fill',
  // 语言类型zh、en
  i18n: 'zh',
  // 消息框消失时间
  messageDuration: 3000,
  // 在哪些环境下显示高亮错误
  errorLog: ['development' /* , 'production' */],
  // 是否开启登录拦截
  loginInterception: true,
  // 是否开启登录RSA加密
  loginRSA: false,
  // 是否需要开启sso单点认证,默认不开启
  ssoPermission: true,
  // 是否需要开启应用权限认证,默认开启
  applyPermission: false,
  // intelligence(前端导出路由)和all(后端导出路由)两种方式
  authentication: 'all',
  //是否支持按钮权限,display-显示与隐藏；approve-审批；none-不走按钮权限
  buttonPermissionMode: 'approve',
  // 是否支持游客模式，支持情况下，访问白名单，可查看所有asyncRoutes
  supportVisit: false,
  // 是否开启roles字段进行角色权限控制(如果是all模式后端完全处理角色并进行json组装，可设置false不处理路由中的roles字段)
  rolesControl: true,
  // vertical column comprehensive common布局时是否只保持一个子菜单的展开
  uniqueOpened: false,
  // vertical column comprehensive common布局时默认展开的菜单path，使用逗号隔开建议只展开一个
  defaultOpeneds: ['/amf'],
  // 需要加loading层的请求，防止重复提交
  debounce: ['doEdit'],
  // 分栏布局和综合布局时，是否点击一级菜单默认开启第一个二级菜单
  openFirstMenu: true,
  // 代码生成机生成在view下的文件夹名称
  templateFolder: 'project',
  //无角色code
  noRoleCode: 401,
  //配后端数据的接收方式application/json;charset=UTF-8或者application/x-www-form-urlencoded;charset=UTF-8
  contentType: 'application/json;charset=UTF-8',
};
