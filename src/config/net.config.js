/*
 * @Description:
 * @Author: bruce
 * @Date: 2021-03-22 14:11:43
 * @LastEditTime: 2021-04-08 16:57:26
 * @LastEditors: bruce
 * @Reference:
 */
/**
 * @description 导出网络配置
 **/
module.exports = {
  // 默认的接口地址，开发环境和生产环境走/mock-server
  // 当然你也可以选择自己配置成需要的接口地址，如"https://api.xxx.com"
  // 问号后边代表开发环境，冒号后边代表生产环境
  baseURL: '/',
  // 配后端数据的接收方式application/json;charset=UTF-8 或 application/x-www-form-urlencoded;charset=UTF-8
  contentType: 'application/json;charset=UTF-8',
  // 最长请求时间
  requestTimeout: 180000,
  // 操作正常code，支持String、Array、int多种类型
  successCode: [200, 0, '200', '0'],
  // 数据状态的字段名称
  statusName: 'code',
  // 状态信息的字段名称
  messageName: 'msg',
  // API请求key，对于应用来说为域名前缩写，例如gams.danchuangglobal.com 即为 'gams'
  apiKey: 'fmis',
  // 为了兼容本地,应用域名如.acg.team
  replaceDomain: '',
};
