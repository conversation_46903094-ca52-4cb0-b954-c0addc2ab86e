/*
 * @Description:
 * @Author: 项萍～
 * @Date: 2021-07-01 10:33:55
 * @LastEditTime: 2021-08-16 10:08:18
 * @LastEditors: 项萍～
 * @Reference:
 */
const chalk = require('chalk'); // eslint-disable-line
const msgPath = process.env.GIT_PARAMS;
const msg = require('fs').readFileSync(msgPath, 'utf-8').trim();

const commitRE = /^(v\d+\.\d+\.\d+(-(alpha|beta|rc.\d+))?)|((revert: )?(feat|fix|docs|style|refactor|perf|test|workflow|ci|chore|types)(\(.+\))?!?: .{1,50})/;

if (!commitRE.test(msg)) {
  console.error(
    `  ${chalk.bgRed.white(' 错了错了： ')} ${chalk.red(
      `commit message 格式错了`,
    )}\n\n` +
      chalk.red(`  这时候你得用正确的格式编写提交信息. 例如:\n\n`) +
      `    ${chalk.green(`feat(compiler): 增加了 'comments' 参数`)}\n` +
      `    ${chalk.green(`fix(v-model): 调整blur事件的处理 (close #28)`)}\n\n` +
      chalk.red(
        `详细规范看下 https://github.com/vuejs/vue/blob/dev/.github/COMMIT_CONVENTION.md.\n`,
      ) +
      chalk.red(
        `其实用 ${chalk.cyan(
          `npm run commit`,
        )} 生成一下提交信息也不是不行吧.\n`,
      ),
  );
  process.exit(1);
}
