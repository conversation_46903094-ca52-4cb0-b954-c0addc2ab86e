/*
 * <AUTHOR> 任洪建
 * @Date         : 2021-04-01 16:12:33
 * @LastEditTime : 2021-05-14 19:18:46
 * @LastEditors  : 任洪建
 * @FilePath     : /frontend-framework-template-next/src/config/sentry.config.js
 * @Description  :
 */
const packages = require('../../package.json');

module.exports = {
  enabled: true,
  // 在https://sentry-frontend.acg.team后台创建项目，并修改为自己的DSN,例如 ：'https://<EMAIL>/4'
  DSN: 'https://<EMAIL>/11',
  uploadURL: 'https://sentry-frontend.acg.team', // 正常不需要改动
  sourceMap: true, // 是否上传sourceMap
  enabledPre: false, // 预发环境是否开启上报
  enabledTest: false, // 测试环境是否开启上报
  name: packages.name, // sentry 后台项目名，默认读取package.name字段，若不一致修改该字段
  urlPrefix: '~/static/js',
  include: './dist/static/js',
};
