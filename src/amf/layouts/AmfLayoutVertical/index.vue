<template>
  <!-- 纵向布局 -->
  <div
    :class="{
      fixed: fixedHeader,
      'no-tabs-bar': !showTabs,
    }"
    class="amf-layout-vertical"
  >
    <amf-side-bar />
    <div
      v-if="device === 'mobile' && !collapse"
      class="v-modal"
      @click="handleFoldSideBar"
    />
    <div
      :class="{
        'is-collapse-main': collapse,
      }"
      class="amf-main"
    >
      <div
        :class="{
          'fixed-header': fixedHeader,
        }"
        class="amf-layout-header"
      >
        <amf-nav />
        <amf-tabs v-show="showTabs" />
      </div>
      <amf-app-main />
    </div>
  </div>
</template>

<script>
  import { mapActions } from 'vuex';

  export default {
    name: 'AmfLayoutVertical',
    props: {
      collapse: {
        type: Boolean,
        default() {
          return false;
        },
      },
      fixedHeader: {
        type: Boolean,
        default() {
          return true;
        },
      },
      showTabs: {
        type: Boolean,
        default() {
          return true;
        },
      },
      device: {
        type: String,
        default() {
          return 'desktop';
        },
      },
    },
    methods: {
      ...mapActions({
        handleFoldSideBar: 'settings/foldSideBar',
      }),
    },
  };
</script>

<style lang="scss" scoped>
  .amf-layout-vertical {
    .fixed-header {
      left: $base-left-menu-width;
      width: $base-right-content-width;
    }
  }
</style>
