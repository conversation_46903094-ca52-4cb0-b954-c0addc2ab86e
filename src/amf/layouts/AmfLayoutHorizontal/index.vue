<template>
  <!-- 横向布局 -->
  <div
    :class="{
      fixed: fixedHeader,
      'no-tabs-bar': !showTabs,
    }"
    class="amf-layout-horizontal"
  >
    <div
      :class="{
        'fixed-header': fixedHeader,
      }"
      class="amf-layout-header"
    >
      <amf-header />
      <div
        v-show="showTabs"
        :class="{
          'amf-tabs-horizontal': showTabs,
        }"
      >
        <div class="amf-main">
          <amf-tabs />
        </div>
      </div>
    </div>
    <div class="amf-main main-padding">
      <amf-app-main />
    </div>
  </div>
</template>

<script>
  export default {
    name: 'AmfLayoutHorizontal',
    props: {
      collapse: {
        type: Boolean,
        default() {
          return false;
        },
      },
      fixedHeader: {
        type: Boolean,
        default() {
          return true;
        },
      },
      showTabs: {
        type: Boolean,
        default() {
          return true;
        },
      },
      device: {
        type: String,
        default() {
          return 'desktop';
        },
      },
    },
  };
</script>

<style lang="scss" scoped>
  .amf-layout-horizontal {
    ::v-deep {
      .amf-main {
        width: 92% !important;
        margin: auto;
      }
    }

    .amf-tabs-horizontal {
      background: $base-color-white;
      box-shadow: $base-box-shadow;
    }

    .amf-nav {
      .fold-unfold {
        display: none;
      }
    }
  }
</style>
