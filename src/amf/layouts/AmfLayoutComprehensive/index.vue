<template>
  <!--综合布局 -->
  <div
    :class="{
      fixed: fixedHeader,
      'no-tabs-bar': !showTabs,
    }"
    class="amf-layout-comprehensive"
  >
    <amf-side-bar layout="comprehensive" />
    <div
      :class="{
        'is-collapse-main': collapse,
      }"
      class="amf-main"
    >
      <div
        :class="{
          'fixed-header': fixedHeader,
        }"
        class="amf-layout-header"
      >
        <amf-nav layout="comprehensive" />
        <amf-tabs v-show="showTabs" />
      </div>
      <amf-app-main />
    </div>
  </div>
</template>

<script>
  export default {
    name: 'AmfLayoutComprehensive',
    props: {
      collapse: {
        type: Boolean,
        default() {
          return false;
        },
      },
      fixedHeader: {
        type: Boolean,
        default() {
          return true;
        },
      },
      showTabs: {
        type: Boolean,
        default() {
          return true;
        },
      },
      device: {
        type: String,
        default() {
          return 'desktop';
        },
      },
    },
  };
</script>

<style lang="scss" scoped>
  .amf-layout-comprehensive {
    .fixed-header {
      left: $base-left-menu-width;
      width: $base-right-content-width;
    }
  }
</style>
