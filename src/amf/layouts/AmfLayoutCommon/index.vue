<template>
  <!--常规布局 -->
  <div
    :class="{
      fixed: fixedHeader,
      'no-tabs-bar': !showTabs,
    }"
    class="amf-layout-common"
  >
    <div
      :class="{
        'fixed-header': fixedHeader,
      }"
      class="amf-layout-header"
    >
      <amf-header layout="common" />
      <div>
        <amf-side-bar layout="common" />
        <div
          v-show="showTabs"
          :class="{
            'is-collapse-main': collapse,
          }"
          class="amf-main"
        >
          <amf-tabs layout="common" />
        </div>
      </div>
    </div>
    <div
      :class="{
        'is-collapse-main': collapse,
      }"
      class="amf-main main-padding"
    >
      <amf-app-main />
    </div>
  </div>
</template>

<script>
  export default {
    name: 'AmfLayoutCommon',
    props: {
      collapse: {
        type: <PERSON>olean,
        default() {
          return false;
        },
      },
      fixedHeader: {
        type: <PERSON>olean,
        default() {
          return true;
        },
      },
      showTabs: {
        type: <PERSON>ole<PERSON>,
        default() {
          return true;
        },
      },
      device: {
        type: String,
        default() {
          return 'desktop';
        },
      },
    },
  };
</script>

<style lang="scss" scoped>
  .amf-layout-common {
    .amf-main {
      .fixed-header {
        left: $base-left-menu-width;
        width: $base-right-content-width;
      }
    }

    ::v-deep {
      .amf-tabs-content {
        width: calc(
          100% - 60px - #{$base-font-size-default} - #{$base-padding} - 2px
        ) !important;
      }

      .amf-header {
        .amf-main {
          width: 100%;
          margin: auto $base-margin;
        }
      }
    }
  }
</style>
