<template>
  <!--分栏布局 -->
  <div
    :class="{
      fixed: fixedHeader,
      'no-tabs-bar': !showTabs,
    }"
    class="amf-layout-column"
  >
    <column-bar />
    <div
      :class="{
        ['amf-main-' + theme.columnStyle]: true,
        'is-collapse-main': collapse,
      }"
      class="amf-main"
    >
      <div
        :class="{
          'fixed-header': fixedHeader,
        }"
        class="amf-layout-header"
      >
        <amf-nav />
        <amf-tabs v-show="showTabs" />
      </div>
      <amf-app-main />
    </div>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';

  export default {
    name: 'AmfLayoutColumn',
    props: {
      collapse: {
        type: <PERSON>olean,
        default() {
          return false;
        },
      },
      fixedHeader: {
        type: Boolean,
        default() {
          return true;
        },
      },
      showTabs: {
        type: Boolean,
        default() {
          return true;
        },
      },
    },
    computed: {
      ...mapGetters({
        theme: 'settings/theme',
      }),
    },
  };
</script>

<style lang="scss" scoped>
  .amf-layout-column {
    .amf-main {
      .fixed-header {
        left: $base-left-menu-width;
        width: $base-right-content-width;
      }

      &.is-collapse-main {
        &.amf-main-horizontal {
          margin-left: $base-left-menu-width-min * 1.3;

          ::v-deep {
            .fixed-header {
              left: $base-left-menu-width-min * 1.3;
              width: calc(100% - #{$base-left-menu-width-min} * 1.3);
            }
          }
        }
      }
    }
  }
</style>
