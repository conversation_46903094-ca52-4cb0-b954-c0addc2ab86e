/*
 * <AUTHOR> 任洪建
 * @Date         : 2021-04-01 17:03:43
 * @LastEditTime : 2021-05-14 19:05:02
 * @LastEditors  : 任洪建
 * @FilePath     : /frontend-framework-template-next/src/amf/plugins/performance.js
 * @Description  :
 */

import Vue from 'vue';
import * as Sentry from '@sentry/vue';
import { Integrations } from '@sentry/tracing';
import { sentry, version } from '@/config';
import { utils } from '@access/core';
const env = process.env.VUE_APP_BUILD_MODE;
import router from '@/router';

if (sentry.enabled && process.env.NODE_ENV === 'production') {
  // build 模式启用
  if (
    (env === 'test' && sentry.enabledTest) ||
    (env === 'pre' && sentry.enabledPre) ||
    env === 'pro'
  ) {
    try {
      // 环境校验
      Sentry.init({
        Vue,
        dsn: sentry.DSN,
        integrations: [new Integrations.BrowserTracing()],
        environment: env,
        release: version,

        // Set tracesSampleRate to 1.0 to capture 100%
        // of transactions for performance monitoring.
        // We recommend adjusting this value in production
        tracesSampleRate: 1.0,
      });
    } catch (e) {
      console.error('sentry 初始化错误:', e);
    }
  }
}

utils.monitor?.monitorRegister({
  env: process.env.BUILD_MODE || 'test',
  service: 'access-fmis-web',
});

router.afterEach(() => {
  utils.monitor?.monitorSpa({
    env: process.env.BUILD_MODE || 'test',
    service: 'access-fmis-web',
  });
});
