/*
 * @Description:
 * @Author: bruce
 * @Date: 2021-03-17 11:25:29
 * @LastEditTime : 2021-05-07 15:42:25
 * @LastEditors  : 任洪建
 * @Reference:
 */
/**
 * @description 路由守卫，目前两种模式：all模式与intelligence模式
 */
import router from '@/router';
import store from '@/store';
import AmfProgress from 'nprogress';
import 'nprogress/nprogress.css';
import getPageTitle from '@/utils/pageTitle';
import { toLoginRoute } from '@/utils/routes';
import {
  authentication,
  loginInterception,
  routesWhiteList,
  supportVisit,
  applyPermission,
  ssoPermission,
} from '@/config';
// import { ssoInit } from '@/utils/sso-sdk.js';
import { utils } from '@access/core';
import { replaceLocalDomain } from '@/utils/index.js';

AmfProgress.configure({
  easing: 'ease',
  speed: 500,
  trickleSpeed: 200,
  showSpinner: false,
});
const hostObj = {
  domain: window.ACCESS_HOSTS.domain,
  apiHost: replaceLocalDomain(window.ACCESS_HOSTS.apiHost),
  ssoHost: window.ACCESS_HOSTS.ssoHost,
};

const ssoConfig = {
  appCode: process.env.VUE_APP_LOGIN_APP_CODE,
  routesWhiteList,
  authentication,
  applyPermission,
  ssoPermission,
};
if (window.ACCESS_HOSTS.domain && ssoPermission) {
  //开启sso验证
  utils.ssoSdk.ssoInit(hostObj, ssoConfig, router, store);
} else {
  router.beforeEach(async (to, from, next) => {
    // const { monitor } = utils;
    // monitor.monitor_spa({
    //   env: 'dev',
    //   service: 'Access-ms-frame-template',
    // });
    const { showProgressBar } = store.getters['settings/theme'];
    if (showProgressBar) AmfProgress.start();
    let hasToken = store.getters['user/token'];

    if (!loginInterception) hasToken = true;

    if (hasToken) {
      if (store.getters['user/isLogin']) {
        // 禁止已登录用户返回登录页
        if (to.path === '/login') {
          next({ path: '/' });
          if (showProgressBar) AmfProgress.done();
        } else next();
      } else {
        try {
          if (loginInterception) await store.dispatch('user/getUserInfo');
          // config/setting.config.js loginInterception为false(关闭登录拦截时)时，创建虚拟角色
          else await store.dispatch('user/setVirtualRoles');
          // 根据路由模式获取路由并根据权限过滤
          await store.dispatch('routes/setRoutesForTest', authentication);
          next({ ...to, replace: true });
        } catch {
          await store.dispatch('user/resetAll');
          next(toLoginRoute(to.path));
        }
      }
    } else {
      if (routesWhiteList.includes(to.path)) {
        // 设置游客路由(不需要可以删除)
        if (supportVisit && !store.getters['routes/routes'].length) {
          await store.dispatch('routes/setRoutes', 'visit');
          next({ path: to.path, replace: true });
        } else next();
      } else next(toLoginRoute(to.path));
    }
  });
}
router.afterEach(to => {
  document.title = getPageTitle(to.meta.title);
  if (AmfProgress.status) AmfProgress.done();
});
