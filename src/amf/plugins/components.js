/*
 * <AUTHOR> 任洪建
 * @Date         : 2021-03-19 18:23:11
 * @LastEditTime: 2021-04-08 16:58:40
 * @LastEditors: bruce
 * @FilePath     : /frontend-framework-template-next/src/amf/plugins/components.js
 * @Description  : 加载AccessComponents
 * @see https://docs.acg.team/#/
 */
import Vue from 'vue';
import Components from '@access/components';
import injectHost from '@/utils/injectHost';
import { replaceLocalDomain } from '@/utils/index.js';

import '@access/components/lib/access-components.css'; // 公共样式
const SSO_APP_CODE = process.env.VUE_APP_LOGIN_APP_CODE; // 项目SSO AppCode
const options = {
  appCode: SSO_APP_CODE,
  apiHost: replaceLocalDomain(injectHost().apiHost), // 项目apiHost
  env: process.env.VUE_APP_BUILD_MODE,
};

Vue.use(Components, options);
