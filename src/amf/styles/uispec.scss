// @import '../../config/variables.scss';

$color-white: #ffffff;
$color-common: #efefef;
$color-accept: '#1890f';
$color-dark-deep: #333333;
$color-dark-normal: #666666;
$color-dark-shallow: #999999;
$color-dark-lite: #bdbdbd;
$color-dark-placeholder: #cbcbcb;
$color-orange: #ff6600;
$size-block: 20px;
$size-item: 10px;

// background
.bg-accept {
  background-color: $color-accept;
}
.bg-common {
  background-color: $color-common;
}
.bg-white {
  background-color: $color-white;
}
.bg-red {
  background-color: red;
}
.bg-green {
  background-color: green;
}
.bg-yellow {
  background-color: yellow;
}

// shadow
.shadow-card {
  box-shadow: 0px 10px 20px 0px rgba(169, 169, 169, 0.1);
}

// text
.text-white {
  color: white;
}
.text-dark-deep {
  color: $color-dark-deep;
}
.text-dark-normal {
  color: $color-dark-normal;
}
.text-dark-shallow {
  color: $color-dark-shallow;
}
.text-dark-lite {
  color: $color-dark-lite;
}
.text-dark-placeholder {
  color: $color-dark-placeholder;
}
.text-accept {
  color: $color-accept;
}
.text-red {
  color: red;
}
.text-orange {
  color: $color-orange;
}
.text-warning {
  // color: $color-warning;
}

.text-size-14 {
  font-size: 14px;
}
.text-size-16 {
  font-size: 16px;
}
.text-size-18 {
  font-size: 18px;
}
.text-size-20 {
  font-size: 20px;
}
.text-size-22 {
  font-size: 22px;
}
.text-size-24 {
  font-size: 24px;
}
.text-size-26 {
  font-size: 26px;
}
.text-size-28 {
  font-size: 28px;
}
.text-size-30 {
  font-size: 30px;
}

.text-bold {
  font-weight: bold;
}
.text-sembold {
  font-weight: bolder;
}
.text-nobold {
  font-weight: normal;
}

.text-delete {
  text-decoration: line-through;
}
.text-suit {
  font-size: 0;
}

.text-1line {
  // display: -webkit-box;
  // -webkit-box-orient: vertical;
  // -webkit-line-clamp: 1;
  // overflow: hidden;

  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.text-2line {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.text-3line {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
  overflow: hidden;
}
.text-4line {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
  overflow: hidden;
}
.text-5line {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
  overflow: hidden;
}

.text-wrap {
  word-wrap: break-word;
  word-break: break-all;
}

.text-no-select {
  user-select: none;
}

.text-hcenter {
  text-align: center;
}

.line-height-none {
  line-height: 100%;
}
.line-height-36 {
  line-height: 36px;
}

.padding-block {
  padding: $size-block;
}
.padding-block-left {
  padding-left: $size-block;
}
.padding-block-right {
  padding-right: $size-block;
}
.padding-block-top {
  padding-top: $size-block;
}
.padding-block-bottom {
  padding-bottom: $size-block;
}
.padding-block-horizontal {
  padding-left: $size-block;
  padding-right: $size-block;
}
.padding-block-vertical {
  padding-top: $size-block;
  padding-bottom: $size-block;
}
.padding-item {
  padding: $size-item;
}
.padding-bottom-10 {
  padding-bottom: 10px;
}

.padding-top-20 {
  padding-top: 20px;
}
.padding-bottom-20 {
  padding-bottom: 20px;
}
.padding-vertical-20 {
  @extend .padding-top-20;
  @extend .padding-bottom-20;
}
.padding-top-30 {
  padding-top: 30px;
}
.padding-bottom-30 {
  padding-bottom: 30px;
}
.padding-vertical-30 {
  @extend .padding-top-30;
  @extend .padding-bottom-30;
}
.padding-left-30 {
  padding-left: 30px;
}
.padding-right-30 {
  padding-right: 30px;
}
.padding-horizontal-30 {
  @extend .padding-left-30;
  @extend .padding-right-30;
}

.margin-block-top {
  margin-top: $size-block;
}
.margin-block-bottom {
  margin-bottom: $size-block;
}
.margin-block-left {
  margin-left: $size-block;
}
.margin-block-right {
  margin-right: $size-block;
}
.margin-block-horizontal {
  @extend .margin-block-left;
  @extend .margin-block-right;
}
.margin-block-vertical {
  @extend .margin-block-top;
  @extend .margin-block-bottom;
}

.margin-left-10 {
  margin-left: 10px !important;
}
.margin-right-10 {
  margin-right: 10px;
}
.margin-top-10 {
  margin-top: 10px !important;
}
.margin-bottom-10 {
  margin-bottom: 10px;
}
.margin-horizontal-10 {
  @extend .margin-left-10;
  @extend .margin-right-10;
}
.margin-top-20 {
  margin-top: 20px;
}
.margin-bottom-20 {
  margin-bottom: 20px;
}
.margin-left-20 {
  margin-left: 20px;
}
.margin-right-20 {
  margin-right: 20px;
}
.margin-top-30 {
  margin-top: 30px;
}
.margin-bottom-30 {
  margin-bottom: 30px;
}
.margin-left-30 {
  margin-left: 30px;
}
.margin-right-30 {
  margin-right: 30px;
}

// 盒模型
.box-inner {
  box-sizing: border-box;
}

.pos-absolute {
  position: absolute;
  z-index: 10;
}
.pos-relative {
  position: relative;
}

.img-stretch {
  position: relative;
  background-size: 100% 100%;
}
.img-cover {
  position: relative;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
.img-contain {
  position: relative;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.fullscreen {
  width: 100%;
  height: 100%;
}

.width-100 {
  width: 100%;
}
.height-100 {
  height: 100%;
}
.width-1px {
  width: 1px;
}
.height-1px {
  height: 1px;
}
.divided-horizontal {
  @extend .width-100;
  @extend .height-1px;
  @extend .bg-common;
}
.divided-vertical {
  @extend .height-100;
  @extend .width-1px;
  @extend .bg-common;
}

.scroll-x {
  overflow-x: scroll;
}
.scroll-y {
  overflow-y: scroll;
}

.overflow-hidden {
  overflow: hidden;
}

.wrap {
  flex-wrap: wrap;
}

.nowrap {
  flex-wrap: nowrap;
}

.wrap-start {
  @extend .wrap;
  align-content: flex-start;
}
.wrap-end {
  @extend .wrap;
  align-content: flex-end;
}
.wrap-center {
  @extend .wrap;
  align-content: center;
}
.wrap-between {
  @extend .wrap;
  align-content: space-between;
}
.wrap-around {
  @extend .wrap;
  align-content: space-around;
}
.wrap-stretch {
  @extend .wrap;
  align-content: stretch;
}

.shrink {
  flex-shrink: 10000;
}

.noshrink {
  flex-shrink: 0;
}
.grow {
  flex-grow: 1;
}
.nogrow {
  flex-grow: 0;
}

.rotate-90 {
  transform: rotateZ(90deg);
}

.in-horizontal.fill-horizontal,
.in-vertical.fill-vertical {
  flex: 1 1;
}

.in-horizontal.fill-vertical,
.in-vertical.fill-horizontal {
  align-self: stretch;
}

.horizontal {
  @extend .box-inner;
  display: flex;
  flex-direction: row;
  // position: relative;
}

.vertical {
  @extend .box-inner;
  display: flex;
  flex-direction: column;
  // position: relative;
}

.horizontal.left,
.vertical.top {
  justify-content: flex-start;
}

.horizontal.hcenter,
.vertical.vcenter {
  justify-content: center;
}

.horizontal.right,
.vertical.bottom {
  justify-content: flex-end;
}

.horizontal.space-between,
.vertical.space-between {
  justify-content: space-between;
}

.horizontal.space-around,
.vertical.space-around {
  justify-content: space-around;
}

.horizontal.left.top,
.horizontal.hcenter.top,
.horizontal.right.top,
.horizontal.space-between.top,
.horizontal.space-around.top,
.vertical.top.left,
.vertical.vcenter.left,
.vertical.bottom.left,
.vertical.space-between.left,
.vertical.space-around.left {
  align-items: flex-start;
}

.horizontal.left.vcenter,
.horizontal.hcenter.vcenter,
.horizontal.right.vcenter,
.horizontal.space-between.vcenter,
.horizontal.space-around.vcenter,
.vertical.top.hcenter,
.vertical.vcenter.hcenter,
.vertical.bottom.hcenter,
.vertical.space-between.hcenter,
.vertical.space-around.hcenter {
  align-items: center;
}

.horizontal.left.bottom,
.horizontal.hcenter.bottom,
.horizontal.right.bottom,
.horizontal.space-between.bottom,
.horizontal.space-around.bottom,
.vertical.top.right,
.vertical.vcenter.right,
.vertical.bottom.right,
.vertical.space-between.right,
.vertical.space-around.right {
  align-items: flex-end;
}

.horizontal.left.stretch,
.horizontal.hcenter.stretch,
.horizontal.right.stretch,
.horizontal.space-between.stretch,
.horizontal.space-around.stretch,
.vertical.top.stretch,
.vertical.vcenter.stretch,
.vertical.bottom.stretch,
.vertical.space-between.stretch,
.vertical.space-around.stretch {
  align-items: stretch;
}

.horizontal.left.baseline,
.horizontal.hcenter.baseline,
.horizontal.right.baseline,
.horizontal.space-between.baseline,
.horizontal.space-around.baseline {
  align-items: baseline;
}

.tap:active {
  // transition: opacity 0.1s;
  opacity: 0.5;
}
.clickable-text-base {
  @extend .tap;
  cursor: pointer;
  user-select: none;
}

.clickable-text {
  @extend .clickable-text-base;
  color: '#1890f';
}
.clickable-text-danger {
  @extend .clickable-text-base;
  color: $base-color-red;
}

.text-disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}
