/**
 * @description 绿荫草场主题
 */

.amf-theme-green {
  $base-menu-background: #16181d;
  $base-color-blue: #009688;
  $base-color-blue-light: mix($base-color-white, $base-color-blue, 90%);

  @mixin container {
    background: $base-menu-background !important;
  }

  @mixin container-common {
    background: #20222a !important;
  }

  @mixin active {
    &:hover {
      color: $base-color-white !important;
      background-color: $base-color-blue !important;
    }

    &.is-active {
      color: $base-color-white !important;
      background-color: $base-color-blue !important;
    }
  }

  .logo-container-horizontal,
  .logo-container-vertical {
    @include container;
  }

  .logo-container-comprehensive {
    @include container;
  }

  .logo-container-column {
    .logo {
      @include container;
    }
  }

  .column-bar-container.el-scrollbar {
    .el-tabs {
      .el-tabs__nav-wrap.is-left {
        @include container;
      }

      .el-tabs__nav {
        @include container;
      }

      .el-tabs__item.is-active {
        background: $base-color-blue !important;
      }
    }

    .el-menu {
      .el-menu-item.is-active,
      .el-submenu__title.is-active,
      .el-menu-item:hover,
      .el-submenu__title:hover {
        i {
          color: $base-color-blue !important;
        }

        color: $base-color-blue !important;
        background-color: $base-color-blue-light !important;
      }
    }
  }

  .column-bar-container-card.el-scrollbar {
    .el-tabs {
      .el-tabs__item.is-active {
        background: transparent !important;

        .column-grid {
          background: $base-color-blue !important;
        }
      }
    }
  }

  .amf-layout-vertical,
  .amf-layout-horizontal,
  .amf-layout-comprehensive {
    .el-menu {
      @include container;

      .el-submenu__title {
        @include container;
      }

      .el-menu-item {
        @include container;
      }
    }

    .amf-side-bar,
    .comprehensive-bar-container {
      @include container;

      .el-menu-item {
        @include active;
      }
    }
  }

  .amf-layout-common {
    .el-menu {
      @include container-common;

      .el-submenu__title {
        @include container-common;
      }

      .el-menu-item {
        @include container-common;
      }
    }

    .amf-side-bar {
      @include container-common;

      .el-menu-item {
        @include active;
      }
    }
  }

  .amf-header {
    @include container;

    .amf-main {
      @include container;

      .right-panel {
        .el-menu.el-menu {
          &--horizontal {
            .el-submenu,
            .el-menu-item {
              @include active;

              &.is-active {
                background-color: $base-color-blue !important;
              }
            }
          }
        }
      }
    }
  }

  .amf-tabs {
    .amf-tabs-content-card {
      .el-tabs__header {
        .el-tabs__item {
          &.is-active {
            color: $base-color-blue !important;
            background: $base-color-blue-light !important;
            border: 1px solid $base-color-blue !important;
          }

          &:hover {
            border: 1px solid $base-color-blue !important;
          }
        }
      }
    }

    .amf-tabs-content-smart {
      .el-tabs__header {
        .el-tabs__item {
          &.is-active {
            background: $base-color-blue-light !important;
          }

          &:after {
            background-color: $base-color-blue !important;
          }

          &:hover {
            background: $base-color-blue-light !important;
          }
        }
      }
    }

    .amf-tabs-content-smooth {
      .el-tabs__header {
        .el-tabs__item {
          &.is-active {
            color: $base-color-blue !important;
            background: $base-color-blue-light !important;

            &:hover {
              color: $base-color-blue !important;
              background: $base-color-blue-light !important;
            }
          }

          &:hover {
            color: $base-color-black !important;
          }
        }
      }
    }
  }

  .amf-nav {
    .el-tabs__item.is-active,
    .el-tabs__item:hover {
      color: $base-color-blue !important;
    }

    .el-tabs__active-bar {
      background-color: $base-color-blue !important;
    }
  }

  #nprogress {
    .bar {
      background: $base-color-blue !important;
    }

    .peg {
      box-shadow: 0 0 10px $base-color-blue, 0 0 5px $base-color-blue !important;
    }
  }
}
