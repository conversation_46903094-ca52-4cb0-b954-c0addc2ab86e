<!--
 * @Description:
 * @Author: 刘攀
 * @Date: 2021-05-13 18:16:51
 * @LastEditTime: 2022-05-06 20:34:44
 * @LastEditors: xuxiang
 * @Reference: 
-->
<template>
  <div class="amf-app-main">
    <section style="padding: 20px; box-sizing: border-box">
      <transition mode="out-in" name="fade-transform">
        <amf-keep-alive v-if="routerView" />
      </transition>
    </section>
    <amf-footer />
  </div>
</template>

<script>
  import { mapActions, mapGetters } from 'vuex';
  import AmfProgress from 'nprogress';

  export default {
    name: 'AmfAppMain',
    data() {
      return {
        routerView: true,
      };
    },
    computed: {
      ...mapGetters({
        theme: 'settings/theme',
        extra: 'settings/extra',
        visitedRoutes: 'tabs/visitedRoutes',
      }),
      changeData() {
        return [
          ...new Set(
            this.visitedRoutes
              .filter(
                item =>
                  !item.meta.noKeepAlive &&
                  item.name !== this.extra.transferRouteName,
              )
              .flatMap(item => item.matched),
          ),
        ];
      },
    },
    watch: {
      changeData: {
        handler(visitedRoutes = []) {
          // 兼容小驼峰
          const arr = visitedRoutes.map(item => this.firstToUpper(item));
          this.setCachedRoutes(arr);
        },
      },
    },
    created() {
      const { showProgressBar } = this.theme;
      // 单页面情况下重载路由
      this.$baseEventBus.$on('reload-router-view', () => {
        this.routerView = false;
        if (showProgressBar) AmfProgress.start();
        this.$nextTick(() => {
          this.routerView = true;
          setTimeout(() => {
            if (showProgressBar) AmfProgress.done();
          }, 200);
        });
      });
    },
    methods: {
      ...mapActions({
        setCachedRoutes: 'routes/setCachedRoutes',
      }),
      firstToUpper(str) {
        return str.replace(/\b(\w)(\w*)/g, function ($0, $1, $2) {
          return $1.toUpperCase() + $2;
        });
      },
    },
  };
</script>
