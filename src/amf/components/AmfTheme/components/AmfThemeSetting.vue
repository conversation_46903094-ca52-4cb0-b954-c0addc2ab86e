<template>
  <ul v-if="theme.showThemeSetting" class="amf-theme-setting">
    <li @click="handleOpenTheme">
      <a>
        <amf-icon icon="brush-2-line" />
        <p>{{ translateTitle('主题配置') }}</p>
      </a>
    </li>
    <li @click="randomTheme">
      <a>
        <amf-icon icon="apps-line" />
        <p>{{ translateTitle('随机换肤') }}</p>
      </a>
    </li>
    <li @click="getCode">
      <a>
        <amf-icon icon="file-copy-line" />
        <p>{{ translateTitle('拷贝源码') }}</p>
      </a>
    </li>
    <li @click="removeLocalStorage">
      <a>
        <amf-icon icon="delete-bin-4-line" />
        <p>
          {{ translateTitle('清理缓存') }}
        </p>
      </a>
    </li>
  </ul>
</template>

<script>
  import { translateTitle } from '@/utils/i18n';
  import { mapGetters } from 'vuex';

  export default {
    name: 'AmfThemeSetting',
    computed: {
      ...mapGetters({
        theme: 'settings/theme',
      }),
    },
    methods: {
      translateTitle,
      handleOpenTheme() {
        this.$baseEventBus.$emit('theme');
      },
      randomTheme() {
        this.$baseEventBus.$emit('random-theme');
      },
      getCode() {
        let path = this.$route.path + '/index.vue';
        let _path = this.$route.path;
        switch (_path) {
          case '/workbench':
            path = '/index/workbench.vue';
            break;
          case '/amf/icon/remixIcon':
            path = '/amf/icon/remixIcon.vue';
            break;
          case '/amf/icon/iconSelector':
            path = '/amf/icon/iconSelector.vue';
            break;
          case '/amf/table/comprehensiveTable':
            path = '/amf/table/comprehensiveTable.vue';
            break;
          case '/amf/table/inlineEditTable':
            path = '/amf/table/inlineEditTable.vue';
            break;
          case '/amf/table/customTable':
            path = '/amf/table/customTable.vue';
            break;
          case '/amf/form/comprehensiveForm':
            path = '/amf/form/comprehensiveForm.vue';
            break;
          case '/amf/form/stepForm':
            path = '/amf/form/stepForm.vue';
            break;
          case '/amf/dynamicSegment/test1/1':
            path = '/amf/dynamicSegment/test1.vue';
            break;
          case '/amf/dynamicSegment/test2?id=1':
            path = '/amf/dynamicSegment/test2.vue';
            break;
          case '/amf/drag/dialogDrag':
            path = '/amf/drag/dialogDrag.vue';
            break;
          case '/amf/drag/cardDrag':
            path = '/amf/drag/cardDrag.vue';
            break;
          case '/amf/drag/flowSheetDrag':
            path = '/amf/drag/flowSheetDrag.vue';
            break;
          case '/amf/editor/richTextEditor':
            path = '/amf/editor/richTextEditor.vue';
            break;
          case '/amf/editor/markdownEditor':
            path = '/amf/editor/markdownEditor.vue';
            break;
          case '/amf/menu1/menu1-1/menu1-1-1/menu1-1-1-1':
            path = '/amf/nested/menu1/menu1-1/menu1-1-1/menu1-1-1-1.vue';
            break;
          case '/amf/excel/exportExcel':
            path = '/amf/excel/exportExcel.vue';
            break;
          case '/amf/excel/exportSelectedExcel':
            path = '/amf/excel/exportSelectedExcel.vue';
            break;
          case '/amf/excel/exportMergeHeaderExcel':
            path = '/amf/excel/exportMergeHeaderExcel.vue';
            break;
        }
        window.open(
          `https://git.acg.team/frontend-framework/access-ms-frame/blob/master/src/views${path}`,
        );
      },
      removeLocalStorage() {
        localStorage.clear();
        location.reload();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .amf-theme-setting {
    position: fixed;
    top: 50%;
    right: 0;
    z-index: $base-z-index + 1;
    padding: 0;
    padding: 10px 0 0 0;
    margin: 0;
    text-align: center;
    cursor: pointer;
    background: $base-color-white;
    border: 1px solid $base-border-color;
    border-top-left-radius: $base-border-radius + 3;
    border-bottom-left-radius: $base-border-radius + 3;
    // box-shadow: 0 0 50px 0 rgb(82 63 105 / 15%);
    transform: translateY(-50%);

    > li {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 8px 10px 10px;
      margin: 0;
      list-style: none;

      &:nth-child(2) {
        [class*='ri-'] {
          animation: rotate 6s linear infinite;
        }
      }

      $colors: (
        1: #3698fd,
        2: #1bc3bb,
        3: #faa500,
        4: #b37feb,
        5: #ef4c5d,
      );

      @each $key, $color in $colors {
        &:nth-child(#{$key}) {
          a {
            color: $color;
            background: mix($base-color-white, $color, 90%);
            transition: color 0.15s ease, background-color 0.15s ease,
              border-color 0.15s ease, box-shadow 0.15s ease,
              -webkit-box-shadow 0.15s ease;

            &:hover {
              color: $base-color-white;
              background: $color;
            }
          }
        }
      }

      a {
        display: inline-block;
        width: 60px;
        height: 60px;
        padding-top: 10px;
        text-align: center;
        background: #f6f8f9;
        border-radius: $base-border-radius + 3;

        p {
          padding: 0;
          margin: 0;
          overflow: hidden;
          font-size: $base-font-size-small;
          line-height: 25px;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
</style>
