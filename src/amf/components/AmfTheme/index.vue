<template>
  <span v-if="theme.showTheme">
    <amf-icon icon="brush-2-line" @click="handleOpenTheme" />
  </span>
</template>

<script>
  import { mapGetters } from 'vuex';

  export default {
    name: 'AmfTheme',
    computed: {
      ...mapGetters({
        theme: 'settings/theme',
      }),
    },
    methods: {
      handleOpenTheme() {
        this.$baseEventBus.$emit('theme');
      },
    },
  };
</script>
