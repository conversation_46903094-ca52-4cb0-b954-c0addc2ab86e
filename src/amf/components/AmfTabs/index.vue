<template>
  <div class="amf-tabs">
    <amf-fold v-if="layout === 'common'" />
    <el-tabs
      v-model="tabActive"
      :class="{
        ['amf-tabs-content-' + theme.tabsBarStyle]: true,
      }"
      class="amf-tabs-content"
      type="card"
      @tab-click="handleTabClick"
      @tab-remove="handleTabRemove"
    >
      <el-tab-pane
        v-for="item in visitedRoutes"
        :key="item.path"
        :closable="!isNoCLosable(item)"
        :name="item.path"
      >
        <span v-if="theme.showTabsBarIcon" slot="label">
          <amf-icon
            v-if="item.meta && item.meta.icon"
            :icon="item.meta.icon"
            :is-custom-svg="item.meta.isCustomSvg"
          />
          <!--  如果没有图标那么取第二级的图标 -->
          <amf-icon v-else-if="item.parentIcon" :icon="item.parentIcon" />
          {{ translateTitle(item.meta.title) }}
        </span>
        <span v-else slot="label">
          {{ translateTitle(item.meta.title) }}
        </span>
        <!--  vue 3.0写法 -->
        <!--  <template v-if="theme.showTabsBarIcon" #label>
          <amf-icon
            v-if="item.meta && item.meta.icon"
            :icon="item.meta.icon"
            :is-custom-svg="item.meta.isCustomSvg"
          />
          <amf-icon v-else :icon="item.parentIcon" />
          {{ translateTitle(item.meta.title) }}
        </template>
        <template v-else #label>
          {{ translateTitle(item.meta.title) }}
        </template> -->
      </el-tab-pane>
    </el-tabs>

    <el-dropdown @command="handleCommand" @visible-change="handleVisibleChange">
      <span class="more">
        {{ translateTitle('更多') }}
        <amf-icon
          :class="{ 'amf-dropdown-active': active }"
          class="amf-dropdown"
          icon="arrow-down-s-line"
        />
      </span>
      <template #dropdown>
        <el-dropdown-menu class="tabs-more">
          <el-dropdown-item command="closeOthersTabs">
            <amf-icon icon="close-line" />
            {{ translateTitle('关闭其他') }}
          </el-dropdown-item>
          <el-dropdown-item command="closeLeftTabs">
            <amf-icon icon="arrow-left-line" />
            {{ translateTitle('关闭左侧') }}
          </el-dropdown-item>
          <el-dropdown-item command="closeRightTabs">
            <amf-icon icon="arrow-right-line" />
            {{ translateTitle('关闭右侧') }}
          </el-dropdown-item>
          <el-dropdown-item command="closeAllTabs">
            <amf-icon icon="close-line" />
            {{ translateTitle('关闭全部') }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script>
  import { translateTitle } from '@/utils/i18n';
  import { mapActions, mapGetters } from 'vuex';
  import { handleActivePath } from '@/utils/routes';

  export default {
    name: 'AmfTabs',
    props: {
      layout: {
        type: String,
        default: '',
      },
    },
    data() {
      // console.log(this.visitedRoutes, 'visitedRoutes000');
      return {
        noCLosableTabs: [],
        tabActive: '',
        active: false,
      };
    },
    computed: {
      ...mapGetters({
        visitedRoutes: 'tabs/visitedRoutes',
        routes: 'routes/routes',
        theme: 'settings/theme',
      }),
    },
    watch: {
      $route: {
        handler(route) {
          this.$nextTick(() => {
            this.addTabs(route);
          });
        },
        immediate: true,
      },
    },
    created() {
      this.initNoCLosableTabs(this.routes);
    },
    methods: {
      translateTitle,
      ...mapActions({
        addVisitedRoute: 'tabs/addVisitedRoute',
        delVisitedRoute: 'tabs/delVisitedRoute',
        delOthersVisitedRoutes: 'tabs/delOthersVisitedRoutes',
        delLeftVisitedRoutes: 'tabs/delLeftVisitedRoutes',
        delRightVisitedRoutes: 'tabs/delRightVisitedRoutes',
        delAllVisitedRoutes: 'tabs/delAllVisitedRoutes',
      }),
      handleTabClick(tab) {
        if (!this.isActive(tab.name))
          // this.$store.dispatch(
          //   'button/onGetButtonList',
          //   this.visitedRoutes[tab.index]?.path,
          // );
          this.$router.push(this.visitedRoutes[tab.index]);
      },
      handleVisibleChange(val) {
        this.active = val;
      },
      initNoCLosableTabs(routes) {
        routes.forEach(route => {
          if (route.meta && route.meta.noCLosable) this.addTabs(route, true);
          if (route.children) this.initNoCLosableTabs(route.children);
        });
      },
      /**
       * 添加标签页
       * @param tag route
       * @param init 是否是从router获取路由
       * @returns {Promise<void>}
       */
      async addTabs(tag, init = false) {
        let parentIcon = '';
        if (tag.matched && tag.matched.length > 1)
          parentIcon = tag.matched[1].meta.icon;
        if (tag.name && tag.meta && tag.meta.tabHidden !== true) {
          const path = handleActivePath(tag, true);
          await this.addVisitedRoute({
            path: path,
            query: tag.query,
            params: tag.params,
            name: tag.name,
            matched: init ? [tag.name] : tag.matched.map(item => item.name),
            parentIcon,
            meta: { ...tag.meta },
          });
          this.tabActive = path;
        }
      },
      /**
       * 根据原生路径删除标签中的标签
       * @param rawPath 原生路径
       * @returns {Promise<void>}
       */
      async handleTabRemove(rawPath) {
        await this.delVisitedRoute(rawPath);
        if (this.isActive(rawPath)) this.toLastTab();
      },
      handleCommand(command) {
        switch (command) {
          case 'closeOthersTabs':
            this.closeOthersTabs();
            break;
          case 'closeLeftTabs':
            this.closeLeftTabs();
            break;
          case 'closeRightTabs':
            this.closeRightTabs();
            break;
          case 'closeAllTabs':
            this.closeAllTabs();
            break;
        }
      },
      /**
       * 删除其他标签页
       * @returns {Promise<void>}
       */
      async closeOthersTabs() {
        await this.delOthersVisitedRoutes(handleActivePath(this.$route, true));
      },
      /**
       * 删除左侧标签页
       * @returns {Promise<void>}
       */
      async closeLeftTabs() {
        await this.delLeftVisitedRoutes(handleActivePath(this.$route, true));
      },
      /**
       * 删除右侧标签页
       * @returns {Promise<void>}
       */
      async closeRightTabs() {
        await this.delRightVisitedRoutes(handleActivePath(this.$route, true));
      },
      /**
       * 删除所有标签页
       * @returns {Promise<void>}
       */
      async closeAllTabs() {
        await this.delAllVisitedRoutes();
        if (
          !this.noCLosableTabs.some(tag =>
            this.isActive(handleActivePath(tag, true)),
          )
        )
          this.toLastTab();
      },
      /**
       * 跳转最后一个标签页
       */
      toLastTab() {
        const latestView = this.visitedRoutes.slice(-1)[0];
        if (latestView) this.$router.push(latestView);
        else this.$router.push('/');
      },
      isActive(path) {
        return path === handleActivePath(this.$route, true);
      },
      isNoCLosable(tag) {
        return tag.meta && tag.meta.noCLosable;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .amf-tabs {
    position: relative;
    box-sizing: border-box;
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: space-between;
    height: $base-tabs-height;
    padding-right: $base-padding;
    padding-left: $base-padding;
    user-select: none;
    background: $base-color-white;
    border-top: 1px solid #f6f6f6;

    ::v-deep {
      .fold-unfold {
        margin-right: $base-margin;
      }
    }

    .amf-tabs-content {
      width: calc(100% - 60px);

      &-card {
        height: $base-tag-item-height;

        ::v-deep {
          .el-tabs__nav-next,
          .el-tabs__nav-prev {
            height: $base-tag-item-height;
            line-height: $base-tag-item-height;
          }

          .el-tabs__header {
            border-bottom: 0;

            .el-tabs__nav {
              border: 0;
            }

            .el-tabs__item {
              box-sizing: border-box;
              height: $base-tag-item-height;
              margin-right: 5px;
              line-height: $base-tag-item-height;
              border: 1px solid $base-border-color;
              border-radius: $base-border-radius;
              transition: padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) !important;

              &.is-active {
                color: $base-color-blue;
                background: mix($base-color-white, $base-color-blue, 90%);
                border: 1px solid $base-color-blue;
              }

              &:hover {
                border: 1px solid $base-color-blue;
              }
            }
          }
        }
      }

      &-smart {
        height: $base-tag-item-height;

        ::v-deep {
          .el-tabs__nav-next,
          .el-tabs__nav-prev {
            height: $base-tag-item-height;
            line-height: $base-tag-item-height;
          }

          .el-tabs__header {
            border-bottom: 0;

            .el-tabs__nav {
              border: 0;
            }

            .el-tabs__item {
              height: $base-tag-item-height;
              margin-right: 5px;
              line-height: $base-tag-item-height;
              border: 0;
              transition: padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) !important;

              &.is-active {
                background: mix($base-color-white, $base-color-blue, 90%);

                &:after {
                  width: 100%;
                  transition: $base-transition;
                }
              }

              &:after {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 0;
                height: 2px;
                content: '';
                background-color: $base-color-blue;
                transition: $base-transition;
              }

              &:hover {
                background: mix($base-color-white, $base-color-blue, 90%);

                &:after {
                  width: 100%;
                  transition: $base-transition;
                }
              }
            }
          }
        }
      }

      &-smooth {
        height: $base-tag-item-height + 4;

        ::v-deep {
          .el-tabs__nav-next,
          .el-tabs__nav-prev {
            height: $base-tag-item-height + 4;
            line-height: $base-tag-item-height + 4;
          }

          .el-tabs__header {
            border-bottom: 0;

            .el-tabs__nav {
              border: 0;
            }

            .el-tabs__item {
              height: $base-tag-item-height + 4;
              padding: 0 30px 0 30px;
              margin-top: ($base-tabs-height - $base-tag-item-height - 4)/2;
              margin-right: -18px;
              line-height: $base-tag-item-height + 4;
              text-align: center;
              text-align: center;
              border: 0;
              transition: padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) !important;

              &.is-active {
                padding: 0 30px 0 30px;
                color: $base-color-blue;
                background: mix($base-color-white, $base-color-blue, 90%);
                mask: url('~@/assets/tabs_images/amf-tab.png');
                mask-size: 100% 100%;

                &:hover {
                  padding: 0 30px 0 30px;
                  color: $base-color-blue;
                  background: mix($base-color-white, $base-color-blue, 90%);
                  mask: url('~@/assets/tabs_images/amf-tab.png');
                  mask-size: 100% 100%;
                }
              }

              &:hover {
                padding: 0 30px 0 30px;
                color: $base-color-black;
                background: #dee1e6;
                mask: url('~@/assets/tabs_images/amf-tab.png');
                mask-size: 100% 100%;
              }
            }
          }
        }
      }
    }

    .more {
      display: flex;
      align-content: center;
      align-items: center;
      cursor: pointer;
    }
  }
</style>
