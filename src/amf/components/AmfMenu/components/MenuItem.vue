<!--
 * @Description: 
 * @Author: bruce
 * @Date: 2021-03-10 14:18:00
 * @LastEditTime: 2021-04-14 15:45:27
 * @LastEditors: bruce
 * @Reference: 
-->
<template>
  <el-menu-item :index="itemOrMenu.path" @click="handleLink">
    <amf-icon
      v-if="itemOrMenu.meta && itemOrMenu.meta.icon"
      :icon="itemOrMenu.meta.icon"
      :is-custom-svg="itemOrMenu.meta.isCustomSvg"
      :title="translateTitle(itemOrMenu.meta.title)"
    />
    <span :title="translateTitle(itemOrMenu.meta.title)">
      {{ translateTitle(itemOrMenu.meta.title) }}
    </span>
    <el-tag
      v-if="itemOrMenu.meta && itemOrMenu.meta.badge"
      effect="dark"
      type="danger"
    >
      {{ itemOrMenu.meta.badge }}
    </el-tag>
    <span
      v-if="itemOrMenu.meta && itemOrMenu.meta.dot"
      class="amf-dot amf-dot-error"
    >
      <span />
    </span>
  </el-menu-item>
</template>

<script>
  import { translateTitle } from '@/utils/i18n';
  import { isExternal } from '@/utils/validate';
  import { mapActions, mapGetters } from 'vuex';
  import { getObjByPropty } from '@/utils/index.js';
  import { buttonPermissionMode } from '@/config/setting.config.js';

  export default {
    name: 'MenuItem',
    props: {
      itemOrMenu: {
        type: Object,
        default() {
          return null;
        },
      },
    },
    data() {
      return {
        curRoute: this.$route.path,
        isTrigger: false,
      };
    },
    computed: {
      ...mapGetters({
        device: 'settings/device',
        routes: 'routes/routes',
      }),
    },
    watch: {
      $route: {
        handler: function (val, oldVal) {
          this.getButtonList(val.path);
          this.isTrigger = true;
        },
        deep: true, //true 深度监听
      },
    },
    created() {
      // if (this.itemOrMenu.path === this.$route.path) {
      //   if (buttonPermissionMode !== 'none') {
      //     this.$store.dispatch('button/onGetButtonList', this.$route.path);
      //   }
      // }
      if (!this.isTrigger) {
        this.getButtonList(this.$route.path);
      }
    },
    methods: {
      translateTitle,
      ...mapActions({
        foldSideBar: 'settings/foldSideBar',
      }),
      getButtonList(path) {
        // if (this.itemOrMenu.path === path) {
        //   if (buttonPermissionMode !== 'none') {
        //     this.$store.dispatch('button/onGetButtonList', path);
        //   }
        // }
      },
      handleLink() {
        const routePath = this.itemOrMenu.path;
        const target = this.itemOrMenu.meta.target;
        if (target === '_blank') {
          if (isExternal(routePath)) window.open(routePath, '_blank');
          else if (this.$route.fullPath !== routePath)
            window.open(routePath.href, '_blank');
        } else {
          if (isExternal(routePath)) window.open(routePath, '_blank');
          else if (this.$route.fullPath !== routePath) {
            if (this.device === 'mobile') this.foldSideBar();
            this.$router.push(routePath);
          }
        }
        // this.$store.dispatch('button/onGetButtonList', routePath);
      },
    },
  };
</script>

<style lang="scss" scoped>
  ::v-deep {
    .el-tag {
      float: right;
      height: 16px;
      padding-right: 4px;
      padding-left: 4px;
      margin-top: ($base-menu-item-height - 16) / 2;
      line-height: 16px;
      border: 0;
    }
  }

  .amf-dot {
    float: right;
    margin-top: ($base-menu-item-height - 6) / 2 + 1;
  }
</style>
