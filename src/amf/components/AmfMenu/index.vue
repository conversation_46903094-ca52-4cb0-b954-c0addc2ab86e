<!--
 * @Description: 
 * @Author: bruce
 * @Date: 2021-03-10 14:18:00
 * @LastEditTime: 2021-04-12 13:34:28
 * @LastEditors: bruce
 * @Reference: 
-->
<template>
  <component :is="menuComponent" v-if="!item.hidden" :item-or-menu="itemOrMenu">
    <template v-if="item.children && item.children.length">
      <el-scrollbar
        v-if="
          (layout === 'horizontal' && item.children.length > 18) ||
          (layout !== 'horizontal' && collapse && item.children.length > 18)
        "
        style="height: 86vh"
      >
        <amf-menu
          v-for="route in item.children"
          :key="route.path"
          :item="route"
        />
      </el-scrollbar>
      <template v-else>
        <amf-menu
          v-for="route in item.children"
          :key="route.path"
          :item="route"
        />
      </template>
    </template>
  </component>
</template>

<script>
  import { mapGetters } from 'vuex';
  import { getObjByPropty } from '@/utils/index.js';
  export default {
    name: 'AmfMenu',
    props: {
      item: {
        type: Object,
        required: true,
      },
      layout: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        // itemOrMenu: this.item,
        menuComponent: 'MenuItem',
      };
    },

    computed: {
      ...mapGetters({
        collapse: 'settings/collapse',
        routes: 'routes/routes',
        buttonList: 'button/buttonList',
      }),
    },

    watch: {
      item: {
        handler() {
          this.itemOrMenu = this.item;
          this.initMenu();
        },
        immediate: true,
        deep: true,
      },
    },
    created() {
      this.initMenu();
    },

    methods: {
      initMenu() {
        // 初始化menuComponent值
        this.menuComponent = 'MenuItem';
        const showChildren = this.handleChildren(this.item.children);
        if (showChildren.length) {
          if (showChildren.length === 1 && this.item.alwaysShow !== true) {
            this.itemOrMenu = this.item.children[0];
          } else {
            this.menuComponent = 'Submenu';
          }
        }
      },

      handleChildren(children = []) {
        if (!children) return [];
        return children.filter(item => {
          return item.hidden !== true;
        });
      },
    },
  };
</script>
