/*
 * @Author: dddd
 * @Date: 2022-10-17 16:14:04
 * @LastEditors: dddd
 * @LastEditTime: 2022-11-18 16:27:59
 * @FilePath: /fmis/src/components/index.js
 * @Description:
 */
import Vue from 'vue';

// 加载组件
const requireComponent = require.context('../components', true, /\.vue$/);
requireComponent.keys().forEach(fileName => {
  const componentConfig = requireComponent(fileName);
  const componentName = componentConfig.default.name;
  Vue.component(componentName, componentConfig.default || componentConfig);
});
