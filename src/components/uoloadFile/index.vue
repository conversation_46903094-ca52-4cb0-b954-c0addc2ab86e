<!--
 * @Author: 七七
 * @Date: 2022-04-21 20:08:24
 * @LastEditors: bruce
 * @LastEditTime: 2023-09-05 17:39:19
 * @FilePath: /access-fmis-web/src/components/uoloadFile/index.vue
-->
<template>
  <el-upload
    :action="action"
    :headers="headers"
    :data="uploadData"
    :on-success="onSuccess"
    :on-error="onError"
    :show-file-list="false"
    :accept="accept"
    :before-upload="beforeUpload"
  >
    <el-button type="primary" :disabled="disabled">{{ btnText }}</el-button>
  </el-upload>
</template>
<script>
  import { getCookie, appCode } from '@/utils/auth';
  import injectHost from '@/utils/injectHost';
  import { replaceLocalDomain } from '@/utils/index.js';
  export default {
    name: 'UploadImg',
    props: {
      disabled: {
        type: Boolean,
        default: false,
      },
      permissionKey: {
        type: String,
        default: '',
      },
      btnText: {
        type: String,
        default: '导入',
      },
      uploadUrl: {
        type: String,
        default: '/api/abmio/api/v1.0/upload',
      },
      accept: {
        type: String,
        default: '.xlsx, .xls,',
      },
      uploadData: {
        type: Object,
        default: () => {
          return {
            appId: 'abmau',
            timeStamp: Date.now(),
          };
        },
      },
    },
    data() {
      return {
        action: '', // 导入接口
        headers: {
          token: getCookie(),
          appCode: appCode(),
        },
      };
    },
    watch: {
      uploadUrl: {
        handler(url) {
          this.action =
            window.location.protocol +
            '//' +
            replaceLocalDomain(injectHost().apiHost) +
            url;
        },
        immediate: true,
      },
      disabled: {
        handler() {},
        immediate: true,
      },
    },
    created() {
      // headers加参数
      let Base64 = require('js-base64').Base64;

      this.headers['userName'] = Base64.encode(this.$store.state.user.username);
      this.headers['userId'] = this.$store.state.user.userInfo.id;
    },
    methods: {
      onSuccess(e) {
        this.$emit('onSuccess', e);
      },
      onError(e) {
        this.$emit('onError', e);
      },
      async beforeUpload(e) {
        return new Promise(async (resolve, reject) => {
          const res = await this.$store.dispatch(
            'button/onGetButtonPermission',
            {
              buttonCode: this.permissionKey,
            },
          );
          if (res) {
            this.$emit('beforeUpload', e);
            resolve();
          } else {
            reject();
          }
        });
      },
      onChange(e) {
        this.$emit('onChange', e);
      },
    },
  };
</script>
