<template>
  <div>
    <el-upload
      ref="upload"
      class="upload"
      :accept="accept"
      :name="fileName"
      :action="uploadUrl"
      :data="uploadData"
      :headers="headers"
      :list-type="listType"
      :file-list="initFileList"
      :limit="max"
      :disabled="disabled"
      :multiple="multiple"
      :before-upload="beforeAvatarUpload"
      :on-preview="handlePictureCardPreview"
      :on-success="handleSuccess"
      :on-remove="handleRemove"
      :on-exceed="handleExceed"
    >
      <i v-if="listType === 'picture-card'" class="el-icon-plus"></i>
      <el-button v-else size="small" type="primary">{{ btnText }}</el-button>
    </el-upload>
    <el-dialog
      v-if="listType == 'picture-card'"
      :visible.sync="dialogVisible"
      append-to-body
    >
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>
  </div>
</template>
<script>
  import { getCookie, appCode } from '@/utils/auth';
  const uploadUrl =
    'https://' + window.ACCESS_HOSTS.apiHost + '/api/abmio/api/v1.0/upload';
  export default {
    name: 'UploadImg',
    props: {
      typeFile: {
        type: Array,
        default: () => {
          return ['image/png', 'image/gif', 'image/jpg', 'image/jpeg'];
        },
      },
      // 图片限制大小
      size: {
        type: Number,
        default: 2,
      },
      // 图片限制大小
      multiple: {
        type: Boolean,
        default: true,
      },
      accept: {
        type: String,
        default: '',
      },
      fileName: {
        type: String,
        default: 'file',
      },
      listType: {
        type: String,
        default: 'picture-card',
      },
      btnText: {
        type: String,
        default: '上传图片',
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      max: {
        type: Number,
        default: 3,
      },
      initFileList: {
        type: Array,
        default: () => [],
      },
      uploadData: {
        type: Object,
        default: () => {
          return {
            appId: 'abmau',
            timeStamp: Date.now(),
          };
        },
      },
    },
    data() {
      return {
        uploadUrl,
        dialogImageUrl: '',
        dialogVisible: false,
        // fileList: this.initFileList,
        headers: {
          token: getCookie(),
          appCode: appCode(),
        },
      };
    },
    methods: {
      clearFiles() {
        this.$refs.upload.clearFiles();
      },
      handleRemove(file, fileList) {
        const filesArr = this.change(file, fileList);
        this.$emit(
          'onRemove',
          {
            id: file.response ? file.response.data.id : file.id,
            file_url: file.response ? file.response.data.url : file.url,
            uid: file.uid,
            name: file.name,
          },
          filesArr,
        );
      },
      handlePictureCardPreview(file) {
        if (this.listType === 'picture-card') {
          this.dialogImageUrl = file.url;
          this.dialogVisible = true;
        } else {
          window.open(file.url);
          // window.location.href = file.url
        }
      },
      beforeAvatarUpload(file) {
        const typeFile = this.typeFile;
        const isFileType = typeFile.length
          ? typeFile.includes(file.type)
          : true;

        const isLt = file.size / 1024 / 1024 < this.size;

        if (!isFileType) {
          this.$message.error(`只能上传 ${typeFile} 格式!`);
        }
        if (!isLt) {
          this.$message.error(`上传大小不能超过 ${this.size}MB!`);
        }
        return isFileType && isLt;
      },
      handleExceed(files, fileList) {
        this.$message.info(
          '最多只能上传' + this.max + '个文件，您可以试试删除后再上传',
        );
      },
      handleSuccess(response, file, fileList) {
        if (!response.success) {
          this.$message.error('图片上传失败');
        }
        const filesArr = this.change(file, fileList);
        this.$emit(
          'changeImage',
          {
            id: file.response.data.id,
            file_url: file.response.data.url,
            extName: file.response.data.extName,
            uid: file.uid,
            name: file.name,
          },
          filesArr,
        );
      },
      change(file, fileList) {
        const filesArr = fileList.map(item => {
          if (!item.response) {
            return {
              id: item.id,
              file_url: item.url,
              extName: item.extName,
              uid: item.uid,
              name: item.name,
            };
          } else {
            return {
              id: item.response.data.id,
              file_url: item.response.data.url,
              extName: item.response.data.extName,
              uid: item.uid,
              name: item.name,
            };
          }
        });
        return filesArr;
      },
    },
  };
</script>
