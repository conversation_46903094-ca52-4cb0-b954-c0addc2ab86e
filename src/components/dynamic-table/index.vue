<template>
  <div class="table">
    <el-table
      v-loading="options.loading"
      :row-class-name="handleRowClassName"
      :data="dataSource"
      :max-height="options.maxHeight"
      :stripe="options.stripe"
      :border="options.border"
      :show-summary="options.showSummary || false"
      header-row-class-name="table-header-row"
      :span-method="handleArraySpanMethod"
      :sum-text="options.sumText || '汇总'"
      :summary-method="summaryMethod ? summaryMethod : null"
      @row-click="handleRowClick"
      @selection-change="handleSelectionChange"
      @header-click="handleHeaderClick"
    >
      <!--selection选择框-->
      <el-table-column
        v-if="options.mutiSelect"
        :selectable="handleCheckSelectable"
        type="selection"
        style="width: 50px"
        align="center"
      ></el-table-column>

      <!--序号-->
      <el-table-column
        v-if="options.index"
        :label="options.indexName || '序号'"
        type="index"
        width="50"
        align="center"
      ></el-table-column>
      <!--数据列-->
      <template v-for="(column, index) in columns">
        <el-table-column
          v-if="!column.hide"
          :key="column.key || index"
          :resizable="column.resizable"
          :prop="column.prop"
          :label="column.label"
          :align="column.align || 'center'"
          :width="column.width"
          :min-width="column.minWidth"
          :max-width="column.maxWidth"
          :fixed="column.fixed"
          :type="column.type"
          :show-overflow-tooltip="column.showOverflowTooltip"
        >
          <template slot-scope="scope">
            <template v-if="!column.render && !column.scopedSlots">
              {{ getText(scope.row, column) }}
            </template>

            <!-- render -->
            <template v-else-if="column.render">
              <RenderDom
                :row="scope.row"
                :index="index"
                :render="column.render"
              />
            </template>

            <!-- slot -->
            <template v-else-if="column.scopedSlots">
              <slot
                :name="column.scopedSlots.customRender"
                :row="scope.row"
                :$index="scope.$index"
              />
            </template>

            <!-- render button -->
            <template v-if="column.button" :width="column.width">
              <template v-for="(btn, i) in column.group">
                <el-button
                  :key="i"
                  :type="btn.type"
                  :size="btn.size || 'mini'"
                  :icon="btn.icon"
                  :disabled="btn.disabled"
                  :plain="btn.plain"
                  @click.stop="btn.onClick(scope.row, scope.$index)"
                >
                  {{ btn.name }}
                </el-button>
              </template>
            </template>
          </template>
        </el-table-column>
      </template>
      <!-- slot 之定义操作-->
      <slot name="table_operation" />
    </el-table>
    <!-- 分页 -->
    <el-pagination
      v-if="pagination && pagination.total > 0"
      ref="listPage"
      background
      :current-page.sync="pagination.pageSize"
      :page-size="pagination.pageLimit"
      :page-sizes="options.pageSize || [10, 20, 50, 100, 200]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      @current-change="handleIndexChange"
      @size-change="handleSizeChange"
    ></el-pagination>
  </div>
</template>

<script>
  import props from './propsTypes';
  export default {
    name: 'Table',
    components: {
      RenderDom: {
        functional: true, // 函数式组件 - 无 data 和 this 上下文 => better render
        props: {
          row: Object,
          index: Number,
          render: Function,
        },
        /**
         * @param {Function} createElement - 原生创建dom元素的方法， 弃用，推荐使用 jsx
         * @param {Object} ctx - 渲染的节点的this对象
         * @argument 传递参数 row index
         */
        render(createElement, ctx) {
          // console.log(createElement, 'createElement, ctx');
          const { row, index } = ctx.props;
          return ctx.props.render(row, index);
        },
      },
    },
    props,
    created() {
      // 传入的options覆盖默认设置
      this.$parent.options = Object.assign(
        {
          maxHeight: 500,
          stripe: true, // 是否为斑马纹
          border: false,
          loading: false,
        },
        this.options,
      );
    },
    methods: {
      getText(row, column) {
        if (column.prop.indexOf('.') != '-1') {
          const arr = column.prop.split('.');
          if (arr.length === 3) {
            return row[arr[0]] && [arr[1]] && row[arr[0]][arr[1]][arr[2]];
          }
          return row[arr[0]] && row[arr[0]][arr[1]];
        }
        return row[column.prop];
      },
      handleSizeChange(size) {
        // 切换每页显示的数量
        this.pagination.pageLimit = size;
        this.fetch && this.fetch();
      },
      handleIndexChange(current) {
        // 切换页码
        this.pagination.pageSize = current;
        this.fetch && this.fetch();
      },
      handleSelectionChange(selection) {
        this.$emit('selection-change', selection);
      },
      handleHeaderClick(column, event) {
        this.$emit('header-click', column, event);
      },
      handleRowClick(row, event, column) {
        this.$emit('row-click', row, event, column);
      },
      handleArraySpanMethod({ row, column, rowIndex, columnIndex }) {
        if (this.arraySpanMethod) {
          return this.arraySpanMethod({ row, column, rowIndex, columnIndex });
        }
      },

      handleRowClassName({ row, rowIndex }) {
        if (this.rowClassName) {
          return this.rowClassName({ row, rowIndex });
        }
      },
      handleSummaryMethod({ columns, data }) {
        if (this.summaryMethod) {
          return this.summaryMethod({ columns, data });
        }
      },
      handleCheckSelectable(row) {
        if (this.checkSelectable) {
          return this.checkSelectable(row);
        } else {
          return true;
        }
      },
    },
  };
</script>

<style lang="scss" scoped></style>
