const props = {
  dataSource: {
    type: Array,
    required: true,
    default: () => {
      return [];
    },
  },
  columns: {
    type: Array,
    required: true,
    default: () => {
      return [];
    },
  },
  pagination: {
    type: Object,
    default: null,
  },
  options: {
    type: Object,
    default: () => {
      return { loading: false };
    },
  },
  fetch: {
    type: Function,
    default: null,
  },
  checkSelectable: {
    type: Function,
    default: null,
  },
  arraySpanMethod: {
    type: Function,
    default: null,
  },
  rowClassName: {
    type: Function,
    default: null,
  },
  summaryMethod: {
    type: Function,
    default: null,
  },
};

export default props;
