<template>
  <el-select
    v-model="selectValue"
    v-selectLoadMore="loadmore"
    filterable
    :filter-method="searchName"
  >
    <el-option
      v-for="item in list"
      :key="item.value"
      :label="item.name"
      :value="item.value"
    ></el-option>
  </el-select>
</template>

<script>
  export default {
    name: 'SelectSearchLoadMore',
    model: {
      prop: 'value',
      event: 'change',
    },
    props: {
      value: {
        type: String,
        default: '',
      },
      api: {
        type: String,
        default: '',
      },
      fetch: {
        type: Function,
        default: null,
      },
      params: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        list: [],
        selectParams: {
          limit: 10,
          pageNo: 1,
        },
      };
    },
    computed: {
      selectValue: {
        get() {
          return this.value;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    created() {},

    methods: {
      loadmore() {
        this.selectParams.pageNo++;
        this.fetch && this.fetch();
      },
      searchName(val) {
        this.selectParams.pageNo = 1;
        this.selectParams.text = val;
        this.fetch && this.fetch();
      },
    },
  };
</script>

<style lang="scss" scoped></style>
