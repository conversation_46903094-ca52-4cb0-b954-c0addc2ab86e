<!--
 * @Author: bruce
 * @Date: 2023-07-17 10:25:09
 * @LastEditors: bruce
 * @FilePath: /access-fmis-web/src/components/dialog/batchDialog.vue
 * @Description: 
-->
<template>
  <el-dialog
    title="批量"
    :visible.sync="isShow"
    width="500px"
    :before-close="onClose"
  >
    <div style="margin-bottom: 10px">多个{{ text }}，每行一个{{ text }}</div>
    <el-form
      ref="ruleForm"
      :model="formData"
      :rules="rules"
      class="margin-top-10"
    >
      <el-form-item prop="batchId">
        <el-input
          v-model="formData.batchId"
          placeholder="请输入"
          type="textarea"
          :autosize="{ minRows: 3, maxRows: 6 }"
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="onClose">取 消</el-button>
      <el-button type="primary" @click="onGetInfo">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    name: 'BatchDialog',
    props: {
      id: {
        type: [String, Number],
        default: '',
      },
      text: {
        type: String,
        default: 'id',
      },
    },
    data() {
      let validatePass = (rule, value, callback) => {
        if (value === '') {
          callback(new Error(`${this.text}不能为空`));
        } else {
          let ids = this.formData.batchId.split('\n');
          if (ids.length > 40) {
            callback(new Error(`${this.text}最多一次为40个`));
          }
          callback();
        }
      };
      return {
        isShow: true,
        formData: {
          batchId: this.id?.replace(/,/g, '\n'),
        },
        rules: {
          batchId: [{ validator: validatePass, trigger: 'blur' }],
        },
      };
    },
    mounted() {},
    methods: {
      onClose() {
        this.$emit('complete');
      },
      onGetInfo() {
        this.$refs['ruleForm'].validate(valid => {
          if (valid) {
            const ids = this.formData.batchId.split('\n');
            this.$emit('complete');
            this.$emit('ok', ids);
          }
        });
      },
    },
  };
</script>
<style lang="scss" scoped></style>
