<template>
  <el-button
    v-if="nodeType === 'btn'"
    :slot="slotBtn"
    :type="type"
    :disabled="disabled"
    :size="size"
    :icon="icon"
    :plain="plain"
    :loading="loading"
    @click="handleClick"
  >
    {{ btnText }}
  </el-button>
  <el-popconfirm
    v-else
    :title="title"
    :confirm-button-text="confirmButtonText"
    :confirm-button-type="confirmButtonType"
    @confirm="handleClick"
  >
    <el-button
      :slot="slotBtn"
      :type="type"
      :disabled="disabled"
      :size="size"
      :plain="plain"
      :icon="icon"
    >
      {{ btnText }}
    </el-button>
  </el-popconfirm>
</template>
<script>
  export default {
    name: 'Ac<PERSON>ermissionButton',
    props: {
      confirmButtonText: {
        type: String,
        default: '确定',
      },
      confirmButtonType: {
        type: String,
        default: '',
      },
      title: {
        type: String,
        default: '',
      },
      nodeType: {
        type: String,
        default: 'btn',
      },
      type: {
        type: String,
        default: 'primary',
      },
      btnText: {
        type: String,
        default: '',
      },
      permissionKey: {
        type: String,
        default: '',
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      size: {
        type: String,
        default: '',
      },
      slotBtn: {
        type: String,
        default: '',
      },
      icon: {
        type: String,
        default: '',
      },
      plain: {
        type: Boolean,
        default: false,
      },
      loading: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {};
    },
    methods: {
      handleClick() {
        this.$store
          .dispatch('button/onGetButtonPermission', {
            buttonCode: this.permissionKey,
          })
          .then(res => {
            if (res) {
              // if (this.nodeType === 'btn') {
              //   this.$emit('click');
              // } else this.$emit('confirm');
              this.$emit('click');
            }
          });
      },
    },
  };
</script>
