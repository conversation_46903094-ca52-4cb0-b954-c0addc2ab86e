/*
 * @Description:
 * @Author: bruce
 * @Date: 2021-03-17 11:25:29
 * @LastEditTime: 2022-06-20 11:28:29
 * @LastEditors: xuxiang
 * @Reference:
 */
import path from 'path';
import { isExternal } from '@/utils/validate';
import { hasAccess } from '@/utils/hasAccess';
import { recordRoute } from '@/config';
import Layout from '@/amf/layouts';
import ParentView from '@/components/ParentView';

/**
 * @description all模式渲染后端返回路由,支持包含views路径的所有页面
 * @param asyncRoutes
 * @returns {*}
 */
export function convertRouter1(asyncRoutes) {
  return asyncRoutes.map(route => {
    if (route.component) {
      if (route.component === 'Layout') {
        route.component = resolve => require(['@/amf/layouts'], resolve);
      } else {
        const index = route.component.indexOf('views');
        const path =
          index > 0 ? route.component.slice(index) : `views/${route.component}`;
        route.component = resolve => require([`@/${path}`], resolve);
      }
    }
    if (route.children && route.children.length)
      route.children = convertRouter1(route.children);
    if (route.children && route.children.length === 0) delete route.children;
    return route;
  });
}

/**
 * @description all模式渲染后端返回路由,支持包含views路径的所有页面
 * @param asyncRoutes
 * @returns {*}
 */
/**
 * <AUTHOR>
 * @description all模式渲染后端返回路由转成挂在路由对应的字段
 * @param constantRoutes
 * @returns {*}
 */
// export function convertRouter(constantRoutes, parentUrl) {
//   const routerArr = ['salesMgt', 'supplyChainMgt'];
//   const routerList = constantRoutes.map(route => {
//     const { title, relativeId, name, icon, children, hidden } = route;
//     const routeName = relativeId;
//     const res = {
//       name,
//       path: routeName,
//       alwaysShow: true,
//       hidden: hidden != null && hidden !== 0,
//       meta: { title, remixIcon: icon, icon: icon },
//       menuId: route.id, // 菜单id,处理按钮权限使用
//     };
//     const componentUrl =
//       (parentUrl && !routerArr.includes(parentUrl) ? parentUrl + '/' : '') +
//       routeName;
//     // console.log(componentUrl, 'componentUrlcomponentUrl')
//     if (!parentUrl) {
//       res.component = Layout;
//     } else if (parentUrl && children.length > 0) {
//       // 有父级同时有子级
//       const redirect = `/${parentUrl}/${routeName}/${children[0].relativeId}`;
//       // console.log(redirect, 'redirectredirectredirect')
//       res.component = ParentView;
//       res.redirect = redirect;
//     } else {
//       res.component = resolve => require([`@/views/${componentUrl}`], resolve);
//     }
//     if (children && children.length > 0) {
//       res.children = convertRouter(children, componentUrl);
//     }
//     return res;
//   });

//   return routerList;
// }

export function convertRouter(constantRoutes) {
  const res = [];
  constantRoutes?.map(route => {
    let routeName = '';
    let routeNameList = route.name.split('-');
    let { relativeId, title, hidden, icon, extension } = route;
    let obj = {
      path: relativeId || '/',
      name:
        route.children && route.children.length > 0
          ? routeNameList[routeNameList.length - 1] + route.id
          : routeNameList[routeNameList.length - 1],
      meta: {
        title,
        hidden: hidden || false,
        remixIcon: icon,
        icon: icon,
      },
      caseSensitive: true, // 匹配规则是否大小写敏感
      redirect: '', //默认展开所有菜单
      alwaysShow: true, //一直显示根路由,只有一个子菜单时,父菜单和子菜单都显示
      hidden: hidden, // 设置true时,左侧菜单不显示
      menuId: route.id, // 菜单id,处理按钮权限使用
    };
    // const componentUrl = parentUrl ? parentUrl + '/' : '' + routeName;
    if (
      route.name.indexOf('http') === -1 &&
      route.name.indexOf('html') === -1
    ) {
      //非外链，外链直接配
      routeName = route.name.split('-').join('/');
      //{redirect: 重定向路径,menuLevel:几级路由,...}
      let extensionParse =
        typeof extension === 'string' ? JSON.parse(extension) : {};

      if (route.children && route.children.length > 0) {
        //有子菜单
        obj.component =
          route.name.indexOf('-') > 0
            ? resolve => require([`@/views/${routeName}`], resolve)
            : Layout;
        obj.componentPath = `@/views/${routeName}`;
        obj.children = convertRouter(route.children);
        // 多级路由
        if (
          extensionParse.menuLevel &&
          extensionParse.menuLevel >= 2 &&
          route.children.length > 0
        ) {
          obj.component = resolve => require([`@/views/Home.vue`], resolve);
          obj.componentPath = `@/views/Index`;
        }
        obj.redirect = extensionParse.redirect ? extensionParse.redirect : null;
      } else {
        obj.componentPath = `@/views/${routeName}`;
        obj.component = resolve => require([`@/views/${routeName}`], resolve);
      }
    } else {
      //外链
      obj.path = route.name;
    }

    res.push(obj);
  });
  // console.log(res, 'res00000');
  return res;
}

/**
 * @description 根据roles数组拦截路由
 * @param routes 路由
 * @param rolesControl 是否进行权限控制
 * @param baseUrl 基础路由
 * @returns {[]}
 */
export function filterRoutes(routes, rolesControl, baseUrl = '/') {
  return routes
    .filter(route => {
      if (rolesControl && route.meta && route.meta.roles)
        return hasAccess(route.meta.roles);
      else return true;
    })
    .map(route => {
      if (route.path !== '*' && !isExternal(route.path))
        route.path = path.resolve(baseUrl, route.path);
      if (route.children)
        route.children = filterRoutes(route.children, rolesControl, route.path);
      return route;
    });
}

/**
 * 根据当前route获取激活菜单
 * @param route 当前路由
 * @param isTabsBar 是否是标签
 * @returns {string|*}
 */
export function handleActivePath(route, isTabsBar = false) {
  const { meta, path, fullPath } = route;
  const rawPath = route.matched
    ? route.matched[route.matched.length - 1].path
    : path;
  if (isTabsBar) return meta.dynamicNewTab ? path : rawPath;
  if (meta.activeMenu) return meta.activeMenu;
  return path ? path : rawPath;
}

/**
 * 获取当前跳转登录页的Route
 * @param currentPath 当前页面地址
 */
export function toLoginRoute(currentPath) {
  if (recordRoute && currentPath !== '/')
    return {
      path: '/login',
      query: { redirect: currentPath },
      replace: true,
    };
  else return { path: '/login', replace: true };
}

/**
 * @description: 全路径title
 * @param  {*}
 * @return {*}
 * @param {*} matched
 */
export function handleMetaTitle(matched) {
  let metaTitleString = '';
  matched?.forEach((item, index) => {
    metaTitleString =
      index < matched.length - 1
        ? metaTitleString + item?.meta?.title + '-'
        : metaTitleString + item?.meta?.title;
  });
  return metaTitleString;
}
