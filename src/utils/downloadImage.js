function getBase64Image(img) {
  const canvas = document.createElement('canvas');
  canvas.width = img.width;
  canvas.height = img.height;
  const ctx = canvas.getContext('2d');
  ctx.drawImage(img, 0, 0, img.width, img.height);
  // 可选其他值 image/jpeg
  const dataURL = canvas.toDataURL('image/png');
  return dataURL;
}

function main(src, cb) {
  const image = new Image();
  // 处理缓存
  image.src = `${src}?v=${Math.random()}`;
  image.crossOrigin = '*'; // 支持跨域图片
  image.onload = () => {
    const base64 = getBase64Image(image);
    cb && cb(base64);
  };
}
function base64Img2Blob(code) {
  const parts = code.split(';base64,');
  const contentType = parts[0].split(':')[1];
  const raw = window.atob(parts[1]);
  const rawLength = raw.length;

  const uInt8Array = new Uint8Array(rawLength);

  for (let i = 0; i < rawLength; i += 1) {
    uInt8Array[i] = raw.charCodeAt(i);
  }

  return new Blob([uInt8Array], { type: contentType });
}

export function downloadImage(url) {
  main(url, base64 => {
    const blob = base64Img2Blob(base64);
    const aLink = document.createElement('a');
    aLink.href = window.URL.createObjectURL(blob);
    aLink.download = '封面.png';
    document.body.appendChild(aLink);
    aLink.click();
    document.body.removeChild(aLink);
  });
}
