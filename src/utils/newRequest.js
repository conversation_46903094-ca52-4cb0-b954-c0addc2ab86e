/*
 * @Description:
 * @Author: bruce
 * @Date: 2021-03-18 09:42:13
 * @LastEditTime: 2022-11-18 16:31:08
 * @LastEditors: dddd
 * @Reference:
 */
import Vue from 'vue';
import { request } from '@access/core';
import {
  contentType,
  debounce,
  invalidCode,
  noRoleCode,
} from '@/config/setting.config.js';
import { requestTimeout } from '@/config/net.config.js';
import store from '@/store';
import qs from 'qs';
import { downloadBlob2 } from '@/utils/index.js';
import injectHost from '@/utils/injectHost';
import { replaceLocalDomain } from '@/utils/index.js';
import apiList from '@/api/index.js';

let loadingInstance;

const BASE_URL = injectHost().apiHost;

/**
 * <AUTHOR> <EMAIL>
 * @description 判断当前url是否需要加loading
 * @param {*} config
 * @returns
 */
const needLoading = config => {
  let status = false;
  debounce.forEach(item => {
    if (Vue.prototype.$baseLodash.includes(config.url, item)) {
      status = true;
    }
  });
  return status;
};

/**
 * <AUTHOR> <EMAIL>
 * @description 处理code异常,202501021-token被重置;202501019-token无效;202501020-用户id为空;-1233-token校验非法
 * @param {*} code
 * @param {*} msg
 */
const handleCode = (code, msg) => {
  switch (code) {
    case invalidCode:
      Vue.prototype.$baseMessage(msg || `后端接口${code}异常`, 'error');
      store.dispatch('user/resetAll').catch(() => {});
      break;
    case noRoleCode:
    case 202501021:
    case 202501019:
    case 202501020:
    case -1233:
      // removeCookie();
      // jumpToSSO();
      break;
    default:
      Vue.prototype.$baseMessage(msg || `后端接口${code}异常`, 'error');
      break;
  }
};

let baseUrl = '';
let href = location.href;
if (href.indexOf('-local') > -1 && href.indexOf('?isServer') > -1) {
  //后端对本地服务
  baseUrl = 'http://localhost:8080/api';
} else {
  //前端对接服务
  baseUrl = `//${replaceLocalDomain(BASE_URL)}/api`;
}

const settings = {
  config: {
    baseURL: baseUrl,
    timeout: requestTimeout,
    headers: {
      'Content-Type': contentType,
      // token,
      appCode: process.env.VUE_APP_LOGIN_APP_CODE,
    },
  },
  apiList,
  reqHook(config) {
    const token = store.getters['user/token'];
    if (token) {
      config.headers.token = token;
    }
    if (
      contentType === 'application/x-www-form-urlencoded;charset=UTF-8' &&
      config.data
    ) {
      config.data = qs.stringify(config.data);
    }

    if (needLoading(config)) {
      loadingInstance = Vue.prototype.$baseLoading();
    }
  },
  resHook(response) {
    if (loadingInstance) loadingInstance.close();
    if (response.status === 200) {
      const responseType = response.request.responseType;
      if (responseType == '') {
        const { code, data, msg } = response.data;
        if (code !== 0) {
          handleCode(code, msg);
          throw { code, msg };
        } else {
          return data;
        }
      } else if (responseType === 'blob') {
        // 下载文件
        const fileType = response.config.headers?.fileType || '.csv';
        const fileName = response.config.headers?.fileName;
        downloadBlob2(response.data, fileType, fileName);
      }
    } else {
      handleCode(response.status, response.statusText);
      throw {
        code: response.status,
        msg: response.statusText,
      };
    }
  },
  resErrHook(error) {
    if (loadingInstance) loadingInstance.close();
    const { response, message } = error;
    if (error.response && error.response.data) {
      const { status, data } = response;
      handleCode(status, data.msg || message);
      return Promise.reject(error);
    } else {
      let { message } = error;
      if (message === 'Network Error') {
        message = '后端接口连接异常';
      }
      if (message.includes('timeout')) {
        message = '后端接口请求超时';
      }
      if (message.includes('Request failed with status code')) {
        const code = message.substr(message.length - 3);
        message = '后端接口' + code + '异常';
      }
      Vue.prototype.$baseMessage(message || `后端接口未知异常`, 'error');
      return Promise.reject(error);
    }
  },
};
const instance = new request(settings);
export default instance;
