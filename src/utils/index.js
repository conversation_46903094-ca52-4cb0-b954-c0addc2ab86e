import { saveAs } from 'file-saver';
import { replaceDomain } from '@/config';

/**
 * @description 格式化时间
 * @param time
 * @param cFormat
 * @returns {string|null}
 */
export function parseTime(time, cFormat) {
  if (!time) return '';
  time =
    typeof time === 'string'
      ? time.replace(/T/g, ' ').replace(/-/g, '/')
      : time;

  if (arguments.length === 0) {
    return null;
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}';
  let date;
  if (typeof time === 'object') {
    date = time;
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time);
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  return format.replace(/{([ymdhisa])+}/g, (result, key) => {
    let value = formatObj[key];
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value];
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value;
    }
    return value || 0;
  });
}

/**
 * @description 格式化时间
 * @param time
 * @param option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000;
  } else {
    time = +time;
  }
  const d = new Date(time);
  const now = Date.now();

  const diff = (now - d) / 1000;

  if (diff < 30) {
    return '刚刚';
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前';
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前';
  } else if (diff < 3600 * 24 * 2) {
    return '1天前';
  }
  if (option) {
    return parseTime(time, option);
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    );
  }
}

/**
 * @description 将url请求参数转为json格式
 * @param url
 * @returns {{}|any}
 */
export function paramObj(url) {
  const search = url.split('?')[1];
  if (!search) {
    return {};
  }
  return JSON.parse(
    '{"' +
      decodeURIComponent(search)
        .replace(/"/g, '\\"')
        .replace(/&/g, '","')
        .replace(/=/g, '":"')
        .replace(/\+/g, ' ') +
      '"}',
  );
}

/**
 * @description 父子关系的数组转换成树形结构数据
 * @param data
 * @returns {*}
 */
export function translateDataToTree(data) {
  const parent = data.filter(
    value => value.parentId === 'undefined' || value.parentId == null,
  );
  const children = data.filter(
    value => value.parentId !== 'undefined' && value.parentId !== null,
  );
  const translator = (parent, children) => {
    parent.forEach(parent => {
      children.forEach((current, index) => {
        if (current.parentId === parent.id) {
          const temp = JSON.parse(JSON.stringify(children));
          temp.splice(index, 1);
          translator([current], temp);
          typeof parent.children !== 'undefined'
            ? parent.children.push(current)
            : (parent.children = [current]);
        }
      });
    });
  };
  translator(parent, children);
  return parent;
}

/**
 * @description 树形结构数据转换成父子关系的数组
 * @param data
 * @returns {[]}
 */
export function translateTreeToData(data) {
  const result = [];
  data.forEach(item => {
    const loop = data => {
      result.push({
        id: data.id,
        name: data.name,
        parentId: data.parentId,
      });
      const child = data.children;
      if (child) {
        for (let i = 0; i < child.length; i++) {
          loop(child[i]);
        }
      }
    };
    loop(item);
  });
  return result;
}

/**
 * @description 10位时间戳转换
 * @param time
 * @returns {string}
 */
export function tenBitTimestamp(time) {
  const date = time ? time : new Date();
  const y = date.getFullYear();
  let m = date.getMonth() + 1;
  m = m < 10 ? '0' + m : m;
  let d = date.getDate();
  d = d < 10 ? '0' + d : d;
  let h = date.getHours();
  h = h < 10 ? '0' + h : h;
  let minute = date.getMinutes();
  let second = date.getSeconds();
  minute = minute < 10 ? '0' + minute : minute;
  second = second < 10 ? '0' + second : second;
  return y + '-' + m + '-' + d + ' ' + h + ':' + minute + ':' + second; //组合
}

/**
 * @description 13位时间戳转换
 * @param time
 * @returns {string}
 */
export function thirteenBitTimestamp(time) {
  const date = new Date(time / 1);
  const y = date.getFullYear();
  let m = date.getMonth() + 1;
  m = m < 10 ? '' + m : m;
  let d = date.getDate();
  d = d < 10 ? '' + d : d;
  let h = date.getHours();
  h = h < 10 ? '0' + h : h;
  let minute = date.getMinutes();
  let second = date.getSeconds();
  minute = minute < 10 ? '0' + minute : minute;
  second = second < 10 ? '0' + second : second;
  return y + '年' + m + '月' + d + '日 ' + h + ':' + minute + ':' + second; //组合
}

/**
 * @description 获取随机id
 * @param length
 * @returns {string}
 */
export function uuid(length = 32) {
  const num = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890';
  let str = '';
  for (let i = 0; i < length; i++) {
    str += num.charAt(Math.floor(Math.random() * num.length));
  }
  return str;
}

/**
 * @description m到n的随机数
 * @param m
 * @param n
 * @returns {number}
 */
export function random(m, n) {
  return Math.floor(Math.random() * (m - n) + n);
}

/**
 * @description addEventListener
 * @type {function(...[*]=)}
 */
export const on = (function () {
  return function (element, event, handler, useCapture = false) {
    if (element && event && handler) {
      element.addEventListener(event, handler, useCapture);
    }
  };
})();

/**
 * @description removeEventListener
 * @type {function(...[*]=)}
 */
export const off = (function () {
  return function (element, event, handler, useCapture = false) {
    if (element && event) {
      element.removeEventListener(event, handler, useCapture);
    }
  };
})();

/**
 * @description 数组打乱
 * @param array
 * @returns {*}
 */
export function shuffle(array) {
  let m = array.length,
    t,
    i;
  while (m) {
    i = Math.floor(Math.random() * m--);
    t = array[m];
    array[m] = array[i];
    array[i] = t;
  }
  return array;
}

/**
 * @description: 导出文件
 * @param  {*}
 * @return {*}
 */
export function downloadBlob2(data, fileType, name) {
  const csvBlob = new Blob([data]);
  let fileName = `${new Date().toLocaleDateString()}${fileType}`;
  if (name && name !== '') {
    fileName = decodeURIComponent(name);
  }
  saveAs(csvBlob, fileName);
}

/**
 * @description: 导出文件
 * @param  {*}
 * @return {*}
 */
export function downloadBlob(data, fileType) {
  const csvBlob = new Blob([data]);
  const fileName = `${new Date().toLocaleDateString()}${fileType}`;
  saveAs(csvBlob, fileName);
}

/**
 * <AUTHOR>
 * @description 多维数组查询符合条件的元素
 * @type {function(...[*]=)}
 */
export function getObjByPropty(list, id, propty) {
  if (!list.length) {
    return null;
  } //遍历数组

  for (let i in list) {
    let item = list[i];
    if (item[propty] == id) {
      return item;
    } else {
      //查不到继续遍历
      if (item.children) {
        let value = getObjByPropty(item.children, id, propty);
        if (value) {
          return value;
        }
      }
    }
  }
}

/**
 * <AUTHOR>
 * @description 替换本地域名
 * @type {function(...[*]=)}
 */
export function replaceLocalDomain(apiHost) {
  let newApiHost = '';
  if (location.href.indexOf('-local') > -1 && replaceDomain) {
    newApiHost = apiHost.replace('.danchuangglobal.com', replaceDomain);
  } else {
    newApiHost = apiHost;
  }
  return newApiHost;
}

export function objValueHaveFalse(obj) {
  if (!obj) return true;
  if (obj.constructor === Object) {
    const values = Object.values(obj);
    return values.some(i => !i);
  }
}

export function downloadFile(content, filename = '下载', extension = 'xlsx') {
  const blob = new Blob([content], { type: `application/${extension}` });
  const url = window.URL || window.webkitURL || window.mozURL;
  let downloadElement = document.createElement('a');
  let href = url.createObjectURL(blob);
  downloadElement.href = href;
  downloadElement.download = `${filename}.${extension}`;
  document.body.appendChild(downloadElement);
  downloadElement.click();
  // 下载完成移除元素
  document.body.removeChild(downloadElement);
  // 释放掉blob对象
  window.URL.revokeObjectURL(href);
}

export function downloadExcel(url, filename = '下载', extension = 'xls') {
  let downloadElement = document.createElement('a');
  downloadElement.href = url;
  downloadElement.rel = 'noopener noreferrer';
  downloadElement.download = `${filename}.${extension}`;
  document.body.appendChild(downloadElement);
  downloadElement.click();
  // 下载完成移除元素
  document.body.removeChild(downloadElement);
}

export function generateRandomApplyNo() {
  // K+YYMMDDHHMMSS(时分秒)+2位随机数
  const date = new Date();
  const YY = date.getFullYear().toString().slice(-2);
  const MM = addDouble(date.getMonth() + 1);
  const DD = addDouble(new Date().getDate());
  const HH = addDouble(new Date().getHours());
  const mm = addDouble(new Date().getMinutes());
  const ss = addDouble(new Date().getSeconds());
  const randomNum = parseInt(Math.random() * 100);
  const num = addDouble(randomNum);
  return `K${YY}${MM}${DD}${HH}${mm}${ss}${num}`;
}

function addDouble(num) {
  const numStr = num.toString();
  if (numStr.length < 2) {
    return '0' + numStr;
  }
  return numStr;
}

export function initSearchParams(params) {
  const args = {};
  for (const key in params) {
    if (
      typeof params[key] === 'undefined' ||
      params[key] === null ||
      params[key] === ''
    )
      continue;
    if (params.hasOwnProperty(key)) args[key] = params[key];
  }
  return args;
}

export function setInitData(date = 7, cFormat) {
  const end = new Date();
  const start = new Date();
  start.setTime(start.getTime() - 3600 * 1000 * 24 * date);
  const initDate = [
    parseTime(start, cFormat || '{y}-{m}-{d} 00:00:00'),
    parseTime(end, cFormat || '{y}-{m}-{d} 23:59:59'),
  ];
  return initDate;
}

/**
 * 函数防抖 立即执行
 * @param fn
 * @param delay
 * @returns {Function}
 * @constructor
 */
export const debounce = (fn, wait) => {
  let timerId = null;
  let flag = true;
  return function () {
    const context = this;
    const args = arguments;
    clearTimeout(timerId);
    if (flag) {
      fn.apply(context, args);
      flag = false;
    }
    timerId = setTimeout(() => {
      flag = true;
    }, wait);
  };
};

/**
 * 函数节流
 * @param func
 * @param wait
 * @returns {function(...[*]=)}
 */
export const throttle = (func, wait = 300) => {
  let timeout;
  return function () {
    let context = this;
    let args = arguments;
    if (!timeout) {
      timeout = setTimeout(() => {
        timeout = null;
        func.apply(context, args);
      }, wait);
    }
  };
};

/**
 * 获取到每月的第一天和最后一天 需要传入一个月份 如果忘记传入 取当前月份
 */
export const getMonthFirstOrLaseDay = month => {
  month = month || new Date().getMonth();
  const nowdays = new Date();
  const year = nowdays.getFullYear();

  if (month == 0) {
    month = 12;
    year = year - 1;
  }
  if (month.length === 1) {
    month = '0' + month;
  }
  const firstDay = year + '-' + month + '-' + '01';
  const myDate = new Date(year, month, 0);
  const lastDay = year + '-' + month + '-' + myDate.getDate();
  return { firstDay: firstDay, lastDay: lastDay };
};

/**
 * 获取提现周期 如: 12月13号-1月12号属于2022-01的周期
 */
export const getWithdrawalCycle = () => {
  const date = new Date();
  let y = date.getFullYear();
  let m = date.getMonth() + 1;
  let d = date.getDate();
  if (d >= 13) {
    if (m < 12) {
      m += 1;
    } else {
      //12月 进1
      y += 1;
      m = 1;
    }
  }
  m = m < 10 ? '0' + m : m;
  return `${y}-${m}`;
};

export const uniqueArrObj = (arr = [], id) => {
  if (!Array.isArray(arr) || arr.length === 0) return [];
  const obj = {};
  const list = arr.reduce(function (item, next) {
    obj[next[id]] ? '' : (obj[next[id]] = true && item.push(next));
    return item;
  }, []);
  return list;
};

export const getApiHost = (name, domain) => {
  const env = process.env.VUE_APP_BUILD_MODE;
  return env === 'pro'
    ? `https://${name}${domain}/api/`
    : `https://${name}-${env}${domain}/api/`;
};
