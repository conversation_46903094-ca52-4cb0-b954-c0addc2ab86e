/*
 * @Description:
 * @Author: bruce
 * @Date: 2021-03-22 14:11:43
 * @LastEditTime: 2021-03-22 14:50:27
 * @LastEditors: bruce
 * @Reference:
 */
import { utils } from '@access/core';
import { apiKey } from '@/config';

export default function injectHost() {
  if (window.ACCESS_HOSTS) {
    return window.ACCESS_HOSTS;
  }

  const env = process.env.VUE_APP_BUILD_MODE;
  return utils.parseLocationHost(window, env, apiKey);
}
