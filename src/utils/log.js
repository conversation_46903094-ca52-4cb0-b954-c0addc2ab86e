/*
 * @Description: 数据上传二次封装
 * @Author: bruce
 * @Date: 2021-09-09 15:43:51
 * @LastEditTime: 2021-09-16 09:56:51
 * @LastEditors: bruce
 * @Reference:
 */
import { utils } from '@access/core';
import store from '@/store';
import { handleMetaTitle } from '@/utils/routes';
const { slsSdk } = utils;

// web-admin埋点
export default function log(type, params, route) {
  let userInfo = {};
  if (store?.getters['user/userInfo']?.id) {
    const { realName, nickname, jobNumber, id, email } = store.getters[
      'user/userInfo'
    ];

    userInfo = {
      realName: realName,
      nickname: nickname,
      jobNumber: jobNumber,
      user_id: id,
      email: email,
    };
  }
  params.page = {
    page_name: handleMetaTitle(route?.matched),
    page_id: route?.path,
    page_type: 'web',

    page_title: route?.meta?.title,
  };
  // console.log('params===', params);
  slsSdk.webBigDataMd(type, params, userInfo);
}
