/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-08-17 16:45:50
 * @LastEditors: zhangwenyuan
 * @LastEditTime: 2021-09-09 14:49:01
 * @FilePath: /access-fmis-web/src/utils/math.js
 * @Description:
 */
/**
 * 不丢失精度的减法
 */
export const subtraction = (arg1, arg2) => {
  let r1, r2, m, n;
  try {
    r1 = arg1.toString().split('.')[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split('.')[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2)); //last modify by deeka //动态控制精度长度
  n = r1 >= r2 ? r1 : r2;
  return ((arg1 * m - arg2 * m) / m).toFixed(n);
};

export function keepTwoDecimalFull(num) {
  const result = parseFloat(num);
  if (isNaN(result)) {
    return 0;
  }
  const re = /([0-9]+.[0-9]{2})[0-9]*/;
  const newNum = String(result).replace(re, '$1');
  return parseFloat(newNum);
}
//加法
export function add(arg1, arg2) {
  var r1, r2, m, n;
  try {
    r1 = arg1.toString().split('.')[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split('.')[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2));
  n = r1 >= r2 ? r1 : r2;
  return ((arg1 * m + arg2 * m) / m).toFixed(n);
}

export function changeTwoDecimal(x) {
  let f_x = parseFloat(x);
  if (isNaN(f_x)) {
    return 0;
  }
  f_x = Math.round(x * 100) / 100;
  let s_x = f_x.toString();
  let pos_decimal = s_x.indexOf('.');
  if (pos_decimal < 0) {
    pos_decimal = s_x.length;
    s_x += '.';
  }
  while (s_x.length <= pos_decimal + 2) {
    s_x += '0';
  }
  return s_x;
}

export default { subtraction, keepTwoDecimalFull, changeTwoDecimal };
