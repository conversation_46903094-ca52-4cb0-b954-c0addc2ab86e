import Vue from 'vue';
import { request } from '@access/core';
import injectHost from '@/utils/injectHost';
import {
  contentType,
  debounce,
  invalidCode,
  noRoleCode,
  tokenName,
} from '@/config/setting.config';
import { requestTimeout } from '@/config/net.config';
import store from '@/store';
import qs from 'qs';
import router from '@/router';
import { jumpToSSO, getCookie } from '@/utils/auth';
import { queryString } from '@/utils/stringUtils';

let loadingInstance;

/**
 * <AUTHOR> <EMAIL>
 * @description 判断当前url是否需要加loading
 * @param {*} config
 * @returns
 */
const needLoading = config => {
  let status = false;
  debounce.forEach(item => {
    if (Vue.prototype.$baseLodash.includes(config.url, item)) {
      status = true;
    }
  });
  return status;
};

/**
 * <AUTHOR> <EMAIL>
 * @description 处理code异常
 * @param {*} code
 * @param {*} msg
 */
const handleCode = (code, msg) => {
  switch (code) {
    case invalidCode:
      Vue.prototype.$baseMessage(msg || `后端接口${code}异常`, 'error');
      store.dispatch('user/resetAll').catch(() => {});
      break;
    case noRoleCode:
      // jumpToSSO();
      router.push({ path: '/401' }).catch(() => {});
      break;
    default:
      Vue.prototype.$baseMessage(msg || `后端接口${code}异常`, 'error');
      break;
  }
};

const settings = {
  config: {
    baseURL: window.location.protocol + '//' + injectHost().apiHost + '/api',
    timeout: requestTimeout,
    headers: {
      'Content-Type': contentType,
      appCode: process.env.VUE_APP_LOGIN_APP_CODE,
      token: getCookie(),
    },
    //withCredentials: true,
  },
  reqHook(config) {
    let Base64 = require('js-base64').Base64;
    const username = store.state.user.username;
    const userId = store.state.user.userInfo.id;
    if (username) {
      config.headers['userName'] = Base64.encode(username);
      config.headers['userId'] = userId;
    }
    if (store.getters['user/accessToken']) {
      config.headers[tokenName] = store.getters['user/accessToken'];
    }

    if (
      contentType === 'application/x-www-form-urlencoded;charset=UTF-8' &&
      config.data
    ) {
      config.data = qs.stringify(config.data);
    }

    if (needLoading(config)) {
      loadingInstance = Vue.prototype.$baseLoading();
    }
  },
  resHook(response, enableResError) {
    const responseURL = response.request.responseURL;
    // 成功请求上传
    if (responseURL.indexOf('sso-auth') === -1) {
      let currentRouteInfo = store?.getters['routes/currentRouteInfo'];
      Vue.prototype.$log(
        'request',
        {
          business: { ...response },
        },
        currentRouteInfo,
      );
    }

    if (loadingInstance) loadingInstance.close();

    if (response.status === 200) {
      const { code, data, msg } = response.data;
      if (code !== 0) {
        if (enableResError) {
          handleCode(code, msg);
        }
        if ([202501019, 202501020, 202501021, 202501022].includes(code)) {
          jumpToSSO();
        }
        throw { code, msg };
      } else {
        return data;
      }
    } else {
      if (enableResError) {
        handleCode(response.status, response.statusText);
      }
      if (response.status === 401) {
        window.location.href = '/401';
      }
      throw Error({
        code: response.status,
        msg: response.statusText,
      });
    }
  },
  resErrHook(error, enableResError) {
    if (loadingInstance) loadingInstance.close();
    const { response, message } = error;
    // 错误请求上传
    let currentRouteInfo = store?.getters['routes/currentRouteInfo'];
    Vue.prototype.$log(
      'request',
      {
        business: { ...message },
      },
      currentRouteInfo,
    );
    if (error.response && error.response.data) {
      const { status, data } = response;
      if (enableResError) {
        handleCode(status, data.msg || message);
      }
      throw Error(error);
    } else {
      let { message } = error;
      if (message === 'Network Error') {
        message = '后端接口连接异常';
      }
      if (message.includes('timeout')) {
        message = '后端接口请求超时';
      }
      if (message.includes('Request failed with status code')) {
        const code = message.substr(message.length - 3);
        message = '后端接口' + code + '异常';
      }
      Vue.prototype.$baseMessage(message || `后端接口未知异常`, 'error');
      throw Error(error);
    }
  },
};
const instance = new request(settings);
const axios = params => {
  return instance.http
    .request(params)
    .then(res => {
      if (res.err) {
        return Promise.reject(res.err);
      }
      return res.res;
    })
    .catch(error => {
      return Promise.reject(error);
    });
};

const urlParamsReg = new RegExp(/(:([a-zA-Z0-9-_]{1,}))/g);
function requestCreator(method, url, params, body, processError = true) {
  // 移除 undefined params
  if (params) {
    Object.keys(params).forEach(key => {
      if (params[key] === undefined) {
        delete params[key];
      }
    });
  }

  const deleteParamsKeys = [];
  let parseUrl = url;

  if (urlParamsReg.test(url)) {
    // 替换
    parseUrl = url.replace(urlParamsReg, (rk, ____, k) => {
      if (k != null && k.length > 0) {
        deleteParamsKeys.push(k);
      }
      return params[k] || rk;
    });
    deleteParamsKeys.forEach(key => delete params[key]);
  }

  const request = {
    method,
    url: queryString(parseUrl, params),
  };
  if (body) {
    request.data = body;
  }
  const service = processError
    ? instance.http(request)
    : instance.httpNoneResErr(request);
  // return service.then(({ err, res }) => {
  //   if (err) {
  //     return Promise.reject(err)
  //   }
  //   return res
  // })
  return service;
}

export const service = {
  post: (url, params, data, processError) =>
    requestCreator('POST', url, params, data, processError),
  put: (url, params, data, processError) =>
    requestCreator('PUT', url, params, data, processError),
  get: (url, params, processError) =>
    requestCreator('GET', url, params, processError),
  delete: (url, params, processError) =>
    requestCreator('DELETE', url, params, processError),
  instance,
};

export default axios;
