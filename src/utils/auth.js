/*
 * @Description:
 * @Author: bruce
 * @Date: 2021-03-17 15:20:17
 * @LastEditTime: 2022-06-20 14:37:53
 * @LastEditors: xuxiang
 * @Reference:
 */
import Cookies from 'js-cookie';
import { tokenTableName } from '@/config/setting.config';
const TokenKey = tokenTableName;
import injectHost from '@/utils/injectHost';

/**
 * 获取Cookie, 默认为TokenKey
 */
export const getCookie = (key = TokenKey) => {
  return Cookies.get(key);
};
/**
 * 设置Cookie, 默认为TokenKey
 * @param {*} value
 */
export const setCookie = (value, key = TokenKey) => {
  Cookies.set(key, value);
};
/**
 * 删除Cookie, 默认为TokenKey
 */
export const removeCookie = (domain, key = TokenKey) => {
  Cookies.remove(TokenKey, { domain: injectHost().domain });
};

/**
 * 获取appCode
 */

export function appCode() {
  return process.env.VUE_APP_LOGIN_APP_CODE;
}

/**
 * 跳转sso
 */
export const jumpToSSO = () => {
  // removeCookie();
  // let url = '';
  // if (location.href.indexOf('?') > -1) {
  //   url = location.href.split('?')[0];
  // } else {
  //   url = location.href;
  // }
  // const appCode = process.env.VUE_APP_LOGIN_APP_CODE;
  // const SSO_LOGIN =
  //   window.location.protocol +
  //   '//' +
  //   window.ACCESS_HOSTS.ssoHost +
  //   '/#/login?backurl=';
  // window.location.replace(
  //   SSO_LOGIN + encodeURIComponent(url) + '&appCode=' + appCode,
  // );
  removeCookie();
  let encodeUrl = '';
  if (
    window.location.href.indexOf('?') > -1 &&
    window.ACCESS_HOSTS.domain.indexOf('danchuangglobal') === -1
  ) {
    // 包含有参数,multiEncode-标记多次encode
    encodeUrl = encodeURIComponent(
      `${encodeURIComponent(window.location.href)}&multiEncode=1`,
    );
  } else {
    encodeUrl = encodeURIComponent(window.location.href);
  }

  let ssoUrl =
    `//${window.ACCESS_HOSTS.ssoHost}/#/login?backurl=` +
    encodeUrl +
    '&appCode=' +
    process.env.VUE_APP_LOGIN_APP_CODE;

  if (window.ACCESS_HOSTS.domain.indexOf('danchuangglobal') > -1) {
    window.location.href = ssoUrl;
  } else {
    let ssoJumpUrl = ['pre', 'pro'].includes(process.env.VUE_APP_BUILD_MODE)
      ? `//sso-jumper.danchuangglobal.com`
      : `//sso-jumper-${process.env.VUE_APP_BUILD_MODE}.danchuangglobal.com`;
    location.href =
      ssoJumpUrl +
      `?origin=${encodeURIComponent(
        ssoUrl,
      )}&domain=.danchuangglobal.com&isDeleteCookie=1`;
  }
};

export default {
  getCookie,
  setCookie,
  removeCookie,
  jumpToSSO,
};
