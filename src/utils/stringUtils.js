export const query = search => {
  const str = search || window.location.search;
  const objURL = {};

  str.replace(new RegExp('([^?=&]+)(=([^&]*))?', 'g'), ($0, $1, $2, $3) => {
    objURL[$1] = $3;
  });
  return objURL;
};

export const queryString = (url, queryObj) => {
  if (queryObj == null || Object.keys(queryObj).length === 0) {
    return url;
  }
  const str = [];
  Object.keys(queryObj).forEach(key => {
    str.push(`${key}=${queryObj[key]}`);
  });
  const paramStr = str.join('&');
  return paramStr
    ? url.indexOf('?') > -1
      ? `${url}&${paramStr}`
      : `${url}?${paramStr}`
    : url;
};

export const removeSpace = str => {
  if (typeof str !== 'string') {
    return str;
  }
  if (str == null || str.length === 0) {
    return str;
  }
  return str.replace(/ /g, '');
};

export default {
  query,
  queryString,
  removeSpace,
};
