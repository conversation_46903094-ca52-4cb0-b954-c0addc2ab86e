export const paginationMixin = {
  data() {
    return {
      m_current: 1,
      m_pageSize: 10,
      m_total: 0,
    };
  },
  methods: {
    m_loadData() {},
    m_handleSizeChange(size) {
      this.m_pageSize = size;
      this.m_loadData();
    },
    m_handleCurrentChange(current) {
      this.m_current = current;
      this.m_loadData();
    },
  },
};

export default {
  paginationMixin,
};
