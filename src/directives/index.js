/*
 * @Description:自定义指令
 * @Author: bruce
 * @Date: 2021-04-08 10:50:32
 * @LastEditTime: 2022-11-18 16:28:16
 * @LastEditors: dddd
 * @Reference:
 */
const files = require.context('./module', false, /\.js$/);
const directives = {};
files.keys().forEach(key => {
  directives[key.replace(/(\.\/|\.js)/g, '')] = files(key).default;
});

export default {
  install(Vue) {
    Object.keys(directives).forEach(key => {
      Vue.directive(key, directives[key]);
    });
  },
};
