/*
 * @Description:输入框不允许输入特殊字符
 * @Author: 刘攀
 * @Date: 2021-05-21 15:07:14
 * @LastEditTime: 2021-05-21 15:08:57
 * @LastEditors: 刘攀
 * @Reference:
 */

const filterSpecialChar = {
  update: function (el, { value, modifiers }, vnode) {
    try {
      let newval = value.replace(/[^\a-\z\A-\Z0-9\u4E00-\u9FA5]/g, '');
      if (value !== newval) {
        el.children[0].value = newval;
        el.children[0].dispatchEvent(
          new Event(modifiers.lazy ? 'change' : 'input'),
        );
      }
    } catch (e) {
      console.log(e);
    }
  },
};

export default filterSpecialChar;
