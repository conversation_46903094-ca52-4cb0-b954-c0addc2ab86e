/*
 * @Description: 控制按钮的显示与隐藏
 * @Author: bruce
 * @Date: 2021-04-08 10:53:36
 * @LastEditTime: 2021-05-21 10:12:50
 * @LastEditors: 刘攀
 * @Reference:
 */
import store from '@/store';
const has = {
  bind: el => {
    el.style.display = 'none';
  },
  inserted: (el, binding, vnode) => {
    const value = binding.value;
    //获取权限按钮的异步在自定义指令之后，因此加了setTimeout
    setTimeout(() => {
      const permissionBtnList = store.getters['button/permissionButtonList'];

      if (!permissionBtnList.some(item => item.buttonCode === value)) {
        el.parentNode && el.parentNode.removeChild(el);
      } else {
        el.style.display = '';
      }
    }, 500);
  },
};

export default has;
