/*
 * @Description: 下拉加载更多
 * @Author: bruce
 * @Date: 2021-04-08 10:53:36
 * @LastEditTime: 2021-05-21 10:12:50
 * @LastEditors: 徐祥
 * @Reference:
 */
const selectLoadMore = {
  bind: function (el, binding) {
    try {
      const selectDom = el.querySelector(
        '.el-select-dropdown .el-select-dropdown__wrap',
      );
      selectDom.addEventListener('scroll', function () {
        const isEnd = this.scrollHeight - this.scrollTop <= this.clientHeight;
        if (isEnd) {
          console.log(binding, 'binding888');
        }
      });
    } catch (e) {
      console.log(e);
    }
  },
};

export default selectLoadMore;
