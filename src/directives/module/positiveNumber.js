/*
 * @Description:只允许正数，小数点控制位数
 * @Author: 刘攀
 * @Date: 2021-05-21 15:17:47
 * @LastEditTime: 2022-11-18 16:30:42
 * @LastEditors: dddd
 * @Reference:
 */
let findEle = (parent, type) => {
  return parent.tagName.toLowerCase() === type
    ? parent
    : parent.querySelector(type);
};

const trigger = (el, type) => {
  const e = document.createEvent('HTMLEvents');
  e.initEvent(type, true, true);
  el.dispatchEvent(e);
};

const positiveNumber = {
  bind: function (el, binding, vnode) {
    const { precision, data } = binding.value;
    let $inp = findEle(el, 'input');
    el.$inp = $inp;

    $inp.handle = function () {
      let val = $inp.value;

      // 输入框只能输入纯数字与小数点(支持小数点后两位数)
      let pattern = new RegExp(
        `^\\D*(\\d*(?:\\.\\d{0,${precision}})?).*$`,
        'g',
      );
      // let pattern = /^\D*(\d*(?:\.\d{0,2})?).*$/g;

      $inp.value = val.replace(pattern, '$1');

      trigger($inp, 'input');
    };
    $inp.addEventListener('keyup', $inp.handle);
  },
  unbind: function (el) {
    el.$inp.removeEventListener('keyup', el.$inp.handle);
  },
};

export default positiveNumber;
