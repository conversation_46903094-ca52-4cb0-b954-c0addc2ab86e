/*
 * @Description:只允许输入数字和字母
 * @Author: 刘攀
 * @Date: 2021-05-21 15:12:16
 * @LastEditTime: 2021-05-21 15:15:53
 * @LastEditors: 刘攀
 * @Reference:
 */
const alphabet = {
  update: function (el, { value, modifiers }, vnode) {
    try {
      let newval = value.replace(/[^\a-\z\A-\Z0-9]/g, '');
      if (value !== newval) {
        el.children[0].value = newval;
        el.children[0].dispatchEvent(
          new Event(modifiers.lazy ? 'change' : 'input'),
        );
      }
    } catch (e) {
      console.log(e);
    }
  },
};

export default alphabet;
