/*
 * @Description:只允许输入正整数
 * @Author: 刘攀
 * @Date: 2021-05-21 15:09:07
 * @LastEditTime: 2022-11-18 16:28:19
 * @LastEditors: dddd
 * @Reference:
 */
const enterNumber = {
  update: function (el, { value, modifiers }, vnode) {
    try {
      let newval = value.replace(/[^\d]/g, '');
      if (value !== newval) {
        el.children[0].value = newval;
        el.children[0].dispatchEvent(
          new Event(modifiers.lazy ? 'change' : 'input'),
        );
      }
    } catch (e) {}
  },
};

export default enterNumber;
