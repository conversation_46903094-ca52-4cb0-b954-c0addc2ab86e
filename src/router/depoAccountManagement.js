/*
 * @Author: 七七
 * @Date: 2021-12-07 10:36:49
 * @LastEditors: xuxiang
 * @LastEditTime: 2022-05-30 14:34:14
 * @FilePath: /access-fmis-web/src/router/serviceFeeManagement.js
 */
import ParentView from '@/components/ParentView';

export default [
  {
    path: '/depoAccountManagement',
    name: 'incomePayment',
    component: ParentView,
    alwaysShow: true,
    meta: { title: '货款管理', icon: '' },
    children: [
      {
        path: '/depoAccountManagement/refundApply',
        name: 'refundApply',
        component: () => import('@/views/depoAccountManagement/refundApply'),
        meta: { title: '退款申请', icon: '' },
      },
      {
        path: '/depoAccountManagement/refundAudit',
        name: 'refundAudit',
        component: () => import('@/views/depoAccountManagement/refundAudit'),
        meta: { title: '退款审核', icon: '' },
      },
      {
        path: '/depoAccountManagement/withdrawal',
        name: 'withdrawal',
        component: () => import('@/views/depoAccountManagement/withdrawal'),
        meta: { title: '提现审核', icon: '' },
      },
    ],
  },
];
