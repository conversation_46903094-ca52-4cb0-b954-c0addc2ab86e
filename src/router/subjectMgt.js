import ParentView from '@/components/ParentView';

export default [
  {
    path: '/relatedTransactionMgt',
    name: 'relatedTransactionMgt',
    component: ParentView,
    redirect: '/subjectMgt/relatedTransactionMgt/subjectInformationMgt',
    meta: { title: '关联交易管理', icon: '' },
    children: [
      {
        path: '/subjectMgt/relatedTransactionMgt/subjectInformationMgt',
        name: 'subjectInformationMgt',
        component: () =>
          import(
            '@/views/subjectMgt/relatedTransactionMgt/subjectInformationMgt'
          ),
        meta: { title: '主体信息管理', icon: '' },
      },
      {
        path: '/subjectMgt/relatedTransactionMgt/locationInformationMgt',
        name: 'locationInformationMgt',
        component: () =>
          import(
            '@/views/subjectMgt/relatedTransactionMgt/locationInformationMgt'
          ),
        meta: { title: '定位信息管理', icon: '' },
      },
      {
        path: '/purchaseLinkMgt',
        name: 'purchaseLinkMgt',
        component: ParentView,
        meta: { title: '购销链路管理', icon: '' },
        children: [
          {
            path:
              '/subjectMgt/relatedTransactionMgt/purchaseLinkMgt/purchaseLink',
            name: 'purchaseLink',
            component: () =>
              import(
                '@/views/subjectMgt/relatedTransactionMgt/purchaseLinkMgt/purchaseLink'
              ),
            meta: { title: '购销链路管理', icon: '' },
          },
          {
            path:
              '/subjectMgt/relatedTransactionMgt/purchaseLinkMgt/transactionLinkLog',
            name: 'transactionLinkLog',
            hidden: true,
            component: () =>
              import(
                '@/views/subjectMgt/relatedTransactionMgt/purchaseLinkMgt/transactionLinkLog'
              ),
            meta: { title: '交易链路日志', icon: '' },
          },
          {
            path:
              '/subjectMgt/relatedTransactionMgt/purchaseLinkMgt/purchaseTransactionLog',
            name: 'purchaseTransactionLog',
            hidden: true,
            component: () =>
              import(
                '@/views/subjectMgt/relatedTransactionMgt/purchaseLinkMgt/purchaseTransactionLog'
              ),
            meta: { title: '购销交易日志', icon: '' },
          },
          {
            path:
              '/subjectMgt/relatedTransactionMgt/purchaseLinkMgt/linkPricingConfiguration',
            name: 'linkPricingConfiguration',
            hidden: true,
            component: () =>
              import(
                '@/views/subjectMgt/relatedTransactionMgt/purchaseLinkMgt/linkPricingConfiguration'
              ),
            meta: { title: '链路定价配置', icon: '' },
          },
          {
            path:
              '/subjectMgt/relatedTransactionMgt/purchaseLinkMgt/purchaseTransactions',
            name: 'purchaseTransactions',
            component: () =>
              import(
                '@/views/subjectMgt/relatedTransactionMgt/purchaseLinkMgt/purchaseTransactions'
              ),
            meta: { title: '购销关联交易', icon: '' },
          },
          {
            path: '/subjectMgt/relatedTransactionMgt/purchaseLinkMgt/LinkModel',
            name: 'LinkModel',
            component: () =>
              import(
                '@/views/subjectMgt/relatedTransactionMgt/purchaseLinkMgt/LinkModel'
              ),
            meta: { title: '购销链路模型', icon: '' },
          },
        ],
      },
      {
        path: '/subjectMgt/relatedTransactionMgt/licenseQuery',
        name: 'licenseQuery',
        hidden: true,
        component: () =>
          import('@/views/subjectMgt/relatedTransactionMgt/licenseQuery'),
        meta: { title: '证照查询', icon: '' },
      },
      {
        path: '/subjectMgt/relatedTransactionMgt/editSubjectInformation',
        name: 'editSubjectInformation',
        hidden: true,
        component: () =>
          import(
            '@/views/subjectMgt/relatedTransactionMgt/editSubjectInformation'
          ),
        meta: { title: '编辑主体信息', icon: '' },
      },
      {
        path: '/subjectMgt/relatedTransactionMgt/serviceTransactionsMgt',
        name: 'serviceTransactionsMgt',
        component: () =>
          import(
            '@/views/subjectMgt/relatedTransactionMgt/serviceTransactionsMgt'
          ),
        meta: { title: '服务交易管理', icon: '' },
      },
      {
        path: '/subjectMgt/relatedTransactionMgt/serviceTransaction',
        name: 'serviceTransaction',
        hidden: true,
        component: () =>
          import('@/views/subjectMgt/relatedTransactionMgt/serviceTransaction'),
        meta: { title: '服务交易查询', icon: '' },
      },
      {
        path: '/subjectMgt/relatedTransactionMgt/serviceTransactionLog',
        name: 'serviceTransactionLog',
        hidden: true,
        component: () =>
          import(
            '@/views/subjectMgt/relatedTransactionMgt/serviceTransactionLog'
          ),
        meta: { title: '服务交易日志', icon: '' },
      },
      {
        path: '/subjectMgt/relatedTransactionMgt/serviceTransactionsType',
        name: 'serviceTransactionsType',
        hidden: true,
        component: () =>
          import(
            '@/views/subjectMgt/relatedTransactionMgt/serviceTransactionsType'
          ),
        meta: { title: '服务交易类型', icon: '' },
      },
      {
        path: '/subjectMgt/relatedTransactionMgt/pricingModelMgt',
        name: 'pricingModelMgt',
        component: () =>
          import('@/views/subjectMgt/relatedTransactionMgt/pricingModelMgt'),
        meta: { title: '定价模型管理', icon: '' },
      },
    ],
  },
];
