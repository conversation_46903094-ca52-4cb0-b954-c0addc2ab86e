/*
 * @Description:
 * @Author: bruce
 * @Date: 2021-03-17 11:25:29
 * @LastEditTime: 2025-04-15 18:18:42
 * @LastEditors: 王赛军
 * @Reference:
 */
/**
 * @description router全局配置，如有必要可分文件抽离，其中asyncRoutes只有在intelligence模式下才会用到，pro版只支持remixIcon图标，具体配置请查看vip群文档
 */
import Vue from 'vue';
import VueRouter from 'vue-router';
import Layout from '@/amf/layouts';
import ParentView from '@/components/ParentView';
import subjectMgt from './subjectMgt';
import documentCenter from './documentCenter';
import serviceFeeManagement from './serviceFeeManagement';
import depoAccountManagement from './depoAccountManagement';
import { publicPath, routerMode } from '@/config';

Vue.use(VueRouter);
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true,
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true,
  },
  {
    path: '/403',
    name: '403',
    component: () => import('@/views/403'),
    hidden: true,
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/404'),
    hidden: true,
  },
  {
    path: '/401',
    name: '401',
    component: () => import('@/views/401'),
    hidden: true,
  },
  {
    path: '/',
    name: 'Root',
    component: Layout,
    redirect: '/index',
    meta: {
      title: '首页',
      icon: 'home-2-line',
    },
    alwaysShow: true,
    children: [
      {
        path: 'index',
        name: 'Index',
        component: () => import('@/views/index/index.vue'),
        meta: {
          title: '首页',
          icon: 'home-2-line',
          noCLosable: true,
        },
      },
    ],
  },
];

export const asyncRoutes = [
  {
    path: '/basicMasterData',
    name: 'basicMasterData',
    component: Layout,
    redirect: '/basicMasterData/exchangeRateInquiry',
    meta: { title: '基础主数据' },
    children: [
      {
        path: '/basicMasterData/exchangeRateInquiry',
        name: 'exchangeRateInquiry',
        component: () =>
          import('@/views/basicMasterData/exchangeRateInquiry.vue'),
        meta: { title: '汇率查询', icon: '' },
      },
      {
        path: '/basicMasterData/currencyRecord',
        name: 'currencyRecord',
        component: () => import('@/views/basicMasterData/currencyRecord.vue'),
        meta: { title: '币种档案', icon: '' },
      },
      {
        path: '/basicMasterData/exchangeRate',
        name: 'exchangeRate',
        component: () =>
          import(
            /* webpackChunkName: "basicMasterData" */ '@/views/basicMasterData/exchangeRate.vue'
          ),
        meta: { title: '汇率获取日志', icon: '' },
      },
      {
        path: '/basicMasterData/getExchangeRateSetters',
        name: 'getExchangeRateSetters',
        hidden: true,
        component: () =>
          import(
            /* webpackChunkName: "basicMasterData" */ '@/views/basicMasterData/getExchangeRateSetters.vue'
          ),
        meta: { title: '汇率设置获取', icon: '' },
      },
    ],
  },
  {
    path: '/taxMgt',
    name: 'taxMgt',
    component: Layout,
    redirect: '/taxMgt/taxRuleSetting',
    meta: { title: '税务管理' },
    children: [
      {
        path: '/taxMgt/taxRuleSetting',
        name: 'taxRuleSetting',
        component: () => import('@/views/taxMgt/taxRuleSetting.vue'),
        meta: { title: '税务规则设置', icon: '' },
      },
    ],
  },
  {
    path: '/liquidation',
    name: 'liquidation',
    component: Layout,
    redirect: '/liquidation/subject',
    meta: { title: '资金清算' },
    children: [
      {
        path: '/liquidation/subject',
        name: 'subjectManager',
        component: () =>
          import(
            /* webpackChunkName: "liquidation" */ '@/views/liquidation/subject.vue'
          ),
        meta: { title: '主体管理' },
      },
      {
        path: '/liquidation/shop',
        name: 'shopManager',
        component: () =>
          import(
            /* webpackChunkName: "liquidation" */ '@/views/liquidation/shop.vue'
          ),
        meta: { title: '店铺管理' },
      },
      {
        path: '/liquidation/counterparty',
        name: 'counterpartyManager',
        component: () =>
          import(
            /* webpackChunkName: "liquidation" */ '@/views/liquidation/counterparty.vue'
          ),
        meta: { title: '客户管理' },
      },
      {
        path: '/liquidation/staff',
        name: 'staffManager',
        component: () =>
          import(
            /* webpackChunkName: "liquidation" */ '@/views/liquidation/staff.vue'
          ),
        meta: { title: '员工管理' },
      },
      {
        path: '/liquidation/account',
        name: 'accountManager',
        component: () =>
          import(
            /* webpackChunkName: "liquidation" */ '@/views/liquidation/account.vue'
          ),
        meta: { title: '账户管理' },
      },
      {
        path: '/liquidation/turnoverlog',
        name: 'turnoverImportLogManager',
        component: () =>
          import(
            /* webpackChunkName: "liquidation" */ '@/views/liquidation/turnoverLog.vue'
          ),
        meta: { title: '流水导入日志' },
      },
      {
        path: '/liquidation/rule',
        name: 'ruleManager',
        component: () =>
          import(
            /* webpackChunkName: "liquidation" */ '@/views/liquidation/rule.vue'
          ),
        meta: { title: '清算事项规则' },
      },
      {
        path: '/liquidation/turnover',
        name: 'turnoverManager',
        component: () =>
          import(
            /* webpackChunkName: "liquidation" */ '@/views/liquidation/turnover.vue'
          ),
        meta: { title: '流水管理' },
      },
      {
        path: '/liquidation/capitalproof',
        name: 'capitalProofManager',
        component: () =>
          import(
            /* webpackChunkName: "liquidation" */ '@/views/liquidation/capitalProof.vue'
          ),
        meta: { title: '资金凭证' },
      },
      {
        path: '/liquidation/privacyturnover',
        name: 'privacyTurnoverManager',
        component: () =>
          import(
            /* webpackChunkName: "liquidation" */ '@/views/liquidation/privacyTurnover.vue'
          ),
        meta: { title: '私域收单-支付渠道流水' },
      },
      {
        path: '/liquidation/publicturnover',
        name: 'publicTurnoverManager',
        component: () =>
          import(
            /* webpackChunkName: "liquidation" */ '@/views/liquidation/publicTurnover.vue'
          ),
        meta: { title: '公域收单-支付渠道流水' },
      },
    ],
  },
  {
    path: '/settle',
    name: 'settle',
    component: Layout,
    redirect: '/settle/item_manage',
    meta: { title: '结算平台' },
    children: [
      {
        path: '/settle/settle-accept',
        name: 'settle-accept',
        component: () => import('@/views/settle/settleAccept'),
        meta: { title: '结算受理', icon: '' },
      },
      {
        path: '/settle/settle-batch',
        name: 'settle-batch',
        component: () => import('@/views/settle/settleBatch'),
        meta: { title: '结算批次', icon: '' },
      },
      {
        path: '/settle/nonPoBill',
        name: 'nonPoBill',
        component: () => import('@/views/settle/nonPoBill'),
        meta: { title: 'NON-PO结算单', icon: '' },
      },
      {
        path: '/settle/nonPoInvoice',
        name: 'nonPoInvoice',
        component: () => import('@/views/settle/nonPoInvoice'),
        meta: { title: 'NON-PO请款单', icon: '' },
      },
    ],
  },
  {
    path: '/accountManagement',
    name: 'accountManagement',
    component: Layout,
    meta: { title: '账户管理' },
    children: [
      {
        path: '/accountManagement/platformAccount',
        name: 'platformAccount',
        component: ParentView,
        redirect: '/accountManagement/platformAccount/serviceFeeBreakdown',
        meta: { title: '平台账户管理', icon: '' },
        children: [
          {
            path: '/accountManagement/platformAccount/serviceFeeBreakdown',
            name: 'serviceFeeBreakdown',
            component: () =>
              import(
                '@/views/accountManagement/platformAccount/serviceFeeBreakdown.vue'
              ),
            meta: { title: '服务费账户管理' },
          },
          {
            path: '/accountManagement/platformAccount/summaryServiceDetails',
            name: 'summaryServiceDetails',
            component: () =>
              import(
                '@/views/accountManagement/platformAccount/summaryServiceDetails.vue'
              ),
            meta: { title: '服务费交易汇总' },
          },
        ],
      },
      {
        path: '/accountManagement/costManagement',
        name: 'costManagement',
        component: ParentView,
        redirect: '/accountManagement/costManagement/processPayment',
        meta: { title: '费用管理', icon: '' },
        children: [
          {
            path: '/accountManagement/costManagement/processPayment',
            name: 'processPayment',
            component: () =>
              import(
                '@/views/accountManagement/costManagement/processPayment.vue'
              ),
            meta: { title: '费用穿透管理', icon: '' },
          },
          {
            path: '/accountManagement/costManagement/processPaymentDetail',
            name: 'processPaymentDetail',
            component: () =>
              import(
                '@/views/accountManagement/costManagement/processPaymentDetail.vue'
              ),
            meta: { title: '费用穿透详情', icon: '' },
          },
        ],
      },
    ],
  },
  {
    path: '/pingan',
    name: 'pingan',
    component: Layout,
    redirect: '/pingan/platform-account',
    meta: {
      title: '平安银行分账管理',
      // remixIcon: 'bank-line'
    },
    children: [
      {
        path: '/pingan/platform-account',
        name: 'platform-account',
        component: () => import('@/views/pingan/platformAccountInfo'),
        meta: { title: '平台子账户信息查询', icon: '' },
      },
      {
        path: '/pingan/member-account',
        name: 'platform-info',
        component: () => import('@/views/pingan/memberAccountInfo'),
        meta: { title: '会员子账户信息查询', icon: '' },
      },
      {
        path: '/pingan/withdrawal-record',
        name: 'withdrawal-record',
        component: () => import('@/views/pingan/withdrawalRecord'),
        meta: { title: '提现记录查询', icon: '' },
      },
      {
        path: '/pingan/trade-config',
        name: 'trade-config',
        component: () => import('@/views/pingan/tradeConfig'),
        meta: { title: '贸易主体配置', icon: '' },
      },
      {
        path: '/pingan/trade-record',
        name: 'trade-record',
        component: () => import('@/views/pingan/tradeRecord'),
        meta: { title: '贸易主体交易记录查询', icon: '' },
      },
      {
        path: '/pingan/member-transfer',
        name: 'member-transfer',
        component: () => import('@/views/pingan/memberTransfer'),
        meta: { title: '会员间转账', icon: '' },
      },
      {
        path: '/pingan/member-transfer-record',
        name: 'member-transfer-record',
        component: () => import('@/views/pingan/memberTransferRecord'),
        meta: { title: '会员间转账交易记录查询', icon: '' },
      },
      {
        path: '/pingan/account-create-record',
        name: 'account-create-record',
        component: () => import('@/views/pingan/accountCreateRecord'),
        meta: { title: '子账户开户及提现绑定', icon: '' },
      },
      {
        path: '/pingan/trade-subject-create-record',
        name: 'trade-subject-create-record',
        component: () => import('@/views/pingan/tradeSubjectCreateRecord'),
        meta: { title: '贸易主体开户', icon: '' },
      },
      {
        path: '/pingan/registerTheAccount',
        name: 'registerTheAccount',
        component: () => import('@/views/pingan/registerTheAccount'),
        meta: { title: '登记挂账', icon: '' },
      },
      {
        path: '/pingan/gatewayRecharge',
        name: 'gatewayRecharge',
        component: () => import('@/views/pingan/gatewayRecharge'),
        meta: { title: '网关充值', icon: '' },
      },

      {
        path: '/pingan/memberAccountMgt',
        name: 'memberAccountMgt',
        component: () => import('@/views/pingan/memberAccountMgt'),
        meta: { title: '会员子账户管理', icon: '' },
      },
      {
        path: '/pingan/memberAccountBankCard',
        name: 'memberAccountBankCard',
        hidden: true,
        component: () => import('@/views/pingan/memberAccountBankCard'),
        meta: { title: '查看银行卡', icon: '' },
      },
      {
        path: '/pingan/withdrawalReviewMgt',
        name: 'withdrawalReviewMgt',
        component: () => import('@/views/pingan/withdrawalReviewMgt'),
        meta: { title: '提现审核管理', icon: '' },
      },
      {
        path: '/pingan/transferAuditMgt',
        name: 'transferAuditMgt',
        component: () => import('@/views/pingan/transferAuditMgt'),
        meta: { title: '转账审核管理', icon: '' },
      },
      {
        path: '/pingan/auditOperationLog',
        name: 'auditOperationLog',
        hidden: true,
        component: () => import('@/views/pingan/auditOperationLog'),
        meta: { title: '操作日志', icon: '' },
      },
      {
        path: '/pingan/secondaryTransferMgt',
        name: 'secondaryTransferMgt',
        component: () => import('@/views/pingan/secondaryTransferMgt'),
        meta: { title: '二级转账管理', icon: '' },
      },
      {
        path: '/pingan/secondaryTransferDetails',
        name: 'secondaryTransferDetails',
        hidden: true,
        component: () => import('@/views/pingan/secondaryTransferDetails'),
        meta: { title: '查看明细', icon: '' },
      },
      {
        path: '/pingan/fundSplitDetail',
        name: 'fundSplitDetail',
        hidden: true,
        component: () => import('@/views/pingan/fundSplitDetail'),
        meta: { title: '资金明细', icon: '' },
      },
      {
        path: '/pingan/ledgerOrderMgt',
        name: 'ledgerOrderMgt',
        component: () => import('@/views/pingan/ledgerOrderMgt'),
        meta: { title: '一级分账管理', icon: '' },
      },
      {
        path: '/pingan/ledgerOrderDetails',
        name: 'ledgerOrderDetails',
        hidden: true,
        component: () => import('@/views/pingan/ledgerOrderDetails'),
        meta: { title: '查看明细', icon: '' },
      },
      {
        path: '/pingan/costRatioSetting',
        name: 'costRatioSetting',
        component: () => import('@/views/pingan/costRatioSetting'),
        meta: { title: '费用比例设置', icon: '' },
      },
    ],
  },
  {
    path: '/salesMgt',
    name: 'salesMgt',
    component: Layout,
    meta: { title: '销售管理', icon: '' },
    children: [
      {
        path: '/award',
        name: 'award',
        component: ParentView,
        redirect: '/award/basic-info',
        meta: { title: '奖惩处理平台', icon: '' },
        children: [
          {
            path: '/award/basic-info',
            name: 'basic-info',
            component: () => import('@/views/award/basicInfo'),
            meta: { title: '基础信息维护' },
          },
          {
            path: '/award/apply-record',
            name: 'apply-record',
            component: () => import('@/views/award/applyRecord'),
            meta: { title: '扣费申请记录' },
          },
          {
            path: '/award/audit-record',
            name: 'audit-record',
            component: () => import('@/views/award/auditRecord'),
            meta: { title: '奖惩审核记录' },
          },
          {
            path: '/award/audit-log',
            name: 'audit-log',
            component: () => import('@/views/award/auditLog'),
            meta: { title: '日志记录' },
          },
        ],
      },
      {
        path: '/orderManagement',
        name: 'orderManagement',
        component: ParentView,
        redirect: '/orderManagement/order',
        meta: { title: '订单管理' },
        alwaysShow: true,
        children: [
          {
            path: '/orderManagement/order',
            name: 'order',
            component: () => import('@/views/orderManagement/order.vue'),
            meta: { title: '退款失败处理', icon: '' },
          },
        ],
      },
      {
        path: '/refundManagement',
        name: 'refundManagement',
        component: ParentView,
        redirect: '/refundManagement/refundFailed',
        meta: { title: '退款管理' },
        alwaysShow: true,
        children: [
          {
            path: '/refundManagement/refundFailed',
            name: 'refundFailed',
            component: () =>
              import('@/views/refundManagement/refundFailed.vue'),
            meta: { title: '退款失败处理', icon: '' },
          },
        ],
      },
    ],
  },
  {
    path: '/invoiceCenter',
    name: 'invoiceCenter',
    component: Layout,
    meta: { title: '发票中心', icon: '' },
    children: [
      {
        path: '/salesManagement',
        name: 'salesManagement',
        component: ParentView,
        redirect: '/invoiceCenter/billing/invoiceApplication',
        meta: { title: '销项管理' },
        children: [
          {
            path: '/billing',
            name: 'billing',
            meta: { title: '开票业务' },
            component: ParentView,
            children: [
              {
                path: '/invoiceCenter/billing/invoiceApplication',
                name: 'invoiceApplication',
                component: () =>
                  import(
                    '@/views/invoiceCenter/salesManagement/billing/invoiceApplication.vue'
                  ),
                meta: { title: '发票申请' },
              },
              {
                path: '/invoiceCenter/billing/invoiceReview',
                name: 'invoiceReview',
                component: () =>
                  import(
                    '@/views/invoiceCenter/salesManagement/billing/invoiceReview.vue'
                  ),
                meta: { title: '发票审核' },
              },
              {
                path: '/invoiceCenter/billing/excelList',
                name: 'excelList',
                component: () =>
                  import(
                    '@/views/invoiceCenter/salesManagement/billing/excelList.vue'
                  ),
                meta: { title: 'Excel导出' },
              },
            ],
          },
          {
            path: '/invoiceCenter/salesInvoicePool',
            name: 'salesInvoicePool',
            component: () =>
              import('@/views/invoiceCenter/salesManagement/salesInvoicePool'),
            meta: { title: '销项发票池' },
          },
          {
            path: '/invoiceCenter/dataStatistics',
            name: 'dataStatistics',
            component: () =>
              import('@/views/invoiceCenter/salesManagement/dataStatistics'),
            meta: { title: '数据统计' },
          },
        ],
      },
      {
        path: '/inputmanagement',
        name: 'inputmanagement',
        component: ParentView,
        meta: { title: '进项管理', icon: '' },
        alwaysShow: true,
        children: [
          {
            path: '/invoiceCenter/inputInvoicePool',
            name: 'inputInvoicePool',
            component: () =>
              import('@/views/invoiceCenter/inputmanagement/inputInvoicePool'),
            meta: { title: '进项发票池' },
          },
        ],
      },
      {
        path: '/invoiceCenter/informationMgt',
        name: 'informationMgt',
        component: ParentView,
        meta: { title: '信息管理', icon: 'coins-fill' },
        alwaysShow: true,
        children: [
          // {
          //   path: '/invoiceCenter/informationMgt/companyInformation',
          //   name: 'companyInformation',
          //   component: () =>
          //     import('@/views/invoiceCenter/informationMgt/companyInformation'),
          //   meta: { title: '公司信息' },
          // },
          {
            path: '/invoiceCenter/informationMgt/saleSubjectRule',
            name: 'saleSubjectRule',
            component: () =>
              import('@/views/invoiceCenter/informationMgt/saleSubjectRule'),
            meta: { title: '销售主体规则' },
          },
          {
            path: '/invoiceCenter/informationMgt/proformaInvoiceTemplate',
            name: 'proformaInvoiceTemplate',
            component: () =>
              import(
                '@/views/invoiceCenter/informationMgt/proformaInvoiceTemplate'
              ),
            meta: { title: '形式发票模板' },
          },
          {
            path: '/invoiceCenter/informationMgt/invoiceTemplateConfiguration',
            name: 'invoiceTemplateConfiguration',
            component: () =>
              import(
                '@/views/invoiceCenter/informationMgt/invoiceTemplateConfiguration'
              ),
            meta: { title: '公司发票模板配置' },
          },
          {
            path: '/invoiceCenter/informationMgt/systemInformation',
            name: 'systemInformation',
            component: () =>
              import('@/views/invoiceCenter/informationMgt/systemInformation'),
            meta: { title: '系统信息' },
          },
        ],
      },
    ],
  },
  {
    path: '/supplyChainMgt',
    name: 'supplyChainMgt',
    component: Layout,
    meta: { title: '供应链管理', icon: '' },
    children: [
      {
        path: '/purchase',
        name: 'purchase',
        component: ParentView,
        redirect: '/purchase/purchase-apply',
        meta: { title: '采购管理', icon: '' },
        children: [
          {
            path: '/purchase/purchase-apply',
            name: 'purchase-apply',
            component: () => import('@/views/purchase/purchaseApply'),
            meta: { title: '采购申请管理', icon: '' },
          },
          {
            path: '/purchase/purchase-detail',
            name: 'purchase-detail',
            component: () => import('@/views/purchase/purchaseDetail'),
            meta: { title: '采购明细管理', icon: '' },
          },
          {
            path: '/purchase/letter-info',
            name: 'letter-info',
            component: () => import('@/views/purchase/letterInfo'),
            meta: { title: '对账信息管理', icon: '' },
          },
          {
            path: '/purchase/settle-apply',
            name: 'settle-apply',
            component: () => import('@/views/purchase/settleApply'),
            meta: { title: '结算申请管理', icon: '' },
          },
          {
            path: '/purchase/sale-info',
            name: 'sale-info',
            component: () => import('@/views/purchase/saleInfo'),
            meta: { title: '销售信息管理', icon: '' },
          },
        ],
      },
    ],
  },
  {
    path: '/receivableManagement',
    name: 'receivableManagement',
    component: Layout,
    redirect: '/receivableManagement/accountsReceivableSettlement',
    meta: { title: '应收管理' },
    children: [
      {
        path: '/receivableManagement/accountsReceivableSettlement',
        name: 'accountsReceivableSettlement',
        component: () =>
          import('@/views/receivableManagement/accountsReceivableSettlement'),
        meta: { title: '应收结算管理', icon: '' },
      },
      {
        path: '/receivableManagement/advanceIncomeMgt',
        name: 'advanceIncomeMgt',
        component: () =>
          import('@/views/receivableManagement/advanceIncomeMgt'),
        meta: { title: '预收收入管理', icon: '' },
      },
      {
        path: '/receivableManagement/advanceIncomeDetails',
        name: 'advanceIncomeDetails',
        hidden: true,
        component: () =>
          import('@/views/receivableManagement/advanceIncomeDetails'),
        meta: { title: '预收收入明细', icon: '' },
      },
      {
        path: '/receivableManagement/advanceIncomeItemDetails',
        name: 'advanceIncomeItemDetails',
        hidden: true,
        component: () =>
          import('@/views/receivableManagement/advanceIncomeItemDetails'),
        meta: { title: '明细详情', icon: '' },
      },
      {
        path: '/receivableManagement/advanceIncomeConsistent',
        name: 'advanceIncomeConsistent',
        component: () =>
          import('@/views/receivableManagement/advanceIncomeConsistent'),
        meta: { title: '预收收入自洽', icon: '' },
      },
      {
        path: '/receivableManagement/billingDocumentMgt',
        name: 'billingDocumentMgt',
        component: () =>
          import('@/views/receivableManagement/billingDocumentMgt'),
        meta: { title: '计费单据管理', icon: '' },
      },
      {
        path: '/receivableManagement/billingDetailsMgt',
        name: 'billingDetailsMgt',
        component: () =>
          import('@/views/receivableManagement/billingDetailsMgt'),
        meta: { title: '计费明细管理', icon: '' },
      },
    ],
  },
  {
    path: '/reconciliationCenter',
    name: 'reconciliationCenter',
    component: Layout,
    redirect: '/reconciliationCenter/reconciliationManagement',
    meta: { title: '对账中心' },
    children: [
      {
        path: '/reconciliationCenter/reconciliationManagement',
        name: 'reconciliationManagement',
        component: () =>
          import('@/views/reconciliationCenter/reconciliationManagement'),
        meta: { title: '渠道对账管理', icon: '' },
      },
      {
        path: '/reconciliationCenter/reconciliationDifferences',
        name: 'reconciliationDifferences',
        component: () =>
          import('@/views/reconciliationCenter/reconciliationDifferences'),
        meta: { title: '订单渠道对账差异', icon: '' },
      },
    ],
  },
  {
    path: '/copingManagement',
    name: 'copingManagement',
    component: Layout,
    meta: { title: '应付管理' },
    children: [
      {
        path: '/copingManagement/coping',
        name: 'coping',
        component: ParentView,
        alwaysShow: true,
        meta: { title: '应付管理', icon: '' },
        redirect: '/copingManagement/coping/purchaseSettlementQuery',
        children: [
          {
            path: '/copingManagement/coping/purchaseSettlementQuery',
            name: 'purchaseSettlementQuery',
            component: () =>
              import('@/views/copingManagement/coping/purchaseSettlementQuery'),
            meta: { title: '主体应付结算管理', icon: '' },
          },
          {
            path: '/copingManagement/coping/payableStatementDistribution',
            name: 'payableStatementDistribution',
            component: () =>
              import(
                '@/views/copingManagement/coping/payableStatementDistribution'
              ),
            meta: { title: '经销-应付结算单', icon: '' },
          },
          {
            path: '/copingManagement/coping/payableStatementConsignment',
            name: 'payableStatementConsignment',
            component: () =>
              import(
                '@/views/copingManagement/coping/payableStatementConsignment'
              ),
            meta: { title: '寄售-应付结算单', icon: '' },
          },
          {
            path: '/copingManagement/coping/payableStatementDetails',
            name: 'payableStatementDetails',
            hidden: true,
            component: () =>
              import('@/views/copingManagement/coping/payableStatementDetails'),
            meta: { title: '应付结算单商品明细', icon: '' },
          },
        ],
      },
      {
        path: '/copingManagement/bill',
        name: 'bill',
        component: ParentView,
        alwaysShow: true,
        meta: { title: '账单管理', icon: '' },
        redirect: '/copingManagement/bill/billDistribution',
        children: [
          {
            path: '/copingManagement/bill/billDistribution',
            name: 'billDistribution',
            component: () =>
              import('@/views/copingManagement/bill/billDistribution'),
            meta: { title: '经销账单管理', icon: '' },
          },
          {
            path: '/copingManagement/bill/billConsignment',
            name: 'billConsignment',
            component: () =>
              import('@/views/copingManagement/bill/billConsignment'),
            meta: { title: '寄售账单管理', icon: '' },
          },
          {
            path: '/copingManagement/bill/billingInvoiceVerification',
            name: 'billingInvoiceVerification',
            component: () =>
              import(
                '@/views/copingManagement/bill/billingInvoiceVerification'
              ),
            meta: { title: '账单发票核销', icon: '' },
          },
          {
            path: '/copingManagement/bill/pleasePayQuery',
            name: 'pleasePayQuery',
            component: () =>
              import('@/views/copingManagement/bill/pleasePayQuery'),
            meta: { title: '请款管理查询', icon: '' },
          },
        ],
      },
    ],
  },
  {
    path: '/financialManagement',
    name: 'financialManagement',
    component: Layout,
    redirect: '/financialManagement/withdrawBankCard',
    meta: { title: '财务管理', icon: '' },
    children: [
      {
        path: '/financialManagement/memberRecharge',
        name: 'memberRecharge',
        component: () => import('@/views/financialManagement/memberRecharge'),
        meta: { title: '会员充值' },
      },
      {
        path: '/financialManagement/withdrawBankCard',
        name: 'withdrawBankCard',
        component: () => import('@/views/financialManagement/withdrawBankCard'),
        meta: { title: '提现银行卡列表' },
      },
      {
        path: '/financialManagement/memberPaymentSearch',
        name: 'memberPaymentSearch',
        component: () =>
          import('@/views/financialManagement/memberPaymentSearch'),
        meta: { title: '会员支付查询' },
      },
      {
        path: '/financialManagement/rechargeApplication',
        name: 'rechargeApplication',
        component: () =>
          import('@/views/financialManagement/rechargeApplication'),
        meta: { title: '充值申请' },
      },
      {
        path: '/financialManagement/rechargeAudit',
        name: 'rechargeAudit',
        component: () => import('@/views/financialManagement/rechargeAudit'),
        meta: { title: '充值审核' },
      },
      {
        path: '/financialManagement/refundPayment',
        name: 'refundPayment',
        component: () => import('@/views/financialManagement/refundPayment'),
        meta: { title: '退货款申请' },
      },
      {
        path: '/financialManagement/refundPaymentCheck',
        name: 'refundPaymentCheck',
        component: () =>
          import('@/views/financialManagement/refundPaymentCheck'),
        meta: { title: '退货款审核' },
      },
      {
        path: '/financialManagement/rechargeProfit',
        name: 'rechargeProfit',
        component: () => import('@/views/financialManagement/rechargeProfit'),
        meta: { title: '提现充值申请' },
      },
      {
        path: '/financialManagement/withdrawalRechargeAudit',
        name: 'withdrawalRechargeAudit',
        component: () =>
          import('@/views/financialManagement/withdrawalRechargeAudit'),
        meta: { title: '提现充值审核' },
      },
    ],
  },
  {
    path: '/subjectMgt',
    name: 'subjectMgt',
    component: Layout,
    meta: { title: '关联交易管理', icon: '' },
    children: subjectMgt,
  },
  {
    path: '/documentCenter',
    name: 'documentCenter',
    component: Layout,
    meta: { title: '单据中心', icon: '' },
    children: documentCenter,
  },
  {
    path: '/xiamen',
    name: 'xiamen',
    component: Layout,
    redirect: '/xiamen/memberInformationMgt',
    meta: {
      title: '厦门银行',
      icon: '',
    },
    children: [
      {
        path: '/xiamen/memberInformationMgt',
        name: 'memberInformationMgt',
        component: () => import('@/views/xiamen/memberInformationMgt'),
        meta: { title: '会员信息管理', icon: '' },
      },
      {
        path: '/xiamen/operationLog',
        name: 'operationLog',
        component: () => import('@/views/xiamen/operationLog'),
        meta: { title: '操作日志', icon: '' },
      },
      {
        path: '/xiamen/memberInforBankCard',
        name: 'memberInforBankCard',
        hidden: true,
        component: () => import('@/views/xiamen/memberInforBankCard'),
        meta: { title: '查看银行卡', icon: '' },
      },
      {
        path: '/xiamen/transferInformationMgt',
        name: 'transferInformationMgt',
        component: () => import('@/views/xiamen/transferInformationMgt'),
        meta: { title: '转账信息管理', icon: '' },
      },
      {
        path: '/xiamen/accountFundMgt',
        name: 'accountFundMgt',
        component: () => import('@/views/xiamen/accountFundMgt'),
        meta: { title: '账户资金管理', icon: '' },
      },
      {
        path: '/xiamen/balanceManager',
        name: 'balanceManager',
        component: () => import('@/views/xiamen/balanceManager'),
        meta: { title: '账户余额管理', icon: '' },
      },
      {
        path: '/xiamen/xibAccTransRecord',
        name: 'xibAccTransRecord',
        component: () => import('@/views/xiamen/xibAccTransRecord'),
        meta: { title: '账户明细管理', icon: '' },
      },
      {
        path: '/xiamen/fundDetail',
        name: 'fundDetail',
        hidden: true,
        component: () => import('@/views/xiamen/fundDetail'),
        meta: { title: '资金明细', icon: '' },
      },
    ],
  },
  {
    path: '/serviceFeeManagement',
    name: 'serviceFeeManagement',
    component: Layout,
    meta: { title: '服务费管理', icon: '' },
    children: serviceFeeManagement,
  },
  {
    path: '/brandBonusMgt',
    name: 'brandBonusMgt',
    component: Layout,
    meta: {
      title: '品牌加成管理',
      icon: '',
    },
    children: [
      {
        path: '/brandBonusMgt/financialBrandBonusMgt',
        name: 'financialBrandBonusMgt',
        component: ParentView,
        alwaysShow: true,
        redirect: '/brandBonusMgt/financialBrandBonusMgt/financialBrandBonus',
        meta: { title: '财务品牌加成管理', icon: '' },
        children: [
          {
            path: '/brandBonusMgt/financialBrandBonusMgt/financialBrandBonus',
            name: 'financialBrandBonus',
            component: () =>
              import(
                '@/views/brandBonusMgt/financialBrandBonusMgt/financialBrandBonus'
              ),
            meta: { title: '品牌加成管理', icon: '' },
          },
        ],
      },
    ],
  },
  {
    path: '/relationshipMapping',
    name: 'relationshipMapping',
    component: Layout,
    redirect: '/relationshipMapping/maintainingRelationships',
    meta: {
      title: '关系映射',
      icon: '',
    },
    children: [
      {
        path: '/relationshipMapping/maintainingRelationships',
        name: 'maintainingRelationships',
        component: () =>
          import('@/views/relationshipMapping/maintainingRelationships.vue'),
        meta: { title: '维护关系', icon: '' },
      },
      {
        path: '/relationshipMapping/defineModelPage',
        name: 'defineModelPage',
        component: () =>
          import('@/views/relationshipMapping/defineModelPage.vue'),
        meta: { title: '定义模型', icon: '' },
      },
    ],
  },
  {
    path: '/paymentCenter',
    name: 'paymentCenter',
    component: Layout,
    redirect: '/fmisTools/paymentOrder',
    meta: { title: '质量工具' },
    children: [
      {
        path: '/fmisTools/paymentOrder',
        name: 'paymentOrder',
        component: () => import('@/views/fmisTools/paymentOrder.vue'),
        meta: { title: 'W单获取', icon: '' },
      },
      // {
      //   path: '/fmisTools/paymentPorder',
      //   name: 'paymentPorder',
      //   component: () => import('@/views/fmisTools/paymentPorder.vue'),
      //   meta: { title: '支付流水获取', icon: '' },
      // },
      {
        path: '/fmisTools/paymentSorder',
        name: 'paymentSorder',
        component: () => import('@/views/fmisTools/paymentSorder.vue'),
        meta: { title: '支付流水信息', icon: '' },
      },
      {
        path: '/fmisTools/overseaOrder',
        name: 'overseaOrder',
        component: () => import('@/views/fmisTools/overseaOrder.vue'),
        meta: { title: '跨境付款可视化', icon: '' },
      },
      {
        path: '/fmisTools/overseaOrderSum',
        name: 'overseaOrderSum',
        component: () => import('@/views/fmisTools/overseaOrderSum.vue'),
        meta: { title: '跨境付款日报表', icon: '' },
      },
    ],
  },
  {
    path: '/public',
    name: 'public',
    component: Layout,
    redirect: '/public/importRecord',
    meta: {
      title: '公域管理',
    },
    children: [
      {
        path: '/public/importRecord',
        name: 'importRecord',
        component: () => import('@/views/public/importRecord.vue'),
        meta: { title: '公域文件导入管理', icon: '' },
      },
      {
        path: '/public/shopFlow',
        name: 'shopFlow',
        component: () => import('@/views/public/shopFlow.vue'),
        meta: { title: '店铺资金流水管理', icon: '' },
      },
      {
        path: '/public/nsCost',
        name: 'nsCost',
        component: () => import('@/views/public/nsCost.vue'),
        meta: { title: '店铺费用流水管理', icon: '' },
      },
      {
        path: '/public/orderDetail',
        name: 'nsCost',
        component: () => import('@/views/public/orderDetail.vue'),
        meta: { title: '店铺订单明细管理', icon: '' },
      },
    ],
  },
  {
    path: '*',
    redirect: '/404',
    hidden: true,
  },
];

const router = createRouter();

export function resetRouter(routes = constantRoutes) {
  router.matcher = createRouter(routes).matcher;
}

function createRouter(routes = constantRoutes) {
  return new VueRouter({
    base: publicPath,
    mode: routerMode,
    scrollBehavior: () => ({
      y: 0,
    }),
    routes: routes,
  });
}

const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject)
    return originalPush.call(this, location, onResolve, onReject);
  return originalPush.call(this, location).catch(err => err);
};

export default router;
