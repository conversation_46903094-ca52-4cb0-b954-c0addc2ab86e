/*
 * @Author: 七七
 * @Date: 2021-12-07 10:36:49
 * @LastEditors: xuxiang
 * @LastEditTime: 2022-05-30 14:34:14
 * @FilePath: /access-fmis-web/src/router/serviceFeeManagement.js
 */
import ParentView from '@/components/ParentView';

export default [
  {
    path: '/serviceFeeManagement/incomePayment',
    name: 'incomePayment',
    component: ParentView,
    alwaysShow: true,
    redirect: '/serviceFeeManagement/incomePayment/approvalBilling',
    meta: { title: '收益打款管理', icon: '' },
    children: [
      {
        path: '/serviceFeeManagement/incomePayment/approvalBilling',
        name: 'approvalBilling',
        component: () =>
          import('@/views/serviceFeeManagement/incomePayment/approvalBilling'),
        meta: { title: '代开票提现审批', icon: '' },
      },
      {
        path: '/serviceFeeManagement/incomePayment/cashWithdrawal',
        name: 'cashWithdrawal',
        component: () =>
          import('@/views/serviceFeeManagement/incomePayment/cashWithdrawal'),
        meta: { title: '代开票提现打款', icon: '' },
      },
      {
        path: '/serviceFeeManagement/incomePayment/personalInvoicing',
        name: 'personalInvoicing',
        component: () =>
          import(
            '@/views/serviceFeeManagement/incomePayment/personalInvoicing'
          ),
        meta: { title: '自开票提现审批', icon: '' },
      },
      {
        path: '/serviceFeeManagement/incomePayment/personalInvoicingPay',
        name: 'personalInvoicingPay',
        component: () =>
          import(
            '@/views/serviceFeeManagement/incomePayment/personalInvoicingPay'
          ),
        meta: { title: '自开票提现打款', icon: '' },
      },
      {
        path:
          '/serviceFeeManagement/incomePayment/overseasWithdrawalApplication',
        name: 'overseasWithdrawalApplication',
        component: () =>
          import(
            '@/views/serviceFeeManagement/incomePayment/overseasWithdrawalApplication'
          ),
        meta: { title: '海外个人提现申请', icon: '' },
      },
      {
        path: '/serviceFeeManagement/incomePayment/overseasWithdrawalPay',
        name: 'overseasWithdrawalPay',
        component: () =>
          import(
            '@/views/serviceFeeManagement/incomePayment/overseasWithdrawalPay'
          ),
        meta: { title: '海外个人提现打款', icon: '' },
      },
    ],
  },
  {
    path: '/serviceFeeManagement/serviceFeeSubject',
    name: 'serviceFeeSubject',
    component: ParentView,
    alwaysShow: true,
    redirect: '/serviceFeeManagement/serviceFeeSubject/serviceFeeQuery',
    meta: { title: '服务费主体管理', icon: '' },
    children: [
      // 服务费主体信息查询 暂时不做
      // {
      //   path: '/serviceFeeManagement/serviceFeeSubject/serviceFeeQuery',
      //   name: 'serviceFeeQuery',
      //   component: () =>
      //     import(
      //       '@/views/serviceFeeManagement/serviceFeeSubject/serviceFeeQuery'
      //     ),
      //   meta: { title: '服务费主体信息查询', icon: '' },
      // },
      {
        path: '/serviceFeeManagement/serviceFeeSubject/agentInvoincingEntity',
        name: 'agentInvoincingEntity',
        component: () =>
          import(
            '@/views/serviceFeeManagement/serviceFeeSubject/agentInvoincingEntity'
          ),
        meta: { title: '代开票主体查询', icon: '' },
      },
      {
        path: '/serviceFeeManagement/serviceFeeSubject/personInvoincingEntity',
        name: 'personInvoincingEntity',
        component: () =>
          import(
            '@/views/serviceFeeManagement/serviceFeeSubject/personInvoincingEntity'
          ),
        meta: { title: '自开票主体查询', icon: '' },
      },
      {
        path:
          '/serviceFeeManagement/serviceFeeSubject/queryOverseasDistributors',
        name: 'queryOverseasDistributors',
        component: () =>
          import(
            '@/views/serviceFeeManagement/serviceFeeSubject/queryOverseasDistributors'
          ),
        meta: { title: '海外经销商主体查询', icon: '' },
      },
    ],
  },
  {
    path: '/serviceFeeManagement/accountBal',
    name: 'accountFeeBal',
    component: ParentView,
    alwaysShow: true,
    redirect: '/serviceFeeManagement/accountBal/balPage',
    meta: { title: '账户余额管理', icon: '' },
    children: [
      {
        path: '/serviceFeeManagement/accountBal/balPage',
        name: 'balPage',
        component: () =>
          import('@/views/serviceFeeManagement/accountBal/balPage'),
        meta: { title: '账户余额', icon: '' },
      },
      {
        path: '/serviceFeeManagement/accountBal/withdrawalAccountFlowPage',
        name: 'withdrawalAccountFlowPage',
        component: () =>
          import(
            '@/views/serviceFeeManagement/accountBal/withdrawalAccountFlowPage'
          ),
        meta: { title: '可提现户流水', icon: '' },
      },
      {
        path: '/serviceFeeManagement/accountBal/frozenAccountFlowPage',
        name: 'frozenAccountFlowPage',
        component: () =>
          import(
            '@/views/serviceFeeManagement/accountBal/frozenAccountFlowPage'
          ),
        meta: { title: '冻结户流水', icon: '' },
      },
    ],
  },
  {
    path: '/serviceFeeManagement/taskCenter',
    name: 'taskCenter',
    component: ParentView,
    alwaysShow: true,
    redirect: '/serviceFeeManagement/taskCenter/taskCenterPage',
    meta: { title: '任务中心', icon: '' },
    children: [
      {
        path: '/serviceFeeManagement/taskCenter/taskCenterPage',
        name: 'taskCenterPage',
        component: () =>
          import('@/views/serviceFeeManagement/taskCenter/taskCenterPage'),
        meta: { title: '任务中心', icon: '' },
      },
    ],
  },
];
