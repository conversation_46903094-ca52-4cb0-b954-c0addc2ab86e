import ParentView from '@/components/ParentView';

export default [
  {
    path: '/documentCenter/documentQuery',
    name: 'documentQuery',
    component: ParentView,
    redirect: '/documentCenter/documentQuery/purchaseOrder',
    meta: { title: '单据查询', icon: '' },
    children: [
      {
        path: '/documentCenter/documentQuery/purchaseOrder',
        name: 'purchaseOrder',
        component: () =>
          import('@/views/documentCenter/documentQuery/purchaseOrder'),
        meta: { title: '采购订单', icon: '' },
      },
      {
        path: '/documentCenter/documentQuery/salesOrder',
        name: 'salesOrder',
        component: () =>
          import('@/views/documentCenter/documentQuery/salesOrder'),
        meta: { title: '销售订单', icon: '' },
      },
      {
        path: '/documentCenter/documentQuery/receiveBill',
        name: 'receiveBill',
        component: () =>
          import('@/views/documentCenter/documentQuery/receiveBill'),
        meta: { title: '收款单', icon: '' },
      },
      {
        path: '/documentCenter/documentQuery/payBill',
        name: 'payBill',
        component: () => import('@/views/documentCenter/documentQuery/payBill'),
        meta: { title: '付款单', icon: '' },
      },
      {
        path: '/documentCenter/documentQuery/bankFlow',
        name: 'bankFlow',
        component: () =>
          import('@/views/documentCenter/documentQuery/bankFlow'),
        meta: { title: '银行流水', icon: '' },
      },
      // {
      //   path: '/documentCenter/documentQuery/paymentSlip',
      //   name: 'paymentSlip',
      //   component: () =>
      //     import('@/views/documentCenter/documentQuery/paymentSlip'),
      //   meta: { title: '支付单', icon: '' },
      // },
      // {
      //   path: '/documentCenter/documentQuery/payRefundOrder',
      //   name: 'payRefundOrder',
      //   component: () =>
      //     import('@/views/documentCenter/documentQuery/payRefundOrder'),
      //   meta: { title: '支付退款单', icon: '' },
      // },
      // {
      //   path: '/documentCenter/documentQuery/invoicingReceipts',
      //   name: 'invoicingReceipts',
      //   component: ParentView,
      //   meta: { title: '进销存单据', icon: '' },
      //   alwaysShow: true,
      //   children: [
      //     {
      //       path: '/documentCenter/documentQuery/invoicingReceipts/outboundOrder',
      //       name: 'outboundOrder',
      //       component: () =>
      //         import(
      //           '@/views/documentCenter/documentQuery/invoicingReceipts/outboundOrder.vue'
      //         ),
      //       meta: { title: '出库单', icon: '' },
      //     },
      //   ],
      // },
      {
        path: '/documentCenter/documentQuery/invoicingReceipts',
        name: 'invoicingReceipts',
        component: ParentView,
        meta: { title: '进销存单据', icon: '' },
        alwaysShow: true,
        children: [
          {
            path:
              '/documentCenter/documentQuery/invoicingReceipts/outboundOrder',
            name: 'outboundOrder',
            component: () =>
              import(
                '@/views/documentCenter/documentQuery/invoicingReceipts/outboundOrder.vue'
              ),
            meta: { title: '出库单', icon: '' },
          },
          {
            path: '/documentCenter/documentQuery/invoicingReceipts/warehousing',
            name: 'warehousing',
            component: () =>
              import(
                '@/views/documentCenter/documentQuery/invoicingReceipts/warehousing.vue'
              ),
            meta: { title: '入库单', icon: '' },
          },
          {
            path:
              '/documentCenter/documentQuery/invoicingReceipts/inventoryAdjustment',
            name: 'inventoryAdjustment',
            component: () =>
              import(
                '@/views/documentCenter/documentQuery/invoicingReceipts/inventoryAdjustment.vue'
              ),
            meta: { title: '库存调整单', icon: '' },
          },
          {
            path:
              '/documentCenter/documentQuery/invoicingReceipts/transferOutInOrder',
            name: 'transferOutInOrder',
            component: () =>
              import(
                '@/views/documentCenter/documentQuery/invoicingReceipts/transferOutInOrder.vue'
              ),
            meta: { title: '调拨出入库单', icon: '' },
          },
        ],
      },
      {
        path: '/documentCenter/documentQuery/paymentSlip',
        name: 'paymentSlip',
        component: () =>
          import('@/views/documentCenter/documentQuery/paymentSlip'),
        meta: { title: '支付单', icon: '' },
      },
      {
        path: '/documentCenter/documentQuery/payRefundOrder',
        name: 'payRefundOrder',
        component: () =>
          import('@/views/documentCenter/documentQuery/payRefundOrder'),
        meta: { title: '支付退款单', icon: '' },
      },
      {
        path: '/documentCenter/documentQuery/expenseReceipt',
        name: 'expenseReceipt',
        component: ParentView,
        meta: { title: '费用单据', icon: '' },
        alwaysShow: true,
        children: [
          {
            path: '/documentCenter/documentQuery/expenseReceipt/billingSheet',
            name: 'billingSheet',
            component: () =>
              import(
                '@/views/documentCenter/documentQuery/expenseReceipt/billingSheet.vue'
              ),
            meta: { title: '计费单', icon: '' },
          },
          {
            path: '/documentCenter/documentQuery/expenseReceipt/actualPayment',
            name: 'actualPayment',
            component: () =>
              import(
                '@/views/documentCenter/documentQuery/expenseReceipt/actualPayment.vue'
              ),
            meta: { title: '跨境综合税实缴单', icon: '' },
          },
        ],
      },
    ],
  },
  {
    path: '/documentCenter/reconciliation',
    name: 'reconciliation',
    component: ParentView,
    redirect: '/documentCenter/reconciliation/report',
    meta: { title: '单据对账', icon: '' },
    alwaysShow: true,
    children: [
      {
        path: '/documentCenter/reconciliation/report',
        name: 'report',
        component: () =>
          import('@/views/documentCenter/reconciliation/report.vue'),
        meta: { title: '对账结果报表', icon: '' },
      },
    ],
  },
];
