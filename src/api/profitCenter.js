/*
 * @Author: dddd
 * @Date: 2023-05-23 10:48:56
 * @LastEditors: dddd
 * @LastEditTime: 2023-05-24 14:05:52
 * @FilePath: /fmis/src/api/profitCenter.js
 * @Description:
 */
import axios from '@/utils/request';

//规则列表分页查询
export function mappingPageQuery(data) {
  return axios({
    url: '/mdm/profit/mapping/pageQuery',
    method: 'post',
    data,
  });
}

//规则列表详情页面
export function mappingQueryDetail(params) {
  return axios({
    url: '/mdm/profit/mapping/queryDetail',
    method: 'get',
    params: params,
  });
}
//查询利润中心树状结构
export function defineTreeQuery(data) {
  return axios({
    url: '/mdm/profit/define/treeQuery',
    method: 'post',
    data,
  });
}
//查询利润中心公共接口  规则类型，字段类型
export function commonContentQuery(data) {
  return axios({
    url: '/mdm/profit/common/contentQuery',
    method: 'get',
    data,
  });
}
//查询利润中心公共接口  通过key 查 value值
export function commonRuleContentQuery(data) {
  return axios({
    url: '/mdm/profit/common/rule/contentQuery?ruleFields=' + data,
    method: 'get',
  });
}
//规则保存
export function saveUpdate(data) {
  return axios({
    url: '/mdm/profit/mapping/save',
    method: 'post',
    data,
  });
}
// 查看版本
//规则列表详情页面
export function mappingQueryHistory(params) {
  return axios({
    url: '/mdm/profit/mapping/queryHistory',
    method: 'get',
    params: params,
  });
}
// 利润中心新增
export function defineAdd(data) {
  return axios({
    url: '/mdm/profit/define/add',
    method: 'post',
    data,
  });
}
