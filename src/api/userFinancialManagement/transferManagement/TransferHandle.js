/*
 * @Author: bruce
 * @Date: 2024-02-01 16:58:31
 * @LastEditors: bruce
 * @FilePath: /access-fmis-web/src/api/userFinancialManagement/transferManagement/TransferHandle.js
 * @Description: 转账处理
 */
import req from '@/utils/newRequest';

//财务转账单列表
export function transferOrderList(params) {
  return req.http.request({
    url: 'fundout-core/transferOrder/list',
    method: 'get',
    params,
  });
}

// 重新转账
export function retryTransfer(data) {
  return req.http.request({
    url: 'fundout-core/transferOrder/retryTransfer',
    method: 'post',
    data,
  });
}

// 转账驳回
export function transferOrderReject(data) {
  return req.http.request({
    url: 'fundout-core/transferOrder/reject',
    method: 'post',
    data,
  });
}
