/*
 * @Description:动态路由api
 * @Author: bruce
 * @Date: 2021-03-10 14:18:00
 * @LastEditTime: 2021-04-14 14:47:03
 * @LastEditors: bruce
 * @Reference:
 */

import axios from '@/utils/request';
const data = [
  {
    path: '/',
    name: 'Root',
    component: 'Layout',
    redirect: '/index',
    meta: {
      title: '首页',
      icon: 'home-2-line',
    },
    children: [
      {
        path: 'index',
        name: 'Index',
        component: '@/views/index',
        meta: {
          title: '首页',
          icon: 'home-2-line',
          noCLosable: true,
        },
      },
      {
        path: 'workbench',
        name: 'Workbench',
        component: '@/views/index/workbench',
        meta: {
          title: '工作台',
          icon: 'settings-6-line',
          dot: true,
        },
      },
    ],
  },
];

export function getRouterList1(params) {
  return Promise.resolve({ data });
}
