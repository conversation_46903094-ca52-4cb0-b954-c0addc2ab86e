/*
 * @Description:
 * @Author: bruce
 * @Date: 2021-03-10 14:18:00
 * @LastEditTime: 2021-04-14 14:46:40
 * @LastEditors: bruce
 * @Reference:
 */
import { encryptedData } from '@/utils/encrypt';
import { loginRSA } from '@/config';
import axios from '@/utils/request';
const { Random } = require('mockjs');
const tokens = {
  admin: `admin-token-${Random.guid()}`,
  editor: `editor-token-${Random.guid()}`,
  test: `test-token-${Random.guid()}`,
};
export async function login(data) {
  if (loginRSA) {
    data = await encryptedData(data);
  }
  const { username } = data;
  const token = tokens[username];
  if (!token) return Promise.resolve({ code: 500, msg: '帐户或密码不正确' });
  data = { token: 'token' };
  return Promise.resolve({ data });
}

export async function socialLogin(data) {
  if (loginRSA) {
    data = await encryptedData(data);
  }
  return Promise.resolve({ data });
}

export function getUserInfo() {
  const authorization =
    config.headers.authorization || config.headers.Authorization;
  let roles = ['admin'];
  let ability = ['READ'];
  let username = 'admin';
  if (authorization.includes('admin-token')) {
    roles = ['admin'];
    ability = ['READ', 'WRITE', 'DELETE'];
    username = 'admin';
  }
  if (authorization.includes('editor-token')) {
    roles = ['editor'];
    ability = ['READ', 'WRITE'];
    username = 'editor';
  }
  if (authorization.includes('test-token')) {
    roles = ['admin', 'editor'];
    ability = ['READ'];
    username = 'test';
  }
  return Promise.resolve({
    data: {
      roles,
      ability,
      username,
      avatar: 'http://api.btstu.cn/sjtx/api.php?lx=c1&format=images',
    },
  });
}

export function logout() {
  return Promise.resolve({ msg: 'success' });
}

export function register() {
  return Promise.resolve({ msg: '模拟注册成功' });
}
