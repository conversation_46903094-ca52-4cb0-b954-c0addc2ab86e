/*
 * @Description:测试
 * @Author: bruce
 * @Date: 2021-03-18 18:17:14
 * @LastEditTime: 2021-08-16 10:08:23
 * @LastEditors: 项萍～
 * @Reference:
 */
import axios from '@/utils/newRequest';

/**
 * @description:下载测试
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function downloadFile(params) {
  return axios.http.request({
    url:
      'https://api-test.datatreeglobal.com/boss/manage/bill/applyRecord/export?codeId=&mobile=&cardNo=&status=&name=&startDate=&endDate=&invoice=&type=&env=test&sign=e6443b4c0c2c2e6a0624b97506b35c1f3c70fb863cead9e47353f81154418905884ef1c84eb87db10c21eb0b4c07b22d48e4149a10eff02522dc189f4383e82a&appCode=APP_ERDHUB&ssoUserId=1964&exportType=1',
    method: 'get',
    responseType: 'blob',
    headers: {
      fileType: '.csv',
    },
  });
}
