import axios from '@/utils/request';
import { getApiHost } from '@/utils/index';

/** 类目信息查询 */
export function getCategoryList(data) {
  return axios({
    url: 'covercharge-service/punish/category/getInfo',
    method: 'post',
    data,
  });
}

/** 发票申请+发票审核-列表 */
export function getInvoiceList(data) {
  return axios({
    url: 'bill/admin/invoice/list',
    method: 'post',
    data,
  });
}

/** 申请发票-查询 */
export function invoiceOrdersList(data) {
  return axios({
    url: 'bill/admin/invoice/orders',
    method: 'post',
    data,
  });
}

/**
 * 发票详情
 * @param id
 */
export function invoiceDetails(data) {
  return axios({
    url: `bill/admin/invoice/details/${data.id}`,
    method: 'get',
  });
}

/**
 * 查询详情电话号码
 * @param id
 */
export function getInvoiceStatus(data) {
  return axios({
    url: `bill/admin/invoice/getInvoiceStatus/${data.id}`,
    method: 'get',
  });
}

/**
 * 批量审核通过
 * @param id_list:arr
 * @param origin:origin
 * @param operationType: (cancel:批量取消  approved:批量审核)
 */
export function batchAudit(data) {
  return axios({
    url: 'bill/admin/invoice/batchAudit',
    method: 'post',
    data,
  });
}

/**
 * 批量取消
 * @param id_list:arr
 * @param origin:origin
 * @param operationType: (cancel:批量取消  approved:批量审核)
 */
export function batchCancel(data) {
  return axios({
    url: 'bill/admin/invoice/batchCancel',
    method: 'post',
    data,
  });
}

/**
 * 单发票操作
 * @param id:string 订单编号
 * @param origin:integer 申请来源:0-VTNAPP,1-管理后台
 * @param reason:string 驳回+作废的原因
 * @param operationType: (approv-审核通过,reject-驳回,invalid-作废)
 */
export function singleInvoiceOperation(data) {
  return axios({
    url: 'bill/admin/invoice/audit',
    method: 'post',
    data,
  });
}

/**
 * 单发票撤回
 * @param id:string 订单编号
 * @param origin:integer 申请来源:0-VTNAPP,1-管理后台
 */
export function invoiceRevoke(data) {
  return axios({
    url: 'bill/admin/invoice/revoke',
    method: 'post',
    data,
  });
}

/**
 * 单发票取消申请
 * @param id:string 订单编号
 * @param origin:integer 申请来源:0-VTNAPP,1-管理后台
 */
export function invoicecancel(data) {
  return axios({
    url: 'bill/admin/invoice/cancel',
    method: 'post',
    data,
  });
}

/**
 * 申请发票-查询后填写发票信息提交
 */
export function generateInvoice(data) {
  return axios({
    url: 'bill/admin/invoice/apply',
    method: 'post',
    data,
  });
}

/**
 * 申请发票-发票修改
 */
export function invoiceModify(data) {
  return axios({
    url: 'bill/admin/invoice/modify',
    method: 'post',
    data,
  });
}

/**
 * 数据统计查询
 */
export function dataStatisticsInquiry(data) {
  return axios({
    url: 'bill/admin/invoice/dataStatisticsInquiry',
    method: 'post',
    data,
  });
}

/**
 * 申请发票-导出
 */
export function buildExcel(data) {
  return axios({
    url: 'bill/admin/invoice/buildExcel',
    method: 'post',
    data,
  });
}

/**
 * 进项发票池查询
 */
export function inputManagementQuery(data) {
  return axios({
    url: 'bill/admin/invoice/inputManagementQuery',
    method: 'post',
    data,
  });
}

/**
 * 销项发票池查询
 */
export function sellInvoice(data) {
  return axios({
    url: 'bill/admin/invoice/sellInvoice',
    method: 'post',
    data,
  });
}

/**
 * excel导出列表
 */
export function excelListForBack(data) {
  return axios({
    url: 'bill/admin/invoice/invoiceReviewExcelListForBack',
    method: 'post',
    data,
  });
}

/**
 * excel导出
 * @param id:string
 */
export function excelToDownload(data) {
  return axios({
    url: 'bill/admin/invoice/excelToDownload',
    method: 'get',
    params: data,
  });
}

/**
 * 发票图片
 */
export function getFileUrl(url) {
  return axios({
    url: `bill/${url}`,
    method: 'get',
  });
}

/****************************  信息管理  *****************************/

/**
 * 形式发票模板 列表
 */
export function templatePage(data) {
  return axios({
    url: 'bill/admin/template/queryPage',
    method: 'get',
    params: data,
  });
}

/**
 * 形式发票模板 下拉列表
 */
export function templateList(data) {
  return axios({
    url: 'bill/admin/template/queryList',
    method: 'get',
    params: data,
  });
}

/**
 * 形式发票模板 新增
 */
export function templateCreate(data) {
  return axios({
    url: 'bill/admin/template/create',
    method: 'post',
    data,
  });
}

/**
 * 形式发票模板 修改
 */
export function templateUpdate(data) {
  return axios({
    url: 'bill/admin/template/update',
    method: 'post',
    data,
  });
}

/**
 * 形式发票模板 启用 停用
 */
export function templateOperate(data) {
  return axios({
    url: 'bill/admin/template/operate',
    method: 'post',
    data,
  });
}

/**
 * 形式发票模板 新增查询是否已存在
 */
export function templateIsExist(data) {
  return axios({
    url: 'bill/admin/template/isExist',
    method: 'get',
    params: data,
  });
}

/**
 * 系统信息  下拉
 */
export function systemManagerPage(data) {
  return axios({
    url: 'bill/admin/systemManager/queryPage',
    method: 'get',
    params: data,
  });
}

/**
 * 系统信息 列表
 */
export function systemManagerList(data) {
  return axios({
    url: 'bill/admin/systemManager/queryList',
    method: 'get',
    params: data,
  });
}

/**
 * 系统信息 新增
 */
export function systemManagerCreate(data) {
  return axios({
    url: 'bill/admin/systemManager/create',
    method: 'post',
    data,
  });
}

/**
 * 系统信息 修改
 */
export function systemManagerUpdate(data) {
  return axios({
    url: 'bill/admin/systemManager/update',
    method: 'post',
    data,
  });
}

/**
 * 系统信息 启用 停用
 */
export function systemManagerOperate(data) {
  return axios({
    url: 'bill/admin/systemManager/operate',
    method: 'post',
    data,
  });
}

/**
 * 系统信息新增 校验
 */
export function systemManagerIsExist(data) {
  return axios({
    url: 'bill/admin/systemManager/isExist',
    method: 'get',
    params: data,
  });
}

/**
 * 公司发票模板配置 新增
 */
export function templateConfigCreate(data) {
  return axios({
    url: 'bill/admin/templateConfig/create',
    method: 'post',
    data,
  });
}

/**
 * 公司发票模板配置 编辑
 */
export function templateConfigUpdate(data) {
  return axios({
    url: 'bill/admin/templateConfig/update',
    method: 'post',
    data,
  });
}

/**
 * 公司发票模板配置 列表
 */
export function templateConfigList(data) {
  return axios({
    url: 'bill/admin/templateConfig/queryList',
    method: 'get',
    params: data,
  });
}

/**
 * 公司发票模板配置 查询操作记录
 */
export function templateConfigQueryOperate(data) {
  return axios({
    url: 'bill/admin/operate/queryList',
    method: 'get',
    params: data,
  });
}

/**
 * 公司发票模板配置新增 校验
 */
export function templateConfigIsExist(data) {
  return axios({
    url: 'bill/admin/templateConfig/isExist',
    method: 'get',
    params: data,
  });
}

/**
 * 公司信息下拉 列表
 */
export function companyQueryList(data) {
  return axios({
    url: 'bill/admin/company/queryList',
    method: 'get',
    params: data,
  });
}

/**
 * 销售主体 列表
 */
/** 会员充值列表(单条可查看) */
export function getSaleSubjectRuleList(data) {
  return axios({
    url: 'order-admin/saleSubjectRule/get',
    method: 'post',
    data,
  });
}
/**
 * 销售主体 新增
 */
export function addSaleSubjectRule(data) {
  return axios({
    url: 'order-admin/saleSubjectRule/create',
    method: 'post',
    data,
  });
}
/**
 * 销售主体 编辑
 */
export function editSaleSubjectRule(data) {
  return axios({
    url: 'order-admin/saleSubjectRule/update',
    method: 'post',
    data,
  });
}
/**
 * 销售主体 获取渠道列表
 */
export function getListChannels(data) {
  return axios({
    url: 'order-admin/saleSubjectRule/listChannel',
    method: 'get',
    params: data,
  });
}
/**
 * 销售主体 获取店铺列表
 */
export function getListStore(data) {
  return axios({
    url: 'order-admin/saleSubjectRule/getStoreListByChannel',
    method: 'get',
    params: data,
  });
}
/**
 * 销售主体 获取仓库列表
 */
export function getListDept(data) {
  return axios({
    url: 'order-admin/depot/listDepotListAll',
    method: 'get',
    params: data,
  });
}
/**
 * 销售主体 获取用户等级列表
 */
export function getUserLevelList(data) {
  return axios({
    url: 'order-admin/saleSubjectRule/getUserLevelList',
    method: 'get',
    params: data,
  });
}
/**
 * 销售主体 获取销售主体列表
 */
export function getMapSaleCompanyMain(data) {
  return axios({
    url: 'order-admin/saleSubjectRule/listSalesPart',
    method: 'post',
    data,
  });
}
/**
 * 销售主体 获取销售主体规则状态
 */
export function getStatusList(data) {
  return axios({
    url: 'order-admin/saleSubjectRule/getStatusList',
    method: 'get',
    params: data,
  });
}
/**
 * 销售主体 审批
 */
export function createProcess(data) {
  return axios({
    url: 'order-admin/saleSubjectRule/createProcess',
    method: 'post',
    data,
  });
}
/**
 * 销售主体 获取日志列表
 */
export function getListLog(data) {
  return axios({
    url: 'order-admin/saleSubjectRule/listLog',
    method: 'get',
    params: data,
  });
}
/**
 * 销售主体 启用，禁止
 */
export function updateStatus(data) {
  return axios({
    url: 'order-admin/saleSubjectRule/updateStatus',
    method: 'post',
    data,
  });
}

/**
 * 销售主体 国家选择
 */
export function queryVirtualWarehouseCoverageCountry(data) {
  const baseUrl = getApiHost('ascm', '.acg.team');
  return axios({
    url: `${baseUrl}oms/oms-manage/wms-center-admin/warehouse/coverage/queryVirtualWarehouseCoverageCountry`,
    method: 'post',
    data,
  });
}
