import axios from '@/utils/request';
import aiAxios from 'axios';

/** 分页查询退款单 */
export function getRefundOrder(data) {
  return axios({
    url: 'pay-dashboard/order/refundOrder',
    method: 'get',
    params: data,
  });
}

/** 下拉框数据 */
export function getCondition(data) {
  return axios({
    url: 'pay-dashboard/order/condition',
    method: 'get',
    params: data,
  });
}

/** 退款批量完成 */
export function getPurchaseApplyList(data) {
  return axios({
    url: 'pay-core/order/refundOverBatch',
    method: 'post',
    data,
  });
}

/** 批量转可提现 */
export function refundToWithdrawalBatch(data) {
  return axios({
    url: 'pay-core/order/refundToWithdrawalBatch',
    method: 'post',
    data,
  });
}

/** 退款重试 */
export function retryRefund(data) {
  return axios({
    url: 'pay-core/order/retryRefund',
    method: 'post',
    data,
  });
}

/** 记录详情 */
export function orderQueryRefundOrderLog(data) {
  return axios({
    url: 'pay-dashboard/order/queryRefundOrderLog',
    method: 'get',
    params: data,
  });
}

/**交易列表 */
export function tradeList(data) {
  return axios({
    url: 'pay-dashboard/orderManage/trade/listTrade',
    method: 'get',
    params: data,
  });
}

/**获取筛选条件 */
export function getConditions(data) {
  return axios({
    url: 'pay-dashboard/orderManage/trade/getCondition',
    method: 'get',
    params: data,
  });
}

/**excel导出 */
export function exportTrade(data) {
  return axios({
    url: 'pay-dashboard/orderManage/trade/exportTrade',
    method: 'get',
    params: data,
  });
}

/**excel导出 */
export function getSimpleDetail(data) {
  return axios({
    url: 'pay-dashboard/orderManage/trade/getSimpleDetail',
    method: 'get',
    params: data,
  });
}

/*
 * listPaymentOrders
 * */
export function listPaymentOrders(data) {
  return axios({
    url: 'pay-dashboard/orderInfo/listPaymentOrders',
    method: 'get',
    params: data,
  });
}

//获取退款列表
export function listRefundOrders(data) {
  return axios({
    url: 'pay-dashboard/orderInfo/listRefundOrders',
    method: 'get',
    params: data,
  });
}

//获取支付事件
export function listOrderEvents(data) {
  return axios({
    url: 'pay-dashboard/orderInfo/listOrderEvents',
    method: 'get',
    params: data,
  });
}

//获取回调商城状态
export function getMallBackStatus(data) {
  return axios({
    url: 'pay-dashboard/orderInfo/getMallBackStatus',
    method: 'get',
    params: data,
  });
}

/** 直接退款 */
export function directRefund(data) {
  return axios({
    url: 'pay-core/order/refund/' + data,
    method: 'post',
    data,
  });
}

/** 渠道列表 */
export function getPayList(data) {
  return axios({
    url: 'pay-core/order/getPayList',
    method: 'get',
    params: data,
  });
}

/** 支付单列表查询 */
export function getPaymentOrderList(data) {
  return axios({
    url: 'pay-core/order/paymentOrder',
    method: 'get',
    params: data,
  });
}

/** 退款单列表查询 */
export function getRefundOrderList(data) {
  return axios({
    url: 'pay-core/order/refundOrder',
    method: 'get',
    params: data,
  });
}

/** 退款至个人账户 */
export function refundToPersonAcc(data) {
  return axios({
    url: 'pay-core/order/refundToPersonAcc',
    method: 'post',
    data,
  });
}

/** 获取银行卡列表 */
export function supportBankList(params) {
  return axios({
    url: 'pay-core/order/supportBankList',
    method: 'get',
    params,
  });
}
export const queryDify = data =>
  aiAxios({
    url: 'http://dify-api-test.idanchuang.vpc/v1/workflows/run',
    data,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      Authorization: 'Bearer app-i95mpsUTOAV0jGeDgguX8YtM', // 系统口令
    },
  }).catch(() => {
    return {};
  });
