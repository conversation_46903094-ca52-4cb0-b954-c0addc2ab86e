/** 结算平台相关接口 */
import axios from '@/utils/request';

/**
 * 结算受理单列表
 * @param { Object } data
 */
export function getSettleAcceptList(data) {
  return axios({
    url: '/settle-center/settleOrder/queryList',
    method: 'post',
    data,
  });
}

// 获取结算单详情
export function getSettleAcceptDetail(settleOrderNo) {
  return axios({
    url: '/settle-center/settleOrder/queryDetail',
    method: 'get',
    params: { settleOrderNo },
  });
}
// 获取结算单复制需要的信息
export function getSettleAcceptCopyInfo(settleOrderNo) {
  return axios({
    url: '/settle-center/settleOrder/queryDetailForCopy',
    method: 'get',
    params: { settleOrderNo },
  });
}

// 修改结算单
export function editSettleAcceptInfo(data) {
  return axios({
    url: '/settle-center/settleOrder/update',
    method: 'post',
    data,
  });
}

/**
 * 结算批次列表
 * @param { Object } data
 */
export function getSettleBatchList(data) {
  return axios({
    url: '/settle-center/batchOrder/pageBatchOrder',
    method: 'post',
    data,
  });
}

/**
 * 复制/新增 结算受理单
 * @param {*} data
 */
export function addSettleAcceptInfo(data) {
  return axios({
    url: '/settle-center/settleOrder/save',
    method: 'post',
    data,
  });
}

/**
 * 生成出款批次
 * @param {*} data
 */
export function generateSettleAcceptInfo(data) {
  return axios({
    url: '/settle-center/batchOrder/createBatchOrder',
    method: 'post',
    data,
  });
}
// 撤销批次单
export function cancelSettleBatch(batchOrderId) {
  return axios({
    url: '/settle-center/batchOrder/withdraw',
    method: 'post',
    data: { batchOrderId },
  });
}
// 查询二级结算事项
export function getSecondMatterList(parentMatterId) {
  return axios({
    url: '/settle-center/settleOrder/queryMatterListForSelect',
    method: 'post',
    data: { parentMatterId },
  });
}
// 查询付款主体下的相关账户
export function getPayerAccountList(payer) {
  return axios({
    url: '/settle-center/settleOrder/queryPayCardNoListForSelect',
    method: 'post',
    data: { payer },
  });
}

/**
 * @description: 结算单列表
 * @param {*}
 * @return {*}
 */
export function getNpBillPage(data) {
  return axios({
    url: '/finance-bill/np/bill/page',
    method: 'post',
    data,
  });
}

/**
 * @description: 抵扣明细
 * @param {*}
 * @return {*}
 */
export function getDeduction(data) {
  return axios({
    url: '/finance-bill/np/bill/deductionTail',
    method: 'post',
    data,
  });
}

/**
 * @description: 结算单汇总金额
 * @param {*}
 * @return {*}
 */
export function getAmountTotal(data) {
  return axios({
    url: '/finance-bill/np/bill/amountTotal',
    method: 'post',
    data,
  });
}

/**
 * @description: nonpo请款单列表
 * @param {*}
 * @return {*}
 */
export function getNpPaymentRequisitionPage(data) {
  return axios({
    url: '/finance-bill/np/payment-requisition/page',
    method: 'post',
    data,
  });
}

export function getTaskList(data) {
  return axios({
    url: '/finance-bill/task/page',
    method: 'post',
    data,
  });
}

/**
 * @description: nonpo下拉数据
 * @param {*}
 * @return {*}
 */
export function getNpPaymentRequisitionDic(params) {
  return axios({
    url: '/finance-bill/dic',
    method: 'get',
    params,
  });
}

/**
 * @description: nonpo支付申请
 * @param {*}
 * @return {*}
 */
export function npPaymentRequisition(data) {
  return axios({
    url: '/finance-bill/np/payment-requisition',
    method: 'post',
    data,
  });
}

/**
 * @description: 请款单汇总金额
 * @param {*}
 * @return {*}
 */
export function getRequisitionAmountTotal(data) {
  return axios({
    url: '/finance-bill/np/payment-requisition/amountTotal',
    method: 'post',
    data,
  });
}

export function getExpenseProcessPage(data) {
  return axios({
    url: '/finance-bill/expenseProcess/page',
    method: 'post',
    data,
  });
}

export function getByOaRequestId(params) {
  return axios({
    url: '/finance-bill/expenseProcess/getByOaRequestId',
    method: 'get',
    params,
  });
}
export function getOAApprovalProcessInfo(params) {
  return axios({
    url: '/finance-bill/expenseProcess/getOAApprovalProcessInfo',
    method: 'get',
    params,
  });
}
export function getExpenseList(data) {
  return axios({
    url: '/finance-bill/expenseProcess/getExpenseList',
    method: 'post',
    data,
  });
}
export function getStatistics(params) {
  return axios({
    url: '/finance-bill/expenseProcess/getStatistics',
    method: 'get',
    params,
  });
}

export function getVoucherList(params) {
  return axios({
    url: '/finance-bill/expenseProcess/getVoucherList',
    method: 'get',
    params,
  });
}
