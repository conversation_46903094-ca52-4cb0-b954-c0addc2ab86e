import axios from '@/utils/request';

export function getPlatformSubAccountList() {
  return axios({
    url: '/split-service/subAccount/getPlatformSubAccountList',
    method: 'post',
    data: { channel: 'lianlian' },
  });
}

/*
 * 发起提现请求
 *
 * @params {*} body
 * @params {*} body.accountId
 * @params {*} body.amount
 * @params {*} body.remark
 * @params {*} body.serviceChargePercent
 * @see https://yapi.idanchuang.net/project/509/interface/api/14866
 */
export function doWithdraw(body) {
  return axios({
    url: '/split-service/withdrawOrder/doWithdraw',
    method: 'post',
    data: body,
  });
}

/**
 * 获取会员子账户列表
 *
 * @params {*} body
 * @params {*} body.accountType 账户类型
 * @params {*} body.cardNo 银行账号
 * @params {*} body.channel 银行渠道类型，pingan,xib
 * @params {*} body.custId 客户编号
 * @params {*} body.current
 * @params {*} body.size
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14846
 */
// export const getMemberSubAccountList = body =>
// service.post('/split-service/subAccount/getMemberSubAccountList', null, body);

export function getMemberSubAccountList(body) {
  return axios({
    url: '/split-service/subAccount/getMemberSubAccountList',
    method: 'post',
    data: body,
  });
}

/**
 * 会员子账户下拉框
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14848
 */
export function getMemberSubAccountOptions(params) {
  return axios({
    url: '/split-service/subAccount/selector',
    method: 'get',
    params,
  });
}

export function getWithdrawRecordOptions(param) {
  return axios({
    url: '/split-service/withdrawOrder/selector',
    method: 'get',
    params: param,
  });
}

export function getCrossPayoutOptions(param) {
  return axios({
    url: '/split-service/cross/payout/selector',
    method: 'get',
    params: param,
  });
}

/**
 * 提现记录列表
 *
 * @params {*} body
 * @params {*} body.accountNo
 * @params {*} body.startTime
 * @params {*} body.endTime
 * @params {*} body.current
 * @params {*} body.size
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14867
 */
export function getWithdrawRecord(body) {
  return axios({
    url: '/split-service/withdrawOrder/page',
    method: 'post',
    data: body,
  });
}

/**
 * 付汇记录列表
 *
 * @params {*} body
 * @params {*} body.accountNo
 * @params {*} body.startTime
 * @params {*} body.endTime
 * @params {*} body.current
 * @params {*} body.size
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14867
 */
export function getCrossPayoutRecord(body) {
  return axios({
    url: '/split-service/cross/payout/page',
    method: 'post',
    data: body,
  });
}

/**
 * 获取贸易主体配置项
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14862
 */
export function getTradeSubjectOptions() {
  return axios({
    url: '/split-service/tradeSubject/selector',
    method: 'get',
  });
}

/**
 * 贸易主体配置列表
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14861
 */
export function getTradeSubjectConfigList(body) {
  return axios({
    url: '/split-service/tradeSubject/getConfigList',
    method: 'post',
    data: body,
  });
}

export function getTradeSubjectConfigDetail(accountId, depoCode) {
  return axios({
    url: 'split-service/tradeSubject/getConfigDetail',
    method: 'post',
    data: { accountId, depoCode },
  });
}

/**
 * 更新贸易主体配置
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14863
 */
export function updateTradeSubjectConfig(body) {
  return axios({
    url: '/split-service/tradeSubject/updateConfig',
    method: 'post',
    data: body,
  });
}

/**
 * 删除记录
 *
 * @see ...问哈啰
 */
export function deleteTradeSubjectConfig(id) {
  return axios({
    url: '/split-service/tradeSubject/delConfig',
    method: 'get',
    params: { id },
  });
}

/**
 * 贸易主体交易记录查询列表
 *
 * @params {*} body
 * @params {*} body.accountNo
 * @params {*} body.depoCode
 * @params {*} body.startTime
 * @params {*} body.endTime
 * @params {*} body.endTime
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14857
 */
export function getTradeSubjectRecords(body) {
  return axios({
    url: '/split-service/subjectOrder/page',
    method: 'post',
    data: body,
  });
}

/**
 * 获取贸易主体交易记录配置
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14859
 */
export function getTradeSubjectRecordOptions() {
  return axios({
    url: '/split-service/subjectOrder/selector',
    method: 'get',
  });
}

/**
 * 贸易主体重新分账
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14858
 */
export function restartTradeSubjectRecords(body) {
  return axios({
    url: '/split-service/subjectOrder/restart',
    method: 'post',
    data: body,
  });
}

/**
 * 会员间转账 下拉框
 * @see https://yapi.idanchuang.net/project/509/interface/api/14854
 */
export function getSubAccountTransferOptions() {
  return axios({
    url: '/split-service/subAccountTransfer/selector',
    method: 'get',
  });
}

/**
 * 获取会员子账户列表 (转账页面)
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14853
 */
export function getSubAccountTransferList(body) {
  return axios({
    url: '/split-service/subAccountTransfer/page',
    method: 'post',
    data: body,
  });
}

/**
 * 新增会员子账户转账
 * @see https://yapi.idanchuang.net/project/509/interface/api/14850
 */
export function addMemberSubAccountTransfer(body) {
  return axios({
    url: '/split-service/subAccountTransfer/addMemberSubAccountTransfer',
    method: 'post',
    data: body,
  });
}

/**
 * 修改会员子账户转账
 * @see https://yapi.idanchuang.net/project/509/interface/api/14850
 */
export function updateMemberSubAccountTransfer(body) {
  return axios({
    url: '/split-service/subAccountTransfer/updateMemberSubAccountTransfer',
    method: 'post',
    data: body,
  });
}

/**
 * 会员子账户转账
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14855
 */
export function createSubAccountTransfer(body) {
  return axios({
    url: '/split-service/subAccountTransfer/transfer',
    method: 'post',
    data: body,
  });
}

/**
 * 会员间转账交易记录查询
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14864
 */
export function getTransferOrderList(body) {
  return axios({
    url: '/split-service/transferOrder/page',
    method: 'post',
    data: body,
  });
}

/**
 * 会员间转账交易记录选项
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14865
 */
export function getTransferOrderOptions(params) {
  return axios({
    url: '/split-service/transferOrder/selector',
    method: 'get',
    params,
  });
}

/**
 * 子账户开户选项
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/15111
 */
export function getOpenSubAccountOptions(params) {
  return axios({
    url: '/split-service/open/subAccount/selector',
    method: 'get',
    params,
  });
}

/**
 * 子账户开户列表
 *
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/15105
 */
export function getOpenSubAccountList(body) {
  return axios({
    url: '/split-service/open/subAccount/getList',
    method: 'post',
    data: body,
  });
}

/**
 * 账户开户
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/15108
 */
export function openSubAccount(body) {
  return axios({
    url: '/split-service/open/subAccount/open',
    method: 'post',
    data: body,
  });
}

/**
 * 绑卡
 * @see https://yapi.idanchuang.net/project/509/interface/api/15099
 */
export function subAccountBindCard(body) {
  return axios({
    url: 'split-service/open/subAccount/bindbing/card',
    method: 'post',
    data: body,
  });
}

/**
 * 验证开户
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/15102
 */
export function subAccountBindValid(body) {
  return axios({
    url: 'split-service/open/subAccount/bindbing/valid',
    method: 'post',
    data: body,
  });
}

/**
 * 主体开户下拉框
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/15096
 */
export function getOpenMainPartAccountOptions() {
  return axios({
    url: '/split-service/open/mainPartAccount/selector',
    method: 'get',
  });
}

/**
 * 主体开户记录
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/15090
 */
export function getOpenMainPartAccountList(body) {
  return axios({
    url: '/split-service/open/mainPartAccount/getList',
    method: 'post',
    data: body,
  });
}

/**
 * 主账户开户
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/15093
 */
export function createMainPartAccount(body) {
  return axios({
    url: '/split-service/open/mainPartAccount/open',
    method: 'post',
    data: body,
  });
}

/**
 * 刷新余额
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14852
 */
export function refreshSubAccountBalance(id) {
  return axios({
    url: '/split-service/subAccountTransfer/getSubAccountBalance',
    method: 'get',
    params: { id },
  });
}

/**
 * 登记挂帐  - 新建
 *
 */
export function hangOrderCreate(body) {
  return axios({
    url: '/split-service/hangOrder/create',
    method: 'post',
    data: body,
  });
}

/**
 * 登记挂帐  - 列表
 *
 */
export function hangOrderList(body) {
  return axios({
    url: '/split-service/hangOrder/list',
    method: 'post',
    data: body,
  });
}

/**
 * 登记挂帐  - 挂帐确认
 *
 */
export function hangOrderConfirm(body) {
  return axios({
    url: '/split-service/hangOrder/confirm',
    method: 'post',
    data: body,
  });
}

/**
 * 登记挂帐  - 挂帐取消
 *
 */
export function hangOrderCancel(body) {
  return axios({
    url: '/split-service/hangOrder/cancel',
    method: 'post',
    data: body,
  });
}

/**
 * 登记挂帐  - 挂帐拒绝
 *
 */
export function hangOrderRefuse(body) {
  return axios({
    url: '/split-service/hangOrder/refuse',
    method: 'post',
    data: body,
  });
}

/**
 * 登记挂帐  - 状态下拉
 *
 */

export function hangOrderSelector() {
  return axios({
    url: '/split-service/hangOrder/selector',
    method: 'get',
  });
}

/**
 * 登记挂帐  - 获取必要信息(创建挂帐等场景)
 *
 */

export function getEssentialInfo() {
  return axios({
    url: '/split-service/hangOrder/getEssentialInfo',
    method: 'get',
  });
}

/**
 * 网关充值 下拉框
 * @see
 */
export function getGatewayRechargeOptions() {
  return axios({
    url: '/split-service/recharge/init',
    method: 'get',
  });
}

/**
 * 网关充值记录列表
 *
 */
export function getGatewayRechargeList(body) {
  return axios({
    url: '/split-service/recharge/list',
    method: 'post',
    data: body,
  });
}

/**
 * 网关充值
 *
 */
export function doGatewayRecharge(body) {
  return axios({
    url: '/split-service/recharge/unionPay',
    method: 'post',
    data: body,
  });
}

/**
 * 解绑卡
 *
 * @see
 */
export function unbindingCard(id) {
  return axios({
    url: '/split-service/open/subAccount/unbindingCashCard',
    method: 'get',
    params: { id },
  });
}

/**
 * 一级分账日汇总
 *
 */
export function firstSplitStatsDaySum(body) {
  return axios({
    url: '/split-service/firstSplitStats/daySum',
    method: 'post',
    data: body,
  });
}

/**
 * 一级分账月汇总
 *
 */
export function firstSplitStatsMonthSum(body) {
  return axios({
    url: '/split-service/firstSplitStats/monthSum',
    method: 'post',
    data: body,
  });
}

/**
 * 一级分账明细
 *
 */
export function firstSplitStatsDaySumDetail(body) {
  return axios({
    url: '/split-service/firstSplitStats/firstSplitDetail',
    method: 'post',
    data: body,
  });
}

/**
 * 二级费用明细汇
 *
 */
export function secondaryItemStatistics(body) {
  return axios({
    url: '/split-service/secondary/item-statistics',
    method: 'post',
    data: body,
  });
}

/**
 * 二级费用明细汇
 *
 */
export function secondaryItemQuery(body) {
  return axios({
    url: '/split-service/secondary/item-query',
    method: 'post',
    data: body,
  });
}

/**
 * 配置查询
 *
 */
export function secondaryConfigQuery(body) {
  return axios({
    url: '/split-service/secondary/config-query',
    method: 'post',
    data: body,
  });
}

/**
 * 上传配置
 *
 */
export function secondaryUploadConfig(body) {
  return axios({
    url: '/split-service/secondary/upload—config',
    method: 'post',
    data: body,
  });
}

/**
 * 模版导出
 *
 */
export function secondaryTemplateImport(body) {
  return axios({
    url: '/split-service/secondary/config-template-url-query',
    method: 'post',
    data: body,
  });
}

/**
 * 贸易主体
 *
 */
export function secondaryTradeSubjectQuery(body) {
  return axios({
    url: '/split-service/secondary/tradeSubject-query',
    method: 'post',
    data: body,
  });
}

/**
 * ---------------------------- 提现审核管理 ------------------------------
 *
 */

/**
 * 提现资金申请记录分页
 *@see https://yapi.acg.team/project/1228/interface/api/131063
 */
export function fundWithdrawPage(body) {
  return axios({
    url: '/split-service/fund/withdraw/page',
    method: 'post',
    data: body,
  });
}

/**
 * 付汇资金记录分页
 *@see https://yapi.acg.team/project/1228/interface/api/131063
 */
export function fundCrossPayoutPage(body) {
  return axios({
    url: '/split-service/fund/cross/payout/page',
    method: 'post',
    data: body,
  });
}

/**
 * 转账资金申请记录分页
 *@see https://yapi.acg.team/project/1228/interface/api/131059
 */
export function fundTransferPage(body) {
  return axios({
    url: '/split-service/fund/transfer/page',
    method: 'post',
    data: body,
  });
}

/**
 * 会员子账户转账页面下拉框
 *
 * @see https://yapi.acg.team/project/1228/interface/api/131055
 */
export function fundSelector(params) {
  return axios({
    url: '/split-service/fund/selector',
    method: 'get',
    params,
  });
}

/**
 * 资金终审
 *@see https://yapi.acg.team/project/1228/interface/api/131045
 */
export function auditFinal(body) {
  return axios({
    url: '/split-service/fund/audit/final',
    method: 'post',
    data: body,
  });
}

/**
 * 资金复核
 *@see https://yapi.acg.team/project/1228/interface/api/131047
 */
export function auditOper(body) {
  return axios({
    url: '/split-service/fund/audit/oper',
    method: 'post',
    data: body,
  });
}

/**
 * 二级分账-下拉框
 *
 * @see
 */
export function secondSplitStatsSelector() {
  return axios({
    url: '/split-service/secondarySplitStats/selector',
    method: 'get',
  });
}

/**
 * 二级分账-日汇总
 *@see https://yapi.acg.team/project/1228/interface/api/131135
 */
export function secondSplitStatsDaySum(body) {
  return axios({
    url: '/split-service/secondarySplitStats/daySum',
    method: 'post',
    data: body,
  });
}

/**
 * 二级分账-月汇总
 *@see https://yapi.acg.team/project/1228/interface/api/131137
 */
export function secondSplitStatsMonthSum(body) {
  return axios({
    url: '/split-service/secondarySplitStats/monthSum',
    method: 'post',
    data: body,
  });
}
/**
 * 二级分账-二级分账明细
 *@see https://yapi.acg.team/project/1228/interface/api/131139
 */
export function secondSplitDetail(body) {
  return axios({
    url: '/split-service/secondarySplitStats/secondSplitDetail',
    method: 'post',
    data: body,
  });
}

/**
 * 刷新子账户余额
 *
 * @see https://yapi.acg.team/project/1228/interface/api/131075
 */
export function refreshSubAccountBal(accountNo) {
  return axios({
    url: '/split-service/subAccount/getSubAccountBal',
    method: 'get',
    params: { accountNo },
  });
}

/**
 * 收款账户列表
 *
 * @see https://yapi.acg.team/project/1228/interface/api/131075
 */
export function bindingCardsList(custId) {
  return axios({
    url: '/split-service/bindingCard/valid/cards',
    method: 'get',
    params: { custId },
  });
}
/**
 * 提现经办
 *
 */
export function withdrawApply(body) {
  return axios({
    url: '/split-service/fund/withdraw/apply',
    method: 'post',
    data: body,
  });
}

/**
 * 银行卡列表
 *
 * @see https://yapi.acg.team/project/1228/interface/api/131123
 */
export function getBindingCardsList(body) {
  return axios({
    url: '/split-service/bindingCard/list',
    method: 'post',
    data: body,
  });
}

/**
 * 绑卡申请经办
 *
 */
export function bindingCardApply(body) {
  return axios({
    url: '/split-service/bindingCard/apply',
    method: 'post',
    data: body,
  });
}

/**
 * 银行卡下拉数据
 *
 */
export function getSubAccountBankList(body) {
  return axios({
    url: '/split-service/open/subAccount/getBankList',
    method: 'post',
    data: body,
  });
}

/**
 * 绑卡复核
 *
 */
export function bindingCardAudit(body) {
  return axios({
    url: '/split-service/bindingCard/audit',
    method: 'post',
    data: body,
  });
}

/**
 * 转账申请验证码
 *
 */
export function mobileCode(body) {
  return axios({
    url: '/split-service/fund/audit/transfer/mobile/code',
    method: 'post',
    data: body,
  });
}

/**
 * 确认转账
 *
 */
export function auditTransferConfirm(body) {
  return axios({
    url: '/split-service/fund/audit/transfer/confirmLianlian',
    method: 'post',
    data: body,
  });
}

/**
 * 失败重试
 *
 */
export function auditTradeRetry(body) {
  return axios({
    url: '/split-service/fund/audit/trade/retry',
    method: 'post',
    data: body,
  });
}

/**
 * 验证金额
 *
 */
export function bindingCardValidAmount(body) {
  return axios({
    url: '/split-service/bindingCard/valid/amount',
    method: 'post',
    data: body,
  });
}

/**
 * 绑卡
 *
 */
export function bindingCardBinding(body) {
  return axios({
    url: '/split-service/bindingCard/do/binding',
    method: 'post',
    data: body,
  });
}

/**
 * 解绑申请
 *
 */
export function unBindingApply(body) {
  return axios({
    url: '/split-service/bindingCard/unBinding/apply',
    method: 'post',
    data: body,
  });
}

/**
 * 解绑复核
 *
 */
export function unBindingAudit(body) {
  return axios({
    url: '/split-service/bindingCard/unBinding/audit',
    method: 'post',
    data: body,
  });
}

/**
 * 解绑重试
 *
 */
export function unBindingRetry(body) {
  return axios({
    url: '/split-service/bindingCard/unBinding/retry',
    method: 'post',
    data: body,
  });
}

/**
 * 账户开户审核
 *
 */
export function subAccountAudit(body) {
  return axios({
    url: '/split-service/open/subAccount/open/audit',
    method: 'post',
    data: body,
  });
}

/**
 * 转账经办
 *
 */
export function transferApply(body) {
  return axios({
    url: '/split-service/fund/transfer/apply',
    method: 'post',
    data: body,
  });
}

/**
 * 开户中检查
 *
 */
export function subAccountOpeningCheck(id) {
  return axios({
    url: '/split-service/open/subAccount/opening/check',
    method: 'get',
    params: { id },
  });
}

/**
 * 资金经办审核操作日志
 *
 * @see https://yapi.acg.team/project/1228/interface/api/131949
 */
export function fundOperLog(id) {
  return axios({
    url: '/split-service/fund/oper/log',
    method: 'get',
    params: { id },
  });
}

/**
 * 获取子账户银行流水明细列表
 *
 */
export function bankDetailQuery(body) {
  return axios({
    url: '/split-service/subAccountDetail/bankDetailQuery',
    method: 'post',
    data: body,
  });
}

/**
 * 账单类型下拉框
 *
 */
export function subAccountDetailSelector(body) {
  return axios({
    url: '/split-service/subAccountDetail/selector',
    method: 'get',
    params: body,
  });
}

/**
 * 日切余额导出
 *
 * @see https://yapi.acg.team/project/509/interface/api/138421
 */
export function exportHisBalance(body) {
  return axios({
    url: '/split-service/acc/hisBalance/exportHisBalance',
    method: 'post',
    data: body,
  });
}

/**
 * 平安日切余额列表
 *
 * @see https://yapi.acg.team/project/509/interface/api/138421
 */
export function listHisBalance(body) {
  return axios({
    url: '/split-service/acc/hisBalance/listHisBalance',
    method: 'get',
    params: body,
  });
}

/**
 * 日切余额导出
 *
 * @see https://yapi.acg.team/project/509/interface/api/139021
 */
export function platDetailQuery(body) {
  return axios({
    url: '/split-service/subAccountDetail/platDetailQuery',
    method: 'post',
    data: body,
  });
}

/**
 * 二级实际数据
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export function secondaryActualList(body) {
  return axios({
    url: '/split-service/serviceFeeUpload/secondarySplitActualPage',
    method: 'post',
    data: body,
  });
}

/**
 * 二级实际数据审核
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export function secondaryActualDataMerge(body) {
  return axios({
    url: '/split-service/serviceFeeUpload/secondarySplitActualAudit',
    method: 'post',
    data: body,
  });
}
/**
 * 二级实际数据-模板下载
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export function secondaryDataDownloadTemplate() {
  return axios({
    url: '/split-service/serviceFeeUpload/fileTemplateDownload',
    method: 'post',
  });
}

/**
 * 二级实际/调整-下拉框
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export function secondaryOption() {
  return axios({
    url: '/split-service/serviceFeeAdjustment/selector',
    method: 'post',
  });
}

/**
 * 二级调整差异
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export function secondaryAdjustmentList(body) {
  return axios({
    url: '/split-service/serviceFeeAdjustment/page',
    method: 'post',
    data: body,
  });
}

/**
 * 二级调整差异明细
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export function secondaryAdjustmentDetail(body) {
  return axios({
    url: '/split-service/serviceFeeAdjustment/detailsPage',
    method: 'post',
    data: body,
  });
}

/**
 * 二级调整差异-支付
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export function secondaryAdjustmentPay(body) {
  return axios({
    url: '/split-service/serviceFeeAdjustment/transfer/confirm',
    method: 'post',
    data: body,
  });
}
/**
 * 二级调整-转账验证码
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export function codeVerify(body) {
  return axios({
    url: '/split-service/serviceFeeAdjustment/transfer/mobile/code',
    method: 'post',
    data: body,
  });
}

/**
 * 资金申报-下拉框
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export function fundDeclarationSelector() {
  return axios({
    url: '/split-service/fund/declare/selector',
    method: 'get',
  });
}

/**
 * 资金申报-日志
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export function fundDeclarationLogList(body) {
  return axios({
    url: '/split-service/fund/declare/listDeclareRecord',
    method: 'post',
    data: body,
  });
}

/**
 * 资金申报-管理列表
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export function fundDeclarationManageList(body) {
  return axios({
    url: '/split-service/fund/declare/listOrderSummary',
    method: 'post',
    data: body,
  });
}

/**
 * 资金申报-明细列表
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export function fundDeclarationDetailList(body) {
  return axios({
    url: '/split-service/fund/declare/listOrderDetail',
    method: 'post',
    data: body,
  });
}

/**
 * 资金申报-提交申报
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export function fundDeclarationSubmit(body) {
  return axios({
    url: '/split-service/fund/declare/submit',
    method: 'post',
    data: body,
  });
}
/* * 跨境综合税差异列表
 * @see https://yapi.acg.team/project/509/interface/api/139977
 */
export function crossBorderConsolidatedTaxPage(body) {
  return axios({
    url: '/split-service/crossBorderConsolidatedTax/page',
    method: 'post',
    data: body,
  });
}

/**
 * 跨境综合税差异列表-明细列表
 * @see https://yapi.acg.team/project/509/interface/api/139975
 */
export function crossBorderConsolidatedTaxDetailPage(body) {
  return axios({
    url: '/split-service/crossBorderConsolidatedTax/detailPage',
    method: 'post',
    data: body,
  });
}

/**
 * 二级调整差异明细(实缴单调整)
 * @see https://yapi.acg.team/project/509/interface/api/139979
 */
export function crossBorderConsolidatedTaxDetailsPage(body) {
  return axios({
    url:
      '/split-service/serviceFeeAdjustment/crossBorderConsolidatedTaxDetailsPage',
    method: 'post',
    data: body,
  });
}

/**
 * 转账审核管理批量审核
 * @see
 */
export function batchAuditSubmit(body) {
  return axios({
    url: '/split-service/fund/transfer/batchAudit',
    method: 'post',
    data: body,
  });
}

/**
 * 转账审核管理批量终审
 * @see
 */
export function batchFinalAuditSubmit(body) {
  return axios({
    url: '/split-service/fund/transfer/batchFinalAudit',
    method: 'post',
    data: body,
  });
}

export default {
  secondaryOption,
  secondaryDataDownloadTemplate,
  secondaryActualDataMerge,
  secondaryAdjustmentPay,
  secondaryAdjustmentDetail,
  secondaryAdjustmentList,
  secondaryActualList,
  codeVerify,
  doWithdraw,
  getPlatformSubAccountList,
  getMemberSubAccountList,
  getMemberSubAccountOptions,
  getWithdrawRecordOptions,
  getCrossPayoutOptions,
  getWithdrawRecord,
  getCrossPayoutRecord,
  getTradeSubjectOptions,
  getTradeSubjectConfigList,
  getTradeSubjectConfigDetail,
  updateTradeSubjectConfig,
  getTradeSubjectRecords,
  getTradeSubjectRecordOptions,
  restartTradeSubjectRecords,
  getSubAccountTransferOptions,
  getSubAccountTransferList,
  addMemberSubAccountTransfer,
  updateMemberSubAccountTransfer,
  getTransferOrderList,
  getTransferOrderOptions,
  getOpenSubAccountOptions,
  getOpenSubAccountList,
  openSubAccount,
  subAccountBindCard,
  subAccountBindValid,
  getOpenMainPartAccountOptions,
  getOpenMainPartAccountList,
  createMainPartAccount,
  refreshSubAccountBalance,
  createSubAccountTransfer,
  deleteTradeSubjectConfig,
  getGatewayRechargeOptions,
  getGatewayRechargeList,
  doGatewayRecharge,
  unbindingCard,
  firstSplitStatsDaySum,
  firstSplitStatsMonthSum,
  firstSplitStatsDaySumDetail,
  secondaryConfigQuery,
  secondaryTradeSubjectQuery,
  secondaryUploadConfig,
  secondaryItemStatistics,
  secondaryTemplateImport,
  secondaryItemQuery,
  fundSelector,
  fundWithdrawPage,
  fundCrossPayoutPage,
  refreshSubAccountBal,
  bindingCardsList,
  withdrawApply,
  getBindingCardsList,
  bindingCardApply,
  getSubAccountBankList,
  bindingCardAudit,
  bindingCardBinding,
  unBindingApply,
  unBindingAudit,
  subAccountAudit,
  transferApply,
  subAccountOpeningCheck,
  unBindingRetry,
  bindingCardValidAmount,
  mobileCode,
  fundOperLog,
  bankDetailQuery,
  exportHisBalance,
  listHisBalance,
  subAccountDetailSelector,
  platDetailQuery,
  crossBorderConsolidatedTaxPage,
  crossBorderConsolidatedTaxDetailPage,
  crossBorderConsolidatedTaxDetailsPage,
  batchAuditSubmit,
  batchFinalAuditSubmit,
};
