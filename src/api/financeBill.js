import axios from '@/utils/request';

/**
 * 执行主任务
 * @param id 主单 id
 */
export function executeJob(id) {
  return axios({
    url: '/finance-bill/test/executeJob',
    method: 'post',
    data: {
      jobName: 'transactionJob',
      jobParam: id,
    },
  });
}

/**
 * 重试子任务
 * @param transactionId
 * @returns {Promise<AxiosResponse<any>>}
 */
export function restartTransaction(transactionId) {
  return axios({
    url: `/finance-bill/test/transaction/executeJob?transactionId=${transactionId}`,
    method: 'post',
  });
}

/**
 * 关闭主任务
 * @param id 主单 id
 */
export function stopJob(id) {
  return axios({
    url: '/finance-bill/stopTask',
    method: 'post',
    data: {
      serialId: id,
    },
  });
}

export function successTransaction(transactionId) {
  return axios({
    url: `/finance-bill/successTransaction`,
    method: 'post',
    data: {
      serialId: transactionId,
    },
  });
}
/**
 * 删除日志
 * @param ascmCode oa 编码
 */
export function deleteRequestLog(ascmCode) {
  return axios({
    url: `/magpie-bridge/erp/mock/request-log/delete?businessId=${ascmCode}`,
    method: 'post',
  });
}

/**
 * 查询请求日志
 * @param oaCode
 */
export function queryNsBillLog(oaCode) {
  return axios({
    url: `/magpie-bridge/accounting/queryNsSyncBill`,
    method: 'post',
    data: {
      oaCode,
    },
  });
}
