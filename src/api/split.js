import axios from '@/utils/request';

export function splitConfigQuery() {
  return axios({
    url: '/split-service/splitConfig/getList',
    method: 'get',
  });
}

export function splitConfigAdd(data) {
  return axios({
    url: '/split-service/splitConfig/add',
    method: 'post',
    data,
  });
}

export function splitConfigUpdate(data) {
  return axios({
    url: '/split-service/splitConfig/update',
    method: 'post',
    data,
  });
}

export function selector() {
  return axios({
    url: '/split-service/splitConfig/selector',
    method: 'get',
  });
}
