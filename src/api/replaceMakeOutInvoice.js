import axios from '../utils/request';
import { service } from '@/utils/request';

const baseUrl = 'financial-account/withdraw/pay/replaceMakeOutInvoice';

//获取下拉框
export function getComboBoxData() {
  return axios({
    url: 'financial-account/withdraw/pay/getComboBoxData',
    method: 'get',
  });
}
//获取验证码
export function sendIdentifyingCode(params) {
  return axios({
    url: 'financial-account/withdraw/pay/sendIdentifyingCode',
    method: 'get',
    params,
  });
}
/**
 * desc: 代开票提现审批
 * @param {*} data
 * @returns
 */

//列表
export function getReviewList(data) {
  return axios({
    url: baseUrl + '/getReviewList',
    method: 'post',
    data,
  });
}

//获取下拉框
export function getReviewComboBoxData() {
  return axios({
    url: baseUrl + '/getReviewComboBoxData',
    method: 'get',
  });
}

//全量审核数据
export function getAllReviewData(params) {
  return axios({
    url: baseUrl + '/getAllReviewData',
    method: 'get',
    params,
  });
}

//全量审核通过
export function allPass(params) {
  return axios({
    url: baseUrl + '/allPass',
    method: 'get',
    params,
  });
}

//详情
export function getReviewDetail(params) {
  return axios({
    url: baseUrl + '/getReviewDetail',
    method: 'get',
    params,
  });
}

//拒绝
export function reject(data) {
  return axios({
    url: baseUrl + '/reject',
    method: 'post',
    data,
  });
}

//通过
export function pass(params) {
  return axios({
    url: baseUrl + '/pass',
    method: 'get',
    params,
  });
}

/**
 * desc: 代开票提现打款
 * @param {*} data
 * @returns
 */

//列表
export function getPayList(data) {
  return axios({
    url: baseUrl + '/getPayList',
    method: 'post',
    data,
  });
}
//导出
export function exportPayList(data) {
  return axios({
    url: baseUrl + '/exportPayList',
    method: 'post',
    data,
  });
}

//获取下拉框
export function getPayComboBoxData() {
  return axios({
    url: baseUrl + '/getPayComboBoxData',
    method: 'get',
  });
}

//获取全量打款数据
export function getAllPayData(params) {
  return axios({
    url: baseUrl + '/getAllPayData',
    method: 'get',
    params,
  });
}

//全量打款通过
// export const allPay = body => service.post(baseUrl + '/allPay', null, body);
export function allPay(data) {
  return axios({
    url: baseUrl + '/allPay',
    method: 'post',
    data,
  });
}

//详情
export function getPayDetail(params) {
  return axios({
    url: baseUrl + '/getPayDetail',
    method: 'get',
    params,
  });
}

//拒绝
// export function reject(data) {
//   return axios({
//     url: baseUrl + '/reject',
//     method: 'post',
//     data,
//   });
// }

//通过打款
// export const pay = body => service.post(baseUrl + '/pay', null, body);
//通过打款
export function pay(data) {
  return axios({
    url: baseUrl + '/pay',
    method: 'post',
    data,
  });
}

//境外打款下载
export function downloadPayOutExcel(data) {
  return axios({
    url: baseUrl + '/downloadPayOutExcel',
    method: 'post',
    data,
  });
}

//代开票导出全部数据
export function replaceMakeOutInvoiceDownload(data) {
  return axios({
    url:
      '/financial-account/withdraw/pay/replaceMakeOutInvoice/downloadReviewList',
    method: 'post',
    data,
  });
}

//境外打款回传
// export function uploadPayOutResultExcel(params) {
//   return axios({
//     url: baseUrl + '/uploadPayOutResultExcel',
//     method: 'post',
//     params,
//   });
// }

//状态变更
export function borderTypeUpdateStatus(data) {
  return axios({
    url: '/financial-account/withdraw/pay/borderType/updateStatus',
    method: 'post',
    data,
  });
}
