/*
 * @Description:
 * @Author: xuxiang
 * @Date: 2022-07-26 17:19:22
 * @LastEditTime: 2022-08-22 19:23:36
 * @LastEditors: xuxiang
 * @Reference:
 */
import req from '@/utils/newRequest';

// 对账明细列表查询
export function getFundsBillDetailSkipList(body) {
  return req.http.request({
    url: '/recon-core/sales/funds/bill/detail/skipList',
    method: 'post',
    data: body,
  });
}
// 对账明细列表查询
export function getFundsBillDetailList(body) {
  return req.http.request({
    url: '/recon-core/sales/funds/bill/detail/list',
    method: 'post',
    data: body,
  });
}
//对账差异-提交审核
export function submitReview(data) {
  return req.http.request({
    url: '/recon-core/recon/platform/channel/diff/submitReview',
    method: 'post',
    data,
  });
}
//对账差异-审核
export function review(data) {
  return req.http.request({
    url: '/recon-core/recon/platform/channel/diff/review',
    method: 'post',
    data,
  });
}

//对账差异备注修改
export function updateRemark(data) {
  return req.http.request({
    url: '/recon-core/recon/platform/channel/diff/updateRemark',
    method: 'post',
    data,
  });
}
// 对账差异列表查询
export function getBillDifferencesList(body) {
  return req.http.request({
    url: '/recon-core/recon/platform/channel/diff/list',
    method: 'post',
    data: body,
  });
}

// 下拉选择框
export function getFundsBillSelector(params) {
  return req.http.request({
    url: '/recon-core/sales/funds/bill/selector',
    method: 'get',
    params,
  });
}

// 日/月对账报表查询
export function getFundsBillList(body) {
  return req.http.request({
    url: '/recon-core/sales/funds/bill/stat/list',
    method: 'post',
    data: body,
  });
}

// 编辑备注
export function getFundsBillEditRemark(body) {
  return req.http.request({
    url: '/recon-core/sales/funds/bill/edit/remark',
    method: 'post',
    data: body,
  });
}

// 对账明细列表导出
export function fundsBillDetailExport(body) {
  return req.http.request({
    url: '/recon-core/sales/funds/bill/detail/export',
    method: 'post',
    data: body,
  });
}
