/*
 * @Description:
 * @Author: 项萍～
 * @Date: 2021-08-16 10:37:05
 * @LastEditTime: 2021-08-17 14:38:18
 * @LastEditors: 项萍～
 * @Reference:
 */
import axios from '@/utils/request';

/** 查询会计凭证 */
export function getJournalList(data) {
  return axios({
    url: '/magpie-bridge/journal-entry',
    method: 'post',
    data,
  });
}
// 查询公司主体
export function getSubsidiaryList(data) {
  return axios({
    url: '/magpie-bridge/journal-entry/subsidiary/select',
    method: 'post',
    data,
  });
}
// 查询凭证明细
export function getDetail(id) {
  return axios({
    url: '/magpie-bridge/journal-entry/' + id,
    method: 'post',
  });
}

/**
 * 发票明细下载
 */
export function invoiceDownload(data) {
  return axios({
    url: '/magpie-bridge/invoice/download',
    method: 'get',
    params: data,
  });
}
