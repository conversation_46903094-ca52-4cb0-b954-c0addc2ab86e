/*
 * @Description:
 * @Author: bruce
 * @Date: 2021-04-14 10:33:51
 * @LastEditTime: 2021-07-19 15:57:19
 * @LastEditors: 项萍～
 * @Reference:
 */
import injectHost from '@/utils/injectHost';
import { replaceLocalDomain } from '@/utils/index.js';
const BASE_URL = injectHost().apiHost;

let baseUrl = `//${replaceLocalDomain(BASE_URL)}/api`;
export default {
  /**
   * @description:检查token有效性
   * @param  {*}
   * @return {*}
   */
  checkUserToken: [baseUrl + '/sso-auth/auth/checkToken', 'get'],
  /**
   * @description:查看有无应用权限
   * @param  {*}
   * @return {*}
   */
  getAppAccess: [baseUrl + '/sso-auth/customPermission/appAccess', 'post'],
  /**
   * @description:获取当前用户角色
   * @param  {*}
   * @return {*}
   * @param {*} data
   */
  getUserRolesList: [baseUrl + '/sso-auth/ui/role/getRolesByUserId', 'get'],
  //查看当前菜单下的所有按钮列表-用于审批
  getBtnList: [baseUrl + '/sso-auth/permission/menu/buttonList', 'get'],
  //查看所有权限的按钮列表-用于显示隐藏
  getAllPermissionBtnList: [
    baseUrl + '/sso-auth/permission/user/allButtonList',
    'get',
  ],
  // 查看当前菜单下的权限的按钮列表-用于显示隐藏
  getPermissionBtnList: [
    baseUrl + '/sso-auth/permission/user/buttonList',
    'get',
  ],
  //查看是否有改按钮的权限
  permissionAccess: [
    baseUrl + '/sso-auth/customPermission/permissionAccess',
    'post',
  ],
  //获取菜单路由
  getRouterList: [baseUrl + '/sso-auth/permission/getMenuList', 'get'],
};
