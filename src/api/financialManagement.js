import axios from '@/utils/request';

/** 类目信息查询 */
export function getCategoryList(data) {
  return axios({
    url: 'covercharge-service/punish/category/getInfo',
    method: 'post',
    data,
  });
}

/** 会员充值列表(单条可查看) */
export function memberList(data) {
  return axios({
    url: 'cust-bus/member/list',
    method: 'post',
    data,
  });
}

/** 充值 */
export function memberRecharge(data) {
  return axios({
    url: 'cust-bus/member/recharge',
    method: 'post',
    data,
  });
}

/** 提现银行卡列表 */
export function withdrawalBankCardList(data) {
  return axios({
    url: 'cust-bus/withdrawalBankCard/list',
    method: 'post',
    data,
  });
}

/** 提现银行卡 - 添加备注 */
export function withdrawalBankCardNote(data) {
  return axios({
    url: 'cust-bus/withdrawalBankCard/note',
    method: 'post',
    data,
  });
}

/** 提现银行卡 - 添加提现银行卡 */
export function withdrawalBankCardCreate(data) {
  return axios({
    url: 'cust-bus/withdrawalBankCard/create',
    method: 'post',
    data,
  });
}

/** 会员支付查询列表 */
export function membersPayQueryList(data) {
  return axios({
    url: 'cust-bus/membersPayQuery/list',
    method: 'post',
    data,
  });
}

/** 充值申请&审核列表 */
export function rechargeApplicationList(data) {
  return axios({
    url: 'cust-bus/rechargeApplication/list',
    method: 'post',
    data,
  });
}

/** 充值申请&审核 - 添加充值申请 */
export function rechargeApplicationCreate(data) {
  return axios({
    url: 'cust-bus/rechargeApplication/create',
    method: 'post',
    data,
  });
}

/** 充值申请&审核列表 - 撤销申请 */
export function rechargeApplicationRevert(data) {
  return axios({
    url: 'cust-bus/rechargeApplication/revert',
    method: 'post',
    data,
  });
}

/** 充值申请&审核列表 - 提交申请 */
export function rechargeApplicationSubmit(data) {
  return axios({
    url: 'cust-bus/rechargeApplication/submit',
    method: 'post',
    data,
  });
}

/** 充值申请&审核列表 - 审核 */
export function rechargeApplicationAudit(data) {
  return axios({
    url: 'cust-bus/rechargeApplication/audit',
    method: 'post',
    data,
  });
}
/** 充值申请&审核列表 - 更新申请 */
export function rechargeApplicationUpdate(data) {
  return axios({
    url: 'cust-bus/rechargeApplication/update',
    method: 'post',
    data,
  });
}

/** 充值申请&审核列表 - 详情 */
export function rechargeApplicationDetails(data) {
  return axios({
    url: `cust-bus/rechargeApplication/${data.id}`,
    method: 'get',
  });
}

/** 退货款申请列表 */
export function returnPaymentApplicantList(data) {
  return axios({
    url: 'cust-bus/returnPaymentApplicant/list',
    method: 'post',
    data,
  });
}

/** 退货款申请 - 删除申请 */
export function returnPaymentApplicantDel(data) {
  return axios({
    url: 'cust-bus/returnPaymentApplicant/del',
    method: 'post',
    data,
  });
}

/** 退货款申请 - 提交申请 */
export function returnPaymentApplicantSubmit(data) {
  return axios({
    url: 'cust-bus/returnPaymentApplicant/submit',
    method: 'post',
    data,
  });
}

/** 退货款申请 - 添加退货款申请 */
export function returnPaymentApplicantCreate(data) {
  return axios({
    url: 'cust-bus/returnPaymentApplicant/create',
    method: 'post',
    data,
  });
}

/** 退货款申请 - 修改退货款申请 */
export function returnPaymentApplicantModify(data) {
  return axios({
    url: 'cust-bus/returnPaymentApplicant/modify',
    method: 'post',
    data,
  });
}

/** 退货款申请 - 审核(初审&复审) */
export function returnPaymentApplicantAudit(data) {
  return axios({
    url: 'cust-bus/returnPaymentApplicant/audit',
    method: 'post',
    data,
  });
}

/** 退货款申请 - 批量审核通过(复审&初审) */
export function returnPaymentApplicantBatchAuditPass(data) {
  return axios({
    url: 'cust-bus/returnPaymentApplicant/audit/batch',
    method: 'post',
    data,
  });
}

/** 提现充值申请/审核 - 列表 */
export function withdrawPayApplyForControllerList(data) {
  return axios({
    url: 'cust-bus/withdrawPayApplyFor/list',
    method: 'post',
    data,
  });
}

/** 提现充值申请/审核 - 添加 */
export function withdrawPayApplyForControllerCreate(data) {
  return axios({
    url: 'cust-bus/withdrawPayApplyFor/create',
    method: 'post',
    data,
  });
}

/** 提现充值申请/审核 - 修改 */
export function withdrawPayApplyForControllerModify(data) {
  return axios({
    url: 'cust-bus/withdrawPayApplyFor/modify',
    method: 'post',
    data,
  });
}

/** 提现充值申请/审核 - 提交 */
export function withdrawPayApplyForControllerSubmit(data) {
  return axios({
    url: 'cust-bus/withdrawPayApplyFor/submit',
    method: 'post',
    data,
  });
}

/** 提现充值申请/审核 - 删除 */
export function withdrawPayApplyForControllerDel(data) {
  return axios({
    url: 'cust-bus/withdrawPayApplyFor/del',
    method: 'post',
    data,
  });
}

/** 提现充值申请/审核 - 批量审核通过(复审&初审) */
export function withdrawPayApplyForControllerBatchAuditPass(data) {
  return axios({
    url: 'cust-bus/withdrawPayApplyFor/batchAuditPass',
    method: 'post',
    data,
  });
}

/** 提现充值申请/审核 - 审核通过(复审&初审) */
export function withdrawPayApplyForControllerBatchAudit(data) {
  return axios({
    url: 'cust-bus/withdrawPayApplyFor/audit',
    method: 'post',
    data,
  });
}

/** 提现列表查询 */
export function listWidthdraw(data) {
  return axios({
    url: '/cust-bus/widthdraw/list',
    method: 'get',
    params: data,
  });
}

/** 提现状态列表 */
export function listConfig(data) {
  return axios({
    url: '/cust-bus/widthdraw/listConfig',
    method: 'get',
    params: data,
  });
}

/** 提现获取验证码手机号列表 */
export function accessToVerifyPhoneNumber(data) {
  return axios({
    url: '/cust-bus/playWithConfirmation/accessToVerifyPhoneNumber',
    method: 'get',
    params: data,
  });
}

/** 验证码通知 */
export function notSinceCertificationBatchSms(data) {
  return axios({
    url: '/cust-bus/playWithConfirmation/notSinceCertificationBatchSms',
    method: 'post',
    data,
  });
}

/** 提现验证码验证 */
export function validationCode(data) {
  return axios({
    url: '/cust-bus/playWithConfirmation/validationCode',
    method: 'post',
    data,
  });
}

/** 提现打款 */
export function playWithConfirmation(data) {
  return axios({
    url: '/cust-bus/playWithConfirmation/playWithConfirmation',
    method: 'post',
    data,
  });
}

/** 导出 */
export function withdrawalExport(data) {
  return axios({
    url: '/cust-bus/widthdraw/listExport',
    method: 'get',
    data,
  });
}
/*老货款转货款 */
export function tranfer2depo(data) {
  return axios({
    url: '/cust-account/cust/account/tranfer2depo',
    method: 'post',
    data,
  });
}
/*查询余额 */
export function queryUnionBalance(data) {
  return axios({
    url: '/cust-account/cust/account/union/balance',
    method: 'get',
    params: data,
  });
}
// 驳回
export function rejectWidthdraw(data) {
  return axios({
    url: '/cust-bus/widthdraw/reject',
    method: 'post',
    data,
  });
}
