import { getCookie } from '@/utils/auth';
import Vue from 'vue';
import axios from 'axios';
const baseUrl = window.location.protocol + '//' + window.ACCESS_HOSTS.apiHost;
import store from '@/store';

export function exportSettleBatch(batchOrderId) {
  return axios({
    method: 'get',
    params: { batchOrderId },
    responseType: 'blob',
    headers: {
      token: getCookie(),
      'Content-Type': 'octet-stream;charset=UTF-8',
      appCode: process.env.VUE_APP_LOGIN_APP_CODE,
    },
    url: baseUrl + '/api/settle-center/batchOrder/export',
  })
    .then(res => {
      if (res.data.type === 'application/json') {
        let reader = new FileReader();
        reader.onload = e => {
          let errData = JSON.parse(e.target.result);
          const msg = errData.msg || errData.message || '导出失败';
          Vue.prototype.$baseMessage(msg, 'error');
          return Promise.reject(msg);
        };
        reader.readAsText(res.data);
      } else {
        return res;
      }
    })
    .catch(e => {
      Vue.prototype.$baseMessage(e, 'error');
      return Promise.reject(e);
    });
}

export function exportApplyMenu(data) {
  return axios({
    method: 'post',
    data,
    responseType: 'blob',
    headers: {
      token: getCookie(),
      appCode: process.env.VUE_APP_LOGIN_APP_CODE,
    },
    url: baseUrl + '/api/covercharge-service/punish/apply/exportApply',
  })
    .then(res => {
      if (res.data.type === 'application/json') {
        let reader = new FileReader();
        reader.onload = e => {
          let errData = JSON.parse(e.target.result);
          const msg = errData.msg || errData.message || '导出失败';
          Vue.prototype.$baseMessage(msg, 'error');
          return Promise.reject(msg);
        };
        reader.readAsText(res.data);
      } else {
        return res;
      }
    })
    .catch(e => {
      Vue.prototype.$baseMessage(e, 'error');
      return Promise.reject(e);
    });
}

export function exportAuditMenu(data) {
  return axios({
    method: 'post',
    params: data,
    responseType: 'blob',
    headers: {
      token: getCookie(),
      appCode: process.env.VUE_APP_LOGIN_APP_CODE,
    },
    url: baseUrl + '/api/covercharge-service/punish/deal/exportInfo',
  })
    .then(res => {
      if (res.data.type === 'application/json') {
        let reader = new FileReader();
        reader.onload = e => {
          let errData = JSON.parse(e.target.result);
          const msg = errData.msg || errData.message || '导出失败';
          Vue.prototype.$baseMessage(msg, 'error');
          return Promise.reject(msg);
        };
        reader.readAsText(res.data);
      } else {
        return res;
      }
    })
    .catch(e => {
      Vue.prototype.$baseMessage(e, 'error');
      return Promise.reject(e);
    });
}

export function exportExcel(params = {}, url, methods = 'get') {
  let Base64 = require('js-base64').Base64;
  const username = store.state.user.username;
  const userId = store.state.user.userInfo.id;
  return axios({
    method: methods,
    params: params,
    data: params,
    responseType: 'blob',
    headers: {
      token: getCookie(),
      userName: Base64.encode(username),
      userId: userId,
      appCode: process.env.VUE_APP_LOGIN_APP_CODE,
    },
    url: baseUrl + url,
  })
    .then(res => {
      if (res.data.type === 'application/json') {
        let reader = new FileReader();
        reader.onload = e => {
          let errData = JSON.parse(e.target.result);
          const msg = errData.msg || errData.message || '导出失败';
          Vue.prototype.$baseMessage(msg, 'error');
          return Promise.reject(msg);
        };
        reader.readAsText(res.data);
      } else {
        return res;
      }
    })
    .catch(e => {
      Vue.prototype.$baseMessage(e, 'error');
      return Promise.reject(e);
    });
}

export function newExportExcel(params = {}, url, methods = 'get') {
  let Base64 = require('js-base64').Base64;
  const username = store.state.user.username;
  const userId = store.state.user.userInfo.id;
  const bodyParams = {
    method: methods,
    responseType: 'blob',
    headers: {
      token: getCookie(),
      userName: Base64.encode(username),
      userId: userId,
      appCode: process.env.VUE_APP_LOGIN_APP_CODE,
    },
    url: baseUrl + url,
  };
  if (methods === 'get') {
    bodyParams.params = params;
  } else {
    bodyParams.data = params;
  }
  return axios(bodyParams)
    .then(res => {
      if (res.data.type === 'application/json') {
        let reader = new FileReader();
        reader.onload = e => {
          let errData = JSON.parse(e.target.result);
          const msg = errData.msg || errData.message || '导出失败';
          Vue.prototype.$baseMessage(msg, 'error');
          return Promise.reject(msg);
        };
        reader.readAsText(res.data);
      } else {
        return res;
      }
    })
    .catch(e => {
      Vue.prototype.$baseMessage(e, 'error');
      return Promise.reject(e);
    });
}
