/*
 * @Description:
 * @Author: xuxiang
 * @Date: 2022-05-18 14:08:19
 * @LastEditTime: 2022-05-23 14:37:03
 * @LastEditors: xuxiang
 * @Reference:
 */

import { service } from '@/utils/request';

/*
 * 关系列表
 * @see https://yapi.acg.team/project/1053/interface/api/139917
 */
export const relationLookupList = body =>
  service.post('/magpie-bridge/base-data-relation-lookup/list', null, body);

/*
 * 映射模型下拉框
 * @see https://yapi.acg.team/project/1053/interface/api/139923
 */
export const relationLookupModuleSelect = body =>
  service.post(
    '/magpie-bridge/base-data-relation-lookup/moduleSelect',
    null,
    body,
  );

/*
 * 映射模型下拉框
 * @see https://yapi.acg.team/project/1053/interface/api/139923
 */
export const relationLookupSelectModule = body =>
  service.post(
    '/magpie-bridge/base-data-relation-lookup/selector-module',
    null,
    body,
  );

/*
 * 映射模型下拉框id-名称映射
 * @see https://yapi.acg.team/project/1053/interface/api/139923
 */
export const relationLookupModuleSelectModuleMapping = body =>
  service.post(
    '/magpie-bridge/base-data-relation-lookup/selector-module-mapping',
    null,
    body,
  );

/*
 * 关系列表 删除
 * @see https://yapi.acg.team/project/1053/interface/api/139921
 */
export const relationLookupDelete = body =>
  service.post('/magpie-bridge/base-data-relation-lookup/delete', null, body);

/*
 * 关系新增
 * @see https://yapi.acg.team/project/1053/interface/api/139919
 */
export const relationLookupAdd = body =>
  service.post('/magpie-bridge/base-data-relation-lookup/add', null, body);

/*
 * 关系新增
 * @see https://yapi.acg.team/project/1053/interface/api/139925
 */
export const relationLookupUpdate = body =>
  service.post('/magpie-bridge/base-data-relation-lookup/update', null, body);

/*
 * 模型列表
 * @see https://yapi.acg.team/project/1053/interface/api/139925
 */
export const relationModuleList = body =>
  service.post('/magpie-bridge/base-data-relation-module/list', null, body);

/*
 * 模型新增
 * @see https://yapi.acg.team/project/1053/interface/api/139925
 */
export const relationModuleAdd = body =>
  service.post('/magpie-bridge/base-data-relation-module/add', null, body);

/*
 * 模型修改
 * @see https://yapi.acg.team/project/1053/interface/api/139925
 */
export const relationModuleUpdate = body =>
  service.post('/magpie-bridge/base-data-relation-module/update', null, body);

/*
 * 模型删除
 * @see https://yapi.acg.team/project/1053/interface/api/139925
 */
export const relationModuleDelete = body =>
  service.post('/magpie-bridge/base-data-relation-module/delete', null, body);
