import axios from '@/utils/request';

/** 类目信息查询 */
export function getCategoryList(data) {
  return axios({
    url: 'covercharge-service/punish/category/getInfo',
    method: 'post',
    data,
  });
}

/** 获取字典数据 */
export function getDataDictAll(data) {
  return axios({
    url: '/affiliate-transaction/dataDict/all',
    method: 'get',
    params: data,
  });
}

/** 主体管理、定位管理 - 列表 */
export function getlistCompanyMain(data) {
  return axios({
    url: '/affiliate-transaction/company/main/listCompanyMain',
    method: 'post',
    data,
  });
}
/** 主体管理 - 编辑 */
export function editCompanyMain(data) {
  return axios({
    url: '/affiliate-transaction/company/main/editCompanyMain',
    method: 'post',
    data,
  });
}

/** 证照列表 */
export function getlistCompanyLicense(data) {
  return axios({
    url: '/affiliate-transaction/company/main/listCompanyLicense',
    method: 'get',
    params: data,
  });
}

/** 主体管理、定位管理 - 状态操作 */
export function statusOperation(data) {
  return axios({
    url: '/affiliate-transaction/company/main/statusOperation',
    method: 'post',
    data,
  });
}

/** 定位管理 - 列表 */
export function getListCompanyPosition(data) {
  return axios({
    url: '/affiliate-transaction/company/main/listCompanyPosition',
    method: 'get',
    params: data,
  });
}

/** 定位管理 - 编辑定位 */
export function editCompanyPosition(data) {
  return axios({
    url: '/affiliate-transaction/company/main/editCompanyPosition',
    method: 'post',
    data,
  });
}

/*** ---------  服务交易 ---------*****/

/** 服务提供方树状列表 */
export function serviceProviderList(data) {
  return axios({
    url: '/affiliate-transaction/coverage/serviceProvider/list',
    method: 'get',
    params: data,
  });
}

/** 服务提供方树新增 */
export function serviceProviderAdd(data) {
  return axios({
    url: '/affiliate-transaction/coverage/serviceProvider/add',
    method: 'post',
    data,
  });
}

/** 查询服务交易日志 */
export function getQuerySettlementLog(data) {
  return axios({
    url: '/affiliate-transaction/coverage/querySettlementLog',
    method: 'post',
    data,
  });
}

/** 查询结算方案 */
export function getSettlementRegulation(data) {
  return axios({
    url: '/affiliate-transaction/coverage/settlementRegulation',
    method: 'get',
    params: data,
  });
}

/** 服务交易日志审核 */
export function settlementLogApprove(data) {
  return axios({
    url: 'affiliate-transaction/coverage/settlementLogApprove',
    method: 'post',
    data,
  });
}

/** 服务交易日志开票 */
export function settlementLogInvoicing(data) {
  return axios({
    url: 'affiliate-transaction/coverage/settlementLogInvoicing',
    method: 'post',
    data,
  });
}

/** 服务交易日志复核 */
export function settlementLogVerify(data) {
  return axios({
    url: 'affiliate-transaction/coverage/settlementLogVerify',
    method: 'post',
    data,
  });
}

/** 服务接收方新增 */
export function serviceConsumerAdd(data) {
  return axios({
    url: 'affiliate-transaction/coverage/serviceConsumer/add',
    method: 'post',
    data,
  });
}

/** 服务接收方编辑 */
export function serviceConsumerEdit(data) {
  return axios({
    url: 'affiliate-transaction/coverage/serviceConsumer/edit',
    method: 'post',
    data,
  });
}

/** 服务接收方废弃 */
export function serviceConsumerDiscard(data) {
  return axios({
    url: 'affiliate-transaction/coverage/serviceConsumer/discard',
    method: 'get',
    params: data,
  });
}

/** 服务接收方启用 */
export function serviceConsumerEnable(data) {
  return axios({
    url: 'affiliate-transaction/coverage/serviceConsumer/enable',
    method: 'get',
    params: data,
  });
}

/** 服务接收方创建合同 */
export function serviceConsumerAddContract(data) {
  return axios({
    url: 'affiliate-transaction/coverage/serviceConsumer/addContract',
    method: 'get',
    params: data,
  });
}

/** 服务接收方提交OA */
export function serviceConsumerSubmitOa(data) {
  return axios({
    url: 'affiliate-transaction/coverage/serviceConsumer/submitOa',
    method: 'get',
    params: data,
  });
}

/** 服务接收方列表 */
export function serviceConsumerList(data) {
  return axios({
    url: '/affiliate-transaction/coverage/serviceConsumer/list',
    method: 'get',
    params: data,
  });
}

/** 手动发起结算 */
export function settlementManual(data) {
  return axios({
    url: '/affiliate-transaction/coverage/settlementManual',
    method: 'post',
    data,
  });
}

/** 结算方案配置 */
export function settlementRegulationEdit(data) {
  return axios({
    url: '/affiliate-transaction/coverage/settlementRegulation/edit',
    method: 'post',
    data,
  });
}

/** 结算方案配置 */
export function getQueryLinkView(data) {
  return axios({
    url: '/affiliate-transaction/coverage/queryLinkView',
    method: 'post',
    data,
  });
}

/** 结算方案配置 */
export function saveSettlementRule(data) {
  return axios({
    url: '/affiliate-transaction/coverage/serviceModel/saveSettlementRule',
    method: 'post',
    data,
  });
}

/** 交易类型生效 */
export function serviceModelTurnNo(data) {
  return axios({
    url: '/affiliate-transaction/coverage/serviceModel/turnNo',
    method: 'post',
    data,
  });
}

/** 生成合同 */
export function serviceModelGet(data) {
  return axios({
    url: '/affiliate-transaction/coverage/serviceModel/get',
    method: 'get',
    params: data,
  });
}

/*** ---------  关联交易管理 ---------*****/

/** 关联交易列表 */
export function getAffTransList(data) {
  return axios({
    url: '/affiliate-transaction/affTrans/list',
    method: 'post',
    data,
  });
}

/** 生成合同 */
export function addContract(data) {
  return axios({
    url: '/affiliate-transaction/affTrans/addContract',
    method: 'get',
    params: data,
  });
}

/** 启用交易链路 */
export function enable(data) {
  return axios({
    url: '/affiliate-transaction/affTrans/enable',
    method: 'get',
    params: data,
  });
}

/** 购销链路查询关联交易 */
export function listByGxId(data) {
  return axios({
    url: '/affiliate-transaction/affTrans/listByGxId',
    method: 'get',
    params: data,
  });
}

/** 提交选择的定价模型 */
export function addselectPricing(data) {
  return axios({
    url: '/affiliate-transaction/affTrans/selectPricing',
    method: 'get',
    params: data,
  });
}

/** 检查链路模型 */
export function checkLinkModel(data) {
  return axios({
    url: '/affiliate-transaction/affTrans/checkLinkModel',
    method: 'get',
    params: data,
  });
}

/*** ---------  链路模型管理 ---------*****/

/** 查询链路模型 */
export function getLinkModelList(data) {
  return axios({
    url: 'affiliate-transaction/linkModel/list',
    method: 'post',
    data,
  });
}

/** 查询定价模型 */
export function linkCompanyList(data) {
  return axios({
    url: 'affiliate-transaction/linkCompany/list',
    method: 'post',
    data,
  });
}

/** 新增定价模型 */
export function linkCompanyAdd(data) {
  return axios({
    url: 'affiliate-transaction/linkCompany/add',
    method: 'post',
    data,
  });
}

/** 新增/编辑链路模型 */
export function saveOrUpdateLinkModel(data) {
  return axios({
    url: 'affiliate-transaction/linkModel/saveOrUpdate',
    method: 'post',
    data,
  });
}

/** 提交审批 */
export function submitLinkModel(data) {
  return axios({
    url: 'affiliate-transaction/linkModel/submit',
    method: 'post',
    data,
  });
}

/*** ---------  定价模型管理  ---------*****/

/** 定价模型列表 */
export function getPricingModelList(data) {
  return axios({
    url: '/affiliate-transaction/pricingModel/list',
    method: 'post',
    data,
  });
}

export function selectPricingList(data) {
  return axios({
    url: '/affiliate-transaction/pricingModel/selectPricing',
    method: 'get',
    params: data,
  });
}

/**  新增定价模型 */
export function pricingModelAdd(data) {
  return axios({
    url: '/affiliate-transaction/pricingModel/add',
    method: 'post',
    data,
  });
}
/**  编辑定价模型 */
export function pricingModelEdit(data) {
  return axios({
    url: '/affiliate-transaction/pricingModel/edit',
    method: 'post',
    data,
  });
}

/**  获取关联交易的定价模型列表 */
export function selectPricingByAffTrans(data) {
  return axios({
    url: '/affiliate-transaction/pricingModel/selectPricingByAffTrans',
    method: 'get',
    params: data,
  });
}

/**  获取服务交易的定价模型列表 */
export function selectPricingByCoverage(data) {
  return axios({
    url: '/affiliate-transaction/pricingModel/selectPricingByCoverage',
    method: 'get',
    params: data,
  });
}

/**  执行中的公司主体列表 */
export function companyMainMap(data) {
  return axios({
    url: '/affiliate-transaction/coverage/companyMainMap',
    method: 'get',
    params: data,
  });
}

/*** ------------------  购销链路管理  ---------------*****/

/**  购销链路列表 */
export function listPurchaseSaleList(data) {
  return axios({
    url: 'affiliate-transaction/purchaseSale/listPurchaseSale',
    method: 'get',
    params: data,
  });
}

/**  定价配置列表 */
export function listPriceConfigByPurchaseSaleId(data) {
  return axios({
    url: 'affiliate-transaction/purchaseSale/listPriceConfigByPurchaseSaleId',
    method: 'get',
    params: data,
  });
}

/**  保存购销链路定价配置 */
export function savePurchaseSalePriceConfig(data) {
  return axios({
    url: '/affiliate-transaction/purchaseSale/savePurchaseSalePriceConfig',
    method: 'post',
    data,
  });
}

/**  链路定价配置节点 */
export function getLinkPriceNodeList(data) {
  return axios({
    url: 'affiliate-transaction/purchaseSale/getLinkPriceNodeList',
    method: 'get',
    params: data,
  });
}

/**  交易链路日志 */
export function listTradeLinkLogList(data) {
  return axios({
    url: 'affiliate-transaction/purchaseSale/listTradeLinkLog',
    method: 'get',
    params: data,
  });
}

/**  根据关联交易Id交易链路日志列表 */
export function listTradeLinkLogByAtId(data) {
  return axios({
    url: 'affiliate-transaction/purchaseSale/listTradeLinkLogByAtId',
    method: 'get',
    params: data,
  });
}

/**  购销链路日志 */
export function listPurchaseSaleLinkLog(data) {
  return axios({
    url: 'affiliate-transaction/purchaseSale/listPurchaseSaleLinkLog',
    method: 'get',
    params: data,
  });
}
