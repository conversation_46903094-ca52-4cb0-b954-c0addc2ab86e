import axios from '@/utils/request';

export function pageImportRecord(data) {
  return axios({
    url: '/business-finance/pageImportRecord',
    method: 'post',
    data,
  });
}

export function pageShopFlow(data) {
  return axios({
    url: '/business-finance/pageShopFlow',
    method: 'post',
    data,
  });
}

export function pageOrderDetail(data) {
  return axios({
    url: '/business-finance/pageOrderDetail',
    method: 'post',
    data,
  });
}

export function pageNsCost(data) {
  return axios({
    url: '/business-finance/pageNsCost',
    method: 'post',
    data,
  });
}

export function importFile(data) {
  return axios({
    url: '/business-finance/importFile',
    method: 'post',
    data,
  });
}

export function getOptionList(params) {
  return axios({
    url: '/business-finance/getOptionList',
    method: 'get',
    params: params,
  });
}

export function invalidFile(params) {
  return axios({
    url: '/business-finance/invalidFile',
    method: 'get',
    params: params,
  });
}

export function retryForFailed(params) {
  return axios({
    url: '/business-finance/retryForFailed',
    method: 'get',
    params: params,
  });
}

export function retrySyncNs(params) {
  return axios({
    url: '/business-finance/retrySyncNs',
    method: 'get',
    params: params,
  });
}

export function getQuickTagCounts(params) {
  return axios({
    url: '/business-finance/getQuickTagCounts',
    method: 'get',
    params: params,
  });
}
