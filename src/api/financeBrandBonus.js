/*
 * @Author: 七七
 * @Date: 2022-02-22 20:04:48
 * @LastEditors: xuxiang
 * @LastEditTime: 2022-04-28 17:46:29
 * @FilePath: /access-fmis-web/src/api/financeBrandBonus.js
 */
import req from '@/utils/newRequest';

// 加成记录列表
export function QueryBrandList(body) {
  return req.http.request({
    url: 'contract-center/addition/list',
    method: 'post',
    data: body,
  });
}
// 加成列表下拉框
export function getListOption() {
  return req.http.request({
    url: 'contract-center/addition/getComboBoxData',
    method: 'get',
  });
}
// 加成记录新建
export function createBrandBonus(body) {
  return req.http.request({
    url: 'contract-center/addition/add',
    method: 'post',
    data: body,
  });
}
// 加成记录新建-导入Excel
export function createBrandBonusExport(body) {
  return req.http.request({
    url: 'contract-center/addition/add/importExcel',
    method: 'post',
    data: body,
  });
}
// 加成记录编辑
export function editBrandBonus(body) {
  return req.http.request({
    url: 'contract-center/addition/edit',
    method: 'post',
    data: body,
  });
}
// 加成记录编辑-导入Excel
export function editBrandBonusExport(body) {
  return req.http.request({
    url: 'contract-center/addition/add/importExcel',
    method: 'post',
    data: body,
  });
}
// 加成记录详情
export function getBrandDetail(query) {
  return req.http.request({
    url: 'contract-center/addition/detail',
    method: 'get',
    params: query,
  });
}
// 作废
export function cancelBrand(body) {
  return req.http.request({
    url: 'contract-center/addition/cancel',
    method: 'post',
    data: body,
  });
}
// 加成记录日志列表
export function journalList(body) {
  return req.http.request({
    url: 'contract-center/addition/operateLogList',
    method: 'post',
    data: body,
  });
}
// 终止
export function stopBrand(body) {
  return req.http.request({
    url: 'contract-center/addition/stop',
    method: 'post',
    data: body,
  });
}
// 获取品牌和货品信息
export function queryBrandInfo(query) {
  return req.http.request({
    url: 'contract-center/addition/listBrandInfo',
    method: 'get',
    params: query,
  });
}

// 加成信息模板下载
export function downloadTemplate() {
  return req.http.request({
    url: 'contract-center/addition/downloadImportTemplate',
    method: 'get',
  });
}

// 加成信息模板下载
export function downloadBrandAdditionInAdd() {
  return req.http.request({
    url: 'contract-center/addition/downloadBrandAdditionInAdd',
    method: 'get',
  });
}

// 加成信息模板下载
export function downloadBrandAdditionInEdit() {
  return req.http.request({
    url: 'contract-center/addition/downloadBrandAdditionInEdit',
    method: 'get',
  });
}
