import axios from '@/utils/request';
/**
 * 采购申请列表分页
 * api url: https://yapi.idanchuang.net/project/561/interface/api/19091
 */
export function getPurchaseApplyList(data) {
  return axios({
    url: '/financial-integration/purchaseOrder/list',
    method: 'post',
    data,
  });
}
// 采购申请列表--详情
export function getPurchaseApplyDetail(data) {
  return axios({
    url: '/financial-integration/purchaseOrder/getPurchaseOrderInfo',
    method: 'get',
    params: data,
  });
}
// 采购订单明细列表
export function getPurchaseApplyDetailList(data) {
  return axios({
    url: '/financial-integration/pasPurchaseOrder/list',
    method: 'post',
    data: data,
  });
}
// 采购订单明细列表--详情
export function getPurchaseApplyDetailDetail(data) {
  return axios({
    url: '/financial-integration/pasPurchaseOrder/getPasPurPchaseOrderInfo',
    method: 'get',
    params: data,
  });
}
// 对账单订单列表
export function getLetterList(data) {
  return axios({
    url: '/financial-integration/reconOrder/getList',
    method: 'post',
    data,
  });
}
// 对账单订单列表 --详情
export function getLetterDetail(data) {
  return axios({
    url: `/financial-integration/reconOrder/getReconDetail/${data.id}`,
    method: 'get',
    // params: data,
  });
}
// 结算单订单列表
export function getSettleList(data) {
  return axios({
    url: '/financial-integration/settlement/list',
    method: 'post',
    data: data,
  });
}
// 结算单订单列表 --详情
export function getSettleDetail(data) {
  return axios({
    url: '/financial-integration/settlement/getDetail',
    method: 'get',
    params: data,
  });
}
// 结算单订单列表 --明细列表

export function getSettleDetailList(data) {
  return axios({
    url: '/financial-integration/settlement/listReconDetails',
    method: 'get',
    params: data,
  });
}
// 结算单订单列表 --发票数据
export function getSettleDetailInvoice(data) {
  return axios({
    url: '/financial-integration/settlement/listInvoiceDetails',
    method: 'get',
    params: data,
  });
}
// 销售订单列表
export function getSaleList(data) {
  return axios({
    url: '/financial-integration/saleOrder/list',
    method: 'post',
    data,
  });
}

// 销售订单列表 --详情
export function getSaleDetail(data) {
  return axios({
    url: '/financial-integration/saleOrder/getSaleOrderInfo',
    method: 'get',
    params: data,
  });
}

export function editSalePrice(data) {
  return axios({
    url: '/financial-integration/saleOrder/updateSalePrice',
    method: 'post',
    data,
  });
}
/**
 * pasPurchaseStatus 采购明细状态,
 * reconStatus 对账单状态,
 * settlementStatus 结算单状态
 * invoiceStatus 发票状态
 * saleStatus 销售单状态
 * @param {*} data
 */
export function getPurchaseDict(data) {
  return axios({
    url: '/financial-integration/component/selector/getSelectors',
    method: 'get',
    params: data,
  });
}

// 手动提交OA
export function submitOA(data) {
  return axios({
    url: '/financial-integration/settlement/submitToOa',
    method: 'get',
    params: data,
  });
}
