import axios from '@/utils/request';

//汇率查询
export function getExchangeList(data) {
  return axios({
    url: '/mdm/exchange_rate/page_list',
    method: 'post',
    data,
  });
}
export function getExchangeChannelList(params) {
  return axios({
    url: '/mdm/exchange_rate/list_channelinfo',
    method: 'get',
    params,
  });
}
//获取币种列表
export function getCurrencyList(params) {
  return axios({
    url: '/mdm/currency/list_all',
    method: 'get',
    params,
  });
}
//汇率获取日志列表查询
export function getExchangeLogList(data) {
  return axios({
    url: '/mdm/exchange_rate_execute_job/page_list',
    method: 'post',
    data,
  });
}
//重新获取汇率，重新下载
export function reDownload(data) {
  return axios({
    url: `/mdm/exchange_rate_execute_job/reexecute/${data.id}`,
    method: 'put',
  });
}
//查询汇率设置信息
export function getExchangeRateConfig(params) {
  return axios({
    url: '/mdm/exchange_rate/get_config',
    method: 'get',
    params,
  });
}
//保存汇率设置信息
export function saveExchangeRateConfig(data) {
  return axios({
    url: `/mdm/exchange_rate/save_config`,
    method: 'post',
    data,
  });
}
//获取操作日志
export function getLogList(params) {
  return axios({
    url: `/mdm/exchange_rate/loglist`,
    method: 'get',
    params,
  });
}
