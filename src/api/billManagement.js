/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-08-20 14:40:13
 * @LastEditors: xuxiang
 * @LastEditTime: 2022-09-15 10:54:11
 * @FilePath: /access-fmis-web/src/api/billManagement.js
 * @Description: 账单管理
 */
import axios from '@/utils/request';

/**
 * @description: 账单列表
 * @param {*} data
 * @return {*}
 */
export function getBillList(data) {
  return axios({
    url: '/finance-bill/bill/manageQuery',
    method: 'post',
    data,
  });
}

/**
 * @description: 账单请款单列表
 * @param {*} data
 * @return {*}
 */
export function getBillRequisition(data) {
  return axios({
    url: '/finance-bill/bill/requisitionQuery',
    method: 'post',
    data,
  });
}

/**
 * @description: 生成请款单
 * @param {*} billNoList: []
 * @return {*}
 */
export function createRequisition(data) {
  return axios({
    url: '/finance-bill/bill/requisitionGenerate',
    method: 'post',
    data,
  });
}

/**
 * @description: 生成请款单预览
 * @param {*} billNoList: []
 * @return {*}
 */
export function previewRequisition(data) {
  return axios({
    url: '/finance-bill/bill/requisitionPreview',
    method: 'post',
    data,
  });
}

/**
 * @description: 查询日志
 * @param {*} relationId
 * @return {*}
 */
export function getBillOperateLog(data) {
  return axios({
    url: '/finance-bill/bill/operatorLogQuery',
    method: 'post',
    data,
  });
}

/**
 * @description: 账单详情
 * @param {*} billNo
 * @return {*}
 */
export function getBillDetail(params) {
  return axios({
    url: '/finance-bill/bill/detailGet',
    method: 'get',
    params,
  });
}

export function detailList(data) {
  return axios({
    url: '/finance-bill/bill/detailList',
    method: 'post',
    data,
  });
}

/**
 * @description: 账单下载
 * @param {*} billNo
 * @return {*}
 */
export function billDownload(params) {
  return axios({
    url: '/finance-bill/bill/download',
    method: 'get',
    params,
  });
}

/**
 * @description: 获取PO明细
 * @param {*} purchaseNo、goodsBarcode
 * @return {*}
 */
export function getPODetail(params) {
  return axios({
    url: '/finance-bill/bill/purchaseItemList',
    method: 'get',
    params,
  });
}

/**
 * @description: 获取供应商、主体列表
 * @param {*} data
 * @return {*}
 */
export function getSupplier(data) {
  return axios({
    url: '/contract-center/welfareWednesday/contract/records/selector',
    method: 'post',
    data,
  });
}

/**
 * @description:根据主体code获取主体明细
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function getFirstPartyInformationByCode(params) {
  return axios({
    url:
      'contract-center/welfareWednesday/create/getFirstPartyInformationByCode',
    method: 'get',
    params,
  });
}

/**
 * @description: 获取下拉列表
 * @param {*} params
 * @return {*}
 */
export function getSelector(params) {
  return axios({
    url: '/finance-document/dic/values',
    method: 'get',
    params,
  });
}

/**
 * @description: 获取请款列表
 * @param {*} data
 * @return {*}
 */
export function getRequisitionList(data) {
  return axios({
    url: '/finance-bill/requisition/manageQuery',
    method: 'post',
    data,
  });
}

/**
 * @description: 确认已支付
 * @param {*} data
 * @return {*}
 */
export function requisitionPaid(data) {
  return axios({
    url: '/finance-bill/requisition/paid',
    method: 'post',
    data,
  });
}

/**
 * @description: 获取请款详情
 * @param {*} data
 * @return {*}
 */
export function getRequisitionDetail(data) {
  return axios({
    url: '/finance-bill/requisition/detailQuery',
    method: 'post',
    data,
  });
}

/**
 * @description: 上传凭证
 * @param {*} data
 * @return {*}
 */
export function uploadVoucher(data) {
  return axios({
    url: '/finance-document/creditDocAdmin/uploadVoucher',
    method: 'post',
    data,
  });
}

/**
 * @description: 确认收款
 * @param {*} data
 * @return {*}
 */
export function billReceived(data) {
  return axios({
    url: '/finance-bill/bill/received',
    method: 'post',
    data,
  });
}

/**
 * @description: bd审核
 * @param {*} data
 * @return {*}
 */
export function billBdApproved(data) {
  return axios({
    url: '/finance-bill/bill/bdApproved',
    method: 'post',
    data,
  });
}

/**
 * @description: bd驳回
 * @param {*} data
 * @return {*}
 */
export function billBdRejected(data) {
  return axios({
    url: '/finance-bill/bill/bdRejected',
    method: 'post',
    data,
  });
}

/**
 * @description: bd审核
 * @param {*} data
 * @return {*}
 */
export function billFinanceApproved(data) {
  return axios({
    url: '/finance-bill/bill/financeApproved',
    method: 'post',
    data,
  });
}

/**
 * @description: 财务驳回
 * @param {*} data
 * @return {*}
 */
export function billFinanceRejected(data) {
  return axios({
    url: '/finance-bill/bill/financeRejected',
    method: 'post',
    data,
  });
}

/**
 * @description: 账单状态列表
 * @param {*} data
 * @return {*}
 */
export function getDicBill(params) {
  return axios({
    url: '/finance-bill/dic/bill',
    method: 'get',
    params,
  });
}
/**
 * @description: 账单关闭
 * @param {*} data
 * @return {*}
 */
export function closeBill(data) {
  return axios({
    url: '/finance-bill/bill/closeBill',
    method: 'post',
    data,
  });
}
/**
 * @description: 回退到待审核
 * @param {*} data
 * @return {*}
 */
export function rollbackPendingAudit(data) {
  return axios({
    url: '/finance-bill/bill/rollbackPendingAudit',
    method: 'post',
    data,
  });
}

/**
 * @description: 结算单重建
 * @param {*} data
 * @return {*}
 */
export function billRebuild(data) {
  return axios({
    url: '/finance-bill/bill/rebuild',
    method: 'post',
    data,
  });
}
export function onExportUrl(params) {
  return axios({
    url: '/finance-bill/bill/downloadUrl',
    method: 'get',
    params,
  });
}
/**
 * @description: 发票通过
 * @param {*} data
 * @return {*}
 */
export function invoiceApprove(data) {
  return axios({
    url: '/finance-bill/bill/invoiceApprove',
    method: 'post',
    data,
  });
}
/**
 * @description: 发票驳回
 * @param {*} data
 * @return {*}
 */
export function invoiceReject(data) {
  return axios({
    url: '/finance-bill/bill/invoiceReject',
    method: 'post',
    data,
  });
}

/**
 * @description: 统计账单金额
 * @param {*} data
 * @return {*}
 */
export function billSummary(data) {
  return axios({
    url: '/finance-bill/bill/summary',
    method: 'post',
    data,
  });
}

/**
 * @description: 统计账单金额
 * @param {*} data
 * @return {*}
 */
export function invoiceUpload(data) {
  return axios({
    url: '/finance-bill/bill/invoiceUpload',
    method: 'post',
    data,
  });
}
