import { service } from '@/utils/request';

export const get_getCountries = data =>
  service.get('/mdm/international/tax/rule/getCountries', data);

export const get_getEntityByCountryId = data =>
  service.get('/mdm/international/tax/rule/getEntityByCountryId', data);

export const get_getInternationalTaxRulePage = data =>
  service.get('/mdm/international/tax/rule/getPage', data);

export const post_addInternationalTaxRule = data =>
  service.post('/mdm/international/tax/rule/add', null, data);

export const post_updateInternationalTaxRule = data =>
  service.post('/mdm/international/tax/rule/update', null, data);
