import axios from '@/utils/request';

/** 商品侧数据汇总分页列表 */
export function getGoodsList(data) {
  return axios({
    url: '/pay-financial-report/goods/list',
    method: 'get',
    params: data,
  });
}

/** 商品侧数据获取明细 */
export function getGoodsDetail(data) {
  return axios({
    url: '/pay-financial-report/goods/detail',
    method: 'get',
    params: data,
  });
}

/** 账户侧数据分页列表 */
export function getAccountsummaryList(data) {
  return axios({
    url: '/pay-financial-report/accountsummary/list',
    method: 'get',
    params: data,
  });
}
/** 账户侧数据明细列表 */
export function accountsummaryDetail(data) {
  return axios({
    url: '/pay-financial-report/accountsummary/detailList',
    method: 'get',
    params: data,
  });
}
/** 账户侧数据明细列表 */
export function goodesDetail(data) {
  return axios({
    url: '/pay-financial-report/accountsummary/detail',
    method: 'get',
    params: data,
  });
}

/** 获取查询表头数据 */
export function accountsummaryCondition(data) {
  return axios({
    url: '/pay-financial-report/accountsummary/condition',
    method: 'get',
    params: data,
  });
}

/** 获取下拉数据 */
export function conditionList(data) {
  return axios({
    url: '/pay-financial-report/accountsummary/conditionList',
    method: 'get',
    params: data,
  });
}
