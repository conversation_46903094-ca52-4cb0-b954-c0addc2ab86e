/*
 * @Description:
 * @Author: 吴青松
 * @Date: 20022-08-08 15:30:49
 * @LastEditTime: 2022-08-18 13:49:00
 * @LastEditors: Please set LastEditors
 * @Reference:
 */
import req from '@/utils/newRequest';

//退款申请
export function refundApplyList(params) {
  return req.http.request({
    url: 'account-core/refund/apply',
    method: 'get',
    params: params,
  });
}

//退款审核列表
export function refundAuditList(params) {
  return req.http.request({
    url: 'account-core/refund/audit',
    method: 'get',
    params: params,
  });
}

//提现审核列表
export function withdrawalAuditApplicantList(params) {
  return req.http.request({
    url: 'account-core/withdrawal',
    method: 'get',
    params: params,
  });
}

//查询余额
export function getAmount(params) {
  return req.http.request({
    url: 'account-core/refund/apply/showUserBalance',
    method: 'get',
    params: params,
  });
}

//查询退款申请详情
export function getRefundDetail(params) {
  return req.http.request({
    url: 'account-core/refund/apply',
    method: 'get',
    params: params,
  });
}

//退款审核通过
export function refundAuditPass(params) {
  return req.http.request({
    url: '/brace-business/applyAudit/pass',
    method: 'post',
    data: params,
  });
}

///withdrawal/audit
export function auditWithdrawl(params) {
  return req.http.request({
    url: 'account-core/withdrawal/audit',
    method: 'post',
    data: params,
  });
}

///提现驳回
export function rejectWithdrawl(params) {
  return req.http.request({
    url: 'account-core/withdrawal/reject',
    method: 'get',
    params: params,
  });
}

///获取用户信息
export function showUserBalance(params) {
  return req.http.request({
    url: 'account-core/refund/apply/showUserBalance',
    method: 'get',
    params: params,
  });
}

///更新退款申请
export function updateApplyStatus(params) {
  return req.http.request({
    url: 'account-core/refund/audit/updateApplyStatus',
    method: 'post',
    data: params,
  });
}

///创建退款申请
export function createApply(params) {
  return req.http.request({
    url: 'account-core/refund/apply/create',
    method: 'post',
    data: params,
  });
}

///提交申请
export function submitInfo(params) {
  return req.http.request({
    url: 'account-core/refund/apply/submit',
    method: 'post',
    data: params,
  });
}

///提交申请
export function editApply(params) {
  return req.http.request({
    url: 'account-core/refund/apply/editDetail',
    method: 'get',
    params: params,
  });
}

///撤销申请
export function undoApply(params) {
  return req.http.request({
    url: 'account-core/refund/apply/undo',
    method: 'post',
    data: params,
  });
}

///编辑申请申请
export function editItemApply(params) {
  return req.http.request({
    url: 'account-core/refund/apply/edit',
    method: 'post',
    data: params,
  });
}
//文件导出
export function exportFile(params) {
  return req.http.request({
    url: 'account-core/withdrawal/export',
    method: 'post',
    data: params,
  });
}
//获取流水类型枚举
export function getDepoTradeTypeList(params) {
  return req.http.request({
    url: 'account-core/depo/manger/getSelectorKeys',
    method: 'get',
    data: params,
  });
}
//获取货款账户列表
export function getAccountFlows(params) {
  return req.http.request({
    url: 'account-core/depo/manger/accountFlows',
    method: 'post',
    data: params,
  });
}
//导出货款账户列表
export function exportAccountFlows(params) {
  return req.http.request({
    url: 'account-core/depo/manger/export',
    method: 'get',
    data: params,
  });
}

//获取提现流水类型枚举
export function getWithdrawalflowTypeList(params) {
  return req.http.request({
    url: 'account-core/withdrawalflow/typelist',
    method: 'get',
    data: params,
  });
}
//获取提现列表
export function getWithdrawalflows(params) {
  return req.http.request({
    url: 'account-core/withdrawalflow',
    method: 'get',
    params: params,
  });
}
//导出提现流水列表
export function exportWithdrawalflows(params) {
  return req.http.request({
    url: 'account-core/withdrawalflow/export',
    method: 'get',
    params: params,
  });
}

//获取账户状态类型枚举
export function getAccountStatusList(params) {
  return req.http.request({
    url: 'account-core/depo/manager/getAccountStatus',
    method: 'get',
    data: params,
  });
}
//获取账户列表
export function getAccounts(params) {
  return req.http.request({
    url: 'account-core/depo/manager/accounts',
    method: 'get',
    params: params,
  });
}
// 手动处理货款可体现

export function cashTransferDepo(data) {
  return req.http.request({
    url: 'account-core/depo/manager/bal/cashTransferDepo',
    method: 'post',
    data,
  });
}
