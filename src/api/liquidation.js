import { service } from '@/utils/request';

/**
 * 查询筛选账户类型字段
 *
 * @return {Promise<[]>}
 * @see https://yapi.idanchuang.net/project/391/interface/api/13204
 *
 */
export const getCapitalDictionaries = () =>
  service.get('/mdm/capitalDictionaries/queryAllByParam');

/**
 * 分页查询主体列表
 *
 * @param {*} body
 * @param {string} body.endTime
 * @param {string} body.startTime
 * @param {string} body.name
 * @param {number} body.limit
 * @param {number} body.pageNo
 *
 * @see https://yapi.idanchuang.net/project/395/interface/api/13396
 */
export const getSubjectList = body =>
  service.post('/mdm/capitalSubject/queryAllCapitalSubjectParam', null, body);

/**
 * 根据名称查询主体信息
 *
 * @param {*} name 主体名称
 * @see https://yapi.idanchuang.net/project/395/interface/api/13399
 */
export const getSubjectByName = name =>
  service.post('/mdm/capitalSubject/querySubjectByName', { name });

/**
 * 新增主体
 *
 * @param {*} body
 * @param {string} body.adress 地址
 * @param {string} body.codePrefix 编码前缀
 * @param {string} body.keyword 关键字
 * @param {string} body.name 主体名称
 * @param {string} body.nsCustCode NS编码
 * @param {string} body.nsSupplierCode NS供应商编码
 * @param {string} body.parentCode 父体code
 * @see https://yapi.idanchuang.net/project/395/interface/api/13353
 */
export const saveSubject = body =>
  service.post('/mdm/capitalSubject/saveCapitalSubject', null, body);

/**
 * 编辑主体
 *
 * @param {*} body
 * @param {*} body.id 主键
 * @param {string} body.adress 地址
 * @param {string} body.codePrefix 编码前缀
 * @param {string} body.keyword 关键字
 * @param {string} body.name 主体名称
 * @param {string} body.nsCustCode NS编码
 * @param {string} body.nsSupplierCode NS供应商编码
 * @param {string} body.parentCode 父体code
 * @see https://yapi.idanchuang.net/project/395/interface/api/13354
 */
export const updateSubject = body =>
  service.post('/mdm/capitalSubject/updateCapitalSubject', null, body);

/**
 * 查询店铺列表
 *
 * @param {*} body
 * @param {number} body.limit
 * @param {number} body.pageNo
 * @param {string} body.name
 * @param {string} body.typeName
 * @see https://yapi.idanchuang.net/project/395/interface/api/13346
 */
export const getShopList = body =>
  service.post('/mdm/capitalStore/queryAllCapitalStoreByParam', null, body);

/**
 * 新增店铺
 *
 * @param {*} body
 * @param {string} body.keyword 关键词
 * @param {string} body.name
 * @param {*} body.parentCode
 * @param {*} body.parentCodeType = 1
 *
 * @see https://yapi.idanchuang.net/project/395/interface/api/13348
 */
export const saveShop = body =>
  service.post('/mdm/capitalStore/saveCapitalStore', null, body);

/**
 * 编辑店铺
 *
 * @param {*} body
 * @param {string} body.keyword 关键词
 * @param {string} body.name
 * @param {*} body.parentCode
 * @param {*} body.parentCodeType = 1
 * @param {*} body.id
 *
 * @see https://yapi.idanchuang.net/project/395/interface/api/13348
 */
export const updateShop = body =>
  service.post('/mdm/capitalStore/updateCapitalStore', null, body);

/**
 * 获取参数字典表
 *
 * @param {*} type 1机构类型 2 账户类型 4客户类型
 * @return {Promise}
 * @see https://yapi.idanchuang.net/project/395/interface/api/13344
 */
export const getParamDictionaries = type =>
  service.get('/mdm/capitalDictionaries/queryAllCapitalDictionariesByParam', {
    type,
  });

/**
 * 获取客户列表
 *
 * @param {*} body
 * @param {*} body.limit
 * @param {*} body.name
 * @param {*} body.pageNo
 * @param {*} body.typeName
 *
 * @see https://yapi.idanchuang.net/project/395/interface/api/13341
 */
export const getCustomerList = body =>
  service.post(
    '/mdm/capitalCustomer/queryAllCapitalCustomerByParam',
    null,
    body,
  );

/**
 * 查询账户列表
 *
 * @param {*} body
 * @param {string} body.accountName
 * @param {number} body.accountTypeCode
 * @param {string} body.bankAccount
 * @param {string} body.bankName
 * @param {number} body.limit
 * @param {string} body.orgCode
 * @param {string} body.orgType
 * @param {number} body.pageNo
 * @param {string} body.subbranchName
 * @param {number} body.type
 * @see https://yapi.idanchuang.net/project/395/interface/api/13336
 */
export const getAccountList = body =>
  service.post('/mdm/capitalAccount/queryAllCapitalAccountByParam', null, body);

/**
 * 根据ID查询账户详情
 *
 * @param {*} id 账户id
 * @see https://yapi.idanchuang.net/project/395/interface/api/13338
 */
export const getAccountDetail = id =>
  service.post('/mdm/capitalAccount/queryUpdateById', { id });

/**
 * 保存账户信息
 *
 * @param {*} body
 * @param {string} body.account
 * @param {string} body.accountType
 * @param {string} body.bankAbbreviation 银行简称
 * @param {string} body.bankAdress
 * @param {string} body.bankCode 银行编码
 * @param {number} body.bankCodeType
 * @param {string} body.bankName
 * @param {number} body.bankType 银行类型 1 境外银行 2 境内银行
 * @param {string} body.bizCode 父类型编码 可能是 主体/客户/店铺
 * @param {string} body.branchBankName 支行名称 bankType=2 不需要必填
 * @param {string} body.capitalPurpose 资金用途
 * @param {string} body.companyAdress
 * @param {string} body.currency
 * @param {string} body.orgName
 * @param {string} body.parentCode
 * @param {number} body.id
 * @param {number} body.parentType 父级的类型 1 主体 2 门店 3 客户
 * @param {number} body.serviceCharge 手续费 1 内扣 2 外扣
 *
 * @return {Promise}
 * @see https://yapi.idanchuang.net/project/395/interface/api/13339
 */
export const saveAccount = body =>
  service.post('/mdm/capitalAccount/saveCapitalAccount', null, body);

/**
 * 更新账户信息
 *
 * @param {*} body
 * @param {string} body.account
 * @param {string} body.accountType
 * @param {string} body.bankAbbreviation 银行简称
 * @param {string} body.bankAdress
 * @param {string} body.bankCode 银行编码
 * @param {number} body.bankCodeType
 * @param {string} body.bankName
 * @param {number} body.bankType 银行类型 1 境外银行 2 境内银行
 * @param {string} body.bizCode 父类型编码 可能是 主体/客户/店铺
 * @param {string} body.branchBankName 支行名称 bankType=2 不需要必填
 * @param {string} body.capitalPurpose 资金用途
 * @param {string} body.companyAdress
 * @param {string} body.currency
 * @param {string} body.orgName
 * @param {string} body.parentCode
 * @param {number} body.id
 * @param {number} body.parentType 父级的类型 1 主体 2 门店 3 客户
 * @param {number} body.serviceCharge 手续费 1 内扣 2 外扣
 *
 * @return {Promise}
 * @see https://yapi.idanchuang.net/project/395/interface/api/13340
 */
export const updateAccount = body =>
  service.post('/mdm/capitalAccount/updateCapitalAccount', null, body);

/**
 * 查询 主体/客户/店铺 下拉列表框
 *
 * @param {*} code SUBJECT/CUST/SHOP
 * @param {*} name
 */
export const searchSectionAttributes = (code, name) =>
  service.post('/mdm/capitalAccount/querySectionAttributes', null, {
    code,
    name,
  });

/**
 * 新增客户
 *
 * @param {*} body
 * @param {string} body.name
 * @param {string} body.keyword
 * @param {string} body.nsCustCode
 * @param {string} body.nsSupplierCode
 * @param {string} body.typeName
 * @return {Promise}
 * @see https://yapi.idanchuang.net/project/395/interface/api/13342
 */
export const saveCustomer = body =>
  service.post('/mdm/capitalCustomer/saveCapitalCustomer', null, body);

/**
 * 更新客户信息
 *
 * @param {*} body
 * @param {number} body.id
 * @param {string} body.name
 * @param {string} body.keyword
 * @param {string} body.nsCustCode
 * @param {string} body.nsSupplierCode
 * @param {string} body.typeName
 */
export const updateCustomer = body =>
  service.post('/mdm/capitalCustomer/updateCapitalCustomer', null, body);

/**
 * 获取员工列表
 *
 * @param {*} body
 * @param {*} body.department
 * @param {*} body.limit
 * @param {*} body.name
 * @param {*} body.pageNo
 * @return {Promise}
 * @see https://yapi.idanchuang.net/project/395/interface/api/13345
 */
export const getStaffList = body =>
  service.post('/mdm/capitalStaff/queryAllCapitalStaffByParam', null, body);

/**
 * 获取清算事项规则
 *
 * @param {*} params
 * @param {*} params.size
 * @param {*} params.current
 * @see http://recon-dev.idanchuang.vpc/doc.html#/default/%E6%B8%85%E7%AE%97%E8%A7%84%E5%88%99/queryLiquidationRuleForPageUsingPOST
 */
export const getLiquidationList = body =>
  service.post(
    '/recon/liquidation/rule/queryLiquidationRuleForPage',
    null,
    body,
  );

/**
 * 删除清算事项规则
 *
 * @param {*} id
 * @see http://recon-dev.idanchuang.vpc/doc.html#/default/%E6%B8%85%E7%AE%97%E8%A7%84%E5%88%99/deleteLiquidationRuleUsingDELETE
 */
export const deleteLiquidation = id =>
  service.delete('/recon/liquidation/rule/deleteLiquidationRule', { id });

/**
 * 获取流水导入日志
 *
 * @param {*} params
 * @param {string} params.accountCode
 * @param {string} params.accountNoOrSuffix
 * @param {*} params.lastDate
 * @param {*} params
 *
 *
 * @see http://recon-dev.idanchuang.vpc/doc.html#/default/%E6%B5%81%E6%B0%B4%E5%AF%BC%E5%85%A5%E7%AE%A1%E7%90%86/searchUsingGET_1
 */
export const getTurnoverLogList = params =>
  service.get('/recon/bill/batch/search', params);

/**
 * 获取流水列表
 *
 * @param {*} params
 * @param {*} params.transMainName
 * @param {*} params.limit
 * @param {*} params.offset
 * @param {*} params.startTransDate
 * @param {*} params.endTransDate
 * @param {*} params.status
 */
export const getTurnoverList = params =>
  service.get('/recon/bill/checked/search', params);

/**
 * 重新匹配流水列表
 *
 * @param {*} id
 * @see http://recon-dev.idanchuang.vpc/swagger-ui.html#/%E6%B5%81%E6%B0%B4%E7%AE%A1%E7%90%86/matchRuleUsingPOST
 */
export const recheckedTurnover = id =>
  service.post('/recon/bill/checked/match/:id', { id });

/**
 * 批量匹配
 *
 * @param {*} id
 * @see http://recon-dev.idanchuang.vpc/swagger-ui.html#/%E6%B5%81%E6%B0%B4%E7%AE%A1%E7%90%86/matchRuleUsingPOST
 */
export const batchMatchTurnover = batchNo =>
  service.post('/recon/bill/checked/match/batch/:batchNo', { batchNo });

/**
 * 手动匹配
 *
 * @param {*} id
 * @param {*} liquidationCode
 * @see http://recon-dev.idanchuang.vpc/doc.html#/default/%E6%B5%81%E6%B0%B4%E7%AE%A1%E7%90%86/addLiquidationUsingPOST
 */
export const directMatchTurnover = (id, liquidationCode) =>
  service.post(
    '/recon/bill/checked/match/direct/:id',
    { id },
    { id, liquidationCode },
  );
/**
 * 导出流水列表
 *
 * @param {*} params
 * @param {*} params.transMainName
 * @param {*} params.limit
 * @param {*} params.offset
 * @param {*} params.startTransDate
 * @param {*} params.endTransDate
 * @param {*} params.status
 *
 * @return {Promise}
 * @see http://recon-dev.idanchuang.vpc/swagger-ui.html#/%E6%B5%81%E6%B0%B4%E7%AE%A1%E7%90%86/exportUsingGET
 */
export const exportTurnover = params =>
  service.get('/recon/bill/checked/export', params);

/**
 * 回滚
 *
 * @param {*} batchNo
 * @see http://recon-dev.idanchuang.vpc/doc.html#/default/%E6%B5%81%E6%B0%B4%E5%AF%BC%E5%85%A5%E7%AE%A1%E7%90%86/batchDeleteUsingDELETE
 */
export const deleteTurnoverLog = batchNo =>
  service.delete('/recon/bill/batch/:batchNo', { batchNo });

/**
 * 流水批次查询
 *
 * @param {*} body
 * @param {*} body.areaType 0 私域，1 公域
 * @param {*} body.channelMerNo 渠道商户号
 * @param {*} body.current 当前页 默认值1
 * @param {*} body.size
 * @param {*} body.startTime 开始时间,传入时间戳
 * @param {*} body.endTime
 * @param {*} body.sourceType 渠道，0:初始状态 1:银联 2:微信 3:支付宝 4:宝付 5通联
 * @return {Promise}
 * @see http://recon-dev.idanchuang.vpc/swagger-ui.html#/%E6%B8%A0%E9%81%93%E6%B5%81%E6%B0%B4/queryFlowUsingPOST
 */
export const getChannelTurnoverList = body =>
  service.post('/recon/channel/flow', null, body);

/**
 * 获取流水明细
 *
 * @param {*} id
 * @param {*} current
 * @param {*} size
 * @return {Promise}
 *
 * @see http://recon-dev.idanchuang.vpc/swagger-ui.html#/%E6%B8%A0%E9%81%93%E6%B5%81%E6%B0%B4/queryInfoUsingGET
 */
export const getChannelTurnoverDetail = (id, current, size) =>
  service.get('/recon/channel/flow/info', {
    pushDataResultId: id,
    current,
    size,
  });

/**
 * 流水明细下载
 *
 *
 * @param {*} pushDataResultId 批次id
 * @return {Promise<String>} file url
 * @see http://recon-dev.idanchuang.vpc/swagger-ui.html#/%E6%B8%A0%E9%81%93%E6%B5%81%E6%B0%B4/getExportUrlUsingGET
 */
export const getChannelExportUrl = pushDataResultId =>
  service.get('/recon/channel/flow/getExportUrl', { pushDataResultId });

/**
 * 获取资金凭证列表
 *
 * @param {*} body
 * @param {*} body.current
 * @param {*} body.size
 * @param {*} body.accountName
 * @param {*} body.accountNo
 * @param {*} body.billDateEnd 账务日期，传入时间戳
 * @param {*} body.billDateStart 账务日期，传入时间戳
 * @return {Promise}
 * @see http://recon-dev.idanchuang.vpc/swagger-ui.html#/%E8%B5%84%E9%87%91%E5%87%AD%E8%AF%81/queryUsingPOST
 */
export const getCapitalproofList = body =>
  service.post('/recon/credentials/query', null, body);

/**
 * 查询流水明细
 *
 * @param {*} params
 * @param {*} params.batchNo
 * @param {*} params.limit
 * @param {*} params.offset
 * @param {*} params.status
 *
 * @see http://recon-dev.idanchuang.vpc/doc.html#/default/%E6%B5%81%E6%B0%B4%E5%AF%BC%E5%85%A5%E7%AE%A1%E7%90%86/searchUsingGET_1
 */
export const getTurnoverlogDetail = params =>
  service.get('/recon/bill/search', params);

export default {
  getCapitalDictionaries,
  getSubjectList,
  getSubjectByName,
  saveSubject,
  updateSubject,
  getShopList,
  saveShop,
  updateShop,
  getParamDictionaries,
  getAccountList,
  getAccountDetail,
  saveAccount,
  searchSectionAttributes,
  getCustomerList,
  saveCustomer,
  updateCustomer,
  getStaffList,
  getLiquidationList,
  getTurnoverLogList,
  getChannelTurnoverList,
  getChannelTurnoverDetail,
  getCapitalproofList,
  getTurnoverList,
  getTurnoverlogDetail,
  updateAccount,
  deleteLiquidation,
  deleteTurnoverLog,
  recheckedTurnover,
  exportTurnover,
  getChannelExportUrl,
  batchMatchTurnover,
  directMatchTurnover,
};
