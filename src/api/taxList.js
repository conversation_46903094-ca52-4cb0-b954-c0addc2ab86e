/*
 * @Author: dddd
 * @Date: 2023-05-16 13:59:43
 * @LastEditors: dddd
 * @LastEditTime: 2023-05-16 18:34:46
 * @FilePath: /fmis/src/api/costManagement.js
 * @Description:
 */
import axios from '@/utils/request';
import req from '@/utils/newRequest';

export function updateTaxRowData(data) {
  return axios({
    url: '/finance-bill/tax/management/update/tax/row',
    method: 'post',
    data,
  });
}

export function deleteTaxRowData(data) {
  return axios({
    url: '/finance-bill/tax/management/remove',
    method: 'post',
    data,
  });
}

export function getListData(data) {
  return axios({
    url: '/finance-bill/tax/management/query',
    method: 'post',
    data,
  });
}

export function listOptions() {
  return axios({
    url: '/finance-bill/tax/management/listQueryOptions',
    method: 'post',
  });
}

export function taxUpload(data) {
  return axios({
    url: '/finance-bill/tax/management/upload/excel',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data,
  });
}

export function exportTax(data, fileName) {
  return req.http.request({
    url: '/finance-bill/tax/management/export',
    method: 'post',
    data,
    headers: {
      fileType: '.xlsx',
      fileName,
    },
    responseType: 'blob',
  });
}
