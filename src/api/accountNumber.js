/*
 * @Author: dddd
 * @Date: 2023-05-16 13:59:43
 * @LastEditors: dddd
 * @LastEditTime: 2023-05-16 18:34:46
 * @FilePath: /fmis/src/api/costManagement.js
 * @Description:
 */
import axios from '@/utils/request';

export function getListData(data) {
  return axios({
    url: '/magpie-bridge/accounting/queryAccountInfo',
    method: 'post',
    data,
  });
}

/** 科目配置 -列表 */
export function listFeeByName(feeName) {
  return axios({
    url: `/magpie-bridge/accounting/queryFeeType?feeName=${feeName}`,
    method: 'post',
  });
}

/**
 * 费用树级结构获取
 * @param id
 */
export function saveAccountNumber(data) {
  return axios({
    url: `/magpie-bridge/accounting/save`,
    method: 'post',
    data,
  });
}

/**
 * 费用项业务域下拉框
 * @param id
 */
export function removeId(id) {
  return axios({
    url: `/magpie-bridge/accounting/remove/${id}`,
    method: 'post',
  });
}
