import axios from '@/utils/request';
// 根据支付流水号获取W单
export function getPayment(query) {
  return axios({
    url: '/apple-prometheus/payment/paymentInfo',
    method: 'get',
    params: query,
  });
}
//根据W单获取支付流水号
export function getPaymentOrder(query) {
  return axios({
    url: '/apple-prometheus/payment/paymentPid',
    method: 'get',
    params: query,
  });
}
//根据微信订单号获取支付流水号
export function getPaymentWOrder(query) {
  return axios({
    url: '/apple-prometheus/payment/paymentWid',
    method: 'get',
    params: query,
  });
}
//出境金额可视化
export function getOverseaOrder(query) {
  return axios({
    url: '/apple-prometheus/split/overseaOrder',
    method: 'get',
    params: query,
  });
}
//出境日报表
export function getOverseaOrderSum(query) {
  return axios({
    url: '/apple-prometheus/split/overseaOrderSum',
    method: 'get',
    params: query,
  });
}
//出境日报表--外币
export function getOverseaOrderSumForeign(query) {
  return axios({
    url: '/apple-prometheus/split/overseaOrderSumForeign',
    method: 'get',
    params: query,
  });
}
