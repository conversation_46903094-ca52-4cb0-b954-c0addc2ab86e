import axios from '@/utils/request';

//异常单查询
export function queryOrderListOfPage(data) {
  return axios({
    url: '/recon-core/financeAdjust/queryOrderListOfPage',
    method: 'post',
    data,
  });
}
//异常单修改
export function modifyOrderByAdjustCostCode(data) {
  return axios({
    url: '/recon-core/financeAdjust/modifyOrderByAdjustCostCode',
    method: 'post',
    data,
  });
}
//存货成本查询
export function queryCostListOfPage(data) {
  return axios({
    url: '/recon-core/financeAdjust/queryCostListOfPage',
    method: 'post',
    data,
  });
}
// 异常单导入
export function importOrderData(params) {
  return axios({
    url: '/recon-core/financeAdjust/importOrderData',
    method: 'get',
    params,
  });
}
// 存货调整导入
export function importCostData(params) {
  return axios({
    url: '/recon-core/financeAdjust/importCostData',
    method: 'get',
    params,
  });
}
// 币种查询
export function getCurrencyList() {
  return axios({
    url: '/recon-core/financeAdjust/currency/list',
    method: 'get',
  });
}

//枚举接口
export async function queryFinanceAdjustEnums(data) {
  return axios({
    url: '/recon-core/financeAdjust/queryFinanceAdjustEnums',
    method: 'post',
    data,
  });
}

// 主体查询
export async function get_getAllEntities() {
  return axios({
    url: '/business-center-admin/bcEntity/listForSelect',
    method: 'get',
  });
}
