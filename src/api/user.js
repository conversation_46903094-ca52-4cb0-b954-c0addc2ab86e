import axios from '@/utils/request';
import { tokenName } from '@/config/setting.config';
import injectHost from '@/utils/injectHost';
export async function login(data) {
  return axios({
    url: '/login',
    method: 'post',
    data,
  });
}

export function getUserInfo(accessToken) {
  //此处为了兼容mock.js使用data传递accessToken，如果使用mock可以走headers
  return axios({
    url: '/userInfo',
    method: 'post',
    data: {
      [tokenName]: accessToken,
    },
  });
}

export function logout() {
  return axios({
    url: '/logout',
    method: 'post',
  });
}

export function register() {
  return axios({
    url: '/register',
    method: 'post',
  });
}

export function checkToken(params) {
  return axios({
    url: `${
      window.location.protocol + '//' + injectHost().gatewayHost
    }/sso-auth/auth/checkToken`,
    method: 'get',
    params,
  });
}

export function getRouterList(params) {
  return axios({
    url: '/sso-auth/permission/getMenuList',
    method: 'get',
    params,
  });
}
