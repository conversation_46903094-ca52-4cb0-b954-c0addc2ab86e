/*
 * @Author: bruce
 * @Date: 2023-08-25 16:35:49
 * @LastEditors: bruce
 * @FilePath: /access-fmis-web/src/api/ticketTaxPlatform/invoiceCenter.js
 * @Description:发票中心接口
 */
import req from '@/utils/newRequest';

// 获取红字发票
export function getCreditNote(params) {
  return req.http.request({
    url: 'bill/invoice/red/list',
    method: 'post',
    data: params,
  });
}

// 红字发票-处理红单
export function handleOrder(params) {
  return req.http.request({
    url: 'bill/invoice/red/handleOrder',
    method: 'post',
    data: params,
  });
}

// 红字发票-下载
export function importOrderData(params) {
  return req.http.request({
    url: 'bill/invoice/red/exportOrderData',
    method: 'post',
    data: params,
    responseType: 'blob',
  });
}
