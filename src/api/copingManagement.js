/*
 * @Description:
 * @Author: xuxiang
 * @Date: 2022-01-07 14:18:41
 * @LastEditTime: 2022-09-13 17:44:29
 * @LastEditors: xuxiang
 * @Reference:
 */
import axios from '@/utils/request';

/**
 * @description: 应付结算单查询
 * @param {*} params
 * @return {*}
 */
export function getSettleOrderList(params) {
  return axios({
    url: 'finance-payable/settle-order/page',
    method: 'get',
    params,
  });
}

/**
 * @description: 应付结算单汇总查询
 * @param {*} params
 * @return {*}
 */
export function getSettleOrderSummary(params) {
  return axios({
    url: 'finance-payable/settle-order/page-summary',
    method: 'post',
    data: params,
  });
}

/**
 * @description:供应商搜索下拉框数据
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function getListSelector(params) {
  return axios({
    url: 'contract-center/welfareWednesday/contract/records/selector',
    method: 'post',
    data: params,
  });
}

/**
 * @description:乙方供应商的信息
 * @param  {*}
 * @return {*}
 * @param {*} supplierId 供应商id
 */
export function getSecondPartInformationById(params) {
  return axios({
    url: 'contract-center/welfareWednesday/create/getSecondPartInformationById',
    method: 'get',
    params,
  });
}

/**
 * @description: 应付结算明细单查询
 * @param {*} params
 * @return {*}
 */
export function settleOrderPageItem(params) {
  return axios({
    url: 'finance-payable/settle-order/page-item',
    method: 'get',
    params,
  });
}

/**
 * @description: 结算方式查询
 * @param {*} params
 * @return {*}
 */
export function querySettleType(params) {
  return axios({
    url: 'finance-payable/settle-order/querySettleType',
    method: 'post',
    data: params,
  });
}

/**
 * @description: 下拉查询
 * @param {*} params
 * @return {*}
 */
export function queryTypes(params) {
  return axios({
    url: 'finance-payable/settle-order/queryTypes',
    method: 'post',
    data: params,
  });
}

/**
 * @description: 经销结算方式查询
 * @param {*} params
 * @return {*}
 */
export function queryDisSettleType(params) {
  return axios({
    url: 'finance-payable/settle-order/queryDisSettleType',
    method: 'post',
    data: params,
  });
}

/**
 * @description: 核销
 * @param {*} params
 * @return {*}
 */
export function settleOrderItemWriteOff(params) {
  return axios({
    url: 'finance-payable/settle-order-item/writeOff',
    method: 'post',
    data: params,
  });
}

/**
 * @description: 应付经销结算单查询
 * @param {*} params
 * @return {*}
 */
export function queryPageDis(params) {
  return axios({
    url: 'finance-payable/settle-order/page-dis',
    method: 'post',
    data: params,
  });
}

/**
 * @description: 应付寄售结算单查询
 * @param {*} params
 * @return {*}
 */
export function queryPageSub(params) {
  return axios({
    url: 'finance-payable/settle-order/page-sub',
    method: 'post',
    data: params,
  });
}

/**
 * @description: 应付结算明细单查询
 * @param {*} params
 * @return {*}
 */
export function querySettleOrderItem(params) {
  return axios({
    url: 'finance-payable/settle-order-item/list',
    method: 'post',
    data: params,
  });
}

/**
 * @description: 删除导入结算单
 * @param {*} params
 * @return {*}
 */
export function importOrderDelete(params) {
  return axios({
    url: 'finance-payable/import-order/delete',
    method: 'post',
    data: params,
  });
}

/**
 * @description: 上传计费单文件
 * @param {*} params
 * @return {*}
 */
export function importOrderUpload(params) {
  return axios({
    url: 'finance-payable/import-order/upload',
    method: 'post',
    data: params,
  });
}

/**
 * @description: 导入费用调整
 * @param {*} params
 * @return {*}
 */
export function importAdjustmentOrderFeesUpload(params) {
  return axios({
    url: 'finance-payable/adjustment-order/import-adjust-fees',
    method: 'post',
    data: params,
  });
}

/**
 * @description: 导入贷款调整
 * @param {*} params
 * @return {*}
 */
export function importAdjustmentOrderGoodsUpload(params) {
  return axios({
    url: 'finance-payable/adjustment-order/import-adjust-goods',
    method: 'post',
    data: params,
  });
}

/**
 * @description: 货款调整
 * @param {*} params
 * @return {*}
 */
export function adjustGoods(params) {
  return axios({
    url: 'finance-payable/adjustment-order/adjust-goods',
    method: 'post',
    data: params,
  });
}

/**
 * @description: 费用调整
 * @param {*} params
 * @return {*}
 */
export function adjustFees(params) {
  return axios({
    url: 'finance-payable/adjustment-order/adjust-fees',
    method: 'post',
    data: params,
  });
}

/**
 * @description: 获取操作记录
 * @param {*} params
 * @return {*}
 */
export function pageAdjustLog(params) {
  return axios({
    url: 'finance-payable/adjustment-order/pageAdjustLog',
    method: 'post',
    data: params,
  });
}

/**
 * @description: 上传计费单文件
 * @param {*} params
 * @return {*}
 */
export function listExcelModel(params) {
  return axios({
    url: 'finance-payable/common/listExcelModel',
    method: 'get',
    params,
  });
}

/**
 * @description: 计费单导入
 * @param {*} params
 * @return {*}
 */
export function importOrderImportFile(params) {
  return axios({
    url: 'finance-payable/import-order/importFile',
    method: 'post',
    data: params,
  });
}

/**
 * @description: 计费单补单导入
 * @param {*} params
 * @return {*}
 */
export function importOrderSupplementImportFile(params) {
  return axios({
    url: 'finance-payable/import-order/supplementImportFile',
    method: 'post',
    data: params,
  });
}

/**
 * @description: 计费单确认
 * @param {*} params
 * @return {*}
 */
export function importOrderConfirmImportFile(params) {
  return axios({
    url: 'finance-payable/import-order/confirmImportFile',
    method: 'post',
    data: params,
  });
}

/**
 * @description: 计费单补确认
 * @param {*} params
 * @return {*}
 */
export function importOrderConfirmSupplementImportFile(params) {
  return axios({
    url: 'finance-payable/import-order/confirmSupplementImportFile',
    method: 'post',
    data: params,
  });
}
