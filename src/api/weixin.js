/*
 * @Author: dddd
 * @Date: 2023-03-01 14:27:24
 * @LastEditors: dddd
 * @LastEditTime: 2023-03-01 14:52:44
 * @FilePath: /fmis/src/api/weixin.js
 * @Description:
 */
import { service } from '@/utils/request';

/**------------------------------------------  会员信息管理 -----------------------------------**/

/**
 * 查询供应商列表
 */

export const getListAccountDetail = data =>
  service.post('/split-service/directAccount/listAccountDetail', null, data);

/**
 * 查询账户信息
 */

export const queryPageAccountBalance = data =>
  service.post('/split-service/directAccount/pageAccountBalance', null, data);
