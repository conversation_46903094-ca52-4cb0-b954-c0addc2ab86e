/*
 * @Author: 七七
 * @Date: 2022-03-18 17:38:44
 * @LastEditors: 七七
 * @LastEditTime: 2022-04-18 15:57:33
 * @FilePath: /access-fmis-web/src/api/compensationDocumentManagement.js
 */
// 赔付单据管理

import axios from '@/utils/request';

/** 退供单下拉列表 */
export function returnFormOption(data) {
  return axios({
    url: 'finance-document/refundDoc/dropdownList',
    method: 'post',
    data,
  });
}
/** 退供单列表数据 */
export function getReturnFormList(data) {
  return axios({
    url: 'finance-document/refundDoc/page',
    method: 'post',
    data,
  });
}

// 赔付单列表
export function getCompensationSheetList(query) {
  return axios({
    url: 'finance-document/compensation/list',
    method: 'get',
    params: query,
  });
}

//责任方下拉列表
export function getResponsiblePartyList(token) {
  return axios({
    headers: { adminToken: token },
    url: 'after-sales-admin/common/responsibilities',
    method: 'get',
  });
}
