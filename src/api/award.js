import axios from '@/utils/request';

/** 类目信息查询 */
export function getCategoryList(data) {
  return axios({
    url: 'covercharge-service/punish/category/getInfo',
    method: 'post',
    data,
  });
}
/** 获取所有一级类目 */
export function getFirstCategoryList(data) {
  return axios({
    url: 'covercharge-service/punish/category/getCategoryInfoList',
    method: 'post',
    data,
  });
}
/** 类目新增  */
export function addCategory(data) {
  return axios({
    url: 'covercharge-service/punish/category/insertCategory',
    method: 'post',
    data,
  });
}
/** 类目值新增  */
export function addCategoryValue(data) {
  return axios({
    url: 'covercharge-service/punish/category/insertCategoryInfo',
    method: 'post',
    data,
  });
}
/** 类目修改  */
export function editCategory(data) {
  return axios({
    url: 'covercharge-service/punish/category/updateInfo',
    method: 'post',
    data,
  });
}

/** 奖惩审核相关 */

/** 单个/批量审核  */
export function auditPass(data) {
  return axios({
    url: 'covercharge-service/punish/deal/batchCheckApply',
    method: 'post',
    data,
  });
}
/** 单个驳回  */
export function auditReject(data) {
  return axios({
    url: 'covercharge-service/punish/deal/rejectApply',
    method: 'post',
    data,
  });
}
// 批量驳回
export function batchAuditReject(data) {
  return axios({
    url: 'covercharge-service/punish/deal/batchRejectApply',
    method: 'post',
    data,
  });
}
/** 单个撤销  */
export function auditCancel(data) {
  return axios({
    url: 'covercharge-service/punish/deal/revokeApply',
    method: 'post',
    data,
  });
}
export function batchAuditCancel(data) {
  return axios({
    url: 'covercharge-service/punish/deal/batchRevokeApply',
    method: 'post',
    data,
  });
}
/** 导出审核列表  */
export function exportAudit(data) {
  return axios({
    url: 'covercharge-service/punish/deal/revokeApply',
    method: 'post',
    data,
  });
}

/** 审核详情  */
export function getAuditDetail(data) {
  return axios({
    url: 'covercharge-service/punish/deal/getDealInfo',
    method: 'post',
    data,
  });
}
// 导出审核单
export function exportAuditList(params) {
  return axios({
    url: 'covercharge-service/punish/deal/exportInfo',
    method: 'post',
    params,
  });
}
/** 违约金相关 */

/** 申请页查询  */
export function getApplyList(data) {
  return axios({
    url: 'covercharge-service/punish/apply/getApplyInfo',
    method: 'post',
    data,
  });
}
// 新增申请单
export function addApply(data) {
  return axios({
    url: 'covercharge-service/punish/apply/saveApplyInfo',
    method: 'post',
    data,
  });
}
export function editApply(data) {
  return axios({
    url: 'covercharge-service/punish/apply/updateApply',
    method: 'post',
    data,
  });
}
// 批量提交 or 删除申请单 status: 1-提交 6-删除
export function submitOrDeleteApply(data) {
  return axios({
    url: 'covercharge-service/punish/apply/delApplyList',
    method: 'post',
    data,
  });
}
// 批量提交
export function submitApply(data) {
  return axios({
    url: 'covercharge-service/punish/apply/submitApply',
    method: 'post',
    data,
  });
}
/** 删除单个申请单 */
export function deleteApplyById(data) {
  return axios({
    url: 'covercharge-service/punish/apply/delApplyById',
    method: 'post',
    data,
  });
}
// 导出申请单
export function exportApplyList(data) {
  return axios({
    url: 'covercharge-service/punish/apply/exportApply',
    method: 'post',
    data,
  });
}
// 导入申请单
export function importApplyList(data) {
  return axios({
    url: 'covercharge-service/punish/apply/inputApply',
    method: 'post',
    data,
  });
}
// 审核日志列表
export function getAuditLogList(data) {
  return axios({
    url: 'covercharge-service/task/list',
    method: 'get',
    params: data,
  });
}
