/*
 * @Author: bruce
 * @Date: 2024-06-12 10:08:16
 * @FilePath: /access-fmis-web/src/api/southEast/index.js
 * @Description:
 */
import req from '@/utils/newRequest';

// 获取虚拟币列表
export function getVirtualCoinList(data) {
  return req.http.request({
    url: '/financial-account/virtualCoin/account/getList',
    method: 'post',
    data,
  });
}

// 获取虚拟币账户流水
export function getVirtualCoinFlows(data) {
  return req.http.request({
    url: '/financial-account/virtualCoin/account/getFlows',
    method: 'post',
    data,
  });
}

// 导出虚拟币流水
export function exportFlows(data) {
  return req.http.request({
    url: '/financial-account/virtualCoin/account/exportFlows',
    method: 'post',
    data,
  });
}

// 获取销售主体
export function getComboBoxData(params) {
  return req.http.request({
    url: '/financial-account/virtualCoin/account/getComboBoxData',
    method: 'get',
    params,
  });
}
