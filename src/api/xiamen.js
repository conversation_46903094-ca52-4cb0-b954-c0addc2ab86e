import { service } from '@/utils/request';

/**
 * 查询账户资金变动审核列表
 *
 */
export const accCapitalChangeReviewList = data =>
  service.post('/brace-business/accCapitalChangeReview/queryList', null, data);

/**
 * 账户资金变动审核
 *
 */
export const accCapitalChangeReviewPass = data =>
  service.get('/brace-business/accCapitalChangeReview/pass', data);

/**
 * 账户资金变动批量审核通过
 *
 */
export const accCapitalChangeRevieBwatchPass = data =>
  service.post('/brace-business/accCapitalChangeReview/batchPass', null, data);

/**
 * 查询大客戶订单列表
 *
 */
export const specialOrderList = data =>
  service.get('/brace-business/special/order/List', data);

/**
 * 大客戶订单支付
 *
 */
export const specialOrderPay = data =>
  service.get('/brace-business/special/order/pay', data);

/**------------------------------------------  会员信息管理 -----------------------------------**/

/**
 * 查询供应商列表
 */

export const supplierQueryList = data =>
  service.post('/brace-business/supplier/queryList', null, data);

/**
 * 查询账户信息
 */

export const accountOperateInfo = data =>
  service.post('/brace-business/accountOperate/queryInfo', null, data);

/**
 * 提交提现待审核记录
 */

export const submitWithdraw = data =>
  service.post(
    '/brace-business/accCapitalChangeReview/submitWithdraw',
    null,
    data,
  );

/**
 * 查询供应商列表为下拉选择
 */

export const supplierQueryListForSelect = data =>
  service.post('/brace-business/supplier/queryListForSelect', null, data);

/**
 * 保存供应商信息
 */

export const supplierSave = data =>
  service.post('/brace-business/supplier/save', null, data);

/**
 * 查询供应商绑卡列表
 *
 */
export const queryBankCardList = data =>
  service.get('/brace-business/supplier/queryBankCardList', data);

/**
 * 银行列表
 *
 */
export const supportBankList = data =>
  service.get('/brace-business/supplier/supportBankList', data);

/**
 * 供应商绑卡
 */

export const supplierBankCardSave = data =>
  service.post('/brace-business/supplier/bankCard', null, data);

/**
 * 供应商更换结算卡
 */

export const supplierReplaceBankCard = data =>
  service.post('/brace-business/supplier/replaceBankCard', null, data);

/**
 * 平台账户余额列表
 */

export const queryXibAccountInfo = data =>
  service.get('/brace-business/accountOperate/queryXibAccountInfo', data);

/**
 * 转账保存
 */

export const submitTransfer = data =>
  service.post(
    '/brace-business/accCapitalChangeReview/submitTransfer',
    null,
    data,
  );

/**
 * 账户明细列表
 */

export const transList = data =>
  service.post('/recon/xib/acc/trans/list', null, data);

/**
 * 账户明细导出
 */

export const transExport = data =>
  service.post('/recon/xib/acc/trans/export', null, data);

/**
 * 供应商信息签约复核
 */

export const supplierReview = data =>
  service.post('/brace-business/supplier/review', null, data);

/**
 * 供应商绑卡申请
 */

export const supplierBankCardApply = data =>
  service.post('/brace-business/supplier/bankCard/apply', null, data);

/**
 * 供应商信息签约提交
 */

export const supplierSubmit = data =>
  service.get('/brace-business/supplier/submit', data);

/**
 * 供应商信息签约申请
 */

export const supplierApply = data =>
  service.post('/brace-business/supplier/apply', null, data);

/**
 * 供应商绑卡复核
 */

export const supplierBankCardReview = data =>
  service.get('/brace-business/supplier/bankCard/review', data);

/**
 * 供应商绑卡提交
 */

export const supplierBankCardSubmit = data =>
  service.get('/brace-business/supplier/bankCard/submit', data);

/**
 * 账户资金变动复核
 */

export const accCapitalChangeReviewReview = data =>
  service.post('/brace-business/accCapitalChangeReview/review', null, data);

/**
 * 账户资金变动终审
 */

export const accCapitalChangeReviewReview2 = data =>
  service.post('/brace-business/accCapitalChangeReview/review2', null, data);

/**
 * 查询供应商绑卡列表
 */

export const querySupplierCards = data =>
  service.get('/brace-business/supplier/querySupplierCards', data);

/**
 * 操作日志查询
 */

export const bankCardLogSearch = data =>
  service.post('/brace-business/log/search', null, data);

/**
 * 平台/会员子账户日切余额查询
 */

export const listDailyBalanceDetail = data =>
  service.get('/account-core/fund/account/listDailyBalanceDetail', data);

/**
 * 厦门分户交易信息列表
 */

export const accTransList = data =>
  service.post('/account-core/fund/account/acc/trans/list', null, data);

/**
 * 平台/会员子账户平台流水明细
 */

export const listPlatformFundFlow = data =>
  service.get('/account-core/platform/listPlatformFundFlow', data);

/**
 * 获取下拉框列表
 */

export const getSelectors = data =>
  service.get('/account-core/component/selector/getSelectors', data);

/**
 * 获取下拉框列表
 */

export const getAccountSelectors = data =>
  service.get('/account-core/fund/account/selectors', data);
