import { service } from '@/utils/request';

// /**
//  * 查询账户资金变动审核列表
//  *
//  */
// export const accCapitalChangeReviewList = data =>
//   service.post('/brace-business/accCapitalChangeReview/queryList', null, data);

/**
 * 分页查询税务信息
 *
 */
export const getTaxRulePage = data =>
  service.get('/mdm/tax/rule/pageQuery', data);

/**
 * 税率查询
 *
 */
export const getCountryRate = data =>
  service.get('/mdm/tax/country/rate', data);

/**
 * 获取国家集合
 */
export const getTaxCountryQuery = data =>
  service.get('/mdm/tax/country/query', data);

/**
 * 税目信息查询
 */
export const getTaxCategoryQuery = data =>
  service.get('/mdm/tax/category/query', data);

/**
 * 添加税目信息
 */
export const categoryAdd = data =>
  service.post('/mdm/tax/category/add', null, data);

/**
 * 添加税则
 */
export const taxRuleAdd = data => service.post('/mdm/tax/rule/add', null, data);

/**
 * 修改税则
 */
export const taxRuleUpdate = data =>
  service.post('/mdm/tax/rule/update', null, data);
