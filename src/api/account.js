import req from '@/utils/request';

//退货款申请列表
export function returnPaymentApplicantList(params) {
  return req.http.request({
    url: 'account-core/refund/apply',
    method: 'get',
    params: params,
  });
}

//提现审核列表
export function withdrawalAuditApplicantList(params) {
  return req.http.request({
    url: 'account-core/withdrawal',
    method: 'get',
    params: params,
  });
}

//退款审核列表
export function refundAuditList(params) {
  return req.http.request({
    url: 'account-core/refund/audit',
    method: 'get',
    params: params,
  });
}

//退款申请
export function refundApplyList(params) {
  return req.http.request({
    url: 'account-core/refund/apply',
    method: 'get',
    params: params,
  });
}

//查询余额
export function getAmount(params) {
  return req.http.request({
    url: 'account-core/refund/apply/showUserBalance',
    method: 'get',
    params: params,
  });
}

//查询退款申请详情
export function getRefundDetail(params) {
  return req.http.request({
    url: 'account-core/refund/apply',
    method: 'get',
    params: params,
  });
}

///withdrawal/audit
export function auditWithdrawl(params) {
  return req.http.request({
    url: 'account-core/withdrawal/audit',
    method: 'post',
    data: params,
  });
}

///提现驳回
export function rejectWithdrawl(params) {
  return req.http.request({
    url: 'account-core/withdrawal/reject',
    method: 'get',
    params: params,
  });
}

///获取用户信息
export function showUserBalance(params) {
  return req.http.request({
    url: 'account-core/refund/apply/showUserBalance',
    method: 'get',
    params: params,
  });
}

///更新退款申请
export function updateApplyStatus(params) {
  return req.http.request({
    url: 'account-core/refund/audit/updateApplyStatus',
    method: 'post',
    data: params,
  });
}

///退款审核
export function abroadRefund(params) {
  return req.http.request({
    url: 'account-core/refund/audit/abroadRefund/audit',
    method: 'post',
    data: params,
  });
}

///创建退款申请
export function createApply(params) {
  return req.http.request({
    url: 'account-core/refund/apply/create',
    method: 'post',
    data: params,
  });
}

///导入excel状态
export function importStatus(params) {
  return req.http.request({
    url: 'account-core/withdrawal/importPaymentResult',
    method: 'get',
    params: params,
  });
}

///提交申请
export function submitInfo(params) {
  return req.http.request({
    url: 'account-core/refund/apply/submit',
    method: 'post',
    data: params,
  });
}

///提交申请
export function editApply(params) {
  return req.http.request({
    url: 'account-core/refund/apply/editDetail',
    method: 'get',
    params: params,
  });
}

///撤销申请
export function undoApply(params) {
  return req.http.request({
    url: 'account-core/refund/apply/undo',
    method: 'post',
    data: params,
  });
}

///编辑申请申请
export function editItemApply(params) {
  return req.http.request({
    url: 'account-core/refund/apply/edit',
    method: 'post',
    data: params,
  });
}
///withdrawal/exportPaymentFile  导出打款模板接口

export function exportPaymentFile(params) {
  return req.http.request({
    url: 'account-core/withdrawal/exportPaymentFile ',
    method: 'post',
    data: params,
  });
}

//国家下拉框
export function getSelectors(params) {
  return req.http.request({
    url: 'account-core/component/selector/getSelectors',
    method: 'get',
    params: params,
  });
}
