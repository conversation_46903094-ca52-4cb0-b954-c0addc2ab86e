/*
 * @Author: dddd
 * @Date: 2023-05-16 13:59:43
 * @LastEditors: dddd
 * @LastEditTime: 2023-05-16 18:34:46
 * @FilePath: /fmis/src/api/costManagement.js
 * @Description:
 */
import axios from '@/utils/request';

/** 费用项管理查询列表-列表 */
export function getListData(data) {
  return axios({
    url: '/magpie-bridge/expense/list',
    method: 'post',
    data,
  });
}

/**
 * 费用树级结构获取
 * @param id
 */
export function getTree(data) {
  return axios({
    url: `/magpie-bridge/expense/getTree`,
    method: 'get',
    params: data,
  });
}

/**
 * 费用项业务域下拉框
 * @param id
 */
export function getBusinessList(data) {
  return axios({
    url: `/magpie-bridge/expense/business/list`,
    method: 'get',
    params: data,
  });
}

/**
 * 费用项新增
 * @param id
 */
export function saveAdd(data) {
  return axios({
    url: `/magpie-bridge/expense/add`,
    method: 'post',
    data,
  });
}
/**
 * 费用项状态更新
 * @param id
 */
//    url: `/magpie-bridge/expense/updateStatus?id=${data.id}&status=${data.status}`,
export function updateStatus(data) {
  return axios({
    url: `/magpie-bridge/expense/updateStatus`,
    method: 'post',
    data,
  });
}
/**
 * 费用项状态编辑
 * @param id
 */
export function saveUpdate(data) {
  return axios({
    url: `/magpie-bridge/expense/update`,
    method: 'post',
    data,
  });
}

/**
 * 费用项状态编辑
 * @param id
 */
export function getCostDocumentData(data) {
  return axios({
    url: `/finance-bill/expense/page`,
    method: 'post',
    data,
  });
}
/**
 * 流程节点成功时间
 * @param businessOrderNo
 */
export function getLinkTime(data) {
  return axios({
    url: `/apple-prometheus/moneyLink/query`,
    method: 'post',
    data,
  });
}
