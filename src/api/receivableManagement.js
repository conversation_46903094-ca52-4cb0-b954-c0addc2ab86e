import axios from '@/utils/request';

/** 对账日汇总列表 */
export function getDayPageList(data) {
  return axios({
    url: 'receivable/reconStatistics/dayPage',
    method: 'post',
    data,
  });
}

/** 对账月汇总列表 */
export function getMonthPageList(data) {
  return axios({
    url: 'receivable/reconStatistics/monthPage',
    method: 'post',
    data,
  });
}

/** 渠道类型 */
export function payChannelList(data) {
  return axios({
    url: 'receivable/reconStatistics/payChannelList',
    method: 'post',
    data,
  });
}

/** 对账信息列表 */
export function reconFlatPage(data) {
  return axios({
    url: 'receivable/reconFlat/page',
    method: 'post',
    data,
  });
}

/** 差异信息列表 */
export function reconDifferencePage(data) {
  return axios({
    url: 'receivable/reconDifference/page',
    method: 'post',
    data,
  });
}

/** 短款信息列表 */
export function reconCashShortPage(data) {
  return axios({
    url: 'receivable/reconCashShort/page',
    method: 'post',
    data,
  });
}

/** 长款信息列表 */
export function reconCashOverPage(data) {
  return axios({
    url: 'receivable/reconCashOver/page',
    method: 'post',
    data,
  });
}

/** 福三对账明细查询 */
export function reconFusanPage(data) {
  return axios({
    url: 'receivable/reconFusan/page',
    method: 'post',
    data,
  });
}

/**----------------------------- 应收结算管理 ----------------------------*/

/** 查询结算单列表 */
export function settleItemList(data) {
  return axios({
    url: 'finance-receivable/settleItem/getList',
    method: 'post',
    data,
  });
}

/** 获取结算单汇总数据 */
export function settleItemSumData(data) {
  return axios({
    url: 'finance-receivable/settleItem/getSumData',
    method: 'post',
    data,
  });
}

/** 查询操作日志列表 */
export function settleItemOperateLog(data) {
  return axios({
    url: 'finance-receivable/operateLog/getList',
    method: 'post',
    data,
  });
}

/** 查询计费单列表 */
export function chargeItemList(data) {
  return axios({
    url: 'finance-receivable/chargeItem/getList',
    method: 'post',
    data,
  });
}

/** 计费单列表详情 */
export function chargeItemGetDetailList(data) {
  return axios({
    url: 'finance-receivable/chargeItemDetail/getValueAddDetailList',
    method: 'post',
    data,
  });
}

/** 导入增值服务费 */
export function chargeItemImportDetailData(data) {
  return axios({
    url: 'finance-receivable/chargeItemDetail/importDetailData',
    method: 'post',
    data,
  });
}

/** 下载导入模版 */
export function chargeItemDownloadDetailExcelTemplate(params) {
  return axios({
    url: 'finance-receivable/chargeItemDetail/downloadDetailExcelTemplate',
    method: 'get',
    params,
  });
}

/** 计费单明细列表 */
export function chargeItemDetailList(data) {
  return axios({
    url: 'finance-receivable/chargeItemDetail/getList',
    method: 'post',
    data,
  });
}

/** 计费单明细列表 */
export function settleItemGetSelector(params) {
  return axios({
    url: 'finance-receivable/settleItem/getSelector',
    method: 'get',
    params,
  });
}

/** 预收收入日/月汇总分页条件查询 */
export function advanceIncomeSummaryList(data) {
  return axios({
    url: 'finance-document/advance-income-summary/list',
    method: 'post',
    data,
  });
}

/** 预收收入自洽分页条件查询 */
export function advanceIncomeMonthCompareList(data) {
  return axios({
    url: 'finance-document/advance-income-month-compare/list',
    method: 'post',
    data,
  });
}

/** 预收收入-已支付日汇总分页查询 */
export function advanceIncomeSummaryPaidPagePaidDaySum(data) {
  return axios({
    url: 'finance-document/advance-income-summary-paid/pagePaidDaySum',
    method: 'post',
    data,
  });
}

/** 预收收入-已签收日汇总分页查询 */
export function advanceIncomeSummarySignpageSignedDaySum(data) {
  return axios({
    url: 'finance-document/advance-income-summary-sign/pageSignedDaySum',
    method: 'post',
    data,
  });
}

/** 预收收入-明细 */
export function advanceIncomeItemList(data) {
  return axios({
    url: 'finance-document/advance-income-item/list',
    method: 'post',
    data,
  });
}

/** 预收收入-下拉数据 */
export function advanceIncomeItemSelector(params) {
  return axios({
    url: 'finance-document/advance-income-item/selector',
    method: 'get',
    params,
  });
}

/** 预收收入-品牌下拉数据 */
export function listBrand(params) {
  return axios({
    url: '/goods-admin/brand/list',
    method: 'get',
    params,
  });
}

/** 计费单据管理-下载模板 */
export function downloadExcelTemplate(data) {
  return axios({
    url: 'finance-receivable/chargeItem/downloadExcelTemplate',
    method: 'post',
    data,
  });
}

/** 计费单据管理-导入 */
export function chargeItemUpload(data) {
  return axios({
    url: 'finance-receivable/chargeItem/upload',
    method: 'post',
    data,
  });
}

/** 计费单据管理-下载模板 */
export function importOrderdownloadExcelTemplate(params) {
  return axios({
    url: 'finance-receivable/import-order/downloadExcelTemplate',
    method: 'get',
    params,
  });
}

/**
 * @description: 计费单补单导入
 * @param {*} params
 * @return {*}
 */
export function financePayableSupplementImportFile(params) {
  return axios({
    url: 'finance-receivable/import-order/supplementImportFile',
    method: 'post',
    data: params,
  });
}

/**
 * @description: 计费单补确认
 * @param {*} params
 * @return {*}
 */
export function financePayableConfirmSupplementImportFile(params) {
  return axios({
    url: 'finance-receivable/import-order/confirmSupplementImportFile',
    method: 'post',
    data: params,
  });
}

/**
 * @description: 计费单确认
 * @param {*} params
 * @return {*}
 */
export function financePayableConfirmImportFile(params) {
  return axios({
    url: 'finance-receivable/import-order/confirmImportFile',
    method: 'post',
    data: params,
  });
}
/**
 * @description: 计费单导入
 * @param {*} params
 * @return {*}
 */
export function financePayableImportFile(params) {
  return axios({
    url: 'finance-receivable/import-order/importFile',
    method: 'post',
    data: params,
  });
}

/**
 * @description: sku维度调整单导入
 * @param {*} params
 * @return {*}
 */
export function financePayableImportSKUAdjustOrder(params) {
  return axios({
    url: 'finance-receivable/import-order/importSKUAdjustOrder',
    method: 'post',
    data: params,
  });
}

/**
 * @description: 调整单导入
 * @param {*} params
 * @return {*}
 */
export function financePayableImportAdjustOrder(params) {
  return axios({
    url: 'finance-receivable/import-order/importAdjustOrder',
    method: 'post',
    data: params,
  });
}

/**
 * @description: 查询导入日志
 * @param {*} params
 * @return {*}
 */
export function financeReceivablePagelist(params) {
  return axios({
    url: 'finance-receivable/import-order/pagelist',
    method: 'post',
    data: params,
  });
}
