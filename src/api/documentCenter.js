import axios from '@/utils/request';

/** 收款单&付款单-收款方名称&付款方名称下拉搜索接口 */
export function getFuzzySearchList(data) {
  return axios({
    url: 'finance-document/creditAndPaymentDocComposite/fuzzySearchList',
    method: 'post',
    data,
  });
}

/**
 * @description:品牌搜索下拉框数据
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function getQueryBrandListBySupplierId(params) {
  return axios({
    url:
      'contract-center/welfareWednesday/contract/temporaryRebate/queryBrandListBySupplierId',
    method: 'get',
    params,
  });
}

/**
 * @description:供应商搜索下拉框数据
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function getListSelector(params) {
  return axios({
    url: 'contract-center/welfareWednesday/contract/records/selector',
    method: 'post',
    data: params,
  });
}

// /**
//  * @description:供应商搜索下拉框数据
//  * @param  {*}
//  * @return {*}
//  * @param {*} data
//  */
// export function getListFirstPartyInfo(params) {
//   return axios({
//     url: 'contract-center/welfareWednesday/create/listFirstPartyInfo',
//     method: 'get',
//     params,
//   });
// }

// /**
//  * @description:采购方搜索下拉框数据
//  * @param  {*}
//  * @return {*}
//  * @param {*} data
//  */
// export function getListSupplierBasicInfo(params) {
//   return axios({
//     url: 'contract-center/welfareWednesday/create/listSupplierBasicInfo',
//     method: 'get',
//     params,
//   });
// }

/** 数据字典 */
export function getDicValues(params) {
  return axios({
    url: 'finance-document/dic/values',
    method: 'get',
    params,
  });
}

/**
 * @description:计费单项分页查询
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function chargeDocItemsList(params) {
  return axios({
    url: 'finance-document/chargeDoc/items/page',
    method: 'post',
    data: params,
  });
}

/**
 * @description:计费单分页查询
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function chargeDocList(params) {
  return axios({
    url: 'finance-document/chargeDoc/page',
    method: 'post',
    data: params,
  });
}

/**
 * @description:收款单&付款单列表查询
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function creditAndPaymentDocCompositeList(params) {
  return axios({
    url: 'finance-document/creditAndPaymentDocComposite/list',
    method: 'post',
    data: params,
  });
}

/**
 * @description:收款单&付款单列表导出
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function creditAndPaymentDocCompositeExport(params) {
  return axios({
    url: 'finance-document/creditAndPaymentDocComposite/export',
    method: 'post',
    data: params,
  });
}

/**
 * @description:查看收款和付款单凭证
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function creditAndPaymentDocCompositeShowVoucher(params) {
  return axios({
    url: 'finance-document/creditAndPaymentDocComposite/showVoucher',
    method: 'post',
    data: params,
  });
}

/**
 * @description:付款确认接口
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function paymentConfirm(params) {
  return axios({
    url: 'finance-document/paymentDocAdmin/paymentConfirm',
    method: 'post',
    data: params,
  });
}

/**
 * @description:po单列表（采购订单）
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function purchaseDocList(params) {
  return axios({
    url: 'finance-document/purchaseDoc/page',
    method: 'post',
    data: params,
  });
}

/**
 * @description:po单项列表（采购订单明细）
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function purchaseDocItems(params) {
  return axios({
    url: 'finance-document/purchaseDoc/items',
    method: 'post',
    data: params,
  });
}

/**
 * @description:po单项采购详情
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function purchaseDocDetails(params) {
  return axios({
    url: `finance-document/purchaseDoc/${params.id}`,
    method: 'post',
    data: params,
  });
}

/**
 * @description:po单结算详情（查看结算数量明细）
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function purchaseDocSettlementDetails(params) {
  return axios({
    url: `finance-document/purchaseDoc/${params.id}/settlement`,
    method: 'post',
    data: params,
  });
}

/**
 * @description:po单项采购详情（查看发货/收货数量明细）
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function purchaseDocSettlementShipping(params) {
  return axios({
    url: `finance-document/purchaseDoc/${params.id}/shipping`,
    method: 'post',
    data: params,
  });
}

/**
 * @description:统计异常PO单
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function getPurchaseDocSum(params) {
  return axios({
    url: 'finance-document/purchaseDoc/sum',
    method: 'post',
    data: params,
  });
}

/**
 * @description:银行流水列表
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function bankImportRecordList(params) {
  return axios({
    url: 'finance-document/bankImportRecord/list',
    method: 'post',
    data: params,
  });
}

/**
 * @description:银行流水导入
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function bankImportWaterImport(params) {
  return axios({
    url: 'finance-document/bankImportRecord/waterImport',
    method: 'post',
    data: params,
  });
}

/* ------------------------销售单接口 --------------------------*/

/**
 * @description:toC SO单列表
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function salesDocList(params) {
  return axios({
    url: 'finance-document/salesDoc/customer/page',
    method: 'post',
    data: params,
  });
}

/**
 * @description:toC SO单履约详情
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function salesDocFulfillmentDocs(params) {
  return axios({
    url: `finance-document/salesDoc/customer/fulfillmentDocs`,
    method: 'post',
    data: params,
  });
}

/**
 * @description:toC SO单详情
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function salesDocDetails({ id }) {
  return axios({
    url: `finance-document/salesDoc/customer/${id}`,
    method: 'post',
  });
}

/**
 * @description:toB SO单列表
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function salesDocBusinessList(params) {
  return axios({
    url: 'finance-document/salesDoc/business/page',
    method: 'post',
    data: params,
  });
}

/**
 * @description:toB SO单详情
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function salesDocBusinessDetails({ id }) {
  return axios({
    url: `finance-document/salesDoc/business/${id}`,
    method: 'post',
  });
}

/**
 * @description:统计异常TO B SO单
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function businessSum(params) {
  return axios({
    url: 'finance-document/salesDoc/business/sum',
    method: 'post',
    data: params,
  });
}

/**
 * @description:统计异常TO C SO单
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function customerSum(params) {
  return axios({
    url: 'finance-document/salesDoc/customer/sum',
    method: 'post',
    data: params,
  });
}

/**
 * @description:出库单分页查询
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function getOutboundOrderList(params) {
  return axios({
    url: 'finance-document/outboundOrder/list',
    method: 'get',
    params,
  });
}

/**
 * @description:入库单分页查询：
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function getInboundOrderList(params) {
  return axios({
    url: 'finance-document/inboundOrder/list',
    method: 'get',
    params,
  });
}

/**
 * @description:库存调整单分页查询
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function getAdjustOrderList(params) {
  return axios({
    url: 'finance-document/adjustOrder/list',
    method: 'get',
    params,
  });
}

/**
 * @description:调拨出入库单分页查询：
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function getTransferOutInOrderList(params) {
  return axios({
    url: 'finance-document/transferOutInOrder/list',
    method: 'get',
    params,
  });
}
/**
 * @description:根据调拨单号查询调拨单（包含明细信息）：
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function getTransferOrderQuery(params) {
  return axios({
    url: 'finance-document/transferOrder/query',
    method: 'get',
    params,
  });
}

/**
 * @description:入库单类型查询接口
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function getDeliveryOrderType(params) {
  return axios({
    url:
      'abmau-service-aggregate-erp/erp/businessOrderType/query/deliveryOrderType',
    method: 'post',
    data: params,
  });
}

/**
 * @description:支付单分页查询
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function getPayList(params) {
  return axios({
    url: 'finance-document/pay/list',
    method: 'get',
    params,
  });
}

/**
 * @description:支付流水分页查询
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function getPaymentList(params) {
  return axios({
    url: 'finance-document/payment/list',
    method: 'get',
    params,
  });
}

/**
 * @description:退款单分页查询
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function getRefundList(params) {
  return axios({
    url: 'finance-document/refund/list',
    method: 'get',
    params,
  });
}

/**
 * @description:退款流水分页查询
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function getRefundmentList(params) {
  return axios({
    url: 'finance-document/refundment/list',
    method: 'get',
    params,
  });
}

/**
 * @description:下拉列表
 * @param  {*}
 * @return {*}
 * @param {*} data
 */

export function docDataCheckDropdownList(params) {
  return axios({
    url: 'finance-document/docDataCheck/dropdownList',
    method: 'post',
    data: params,
  });
}

/**
 * @description:对账结果报表
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function docDataCheckPage(params) {
  return axios({
    url: 'finance-document/docDataCheck/page',
    method: 'post',
    data: params,
  });
}

/**
 * @description:跨境综合税实缴单
 * @param  {*}
 * @return {*}
 * @param {*} data
 */
export function comprehensiveTaxList(params) {
  return axios({
    url: 'finance-document/comprehensiveTax/list',
    method: 'get',
    params,
  });
}
