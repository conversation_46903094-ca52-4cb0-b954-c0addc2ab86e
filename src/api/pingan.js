import { service } from '@/utils/request';

export const getPlatformSubAccountList = () =>
  service.post('/split-service/subAccount/getPlatformSubAccountList', null, {
    channel: 'pingan',
  });

/*
 * 发起提现请求
 *
 * @params {*} body
 * @params {*} body.accountId
 * @params {*} body.amount
 * @params {*} body.remark
 * @params {*} body.serviceChargePercent
 * @see https://yapi.idanchuang.net/project/509/interface/api/14866
 */
export const doWithdraw = body =>
  service.post('/split-service/withdrawOrder/doWithdraw', null, body);

/**
 * 获取会员子账户列表
 *
 * @params {*} body
 * @params {*} body.accountType 账户类型
 * @params {*} body.cardNo 银行账号
 * @params {*} body.channel 银行渠道类型，pingan,xib
 * @params {*} body.custId 客户编号
 * @params {*} body.current
 * @params {*} body.size
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14846
 */
export const getMemberSubAccountList = body =>
  service.post('/split-service/subAccount/getMemberSubAccountList', null, body);

/**
 * 会员子账户下拉框
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14848
 */
export const getMemberSubAccountOptions = () =>
  service.get('/split-service/subAccount/selector', {
    channel: 'pingan',
  });

export const getWithdrawRecordOptions = () =>
  service.get('/split-service/withdrawOrder/selector', {
    channel: 'pingan',
  });

/**
 * 提现记录列表
 *
 * @params {*} body
 * @params {*} body.accountNo
 * @params {*} body.startTime
 * @params {*} body.endTime
 * @params {*} body.current
 * @params {*} body.size
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14867
 */
export const getWithdrawRecord = body =>
  service.post('/split-service/withdrawOrder/page', null, {
    ...body,
    channel: 'pingan',
  });

/**
 * 获取贸易主体配置项
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14862
 */
export const getTradeSubjectOptions = () =>
  service.get('/split-service/tradeSubject/selector', {
    channel: 'pingan',
  });

/**
 * 贸易主体配置列表
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14861
 */
export const getTradeSubjectConfigList = body =>
  service.post('/split-service/tradeSubject/getConfigList', null, body);

export const getTradeSubjectConfigDetail = (accountId, depoCode) =>
  service.post('split-service/tradeSubject/getConfigDetail', null, {
    accountId,
    depoCode,
  });

/**
 * 更新贸易主体配置
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14863
 */
export const updateTradeSubjectConfig = body =>
  service.post('/split-service/tradeSubject/updateConfig', null, body);

/**
 * 删除记录
 *
 * @see ...问哈啰
 */
export const deleteTradeSubjectConfig = id =>
  service.get('/split-service/tradeSubject/delConfig', { id });

/**
 * 贸易主体交易记录查询列表
 *
 * @params {*} body
 * @params {*} body.accountNo
 * @params {*} body.depoCode
 * @params {*} body.startTime
 * @params {*} body.endTime
 * @params {*} body.endTime
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14857
 */
export const getTradeSubjectRecords = body =>
  service.post('/split-service/subjectOrder/page', null, body);

/**
 * 获取贸易主体交易记录配置
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14859
 */
export const getTradeSubjectRecordOptions = () =>
  service.get('/split-service/subjectOrder/selector');

/**
 * 贸易主体重新分账
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14858
 */
export const restartTradeSubjectRecords = body =>
  service.post('/split-service/subjectOrder/restart', null, body);

/**
 * 会员间转账 下拉框
 * @see https://yapi.idanchuang.net/project/509/interface/api/14854
 */
export const getSubAccountTransferOptions = () =>
  service.get('/split-service/subAccountTransfer/selector');

/**
 * 获取会员子账户列表 (转账页面)
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14853
 */
export const getSubAccountTransferList = body =>
  service.post('/split-service/subAccountTransfer/page', null, body);

/**
 * 新增会员子账户转账
 * @see https://yapi.idanchuang.net/project/509/interface/api/14850
 */
export const addMemberSubAccountTransfer = body =>
  service.post(
    '/split-service/subAccountTransfer/addMemberSubAccountTransfer',
    null,
    body,
  );

/**
 * 修改会员子账户转账
 * @see https://yapi.idanchuang.net/project/509/interface/api/14850
 */
export const updateMemberSubAccountTransfer = body =>
  service.post(
    '/split-service/subAccountTransfer/updateMemberSubAccountTransfer',
    null,
    body,
  );

/**
 * 会员子账户转账
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14855
 */
export const createSubAccountTransfer = body =>
  service.post('/split-service/subAccountTransfer/transfer', null, body);

/**
 * 会员间转账交易记录查询
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14864
 */
export const getTransferOrderList = body =>
  service.post('/split-service/transferOrder/page', null, body);

/**
 * 会员间转账交易记录选项
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14865
 */
export const getTransferOrderOptions = () =>
  service.get('/split-service/transferOrder/selector');

/**
 * 子账户开户选项
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/15111
 */
export const getOpenSubAccountOptions = () =>
  service.get('/split-service/open/subAccount/selector');

/**
 * 子账户开户列表
 *
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/15105
 */
export const getOpenSubAccountList = body =>
  service.post('/split-service/open/subAccount/getList', null, body);

/**
 * 账户开户
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/15108
 */
export const openSubAccount = body =>
  service.post('/split-service/open/subAccount/open', null, body);

/**
 * 绑卡
 * @see https://yapi.idanchuang.net/project/509/interface/api/15099
 */
export const subAccountBindCard = body =>
  service.post('split-service/open/subAccount/bindbing/card', null, body);

/**
 * 验证开户
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/15102
 */
export const subAccountBindValid = body =>
  service.post('split-service/open/subAccount/bindbing/valid', null, body);

/**
 * 主体开户下拉框
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/15096
 */
export const getOpenMainPartAccountOptions = () =>
  service.get('/split-service/open/mainPartAccount/selector');

/**
 * 主体开户记录
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/15090
 */
export const getOpenMainPartAccountList = body =>
  service.post('/split-service/open/mainPartAccount/getList', null, body);

/**
 * 主账户开户
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/15093
 */
export const createMainPartAccount = body =>
  service.post('/split-service/open/mainPartAccount/open', null, body);

/**
 * 刷新余额
 *
 * @see https://yapi.idanchuang.net/project/509/interface/api/14852
 */
export const refreshSubAccountBalance = id =>
  service.get('/split-service/subAccountTransfer/getSubAccountBalance', { id });

/**
 * 登记挂帐  - 新建
 *
 */
export const hangOrderCreate = body =>
  service.post('/split-service/hangOrder/create', null, body);

/**
 * 登记挂帐  - 列表
 *
 */
export const hangOrderList = body =>
  service.post('/split-service/hangOrder/list', null, body);

/**
 * 登记挂帐  - 挂帐确认
 *
 */
export const hangOrderConfirm = body =>
  service.post('/split-service/hangOrder/confirm', null, body);

/**
 * 登记挂帐  - 挂帐取消
 *
 */
export const hangOrderCancel = body =>
  service.post('/split-service/hangOrder/cancel', null, body);

/**
 * 登记挂帐  - 挂帐拒绝
 *
 */
export const hangOrderRefuse = body =>
  service.post('/split-service/hangOrder/refuse', null, body);

/**
 * 登记挂帐  - 状态下拉
 *
 */

export const hangOrderSelector = body =>
  service.get('/split-service/hangOrder/selector');

/**
 * 登记挂帐  - 获取必要信息(创建挂帐等场景)
 *
 */

export const getEssentialInfo = body =>
  service.get('/split-service/hangOrder/getEssentialInfo');

/**
 * 网关充值 下拉框
 * @see
 */
export const getGatewayRechargeOptions = () =>
  service.get('/split-service/recharge/init');

/**
 * 网关充值记录列表
 *
 */
export const getGatewayRechargeList = body =>
  service.post('/split-service/recharge/list', null, body);

/**
 * 网关充值
 *
 */
export const doGatewayRecharge = body =>
  service.post('/split-service/recharge/unionPay', null, body);

/**
 * 解绑卡
 *
 * @see
 */
export const unbindingCard = id =>
  service.get('/split-service/open/subAccount/unbindingCashCard', { id });

/**
 * 一级分账日汇总
 *
 */
export const firstSplitStatsDaySum = body =>
  service.post('/split-service/firstSplitStats/daySum', null, body);

/**
 * 一级分账月汇总
 *
 */
export const firstSplitStatsMonthSum = body =>
  service.post('/split-service/firstSplitStats/monthSum', null, body);

/**
 * 一级分账明细
 *
 */
export const firstSplitStatsDaySumDetail = body =>
  service.post('/split-service/firstSplitStats/firstSplitDetail', null, body);

/**
 * 二级费用明细汇
 *
 */
export const secondaryItemStatistics = body =>
  service.post('/split-service/secondary/item-statistics', null, body);

/**
 * 二级费用明细汇
 *
 */
export const secondaryItemQuery = body =>
  service.post('/split-service/secondary/item-query', null, body);

/**
 * 配置查询
 *
 */
export const secondaryConfigQuery = body =>
  service.post('/split-service/secondary/config-query', null, body);

/**
 * 上传配置
 *
 */
export const secondaryUploadConfig = body =>
  service.post('/split-service/secondary/upload—config', null, body);

/**
 * 模版导出
 *
 */
export const secondaryTemplateImport = body =>
  service.post(
    '/split-service/secondary/config-template-url-query',
    null,
    body,
  );

/**
 * 贸易主体
 *
 */
export const secondaryTradeSubjectQuery = body =>
  service.post('/split-service/secondary/tradeSubject-query', null, body);

/**
 * ---------------------------- 提现审核管理 ------------------------------
 *
 */

/**
 * 提现资金申请记录分页
 *@see https://yapi.acg.team/project/1228/interface/api/131063
 */
export const fundWithdrawPage = body =>
  service.post('/split-service/fund/withdraw/page', null, body);

/**
 * 转账资金申请记录分页
 *@see https://yapi.acg.team/project/1228/interface/api/131059
 */
export const fundTransferPage = body =>
  service.post('/split-service/fund/transfer/page', null, body);

/**
 * 会员子账户转账页面下拉框
 *
 * @see https://yapi.acg.team/project/1228/interface/api/131055
 */
export const fundSelector = () => service.get('/split-service/fund/selector');

/**
 * 资金终审
 *@see https://yapi.acg.team/project/1228/interface/api/131045
 */
export const auditFinal = body =>
  service.post('/split-service/fund/audit/final', null, body);

/**
 * 资金复核
 *@see https://yapi.acg.team/project/1228/interface/api/131047
 */
export const auditOper = body =>
  service.post('/split-service/fund/audit/oper', null, body);

/**
 * 二级分账-下拉框
 *
 * @see
 */
export const secondSplitStatsSelector = () =>
  service.get('/split-service/secondarySplitStats/selector');

/**
 * 二级分账-日汇总
 *@see https://yapi.acg.team/project/1228/interface/api/131135
 */
export const secondSplitStatsDaySum = body =>
  service.post('/split-service/secondarySplitStats/daySum', null, body);

/**
 * 二级分账-月汇总
 *@see https://yapi.acg.team/project/1228/interface/api/131137
 */
export const secondSplitStatsMonthSum = body =>
  service.post('/split-service/secondarySplitStats/monthSum', null, body);
/**
 * 二级分账-二级分账明细
 *@see https://yapi.acg.team/project/1228/interface/api/131139
 */
export const secondSplitDetail = body =>
  service.post(
    '/split-service/secondarySplitStats/secondSplitDetail',
    null,
    body,
  );

/**
 * 刷新子账户余额
 *
 * @see https://yapi.acg.team/project/1228/interface/api/131075
 */
export const refreshSubAccountBal = accountNo =>
  service.get('/split-service/subAccount/getSubAccountBal', { accountNo });

/**
 * 收款账户列表
 *
 * @see https://yapi.acg.team/project/1228/interface/api/131075
 */
export const bindingCardsList = custId =>
  service.get('/split-service/bindingCard/valid/cards', { custId });

/**
 * 提现经办
 *
 */
export const withdrawApply = body =>
  service.post('/split-service/fund/withdraw/apply', null, body);

/**
 * 银行卡列表
 *
 * @see https://yapi.acg.team/project/1228/interface/api/131123
 */
export const getBindingCardsList = body =>
  service.post('/split-service/bindingCard/list', null, body);

/**
 * 绑卡申请经办
 *
 */
export const bindingCardApply = body =>
  service.post('/split-service/bindingCard/apply', null, body);

/**
 * 银行卡下拉数据
 *
 */
export const getSubAccountBankList = body =>
  service.post('/split-service/open/subAccount/getBankList', null, body);

/**
 * 绑卡复核
 *
 */
export const bindingCardAudit = body =>
  service.post('/split-service/bindingCard/audit', null, body);

/**
 * 转账申请验证码
 *
 */
export const mobileCode = body =>
  service.post('/split-service/fund/audit/transfer/mobile/code', null, body);

/**
 * 确认转账
 *
 */
export const auditTransferConfirm = body =>
  service.post('/split-service/fund/audit/transfer/confirm', null, body);

/**
 * 失败重试
 *
 */
export const auditTradeRetry = body =>
  service.post('/split-service/fund/audit/trade/retry', null, body);

/**
 * 验证金额
 *
 */
export const bindingCardValidAmount = body =>
  service.post('/split-service/bindingCard/valid/amount', null, body);

/**
 * 绑卡
 *
 */
export const bindingCardBinding = body =>
  service.post('/split-service/bindingCard/do/binding', null, body);

/**
 * 解绑申请
 *
 */
export const unBindingApply = body =>
  service.post('/split-service/bindingCard/unBinding/apply', null, body);

/**
 * 解绑复核
 *
 */
export const unBindingAudit = body =>
  service.post('/split-service/bindingCard/unBinding/audit', null, body);

/**
 * 解绑重试
 *
 */
export const unBindingRetry = body =>
  service.post('/split-service/bindingCard/unBinding/retry', null, body);

/**
 * 账户开户审核
 *
 */
export const subAccountAudit = body =>
  service.post('/split-service/open/subAccount/open/audit', null, body);

/**
 * 转账经办
 *
 */
export const transferApply = body =>
  service.post('/split-service/fund/transfer/apply', null, body);

/**
 * 开户中检查
 *
 */
export const subAccountOpeningCheck = id =>
  service.get('/split-service/open/subAccount/opening/check', { id });

/**
 * 资金经办审核操作日志
 *
 * @see https://yapi.acg.team/project/1228/interface/api/131949
 */
export const fundOperLog = id =>
  service.get('/split-service/fund/oper/log', { id });

/**
 * 获取子账户银行流水明细列表
 *
 */
export const bankDetailQuery = body =>
  service.post('/split-service/subAccountDetail/bankDetailQuery', null, body);

/**
 * 账单类型下拉框
 *
 */
export const subAccountDetailSelector = body =>
  service.get('/split-service/subAccountDetail/selector', body);

/**
 * 日切余额导出
 *
 * @see https://yapi.acg.team/project/509/interface/api/138421
 */
export const exportHisBalance = body =>
  service.post('/split-service/acc/hisBalance/exportHisBalance', null, body);

/**
 * 平安日切余额列表
 *
 * @see https://yapi.acg.team/project/509/interface/api/138421
 */
export const listHisBalance = body =>
  service.get('/split-service/acc/hisBalance/listHisBalance', body);

/**
 * 日切余额导出
 *
 * @see https://yapi.acg.team/project/509/interface/api/139021
 */
export const platDetailQuery = body =>
  service.post('/split-service/subAccountDetail/platDetailQuery', null, body);

/**
 * 二级实际数据
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export const secondaryActualList = body =>
  service.post(
    '/split-service/serviceFeeUpload/secondarySplitActualPage',
    null,
    body,
  );

/**
 * 二级实际数据审核
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export const secondaryActualDataMerge = body =>
  service.post(
    '/split-service/serviceFeeUpload/secondarySplitActualAudit',
    null,
    body,
  );
/**
 * 二级实际数据-模板下载
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export const secondaryDataDownloadTemplate = () =>
  service.post('/split-service/serviceFeeUpload/fileTemplateDownload');

/**
 * 二级实际/调整-下拉框
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export const secondaryOption = () =>
  service.post('/split-service/serviceFeeAdjustment/selector');

/**
 * 二级调整差异
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export const secondaryAdjustmentList = body =>
  service.post('/split-service/serviceFeeAdjustment/page', null, body);

/**
 * 二级调整差异明细
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export const secondaryAdjustmentDetail = body =>
  service.post('/split-service/serviceFeeAdjustment/detailsPage', null, body);

/**
 * 二级调整差异-支付
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export const secondaryAdjustmentPay = body =>
  service.post(
    '/split-service/serviceFeeAdjustment/transfer/confirm',
    null,
    body,
  );
/**
 * 二级调整-转账验证码
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export const codeVerify = body =>
  service.post(
    '/split-service/serviceFeeAdjustment/transfer/mobile/code',
    null,
    body,
  );

/**
 * 资金申报-下拉框
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export const fundDeclarationSelector = () =>
  service.get('/split-service/fund/declare/selector', null);

/**
 * 资金申报-日志
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export const fundDeclarationLogList = body =>
  service.post('/split-service/fund/declare/listDeclareRecord', null, body);

/**
 * 资金申报-管理列表
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export const fundDeclarationManageList = body =>
  service.post('/split-service/fund/declare/listOrderSummary', null, body);

/**
 * 资金申报-明细列表
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export const fundDeclarationDetailList = body =>
  service.post('/split-service/fund/declare/listOrderDetail', null, body);

/**
 * 资金申报-提交申报
 * @see https://yapi.acg.team/project/1228/interface/api/139011
 */
export const fundDeclarationSubmit = body =>
  service.post('/split-service/fund/declare/submit', null, body);
/* * 跨境综合税差异列表
 * @see https://yapi.acg.team/project/509/interface/api/139977
 */
export const crossBorderConsolidatedTaxPage = body =>
  service.post('/split-service/crossBorderConsolidatedTax/page', null, body);

/**
 * 跨境综合税差异列表-明细列表
 * @see https://yapi.acg.team/project/509/interface/api/139975
 */
export const crossBorderConsolidatedTaxDetailPage = body =>
  service.post(
    '/split-service/crossBorderConsolidatedTax/detailPage',
    null,
    body,
  );

/**
 * 二级调整差异明细(实缴单调整)
 * @see https://yapi.acg.team/project/509/interface/api/139979
 */
export const crossBorderConsolidatedTaxDetailsPage = body =>
  service.post(
    '/split-service/serviceFeeAdjustment/crossBorderConsolidatedTaxDetailsPage',
    null,
    body,
  );

/**
 * 转账审核管理批量审核
 * @see
 */
export const batchAuditSubmit = body =>
  service.post('/split-service/fund/transfer/batchAudit', null, body);

/**
 * 转账审核管理批量终审
 * @see
 */
export const batchFinalAuditSubmit = body =>
  service.post('/split-service/fund/transfer/batchFinalAudit', null, body);

export default {
  secondaryOption,
  secondaryDataDownloadTemplate,
  secondaryActualDataMerge,
  secondaryAdjustmentPay,
  secondaryAdjustmentDetail,
  secondaryAdjustmentList,
  secondaryActualList,
  codeVerify,
  doWithdraw,
  getPlatformSubAccountList,
  getMemberSubAccountList,
  getMemberSubAccountOptions,
  getWithdrawRecordOptions,
  getWithdrawRecord,
  getTradeSubjectOptions,
  getTradeSubjectConfigList,
  getTradeSubjectConfigDetail,
  updateTradeSubjectConfig,
  getTradeSubjectRecords,
  getTradeSubjectRecordOptions,
  restartTradeSubjectRecords,
  getSubAccountTransferOptions,
  getSubAccountTransferList,
  addMemberSubAccountTransfer,
  updateMemberSubAccountTransfer,
  getTransferOrderList,
  getTransferOrderOptions,
  getOpenSubAccountOptions,
  getOpenSubAccountList,
  openSubAccount,
  subAccountBindCard,
  subAccountBindValid,
  getOpenMainPartAccountOptions,
  getOpenMainPartAccountList,
  createMainPartAccount,
  refreshSubAccountBalance,
  createSubAccountTransfer,
  deleteTradeSubjectConfig,
  getGatewayRechargeOptions,
  getGatewayRechargeList,
  doGatewayRecharge,
  unbindingCard,
  firstSplitStatsDaySum,
  firstSplitStatsMonthSum,
  firstSplitStatsDaySumDetail,
  secondaryConfigQuery,
  secondaryTradeSubjectQuery,
  secondaryUploadConfig,
  secondaryItemStatistics,
  secondaryTemplateImport,
  secondaryItemQuery,
  fundSelector,
  fundWithdrawPage,
  refreshSubAccountBal,
  bindingCardsList,
  withdrawApply,
  getBindingCardsList,
  bindingCardApply,
  getSubAccountBankList,
  bindingCardAudit,
  bindingCardBinding,
  unBindingApply,
  unBindingAudit,
  subAccountAudit,
  transferApply,
  subAccountOpeningCheck,
  unBindingRetry,
  bindingCardValidAmount,
  mobileCode,
  fundOperLog,
  bankDetailQuery,
  exportHisBalance,
  listHisBalance,
  subAccountDetailSelector,
  platDetailQuery,
  crossBorderConsolidatedTaxPage,
  crossBorderConsolidatedTaxDetailPage,
  crossBorderConsolidatedTaxDetailsPage,
  batchAuditSubmit,
  batchFinalAuditSubmit,
};
