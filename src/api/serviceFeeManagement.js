/*
 * @Description:服务费管理
 * @Author: 王嘉丽
 * @Date: 2021-12-08 10:22:46
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-04-07 14:53:45
 * @FilePath: /access-fmis-web/src/api/serviceFeeManagement.js
 */
import axios from '@/utils/request';

// 服务费管理-服务费主体管理-代开票主体查询列表
export function getAgentInvoincingEntityList(data) {
  return axios({
    url: '/financial-account/withdraw/pay/replaceMakeOutInvoice/getSubjectList',
    method: 'post',
    data,
  });
}

// 服务费管理-服务费主体管理-代开票主体页面下拉框数据
export function getAgentInvoincingEntityEnums(params) {
  return axios({
    url:
      '/financial-account/withdraw/pay/replaceMakeOutInvoice/getSubjectComboBoxData',
    method: 'get',
    params,
  });
}

// 服务费管理-服务费主体管理-代开票主体异步导出
export function exportAgentInvoincingEntityList(data) {
  return axios({
    url:
      '/financial-account/withdraw/pay/replaceMakeOutInvoice/downloadSubjectExcel',
    method: 'post',
    data,
  });
}

// 服务费管理-服务费主体管理-自开票主体查询列表
export function getPersonInvoincingEntityList(data) {
  return axios({
    url: '/financial-account/withdraw/pay/selfMakeOutInvoice/getSubjectList',
    method: 'post',
    data,
  });
}

// 服务费管理-服务费主体管理-自开票主体页面下拉框数据
export function getPersonInvoincingEntityEnums(params) {
  return axios({
    url:
      '/financial-account/withdraw/pay/selfMakeOutInvoice/getSubjectComboBoxData',
    method: 'get',
    params,
  });
}

// 服务费管理-服务费主体管理-自开票主体异步导出
export function exportPersonInvoincingEntityList(data) {
  return axios({
    url:
      '/financial-account/withdraw/pay/selfMakeOutInvoice/downloadSubjectExcel',
    method: 'post',
    data,
  });
}

// 服务费管理-任务中心-任务中心查询列表
export function getTaskCenterList(data) {
  return axios({
    url: '/financial-account/account/task/getList',
    method: 'post',
    data,
  });
}

// 服务费管理-服务费主体管理-任务中心页面下拉框数据
export function getTaskCenterEnums(params) {
  return axios({
    url: '/financial-account/account/task/getComboBoxData',
    method: 'get',
    params,
  });
}

// 服务费管理-服务费主体管理-任务中心下载
export function downloadTaskCenter(params, id) {
  return axios({
    url: '/financial-account/account/task/downloadFile?taskNo=' + id,
    method: 'get',
    params,
  });
}

//公共-发送手机验证码
export function getMobileCode(query) {
  return axios({
    url: '/financial-account/withdraw/pay/sendIdentifyingCode',
    method: 'get',
    params: query,
  });
}

//自开票提现-全量打款数据
export function personalAllPayData(body) {
  return axios({
    url: '/financial-account/withdraw/pay/selfMakeOutInvoice/getAllPayData',
    method: 'post',
    data: body,
  });
}

//自开票提现-全量打款
export function personalAllPay(body) {
  return axios({
    url: '/financial-account/withdraw/pay/selfMakeOutInvoice/allPay',
    method: 'post',
    data: body,
  });
}

//自开票提现-批量发票下载
export function batchInvoiceDownload(body) {
  return axios({
    url:
      '/financial-account/withdraw/pay/selfMakeOutInvoice/batchDownloadInvoice',
    method: 'post',
    data: body,
  });
}

//自开票提现-发票下载
export function invoiceDownload(query) {
  return axios({
    url: '/financial-account/withdraw/pay/selfMakeOutInvoice/downloadInvoice',
    method: 'get',
    params: query,
  });
}

//自开票提现-境外打款下载
export function payOutDownload(body) {
  return axios({
    url:
      '/financial-account/withdraw/pay/selfMakeOutInvoice/downloadPayOutExcel',
    method: 'post',
    data: body,
  });
}

//自开票提现-弹窗详情
export function getDetail(query) {
  return axios({
    url: '/financial-account/withdraw/pay/selfMakeOutInvoice/getDetail',
    method: 'get',
    params: query,
  });
}

//自开票提现-打款列表
export function queryPayList(body) {
  return axios({
    url: '/financial-account/withdraw/pay/selfMakeOutInvoice/getPayList',
    method: 'post',
    data: body,
  });
}
//自开票提现-导出
export function downloadPayList(body) {
  return axios({
    url: '/financial-account/withdraw/pay/selfMakeOutInvoice/downloadPayList',
    method: 'post',
    data: body,
  });
}

//自开票提现-审核列表
export function queryExamineList(body) {
  return axios({
    url: '/financial-account/withdraw/pay/selfMakeOutInvoice/getReviewList',
    method: 'post',
    data: body,
  });
}

//自开票提现-打款
export function handlePay(body) {
  return axios({
    url: '/financial-account/withdraw/pay/selfMakeOutInvoice/pay',
    method: 'post',
    data: body,
  });
}

//自开票提现-初审通过
export function firstPass(query) {
  return axios({
    url: '/financial-account/withdraw/pay/selfMakeOutInvoice/firstPass',
    method: 'get',
    params: query,
  });
}

//自开票提现-初审拒绝
export function firstReject(body) {
  return axios({
    url: '/financial-account/withdraw/pay/selfMakeOutInvoice/firstReject',
    method: 'post',
    data: body,
  });
}

//自开票提现-复审通过
export function secPass(query) {
  return axios({
    url: '/financial-account/withdraw/pay/selfMakeOutInvoice/retrialPass',
    method: 'get',
    params: query,
  });
}

//自开票提现-复审拒绝
export function secReject(body) {
  return axios({
    url: '/financial-account/withdraw/pay/selfMakeOutInvoice/retrialReject',
    method: 'post',
    data: body,
  });
}

//自开票提现-境外打款回传
export function uploadPayOutResult(body) {
  return axios({
    url:
      '/financial-account/withdraw/pay/selfMakeOutInvoice/uploadPayOutResultExcel',
    method: 'post',
    data: body,
  });
}

//自开票提现-打款下拉框数据
export function getPayOption() {
  return axios({
    url:
      '/financial-account/withdraw/pay/selfMakeOutInvoice/getPayComboBoxData',
    method: 'get',
  });
}

//自开票提现-审核下拉框数据
export function getExamineOption() {
  return axios({
    url:
      '/financial-account/withdraw/pay/selfMakeOutInvoice/getReviewComboBoxData',
    method: 'get',
  });
}

//自开票导出全部数据
export function selfMakeOutInvoiceDownload(body) {
  return axios({
    url:
      '/financial-account/withdraw/pay/selfMakeOutInvoice/downloadReviewList',
    method: 'post',
    data: body,
  });
}

//海外个人提现申请-下拉框
export function getOverseasReviewComboBoxData() {
  return axios({
    url: '/financial-account/withdraw/pay/overseas/getReviewComboBoxData',
    method: 'get',
  });
}

//海外个人提现申请-列表
export function getOverseasReviewList(body) {
  return axios({
    url: '/financial-account/withdraw/pay/overseas/getReviewList',
    method: 'post',
    data: body,
  });
}

//海外个人提现申请-导出excel
export function downloadOverseasReviewList(body) {
  return axios({
    url: '/financial-account/withdraw/pay/overseas/downloadReviewList',
    method: 'post',
    data: body,
  });
}

//海外个人提现申请-全量审核统计数据
export function getOverseasAllReviewData(body) {
  return axios({
    url: '/financial-account/withdraw/pay/overseas/getAllReviewData',
    method: 'post',
    data: body,
  });
}

//海外个人提现申请-全量审核
export function overseasAllPass(body) {
  return axios({
    url: '/financial-account/withdraw/pay/overseas/allPass',
    method: 'post',
    data: body,
  });
}

//海外个人提现申请-复审通过
export function overseasPass(query) {
  return axios({
    url: '/financial-account/withdraw/pay/overseas/pass',
    method: 'get',
    params: query,
  });
}

//海外个人提现申请-复审拒绝
export function overseasReject(body) {
  return axios({
    url: '/financial-account/withdraw/pay/overseas/reject',
    method: 'post',
    data: body,
  });
}

//海外个人提现打款-下拉框
export function getOverseasPayComboBoxData() {
  return axios({
    url: '/financial-account/withdraw/pay/overseas/getPayComboBoxData',
    method: 'get',
  });
}

//海外个人提现打款-列表
export function getOverseasPayList(body) {
  return axios({
    url: '/financial-account/withdraw/pay/overseas/getPayList',
    method: 'post',
    data: body,
  });
}

//海外个人提现打款-单个打款
export function overseasPay(body) {
  return axios({
    url: '/financial-account/withdraw/pay/overseas/pay',
    method: 'post',
    data: body,
  });
}

//海外个人提现打款-全量打款数据
export function overseasAllPayData(body) {
  return axios({
    url: '/financial-account/withdraw/pay/overseas/getAllPayData',
    method: 'post',
    data: body,
  });
}

//海外个人提现打款-全量打款
export function overseasAllPay(body) {
  return axios({
    url: '/financial-account/withdraw/pay/overseas/allPay',
    method: 'post',
    data: body,
  });
}

//海外个人提现打款-修改状态
export function overseasUpdateStatus(body) {
  return axios({
    url: '/financial-account/withdraw/pay/overseas/updateStatus',
    method: 'post',
    data: body,
  });
}

//海外个人提现打款-导出excel
export function overseasDownloadPayList(body) {
  return axios({
    url: '/financial-account/withdraw/pay/overseas/downloadPayList',
    method: 'post',
    data: body,
  });
}

// 服务费管理-服务费主体管理-海外主体查询列表
export function getOverseasSubjectList(data) {
  return axios({
    url: '/financial-account/withdraw/pay/overseas/getSubjectList',
    method: 'post',
    data,
  });
}

// 服务费管理-服务费主体管理-海外主体查询下拉框数据
export function getOverseasSubjectComboBoxData(params) {
  return axios({
    url: '/financial-account/withdraw/pay/overseas/getSubjectComboBoxData',
    method: 'get',
    params,
  });
}

// 服务费管理-服务费主体管理-海外主体查询异步导出
export function overseasDownloadSubjectList(data) {
  return axios({
    url: '/financial-account/withdraw/pay/overseas/downloadSubjectExcel',
    method: 'post',
    data,
  });
}

// 账户余额下拉框数据
export function getBalanceComboBoxData() {
  return axios({
    url: '/financial-account/accountManage/getBalanceComboBoxData',
    method: 'get',
  });
}

// 账户余额列表
export function getAccountBalanceList(data) {
  return axios({
    url: '/financial-account/accountManage/getAccountBalanceList',
    method: 'post',
    data,
  });
}

// 可提现户明细下拉框数据
export function getWithdrawalAccountComboBoxData() {
  return axios({
    url: '/financial-account/accountManage/getWithdrawalAccountComboBoxData',
    method: 'get',
  });
}

// 可提现户流水列表
export function getWithdrawalAccountDetailList(data) {
  return axios({
    url: '/financial-account/accountManage/getWithdrawalAccountDetailList',
    method: 'post',
    data,
  });
}

// 冻结户明细下拉框数据
export function getFrozenAccountComboBoxData() {
  return axios({
    url: '/financial-account/accountManage/getFrozenAccountComboBoxData',
    method: 'get',
  });
}

// 冻结户流水列表
export function getFrozenAccountDetailList(data) {
  return axios({
    url: '/financial-account/accountManage/getFrozenAccountDetailList',
    method: 'post',
    data,
  });
}
// 罚金，欠费流水列表
export function getAccountDetailByCategoryList(data) {
  return axios({
    url: '/financial-account/accountManage/getAccountDetailByCategoryList',
    method: 'post',
    data,
  });
}
// 订正申请
export function revisionApply(data) {
  return axios({
    url: '/bcp/revision/revisionApply',
    method: 'post',
    data,
  });
}

export function revisionQueryApply(data) {
  return axios({
    url: '/bcp/revision/queryApply',
    method: 'post',
    data,
  });
}

export function checkRevisionApply(data) {
  return axios({
    url: '/bcp/checkRevision/checkRevisionApply',
    method: 'post',
    data,
  });
}

export function rejectRevisionApply(data) {
  return axios({
    url: '/bcp/checkRevision/rejectRevisionApply',
    method: 'post',
    data,
  });
}

export function revisionImportExcel(data) {
  return axios({
    url: '/bcp/revision/importExcel',
    method: 'post',
    data,
  });
}

export function batchCheckRevisionApply(data) {
  return axios({
    url: '/bcp/checkRevision/batchCheckRevisionApply',
    method: 'post',
    data,
  });
}

export function batchRejectRevisionApply(data) {
  return axios({
    url: '/bcp/checkRevision/batchRejectRevisionApply',
    method: 'post',
    data,
  });
}
// 批量变更打款状态
export function importPayStatus(params) {
  return axios({
    url: '/financial-account/withdraw/pay/overseas/importPayStatus',
    method: 'get',
    params,
  });
}
// 收益打款管理-海外个人体现打款页面-打款明细导出
export function exportPaymentDetails(body) {
  return axios({
    url: '/financial-account/withdraw/pay/overseas/getPayListExport',
    method: 'post',
    data: body,
  });
}

// 批量变更打款状态
export function importCleanUser(params) {
  return axios({
    url: '/financial-account/accountManage/importCleanUser',
    method: 'get',
    params,
  });
}

// 海外主体结算列表
export function overseaSettleList(data) {
  return axios({
    url: '/financial-account/oversea/settle/getList',
    method: 'post',
    data,
  });
}

// 海外主体结算导出
export function overseaSettleExport(data) {
  return axios({
    url: '/financial-account/oversea/settle/export',
    method: 'post',
    data,
  });
}

// 修改提现税务信息
export function updateOverseasPayTax(data) {
  return axios({
    url: '/financial-account/withdraw/pay/overseas/updateOverseasPayTax',
    method: 'post',
    data,
  });
}

// 获取账号关系列表
export function getAccountRelations(data) {
  return axios({
    url: '/financial-account/relations/list',
    method: 'post',
    data,
  });
}

// 添加账号关系
export function addAccountRelations(data) {
  return axios({
    url: '/financial-account/relations/add',
    method: 'post',
    data,
  });
}

//终止关系
export function stopAccountRelations(data) {
  return axios({
    url: '/financial-account/relations/stop',
    method: 'post',
    data,
  });
}

//银行退票
export function bankReturn(data) {
  return axios({
    url: '/financial-account//withdraw/pay/overseas/bankReturn',
    method: 'post',
    data,
  });
}
