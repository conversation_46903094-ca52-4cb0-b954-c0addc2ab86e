<template>
  <div>
    <el-form v-model="searchParams" inline>
      <el-form-item label="业务单号:">
        <el-input
          v-model="searchParams.businessNumber"
          placeholder="请输入业务单号"
        />
      </el-form-item>
      <el-form-item label="发票号码:">
        <el-input
          v-model="searchParams.invoiceNumber"
          placeholder="请输入发票号码"
        />
      </el-form-item>
      <el-form-item
        v-if="![1, 3].includes(searchParams.origin)"
        label="发票代码:"
      >
        <el-input
          v-model="searchParams.invoiceCode"
          placeholder="请输入发票代码"
        />
      </el-form-item>
      <!-- <el-form-item label="抬头类型:">
        <el-select v-model="searchParams.headerType" placeholder="请选择">
          <el-option label="个人及企事业单位" :value="0"></el-option>
          <el-option label="企业" :value="1"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="开票日期:">
        <el-date-picker
          v-model="searchDate"
          :popper-class="'currentDatePickerClass'"
          :clearable="false"
          type="datetimerange"
          range-separator="至"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="isSalesInvoice ? '销方名称 :' : '购方名称 :'">
        <el-input
          v-model="searchParams[isSalesInvoice ? 'sellerName' : 'buyersName']"
          placeholder="请输入购方名称"
        />
      </el-form-item>
      <el-form-item v-if="isSalesInvoice" label="发票类别:">
        <el-select v-model="searchParams.origin" placeholder="请选择">
          <el-option
            v-for="item in INVOICE_CATEGORY_TYPES"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发票类目:">
        <el-select
          v-model="searchParams.invoiceCategory"
          placeholder="请选择"
          filterable
          clearable=""
        >
          <el-option
            v-for="item in invoiceCategoryList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="invoiceNumber" slot-scope="{ row }">
        <a style="cursor: pointer" @click="getFileUrl(row.fileUrl)">
          {{ row.invoiceNo }}
        </a>
      </template>
      <template slot="operation" slot-scope="scope">
        <el-popconfirm
          title="通知业务系统用票单据的来票信息和状态"
          @confirm="handelNotice(scope.row)"
        >
          <el-button slot="reference" type="text" size="mini">
            来票通知
          </el-button>
        </el-popconfirm>
      </template>
    </dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    getCategoryList,
    inputManagementQuery,
    sellInvoice,
    getFileUrl,
  } from '@/api/invoiceCenter';
  import {
    INVOICE_STATUS,
    INVOICE_TYPES,
    INVOICE_CATEGORY_TYPES,
    INVOICE_CATEGORY_LIST,
  } from '@/consts';
  import { parseTime, initSearchParams } from '@/utils';
  export default {
    components: {
      dynamictable,
    },
    props: {
      isSalesInvoice: {
        type: Boolean,
        required: false,
      },
    },
    data() {
      const invoiceTypeArr = [
        '增值税普通发票',
        '增值税专用发票',
        '增值税电子普通发票',
        '增值税电子专用发票',
        '形式发票',
      ];
      let columns = [
        {
          prop: this.isSalesInvoice ? 'sellerName' : 'annulNameJust',
          label: '销方名称',
        },
        {
          prop: 'buyersName',
          label: '购方名称',
        },
        {
          prop: 'businessNumber',
          label: '业务单号',
        },
        {
          prop: 'invoiceType',
          label: '发票类型',
          resizable: false,
          width: '120px',
        },
        {
          prop: 'invoiceCategoryName',
          label: '发票类目',
          resizable: false,
          width: '120px',
        },
        // {
        //   prop: 'headerType',
        //   label: '抬头类型',
        //   render: row => (
        //     <span>{row.headerType === 0 ? '个人及政府事业单位' : '企业'}</span>
        //   ),
        // },
        {
          prop: 'invoiceNo',
          label: '发票号码',
          scopedSlots: { customRender: 'invoiceNumber' },
        },
        {
          prop: 'invoiceCode',
          label: '发票代码',
        },
        {
          prop: 'makeOutAnInvoiceDate',
          label: '开票日期',
          render: row => (
            <span>
              {row.makeOutAnInvoiceDate
                ? parseTime(row.makeOutAnInvoiceDate)
                : '--'}
            </span>
          ),
        },
        {
          prop: 'invoiceState',
          label: '发票状态',
          resizable: false,
          width: '80px',
        },
        {
          prop: 'amount',
          label: '金额',
        },
        {
          prop: 'tax',
          label: '税额',
        },
      ];
      // if (!this.isSalesInvoice) {
      //   columns.push({
      //     prop: 'operation',
      //     label: '操作',
      //     fixed: 'right',
      //     scopedSlots: { customRender: 'operation' },
      //   })
      // }
      const key = this.isSalesInvoice ? 'sellerName' : 'buyersName';
      let searchParams = {
        businessNumber: undefined,
        // headerType: 0,
        invoiceCode: undefined,
        invoiceNumber: undefined,
        startTime: undefined,
        endTime: undefined,
        invoiceCategory:1,
      };
      if (this.isSalesInvoice) searchParams.origin = 0;
      return {
        INVOICE_STATUS,
        INVOICE_TYPES,
        INVOICE_CATEGORY_TYPES,
        searchDate: '',
        searchParams,
        list: [],
        columns,
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
        invoiceCategoryList: INVOICE_CATEGORY_LIST,
      };
    },
    created() {
      const { businessNumber, startTime, endTime, origin } = this.$route.query;
      let dateTime = '';
      if (businessNumber) {
        this.searchParams.businessNumber = businessNumber;
        this.searchParams.origin = Number(origin);
        dateTime = [
          parseTime(startTime, '{y}-{m}-{d} 00:00:00'),
          parseTime(endTime, '{y}-{m}-{d} 23:59:59'),
        ];
      }
      this.setData(() => {
        this.getList(true);
      }, dateTime);
    },
    methods: {
      setData(cb, dateTime) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
        if (dateTime) {
          this.searchDate = dateTime;
        } else {
          this.searchDate = [
            parseTime(start, '{y}-{m}-{d} 00:00:00'),
            parseTime(end, '{y}-{m}-{d} 23:59:59'),
          ];
        }

        cb && cb();
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.setData(() => {
          this.getList(true);
        });
      },
      async getList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        this.searchParams.startTime = this.searchDate
          ? parseTime(this.searchDate[0])
          : '';
        this.searchParams.endTime = this.searchDate
          ? parseTime(this.searchDate[1])
          : '';
        const params = {
          ...this.searchParams,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        this.options.loading = true;
        const api = this.isSalesInvoice ? sellInvoice : inputManagementQuery;
        const res = await api(initSearchParams(params));
        this.options.loading = false;
        this.list = res ? res.list : [];
        this.pagination.total = res ? res.total : 0;
      },
      handelNotice({ id }) {
        getCategoryList({ id }).then(res => {
          this.$message.success('操作成功');
          this.getList();
        });
      },
      getFileUrl(url) {
        if (url.includes('http://')) {
          window.open(url);
        } else {
          getFileUrl(url).then(res => {
            if (res) {
              window.open(res);
            }
          });
        }
      },
    },
  };
</script>

<style>
  .currentDatePickerClass
    > .el-picker-panel__footer
    > .el-button--text:first-child {
    display: none;
  }
</style>
