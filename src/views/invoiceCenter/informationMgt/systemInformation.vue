<template>
  <div>
    <el-form inline>
      <el-form-item label="系统编码:">
        <el-input
          v-model="searchParams.systemCode"
          placeholder="请输入系统编码"
        />
      </el-form-item>
      <el-form-item label="系统名称:">
        <!-- <el-input
          v-model="searchParams.systemName"
          placeholder="请输入系统名称"
        /> -->
        <el-autocomplete
          v-model="searchParams.systemName"
          :fetch-suggestions="querySearch"
          placeholder="请输入系统名称"
          @keyup.enter.native="findFirPage"
          @select="findFirPage"
          @blur="findFirPage"
        ></el-autocomplete>
      </el-form-item>
      <el-form-item label="内部代码:">
        <el-input
          v-model="searchParams.systemNo"
          placeholder="请输入内部代码"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <ac-permission-button
          type="primary"
          btn-text="新增"
          permission-key="systemInformation-add"
          @click="
            showDialog = true;
            currentRow = null;
          "
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="编辑"
          permission-key="systemInformation-edit"
          @click="handleEdit(scope.row)"
        ></ac-permission-button>
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          :btn-text="scope.row.status ? '禁用' : '启用'"
          :permission-key="
            scope.row.status
              ? 'systemInformation-disable'
              : 'systemInformation-enable'
          "
          @click="handelOperate(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <el-dialog
      width="600px"
      :title="saveParams.id ? '编辑系统信息' : '新增系统信息'"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
      @closed="onClose"
    >
      <el-form
        ref="formData"
        :model="saveParams"
        :rules="rulesForm"
        label-width="140px"
      >
        <el-form-item prop="systemCode" label="系统编码">
          <el-input
            v-model="saveParams.systemCode"
            style="width: 400px"
            maxlength="16"
            placeholder="请输入系统编码"
          ></el-input>
        </el-form-item>
        <el-form-item prop="systemName" label="系统名称">
          <el-input
            v-model="saveParams.systemName"
            style="width: 400px"
            maxlength="16"
            placeholder="请输入系统名称"
          ></el-input>
        </el-form-item>
        <el-form-item prop="systemNo" label="内部编码">
          <el-input
            v-model="saveParams.systemNo"
            style="width: 400px"
            maxlength="16"
            placeholder="请输入内部编码"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onOK">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import { specialSymbol } from '@/utils/validate';
  import localStorageKey from '@/consts/localStorage_key.js';
  import { throttle, debounce } from '@/utils';
  import {
    systemManagerPage,
    systemManagerIsExist,
    systemManagerOperate,
    systemManagerCreate,
    systemManagerUpdate,
  } from '@/api/invoiceCenter';

  export default {
    components: {
      dynamictable,
    },

    data() {
      let columns = [
        // {
        //   prop: 'id',
        //   label: '序号',
        // },
        {
          prop: 'systemNo',
          label: '系统简称',
        },
        {
          prop: 'systemName',
          label: '系统名称',
        },
        {
          prop: 'systemCode',
          label: '系统编码',
        },
        {
          prop: 'status',
          label: '状态',
          render: ({ status }) => <span>{status ? '启用' : '禁用'}</span>,
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '200',
          scopedSlots: { customRender: 'operation' },
        },
      ];
      const systemName = async (rule, value, callback) => {
        let res;
        if (value) {
          if (specialSymbol(value)) {
            callback(new Error('不能包含特殊符号, 表情包和null'));
            return;
          }

          res = await this.systemManagerIsExist({ systemName: value });

          if (res) {
            if (this.currentRow && this.currentRow.systemName === value) {
              callback();
            }
            callback(new Error('系统名称已存在'));
          }
        } else {
          callback();
        }
      };
      const systemNo = async (rule, value, callback) => {
        let res;

        if (value) {
          if (specialSymbol(value)) {
            callback(new Error('不能包含特殊符号, 表情包和null'));
            return;
          }

          res = await this.systemManagerIsExist({ systemNo: value });

          if (res) {
            if (this.currentRow && this.currentRow.systemNo === value) {
              callback();
            }
            callback(new Error('内部编码已存在'));
          }
        } else {
          callback();
        }
      };
      const systemCode = async (rule, value, callback) => {
        let res;

        if (value) {
          if (specialSymbol(value)) {
            callback(new Error('不能包含特殊符号, 表情包和null'));
            return;
          }

          res = await this.systemManagerIsExist({ systemCode: value });

          if (res) {
            if (this.currentRow && this.currentRow.systemCode === value) {
              callback();
            }
            callback(new Error('系统编码已存在'));
          }
        } else {
          callback();
        }
      };
      return {
        showDialog: false,
        rulesForm: {
          systemName: [
            {
              required: true,
              message: '请输入系统名称',
              trigger: 'change',
            },
            { validator: systemName, trigger: 'blur' },
          ],
          systemNo: [
            {
              required: true,
              message: '请输入内部代码',
              trigger: 'change',
            },
            { validator: systemNo, trigger: 'blur' },
          ],
          systemCode: [
            {
              required: true,
              message: '请输入系统编码',
              trigger: 'change',
            },
            { validator: systemCode, trigger: 'blur' },
          ],
        },
        saveParams: {
          systemName: '',
          systemCode: '',
          systemNo: '',
          id: '',
        },
        currentRow: null,
        searchParams: {
          systemName: '',
          systemCode: '',
          systemNo: '',
        },
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          mutiSelect: false,
          index: true,
        },
        columns,
      };
    },
    created() {
      this.getList(true);
    },
    methods: {
      findFirPage() {
        this.setIntoStorage();
      },
      createFilter(queryString) {
        return restaurant => {
          return (
            restaurant.value
              .toLowerCase()
              .indexOf(queryString.toLowerCase()) === 0
          );
        };
      },
      querySearch(queryString, cb) {
        // 调用 callback 返回建议列表的数据
        const noRoNameHistory =
          JSON.parse(localStorage.getItem(localStorageKey.SYSTEM_NAME)) || [];
        const results = queryString
          ? noRoNameHistory.filter(this.createFilter(queryString))
          : noRoNameHistory;
        cb(results);
      },
      setIntoStorage() {
        const noArr = []; // 历史记录数组
        const value = this.searchParams.systemName;
        const SYSTEM_NAME = localStorageKey.SYSTEM_NAME;
        noArr.push({ value: value, type: SYSTEM_NAME });
        console.log(this.searchParams.systemName);
        if (JSON.parse(localStorage.getItem(SYSTEM_NAME))) {
          // 判断是否已有xxx查询记录的localStorage
          if (localStorage.getItem(SYSTEM_NAME).indexOf(value) > -1) {
            // 判断是否已有此条查询记录，若已存在，则不需再存储
            return;
          }
          if (JSON.parse(localStorage.getItem(SYSTEM_NAME)).length >= 10) {
            const arr = JSON.parse(localStorage.getItem(SYSTEM_NAME));
            arr.pop();
            localStorage.setItem(SYSTEM_NAME, JSON.stringify(arr));
          }
          localStorage.setItem(
            SYSTEM_NAME,
            JSON.stringify(
              noArr.concat(JSON.parse(localStorage.getItem(SYSTEM_NAME))),
            ),
          );
        } else {
          // 首次创建
          localStorage.setItem(SYSTEM_NAME, JSON.stringify(noArr));
        }
        console.log(localStorage.getItem(SYSTEM_NAME));
      },
      async systemManagerIsExist(params) {
        const res = await systemManagerIsExist(params);
        if (res) {
          return true;
        }
        return false;
      },
      async handelOperate(val) {
        await systemManagerOperate({
          id: val.id,
          status: val.status ? false : true,
        });
        this.$message({
          message: val.status ? '已禁用!' : '已启用!',
          type: 'success',
        });
        this.getList();
      },
      getParams() {
        const params = {
          ...this.searchParams,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await systemManagerPage(params);
        this.options.loading = false;
        this.list = res ? res.list : [];
        this.pagination.total = res ? res.total : 0;
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        // this.getList(true)
      },
      onClose() {
        Object.assign(this.$data.saveParams, this.$options.data().saveParams);
        this.$nextTick(function () {
          this.$refs.formData.clearValidate();
        });
      },
      handleEdit(val) {
        const { systemName, systemCode, systemNo, id } = val;
        this.currentRow = val;
        this.saveParams = {
          systemName,
          systemCode,
          systemNo,
          id,
        };
        this.showDialog = true;
      },
      getEditParam(params) {
        let newPArams = {};
        Object.keys(params).forEach(item => {
          if (params[item] != this.currentRow[item]) {
            newPArams[item] = params[item];
          }
        });
        return newPArams;
      },

      onOK: debounce(function () {
        this.$refs.formData.validate(valid => {
          if (!valid) return;
          const saveApi = this.saveParams.id
            ? systemManagerUpdate
            : systemManagerCreate;
          let editParams = {
            ...this.saveParams,
          };

          saveApi(editParams).then(() => {
            this.$message.success(this.saveParams.id ? '修改成功' : '添加成功');
            this.showDialog = false;
            this.getList(true);
          });
        });
      }, 800),
    },
  };
</script>
<style lang="scss"></style>
