<template>
  <div>
    <el-form inline>
      <el-form-item label="公司中文简称:">
        <el-input
          v-model="searchParams.memberId"
          placeholder="请输入公司中文简称"
        />
      </el-form-item>
      <el-form-item label="公司代码:">
        <el-input
          v-model="searchParams.tenantRefundOrderId"
          placeholder="请输入公司代码"
        />
      </el-form-item>
      <el-form-item label="公司中文名称(全称):">
        <el-input
          v-model="searchParams.tenantRefundOrderId"
          placeholder="请输入公司名称"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <el-button
          slot="reference"
          type="text"
          size="small"
          @click="showDialog = true"
        >
          编辑前缀
        </el-button>
        <el-button
          slot="reference"
          :disabled="scope.row.status !== '3'"
          type="text"
          size="small"
          @click="handleDetails(scope.row)"
        >
          禁用
        </el-button>
        <el-button
          slot="reference"
          type="text"
          size="small"
          @click="handleDetails(scope.row)"
        >
          启用
        </el-button>
      </template>
    </dynamictable>
    <editCompany v-model="showDialog"></editCompany>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import editCompany from './components/editCompany';
  import {
    getRefundOrder,
    getCondition,
    retryRefund,
    refundToWithdrawalBatch,
    getPurchaseApplyList,
    orderQueryRefundOrderLog,
  } from '@/api/refundManagement';
  import { exportExcel } from '@/api/blob';
  import { downloadFile } from '@/utils';

  export default {
    components: {
      dynamictable,
      editCompany,
    },

    data() {
      let columns = [
        {
          prop: 'id',
          label: '序号',
        },
        {
          prop: 'tenantRefundOrderId',
          label: '公司代码',
        },
        {
          prop: 'statusName',
          label: '公司中文简称',
        },
        {
          prop: 'subStatusName',
          label: '公司中文全称',
        },
        {
          prop: 'amount',
          label: '公司英文简称',
        },
        {
          prop: 'refundAmount',
          label: '公司英文全称',
        },
        {
          prop: 'createdTime',
          label: '统一社会信用代码',
        },
        {
          prop: 'updatedTime',
          label: '公司地址',
        },
        {
          prop: 'memberId',
          label: '公司电话',
        },
        {
          prop: 'orderId',
          label: '发票前缀',
        },
        {
          prop: 'channelMessage',
          label: '开户行名称',
        },
        {
          prop: 'productionName',
          label: '账户名称',
        },
        {
          prop: 'channelName',
          label: '状态',
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '200',
          scopedSlots: { customRender: 'operation' },
        },
      ];
      return {
        showDialog: false,
        searchParams: {
          tenantRefundOrderId: '',
          status: '',
          paymentOrderId: '',
          memberId: '',
          startTimeStr: '',
          endTimeStr: '',
          tenantId: '',
          productionId: '',
        },
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          mutiSelect: false,
        },
        columns,
      };
    },
    created() {
      this.getList(true);
    },

    methods: {
      async handleDetails(val) {
        const res = await orderQueryRefundOrderLog({
          platformOrderId: val.platformOrderId,
        });
        this.logList = res ? res : [];
      },
      getParams() {
        const params = {
          ...this.searchParams,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await getRefundOrder(params);
        this.options.loading = false;
        this.list = res ? res.records : [];
        this.pagination.total = res ? res.total : 0;
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.getList(true);
      },
    },
  };
</script>
<style lang="scss"></style>
