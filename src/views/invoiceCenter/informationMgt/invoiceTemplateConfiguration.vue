<template>
  <div>
    <el-form inline>
      <el-form-item label="模板名称:">
        <el-input
          v-model="searchParams.templateName"
          placeholder="请输入模板名称"
        />
      </el-form-item>
      <el-form-item label="模板编号:">
        <el-input
          v-model="searchParams.templateCode"
          placeholder="请输入模板编号"
        />
      </el-form-item>
      <el-form-item label="公司名称:">
        <el-input
          v-model="searchParams.companyName"
          placeholder="请输入公司名称"
        />
      </el-form-item>
      <el-form-item label="公司代码:">
        <el-input
          v-model="searchParams.companyCode"
          placeholder="请输入公司代码"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <ac-permission-button
          type="primary"
          btn-text="编辑"
          permission-key="invoiceTemplateConfiguration-add"
          @click="
            showDialog = true;
            currentRow = null;
          "
        >
          新增
        </ac-permission-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="编辑"
          permission-key="invoiceTemplateConfiguration-edit"
          @click="
            showDialog = true;
            currentRow = { ...scope.row };
          "
        >
          编辑
        </ac-permission-button>
      </template>
    </dynamictable>
    <addEditTemplate
      v-model="showDialog"
      :current-row="currentRow"
      @onGet="getList(true)"
    ></addEditTemplate>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import addEditTemplate from './components/addEditTemplate';
  import { templateConfigList } from '@/api/invoiceCenter';

  export default {
    components: {
      dynamictable,
      addEditTemplate,
    },

    data() {
      let columns = [
        // {
        //   prop: 'id',
        //   label: '序号',
        // },
        {
          prop: 'companyName',
          label: '公司名称',
        },
        {
          prop: 'companyCode',
          label: '公司代码',
        },
        {
          prop: 'systemName',
          label: '系统名称',
        },
        {
          prop: 'systemCode',
          label: '系统编码',
        },
        {
          prop: 'templateName',
          label: '模板名称',
        },
        {
          prop: 'templateCode',
          label: '模板编号',
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '200',
          scopedSlots: { customRender: 'operation' },
        },
      ];
      return {
        showDialog: false,
        currentRow: null,
        searchParams: {
          templateName: '',
          templateCode: '',
          companyName: '',
          companyCode: '',
        },
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          mutiSelect: false,
          index: true,
        },
        columns,
      };
    },
    created() {
      this.getList(true);
    },

    methods: {
      getParams() {
        const params = {
          ...this.searchParams,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await templateConfigList(params);
        this.options.loading = false;
        this.list = res ? res.list : [];
        this.pagination.total = res ? res.total : 0;
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        // this.getList(true)
      },
    },
  };
</script>
<style lang="scss"></style>
