<!--
 * @Description: 
 * @Author: 刘攀
 * @Date: 2021-07-06 09:56:30
 * @LastEditTime: 2024-04-08 16:02:19
 * @LastEditors: bruce
 * @Reference: 
-->
<template>
  <div class="saleSubjectRule">
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      label-width="100px"
      class="demo-ruleForm"
      inline
    >
      <el-form-item label="渠道名称" prop="channelCode">
        <el-select
          v-model="ruleForm.channelCode"
          placeholder="请选择渠道名称"
          filterable
          clearable
        >
          <el-option
            v-for="(item, index) in selectoptions.channelList"
            :key="index"
            :label="item.channelName"
            :value="item.channelCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="店铺名称" prop="shopCode">
        <el-select
          v-model="ruleForm.shopCode"
          placeholder="请选择店铺名称"
          filterable
          clearable
        >
          <el-option
            v-for="(item, index) in selectoptions.allStoreList"
            :key="index"
            :label="item.storeName"
            :value="item.storeCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="searchDate"
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="ruleForm.status"
          placeholder="请选择状态"
          filterable
          clearable
        >
          <el-option
            v-for="(item, index) in selectoptions.statusList"
            :key="index"
            :label="item.desc"
            :value="item.status"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="仓库名称" prop="depotId">
        <el-select
          v-model="ruleForm.depotId"
          placeholder="请选择仓库名称"
          filterable
          clearable
        >
          <el-option
            v-for="(item, index) in selectoptions.deptList"
            :key="index"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="销售主体" prop="saleSubjectId">
        <el-select
          v-model="ruleForm.saleSubjectId"
          placeholder="请选择销售主体"
          filterable
          clearable
        >
          <el-option
            v-for="(item, key, index) in selectoptions.mapSaleList"
            :key="index"
            :label="item.name"
            :value="item.entityCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item style="margin-left: 20px">
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button @click="resetForm('ruleForm')">重置</el-button>
        <ac-permission-button
          type="primary"
          btn-text="新增销售主体规则"
          permission-key="message_add"
          plain
          @click="addHandle()"
        ></ac-permission-button>
        <ac-permission-button
          type="primary"
          btn-text="提交审核"
          permission-key="message_save"
          plain
          @click="submitReview()"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
      @selection-change="handleSelectionChange"
    >
      <template slot="countryName" slot-scope="scope">
        <el-popover
          placement="top"
          trigger="hover"
          :content="scope.row.countryName"
        >
          <el-button
            slot="reference"
            class="ellipsis"
            type="text"
            style="width: 80px"
          >
            {{ scope.row.countryName }}
          </el-button>
        </el-popover>
      </template>
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          v-if="
            (scope.row.status == 0 &&
              scope.row.approvalStatus != 1 &&
              scope.row.approvalStatus != 3) ||
            (scope.row.status != 0 && scope.row.approvalStatus == 1)
          "
          slot="reference"
          type="text"
          size="small"
          btn-text="编辑"
          permission-key="message_edit"
          @click="addHandle(scope.row)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="scope.row.status == 0 && scope.row.approvalStatus == 0"
          slot="reference"
          permission-key="message_save"
          type="text"
          size="small"
          btn-text="提交审核"
          @click="submitReview(scope.row.id)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="scope.row.approvalStatus == 1 && scope.row.status != 0"
          :key="scope.row.id"
          slot-btn="reference"
          node-type="popconfirm"
          :title="scope.row.status == 2 ? '确定启用吗？' : '确定禁用吗？'"
          type="text"
          size="small"
          :btn-text="scope.row.status == 1 ? '禁用' : '启用'"
          :permission-key="
            scope.row.status == 1 ? 'message_close' : 'message_open'
          "
          @click="handelOperate(scope.row)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="
            (scope.row.status == 0 && scope.row.approvalStatus != 1) ||
            (scope.row.status != 0 && scope.row.approvalStatus == 1)
          "
          slot="reference"
          permission-key="message_log"
          type="text"
          size="small"
          btn-text="日志"
          @click="openLogDialog(scope.row.id)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <!-- 新增销售主体/编辑销售主体 -->
    <el-dialog
      v-if="Object.keys(formData).length"
      width="40%"
      :title="formData.id ? '编辑销售主体' : '新增销售主体规则'"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
      @closed="onClose"
    >
      <sale-subject-dialog
        ref="form"
        :form-data="formData"
        :select-options="selectoptions"
        @saveForm="saveForm"
        @close="showDialog = false"
        @handleSelectDept="handleSelectDept"
        @handleSelectChannel="handleSelectChannel"
      ></sale-subject-dialog>
    </el-dialog>
    <!-- 日志表格 -->

    <el-dialog title="查看日志" :visible.sync="dialogTableVisible">
      <dynamictable
        :data-source="logList"
        :columns="logColumns"
        :options="logOptions"
      />
    </el-dialog>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    getSaleSubjectRuleList,
    addSaleSubjectRule,
    editSaleSubjectRule,
    createProcess,
    getListLog,
    updateStatus,
  } from '@/api/invoiceCenter';
  import SaleSubjectDialog from './components/saleSubjectDialog.vue';
  export default {
    components: {
      dynamictable,
      SaleSubjectDialog,
    },

    data() {
      let columns = [
        {
          prop: 'id',
          label: '规则编号',
        },
        {
          prop: 'channelCode',
          label: '渠道代码',
        },
        {
          prop: 'channelName',
          label: '渠道名称',
        },
        {
          prop: 'shopCode',
          label: '店铺编码',
        },
        {
          prop: 'shopName',
          label: '店铺名称',
        },
        {
          prop: 'depotId',
          label: '仓库ID',
        },
        {
          prop: 'depotName',
          label: '仓库名称',
        },
        {
          prop: 'countryName',
          label: '目的国',
          width: 120,
          scopedSlots: { customRender: 'countryName' },
        },
        {
          prop: 'userLevelName',
          label: '下单用户等级',
        },
        {
          prop: 'saleSubjectCode',
          label: '销售主体编码',
        },
        {
          prop: 'saleSubjectName',
          label: '销售主体名称',
          width: 120,
        },
        {
          prop: 'beginTime',
          label: '生效时间',
          width: 180,
        },
        {
          prop: 'endTime',
          label: '失效时间',
          width: 180,
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 180,
        },
        {
          prop: 'updateTime',
          label: '更新时间',
          width: 180,
        },
        {
          prop: 'statusName',
          label: '状态',
        },
        {
          prop: 'approvalStatusName',
          label: '审核状态',
          width: 100,
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '160',
          scopedSlots: { customRender: 'operation' },
        },
      ];
      return {
        ruleForm: {
          channelCode: '', //渠道编码
          depotId: '', //虚仓id
          createEndTime: '',
          createStartTime: '',
          saleSubjectId: '', //销售主体id
          shopCode: '', // 店铺编码
          status: '', //状态
        },
        searchDate: [],
        selectoptions: {},
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        selectArr: [],
        options: {
          loading: false,
          border: true,
          mutiSelect: true,
        },
        columns,
        // 弹窗
        showDialog: false,
        formData: {},
        // 日志列表
        dialogTableVisible: false,
        logList: [],
        logColumns: [
          {
            prop: 'operatorId',
            label: '操作人id',
            width: 80,
          },
          {
            prop: 'operatorName',
            label: '操作人名称',
          },
          {
            prop: 'action',
            label: '操作内容',
          },
          {
            prop: 'content',
            label: '操作说明',
          },
          {
            prop: 'createTime',
            label: '操作时间',
          },
        ],
        logOptions: {
          loading: false,
          border: true,
          mutiSelect: false,
        },
      };
    },

    created() {
      this.getList(true);
      this.init();
    },
    methods: {
      handleClear(val) {
        this.ruleForm.shopCode = '';
      },
      onClose() {
        this.formData = {};
        this.$store.dispatch('selectOption/resetData');
      },
      async init() {
        await this.$store.dispatch('selectOption/setChannelList');
        this.selectoptions = this.$store.state.selectOption;
      },
      getParams() {
        this.ruleForm.createStartTime = this.searchDate
          ? this.searchDate[0]
          : '';
        this.ruleForm.createEndTime = this.searchDate ? this.searchDate[1] : '';
        const params = {
          ...this.ruleForm,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        try {
          const res = await getSaleSubjectRuleList(params);
          this.options.loading = false;
          this.list = res ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        } catch (error) {
          this.options.loading = false;
        }
      },
      // 仓库名称选择
      async handleSelectDept(val, cb) {
        if (!val) return;
        await this.$store.dispatch('selectOption/setNationList', {
          virtualWarehouseCodeList: Array.isArray(val) ? val : [val.depotCode],
        });
        cb && cb();
      },
      // 渠道名称
      async handleSelectChannel({ channelCode }, cb) {
        if (!channelCode) return;
        await this.$store.dispatch('selectOption/setStoreList', {
          channelCode,
        });
        cb && cb(true);
      },
      // 新增
      async addHandle(row) {
        if (row && row.id) {
          if (row.channelType === 1) {
            const { deptList = [] } = this.selectoptions;
            const depotCodeList = deptList.map(item => item.depotCode);
            this.handleSelectDept(depotCodeList);
          } else {
            this.handleSelectDept(row);
          }

          this.handleSelectChannel(row);
        }
        let data = {
          channelCode: '',
          depotId: '',
          beginTime: null,
          endTime: null,
          saleSubjectCode: '',
          shopCode: '',
          userLevelList: [],
          countryIdList: [], // 目的国列表
        };
        let detailsInfo = {
          ...row,
        };
        if (row && row.channelType === 1) {
          detailsInfo.depotId = '';
          detailsInfo.depotName = '';
        }
        this.formData = row ? JSON.parse(JSON.stringify(detailsInfo)) : data;
        this.showDialog = true;
        if (this.$refs.form) {
          console.log(this.formData, data, 'this.formDataeerr');
          this.$refs.form.$emit(
            'initData',
            JSON.parse(JSON.stringify(this.formData)),
          );
        }
      },
      // 重置
      resetForm(formName) {
        this.searchDate = [];
        this.$refs[formName].resetFields();
        this.getList(true);
      },
      // 选择
      handleSelectionChange(val) {
        this.selectArr = val;
      },
      // 提交审核
      async submitReview(e) {
        var ids = [];
        if (e) {
          this.$confirm('确定提交审核吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(async () => {
            ids = [e];
            const res = await createProcess(ids);
            this.getList();
            this.$message.success('操作成功');
          });
        } else {
          if (this.selectArr.length == 0) {
            this.$message.warning('请选择要提交审核的列');
            return;
          }
          ids = this.selectArr.map(ele => ele.id);
          const res = await createProcess(ids);
          this.getList();
          this.$message.success('操作成功');
        }
      },
      // 启用，禁用
      async handelOperate(row) {
        let params = {
          id: row.id,
          status: row.status == 1 ? 2 : 1,
        };
        const res = await updateStatus(params);
        this.$message.success('操作成功');
        this.getList();
      },
      // 弹窗保存
      async saveForm(e) {
        if (e.id) {
          const res = await editSaleSubjectRule(e);
          this.getList();
          this.$message.success('编辑成功');
          this.showDialog = false;
        } else {
          const res = await addSaleSubjectRule(e);
          this.getList(true);
          this.$message.success('新增成功');
          this.showDialog = false;
        }
      },
      // 打开日志弹框
      async openLogDialog(e) {
        this.dialogTableVisible = true;
        this.logOptions.loading = true;
        const res = await getListLog({ id: e });
        this.logList = res ? res : [];
        this.logOptions.loading = false;
      },
    },
  };
</script>
<style>
  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
