<template>
  <div class="proformaInvoiceTemplate">
    <div v-if="tab === 1">
      <el-form inline>
        <el-form-item label="模板名称:">
          <el-input
            v-model="searchParams.templateName"
            placeholder="请输入模板名称"
          />
        </el-form-item>
        <el-form-item label="模板编号:">
          <el-input
            v-model="searchParams.templateCode"
            placeholder="请输入模板编号"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="getList(true)">查询</el-button>
          <el-button type="primary" @click="onReset">重置</el-button>
          <ac-permission-button
            type="primary"
            btn-text="新增"
            permission-key="proformaInvoiceTemplate-add"
            @click="
              showDialog = true;
              currentRow = null;
            "
          ></ac-permission-button>
        </el-form-item>
      </el-form>
      <dynamictable
        :data-source="list"
        :columns="columns"
        :options="options"
        :pagination="pagination"
        :fetch="getList"
      >
        <template slot="operation" slot-scope="scope">
          <ac-permission-button
            slot="reference"
            type="text"
            size="small"
            btn-text="编辑"
            permission-key="proformaInvoiceTemplate-edit"
            @click="
              currentRow = scope.row;
              showDialog = true;
            "
          ></ac-permission-button>
          <ac-permission-button
            slot="reference"
            type="text"
            size="small"
            :btn-text="scope.row.status ? '禁用' : '启用'"
            :permission-key="
              scope.row.status
                ? 'proformaInvoiceTemplate-disable'
                : 'proformaInvoiceTemplate-enable'
            "
            @click="handelOperate(scope.row)"
          ></ac-permission-button>
          <el-button
            slot="reference"
            type="text"
            size="small"
            @click="handleSee(scope.row)"
          >
            查看模版
          </el-button>
          <ac-permission-button
            slot="reference"
            type="text"
            size="small"
            btn-text="下载模版"
            permission-key="proformaInvoiceTemplate-download-template"
            @click="downloadFile(scope.row)"
          ></ac-permission-button>
        </template>
      </dynamictable>
      <addProformaInvoiceTemplate
        v-model="showDialog"
        :current-row="currentRow"
        @onGet="getList(true)"
      ></addProformaInvoiceTemplate>
    </div>
    <div v-if="tab === 2">
      <div class="stencil-style">模板样式查看</div>
      <div class="stencil-heard">
        <span>模板样式</span>

        <div>
          <!-- <el-button>保存</el-button> -->
          <el-button @click="tab = 1">返回</el-button>
        </div>
      </div>
      <el-divider content-position="left"></el-divider>
      <!-- <div class="wordView" v-html="vHtml" /> -->
      <iframe :src="docxUrl" width="100%" height="800"></iframe>
    </div>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import mammoth from 'mammoth';
  import addProformaInvoiceTemplate from './components/addProformaInvoiceTemplate';
  import { templatePage, templateOperate } from '@/api/invoiceCenter';

  export default {
    components: {
      dynamictable,
      addProformaInvoiceTemplate,
    },

    data() {
      let columns = [
        // {
        //   prop: 'id',
        //   label: '序号',
        // },
        {
          prop: 'templateName',
          label: '模板名称',
        },
        {
          prop: 'templateCode',
          label: '模板编号',
        },
        {
          prop: 'status',
          label: '状态',
          render: ({ status }) => <span>{status ? '启用' : '禁用'}</span>,
        },
        {
          prop: 'remark',
          label: '备注',
        },
        {
          prop: 'templateType',
          label: '类型',
          render: ({ templateType }) => (
            <span>{templateType === 0 ? '进项' : '销项'}</span>
          ),
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '200',
          scopedSlots: { customRender: 'operation' },
        },
      ];
      return {
        tab: 1,
        docxUrl: null,
        showDialog: false,
        currentRow: null,
        vHtml: '',
        searchParams: {
          templateName: '',
          templateCode: '',
        },
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          mutiSelect: false,
          index: true,
        },
        columns,
      };
    },
    created() {
      this.getList(true);
    },

    methods: {
      handleSee(val) {
        this.tab = 2;
        this.docxUrl = `https://view.officeapps.live.com/op/view.aspx?src=${val.fileUrl}`;

        // let vm = this;
        // vm.wordURL = val.fileUrl;

        // const xhr = new XMLHttpRequest();
        // xhr.open('get', this.wordURL, true);
        // xhr.responseType = 'arraybuffer';
        // xhr.onload = function () {
        //   if (xhr.status == 200) {
        //     mammoth
        //       .convertToHtml({ arrayBuffer: new Uint8Array(xhr.response) })
        //       .then(function (resultObject) {
        //         vm.$nextTick(() => {
        //           vm.vHtml = resultObject.value;
        //         });
        //       });
        //   }
        // };
        // xhr.send();
      },
      async handelOperate({ id, status }) {
        await templateOperate({
          id,
          status: status ? false : true,
        });
        this.$message({
          message: status ? '已禁用!' : '已启用!',
          type: 'success',
        });
        this.getList();
      },
      getParams() {
        const params = {
          ...this.searchParams,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await templatePage(params);
        this.options.loading = false;
        this.list = res ? res.list : [];
        this.pagination.total = res ? res.total : 0;
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        // this.getList(true)
      },
      downloadFile({ fileUrl }) {
        window.location.href = fileUrl.replace(
          /^http[s]?:\/\//,
          window.location.protocol + '//',
        );
      },
    },
  };
</script>
<style lang="scss">
  .proformaInvoiceTemplate {
    .wordView {
      width: 60%;
      margin-bottom: 20px;
      padding: 20px;
      border: 1px solid #ebeef5;
      // table {
      //   padding: 20px;
      //   border: 1px solid #ebeef5;
      // }
    }
    .stencil-heard {
      margin-top: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .stencil-style {
      font-size: 18px;
      text-align: center;
      line-height: 50px;
      background: #13ce68;
    }
  }
</style>
