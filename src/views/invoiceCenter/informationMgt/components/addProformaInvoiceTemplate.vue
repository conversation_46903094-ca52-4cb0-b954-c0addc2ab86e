<template>
  <div>
    <el-dialog
      width="600px"
      :title="id ? '编辑模版' : '新增模版'"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="saveParams"
        :rules="rulesForm"
        label-width="140px"
      >
        <el-form-item label="模板名称" prop="templateName">
          <el-input
            v-model="saveParams.templateName"
            style="width: 340px"
            maxlength="16"
            placeholder="请输入模板名称"
          />
        </el-form-item>
        <el-form-item label="模板编号" prop="templateCode">
          <el-input
            v-model="saveParams.templateCode"
            style="width: 340px"
            maxlength="16"
            placeholder="请输入模板编号"
          />
          <div style="color: rgba(0, 0, 0, 0.45)">格式例如：TINV0001</div>
        </el-form-item>
        <el-form-item label="类型" prop="templateType">
          <!-- <el-input
            v-model="saveParams.templateType"
            style="width: 340px"
            placeholder="请输入，如进项/销项"
          /> -->
          <el-select v-model="saveParams.templateType" placeholder="请选择类型">
            <el-option label="进项" :value="0"></el-option>
            <el-option label="销项" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="saveParams.remark"
            type="textarea"
            :rows="4"
            style="width: 340px"
            maxlength="256"
            placeholder="请输入备注"
          />
        </el-form-item>
        <el-form-item required :error="errorTip">
          <uploadImg
            ref="uploadImg"
            btn-text="上传模版"
            list-type="text"
            :upload-data="{
              appId: 'bill',
              folder: 'invoice/template',
              timeStamp: Date.now(),
            }"
            accept=".docx"
            :init-file-list="initFileList"
            :type-file="[]"
            :max="1"
            @changeImage="changeImage"
            @onRemove="onRemove"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onOK">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import uploadImg from '@/components/uploadImg';
  import {
    templateCreate,
    templateUpdate,
    templateIsExist,
  } from '@/api/invoiceCenter';
  import { specialSymbol } from '@/utils/validate';
  import { debounce } from '@/utils';
  export default {
    components: {
      uploadImg,
    },
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      currentRow: {
        type: Object,
        default: null,
      },
    },
    data() {
      const validateTemplateName = async (rule, value, callback) => {
        let res;
        // if (value) {
        //   res = await this.templateIsExist({ templateName: value })
        // }
        if (value) {
          if (specialSymbol(value)) {
            callback(new Error('不能包含特殊符号, 表情包和null'));
            return;
          }

          res = await this.templateIsExist({ templateName: value });

          if (res) {
            if (this.currentRow && this.currentRow.templateName === value) {
              callback();
            }
            callback(new Error('模板名称已存在'));
          }
        } else {
          callback();
        }
      };
      const validateRemark = async (rule, value, callback) => {
        if (value && specialSymbol(value)) {
          callback(new Error('不能包含特殊符号, 表情包和null'));
        } else {
          callback();
        }
      };
      const validateTemplateNo = async (rule, value, callback) => {
        let res;
        // if (value) {
        //   res = await this.templateIsExist({ templateCode: value })
        // }
        if (value) {
          if (specialSymbol(value)) {
            callback(new Error('不能包含特殊符号, 表情包和null'));
            return;
          }
          res = await this.templateIsExist({ templateCode: value });
          if (res) {
            if (this.currentRow && this.currentRow.templateCode === value) {
              callback();
            }
            callback(new Error('模板编号已存在'));
          }
        } else {
          callback();
        }
      };
      return {
        errorTip: '',
        initFileList: [],
        id: null,

        saveParams: {
          templateName: '',
          templateCode: '',
          fileId: '',
          fileUrl: '',
          templateType: '',
          remark: '',
        },
        rulesForm: {
          templateName: [
            {
              required: true,
              message: '请输入模板名称',
              trigger: 'change',
            },
            { validator: validateTemplateName, trigger: 'blur' },
          ],
          templateCode: [
            {
              required: true,
              message: '请输入模板编号',
              trigger: 'change',
            },
            { validator: validateTemplateNo, trigger: 'blur' },
          ],
          templateType: [
            {
              required: true,
              message: '请选择类型',
              trigger: 'change',
            },
          ],
          remark: [
            {
              required: false,
              validator: validateRemark,
              trigger: 'blur',
            },
          ],
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(val) {
        if (val) {
          if (this.currentRow) {
            const {
              templateName,
              templateCode,
              fileId,
              fileUrl,
              templateType,
              remark,
              id,
            } = this.currentRow;
            this.id = id;
            this.saveParams = {
              templateName,
              templateCode,
              fileId,
              fileUrl,
              templateType,
              remark,
            };
            if (fileId && fileUrl) {
              this.errorTip = '';
              this.initFileList = [
                {
                  url: fileUrl,
                  id: fileId,
                  name: '文件',
                },
              ];
            }
          }
        } else {
          Object.assign(this.$data.saveParams, this.$options.data().saveParams);
          this.id = null;
          this.$nextTick(function () {
            this.$refs.formData.clearValidate();
            this.errorTip = '';
            this.initFileList = [];
          });
        }
      },
    },
    methods: {
      async templateIsExist(params) {
        const res = await templateIsExist(params);
        if (res) {
          return true;
        }
        return false;
      },
      onRemove() {
        this.saveParams.fileId = '';
        this.saveParams.fileUrl = '';
        this.errorTip = '请上传模版';
      },
      changeImage(files) {
        if (files.id) {
          this.saveParams.fileId = files.id;
          this.saveParams.fileUrl = files.file_url;
          this.errorTip = '';
        }
      },
      getEditParam(params) {
        let newPArams = {};
        Object.keys(params).forEach(item => {
          if (params[item] != this.currentRow[item]) {
            newPArams[item] = params[item];
          }
        });
        return newPArams;
      },
      onOK: debounce(function () {
        this.$refs.formData.validate(valid => {
          if (!this.saveParams.fileId) {
            this.errorTip = '请上传模版';
            return;
          }
          if (!valid) return;

          const saveApi = this.id ? templateUpdate : templateCreate;
          const params = {
            ...this.saveParams,
          };

          if (this.id) {
            params.id = this.id;
          }
          saveApi(params).then(res => {
            this.$message.success(this.id ? '修改成功' : '添加成功');
            this.showDialog = false;

            this.$emit('onGet');
          });
        });
      }, 800),
    },
  };
</script>

<style></style>
