<template>
  <div>
    <el-dialog
      class="new-invoice-dialog"
      width="1100px"
      title="编辑公司信息"
      :visible.sync="showDialog"
    >
      <div style="margin-left: 60px; margin-bottom: 40px">
        <el-button @click="handelEdit">
          {{ isEdit ? '修 改' : '取 消' }}
        </el-button>
        <el-button style="margin-left: 40px" type="primary" @click="onOK">
          保 存
        </el-button>
      </div>
      <h3 style="margin-left: 60px">基本信息：</h3>
      <el-form
        ref="formData"
        :inline="true"
        :model="saveParams"
        label-width="140px"
      >
        <el-form-item prop="bankNo" label="公司代码">
          <el-input
            v-model="saveParams.bankNo"
            :disabled="isEdit"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item prop="bankNo" label="公司所属区域">
          <el-input
            v-model="saveParams.bankNo"
            :disabled="isEdit"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item prop="bankNo" label="统一社会信用代码">
          <el-input
            v-model="saveParams.bankNo"
            :disabled="isEdit"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item prop="bankNo" label="公司全称(中文)">
          <el-input
            v-model="saveParams.bankNo"
            :disabled="isEdit"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item prop="bankNo" label="公司简称(中文)">
          <el-input
            v-model="saveParams.bankNo"
            :disabled="isEdit"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item prop="bankNo" label="公司统一编码">
          <el-input
            v-model="saveParams.bankNo"
            :disabled="isEdit"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item prop="bankNo" label="公司全称(英文)">
          <el-input
            v-model="saveParams.bankNo"
            :disabled="isEdit"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item prop="bankNo" label="公司简称(英文)">
          <el-input
            v-model="saveParams.bankNo"
            :disabled="isEdit"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item prop="bankNo" label="开户行名称">
          <el-input
            v-model="saveParams.bankNo"
            :disabled="isEdit"
            placeholder="请输入"
          />
        </el-form-item>

        <el-form-item prop="bankNo" label="银行账户号">
          <el-input
            v-model="saveParams.bankNo"
            :disabled="isEdit"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item prop="bankNo" label="银行账户名">
          <el-input
            v-model="saveParams.bankNo"
            :disabled="isEdit"
            placeholder="请输入"
          />
        </el-form-item>

        <el-form-item prop="bankNo" label="银行地址">
          <el-input
            v-model="saveParams.bankNo"
            :disabled="isEdit"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item prop="bankNo" label="公司地址">
          <el-input
            v-model="saveParams.bankNo"
            :disabled="isEdit"
            placeholder="请输入"
          />
        </el-form-item>

        <el-form-item prop="bankNo" label="公司电话">
          <el-input
            v-model="saveParams.bankNo"
            :disabled="isEdit"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item prop="bankNo" label="发票前缀">
          <el-input
            v-model="saveParams.bankNo"
            :disabled="!isEdit"
            placeholder="请输入"
          />
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        isEdit: true,
        saveParams: {},
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(val) {
        console.log(val, '111111111111111111');
      },
    },
    methods: {
      handelEdit() {
        this.isEdit = !this.isEdit;
      },
      onOK() {},
    },
  };
</script>

<style></style>
