<template>
  <div>
    <el-dialog
      width="800px"
      :title="saveParams.id ? '编辑模板关系配置' : '新增模板关系配置'"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :inline="true"
        :model="saveParams"
        :rules="rulesForm"
        label-width="140px"
      >
        <el-divider content-position="left">选择公司和系统信息</el-divider>
        <el-form-item label="公司全称" prop="companyName">
          <el-select
            v-model="saveParams.companyName"
            filterable
            placeholder="请选择公司全称"
            @change="e => handleSelect(e, 'companyCode', 'companyName')"
          >
            <el-option
              v-for="item in companyList"
              :key="item.companyCode"
              :label="item.companyName"
              :value="item.companyName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="companyCode" label="公司代码">
          <el-input
            v-model="saveParams.companyCode"
            :disabled="true"
            placeholder="选择公司全称后自动补齐"
          />
        </el-form-item>
        <el-form-item label="系统名称" prop="systemName">
          <el-select
            v-model="saveParams.systemName"
            placeholder="请选择系统名称"
            @change="e => handleSelect(e, 'systemCode', 'systemName')"
          >
            <el-option
              v-for="item in systemManagerList"
              :key="item.systemCode"
              :label="item.systemName"
              :value="item.systemName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="systemCode" label="系统代码">
          <el-input
            v-model="saveParams.systemCode"
            :disabled="true"
            placeholder="选择系统名称后自动补齐"
          />
        </el-form-item>

        <el-divider content-position="left">选择模板</el-divider>

        <el-form-item label="模板名称" prop="templateName">
          <el-select
            v-model="saveParams.templateName"
            placeholder="请选择模板名称"
            @change="e => handleSelect(e, 'templateCode', 'templateName')"
          >
            <el-option
              v-for="item in templateList"
              :key="item.templateCode"
              :label="item.templateName"
              :value="item.templateName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="templateCode" label="模板编号">
          <el-input
            v-model="saveParams.templateCode"
            :disabled="true"
            placeholder="选择模板名称后自动补齐"
          />
        </el-form-item>
      </el-form>
      <el-divider v-if="saveParams.id" content-position="left">
        日志信息
      </el-divider>
      <dynamictable
        v-if="saveParams.id"
        :data-source="list"
        :columns="columns"
      ></dynamictable>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onOK">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    templateConfigCreate,
    templateConfigUpdate,
    templateConfigQueryOperate,
    systemManagerList,
    templateList,
    companyQueryList,
    templateConfigIsExist,
  } from '@/api/invoiceCenter';
  import { parseTime, debounce } from '@/utils';
  export default {
    components: {
      dynamictable,
    },
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      currentRow: {
        type: Object,
        default: null,
      },
    },
    data() {
      return {
        columns: [
          {
            prop: 'userId',
            label: '操作人ID',
          },
          {
            prop: 'userName',
            label: '操作人名称',
          },
          {
            prop: 'operateContent',
            label: '操作内容',
          },
          {
            prop: 'operateTime',
            label: '操作时间',
            render: ({ operateTime }) => (
              <span>{operateTime ? parseTime(operateTime) : ''}</span>
            ),
          },
        ],
        list: [],
        companyList: [],
        templateList: [],
        systemManagerList: [],
        rulesForm: {
          companyName: [
            {
              required: true,
              message: '请选择公司全称',
              trigger: 'change',
            },
          ],
          systemName: [
            {
              required: true,
              message: '请选择系统名称',
              trigger: 'change',
            },
          ],
          templateName: [
            {
              required: true,
              message: '请选择模板名称',
              trigger: 'change',
            },
          ],
        },

        saveParams: {
          id: '',
          companyName: '',
          companyCode: '',
          systemId: '',
          systemName: '',
          systemCode: '',
          templateId: '',
          templateName: '',
          templateCode: '',
          fileUrl: '',
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(val) {
        if (val) {
          if (this.currentRow) {
            this.saveParams = {
              ...this.currentRow,
            };
            templateConfigQueryOperate({
              id: this.currentRow.id,
            }).then(res => {
              if (res) {
                this.list = res;
              }
            });
          }
        } else {
          Object.assign(this.$data.saveParams, this.$options.data().saveParams);
          this.$nextTick(function () {
            this.$refs.formData.clearValidate();
          });
        }
      },
    },
    created() {
      systemManagerList({}).then(res => {
        if (res) {
          this.systemManagerList = res
            .map(item => {
              if (item.status) {
                return item;
              }
            })
            .filter(i => !!i);
          console.log(this.systemManagerList, 'this.systemManagerList');
        }
      });
      templateList({}).then(res => {
        if (res) {
          this.templateList = res
            .map(item => {
              if (item.status) {
                return item;
              }
            })
            .filter(i => !!i);
        }
      });
      companyQueryList({}).then(res => {
        if (res) {
          this.companyList = res;
        }
      });
    },
    methods: {
      async templateConfigIsExist(params) {
        const res = await templateConfigIsExist(params);
        if (res) {
          return true;
        }
        return false;
      },
      handleSelect(val, key, id) {
        const listObj = {
          companyCode: this.companyList,
          systemCode: this.systemManagerList,
          templateCode: this.templateList,
        };
        listObj[key].forEach(item => {
          if (val === item[id]) {
            if (key === 'templateCode') {
              this.saveParams.fileUrl = item.fileUrl;
              this.saveParams.templateId = item.id;
            }
            if (key === 'systemCode') {
              this.saveParams.systemId = item.id;
            }

            this.saveParams[key] = item[key];
          }
        });
      },
      onOK: debounce(function () {
        this.$refs.formData.validate(async valid => {
          if (!valid) return;
          // const flag = await this.templateConfigIsExist({
          //   companyCode: this.saveParams.companyCode,
          //   systemId: this.saveParams.systemId,
          //   templateId: this.saveParams.templateId,
          // })
          // if (flag) {
          //   this.$message.error('当前发票模版已存在')
          //   return
          // }
          const saveApi = this.saveParams.id
            ? templateConfigUpdate
            : templateConfigCreate;

          saveApi({
            ...this.saveParams,
          }).then(res => {
            this.$message.success(this.saveParams.id ? '修改成功' : '添加成功');
            this.showDialog = false;
            this.$emit('onGet');
          });
        });
      }, 800),
    },
  };
</script>

<style></style>
