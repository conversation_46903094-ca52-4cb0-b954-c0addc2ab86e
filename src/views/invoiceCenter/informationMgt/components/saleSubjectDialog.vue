<!--
 * @Description: 
 * @Author: 刘攀
 * @Date: 2021-07-06 14:03:13
 * @LastEditTime: 2023-01-04 14:12:04
 * @LastEditors: dddd
 * @Reference: 
-->
<template>
  <div class="saleSubjectDialog">
    <el-form
      ref="saveParams"
      :model="saveParams"
      :rules="rulesForm"
      label-width="140px"
    >
      <el-form-item class="subTitle" label="选择规则因素："></el-form-item>
      <el-form-item label="渠道名称" prop="channelCode">
        <el-select
          :value="saveParams.channelCode"
          placeholder="请选择渠道名称"
          filterable
          :disabled="
            saveParams.status && saveParams.status !== 0 ? true : false
          "
        >
          <el-option
            v-for="(item, index) in selectOptions.channelList"
            :key="index"
            :label="item.channelName"
            :value="item.channelCode"
            @click.native="handleSelectChannel(item)"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="店铺名称" prop="shopCode">
        <el-select
          v-model="saveParams.shopCode"
          placeholder="请选择店铺名称"
          :disabled="
            saveParams.status && saveParams.status !== 0 ? true : false
          "
          clearable
        >
          <el-option
            v-for="(item, index) in selectOptions.storeList"
            :key="index"
            :label="item.storeName"
            :value="item.storeCode"
          ></el-option>
        </el-select>
      </el-form-item>

      <div v-if="saveParams.channelType === 2">
        <el-form-item label="仓库名称" prop="depotId">
          <el-select
            :value="saveParams.channelType === 2 ? saveParams.depotId : ''"
            placeholder="请选择仓库名称"
            :disabled="
              saveParams.status && saveParams.status !== 0 ? true : false
            "
          >
            <el-option
              v-for="(item, index) in selectOptions.deptList"
              :key="index"
              :label="item.name"
              :value="item.id"
              @click.native="handleSelectDept(item, saveParams.depotId)"
            ></el-option>
          </el-select>
        </el-form-item>
      </div>

      <el-form-item label="目的国" prop="countryIdList">
        <el-select
          v-model="saveParams.countryIdList"
          placeholder="请选择目的国名称"
          multiple
          :disabled="
            saveParams.status && saveParams.status !== 0 ? true : false
          "
        >
          <el-option
            v-for="(item, index) in uniqueArrObj(
              selectOptions.nationList,
              'id',
            )"
            :key="index"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="saveParams.channelType === 2"
        label="下单用户等级"
        prop="userLevelList"
      >
        <el-select
          v-model="saveParams.userLevelList"
          placeholder="请选择用户等级"
          multiple
          :disabled="
            saveParams.status && saveParams.status !== 0 ? true : false
          "
        >
          <el-option
            v-for="(item, index) in selectOptions.userLevelList"
            :key="index"
            :label="item.name"
            :value="item.level"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="生效开始时间" prop="beginTime">
        <el-date-picker
          v-model="saveParams.beginTime"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          placeholder="选择生效时间"
          :disabled="
            saveParams.status && saveParams.status !== 0 ? true : false
          "
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="失效时间" prop="endTime" class="timeItem">
        <el-date-picker
          v-model="saveParams.endTime"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          placeholder="选择失效时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item class="subTitle" label="确认销售主体："></el-form-item>
      <el-form-item label="销售主体" prop="saleSubjectCode">
        <el-select
          v-model="saveParams.saleSubjectCode"
          placeholder="请选择销售主体"
          :disabled="
            saveParams.status && saveParams.status !== 0 ? true : false
          "
          size="medium"
        >
          <el-option
            v-for="(item, index) in selectOptions.mapSaleList"
            :key="index"
            :label="item.name"
            :value="item.entityCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item style="margin-left: 20px">
        <el-button @click="resetForm">取消</el-button>
        <el-button type="primary" @click="saveForm('saveParams')">
          保存
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import { tenBitTimestamp, uniqueArrObj } from '@/utils';
  export default {
    props: {
      formData: {
        type: Object,
        default: () => {
          return {};
        },
      },
      selectOptions: {
        type: Object,
        default: () => {
          return {};
        },
      },
    },
    data() {
      //这里是自定义的开始时间规则
      var begindateRule = (rule, value, callback) => {
        if (!this.saveParams.status || this.saveParams.status == 0) {
          if (!value || Date.parse(value) > Date.parse(tenBitTimestamp())) {
            callback();
          } else {
            return callback(new Error('开始时间必须大于当前时间'));
          }
        } else {
          callback();
        }
      };
      //这里是自定义的失效时间规则
      var enddateRule = (rule, value, callback) => {
        if (this.saveParams.beginTime && value) {
          if (Date.parse(this.saveParams.beginTime) < Date.parse(value)) {
            callback();
          } else {
            return callback(new Error('失效时间必须大于开始时间'));
          }
        } else {
          callback();
        }
      };
      return {
        saveParams: this.formData,
        rulesForm: {
          channelCode: [
            { required: true, message: '渠道名称不能为空', trigger: 'change' },
          ],
          depotId: [
            { required: true, message: '仓库名称不能为空', trigger: 'blur' },
          ],
          countryIdList: [
            { required: true, message: '目的国不能为空', trigger: 'change' },
          ],
          shopCode: [
            { required: true, message: '店铺不能为空', trigger: 'change' },
          ],
          userLevelList: [
            {
              required: true,
              message: '下单用户等级不能为空',
              trigger: 'change',
            },
          ],
          saleSubjectCode: [
            { required: true, message: '销售主体不能为空', trigger: 'change' },
          ],
          // beginTime: [
          //   { required: true, validator: begindateRule, trigger: 'blur' },
          // ],
          endTime: [
            { required: true, validator: enddateRule, trigger: 'blur' },
          ],
        },
      };
    },

    mounted() {
      this.$nextTick(() => {
        this.$on('initData', e => {
          this.$refs.saveParams.resetFields();

          this.saveParams = e;
        });
      });
    },
    methods: {
      uniqueArrObj,
      async handleSelectDept(val) {
        console.log(val, 'mmwwm');
        if (this.saveParams.depotId != val.id) {
          this.saveParams.depotId = val.id;
          this.saveParams.countryIdList = [];
        }
        this.$emit('handleSelectDept', val);
      },
      async handleSelectChannel(val) {
        if (this.saveParams.channelCode != val.channelCode) {
          this.saveParams.channelCode = val.channelCode;
          this.saveParams.shopCode = '';
          this.saveParams.channelType = val.channelType;
          this.saveParams.depotId = '';
          console.log(this.saveParams.depotId, 'this.saveParams.depotId');
          this.$store.dispatch('selectOption/resetNationListData');
          this.saveParams.countryIdList = [];
          if (val.channelType === 1) {
            const depotCodeList = this.selectOptions.deptList.map(
              item => item.depotCode,
            );
            this.$emit('handleSelectDept', depotCodeList);
          }
        }
        this.$emit('handleSelectChannel', val, () => {});
      },
      saveForm(formName) {
        this.$refs[formName].validate(valid => {
          if (valid) {
            this.$emit('saveForm', JSON.parse(JSON.stringify(this.saveParams)));
          }
        });
      },
      resetForm() {
        this.$emit('close');
      },
    },
  };
</script>

<style lang="scss" scoped>
  .saleSubjectDialog {
    .subTitle {
      font-weight: bold;
      font-size: 16px !important;
    }
    .timeItem {
      /deep/.el-form-item__label:before {
        content: '' !important;
      }
    }
    .el-select,
    .el-date-editor {
      width: 280px;
    }
  }
</style>
>
