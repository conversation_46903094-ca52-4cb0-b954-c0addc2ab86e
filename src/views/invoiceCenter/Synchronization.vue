<!--
 * @Author: bruce
 * @Date: 2023-08-25 14:56:06
 * @LastEditors: bruce
 * @FilePath: /access-fmis-web/src/views/invoiceCenter/Synchronization.vue
 * @Description: 
-->
<template>
  <div style="display: flex">
    <el-button type="success" style="margin-right: 10px" @click="tempDownload">
      模版下载
    </el-button>
    <uploadFile
      :accept="accept"
      :upload-url="uploadUrl"
      btn-text="商品数据导入"
      @onSuccess="onSuccess"
      @beforeUpload="beforeUpload"
    ></uploadFile>
  </div>
</template>

<script>
  import uploadFile from '@/components/uoloadFile';
  export default {
    name: 'Synchronization',
    components: {
      uploadFile,
    },
    data() {
      return {
        uploadUrl: '/api/bill/goods/sync/importOrderDataNew',
        accept: '.xlsx, .xls, .xltx',
      };
    },
    methods: {
      tempDownload() {
        window.open(
          'https://zkt-pro-bucket.obs.cn-east-3.myhuaweicloud.com/business/tmplate_goods_sync.xlsx',
        );
      },
      onSuccess(e) {
        if (!e.success) {
          this.$message.error(e.msg || '导入失败');

          return;
        }
        this.$message.success('导入成功');
      },
      beforeUpload() {},
    },
  };
</script>
