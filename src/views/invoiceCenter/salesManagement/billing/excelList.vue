<template>
  <div>
    <el-form inline>
      <el-form-item label="操作人:">
        <el-input v-model="searchParams.name" placeholder="请输入操作人" />
      </el-form-item>
      <el-form-item label="创建时间:">
        <el-date-picker
          v-model="creationTime"
          :popper-class="'currentDatePickerClass'"
          :clearable="false"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="贸易方式:">
        <el-select v-model="searchParams.businessType" placeholder="请选择">
          <el-option
            v-for="item in INVOICE_TYPES"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          :disabled="scope.row.status !== 2"
          type="text"
          size="mini"
          btn-text="下载"
          permission-key="excelList-download"
          @click="downloadExcel(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
</template>

<script>
  import { INVOICE_TYPES } from '@/consts';
  import dynamictable from '@/components/dynamic-table';
  import { excelToDownload, excelListForBack } from '@/api/invoiceCenter';
  import { parseTime, downloadExcel, initSearchParams } from '@/utils';
  export default {
    components: {
      dynamictable,
    },
    data() {
      const statusArr = ['未运行', '执行中', '执行成功', '执行失败'];
      return {
        INVOICE_TYPES,
        creationTime: '',
        searchParams: {
          createdTimeEnd: undefined,
          businessType: 0,
          createdTimeStart: undefined,
          name: undefined,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
        list: [],
        columns: [
          {
            prop: 'id',
            label: '编号',
          },
          {
            prop: 'status',
            label: '执行情况',
            render: row => <span>{statusArr[row.status]}</span>,
          },
          {
            prop: 'name',
            label: '操作人',
          },
          {
            prop: 'isDown',
            label: '状态',
            render: row => (
              <span>{row.isDown === 0 ? '未下载' : '已下载'}</span>
            ),
          },
          {
            prop: 'createdAt',
            label: '创建时间',
          },
          {
            prop: 'updatedAt',
            label: '完成时间',
          },
          {
            prop: 'operation',
            label: '操作',
            scopedSlots: { customRender: 'operation' },
          },
        ],
      };
    },
    created() {
      this.setData(() => {
        this.getList();
      });
    },
    methods: {
      setData(cb) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
        this.creationTime = [
          parseTime(start, '{y}-{m}-{d} 00:00:00'),
          parseTime(end, '{y}-{m}-{d} 23:59:59'),
        ];
        cb && cb();
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.setData();
        this.getList();
      },
      async getList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const { searchParams, pagination, creationTime } = this;
        searchParams.createdTimeStart = creationTime
          ? parseTime(creationTime[0])
          : '';
        searchParams.createdTimeEnd = creationTime
          ? parseTime(creationTime[1])
          : '';
        const params = {
          ...searchParams,
          pageNo: pagination.pageSize,
          limit: pagination.pageLimit,
        };
        this.options.loading = true;
        const res = await excelListForBack(initSearchParams(params));
        if (res) {
          this.list = res.list;
          this.pagination.total = res.total;
        }
        this.options.loading = false;
      },
      async downloadExcel({ id, link }) {
        console.log(
          window.location.protocol,
          link,
          link.replace(/^http[s]?:\/\//, window.location.protocol + '//'),
          'window.location.protocol',
        );
        window.location.href = link.replace(
          /^http[s]?:\/\//,
          window.location.protocol + '//',
        );
        await excelToDownload({ id });
        // window.open(link)
        // downloadExcel(link)
      },
    },
  };
</script>

<style>
  .currentDatePickerClass
    > .el-picker-panel__footer
    > .el-button--text:first-child {
    display: none;
  }
</style>
