export const isRequire = (res = {}, obj = {}) => {
  console.log(res, ';;;;;;');
  let initData = {
    type: true,
    orderIdList: true,
    invoiceMoney: true,
    headerType: true,
    invoiceType: true,
    invoiceHeader: true,
    taxNumber: true,
    companyAddress: true,
    companyTel: true,
    companyBankName: true,
    companyBankNo: true,
    receiveTel: true,
    companyMailbox: true,
    receiveAddress: true,
    receiveName: true,
    remarks: true,
  };
  let require;
  if (obj.businessType !== 0) {
    require = { ...initData };
  }
  if (obj.businessType === 0 && res.headerType === 0) {
    require = {
      ...initData,
      taxNumber: false,
      companyAddress: false,
      companyTel: false,
      companyBankName: false,
      companyBankNo: false,
      companyMailbox: false,
      receiveAddress: false,
      receiveName: false,
      remarks: false,
    };
  }

  if (obj.businessType === 0 && res.headerType === 1) {
    require = {
      ...initData,
      companyMailbox: false,
      receiveAddress: false,
      receiveName: false,
      remarks: false,
      companyAddress: [3, 1].includes(res.invoiceType) ? false : true,
      companyTel: [3, 1].includes(res.invoiceType) ? false : true,
      companyBankName: [3, 1].includes(res.invoiceType) ? false : true,
      companyBankNo: [3, 1].includes(res.invoiceType) ? false : true,
    };
    if (res.invoiceType === 2) {
      require = {
        ...initData,
        remarks: false,
      };
    }
  }
  return {
    require,
  };
};
