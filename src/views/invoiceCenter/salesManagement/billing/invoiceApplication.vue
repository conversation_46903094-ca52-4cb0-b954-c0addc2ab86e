<template>
  <div>
    <search-params
      :search-params="searchParams"
      :route-type="routeType"
      @handleCancelApply="handleCancelApply"
      @handleApply="handleApply"
      @search="getList(true)"
      @reset="onReset"
      @onImport="onImport"
    ></search-params>
    <div style="margin-top: 10px">
      <billing-table
        :loading="loading"
        :list="list"
        :pagination="pagination"
        :route-type="routeType"
        @handlePaginationChange="handlePaginationChange"
        @handleSelectionChange="handleSelectionChange"
        @handleEdit="handleEdit"
        @handleReject="handleReject"
        @handleReopenInvoice="handleReopenInvoice"
        @onGet="getList"
        @jump="jump"
      ></billing-table>
    </div>
    <new-invoice-dialog
      ref="invoiceDialog"
      @onGet="getList"
    ></new-invoice-dialog>
    <el-dialog
      width="400px"
      :title="getDialogTitle()"
      :visible.sync="visible"
      @closed="onClose"
    >
      <el-form ref="formData" :model="saveParams">
        <span v-if="type === 'invalid' || type === 'red'" style="color: red">请谨慎操作！</span>
        <el-form-item
          prop="reason"
          :rules="[{ required: true, message: '请输入', trigger: 'change' }]"
          :label="getReasonLabel()"
        >
          <el-input
            v-model="saveParams.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" style="text-align: center" class="dialog-footer">
        <el-button type="primary" @click="onOK">
          {{ getConfirmButtonText() }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { parseTime, initSearchParams, paramObj } from '@/utils';
  import {
    getInvoiceList,
    batchAudit,
    batchCancel,
    singleInvoiceOperation,
    invoiceDetails,
    buildExcel,
  } from '@/api/invoiceCenter';
  import SearchParams from './components/searchParams';
  import BillingTable from './components/billingTable';
  import NewInvoiceDialog from './components/newInvoiceDialog';
  export default {
    components: {
      SearchParams,
      BillingTable,
      NewInvoiceDialog,
    },
    data() {
      return {
        visible: false,
        type: 'reject', // reject-驳回,invalid-作废,red-红冲
        info: {}, // 单条列表数据
        saveParams: {
          reason: '',
        },
        list: [],
        // 发票选择的 ids
        selectIds: [],
        loading: false,
        routeType: '', // routeType == 'invoiceReview' 发票审核模块
        // 查询
        searchParams: {
          id: undefined,
          applicant: undefined,
          buyerName: undefined,
          mergedNumber: undefined,
          orderSn: undefined,
          status: '',
          businessType: 0,
          origin: 1,
          userId: undefined,
          time: undefined,
        },
        // 分页
        pagination: {
          pageNo: 1,
          limit: 10,
          total: null,
        },
      };
    },
    watch: {
      $route: {
        handler(val, oldval) {
          this.routeType = val.name.trim();
        },
        deep: true,
        immediate: true,
      },
    },
    created() {
      this.setData(() => {
        this.getList();
      });
    },
    methods: {
      jump(val) {
        const { mergedNumber, businessType, orders = [] } = val;
        const businessNumber =
          Array.isArray(orders) && orders.length ? orders[0]['sn'] : '';
        const { time } = this.searchParams;
        this.$router.push({
          path:
            '/ticketTaxPlatform/invoiceCenter/salesManagement/salesInvoicePool',
          query: {
            businessNumber: mergedNumber ? mergedNumber : businessNumber,
            origin: businessType === 0 ? '0' : '1',
            startTime: time ? parseTime(time[0], '{y}-{m}-{d}') : '',
            endTime: time ? parseTime(time[1], '{y}-{m}-{d}') : '',
          },
        });
      },
      setData(cb) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
        this.searchParams.time = [
          parseTime(start, '{y}-{m}-{d} 00:00:00'),
          parseTime(end, '{y}-{m}-{d} 23:59:59'),
        ];
        cb && cb();
      },
      async getList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageNo = 1;
        }
        const { time, ...searchParams } = this.searchParams;
        const params = {
          ...searchParams,
          startTime: time ? parseTime(time[0]) : '',
          endTime: time ? parseTime(time[1]) : '',
          pageNo: this.pagination.pageNo,
          limit: this.pagination.limit,
        };
        this.loading = true;
        try {
          const res = await getInvoiceList(initSearchParams(params));
          if (res) {
            this.list = res.list;
            this.pagination.total = res.total;
          }
          this.loading = false;
        } catch (error) {
          this.list = [];
          this.pagination.total = null;
          this.loading = false;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.setData();
        this.getList();
      },
      handlePaginationChange(pageNo, limit) {
        this.pagination.pageNo = pageNo;
        this.pagination.limit = limit;
        this.getList();
      },
      handleSelectionChange(ids) {
        this.selectIds = ids;
      },
      handleApply() {
        this.$refs['invoiceDialog'].open(undefined, false);
      },
      // 导出
      async onImport() {
        const { time, ...searchParams } = this.searchParams;
        const params = {
          ...searchParams,
          startTime: time ? parseTime(time[0]) : '',
          endTime: time ? parseTime(time[1]) : '',
          pageNo: this.pagination.pageNo,
          limit: this.pagination.limit,
        };
        const res = await buildExcel(params);
        this.$message({
          message: '导出任务已经建立，请稍后去Excel导出页面中下载！',
          type: 'success',
        });
      },
      // 批量审核 与 批量取消
      async handleCancelApply(operationType) {
        const status = this.searchParams.status;
        if (this.selectIds.length === 0) {
          this.$message.error('请选择发票');
          return;
        }
        if (
          (operationType === 'cancel' && ![0, 1, 3].includes(status)) ||
          (operationType === 'approved' && status !== 3)
        ) {
          this.$message.error(
            '你选择了不可执行该操作的记录，请检查后重新选择再操作！',
          );
          return;
        }
        const api = operationType === 'cancel' ? batchCancel : batchAudit;
        const res = await api({
          idList: this.selectIds,
          origin: this.searchParams.origin,
        });
        this.getList();
        this.$message({
          message:
            operationType === 'approved'
              ? '发票批量审核通过'
              : '发票批量取消成功',
          type: 'success',
        });
      },
      handleEdit({ id }, noEdit = false) {
        this.$refs['invoiceDialog'].open(id, noEdit);
      },
      handleReopenInvoice(row) {
        this.$refs['invoiceDialog'].open(row.id, false, 'REOPEN');
      },
      handleReject(val, key) {
        this.visible = true;
        this.type = key;
        this.info = val;
      },
      onOK() {
        this.$refs.formData.validate(valid => {
          if (!valid) return;
          const { id, origin } = this.info;
          const params = {
            id,
            operationType: this.type,
            origin,
            ...this.saveParams,
          };
          singleInvoiceOperation(params).then(res => {
            this.getList();
            this.$message.success('操作成功');
            this.visible = false;
          });
        });
      },
      onClose() {
        Object.assign(this.$data.saveParams, this.$options.data().saveParams);
        this.$nextTick(function () {
          this.$refs.formData.clearValidate();
        });
      },
      getDialogTitle() {
        switch (this.type) {
          case 'reject':
            return '驳回原因';
          case 'invalid':
            return '作废原因';
          case 'red':
            return '红冲原因';
          default:
            return '原因';
        }
      },
      getReasonLabel() {
        switch (this.type) {
          case 'reject':
            return '驳回原因';
          case 'invalid':
            return '作废原因';
          case 'red':
            return '红冲原因';
          default:
            return '原因';
        }
      },
      getConfirmButtonText() {
        switch (this.type) {
          case 'reject':
            return '确认驳回';
          case 'invalid':
            return '确认作废';
          case 'red':
            return '确认红冲';
          default:
            return '确认';
        }
      },
    },
  };
</script>

<style></style>
