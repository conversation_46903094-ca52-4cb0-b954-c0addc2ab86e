<template>
  <div>
    <el-table
      v-loading="loading"
      border
      :data="list"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column prop="id" label="编号"></el-table-column>
      <el-table-column prop="orders" label="订单信息" type="expand">
        <template slot-scope="props">
          <el-table
            class="table-in-table"
            :data="props.row.orders"
            row-key="id"
            border
            max-height="220"
            style="width: 50%"
          >
            <el-table-column
              width="100"
              prop="orderId"
              label="订单ID"
            ></el-table-column>
            <el-table-column
              width="200"
              prop="sn"
              label="订单编号"
            ></el-table-column>
            <el-table-column
              width="140"
              prop="barCode"
              label="商品条码"
            ></el-table-column>
            <el-table-column
              width="200"
              prop="name"
              label="商品名称"
            ></el-table-column>
            <el-table-column prop="count" label="数量"></el-table-column>
            <el-table-column
              prop="taxUnitPrice"
              label="含税单价"
            ></el-table-column>
            <el-table-column
              prop="taxAmount"
              label="含税金额"
            ></el-table-column>
            <el-table-column prop="orderStatus" label="订单状态">
              <template slot-scope="{ row }">
                {{ orderStatusArr[row.orderStatus] }}
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>
      <el-table-column
        :resizable="false"
        width="80px"
        prop="mergedNumber"
        label="合并单号"
      ></el-table-column>
      <el-table-column prop="remark" label="备注"></el-table-column>
      <el-table-column prop="invoiceType" label="发票类型">
        <template slot-scope="{ row }">
          <h3>{{ invoiceTypeArr[row.invoiceType - 1] }}</h3>
        </template>
      </el-table-column>

      <el-table-column prop="sellerName" label="销方名称"></el-table-column>
      <el-table-column prop="headerType" label="抬头类型">
        <template slot-scope="{ row }">
          {{ row.headerType === 0 ? '个人及政府事业单位' : '企业' }}
        </template>
      </el-table-column>
      <el-table-column prop="buyerName" label="购方名称"></el-table-column>
      <el-table-column prop="userId" label="用户ID_Code"></el-table-column>

      <el-table-column prop="businessType" label="贸易方式">
        <template slot-scope="{ row }">
          {{ businessArr[row.businessType] }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="invoiceNo" label="发票号码">
        <template slot-scope="{ row }">
          <a style="cursor: pointer" @click="getFileUrl(row.fileUrl)">
            {{ row.invoiceNo }}
          </a>
        </template>
      </el-table-column> -->
      <el-table-column prop="invoiceAmount" label="发票金额"></el-table-column>

      <el-table-column prop="status" label="状态">
        <template slot-scope="{ row }">
          {{ statusArr[row.status] }}
        </template>
      </el-table-column>
      <el-table-column prop="origin" label="申请来源">
        <template slot-scope="{ row }">
          {{ row.origin === 0 ? 'VTNAPP' : '管理后台' }}
        </template>
      </el-table-column>
      <el-table-column prop="appliedTime" label="申请时间">
        <template slot-scope="{ row }">
          {{ row.appliedTime ? parseTime(row.appliedTime) : '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="completedTime" label="完成时间">
        <template slot-scope="{ row }">
          {{ row.completedTime ? parseTime(row.completedTime) : '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="proposer" label="申请人"></el-table-column>

      <el-table-column prop="reviewer" label="审核人"></el-table-column>

      <el-table-column prop="invalidReason" label="作废原因"></el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        :resizable="false"
        width="150"
      >
        <template slot-scope="scope">
          <div></div>
          <div v-if="isInvoiceReview()">
            <ac-permission-button
              :disabled="isDisabled(scope.row)"
              type="text"
              size="small"
              btn-text="驳回"
              permission-key="invoiceReview-reject"
              @click="handleReject(scope.row, 'reject')"
            ></ac-permission-button>

            <ac-permission-button
              :disabled="isDisabled(scope.row)"
              type="text"
              size="small"
              btn-text="审核通过"
              permission-key="invoiceReview-examine"
              @click="handleOperation(scope.row, 'approv')"
            ></ac-permission-button>
            <ac-permission-button
              v-if="scope.row.businessType !== 0"
              node-type="popconfirmBtn"
              title="请再次确认，谨慎操作！"
              slot-btn="reference"
              :disabled="scope.row.status !== 2"
              type="text"
              size="small"
              btn-text="作废"
              permission-key="invoiceReview-void"
              @click="handleReject(scope.row, 'invalid')"
            ></ac-permission-button>
            <el-button
              :disabled="scope.row.origin === 0"
              type="text"
              size="small"
              @click="handleEdit(scope.row, true)"
            >
              详情
            </el-button>
          </div>
          <div v-if="routeType !== 'invoiceReview'">
            <ac-permission-button
              :disabled="isDisabled(scope.row)"
              type="text"
              btn-text="撤回"
              permission-key="invoiceApplication-withdraw"
              size="small"
              @click="handleOperation(scope.row, 'withdraw')"
            ></ac-permission-button>
            <ac-permission-button
              :disabled="![0, 1].includes(scope.row.status)"
              type="text"
              btn-text="修改"
              permission-key="invoiceApplication-edit"
              size="small"
              @click="handleEdit(scope.row, false)"
            ></ac-permission-button>
            <el-button
              :disabled="scope.row.origin === 0"
              type="text"
              size="small"
              @click="handleEdit(scope.row, true)"
            >
              详情
            </el-button>
            <ac-permission-button
              :disabled="![0, 1, 3].includes(scope.row.status)"
              type="text"
              size="small"
              btn-text="取消申请"
              permission-key="invoiceApplication-cancel-apply"
              @click="handleOperation(scope.row, 'cancel')"
            ></ac-permission-button>
          </div>
          <div>
            <ac-permission-button
              :disabled="scope.row.status !== 2"
              type="text"
              size="small"
              btn-text="查看发票"
              permission-key=""
              @click="jump(scope.row)"
            ></ac-permission-button>
            <ac-permission-button
              v-if="scope.row.invoiceType === 5 && scope.row.status === 2"
              type="text"
              size="small"
              btn-text="红冲"
              permission-key="invoiceReview-invalidate"
              @click="handleReject(scope.row, 'red')"
            ></ac-permission-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      ref="listPage"
      background
      :current-page.sync="pagination.pageNo"
      :page-size.sync="pagination.limit"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      @current-change="pagination_currentChange"
      @size-change="pagination_sizeChange"
    ></el-pagination>
  </div>
</template>

<script>
  import {
    singleInvoiceOperation,
    invoiceRevoke,
    invoicecancel,
    getFileUrl,
  } from '@/api/invoiceCenter';
  import { INVOICE_STATUS, INVOICE_TYPES } from '@/consts';
  import { parseTime, paramObj } from '@/utils';
  export default {
    props: {
      pagination: {
        type: Object,
        default: null,
      },
      list: {
        type: Array,
        default: null,
      },
      loading: {
        type: Boolean,
        default: false,
      },
      routeType: {
        type: String,
        default: '',
      },
    },
    data() {
      console.log(this.routeType, 'oooooo');
      return {
        INVOICE_STATUS,
        INVOICE_TYPES,
        businessArr: [
          '国内一般贸易(人民币)',
          '澳洲一般贸易',
          '新西兰一般贸易(澳币)',
          '新西兰一般贸易(新西兰币)',
        ],
        invoiceTypeArr: [
          '增值税普通发票',
          '增值税专用发票',
          '增值税电子普通发票',
          '增值税电子专用发票',
          '形式发票',
        ],
        statusArr: ['已撤回', '已驳回', '已开票', '待开票', '已取消', '已作废'],
        orderStatusArr: [
          '待付款',
          '已完成',
          '待发货',
          '待收货',
          '已签收',
          '已超时关闭',
        ],
      };
    },
    methods: {
      parseTime,
      isDisabled(row) {
        return row.status !== 3 || row.origin === 0;
      },
      isInvoiceReview() {
        return this.routeType === 'invoiceReview';
      },
      pagination_currentChange(val) {
        this.$emit('handlePaginationChange', val, this.pagination.limit);
      },
      pagination_sizeChange(val) {
        this.$emit('handlePaginationChange', this.pagination.pageNo, val);
      },
      handleSelectionChange(val) {
        const ids = val.map(item => item.id);
        this.$emit('handleSelectionChange', ids);
      },
      handleReject(val, type) {
        this.$emit('handleReject', val, type);
      },
      handleEdit(val, noEdit) {
        this.$emit('handleEdit', val, noEdit);
      },
      // 操作
      handleOperation({ origin, id }, operationType) {
        let params = {
          id,
          origin,
        };
        if (operationType === 'approv') {
          params.operationType = operationType;
          singleInvoiceOperation(params).then(res => {
            this.$message.success('操作成功');
            this.$emit('onGet');
          });
        } else {
          const save =
            operationType === 'withdraw' ? invoiceRevoke : invoicecancel;
          save(params).then(res => {
            this.$message.success('操作成功');
            this.$emit('onGet');
          });
        }
      },
      getFileUrl(url) {
        getFileUrl(url).then(res => {
          if (res) {
            window.open(res);
          }
        });
      },
      jump(row) {
        this.$emit('jump', row);
      },
      handleReopenInvoice(row) {
        this.$emit('handleReopenInvoice', row);
      },
    },
  };
</script>

<style></style>
