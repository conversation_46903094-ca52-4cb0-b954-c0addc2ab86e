<template>
  <div class="searchParams">
    <el-form ref="search" inline :model="params">
      <el-form-item
        label="编号:"
        prop="id"
        :rules="[
          {
            required: false,
            message: '请输入',
            trigger: 'change',
          },
          {
            validator: rules.validateNum,
            trigger: 'change',
          },
        ]"
      >
        <el-input v-model="params.id" placeholder="请输入编号" />
      </el-form-item>
      <el-form-item label="订单编号:">
        <el-input v-model="params.orderSn" placeholder="订单编号以W开头" />
      </el-form-item>
      <el-form-item label="合并单号:">
        <el-input
          v-model="params.mergedNumber"
          placeholder="合并单号以INO-开头"
        />
      </el-form-item>
      <el-form-item label="贸易方式:">
        <el-select
          v-model="params.businessType"
          placeholder="请选择"
          @change="handleBusinessTypeSelect"
        >
          <el-option
            v-for="item in INVOICE_TYPES"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="routeType !== 'invoiceReview'" label="购方名称:">
        <el-input v-model="params.buyerName" placeholder="请输入购方名称" />
      </el-form-item>
      <el-form-item
        label="用户ID_Code:"
        prop="userId"
        :rules="[
          {
            required: false,
            message: '请输入',
            trigger: 'change',
          },
          {
            validator: rules.validateNum,
            trigger: 'change',
          },
        ]"
      >
        <el-input v-model="params.userId" placeholder="请输入用户ID_Code" />
      </el-form-item>
      <el-form-item v-if="params.origin === 1" label="申请人:">
        <el-input v-model="params.applicant" placeholder="请输入申请人" />
      </el-form-item>
      <el-form-item label="申请来源:">
        <el-select
          v-model="params.origin"
          placeholder="请选择"
          @change="handleSelectOrigin"
        >
          <el-option
            v-if="params.businessType !== 0 && routeType === 'invoiceReview'"
            label="全部"
            value=""
          ></el-option>
          <el-option label="VTNAPP" :value="0"></el-option>
          <el-option label="管理后台" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态:">
        <el-select v-model="params.status" placeholder="请选择">
          <el-option
            v-for="item in INVOICE_STATUS"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间:">
        <el-date-picker
          v-model="params.time"
          :popper-class="'currentDatePickerClass'"
          :clearable="false"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
    </el-form>
    <div>
      <el-button type="primary" @click="onSearch">查询</el-button>
      <el-button type="primary" @click="onReset">重置</el-button>
      <template v-if="routeType !== 'invoiceReview'">
        <ac-permission-button
          btn-text="申请发票"
          type="primary"
          permission-key="invoiceApplication-add"
          @click="handleApply"
        ></ac-permission-button>
      </template>
      <ac-permission-button
        :btn-text="routeType === 'invoiceReview' ? '批量审核通过' : '批量取消'"
        type="primary"
        :permission-key="
          routeType === 'invoiceReview'
            ? 'invoiceReview-batch-examine'
            : 'invoiceApplication-batch-cancel'
        "
        @click="handleCancelApply"
      ></ac-permission-button>
      <ac-permission-button
        v-if="routeType === 'invoiceReview'"
        btn-text="导出"
        type="primary"
        permission-key="invoiceReview-export"
        @click="onImport"
      ></ac-permission-button>
    </div>
  </div>
</template>

<script>
  import { INVOICE_STATUS, INVOICE_TYPES } from '@/consts';
  import { btnPermission } from '@/utils/permission';
  export default {
    props: {
      searchParams: {
        type: Object,
        default: null,
      },
      // routeType === invoiceReview为发票审核页面
      routeType: {
        type: String,
        default: null,
      },
    },
    data() {
      const validateNum = (rule, value, callback) => {
        const reg = /^[0-9]*$/g;
        if (!reg.test(value) && value) {
          callback(new Error('只能输入数字'));
        } else {
          callback();
        }
      };
      return {
        INVOICE_STATUS,
        INVOICE_TYPES,
        selectDate: '',
        rules: {
          validateNum,
        },
        params: this.searchParams,
      };
    },
    methods: {
      /**
       * 申请来源下拉
       * @val 0: VTNapp, 1: 管理后台 , '': 全部
       */
      handleSelectOrigin(val) {
        this.params.applicant = '';
        // 申请来源为VTNapp 贸易方式为国内的 状态搜索 3待开票 4 已取消
        if (val === 0) {
          this.params.status = 3;
          if (this.params.businessType === 0) {
            this.INVOICE_STATUS = this.getStatus([2, 3, 4]);
          } else {
            this.INVOICE_STATUS = this.getStatus([1, 2, 3, 5]);
          }
        } else if (val === '') {
          // 申请来源为全部
          this.params.status = 2;
          this.INVOICE_STATUS = this.getStatus([2, 5]);
        } else {
          // 申请来源为管理后台
          this.params.status = '';
          this.INVOICE_STATUS = INVOICE_STATUS;
        }
      },
      /**
       * 贸易方式下拉
       * @val 0: 国内, 非0：国外
       */
      handleBusinessTypeSelect(val) {
        if (
          val === 0 &&
          this.params.origin === '' &&
          this.routeType === 'invoiceReview'
        ) {
          this.params.origin = 1;
          this.params.status = '';
          this.INVOICE_STATUS = INVOICE_STATUS;
        }
        // 申请来源为VTNapp 贸易方式为国内的 状态搜索 3待开票
        if (val === 0 && this.params.origin === 0) {
          this.params.status = 3;
          this.INVOICE_STATUS = this.getStatus([2, 3, 4]);
        }

        // 申请来源为VTNapp 贸易方式为国外的 状态搜索 3待开票
        if (val !== 0 && this.params.origin === 0) {
          this.params.status = 3;
          this.INVOICE_STATUS = this.getStatus([2, 3]);
        }
      },
      getStatus(list) {
        return INVOICE_STATUS.map(item => {
          if (list.includes(item.value)) {
            return item;
          }
        }).filter(i => !!i);
      },
      handleApply() {
        this.$emit('handleApply');
      },
      handleCancelApply() {
        this.$emit(
          'handleCancelApply',
          this.routeType === 'invoiceReview' ? 'approved' : 'cancel',
        );
      },
      onSearch() {
        this.$refs.search.validate(async valid => {
          if (!valid) return;
          this.$emit('search');
        });
      },
      onReset() {
        this.$emit('reset');
      },
      onImport() {
        this.$emit('onImport');
      },
    },
  };
</script>

<style lang="scss">
  .currentDatePickerClass
    > .el-picker-panel__footer
    > .el-button--text:first-child {
    display: none;
  }
</style>
