<template>
  <el-dialog
    class="new-invoice-dialog"
    width="800px"
    :title="active === '1' ? '申请发票' : '申请信息'"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @closed="onClose"
  >
    <section v-if="active === '1'">
      <el-form ref="formSearch" inline :model="params">
        <el-form-item
          prop="mobile"
          :rules="[
            {
              validator: rules.validateMobile,
              trigger: 'change',
            },
          ]"
          label="用户手机号:"
        >
          <el-input v-model="params.mobile" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="用户ID:">
          <el-input v-model="params.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="订单号(批量):">
          <el-input
            v-model="params.orderSns"
            type="textarea"
            :rows="3"
            placeholder="请输入订单号，多个订单号请用回车键分割"
            @keyup.enter.native="handleOrderIdsInput"
          />
        </el-form-item>
        <el-form-item label="贸易方式:">
          <el-select v-model="params.businessType" placeholder="请选择">
            <el-option
              v-for="item in INVOICE_TYPES"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="next">下一步</el-button>
        </el-form-item>
      </el-form>
      <dynamictable
        :data-source="list"
        :columns="columns"
        :options="options"
        :pagination="pagination"
        :fetch="getList"
        @selection-change="handleSelectionChange"
      ></dynamictable>
    </section>
    <section v-if="active === '2'">
      <el-form
        ref="formData"
        style="width: 540px"
        label-width="260px"
        :model="saveParams"
      >
        <el-form-item label-width="160px" label="">
          <h3>开票信息</h3>
        </el-form-item>
        <el-form-item label="贸易方式:">
          <el-input v-if="!noEdit" v-model="name" :disabled="true" />
          <span v-else>{{ name }}</span>
        </el-form-item>
        <el-form-item label="订单:">
          共计{{ saveParams.orderIdList.length }}笔订单
        </el-form-item>
        <el-form-item label="开票金额(含税):">
          {{ saveParams.invoiceMoney }}
        </el-form-item>
        <el-form-item label="抬头类型:">
          <el-radio-group
            v-if="!noEdit"
            v-model="saveParams.headerType"
            :disabled="noEdit"
          >
            <el-radio :label="0">
              {{ saveParams.type === 0 ? '个人及政府事业单位' : '个人' }}
            </el-radio>
            <el-radio :label="1">企业</el-radio>
          </el-radio-group>
          <span v-else>
            {{
              saveParams.headerType === 0
                ? saveParams.type === 0
                  ? '个人及政府事业单位'
                  : '个人'
                : '企业'
            }}
          </span>
        </el-form-item>
        <el-form-item label="发票类型:">
          <el-select
            v-if="!noEdit"
            v-model="saveParams.invoiceType"
            placeholder="请选择"
          >
            <el-option
              v-for="item in saveParams.type === 0
                ? INVOICE_BILLING_TYPES
                : dutyParagraphList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <span v-else>
            {{ getText(saveParams.invoiceType, INVOICE_BILLING_TYPES) }}
          </span>
        </el-form-item>
        <el-form-item
          prop="invoiceHeader"
          :rules="[
            {
              required: isRequireObj.invoiceHeader,
              message: '请输入',
              trigger: 'change',
            },
          ]"
          label="名称:"
        >
          <el-input
            v-if="!noEdit"
            v-model="saveParams.invoiceHeader"
            placeholder="请输入购方名称"
          />
          <span v-else>{{ saveParams.invoiceHeader }}</span>
        </el-form-item>
        <el-form-item
          prop="taxNumber"
          :rules="[
            {
              required:
                saveParams.type !== 0
                  ? saveParams.headerType === 1
                  : isRequireObj.taxNumber,
              message: '请输入',
              trigger: 'change',
            },
            {
              validator: saveParams.type !== 0 ? rules.dutyParagraph : '',
              trigger: 'change',
            },
          ]"
          :label="dutyParagraphArr[saveParams.type]"
        >
          <el-input
            v-if="!noEdit"
            v-model="saveParams.taxNumber"
            placeholder="请输入税号"
          />
          <span v-else>{{ saveParams.taxNumber }}</span>
        </el-form-item>
        <el-form-item
          v-if="saveParams.type === 0"
          prop="companyAddress"
          :rules="[
            {
              required: isRequireObj.companyAddress,
              message: '请输入',
              trigger: 'change',
            },
          ]"
          label="公司地址:"
        >
          <el-input
            v-if="!noEdit"
            v-model="saveParams.companyAddress"
            placeholder="请输入公司地址"
          />
          <span v-else>{{ saveParams.companyAddress }}</span>
        </el-form-item>
        <el-form-item
          v-if="saveParams.type === 0"
          prop="companyTel"
          :rules="[
            {
              required: isRequireObj.companyTel,
              message: '请输入',
              trigger: 'change',
            },
          ]"
          label="电话号码:"
        >
          <el-input
            v-if="!noEdit"
            v-model="saveParams.companyTel"
            placeholder="请输入电话号码"
          />
          <span v-else>{{ saveParams.companyTel }}</span>
        </el-form-item>
        <el-form-item
          v-if="saveParams.type === 0"
          prop="companyBankName"
          :rules="[
            {
              required: isRequireObj.companyBankName,
              message: '请输入',
              trigger: 'change',
            },
          ]"
          label="开户银行:"
        >
          <el-input
            v-if="!noEdit"
            v-model="saveParams.companyBankName"
            placeholder="请输入开户银行"
          />
          <span v-else>{{ saveParams.companyBankName }}</span>
        </el-form-item>
        <el-form-item
          v-if="saveParams.type === 0"
          prop="companyBankNo"
          :rules="[
            {
              required: isRequireObj.companyBankNo,
              message: '请输入',
              trigger: 'change',
            },
          ]"
          label="银行账户:"
        >
          <el-input
            v-if="!noEdit"
            v-model="saveParams.companyBankNo"
            placeholder="请输入银行账户"
          />
          <span v-else>{{ saveParams.companyBankNo }}</span>
        </el-form-item>
        <el-form-item v-if="saveParams.type === 0" label-width="160px" label="">
          <h3>收票信息</h3>
        </el-form-item>
        <el-form-item
          prop="receiveTel"
          :rules="[
            {
              required: saveParams.type === 0 && isRequireObj.receiveTel,
              message: '请输入',
              trigger: 'change',
            },
          ]"
          label="手机号:"
        >
          <el-input
            v-if="!noEdit"
            v-model="saveParams.receiveTel"
            placeholder="请输入手机号"
          />
          <span v-else>{{ saveParams.receiveTel }}</span>
        </el-form-item>
        <el-form-item
          prop="companyMailbox"
          :rules="[
            {
              required: saveParams.type === 0 && isRequireObj.companyMailbox,
              message: '请输入',
              trigger: 'change',
            },
          ]"
          label="邮箱:"
        >
          <el-input
            v-if="!noEdit"
            v-model="saveParams.companyMailbox"
            placeholder="请输入邮箱"
          />
          <span v-else>{{ saveParams.companyMailbox }}</span>
        </el-form-item>
        <el-form-item
          prop="receiveAddress"
          :rules="[
            {
              required: saveParams.type === 0 && isRequireObj.receiveAddress,
              message: '请输入',
              trigger: 'change',
            },
          ]"
          label="地址:"
        >
          <el-input
            v-if="!noEdit"
            v-model="saveParams.receiveAddress"
            placeholder="请输入收件人地址"
          />
          <span v-else>{{ saveParams.receiveAddress }}</span>
        </el-form-item>
        <el-form-item
          prop="receiveName"
          :rules="[
            {
              required: saveParams.type === 0 && isRequireObj.receiveName,
              message: '请输入',
              trigger: 'change',
            },
          ]"
          label="姓名:"
        >
          <el-input
            v-if="!noEdit"
            v-model="saveParams.receiveName"
            placeholder="请输入收件人姓名"
          />
          <span v-else>{{ saveParams.receiveName }}</span>
        </el-form-item>
        <el-form-item class="remarks" label-width="210px" label="备注:">
          <el-input
            v-if="!noEdit"
            v-model="saveParams.remarks"
            type="textarea"
            placeholder="请输入备注"
          />
          <span v-else>{{ saveParams.remarks }}</span>
        </el-form-item>
        <el-form-item v-if="!noEdit">
          <el-button @click="visible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit()">提交</el-button>
        </el-form-item>
      </el-form>
    </section>
  </el-dialog>
</template>

<script>
  import { initSearchParams } from '@/utils';
  import { INVOICE_BILLING_TYPES, INVOICE_TYPES } from '@/consts';
  import {
    invoiceOrdersList,
    generateInvoice,
    invoiceModify,
    invoiceDetails,
    getInvoiceStatus,
  } from '@/api/invoiceCenter';
  import { validatePhoneTwo, isPhone, isEmail } from '@/utils/validate';
  import { isRequire } from '../js/index';
  import dynamictable from '@/components/dynamic-table';

  export default {
    components: {
      dynamictable,
    },

    data() {
      const orderStatusArr = [
        '待付款',
        '已完成',
        '待发货',
        '待收货',
        '已签收',
        '已超时关闭',
      ];
      const validatePhone = (rule, value, callback) => {
        if (!isPhone(value) && value) {
          callback(new Error('手机号输入有误，请重新输'));
        } else {
          callback();
        }
      };
      const validatePhone1 = (rule, value, callback) => {
        const reg = /^((0\d{2,3}-\d{7,8})|(1[34578]\d{9}))$/;
        if (!reg.test(value) && value) {
          callback(new Error('电话号码输入有误，请重新输'));
        } else {
          callback();
        }
      };

      const validateAbroadPhone = (rule, value, callback) => {
        const reg = /^[0-9]*$/;
        if (!reg.test(value)) {
          callback(new Error('手机号输入有误，请重新输'));
        } else {
          callback();
        }
      };

      const validateEmail = (rule, value, callback) => {
        if (!isEmail(value) && value) {
          callback(new Error('邮箱输入有误，请重新输入'));
        } else {
          callback();
        }
      };
      const dutyParagraph = (rule, value, callback) => {
        const str = value.replace(/\s/g, '');
        const num = this.$data.saveParams.type == 1 ? 11 : 9;
        if (str.length !== num && value) {
          callback(new Error(`税号长度必须为${num}位`));
        } else {
          callback();
        }
      };
      const validateMobile = (rule, value, callback) => {
        // 去掉手机号必填校验
        callback();
      };
      

      return {
        INVOICE_BILLING_TYPES,
        INVOICE_TYPES,
        visible: false,
        btnLoading: false,
        list: [],
        name: '',
        isRequireObj: {},
        selectIds: [],
        id: '', // 详情id
        noEdit: false, // 是否编辑
        rules: {
          validateMobile,
          validatePhone,
          validatePhone1,
          validateEmail,
          dutyParagraph,
          validateAbroadPhone,
        },
        params: {
          businessType: 0,
          mobile: undefined,
          userId: undefined,
          orderSns: '',
        },
        // 表单数据
        saveParams: {
          type: '',
          orderIdList: [],
          invoiceMoney: '',
          headerType: 1,
          invoiceType: 3,
          invoiceHeader: '',
          taxNumber: '',
          companyAddress: '',
          companyTel: '',
          companyBankName: '',
          companyBankNo: '',
          receiveTel: '',
          companyMailbox: '',
          receiveAddress: '',
          receiveName: '',
          remarks: '',
          userMobile: '', // 用户手机号(搜索传入)
          sourceType: '', // 来源类型，REOPEN表示重新开票
        },
        userMobile: '', //用户手机号,
        active: '1',
        options: {
          loading: false,
          mutiSelect: true,
        },
        dutyParagraphList: [{ label: '形式发票', value: 5 }],
        dutyParagraphArr: ['税号', 'ABN号', 'GST号', 'GST号'],
        columns: [
          {
            prop: 'sn',
            label: '订单号',
          },
          {
            prop: 'newAmount',
            label: '订单金额',
          },
          {
            prop: 'status',
            label: '订单状态',
            render: row => <span>{orderStatusArr[row.status]}</span>,
          },
          {
            prop: 'createdAt',
            label: '下单时间',
          },
          {
            prop: 'acceptedAt',
            label: '签收时间',
          },
          {
            prop: 'completedAt',
            label: '完成时间',
          },
        ],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },
    computed: {
      listenChange() {
        const { headerType, invoiceType } = this.saveParams;
        const businessType = this.params.businessType;
        return { headerType, invoiceType, businessType };
      },
    },
    watch: {
      listenChange: {
        handler(newName, oldName) {
          this.isRequireObj = this.isRequire(
            this.saveParams,
            this.params,
          ).require;
        },
        deep: true,
        immediate: true,
      },
    },

    methods: {
      isRequire,
      getText(value, arr = []) {
        let text;
        arr.map(item => {
          if (item.value === value) {
            text = item.label;
          }
        });
        return text;
      },
      handleOrderIdsInput() {
        // 处理订单号输入，可以在这里添加验证逻辑
        const orderSns = this.params.orderSns.trim();
        if (orderSns) {
          // 可以在这里添加订单号格式验证
          console.log('输入的订单号:', orderSns);
        }
      },
      handleSearch() {
        // 手动验证至少填写一个字段
        const mobile = this.params.mobile ? this.params.mobile.trim() : '';
        const userId = this.params.userId ? this.params.userId.trim() : '';
        const orderSns = this.params.orderSns ? this.params.orderSns.trim() : '';
        
        if (!mobile && !userId && !orderSns) {
          this.$message.error('用户手机号、用户ID、订单号至少填写一个');
          return;
        }
        
        this.$refs.formSearch.validate(async valid => {
          if (!valid) return;
          this.getList(true);
        });
      },
      async getList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const { params, pagination } = this;
        const searchParams = {
          ...params,
          pageNo: pagination.pageSize,
          limit: pagination.pageLimit,
        };
        
        // 处理订单号参数，如果有批量订单号输入，则优先使用
        if (params.orderSns && params.orderSns.trim()) {
          const orderIdArray = params.orderSns.split('\n').filter(id => id.trim());
          searchParams.orderSns = orderIdArray;
        }
        
        this.options.loading = true;
        const res = await invoiceOrdersList(initSearchParams(searchParams));
        if (res) {
          this.userMobile = searchParams.mobile;
          this.list = res.list;
          this.pagination.total = res.total;
        } else {
          this.userMobile = '';
          this.list = [];
          this.pagination.total = null;
        }
        this.options.loading = false;
      },
      async open(id, noEdit, sourceType = '') {
        if (id) {
          this.active = '2';
          this.id = id;
          const res = await invoiceDetails({ id });
          Object.keys(this.saveParams).forEach(item => {
            this.saveParams[item] =
              item == 'taxNumber'
                ? res['taxNumber'] || res['abnNumber']
                : res[item];
          });

          this.INVOICE_TYPES.forEach(({ value, label }) => {
            if (value === this.saveParams.type) {
              this.name = label;
            }
          });
        }
        // 设置来源类型
        this.saveParams.sourceType = sourceType;
        this.noEdit = noEdit;
        this.visible = true;
      },
      onClose() {
        Object.assign(this.$data.saveParams, this.$options.data().saveParams);
        Object.assign(this.$data.params, this.$options.data().params);
        this.$nextTick(function () {
          if (this.active === '1') {
            this.$refs.formSearch.clearValidate();
          } else {
            this.$refs.formData.clearValidate();
          }

          this.pagination.pageSize = 1;
          this.active = '1';
          this.id = '';
          this.selectIds = [];
          this.list = [];
          this.saveParams.sourceType = ''; // 重置来源类型
        });
      },
      next() {
        if (this.selectIds.length === 0) {
          this.$message.error('请选择订单');
          return;
        }
        // 选择订单的总金额
        let amount = 0;
        this.selectIds.forEach(item => {
          amount = Math.round(amount * 100 + item.newAmount * 100) / 100;
          // amount = amount + item.newAmount
        });
        // 发票类型是国外的 统一选择 5 形式发票
        if (this.params.businessType !== 0) {
          this.saveParams.invoiceType = 5;
        }
        this.saveParams.orderIdList = this.selectIds.map(item => item.id);
        this.saveParams.invoiceMoney = amount;
        this.saveParams.userMobile = this.userMobile;
        this.INVOICE_TYPES.forEach(({ value, label }) => {
          if (value === this.params.businessType) {
            this.saveParams.type = value;
            this.name = label;
          }
        });
        this.active = '2';
      },
      handleSubmit() {
        this.$refs.formData.validate(async valid => {
          if (!valid) return;
          this.btnLoading = true;
          const { taxNumber, type, ...params } = this.saveParams;
          let saveParams = {
            ...params,
          };
          if (this.id) {
            saveParams.id = this.id;
          } else {
            saveParams.type = type;
          }
          if (this.saveParams.type === 0) {
            saveParams.taxNumber = taxNumber;
          } else saveParams.abnNumber = taxNumber;
          const save = this.id ? invoiceModify : generateInvoice;
          await save(saveParams);
          this.$message.success('申请成功');
          this.$emit('onGet');
          this.visible = false;
          this.btnLoading = false;
        });
      },
      handleSelectionChange(val) {
        this.selectIds = val;
      },
    },
  };
</script>

<style>
  .new-invoice-dialog .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }
  .new-invoice-dialog .el-dialog .el-dialog__header {
    background: #1890ff !important;
    text-align: center;
    padding: 10px;
  }
  .new-invoice-dialog .el-dialog__headerbtn .el-dialog__close {
    color: #fff;
  }
  .new-invoice-dialog .el-dialog__headerbtn .el-dialog__close:hover {
    color: #fff;
  }
  .new-invoice-dialog h3,
  .remarks .el-form-item__label {
    margin: 0;
    font-size: 16px;
    color: #000;
    font-weight: 900;
  }
</style>
