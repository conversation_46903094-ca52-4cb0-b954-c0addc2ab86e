<template>
  <div>
    <el-form inline>
      <el-form-item label="公司名称:">
        <el-input
          v-model="searchParams.nameTheCompany"
          placeholder="请输入公司名称"
        />
      </el-form-item>
      <el-form-item label="抬头类型:">
        <el-select v-model="searchParams.headerType" placeholder="请选择">
          <el-option label="个人及企事业单位" :value="0"></el-option>
          <el-option label="企业" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="开票日期:">
        <el-date-picker
          v-model="searchDate"
          :popper-class="'currentDatePickerClass'"
          :clearable="false"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList()">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="pie">
      <!-- 为 ECharts 准备一个具备大小（宽高）的 DOM -->
      <div id="main" style="width: 66%; height: 300px"></div>
      <div>
        <h3>总开票量 （{{ info.total }}）张</h3>
        <div class="item-invoice">
          <span>蓝字发票 :</span>
          <span>{{ info.blueInvoice }} 张</span>
        </div>
        <div class="item-invoice">
          <span>红字发票 :</span>
          <span>{{ info.redInvoice }} 张</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { parseTime, initSearchParams } from '@/utils';
  import { dataStatisticsInquiry } from '@/api/invoiceCenter';
  let echarts = require('echarts/lib/echarts');
  // 引入饼状图组件
  require('echarts/lib/chart/pie');
  // 引入提示框和title组件
  require('echarts/lib/component/tooltip');
  require('echarts/lib/component/legend');
  export default {
    data() {
      return {
        searchParams: {
          nameTheCompany: undefined,
          headerType: 0,
          startTime: undefined,
          endTime: undefined,
        },
        loading: false,
        searchDate: '',
        info: {},
        list: [],
      };
    },
    created() {
      this.setData(() => {
        this.getList();
      });
    },
    methods: {
      setData(cb) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
        this.searchDate = [
          parseTime(start, '{y}-{m}-{d} 00:00:00'),
          parseTime(end, '{y}-{m}-{d} 23:59:59'),
        ];
        cb && cb();
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.setData(() => {
          this.getList(true);
        });
      },
      async getList() {
        this.loading = true;
        this.searchParams.startTime = this.searchDate
          ? parseTime(this.searchDate[0])
          : '';
        this.searchParams.endTime = this.searchDate
          ? parseTime(this.searchDate[1])
          : '';
        const res = await dataStatisticsInquiry(
          initSearchParams(this.searchParams),
        );
        const data = [
          { value: res.ccount, name: '增值税普通发票' },
          { value: res.scount, name: '增值税专用发票' },
          { value: res.ceCount, name: '增值税电子普通发票' },
          { value: res.seCount, name: '增值税电子专用发票' },
        ];
        this.loading = false;
        this.info = res;
        this.list = data;
        this.initData();
      },
      initData() {
        // 基于准备好的dom，初始化echarts实例
        this.myChart = echarts.init(document.getElementById('main'));
        // 绘制图表
        this.myChart.setOption({
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b} : {c} ({d}%)',
          },
          legend: {
            orient: 'vertical',
            top: '60%',
            left: 'left',
            formatter: name => {
              const obj = this.list.find(item => item.name === name);
              return `${name} ${obj ? obj.value : 0} 张`;
            },
          },
          series: [
            {
              name: '访问来源',
              type: 'pie',
              radius: '70%',
              center: ['66%', '60%'],
              data: this.list,
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)',
                },
              },
              label: {
                formatter: name => {
                  return `${name.percent}%`;
                },
              },
            },
          ],
        });
      },
    },
  };
</script>

<style>
  .pie {
    width: 100%;
    height: 100%;
    display: flex;
  }
  .item-invoice {
    margin-bottom: 10px;
  }
  .currentDatePickerClass
    > .el-picker-panel__footer
    > .el-button--text:first-child {
    display: none;
  }
</style>
