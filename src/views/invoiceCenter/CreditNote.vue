<!--
 * @Author: bruce
 * @Date: 2023-08-25 15:02:29
 * @LastEditors: bruce
 * @FilePath: /access-fmis-web/src/views/invoiceCenter/CreditNote.vue
 * @Description: 红字发票
-->

<template>
  <div class="table">
    <el-form :inline="true" :model="formInline" class="demo-form-inline">
      <el-form-item label="订单编号">
        <div style="display: flex">
          <el-input
            v-model="formInline.orderSn"
            placeholder="请输入"
            clearable
            @clear="handleClearOrderSn"
            @blur="handleBlurBarCode"
          ></el-input>
          <el-button
            size="small"
            icon="el-icon-plus"
            @click="openBatchDialog"
          ></el-button>
        </div>
      </el-form-item>
      <el-form-item label="购方名称">
        <el-input
          v-model="formInline.buyerName"
          placeholder="请输入"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="开票时间">
        <el-date-picker
          v-model="formInline.invoiceTime"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00']"
        />
      </el-form-item>
      <el-form-item label="合并单号">
        <el-input
          v-model="formInline.batchId"
          placeholder="请输入"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="formInline.status" placeholder="请输入">
          <el-option
            v-for="(item, index) in stautsList"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="发票类目:">
        <el-select
            v-model="formInline.invoiceCategory"
            placeholder="请选择"
            filterable
            clearable=""
        >
          <el-option
              v-for="item in invoiceCategoryList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="">
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="warning" @click="onReset">重置</el-button>
        <el-button type="success" @click="onDownload">下载</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column
        prop="orderSn"
        label="订单编号"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="batchId"
        label="合并单号"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="invoiceTypeName"
        label="发票类型"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="purchaserName"
        label="购方名称"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="invoiceNo"
        label="正向发票号"
        width="180"
      ></el-table-column>
      <el-table-column
          prop="invoiceCategoryName"
          label="发票类目"
          width="180"
      ></el-table-column>
      <el-table-column
        prop="invoiceTime"
        label="开票时间"
        width="180"
      ></el-table-column>
      <el-table-column
          prop="businessTypeName"
          label="发票方向"
          width="180"
      ></el-table-column>
      <el-table-column
        prop="afterSaleOrderCode"
        label="逆向工单号"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="rsoRefundTotalAmt"
        label="退款金额"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="rsoRefundTime"
        label="退款退货申请时间"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="soFundTotalAmt"
        label="订单金额"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="invoiceTotalAmt"
        label="发票金额"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="statusName"
        label="处理状态"
        width="180"
      ></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="{ row }">
          <el-button v-if="row.status === 1" type="text" @click="onEdit(row)">
            处理完成
          </el-button>

          <el-button v-if="row.status === 2 && (row.invoiceType === 6 || row.invoiceType === 7 || row.invoiceType === 4 || row.invoiceType === 5 || row.invoiceType === 2 || row.invoiceType === 3) " type="text" @click="onLookInvoice(row)">
            查看红冲票
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="pagination.pageNo"
      :page-sizes="pagination.pageSizes"
      :page-size="pagination.limit"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <BatchDialog
      v-if="batchIdsShow"
      :id="formInline.orderSnList.toString()"
      text="订单编号"
      @complete="batchIdsShow = false"
      @ok="onconfirmBatch"
    />
  </div>
</template>
<script>
  import {
    getCreditNote,
    handleOrder,
    importOrderData,
  } from '@/api/ticketTaxPlatform/invoiceCenter.js';
  import BatchDialog from '@/components/dialog/batchDialog.vue';
  import {INVOICE_CATEGORY_LIST} from "@/consts";

  export default {
    name: 'Table',
    components: {
      BatchDialog,
    },
    props: {},
    data() {
      return {
        formInline: { 
          invoiceTime: [], 
          orderSnList: [],
          invoiceCategory: 1  // 默认选中"货物"
        },
        stautsList: [
          { label: '已处理', value: 2 },
          { label: '未处理', value: 1 },
        ],
        dialogVisible: false,
        pagination: {
          pageNo: 1,
          limit: 10,
          total: 0,
          pageSizes: [10, 20, 50, 100],
        },
        rowData: {},
        tableData: [],
        batchIdsShow: false,
        invoiceCategoryList: INVOICE_CATEGORY_LIST,
      };
    },
    mounted() {
      this.initData();
    },
    methods: {
      simplifyParams(comParmam) {
        let params = comParmam;
        delete params.pageSizes;
        delete params.total;

        if (this.formInline?.orderSnList.length > 0) {
          params.orderSnList = this.formInline?.orderSnList.filter(
            item => item,
          );
        } else {
          delete params.orderSnList;
        }

        delete params.orderSn;

        if (this.formInline.invoiceTime?.length > 0) {
          params.invoiceTimeStart = this.formInline.invoiceTime[0];
          params.invoiceTimeEnd = this.formInline.invoiceTime[1];
        }

        delete params.invoiceTime;
        return params;
      },
      async initData() {
        let params = this.simplifyParams({
          ...this.formInline,
          ...this.pagination,
        });
        const { res, err } = await getCreditNote(params);
        if (!err) {
          this.tableData = res.records;
          this.pagination.total = res.total;
        }
      },
      async onDownload() {
        let params = this.simplifyParams({
          ...this.formInline,
          ...this.pagination,
        });
        const { res, err } = await importOrderData(params);
        if (!err) {
          this.$message.success('下载成功');
        }
      },
      async onEdit(row) {
        this.rowData = row;
        const { res, err } = await handleOrder({
          id: row.id,
          newStatus: 2,
        });
        if (!err) {
          this.$message.success('处理成功');
          this.initData();
        }
      },

      async onLookInvoice(row) {
        this.rowData = row;
        window.open(row.invoiceFileUrl,'_blank')
      },
      onReset() {
        this.formInline = { 
          invoiceTime: [], 
          orderSnList: [],
          invoiceCategory: 1  // 重置时也默认选中"货物"
        };
        this.initData();
      },
      onSearch() {
        if (
          this.formInline.orderSn &&
          this.formInline.orderSnList.indexOf(',') === -1
        )
          this.formInline.orderSnList.push(this.formInline.orderSn);
        this.formInline.orderSnList = [...new Set(this.formInline.orderSnList)];
        this.initData();
      },
      handleSizeChange(val) {
        this.pagination.limit = val;
        this.initData();
      },
      handleCurrentChange(val) {
        this.pagination.pageNo = val;
        this.initData();
      },
      openBatchDialog() {
        this.$set(this.formInline, 'orderSn', '');
        this.$set(this.formInline, 'orderSnList', []);
        this.batchIdsShow = true;
      },
      onconfirmBatch(ids) {
        this.$set(this.formInline, 'orderSnList', [...ids]);
        this.$set(
          this.formInline,
          'orderSn',
          this.formInline.orderSnList.toString(),
        );
      },
      handleBlurBarCode() {
        this.formInline.orderSnList = [...this.formInline.orderSn?.split(',')];
      },
      handleClearOrderSn() {
        this.$set(this.formInline, 'orderSn', '');
        this.$set(this.formInline, 'orderSnList', []);
      },
    },
  };
</script>
<style lang="scss" scoped></style>
