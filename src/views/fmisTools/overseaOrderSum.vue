<template>
  <div>
    <el-form :inline="true" :model="params">
      <el-form-item>
        <ac-permission-button
          btn-text="查询"
          permission-key=""
          @click="search(1)"
        ></ac-permission-button>
      </el-form-item>

      <el-form-item>
        <ac-permission-button
          btn-text="查询外币"
          permission-key=""
          @click="searchForeign(2)"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <el-table
      v-if="tab === 1"
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
    >
      <el-table-column
        align="center"
        prop="updateTime"
        label="日期"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="mchid"
        label="商户号"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="actualOverseaRmbAmount"
        label="出境人民币金额"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="payCurrency"
        label="币种"
        min-width="100"
      ></el-table-column>
    </el-table>
    <div>
      <el-table
        v-if="tab === 2"
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
      >
        <el-table-column
          align="center"
          prop="updateTime"
          label="日期"
          min-width="100"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="mchid"
          label="商户号"
          min-width="100"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="actualOverseaForeignAmount"
          label="出境外币金额"
          min-width="120"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="foreignCurrency"
          label="外币币种"
          min-width="100"
        ></el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
  import {
    getOverseaOrderSum,
    getOverseaOrderSumForeign,
  } from '@/api/fmisTools';

  export default {
    data() {
      return {
        tab: 1,
        params: '',
        tableData: [],
        loading: false, // loading表格加载层
      };
    },
    methods: {
      async fetch() {
        let params = { ...this.params };
        this.loading = true;
        try {
          const res = await getOverseaOrderSum(params);
          console.log(res, 'res');
          if (res) {
            this.tableData = res;
            this.loading = false;
          } else {
            this.loading = false;
          }
        } catch (error) {
          this.loading = false;
        }
      },
      search(val) {
        this.tab = val;
        this.fetch();
      },
      searchForeign(val) {
        this.tab = val;
        this.fetchForeign();
      },
      async fetchForeign() {
        let params = { ...this.params };
        this.loading = true;
        try {
          const res = await getOverseaOrderSumForeign(params);
          console.log(res, 'res');
          if (res) {
            this.tableData = res;
            this.loading = false;
          } else {
            this.loading = false;
          }
        } catch (error) {
          this.loading = false;
        }
      },
      reset() {
        Object.assign(this.params, this.$options.data.call(this).params);
      },
    },
  };
</script>
<style lang="scss"></style>
