<template>
  <div>
    <div>
      <el-tag>
        注意：0待跨境付款，1跨境付款中，2跨境付款成功，3跨境付款错误,4完全退款，无付款金额,5单据不完整，推单或物流信息不足
      </el-tag>
    </div>
    <br />
    <el-form :inline="true" :model="params">
      <el-form-item label="状态:">
        <el-input v-model="params.overseaStatus" placeholder="请输入" />
      </el-form-item>
      <el-form-item>
        <ac-permission-button
          btn-text="查询"
          permission-key=""
          @click="search(true)"
        ></ac-permission-button>
        <el-button type="primary" @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column
        align="center"
        prop="salesNo"
        label="订单号"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="orderAmount"
        label="订单金额"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="payCurrency"
        label="支付币种"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="payFee"
        label="支付手续费"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="goodsPayment"
        label="货款"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="associatedFee"
        label="关联费用"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="overseaRmbAmount"
        label="跨境付款金额"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="actualOverseaRmbAmount"
        label="实际出境金额"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="overseaStatus"
        label="出境状态"
        min-width="100"
      ></el-table-column>
    </el-table>
  </div>
</template>
<script>
  import { getOverseaOrder } from '@/api/fmisTools';

  export default {
    data() {
      return {
        params: {
          overseaStatus: '',
        },
        tableData: [],
        loading: false, // loading表格加载层
      };
    },
    methods: {
      async fetch() {
        let params = { ...this.params };
        if (!params.overseaStatus) {
          this.$message.error('状态不能为空!');
          return;
        }
        this.loading = true;
        try {
          const res = await getOverseaOrder(params);
          console.log(res, 'res');
          if (res) {
            this.tableData = res;
            this.loading = false;
          } else {
            this.loading = false;
          }
        } catch (error) {
          this.loading = false;
        }
      },
      search() {
        this.fetch();
      },
      reset() {
        Object.assign(this.params, this.$options.data.call(this).params);
      },
    },
  };
</script>
<style lang="scss"></style>
