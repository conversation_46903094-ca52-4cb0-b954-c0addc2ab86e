<template>
  <div>
    <el-form ref="search" inline :model="searchParams">
      <el-form-item
        prop="payId"
        :rules="[
          {
            validator: rules.validatePayId,
            trigger: 'change',
          },
        ]"
        label="支付单号:"
      >
        <el-input
          v-model="searchParams.payId"
          clearable
          placeholder="请输入支付单编号"
        />
      </el-form-item>
      <el-form-item v-if="tab === 2" label="支付流水号:">
        <el-input
          v-model="searchParams.paymentId"
          clearable
          placeholder="请输入支付流水号"
        />
      </el-form-item>
      <el-form-item label="销售下单时间:">
        <el-date-picker
          v-model="transactionDate"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="创建时间:">
        <el-date-picker
          v-model="payCreatedDate"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :picker-options="pickerOptions"
        ></el-date-picker>
      </el-form-item>
      <el-form-item v-if="tab === 1" label="支付单状态:">
        <el-select
          v-model="searchParams.orderStatusList"
          clearable
          multiple
          placeholder="请选择"
        >
          <el-option :value="0" label="预订单"></el-option>
          <el-option :value="1" label="支付中"></el-option>
          <el-option :value="2" label="支付成功"></el-option>
          <el-option :value="3" label="支付失败"></el-option>
          <el-option :value="4" label="商户关单"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="tab === 2" label="订单状态:">
        <el-select
          v-model="searchParams.statusList"
          clearable
          multiple
          placeholder="请选择"
        >
          <el-option :value="1" label="交易中"></el-option>
          <el-option :value="2" label="交易成功"></el-option>
          <el-option :value="3" label="交易失败"></el-option>
          <el-option :value="50" label="冲正"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="tab === 1" label="退款状态:">
        <el-select
          v-model="searchParams.refundStatusList"
          clearable
          multiple
          placeholder="请选择"
        >
          <el-option :value="0" label="无退款"></el-option>
          <el-option :value="1" label="部分退款"></el-option>
          <el-option :value="2" label="全部退款"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button type="primary" @click="paymentPorder()">查流水号</el-button>
        <el-button type="primary" @click="paymentWorder()">
          按微信单号查流水号
        </el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="{ ...options }"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="props">
        <ac-permission-button
          slot="reference"
          btn-text="查看支付流水"
          type="text"
          size="small"
          permission-key=""
          @click="handelJump(2, props.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <el-dialog title="查流水号" :visible.sync="showAuthUser">
      <div>
        <el-form :inline="true" :model="params">
          <el-form-item label="W订单号:">
            <el-input v-model="params.orderSn" placeholder="请输入orderSn" />
          </el-form-item>
          <el-form-item>
            <ac-permission-button
              btn-text="查询"
              permission-key=""
              @click="searchW(true)"
            ></ac-permission-button>
            <el-button type="primary" @click="reset">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table
          v-loading="loading"
          :data="tableData"
          border
          style="width: 100%"
        >
          <el-table-column
            align="center"
            prop="platformOrderId"
            label="支付订单号"
            min-width="100"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="tenantOrderId"
            label="租户订单号"
            min-width="120"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="channelId"
            label="渠道"
            min-width="100"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="amount"
            label="支付金额"
            min-width="100"
          ></el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <el-dialog title="按微信订单号查流水号" :visible.sync="showAuthUser_W">
      <div>
        <el-form :inline="true" :model="params">
          <el-form-item label="微信订单号:">
            <el-input v-model="params.orderWSn" placeholder="请输入orderWSn" />
          </el-form-item>
          <el-form-item>
            <ac-permission-button
              btn-text="查询"
              permission-key=""
              @click="searchWOrder(true)"
            ></ac-permission-button>
            <el-button type="primary" @click="reset">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table
          v-loading="loading"
          :data="tableData"
          border
          style="width: 100%"
        >
          <el-table-column
            align="center"
            prop="platformOrderId"
            label="支付订单号"
            min-width="100"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="tenantOrderId"
            label="租户订单号"
            min-width="120"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="channelId"
            label="渠道"
            min-width="100"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="amount"
            label="支付金额"
            min-width="100"
          ></el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import { getPayList, getPaymentList } from '@/api/documentCenter';
  import { debounce, parseTime, initSearchParams } from '@/utils';
  import { isNumber } from '@/utils/validate';
  import { getPaymentOrder, getPaymentWOrder } from '@/api/fmisTools';
  export default {
    components: {
      dynamictable,
    },
    data() {
      const validatePayId = (rule, value, callback) => {
        const val = value ? value.trim() : '';
        if (!isNumber(val)) {
          callback(new Error('支付单号必须为数字'));
        } else {
          callback();
        }
      };
      return {
        params: {
          orderSn: '',
          orderWSn: '',
        },
        tableData: [],
        loading: false, // loading表格加载层
        showAuthUser: false,
        showAuthUser_W: false,
        tab: 2,
        list: [],
        transactionDate: '',
        payCreatedDate: '',
        rules: {
          validatePayId,
        },
        searchParams: {
          payId: '',
          paymentId: '',
          statusList: [],
          orderStatusList: [],
          refundStatusList: [],
        },
        search: {}, // 缓存搜索条件
        options: {
          loading: false,
          border: true,
        },
        pageSize: 1, // 缓存页数
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        copyDate: '',
        pickerOptions: {
          onPick: ({ maxDate, minDate }) => {
            this.copyDate = minDate.getTime();
            if (maxDate) {
              this.copyDate = '';
            }
          },
          disabledDate: time => {
            if (this.copyDate !== '') {
              const one = 30 * 24 * 3600 * 1000;
              const minTime = this.copyDate - one;
              const maxTime = this.copyDate + one;
              return time.getTime() < minTime || time.getTime() > maxTime;
            }
          },
        },
      };
    },
    created() {
      // this.getList();
    },
    methods: {
      async fetch() {
        let params = { ...this.params };
        if (!params.orderSn) {
          this.$message.error('W单号不能为空!');
          return;
        }
        this.loading = true;
        try {
          const res = await getPaymentOrder(params);
          console.log(res, 'res');
          if (res) {
            this.tableData = res;
            this.loading = false;
          } else {
            this.loading = false;
          }
        } catch (error) {
          this.loading = false;
        }
      },
      async fetchW() {
        let params = { ...this.params };
        if (!params.orderWSn) {
          this.$message.error('微信订单号不能为空!');
          return;
        }
        this.loading = true;
        try {
          const res = await getPaymentWOrder(params);
          console.log(res, 'res');
          if (res) {
            this.tableData = res;
            this.loading = false;
          } else {
            this.loading = false;
          }
        } catch (error) {
          this.loading = false;
        }
      },
      searchW() {
        this.fetch();
      },
      searchWOrder() {
        this.fetchW();
      },
      reset() {
        Object.assign(this.params, this.$options.data.call(this).params);
      },
      handelJump(val, row) {
        this.tab = val;
        this.list = [];
        if (val === 1) {
          this.getCacheData();
        } else {
          this.setCacheData(row);
        }
        this.getList();
      },
      getCacheData() {
        this.pagination.total = null;
        // 返回记住页数
        this.pagination.pageSize = this.pageSize;
        const {
          transactionTimeStart,
          transactionTimeEnd,
          paymentCreatetimeStart,
          paymentCreatetimeEnd,
          payCreatedTimeStart,
          payCreatedTimeEnd,
        } = this.search;
        this.transactionDate = [transactionTimeStart, transactionTimeEnd];
        this.payCreatedDate = [
          this.tab === 1 ? payCreatedTimeStart : paymentCreatetimeStart,
          this.tab === 1 ? payCreatedTimeEnd : paymentCreatetimeEnd,
        ];
        this.searchParams = { ...this.search };
      },
      setCacheData(row) {
        this.pageSize = this.pagination.pageSize;
        this.search = { ...this.searchParams };
        this.searchParams = {
          payId: row.payId,
        };
        this.transactionDate = '';
        this.payCreatedDate = '';
        Object.assign(this.$data.pagination, this.$options.data().pagination);
      },
      getParams() {
        this.searchParams.transactionTimeStart = this.transactionDate
          ? parseTime(this.transactionDate[0], '{y}-{m}-{d} {h}:{i}:{s}')
          : '';
        this.searchParams.transactionTimeEnd = this.transactionDate
          ? parseTime(this.transactionDate[1], '{y}-{m}-{d} {h}:{i}:{s}')
          : '';
        this.searchParams[
          this.tab === 1 ? 'payCreatedTimeStart' : 'paymentCreatetimeStart'
        ] = this.payCreatedDate
          ? parseTime(this.payCreatedDate[0], '{y}-{m}-{d} {h}:{i}:{s}')
          : '';
        this.searchParams[
          this.tab === 1 ? 'payCreatedTimeEnd' : 'paymentCreatetimeEnd'
        ] = this.payCreatedDate
          ? parseTime(this.payCreatedDate[1], '{y}-{m}-{d} {h}:{i}:{s}')
          : '';
        const {
          orderStatusList = [],
          refundStatusList = [],
          statusList = [],
        } = this.searchParams;

        const params = {
          ...this.searchParams,
          orderStatusList: orderStatusList.length
            ? orderStatusList.join(',')
            : '',
          refundStatusList: refundStatusList.length
            ? refundStatusList.join(',')
            : '',
          statusList: statusList.length ? statusList.join(',') : '',
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const getApiList = this.tab === 1 ? getPayList : getPaymentList;

        try {
          const res = await getApiList(params);
          this.options.loading = false;
          if (res) {
            this.list = res && res.list ? res.list : [];
            this.pagination.total = res ? res.total : 0;
          }
        } catch (error) {
          this.options.loading = false;
        }
      },
      onSearch: debounce(function (e) {
        const searchParams = initSearchParams(this.getParams());
        if (Object.keys(searchParams).length === 2) {
          return this.$message.warning('请最少选择一个查询条件');
        }
        this.$refs.search.validate(async valid => {
          if (!valid) return;
          this.getList(true);
        });
      }, 1000),
      paymentPorder() {
        this.params.orderSn = '';
        this.tableData = [];
        this.showAuthUser = true;
      },
      paymentWorder() {
        this.params.orderWSn = '';
        this.tableData = [];
        this.showAuthUser_W = true;
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.transactionDate = '';
        this.payCreatedDate = '';
      },
      getColumns() {
        const columns = [
          {
            prop: 'payId',
            label: '支付单号',
          },
          {
            prop: 'paySn',
            label: '租户订单号',
          },
          {
            prop: 'memberId',
            label: '会员ID',
          },
          {
            prop: 'tenantId',
            label: '租户ID',
          },
          {
            prop: 'tenantName',
            label: '租户名称',
          },
          {
            prop: 'currencyUnit',
            label: '币种',
          },
          {
            prop: 'orderTitle',
            label: '订单标题',
          },
          {
            prop: 'orderSn',
            label: '销售单号',
          },
          {
            prop: 'amount',
            label: '销售单金额(CNY)',
          },
          {
            prop: 'orderRefundedAmount',
            label: '已退款金额',
            render: ({ orderRefundedAmount, payId }) => (
              <div
                onClick={() => {
                  this.$router.push({
                    path: 'payRefundOrder',
                    query: {
                      payId,
                    },
                  });
                }}
              >
                <a style="cursor:pointer">{orderRefundedAmount}</a>
              </div>
            ),
          },
          {
            prop: 'orderStatusName',
            label: '支付单状态',
          },
          {
            prop: 'refundStatusName',
            label: '退款状态',
          },
          {
            prop: 'applicationName',
            label: '客户端来源',
          },
          {
            prop: 'transactionTime',
            label: '销售下单时间',
          },
          {
            prop: 'payCreatedTime',
            label: '创建时间',
          },
          {
            prop: 'payUpdatedTime',
            label: '更新时间',
          },
          {
            prop: 'payCloseTime',
            label: '关单时间',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            minWidth: '80px',
            maxWidth: '140px',
            scopedSlots: { customRender: 'operation' },
          },
        ];
        const columns1 = [
          {
            prop: 'paymentId',
            label: '支付流水号',
          },
          {
            prop: 'payId',
            label: '支付单号',
          },
          {
            prop: 'paySn',
            label: '租户订单号',
          },
          {
            prop: 'memberId',
            label: '会员ID',
          },
          {
            prop: 'companyName',
            label: '收单主体名称',
          },
          {
            prop: 'tenantId',
            label: '租户ID',
          },
          {
            prop: 'tenantName',
            label: '租户名称',
          },
          {
            prop: 'currencyUnit',
            label: '币种',
          },
          {
            prop: 'rate',
            label: '汇率',
          },
          {
            prop: 'amount',
            label: '支付金额',
          },
          {
            prop: 'chargeAmount',
            label: '支付手续费',
          },
          {
            prop: 'orderSn',
            label: '销售单号',
          },
          {
            prop: 'payAmount',
            label: '销售单金额(CNY)',
          },
          {
            prop: 'statusName',
            label: '订单状态',
          },
          {
            prop: 'subStatusName',
            label: '子状态',
          },
          {
            prop: 'paymentProductionName',
            label: '支付产品',
          },
          {
            prop: 'channelName',
            label: '支付渠道',
          },
          {
            prop: 'payTypeName',
            label: '支付方式',
          },
          {
            prop: 'transactionTime',
            label: '销售下单时间',
          },
          {
            prop: 'createTime',
            label: '创建时间',
          },
          {
            prop: 'updateTime',
            label: '更新时间',
          },
          {
            prop: 'closeTime',
            label: '关单时间',
          },
        ];
        return this.tab === 1 ? columns : columns1;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
