<template>
  <div>
    <el-form :inline="true" :model="params">
      <el-form-item label="W订单号:">
        <el-input v-model="params.orderSn" placeholder="请输入orderSn" />
      </el-form-item>
      <el-form-item>
        <ac-permission-button
          btn-text="查询"
          permission-key=""
          @click="search(true)"
        ></ac-permission-button>
        <el-button type="primary" @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column
        align="center"
        prop="platformOrderId"
        label="支付订单号"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="tenantOrderId"
        label="租户订单号"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="channelId"
        label="渠道"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="amount"
        label="支付金额"
        min-width="100"
      ></el-table-column>
    </el-table>
  </div>
</template>
<script>
  import { getPaymentOrder } from '@/api/fmisTools';

  export default {
    data() {
      return {
        params: {
          orderSn: '',
        },
        tableData: [],
        loading: false, // loading表格加载层
      };
    },
    methods: {
      async fetch() {
        let params = { ...this.params };
        if (!params.orderSn) {
          this.$message.error('W单号不能为空!');
          return;
        }
        this.loading = true;
        try {
          const res = await getPaymentOrder(params);
          console.log(res, 'res');
          if (res) {
            this.tableData = res;
            this.loading = false;
          } else {
            this.loading = false;
          }
        } catch (error) {
          this.loading = false;
        }
      },
      search() {
        this.fetch();
      },
      reset() {
        Object.assign(this.params, this.$options.data.call(this).params);
      },
    },
  };
</script>
<style lang="scss"></style>
