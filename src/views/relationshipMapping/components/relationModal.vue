<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-18 11:40:19
 * @LastEditTime: 2022-05-25 10:21:55
 * @LastEditors: xuxiang
 * @Reference: 
-->
<template>
  <el-dialog
    :title="curRow ? '编辑' : '新增'"
    :visible.sync="showDialog"
    :close-on-click-modal="false"
    width="700px"
  >
    <el-form ref="formData" :model="saveParams" label-width="200px">
      <!-- <el-form-item
        prop="lookupId"
        :rules="[
          {
            required: true,
            message: '请输入',
            trigger: 'change',
          },
        ]"
        label="关系id:"
      >
        <el-input
          v-model="saveParams.lookupId"
          style="width: 300px"
          placeholder="请输入"
        />
      </el-form-item> -->

      <el-form-item
        prop="moduleId"
        :rules="[
          {
            required: true,
            message: '请输入',
            trigger: 'change',
          },
        ]"
        label="模型id:"
      >
        <el-input
          v-model="saveParams.moduleId"
          disabled
          style="width: 300px"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item
        prop="leftKey"
        :rules="[
          {
            required: true,
            message: '请输入',
            trigger: 'change',
          },
        ]"
        :label="`${modelObj.leftKeyView}:`"
      >
        <el-input
          v-model="saveParams.leftKey"
          style="width: 300px"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item
        prop="leftName"
        :rules="[
          {
            required: true,
            message: '请输入',
            trigger: 'change',
          },
        ]"
        :label="`${modelObj.leftNameView}:`"
      >
        <el-input
          v-model="saveParams.leftName"
          style="width: 300px"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item
        prop="rightValue"
        :rules="[
          {
            required: true,
            message: '请输入',
            trigger: 'change',
          },
        ]"
        :label="`${modelObj.rightValueView}:`"
      >
        <el-input
          v-model="saveParams.rightValue"
          style="width: 300px"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item
        prop="rightName"
        :rules="[
          {
            required: true,
            message: '请输入',
            trigger: 'change',
          },
        ]"
        :label="`${modelObj.rightNameView}:`"
      >
        <el-input
          v-model="saveParams.rightName"
          style="width: 300px"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="showDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit()">提交</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
  import {
    relationLookupAdd,
    relationLookupUpdate,
  } from '@/api/relationshipMapping';
  import { initSearchParams } from '@/utils';
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      modelObj: {
        type: Object,
        default: () => {},
      },
      curRow: {
        type: Object,
        default: null,
      },
    },
    data() {
      return {
        saveParams: {
          leftKey: '',
          leftName: '',
          lookupId: '',
          moduleId: '',
          rightValue: '',
          rightName: '',
          id: '',
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {
        console.log(value, this.modelObj, this.curRow, 'this.modelObj');
        if (!value) {
          Object.assign(this.$data.saveParams, this.$options.data().saveParams);
          this.$refs.formData.clearValidate();
        }

        if (value) {
          this.saveParams.moduleId = this.modelObj.id;
          if (this.curRow) {
            this.saveParams = {
              ...this.curRow,
            };
          }
        }
      },
    },
    created() {},
    methods: {
      handleSubmit() {
        this.$refs.formData.validate(async valid => {
          if (!valid) return;
          const api = this.curRow ? relationLookupUpdate : relationLookupAdd;
          const { id, ...params } = this.saveParams;
          const body = {
            ...params,
            lookupId: id,
          };
          // console.log(this.saveParams, 'this.saveParams');
          // return;
          const { err } = await api(initSearchParams(body));
          if (!err) {
            this.$message.success(this.curRow ? '修改成功' : '添加成功');
            this.showDialog = false;
            this.$emit('onGet');
          }
        });
      },
    },
  };
</script>
<style lang="scss"></style>
