<!-- 合并报表对账 -->
<template>
  <div>
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      border
      style="width: 100%"
    >
      <el-table-column
        align="center"
        prop="period"
        label="账期"
        min-width="165"
      ></el-table-column>

      <el-table-column
        align="center"
        label="对账文件链接"
        min-width="165"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            :loading="detailLoading"
            @click="checkDetails(scope.row)"
          >
            下载
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page="params.current"
      :page-size="params.size"
      :page-sizes="[10, 20, 30, 50, 100]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>
<script>
  import { getList } from '@/api/consolidatedStatement';

  export default {
    name: 'ConsolidatedStatement',
    data() {
      return {
        params: {
          current: 1,
          size: 10,
        },
        tableData: [], //报表数据
        total: null,
        tableLoading: false, //table加载
        detailLoading: false,
      };
    },
    computed: {},
    watch: {},
    created() {
      this.fetch();
    },
    mounted() {},
    methods: {
      // 搜索
      async fetch() {
        let params = { ...this.params };
        this.tableLoading = true;
        const { res } = await getList(params);
        if (res) {
          this.tableData = res?.records || [];
          this.total = res?.total || 0;
        }
        this.tableLoading = false;
      },
      //下载
      checkDetails(row) {
        if (row.url) {
          this.detailLoading = true;
          window.open(row.url, '_blank');
          this.detailLoading = false;
        } else {
          this.$$message.warning('链接址为空');
        }
      },
      //分页大小
      handleSizeChange(val) {
        this.params.current = 1;
        this.params.size = val;
        this.fetch();
      },
      //分页
      handleCurrentChange(val) {
        this.params.current = val;
        this.fetch();
      },
    },
  };
</script>
<style scoped></style>
