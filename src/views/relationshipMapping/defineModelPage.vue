<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-19 16:33:21
 * @LastEditTime: 2023-07-10 10:38:42
 * @LastEditors: dddd
 * @Reference: 
-->
<template>
  <div style="height: 100%">
    <el-row
      :style="{ width: '100%' }"
      :gutter="0"
      :type="''"
      :align="''"
      :justify="'start'"
    >
      <el-col :span="24" :offset="0" :push="0" :pull="0">
        <ac-data-page
          ref="myAcDataPageRef_16529456670645134"
          :style="{ width: '100%' }"
          :search="acDataPageSearch_16529456670645134"
          :table="acDataPageTable_16529456670645134"
          :add="acDataPageAdd_16529456670645134"
          :edit="acDataPageEdit_16529456670645134"
          :before-open-add="beforeOpenAdd_16529456670645134"
          :before-open-edit="beforeOpenEdit_16529456670645134"
          @getTableData="getTableData_16529456670645134"
          @searchCustomClick="searchCustomClick_16529456670645134"
          @searchFormChange="searchFormChange_16529456670645134"
          @tableCustomClick="tableCustomClick_16529456670645134"
          @addCommit="addCommit_16529456670645134"
          @delTableData="delTableData_16529456670645134"
          @editCommit="editCommit_16529456670645134"
        ></ac-data-page>
      </el-col>
    </el-row>
  </div>
</template>
<script>
  import {
    relationModuleList,
    relationModuleDelete,
    relationModuleAdd,
    relationModuleUpdate,
  } from '@/api/relationshipMapping';
  export default {
    data() {
      return {
        // 数据页面新增表单配置
        acDataPageAdd_16529456670645134: {
          width: '800px',
          config: {
            valueWidth: '500px',
            labelWidth: '200px',
          },

          options: [
            {
              key: 'module',
              type: 'input',
              label: '模型代码定义',
              clearable: true,
              placeholder: '请输入',
              rules: [{ required: true, message: '请输入', trigger: 'blur' }],
            },
            {
              type: 'input',
              key: 'moduleView',
              label: '模型名称',
              clearable: true,
              placeholder: '请输入',
              rules: [{ required: true, message: '请输入', trigger: 'blur' }],
            },
            {
              type: 'input',
              key: 'leftKeyView',
              label: '左侧标识类型',
              clearable: true,
              placeholder: '请输入',
              rules: [{ required: true, message: '请输入', trigger: 'blur' }],
            },
            {
              type: 'input',
              key: 'leftNameView',
              label: '左侧标识类型描述',
              clearable: true,
              placeholder: '请输入',
              rules: [{ required: true, message: '请输入', trigger: 'blur' }],
            },
            {
              type: 'input',
              key: 'rightValueView',
              label: '右侧标识类型',
              clearable: true,
              placeholder: '请输入',
              rules: [{ required: true, message: '请输入', trigger: 'blur' }],
            },
            {
              type: 'input',
              key: 'rightNameView',
              label: '右侧标识类型描述',
              clearable: true,
              placeholder: '请输入',
              rules: [{ required: true, message: '请输入', trigger: 'blur' }],
            },
          ],
        },
        // 数据页面编辑表单配置
        acDataPageEdit_16529456670645134: {
          width: '800px',
          config: {
            valueWidth: '500px',
            labelWidth: '200px',
          },
          options: [
            {
              key: 'module',
              type: 'input',
              label: '模型代码定义',
              clearable: true,
              placeholder: '请输入',
              rules: [{ required: true, message: '请输入', trigger: 'blur' }],
            },
            {
              type: 'input',
              key: 'moduleView',
              label: '模型名称',
              clearable: true,
              placeholder: '请输入',
              rules: [{ required: true, message: '请输入', trigger: 'blur' }],
            },
            {
              type: 'input',
              key: 'leftKeyView',
              label: '左侧标识类型',
              clearable: true,
              placeholder: '请输入',
              rules: [{ required: true, message: '请输入', trigger: 'blur' }],
            },
            {
              type: 'input',
              key: 'leftNameView',
              label: '左侧标识类型描述',
              clearable: true,
              placeholder: '请输入',
              rules: [{ required: true, message: '请输入', trigger: 'blur' }],
            },
            {
              type: 'input',
              key: 'rightValueView',
              label: '右侧标识类型',
              clearable: true,
              placeholder: '请输入',
              rules: [{ required: true, message: '请输入', trigger: 'blur' }],
            },
            {
              type: 'input',
              key: 'rightNameView',
              label: '右侧标识类型描述',
              clearable: true,
              placeholder: '请输入',
              rules: [{ required: true, message: '请输入', trigger: 'blur' }],
            },
          ],
        },
        // 数据页面筛选栏表单配置
        acDataPageSearch_16529456670645134: {
          button: { config: { hidden: [], resetNotSearch: true }, options: [] },
          form: {
            options: [
              {
                key: 'moduleView',
                type: 'input',
                label: '模型名称',
                clearable: true,
                placeholder: '请输入',
              },
            ],
          },
        },
        // 数据页面表格列配置
        acDataPageTable_16529456670645134: {
          config: { hidden: ['删除'] },
          options: [
            { prop: 'id', label: 'ID' },
            { prop: 'module', label: '模型代码' },
            { prop: 'moduleView', label: '模型名称', type: '', clickKey: '' },
            {
              prop: 'leftKeyView',
              label: '左侧标识类型',
              type: '',
              clickKey: '',
            },
            {
              prop: 'leftNameView',
              label: '左侧标识类型描述',
              type: '',
              clickKey: '',
            },
            {
              prop: 'rightValueView',
              label: '右侧标识类型',
              type: '',
              clickKey: '',
            },
            {
              prop: 'rightNameView',
              label: '右侧标识类型描述',
              type: '',
              clickKey: '',
            },
            { type: 'operation', prop: '', label: '' },
          ],
        },
      };
    },
    computed: {},
    watch: {},
    created() {
      this.getTableData_16529456670645134();
    },
    mounted() {},
    methods: {
      // 数据页面-新增表单确认事件
      async addCommit_16529456670645134(saveData, done) {
        // 内置新增弹窗确认事件
        console.log(saveData, 'saveData');
        await relationModuleAdd({
          ...saveData,
        });
        this.getTableData_16529456670645134();
        done();
        this.$message.success('添加成功');
      },
      // 数据页面-编辑表单确认事件
      async editCommit_16529456670645134(saveData, done) {
        console.log(1234);
        // 内置编辑弹窗确认事件
        await relationModuleUpdate({
          ...saveData,
        });
        this.getTableData_16529456670645134();
        done();
        this.$message.success('编辑成功');
      },

      // 数据页面-获取表格数据
      getTableData_16529456670645134(requestData) {
        // 点击搜索按钮获取数据事件
        this.$nextTick(async function () {
          const reqData =
            requestData ||
            this.$refs.myAcDataPageRef_16529456670645134.GetRequestData();
          const { currentPage = 1, moduleView, pageSize = 10 } = reqData;
          const { err, res } = await relationModuleList({
            moduleView,
            current: currentPage,
            size: pageSize,
          });
          this.$refs.myAcDataPageRef_16529456670645134.OverLoading();
          if (res && !err) {
            this.$refs.myAcDataPageRef_16529456670645134.SetTableData(
              res.records,
              res.total,
            );
          }
        });
      },
      // 数据页面-筛选栏自定义按钮点击事件
      searchCustomClick_16529456670645134(key) {
        // 自定义筛选栏按钮点击事件
        console.log(key);
      },
      // 数据页面-筛选栏表单值改变触发
      searchFormChange_16529456670645134(obj) {
        // 筛选栏表单项值改变事件
        const { key, value } = obj;
        console.log(key);
        console.log(value);
      },
      // 数据页面-表格自定义点击事件
      tableCustomClick_16529456670645134(key, row) {
        // 表格自定义点击事件
        console.log(key);
        console.log(row);
      },
      // 数据页面-打开新增表单拦截
      beforeOpenAdd_16529456670645134(done) {
        // 新建数据确认拦截
        done();
      },
      // 数据页面-打开编辑表单拦截
      beforeOpenEdit_16529456670645134(done, row) {
        // 编辑数据确认拦截
        console.log(row);
        done(row);
      },
      // 数据页面-表格删除单条数据触发
      async delTableData_16529456670645134(row, index, done) {
        // 表格行删除事件
        await relationModuleDelete({ id: row.id });
        this.$message.success('删除成功');
        this.getTableData_16529456670645134();
      },
    },
  };
</script>
<style scoped></style>
