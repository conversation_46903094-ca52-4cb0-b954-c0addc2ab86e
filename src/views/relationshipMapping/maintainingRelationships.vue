<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-18 10:52:14
 * @LastEditTime: 2022-06-06 10:19:23
 * @LastEditors: xuxiang
 * @Reference: 
-->

<template>
  <div v-loading="pageLoading">
    <div style="margin-bottom: 20px">
      <span style="margin-right: 16px">映射模型:</span>
      <el-select v-model="moduleView" placeholder="请选择">
        <el-option
          v-for="item in modelViveList"
          :key="item.id"
          :label="item.moduleView"
          :value="item.id"
          @click.native="handleModule(item)"
        ></el-option>
      </el-select>
    </div>

    <el-form
      inline
      style="border: 1px solid #c0c4cc; padding: 20px; margin-bottom: 20px"
    >
      <el-form-item :label="`${modelObj['leftKeyView']}:`">
        <el-input v-model="searchParams.leftKey" placeholder="请输入" />
      </el-form-item>

      <el-form-item :label="`${modelObj['leftNameView']}:`">
        <el-input v-model="searchParams.leftName" placeholder="请输入" />
      </el-form-item>
      <el-form-item :label="`${modelObj['rightValueView']}:`">
        <el-input v-model="searchParams.rightValue" placeholder="请输入" />
      </el-form-item>
      <el-form-item :label="`${modelObj['rightNameView']}:`">
        <el-input v-model="searchParams.rightName" placeholder="请输入" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button type="primary" @click="handleAddEdit(null)">新增</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <el-button
          slot="reference"
          type="text"
          size="small"
          @click="handleAddEdit(scope.row)"
        >
          编辑
        </el-button>
        <!-- <el-button
          slot="reference"
          type="text"
          size="small"
          @click="handleDel(scope.row)"
        >
          删除
        </el-button> -->
        <el-popconfirm title="是否确定删除？" @confirm="handleDel(scope.row)">
          <el-button slot="reference" type="text" size="small">删除</el-button>
        </el-popconfirm>
      </template>
    </dynamictable>
    <relationModal
      v-model="showModal"
      :model-obj="modelObj"
      :cur-row="curRow"
      @onGet="getList"
    />
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import relationModal from './components/relationModal';
  import {
    relationLookupList,
    relationLookupDelete,
    relationLookupModuleSelectModuleMapping,
    relationLookupSelectModule,
  } from '@/api/relationshipMapping';

  export default {
    components: {
      dynamictable,
      relationModal,
    },

    data() {
      return {
        moduleView: '',
        showModal: false,
        curRow: null,
        pageLoading: true,
        searchParams: {
          leftKey: '',
          leftName: '',
          rightValue: '',
          rightName: '',
        },
        list: [],
        modelViveList: [],
        modelObj: {
          leftKeyView: '',
          leftNameView: '',
          rightValueView: '',
          rightNameView: '',
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
      };
    },

    created() {
      relationLookupModuleSelectModuleMapping().then(data => {
        const { res, err } = data;
        if (res && !err) {
          this.moduleView = res[0].id;
          this.modelViveList = res;
          this.getRelationLookupSelectModule(res[0]);
        }
      });
    },

    methods: {
      handleModule(val) {
        this.getRelationLookupSelectModule(val);
      },
      async getRelationLookupSelectModule({ id = '', moduleView = '' }) {
        const { res, err } = await relationLookupSelectModule({
          moduleId: id,
          moduleView: moduleView,
        });
        if (res && !err) {
          this.modelObj = res;
          this.getList(true);
        }
        this.pageLoading = false;
      },
      async handleAddEdit(row) {
        this.curRow = row;
        this.showModal = true;
      },
      async handleDel(val) {
        const { err } = await relationLookupDelete({
          lookupId: val.id,
          moduleId: this.modelObj.id,
        });
        if (!err) {
          this.$message.success('删除成功');
          this.getList();
        }
      },

      getParams() {
        const params = {
          ...this.searchParams,
          moduleId: this.modelObj.id,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const { res, err } = await relationLookupList(params);
        this.options.loading = false;
        if (!err && res) {
          this.list = res ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },
      getColumns() {
        const { modelObj } = this;
        console.log(modelObj, 'modelObj');
        return [
          {
            prop: 'id',
            label: '关系id',
          },
          {
            prop: 'moduleId',
            label: '模型名称',
            render: () => <span>{this.modelObj.moduleView}</span>,
          },
          {
            prop: 'leftKey',
            label: modelObj.leftKeyView,
            key: modelObj.leftKeyView,
          },
          {
            prop: 'leftName',
            label: modelObj.leftNameView,
            key: modelObj.leftNameView,
          },
          {
            prop: 'rightValue',
            label: modelObj.rightValueView,
            key: modelObj.rightValueView,
          },
          {
            prop: 'rightName',
            label: modelObj.rightNameView,
            key: modelObj.rightNameView,
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            minWidth: '150',
            maxWidth: '300',
            scopedSlots: { customRender: 'operation' },
          },
        ];
      },
    },
  };
</script>
<style lang="scss"></style>
