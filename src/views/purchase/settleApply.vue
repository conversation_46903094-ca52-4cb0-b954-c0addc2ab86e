<template>
  <div>
    <div v-if="tab == 1">
      <Form
        :status-list="statusList"
        :invoice-list="invoiceList"
        :has-brand="false"
        :has-purchase="false"
        id-title="结算单号"
        permission-key="settleApply-expot"
        @search="onSearch"
        @export="onExport"
      />
      <div style="text-align: right; margin-bottom: 10px">
        应付总额 {{ totalPayableAmount }} 发票总额 {{ totalInvoiceAmount }}
      </div>
      <el-table :data="tableData" border>
        <el-table-column label="结算单号" prop="settlementCode" />

        <el-table-column label="公司名称" prop="companyName" />
        <!-- <el-table-column label="品牌名称" prop="brandName" /> -->
        <el-table-column label="供应商名称" prop="supplierName" />
        <el-table-column label="应付金额" prop="payableAmount" />
        <el-table-column label="发票金额" prop="receiptAmount" />
        <el-table-column label="创建时间" prop="createdTime" />
        <el-table-column label="发票状态" prop="invoiceStatusDesc" />
        <el-table-column label="状态" prop="statusDesc" />
        <el-table-column width="120px" label="操作">
          <template slot-scope="{ row }">
            <el-button type="text" @click="onDetail(row, 'a2')">查看</el-button>
            <el-button type="text" @click="onDetail(row, 'a3')">明细</el-button>
            <el-button
              v-if="
                row.invoiceStatusDesc === '已来票' &&
                row.statusDesc === '待结算'
              "
              type="text"
              @click="handleSubmit(row)"
            >
              提交
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="form.current"
        :page-sizes="[10, 20, 30, 100]"
        :page-size="form.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <div v-else style="position: relative">
      <el-button
        type="text"
        style="position: absolute; right: 0; z-index: 999"
        @click="tab = 1"
      >
        返回
      </el-button>
      <el-tabs v-model="tab" type="card">
        <el-tab-pane label="详情信息" name="a2">
          <el-divider content-position="left">基础信息</el-divider>
          <div class="info-children">
            <el-form inline label-width="200px" label-position="left">
              <el-form-item label="单号">
                {{ baseInfo.purchaseOrderId }}
              </el-form-item>
              <el-form-item label="公司名称">
                {{ baseInfo.companyName }}
              </el-form-item>
              <el-form-item label="状态">
                {{ baseInfo.statusDesc }}
              </el-form-item>
              <el-form-item label="创建时间">
                {{ baseInfo.createdTime }}
              </el-form-item>
            </el-form>
          </div>
          <SupplierInfo :info="supplyInfo" />
          <MoneyInfo type="settleApply" :info="moneyInfo" />
          <!-- <GoodsInfo :list="goodsInfo" /> -->
          <LogInfo :list="logsInfo" />
        </el-tab-pane>
        <el-tab-pane label="明细信息" name="a3">
          <el-table border :data="detailInfoList">
            <el-table-column prop="reconCode" label="单号" />
            <el-table-column prop="num" label="数量" />
            <el-table-column prop="totalAmount" label="总额" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="开票信息" name="a4">
          <div class="invoice">
            <span v-for="(item, index) in invoiceDataList" :key="index">
              <div class="header long">
                <div class="item">发票类型</div>
                <div class="item">发票号码</div>
                <div class="item">发票代码</div>
                <div class="item">含税金额</div>
                <div class="item">税额</div>
                <div class="item">不含税金额</div>
                <div class="item">发票链接</div>
              </div>
              <div class="long">
                <div class="item">{{ item.invoiceType }}</div>
                <div class="item">{{ item.invoiceNumber }}</div>
                <div class="item">{{ item.invoiceCode }}</div>
                <div class="item">{{ item.totalAmount }}</div>
                <div class="item">{{ item.taxAmount }}</div>
                <div class="item">{{ item.noTaxAmount }}</div>
                <div class="item">
                  <a
                    v-if="item.invoiceUrl"
                    :href="item.invoiceUrl"
                    target="_blank"
                    style="margin-top: 10px"
                  >
                    预览
                  </a>
                </div>
              </div>
              <div class="header long">
                <div class="item item2">货物或应税劳务、服务名称</div>
                <div class="item">规格型号</div>
                <div class="item">单位</div>
                <div class="item">数量</div>
                <div class="item">单价</div>
                <div class="item">金额</div>
                <div class="item">税率</div>
                <div class="item">税额</div>
              </div>
              <div
                v-for="(it, idx) in item.invoiceDetails"
                :key="idx"
                class="long"
              >
                <div class="item item2">{{ it.title }}</div>
                <div class="item">{{ it.model }}</div>
                <div class="item">{{ it.unit }}</div>
                <div class="item">{{ it.number }}</div>
                <div class="item">{{ it.unitPrice }}</div>
                <div class="item">{{ it.amount }}</div>
                <div class="item">{{ it.taxRate }}</div>
                <div class="item">{{ it.taxAmount }}</div>
              </div>
            </span>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
  import Form from './components/Form';
  // import GoodsInfo from './components/GoodsInfo'
  import MoneyInfo from './components/MoneyInfo';
  import LogInfo from './components/LogInfo';
  import SupplierInfo from './components/SupplierInfo';
  import {
    getSettleList,
    getSettleDetail,
    getSettleDetailList,
    getSettleDetailInvoice,
    getPurchaseDict,
    submitOA,
  } from '@/api/purchase';
  import { formatDetail } from './js/index';
  import { exportExcel } from '@/api/blob';
  import { downloadFile } from '@/utils';
  export default {
    components: { Form, SupplierInfo, LogInfo, MoneyInfo },
    data() {
      return {
        form: {
          current: 1,
          size: 10,
        },
        totalInvoiceAmount: 0,
        totalPayableAmount: 0,
        total: 0,
        tableData: [],
        statusList: [],
        invoiceList: [],
        tab: 1,
        baseInfo: {},
        goodsInfo: [],
        logsInfo: [],
        moneyInfo: {},
        supplyInfo: {},
        detailInfoList: [],
        invoiceDataList: [],
        search: {},
      };
    },
    created() {
      getPurchaseDict().then(res => {
        this.invoiceList = res.filter(
          i => i.bizType === 'invoiceStatus',
        )[0].selectors;
        this.statusList = res.filter(
          i => i.bizType === 'settlementStatus',
        )[0].selectors;
      });
    },
    methods: {
      onSearch(e) {
        this.search = e;
        this.form.current = 1;
        this.form.size = 10;
        this.onGetList();
      },
      onGetList(boo = null) {
        if (boo) {
          this.form.current = 1;
        }
        let form = this.search;
        getSettleList({
          ...form,
          page: this.form.current,
          pageSize: this.form.size,
        }).then(res => {
          console.log('settle:', res);
          const { pageList, totalInvoiceAmount, totalPayableAmount } = res;
          this.total = pageList.total;
          this.tableData = pageList.records;
          this.totalInvoiceAmount = totalInvoiceAmount;
          this.totalPayableAmount = totalPayableAmount;
        });
      },
      handleSubmit({ settlementCode }) {
        submitOA({ settlementCode }).then(res => {
          if (res !== undefined) {
            this.$message({
              message: '提交成功',
              type: 'success',
            });
            this.onGetList();
          }
        });
      },
      onDetail(obj, tab) {
        getSettleDetail({
          settlementId: obj.settlementId,
        }).then(res => {
          this.baseInfo = formatDetail(res).baseInfo;
          this.supplyInfo = formatDetail(res).supplyInfo;
          this.moneyInfo = formatDetail(res).moneyInfo;
          this.logsInfo = res.operationLogs;
          this.goodsInfo = res.purchaseSkus;
          this.tab = tab;
        });
        getSettleDetailList({ settlementId: obj.settlementId }).then(res => {
          // 明细信息
          this.detailInfoList = res;
        });
        getSettleDetailInvoice({ settlementId: obj.settlementId }).then(res => {
          // 开票信息
          console.log('开票信息：', res);
          this.invoiceDataList = res;
        });
      },
      handleCurrentChange(e) {
        this.form.current = e;
        this.onGetList();
      },
      handleSizeChange(e) {
        this.form.size = e;
        this.onGetList();
      },
      onExport(e) {
        this.$confirm('确定将该数据导出?', '导出', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          exportExcel(
            e,
            '/api/financial-integration/settlement/export',
            'post',
          ).then(res => {
            downloadFile(res.data, '结算单列表');
          });
        });
      },
    },
  };
</script>
<style lang="scss">
  .invoice {
    width: 100%;
    border: 1px solid #efefef;
    box-sizing: border-box;
    .long {
      width: 100%;
      box-sizing: border-box;
      display: flex;
      border-bottom: 1px solid #efefef;
    }
    .long:nth-last-of-type(1) {
      border-bottom: none;
    }
    .header {
      .item {
        background: #ccc;
      }
    }
    .item {
      flex: 1;
      text-align: center;
      height: 30px;
      line-height: 30px;
      border-right: 1px solid #efefef;
    }
    .item2 {
      flex: 2;
    }
    .item:nth-last-of-type(1) {
      border-right: none;
    }
  }
</style>
