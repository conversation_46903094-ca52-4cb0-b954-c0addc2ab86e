export function formatDetail(res) {
  const baseInfo = {
    // 基础信息
    id: res.id,
    purchaseOrderId:
      res.purchaseOrderId || res.orderCode || res.settlementCode || res.reconNo, // 采购单号
    orderCode: res.orderCode, // 销售单号特殊用
    storageOrderCode: res.storageOrderCode, // 销售原单号
    companyName: res.companyName || res.companyLegalName,
    brandCode: res.brandCode, // 品牌id
    brandName: res.brandName, // 品牌名称
    brandGroupId: res.brandGroupId, // 品牌组id
    brandGroupName: res.brandGroupName,
    statusDesc: res.statusDesc || res.statusStr,
    title: res.title, // 单号名称
    createdTime: res.createdTime, // 创建时间
    warehouseName: res.warehouseName, // 仓库名称
    warehouseId: res.warehouseId, // 仓库id
    businessTypeDesc: res.businessTypeDesc, // 业务类型
    inStorageTime: res.inStorageTime, // 入库时间
    purchaseOrderCode: res.purchaseOrderCode, // 关联采购单号
    sourceCreateTime: res.sourceCreateTime, // 下单时间
    outStorageTime: res.outStorageTime, // 出库时间
    originalStorageTime: res.originalStorageTime, // 原出库时间
  };
  const supplyInfo = {
    supplierCode: res.supplierCode, // 供应商id
    supplierName: res.supplierName,
    bankName: res.bankName, // 开户行
    bankBranceName: res.bankBranceName || res.bankBranchName, // 开户支行
    accNo: res.accNo, // 供应商账号
    largeBankNo: res.largeBankNo, // 供应商开户行大额行号
    oppositeCompanyCode: res.oppositeCompanyCode, // 对方公司id
    oppositeCompanyName: res.oppositeCompanyName, // 对方公司名称
  };
  const moneyInfo = {
    amount: res.amount || res.payableAmount, // 应付总额
    realPayAmount: res.realPayAmount || res.paidAmount || '', // 实付金额
    realPayTime: res.realPayTime || res.paymentTime, // 实付时间
    receiptAmount: res.receiptAmount || '', // 发票金额
    expectedTime: res.expectedTime || '', // 预计付款日期
    purchaseNum: res.purchaseNum, //采购数量
    deliveryNum: res.deliveryNum, // 发货数量
    receivedNum: res.receivedNum, //收货数量
    deliveryStatusDesc: res.deliveryStatusDesc, //发货状态
    arrivalStatusDesc: res.arrivalStatusDesc, //收货状态
    totalQuantity: res.totalQuantity, //商品数量
    totalAmount: res.totalAmount, //商品总额
    totalActualQuantity: res.totalActualQuantity, //商品总数
    actualQuantity: res.actualQuantity, // 入库数量
  };
  return {
    baseInfo,
    supplyInfo,
    moneyInfo,
  };
}
