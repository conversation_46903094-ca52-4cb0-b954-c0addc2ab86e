<template>
  <div>
    <div v-if="tab === 1">
      <Form
        :has-import="true"
        :has-purchase="false"
        :has-buy="true"
        :has-supply="false"
        :depot="true"
        :warehousing-date="true"
        warehousing-type="saleInfo"
        permission-key="saleInfo-export"
        download-permission-key="saleInfo-download"
        id-title="销售单号"
        :has-brand="false"
        :status-list="statusList"
        @search="onSearch"
        @export="onExport"
      />
      <div style="text-align: right; margin-bottom: 10px">
        应付总额 {{ totalAmount }} 商品数量 {{ totalNum }}
      </div>
      <el-table :data="tableData" border>
        <el-table-column label="销售单号" prop="orderCode" />

        <el-table-column label="公司名称" prop="companyName" />
        <el-table-column label="仓库名称" prop="warehouseName" />

        <!-- <el-table-column label="品牌名称" prop="brandName" /> -->
        <el-table-column label="对方名称" prop="oppositeCompanyName" />
        <el-table-column label="原入库时间" prop="originalStorageTime" />
        <el-table-column label="总金额" prop="totalAmount" />
        <el-table-column label="商品数量" prop="totalQuantity" />
        <el-table-column label="创建时间" prop="createdTime" min-width="160" />
        <el-table-column label="状态" prop="statusDesc" />
        <el-table-column label="操作">
          <template slot-scope="{ row }">
            <el-button type="text" @click="onDetail(row)">查看</el-button>
            <ac-permission-button
              v-if="row.statusDesc != '已出库'"
              btn-text="编辑"
              permission-key="saleInfo-edit"
              type="text"
              @click="onEdit(row)"
            ></ac-permission-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="form.current"
        :page-sizes="[10, 20, 30, 100]"
        :page-size="form.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <div v-if="tab === 2">
      <div style="margin-top: 20px">
        <span>基础信息</span>
        <el-button type="text" style="float: right" @click="tab = 1">
          返回
        </el-button>
      </div>
      <el-divider content-position="left"></el-divider>
      <div class="info-children">
        <el-form inline label-width="200px" label-position="left">
          <el-form-item label="ID">{{ baseInfo.orderCode }}</el-form-item>
          <el-form-item label="原单号">
            {{ baseInfo.storageOrderCode }}
          </el-form-item>
          <!-- <el-form-item label="单号名称">
            {{ baseInfo.title }}
          </el-form-item> -->
          <el-form-item label="公司名称">
            {{ baseInfo.companyName }}
          </el-form-item>
          <el-form-item label="仓库ID">{{ baseInfo.warehouseId }}</el-form-item>
          <el-form-item label="仓库名称">
            {{ baseInfo.warehouseName }}
          </el-form-item>
          <!-- <el-form-item label="品牌ID">{{ baseInfo.brandCode }}</el-form-item>
          <el-form-item label="品牌名称">{{ baseInfo.brandName }}</el-form-item> -->
          <!-- <el-form-item label="品牌组ID">
            {{ baseInfo.brandGroupId }}
          </el-form-item>
          <el-form-item label="品牌组名称">
            {{ baseInfo.brandGroupName }}
          </el-form-item> -->
          <el-form-item label="状态">{{ baseInfo.statusDesc }}</el-form-item>
          <el-form-item label="业务类型">
            {{ baseInfo.businessTypeDesc }}
          </el-form-item>
          <el-form-item label="创建时间">
            {{ baseInfo.createdTime }}
          </el-form-item>
          <el-form-item label="出库时间">
            {{ baseInfo.outStorageTime }}
          </el-form-item>
          <el-form-item label="原出库时间">
            {{ baseInfo.originalStorageTime }}
          </el-form-item>
        </el-form>
      </div>
      <SupplierInfo type="saleInfo" :info="supplyInfo" />
      <MoneyInfo type="saleInfo" :info="moneyInfo" />
      <GoodsInfo :list="goodsInfo" :is-edit="isEdit" @change="onPriceChange" />
      <LogInfo :list="logsInfo" />
      <div v-if="isEdit" class="edit-button">
        <el-button @click="onEditConfirm(false)">取消</el-button>
        <el-button type="primary" @click="onEditConfirm(true)">确认</el-button>
      </div>
    </div>
  </div>
</template>
<script>
  import Form from './components/Form';
  import GoodsInfo from './components/GoodsInfo';
  import LogInfo from './components/LogInfo';
  import MoneyInfo from './components/MoneyInfo';
  import SupplierInfo from './components/SupplierInfo';
  import {
    getSaleList,
    getSaleDetail,
    editSalePrice,
    getPurchaseDict,
  } from '@/api/purchase';
  import { formatDetail } from './js/index';
  import { exportExcel } from '@/api/blob';
  import { downloadFile } from '@/utils';
  export default {
    components: { Form, GoodsInfo, SupplierInfo, LogInfo, MoneyInfo },
    data() {
      return {
        form: {
          current: 1,
          size: 10,
        },
        totalAmount: 0,
        totalNum: 0,
        total: 0,
        tableData: [],
        tab: 1,
        moneyInfo: {},
        baseInfo: {}, //基础信息-详情
        supplyInfo: {}, // 供应商信息-详情
        goodsInfo: [], // 商品信息-详情
        logsInfo: [], // 日志信息
        isEdit: false,
        editGoodsInfo: [],
        detailId: '',
        statusList: [],
        search: {},
      };
    },
    created() {
      getPurchaseDict().then(res => {
        this.statusList = res.filter(
          i => i.bizType === 'saleStatus',
        )[0].selectors;
      });
    },
    methods: {
      onSearch(e) {
        this.search = e;
        this.form.current = 1;
        this.form.size = 10;
        this.onGetList();
      },
      onGetList(boo = null) {
        if (boo) {
          this.form.current = 1;
        }
        let form = this.search;
        getSaleList({
          ...form,
          saleOrderId: form.id, // 销售单号
          page: this.form.current,
          pageSize: this.form.size,
        }).then(res => {
          const { page, totalAmount, totalNum } = res;
          this.total = page.total;
          this.tableData = page.records;
          this.totalNum = totalNum;
          this.totalAmount = totalAmount;
        });
      },
      onDetail(obj, isEdit = false) {
        this.detailId = obj.id;
        getSaleDetail({ id: obj.id }).then(res => {
          const detail = formatDetail(res);
          this.moneyInfo = formatDetail(res).moneyInfo;
          this.baseInfo = detail.baseInfo;
          this.supplyInfo = detail.supplyInfo;
          this.goodsInfo = res.skuList;
          this.logsInfo = res.operationLogs;
          this.tab = 2;
          this.isEdit = isEdit;
        });
      },
      onEdit(obj) {
        this.onDetail(obj, true);
      },
      onPriceChange(e) {
        this.editGoodsInfo = e;
      },
      handleCurrentChange(e) {
        this.form.current = e;
        this.onGetList();
      },
      handleSizeChange(e) {
        this.form.size = e;
        this.onGetList();
      },
      onEditConfirm(e) {
        if (!e) {
          // 取消
          this.tab = 1;
        } else {
          const list = this.editGoodsInfo.map(i => {
            return {
              saleOrderId: i.saleOrderNo,
              goodsCode: i.goodsCode,
              salePrice: i.salePrice,
            };
          });
          let check = false;
          if (list.length > 0) {
            check = list.every(i => i.salePrice > 0 || i.salePrice == '0');
          } else {
            check = false;
          }
          if (!check) {
            this.$message.error('销售单价必须大于等于0');
            return;
          }
          editSalePrice({ list, id: this.detailId }).then(() => {
            this.$message.success('价格修改成功');
            this.tab = 1;
          });
        }
      },
      onExport(e) {
        this.$confirm('确定将该数据导出?', '导出', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          exportExcel(
            {
              ...e,
              saleOrderId: e.id,
            },
            '/api/financial-integration/saleOrder/export',
            'post',
          ).then(res => {
            downloadFile(res.data, '销售单列表');
          });
        });
      },
    },
  };
</script>
<style lang="scss">
  .edit-button {
    padding: 20px 0;
    display: flex;
    justify-content: center;
  }
</style>
