<template>
  <div>
    <div v-if="tab === 1">
      <Form
        :deliver-goods-status="deliverGoodsStatus"
        :receiving-goods-status="receivingGoodsStatus"
        permission-key="purchaseApply-export"
        @search="onSearch"
        @export="onExport"
      />
      <div style="text-align: right; margin-bottom: 10px">
        采购总数 {{ totalPurchaseNum }} 发货总数 {{ totalDeliveryNum }} 到货总数
        {{ totalReceivedNum }}
      </div>
      <el-table :data="tableData" border>
        <el-table-column label="ID" prop="id" />
        <el-table-column label="采购单号" prop="orderCode" />
        <el-table-column label="单号名称" prop="title" width="160" />
        <el-table-column label="公司名称" prop="companyName" width="160" />
        <el-table-column label="品牌名称" prop="brandName" />
        <el-table-column
          label="供应商名称"
          prop="supplierName"
          min-width="160"
        />
        <el-table-column label="创建时间" prop="createdTime" min-width="160" />
        <el-table-column label="采购数量" prop="purchaseNum" min-width="160" />
        <el-table-column label="发货数量" prop="deliveryNum" min-width="160" />
        <el-table-column
          label="发货状态"
          prop="deliveryStatusDesc"
          min-width="160"
        />
        <el-table-column label="到货数量" prop="receivedNum" min-width="160" />
        <el-table-column
          label="到货状态"
          prop="arrivalStatusDesc"
          min-width="160"
        />
        <el-table-column fixed="right" label="操作">
          <template slot-scope="{ row }">
            <el-button type="text" @click="onDetail(row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="form.current"
        :page-sizes="[10, 20, 30, 100]"
        :page-size="form.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <div v-if="tab === 2">
      <div style="margin-top: 20px">
        <span>基础信息</span>
        <el-button type="text" style="float: right" @click="tab = 1">
          返回
        </el-button>
      </div>
      <el-divider content-position="left"></el-divider>
      <div class="info-children">
        <el-form inline label-width="200px" label-position="left">
          <el-form-item label="ID">{{ baseInfo.id }}</el-form-item>
          <el-form-item label="采购订单号">
            {{ baseInfo.purchaseOrderId }}
          </el-form-item>
          <el-form-item label="单号名称">{{ baseInfo.title }}</el-form-item>
          <el-form-item label="公司名称">
            {{ baseInfo.companyName }}
          </el-form-item>
          <el-form-item label="品牌ID">{{ baseInfo.brandCode }}</el-form-item>
          <el-form-item label="品牌名称">{{ baseInfo.brandName }}</el-form-item>
          <!-- <el-form-item label="品牌组ID">
            {{ baseInfo.brandGroupId }}
          </el-form-item>
          <el-form-item label="品牌组名称">
            {{ baseInfo.brandGroupName }}
          </el-form-item> -->
          <el-form-item label="状态">{{ baseInfo.statusDesc }}</el-form-item>
          <el-form-item label="创建时间">
            {{ baseInfo.createdTime }}
          </el-form-item>
          <el-form-item label="下单日期">
            {{ baseInfo.sourceCreateTime }}
          </el-form-item>
        </el-form>
      </div>
      <SupplierInfo :info="supplyInfo" />
      <MoneyInfo type="purchaseApply" :info="moneyInfo" />
      <GoodsInfo :list="goodsInfo" />
      <LogInfo :list="logsInfo" />
    </div>
  </div>
</template>
<script>
  import Form from './components/Form';
  import GoodsInfo from './components/GoodsInfo';
  import MoneyInfo from './components/MoneyInfo';
  import LogInfo from './components/LogInfo';
  import SupplierInfo from './components/SupplierInfo';
  import {
    getPurchaseApplyList,
    getPurchaseApplyDetail,
    getPurchaseDict,
  } from '@/api/purchase';
  import { formatDetail } from './js/index';
  import { exportExcel } from '@/api/blob';
  import { downloadFile } from '@/utils';
  export default {
    components: { Form, GoodsInfo, SupplierInfo, LogInfo, MoneyInfo },
    data() {
      return {
        deliverGoodsStatus: [],
        receivingGoodsStatus: [],
        form: {
          current: 1,
          size: 10,
        },
        total: 0,
        tableData: [],
        totalDeliveryNum: '',
        totalPurchaseNum: '',
        totalReceivedNum: '',
        tab: 1,
        baseInfo: {},
        goodsInfo: [],
        logsInfo: [],
        moneyInfo: {},
        supplyInfo: {},
        search: {},
      };
    },
    created() {
      getPurchaseDict().then(res => {
        this.deliverGoodsStatus = res.filter(
          i => i.bizType === 'deliveryStatus',
        )[0].selectors;
        this.receivingGoodsStatus = res.filter(
          i => i.bizType === 'arrivalStatus',
        )[0].selectors;
      });
    },
    methods: {
      onSearch(e) {
        this.search = e;
        this.form.current = 1;
        this.form.size = 10;
        this.onGetList();
      },
      onGetList(boo = null) {
        if (boo) {
          this.form.current = 1;
        }
        let form = this.search;
        delete form.status;
        getPurchaseApplyList({
          ...form,
          // orderCode: form.id,
          page: this.form.current,
          pageSize: this.form.size,
        }).then(res => {
          const {
            page,
            totalDeliveryNum = 0,
            totalPurchaseNum = 0,
            totalReceivedNum = 0,
          } = res;
          this.total = page.total;
          this.tableData = page.records;
          this.totalDeliveryNum = totalDeliveryNum;
          this.totalPurchaseNum = totalPurchaseNum;
          this.totalReceivedNum = totalReceivedNum;
        });
      },
      onDetail(obj) {
        getPurchaseApplyDetail({
          purchaseOrderId: obj.id,
        }).then(res => {
          this.baseInfo = formatDetail(res).baseInfo;
          this.supplyInfo = formatDetail(res).supplyInfo;
          this.moneyInfo = formatDetail(res).moneyInfo;
          this.logsInfo = res.operationLogs;
          this.goodsInfo = res.purchaseSkus;
          this.tab = 2;
          console.log(1212, res, this.moneyInfo);
        });
      },
      handleCurrentChange(e) {
        this.form.current = e;
        this.onGetList();
      },
      handleSizeChange(e) {
        this.form.size = e;
        this.onGetList();
      },
      onExport(e) {
        this.$confirm('确定将该数据导出?', '导出', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          exportExcel(
            e,
            '/api/financial-integration/purchaseOrder/export',
            'post',
          ).then(res => {
            downloadFile(res.data, '采购申请列表');
          });
        });
      },
    },
  };
</script>
