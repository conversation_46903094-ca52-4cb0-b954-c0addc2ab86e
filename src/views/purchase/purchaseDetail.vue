<template>
  <div>
    <div v-if="tab === 1">
      <Form
        :depot="true"
        :warehousing-date="true"
        :has-brand="false"
        :has-purchase="false"
        id-title="入库单号"
        :status-list="statusList"
        permission-key="purchaseDetail-export"
        @search="onSearch"
        @export="onExport"
      />
      <div style="text-align: right; margin-bottom: 10px">
        应付总额 {{ totalPayableAmount }} 商品总数 {{ totalGoodsNumber }}
      </div>
      <el-table :data="tableData" border>
        <el-table-column label="入库单号" prop="orderCode" />
        <!-- <el-table-column label="单号名称" prop="title" /> -->
        <el-table-column label="公司名称" prop="companyLegalName" />
        <el-table-column label="仓库名称" prop="warehouseName" />
        <!-- <el-table-column label="品牌名称" prop="brandName" /> -->
        <el-table-column label="供应商名称" prop="supplierName" />
        <el-table-column label="应付金额" prop="payableAmount" />
        <el-table-column label="商品数量" prop="totalQuantity" />
        <el-table-column label="实付金额" prop="realPayAmount" />
        <el-table-column label="创建时间" prop="createdTime" />
        <el-table-column label="入库时间" prop="inStorageTime" />
        <el-table-column label="状态" prop="statusDesc" />
        <el-table-column label="操作">
          <template slot-scope="{ row }">
            <el-button type="text" @click="onDetail(row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="form.current"
        :page-sizes="[10, 20, 30, 100]"
        :page-size="form.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <div v-if="tab === 2">
      <div style="margin-top: 20px">
        <span>基础信息</span>
        <el-button type="text" style="float: right" @click="tab = 1">
          返回
        </el-button>
      </div>
      <el-divider content-position="left"></el-divider>
      <div class="info-children">
        <el-form inline label-width="200px" label-position="left">
          <el-form-item label="单号">
            {{ baseInfo.storageOrderCode }}
          </el-form-item>
          <!-- <el-form-item label="单号名称">{{ baseInfo.title }}</el-form-item> -->
          <el-form-item label="关联采购单号">
            {{ baseInfo.purchaseOrderCode }}
          </el-form-item>
          <el-form-item label="仓库ID">{{ baseInfo.warehouseId }}</el-form-item>
          <el-form-item label="仓库名称">
            {{ baseInfo.warehouseName }}
          </el-form-item>
          <el-form-item label="公司名称">
            {{ baseInfo.companyName }}
          </el-form-item>
          <el-form-item label="状态">{{ baseInfo.statusDesc }}</el-form-item>
          <el-form-item label="业务类型">
            {{ baseInfo.businessTypeDesc }}
          </el-form-item>
          <el-form-item label="入库时间">
            {{ baseInfo.inStorageTime }}
          </el-form-item>
          <el-form-item label="创建时间">
            {{ baseInfo.createdTime }}
          </el-form-item>
        </el-form>
      </div>
      <SupplierInfo :info="supplyInfo" />
      <MoneyInfo type="purchaseDetail" :info="moneyInfo" />
      <GoodsInfo :list="goodsInfo" />
      <LogInfo :list="logsInfo" />
    </div>
  </div>
</template>
<script>
  import Form from './components/Form';
  import GoodsInfo from './components/GoodsInfo';
  import MoneyInfo from './components/MoneyInfo';
  import LogInfo from './components/LogInfo';
  import SupplierInfo from './components/SupplierInfo';
  import {
    getPurchaseApplyDetailList,
    getPurchaseApplyDetailDetail,
    getPurchaseDict,
  } from '@/api/purchase';
  import { formatDetail } from './js/index';
  import { exportExcel } from '@/api/blob';
  import { downloadFile } from '@/utils';
  export default {
    components: { Form, GoodsInfo, SupplierInfo, LogInfo, MoneyInfo },
    data() {
      return {
        form: {
          current: 1,
          size: 10,
        },
        totalGoodsNumber: 0,
        totalPayableAmount: 0,
        total: 0,
        tableData: [],
        statusList: [],
        tab: 1,
        baseInfo: {},
        goodsInfo: [],
        logsInfo: [],
        moneyInfo: {},
        supplyInfo: {},
        search: {},
      };
    },
    created() {
      // this.onGetList()
      getPurchaseDict().then(res => {
        this.statusList = res.filter(
          i => i.bizType === 'pasPurchaseStatus',
        )[0].selectors;
      });
    },
    methods: {
      onSearch(e) {
        this.search = e;
        this.form.current = 1;
        this.form.size = 10;
        this.onGetList();
      },
      onGetList(boo = null) {
        if (boo) {
          this.form.current = 1;
        }
        let form = this.search;
        getPurchaseApplyDetailList({
          ...form,
          storageOrderCode: form.id,
          page: this.form.current,
          pageSize: this.form.size,
        }).then(res => {
          const { pageList, totalGoodsNumber, totalPayableAmount } = res;
          this.total = pageList.total;
          this.tableData = pageList.records;
          this.totalGoodsNumber = totalGoodsNumber;
          this.totalPayableAmount = totalPayableAmount;
        });
      },
      onDetail(obj) {
        getPurchaseApplyDetailDetail({
          id: obj.id,
        }).then(res => {
          console.log(1212, res);
          this.baseInfo = formatDetail(res).baseInfo;
          this.supplyInfo = formatDetail(res).supplyInfo;
          this.moneyInfo = formatDetail(res).moneyInfo;
          this.logsInfo = res.operationLogs;
          this.goodsInfo = res.skuList;
          this.tab = 2;
        });
      },
      handleCurrentChange(e) {
        this.form.current = e;
        this.onGetList();
      },
      handleSizeChange(e) {
        this.form.size = e;
        this.onGetList();
      },
      onExport(e) {
        this.$confirm('确定将该数据导出?', '导出', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          exportExcel(
            {
              ...e,
              storageOrderCode: e.id,
            },
            '/api/financial-integration/pasPurchaseOrder/export',
            'post',
          ).then(res => {
            downloadFile(res.data, '采购明细列表');
          });
        });
      },
    },
  };
</script>
