<template>
  <div class="info-children">
    <el-divider content-position="left">商品信息</el-divider>
    <el-table border :data="newList.length > 0 ? newList : list">
      <el-table-column
        v-if="code"
        prop="storageOrderCode"
        label="采购明细单号"
        width="200px"
      />
      <el-table-column prop="brandCode" label="品牌ID" />
      <el-table-column prop="brandName" label="品牌名称" />
      <!-- <el-table-column prop="brandGroupId" label="品牌组ID" />
      <el-table-column prop="brandGroupName" label="品牌组名称" /> -->
      <el-table-column prop="goodsCode" label="商品编码" />
      <el-table-column prop="goodsName" label="商品名称" />
      <el-table-column prop="goodsSpec" label="商品规格">
        <template slot-scope="{ row }">
          {{ row.goodsSpec || row.boxGauge }}
        </template>
      </el-table-column>
      <el-table-column label="商品品级">
        <template slot-scope="{ row }">
          {{ row.inventoryTypeStr || row.inventoryTypeDesc }}
        </template>
      </el-table-column>
      <el-table-column prop="purchasePrice" label="采购单价" />
      <el-table-column prop="salePrice" label="销售单价">
        <template slot-scope="{ row }">
          <el-input
            v-if="isEdit"
            v-model="row.salePrice"
            type="number"
            @input="onEdit"
          ></el-input>
          <span v-else>{{ row.salePrice }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="num" label="数量">
        <template slot-scope="{ row }">
          {{ row.num || row.actualQuantity || row.actualPurchaseNum }}
        </template>
      </el-table-column>
      <el-table-column prop="totalAmount" label="总额" />
      <el-table-column prop="remark" label="备注" />
    </el-table>
  </div>
</template>
<script>
  export default {
    props: {
      list: {
        type: Array,
        default: () => [],
      },
      isEdit: {
        type: Boolean,
        default: false,
      },
      isStr: {
        type: Boolean,
        default: false,
      },
      code: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        newList: [],
      };
    },
    watch: {
      isEdit(cur) {
        if (cur) {
          this.newList = this.list.map(i => {
            return {
              ...i,
              salePrice: 0,
            };
          });
        }
      },
    },
    mounted() {
      if (this.isEdit) {
        // 编辑售价默认为''
        this.newList = this.list.map(i => {
          return {
            ...i,
            salePrice: '',
          };
        });
      }
    },
    methods: {
      onEdit(e) {
        this.$emit(
          'change',
          this.newList.length > 0 ? this.newList : this.list,
        );
      },
    },
  };
</script>
