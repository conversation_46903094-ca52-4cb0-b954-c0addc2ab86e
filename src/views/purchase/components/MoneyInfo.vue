<template>
  <div class="info-children">
    <el-divider content-position="left">汇总信息</el-divider>
    <el-form inline label-width="200px" label-position="left">
      <el-form-item v-if="type !== 'saleInfo'" label="应付金额">
        <span>{{ info.amount }}</span>
      </el-form-item>
      <el-form-item v-if="type === 'saleInfo'" label="商品总额">
        <span>{{ info.totalAmount }}</span>
      </el-form-item>
      <el-form-item v-if="type === 'saleInfo'" label="商品总数">
        <span>{{ info.totalActualQuantity }}</span>
      </el-form-item>
      <el-form-item v-if="type === 'purchaseApply'" label="采购数量">
        <span>{{ info.purchaseNum }}</span>
      </el-form-item>
      <el-form-item v-if="type === 'purchaseApply'" label="发货数量">
        <span>{{ info.deliveryNum }}</span>
      </el-form-item>
      <el-form-item v-if="type === 'purchaseApply'" label="发货状态">
        <span>{{ info.deliveryStatusDesc }}</span>
      </el-form-item>
      <el-form-item v-if="type === 'purchaseApply'" label="收货数量">
        <span>{{ info.receivedNum }}</span>
      </el-form-item>
      <el-form-item v-if="type === 'purchaseApply'" label="收货状态">
        <span>{{ info.arrivalStatusDesc }}</span>
      </el-form-item>
      <el-form-item v-if="type === 'settleApply'" label="发票金额">
        <span>{{ info.receiptAmount }}</span>
      </el-form-item>
      <el-form-item
        v-if="['purchaseDetail', 'letterInfo', 'settleApply'].includes(type)"
        label="实付金额"
      >
        <span>{{ info.realPayAmount }}</span>
      </el-form-item>
      <el-form-item v-if="type === 'settleApply'" label="预计付款日期">
        <span>{{ info.expectedTime }}</span>
      </el-form-item>
      <el-form-item
        v-if="['purchaseDetail', 'letterInfo', 'settleApply'].includes(type)"
        label="实付日期"
      >
        <span>{{ info.realPayTime }}</span>
      </el-form-item>
      <el-form-item v-if="type === 'letterInfo'" label="商品数量">
        <span>{{ info.totalQuantity }}</span>
      </el-form-item>
      <el-form-item v-if="type === 'purchaseDetail'" label="入库数量">
        <span>{{ info.actualQuantity }}</span>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
  export default {
    props: {
      info: {
        type: Object,
        default: () => {},
      },
      type: {
        type: String,
        default: '',
      },
    },
  };
</script>
