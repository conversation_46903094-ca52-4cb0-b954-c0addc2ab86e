<template>
  <div class="common-form">
    <el-form inline>
      <el-form-item :label="idTitle + ':'">
        <el-input v-model="form.id" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item v-if="hasPurchase" label="采购单号:">
        <el-input
          v-model="form.orderCode"
          placeholder="请输入采购单号"
          clearable
        />
      </el-form-item>
      <el-form-item label="公司名称:">
        <el-input
          v-model="form.companyName"
          placeholder="输入公司名称"
          clearable
        />
      </el-form-item>
      <el-form-item v-if="hasBrand" label="品牌名称:">
        <el-input
          v-model="form.brandName"
          placeholder="输入品牌名称"
          clearable
        />
      </el-form-item>
      <el-form-item v-if="hasSupply" label="供应商名称:">
        <el-input
          v-model="form.supplierName"
          placeholder="请输入供应商名称"
          clearable
        />
      </el-form-item>
      <el-form-item v-if="hasBuy" label="对方名称:">
        <el-input
          v-model="form.buyCompanyName"
          placeholder="对方名称"
          clearable
        />
      </el-form-item>
      <el-form-item v-if="depot" label="仓库名称:">
        <el-input
          v-model="form.warehouseName"
          placeholder="请输入仓库名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="创建时间:">
        <el-date-picker
          v-model="form.date"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item
        v-if="warehousingDate"
        :label="warehousingType === 'saleInfo' ? '原入库时间:' : '入库时间:'"
      >
        <el-date-picker
          v-model="form.warehousingDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item v-if="deliverGoodsStatus.length > 0" label="发货状态:">
        <el-select v-model="form.deliveryStatus" clearable>
          <el-option
            v-for="item in deliverGoodsStatus"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="receivingGoodsStatus.length > 0" label="收货状态:">
        <el-select v-model="form.arrivalStatus" clearable>
          <el-option
            v-for="item in receivingGoodsStatus"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="invoiceList.length > 0" label="发票状态:">
        <el-select v-model="form.invoiceStatus" clearable>
          <el-option
            v-for="item in invoiceList"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="statusList.length > 0" label="状态:">
        <el-select v-model="form.status" clearable="">
          <el-option
            v-for="item in statusList"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button @click="onReset">重置</el-button>
        <ac-permission-button
          btn-text="导出"
          :permission-key="permissionKey"
          type="danger"
          @click="onExport"
        ></ac-permission-button>
        <el-upload
          :action="action"
          :headers="headers"
          style="margin-left: 10px; display: inline"
          :on-success="onImport"
          :http-request="onUpload"
        >
          <el-button v-if="hasImport" type="primary">导入价格</el-button>
        </el-upload>
        <ac-permission-button
          v-if="hasImport"
          btn-text="下载导入模板"
          :permission-key="downloadPermissionKey"
          style="margin-left: 10px"
          type="warning"
          @click="onDownload"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
  import { getCookie } from '@/utils/auth';
  import { downloadFile } from '@/utils';
  import { exportExcel } from '@/api/blob';
  export default {
    props: {
      statusList: {
        type: Array,
        default: () => [],
      },
      invoiceList: {
        type: Array,
        default: () => [],
      },
      deliverGoodsStatus: {
        type: Array,
        default: () => [],
      },
      receivingGoodsStatus: {
        type: Array,
        default: () => [],
      },
      hasImport: {
        type: Boolean,
        default: false,
      },
      depot: {
        type: Boolean,
        default: false,
      },
      warehousingDate: {
        type: Boolean,
        default: false,
      },
      hasPurchase: {
        type: Boolean,
        default: true,
      },
      hasSupply: {
        type: Boolean,
        default: true,
      },
      hasBrand: {
        type: Boolean,
        default: true,
      },
      hasBuy: {
        type: Boolean,
        default: false,
      },
      download: {
        type: Boolean,
        default: false,
      },
      idTitle: {
        type: String,
        default: 'ID',
      },
      warehousingType: {
        type: String,
        default: '',
      },
      permissionKey: {
        type: String,
        default: '',
      },
      downloadPermissionKey: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        formModal: {
          id: '', // id
          companyName: '',
          brandName: '',
          supplierName: '', // 供应商名称
          buyCompanyName: '', // 对方名称
          orderCode: '', // 采购单号
          warehouseName: '', // 仓库名称
          invoiceStatus: '', // 发票状态
          status: '', // 状态，默认全部
          date: [],
          warehousingDate: [], // 入库时间
          depot: '',
          arrivalStatus: '',
          deliveryStatus: '',
        },
        form: {},
        action: '',
        headers: {
          token: getCookie(),
          appCode: process.env.VUE_APP_LOGIN_APP_CODE,
        },
      };
    },
    created() {
      const dayjs = require('dayjs');
      const now = dayjs().format('YYYY-MM-DD');
      const before = dayjs().subtract(30, 'day').format('YYYY-MM-DD');
      this.formModal.date = [before, now];
      this.action =
        '//' +
        window.ACCESS_HOSTS.apiHost +
        '/api/financial-integration/saleOrder/importSalePrice';

      // headers加参数
      let Base64 = require('js-base64').Base64;
      this.headers['userName'] = Base64.encode(this.$store.state.user.username);
      this.headers['userId'] = this.$store.state.user.userInfo.id;

      this.onReset();
    },
    methods: {
      onSearch() {
        this.$emit('search', this.formatForm(this.form));
      },
      onExport() {
        this.$emit('export', this.formatForm(this.form));
      },
      onDownload() {
        window.location.href =
          'https://dc-pay.obs.cn-east-3.myhuaweicloud.com/templates/sale-price-import-template.xlsx';
      },
      onUpload(e) {
        const formData = new FormData();
        formData.append('file', e.file);
        exportExcel(
          formData,
          '/api/financial-integration/saleOrder/importSalePrice',
          'post',
        ).then(res => {
          downloadFile(res.data);
          this.onSearch();
        });
      },
      onImport(e) {
        if (e.success) {
          this.$message.success('导入成功');
          this.onSearch();
        } else {
          this.$message.success('导入失败');
          downloadFile(e, '导入失败数据');
        }
      },
      onReset() {
        this.form = JSON.parse(JSON.stringify(this.formModal));
        this.onSearch();
      },
      formatForm(obj) {
        const timeStart =
          this.warehousingType === 'saleInfo'
            ? 'originalStorageTimeBegin'
            : 'inStorageBeginTime';
        const timeEnd =
          this.warehousingType === 'saleInfo'
            ? 'originalStorageTimeEnd'
            : 'inStorageEndTime';
        return {
          id: obj.id, // id
          companyName: obj.companyName,
          brandName: obj.brandName,
          supplierName: obj.supplierName, // 供应商名称
          orderCode: obj.orderCode, // 采购单号
          warehouseName: obj.warehouseName, // 仓库名
          status: obj.status,
          invoiceStatus: obj.invoiceStatus,
          createdBeginDate: (obj.date && obj.date[0]) || '',
          createdEndDate: (obj.date && obj.date[1]) || '',
          [timeStart]: (obj.warehousingDate && obj.warehousingDate[0]) || '',
          [timeEnd]: (obj.warehousingDate && obj.warehousingDate[1]) || '',
          buyCompanyName: obj.buyCompanyName, // 对方名称
          arrivalStatus: obj.arrivalStatus, // 发货状态
          deliveryStatus: obj.deliveryStatus, // 到货状态
        };
      },
    },
  };
</script>
<style lang="scss">
  .common-form {
    .el-upload-list {
      display: none !important;
    }
  }
</style>
