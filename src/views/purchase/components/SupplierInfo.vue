<template>
  <div class="info-children">
    <el-divider content-position="left">
      {{ type === 'saleInfo' ? '对方信息' : '供应商信息' }}
    </el-divider>
    <el-form inline label-width="200px" label-position="left">
      <el-form-item :label="type === 'saleInfo' ? '对方ID' : '供应商ID'">
        <span>
          {{
            type === 'saleInfo' ? info.oppositeCompanyCode : info.supplierCode
          }}
        </span>
      </el-form-item>
      <el-form-item :label="type === 'saleInfo' ? '对方名称' : '供应商名称'">
        <span>
          {{
            type === 'saleInfo' ? info.oppositeCompanyName : info.supplierName
          }}
        </span>
      </el-form-item>
      <el-divider v-if="type === 'saleInfo'" content-position="left">
        我方账户信息
      </el-divider>
      <el-form-item :label="type === 'saleInfo' ? '开户行' : '供应商开户行'">
        <span>{{ info.bankName }}</span>
      </el-form-item>
      <el-form-item :label="type === 'saleInfo' ? '账号' : '供应商账号'">
        <span>{{ info.accNo }}</span>
      </el-form-item>
      <el-form-item
        :label="type === 'saleInfo' ? '开户支行名称' : '供应商开户支行名称'"
      >
        <span>{{ info.bankBranceName }}</span>
      </el-form-item>
      <el-form-item
        :label="type === 'saleInfo' ? '开户行大额行号' : '供应商开户行大额行号'"
      >
        <span>{{ info.largeBankNo }}</span>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
  export default {
    props: {
      info: {
        type: Object,
        default: () => {},
      },
      type: {
        type: String,
        default: '',
      },
    },
  };
</script>
<style lang="scss">
  .info-children {
    margin-top: 20px;
    .el-form-item {
      width: 45%;
      min-width: 400px;
      margin-bottom: 5px;
    }
  }
</style>
