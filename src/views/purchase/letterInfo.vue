<template>
  <div>
    <div v-if="tab === 1">
      <Form
        :depot="true"
        :has-purchase="false"
        :status-list="statusList"
        id-title="对账单号"
        permission-key="letterInfo-export"
        @search="onSearch"
        @export="onExport"
      />
      <div style="text-align: right; margin-bottom: 10px">
        应付总额 {{ totalPayableAmount }} 商品总数 {{ totalGoodsNumber }}
      </div>
      <el-table :data="tableData" border>
        <el-table-column label="对账单号" prop="reconNo" width="200px" />
        <el-table-column
          label="公司名称"
          prop="companyLegalName"
          min-width="120"
        />
        <el-table-column label="仓库名称" prop="warehouseName" />
        <el-table-column label="品牌名称" prop="brandName" min-width="120" />
        <el-table-column
          label="供应商名称"
          prop="supplierName"
          min-width="120"
        />
        <el-table-column label="应付金额" prop="payableAmount" />
        <el-table-column label="商品数量" prop="totalQuantity" />
        <el-table-column label="创建时间" prop="createdTime" min-width="150" />
        <el-table-column label="状态" prop="statusStr" />
        <el-table-column label="操作">
          <template slot-scope="{ row }">
            <el-button type="text" @click="onDetail(row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="form.current"
        :page-sizes="[10, 20, 30, 100]"
        :page-size="form.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <div v-if="tab === 2">
      <div style="margin-top: 20px">
        <span>基础信息</span>
        <el-button type="text" style="float: right" @click="tab = 1">
          返回
        </el-button>
      </div>
      <el-divider content-position="left"></el-divider>
      <div class="info-children">
        <el-form inline label-width="200px" label-position="left">
          <el-form-item label="单号">
            {{ baseInfo.purchaseOrderId }}
          </el-form-item>
          <el-form-item label="状态">{{ baseInfo.statusDesc }}</el-form-item>
          <el-form-item label="仓库ID">{{ baseInfo.warehouseId }}</el-form-item>
          <el-form-item label="仓库名称">
            {{ baseInfo.warehouseName }}
          </el-form-item>
          <el-form-item label="品牌ID">{{ baseInfo.brandCode }}</el-form-item>
          <el-form-item label="品牌名称">
            {{ baseInfo.brandName }}
          </el-form-item>
          <el-form-item label="公司名称">
            {{ baseInfo.companyName }}
          </el-form-item>
          <el-form-item label="创建时间">
            {{ baseInfo.createdTime }}
          </el-form-item>
        </el-form>
      </div>
      <SupplierInfo :info="supplyInfo" />
      <MoneyInfo type="letterInfo" :info="moneyInfo" />
      <GoodsInfo :list="goodsInfo" :code="true" />
      <LogInfo :list="logsInfo" />
    </div>
  </div>
</template>
<script>
  import Form from './components/Form';
  import GoodsInfo from './components/GoodsInfo';
  import MoneyInfo from './components/MoneyInfo';
  import LogInfo from './components/LogInfo';
  import SupplierInfo from './components/SupplierInfo';
  import {
    getLetterList,
    getLetterDetail,
    getPurchaseDict,
  } from '@/api/purchase';
  import { formatDetail } from './js/index';
  import { exportExcel } from '@/api/blob';
  import { downloadFile } from '@/utils';
  export default {
    components: { Form, GoodsInfo, SupplierInfo, LogInfo, MoneyInfo },
    data() {
      return {
        form: {
          current: 1,
          size: 10,
        },
        totalGoodsNumber: 0,
        totalPayableAmount: 0,
        total: 0,
        tableData: [],
        tab: 1,
        baseInfo: {},
        goodsInfo: [],
        logsInfo: [],
        moneyInfo: {},
        supplyInfo: {},
        statusList: [],
        search: {},
      };
    },
    created() {
      getPurchaseDict().then(res => {
        this.statusList = res.filter(
          i => i.bizType === 'reconStatus',
        )[0].selectors;
      });
    },
    methods: {
      onGetList(boo = null) {
        if (boo) {
          this.form.current = 1;
        }
        let form = this.search;
        getLetterList({
          ...form,
          reconNo: form.id,
          page: this.form.current,
          pageSize: this.form.size,
        }).then(res => {
          const { pageList, totalGoodsNumber, totalPayableAmount } = res;
          this.total = pageList.total;
          this.tableData = pageList.records;
          this.totalGoodsNumber = totalGoodsNumber;
          this.totalPayableAmount = totalPayableAmount;
        });
      },
      onSearch(e) {
        this.search = e;
        this.form.current = 1;
        this.form.size = 10;
        this.onGetList();
      },
      onDetail(obj) {
        getLetterDetail({ id: obj.id }).then(res => {
          this.baseInfo = formatDetail(res).baseInfo;
          this.supplyInfo = formatDetail(res).supplyInfo;
          this.moneyInfo = formatDetail(res).moneyInfo;
          this.logsInfo = res.operationLogs;
          this.goodsInfo = res.skuVOList;
          this.tab = 2;
        });
      },
      handleCurrentChange(e) {
        this.form.current = e;
        this.onGetList();
      },
      handleSizeChange(e) {
        this.form.size = e;
        this.onGetList();
      },
      onExport(e) {
        this.$confirm('确定将该数据导出?', '导出', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          exportExcel(
            {
              ...e,
              reconNo: e.id,
            },
            '/api/financial-integration/reconOrder/exportList',
            'post',
          ).then(res => {
            downloadFile(res.data, '对账单列表');
          });
        });
      },
    },
  };
</script>
