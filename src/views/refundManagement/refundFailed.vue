<template>
  <div>
    <el-form inline class="acg-filter-form">
      <el-form-item label="会员ID:">
        <el-input
          v-model="searchParams.memberId"
          placeholder="请输入会员ID"
          clearable
        />
      </el-form-item>
      <el-form-item label="单号:">
        <el-input
          v-model="searchParams.tenantRefundOrderId"
          placeholder="请输入订单编号/工单号"
          clearable
        />
      </el-form-item>
      <el-form-item label="申请时间:">
        <el-date-picker
          v-model="searchDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          clearable
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="支付方式:">
        <el-select
          v-model="searchParams.productionId"
          placeholder="请选择"
          filterable
          clearable
        >
          <el-option
            v-for="item in productionList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="退款状态:">
        <el-select
          v-model="searchParams.status"
          placeholder="请选择"
          filterable
          clearable
        >
          <el-option
            v-for="item in statusList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="退款申请来源:">
        <el-select
          v-model="searchParams.tenantId"
          placeholder="请选择"
          clearable
          filterable
        >
          <el-option
            v-for="item in tenantIdList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="getList(true)">
          查询
        </el-button>
        <el-button icon="el-icon-refresh" @click="onReset">重置</el-button>
        <!-- <ac-permission-button
          btn-text="批量完成"
          type="primary"
          permission-key="refundFailed-batch-complete"
          @click="openModal('批量完成', 1)"
        ></ac-permission-button> -->
        <ac-permission-button
          btn-text="批量转可提现"
          type="primary"
          icon="el-icon-coin"
          permission-key="refundFailed-batch-reflect"
          @click="openModal('批量转可提现', 2)"
        ></ac-permission-button>
        <ac-permission-button
          btn-text="批量重新退款"
          permission-key="refundFailed-batch-refund"
          type="primary"
          @click="handleRefund(false)"
        ></ac-permission-button>
        <ac-permission-button
          btn-text="导出"
          icon="el-icon-upload2"
          permission-key="refundFailed-batch-export"
          type="primary"
          @click="onExport"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
      :check-selectable="handleCheckSelectable"
      @selection-change="handleSelectionChange"
    >
      <template slot="operation" slot-scope="scope">
        <!-- <el-popconfirm
          title="确认重新退款？"
          @confirm="handleRefund(scope.row)"
        >
          <el-button
            slot="reference"
            :disabled="scope.row.status !== '3'"
            type="text"
            size="small"
          >
            重新退款
          </el-button>
        </el-popconfirm> -->

        <ac-permission-button
          slot-btn="reference"
          btn-text="重新退款"
          permission-key="refundFailed-refund"
          :disabled="scope.row.status !== '3'"
          type="text"
          size="small"
          @click="handleConfirm(scope.row)"
        ></ac-permission-button>

        <!-- <ac-permission-button
          slot-btn="reference"
          btn-text="直接退款"
          permission-key="refundFailed-direct"
          :disabled="scope.row.status !== '1'"
          type="text"
          size="small"
          @click="directConfirm(scope.row)"
        ></ac-permission-button> -->

        <ac-permission-button
          v-if="scope.row.haveWithdrawal"
          slot-btn="reference"
          btn-text="退款至可提现"
          permission-key="refundFailed-reflect"
          :disabled="scope.row.status !== '3'"
          type="text"
          size="small"
          @click="openModal('退款至可提现', 2, scope.row)"
        ></ac-permission-button>

        <ac-permission-button
          slot-btn="reference"
          btn-text="退款至个人户"
          permission-key="refundFailed-reflect"
          :disabled="scope.row.status !== '3'"
          type="text"
          size="small"
          @click="openModal('退款至个人户', 3, scope.row)"
        ></ac-permission-button>

        <ac-permission-button
          slot-btn="reference"
          btn-text="完成"
          permission-key="refundFailed-complete"
          :disabled="scope.row.status !== '3'"
          type="text"
          size="small"
          @click="openModal('完成', 1, scope.row.platformOrderId)"
        ></ac-permission-button>
        <el-button
          slot="reference"
          type="text"
          size="small"
          @click="handleDetails(scope.row)"
        >
          详情
        </el-button>
      </template>
    </dynamictable>
    <el-dialog
      width="400px"
      :title="title"
      :visible.sync="visible"
      @closed="onClose"
    >
      <el-form ref="formData" :model="saveParams">
        <el-form-item
          prop="operatorRemark"
          :rules="[{ required: true, message: '请输入', trigger: 'change' }]"
          label="备注"
        >
          <el-input
            v-model="saveParams.operatorRemark"
            type="textarea"
            :rows="4"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" style="text-align: center" class="dialog-footer">
        <el-button type="primary" @click="onOK">确认</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="visible1"
      width="1000px"
      title="退款记录"
      :visible.sync="visible1"
      @closed="visible1 = false"
    >
      <dynamictable
        :data-source="logList"
        :columns="columns1"
        :options="{ ...options, mutiSelect: false }"
      ></dynamictable>
      <el-descriptions
        v-if="rowData.refundAccountInfoVO"
        style="margin-top: 20px"
        title="退款信息"
        :column="3"
        border
      >
        <el-descriptions-item>
          <template slot="label">姓名</template>
          {{ rowData.refundAccountInfoVO.accountName }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="rowData.refundAccountInfoVO.channelId === 'alipay'"
        >
          <template slot="label">支付宝账户</template>
          {{ rowData.refundAccountInfoVO.accountNo }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="rowData.refundAccountInfoVO.channelId !== 'alipay'"
        >
          <template slot="label">银行名称</template>
          {{ rowData.refundAccountInfoVO.bankName }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="rowData.refundAccountInfoVO.channelId !== 'alipay'"
        >
          <template slot="label">银行账户</template>
          {{ rowData.refundAccountInfoVO.accountNo }}
        </el-descriptions-item>
      </el-descriptions>
      <div v-else style="margin-top: 20px; width: 100%">
        <div
          style="
            font-size: 14px;
            color: #303133;
            margin-bottom: 20px;
            font-weight: bold;
          "
        >
          退款信息
        </div>
        <div
          style="
            width: 100%;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
          "
        >
          暂无信息
        </div>
      </div>
      <div slot="footer" style="text-align: center" class="dialog-footer">
        <el-button type="primary" @click="visible1 = false">确认</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-if="billingMessageVisible"
      width="700px"
      title="收款信息"
      :visible.sync="billingMessageVisible"
      @closed="billingMessageVisible = false"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="122px"
        class="demo-ruleForm acg-filter-form"
      >
        <el-form-item label="" prop="name">
          <el-radio-group v-model="ruleForm.channelId">
            <el-radio label="alipay">支付宝</el-radio>
            <el-radio label="unionpay">银行卡</el-radio>
          </el-radio-group>
        </el-form-item>
        <div v-if="ruleForm.channelId === 'alipay'">
          <el-form-item label="姓名" prop="accountName">
            <el-input v-model="ruleForm.accountName" clearable></el-input>
          </el-form-item>
          <el-form-item label="支付宝账号" prop="accountNo">
            <el-input v-model="ruleForm.accountNo" clearable></el-input>
          </el-form-item>
        </div>

        <div v-if="ruleForm.channelId === 'unionpay'">
          <el-form-item label="户名" prop="accountName">
            <el-input v-model="ruleForm.accountName" clearable></el-input>
          </el-form-item>
          <el-form-item label="账号" prop="accountNo">
            <el-input v-model="ruleForm.accountNo" clearable></el-input>
          </el-form-item>
          <el-form-item label="银行" prop="bankInfo">
            <el-select
              v-model="ruleForm.bankInfo"
              value-key="bankLineNo"
              filterable
              clearable
            >
              <el-option
                v-for="(item, index) in bankList"
                :key="index"
                :value="item"
                :label="item.bankName"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <el-form-item>
          <div>
            <el-button type="primary" @click="submitForm('ruleForm')">
              确定
            </el-button>
            <el-button @click="resetForm('ruleForm')">取消</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    getRefundOrder,
    getCondition,
    retryRefund,
    refundToWithdrawalBatch,
    getPurchaseApplyList,
    orderQueryRefundOrderLog,
    directRefund,
    refundToPersonAcc,
    supportBankList,
  } from '@/api/refundManagement';
  import { exportExcel } from '@/api/blob';
  import { downloadFile, parseTime } from '@/utils';
  import { mapGetters } from 'vuex';

  export default {
    components: {
      dynamictable,
    },

    data() {
      let columns1 = [
        {
          prop: 'id',
          label: '编号',
        },
        {
          prop: 'tenantRefundOrderId',
          label: '单号（订单/工单）',
        },
        {
          prop: 'statusName',
          label: '退款状态',
        },
        {
          prop: 'subStatusName',
          label: '子状态',
        },
        {
          prop: 'amount',
          label: '支付金额',
        },
        {
          prop: 'refundAmount',
          label: '退款金额',
        },
        {
          prop: 'createdTime',
          label: '申请时间',
        },
        {
          prop: 'updatedTime',
          label: '更新时间',
        },
        {
          prop: 'memberId',
          label: '会员ID',
        },
        {
          prop: 'orderId',
          label: '交易单号',
        },
        {
          prop: 'channelMessage',
          label: '失败原因',
        },
        {
          prop: 'productionName',
          label: '支付方式',
        },
        {
          prop: 'channelName',
          label: '渠道名称',
        },
        {
          prop: 'paymentChannelMerchantId',
          label: '商户号',
        },
        {
          prop: 'tenantIdName',
          label: '退款申请来源',
        },
      ];
      const columns = columns1.concat([
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '200',
          scopedSlots: { customRender: 'operation' },
        },
      ]);

      return {
        showPopconfirm: false,
        visible: false,
        visible1: false,
        billingMessageVisible: false, // 收款信息弹窗
        searchDate: '',
        title: '',
        searchParams: {
          tenantRefundOrderId: '',
          status: '',
          paymentOrderId: '',
          memberId: '',
          startTimeStr: '',
          endTimeStr: '',
          tenantId: '',
          productionId: '',
        },
        saveParams: {
          platformOrderIds: [],
          operatorRemark: '',
        },
        list: [],
        logList: [], // 退款记录列表
        type: 1, // 1, 完成, 2, 退款至可提现
        selectArr: [],
        productionList: [],
        statusList: [],
        tenantIdList: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          mutiSelect: true,
        },
        columns,
        columns1,
        ruleForm: {
          channelId: 'alipay',
        },
        rules: {
          accountName: [
            { required: true, message: '请输入', trigger: 'change' },
          ],
          accountNo: [{ required: true, message: '请输入', trigger: 'blur' }],
          bankInfo: [{ required: true, message: '请选择', trigger: 'change' }],
        },
        inputWidth: '394px',
        rowData: {},
        bankList: [],
      };
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        userId: 'user/userId',
      }),
    },
    created() {
      this.setData();
      getCondition({}).then(res => {
        this.productionList = res.productionList;
        let statusArr = Array.isArray(res.statusList) ? res.statusList : [];
        statusArr.unshift({
          name: '全部',
          value: '',
        });
        this.statusList = statusArr;
        this.tenantIdList = res.tenantIdList;
      });
      this.getList(true);
      this.getSupportBankList();
    },
    beforeDestroy() {
      clearInterval(this.timer);
    },

    methods: {
      handleConfirm(row) {
        let that = this;
        this.$confirm('确认重新退款？', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          that.handleRefund(row);
        });
      },
      directConfirm(row) {
        let that = this;
        this.$confirm('确定直接退款？', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          that.directRefund(row);
        });
      },
      async handleDetails(val) {
        this.visible1 = true;
        this.rowData = val;
        const res = await orderQueryRefundOrderLog({
          platformOrderId: val.platformOrderId,
        });
        this.logList = res ? res : [];
      },
      handleCheckSelectable(row) {
        return row.status === '3';
      },
      setData() {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
        this.searchDate = [
          parseTime(start, '{y}-{m}-{d}'),
          parseTime(end, '{y}-{m}-{d}'),
        ];
      },
      getTenantTest(id) {
        let text = '';
        this.tenantIdList.forEach(item => {
          if (item.id === id) {
            text = item.name;
          }
        });
        return text;
      },
      getParams() {
        this.searchParams.startTimeStr = this.searchDate
          ? parseTime(this.searchDate[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.endTimeStr = this.searchDate
          ? parseTime(this.searchDate[1], '{y}-{m}-{d}')
          : '';
        const params = {
          ...this.searchParams,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await getRefundOrder(params);
        this.options.loading = false;
        this.list = res ? res.records : [];
        this.pagination.total = res ? res.total : 0;
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.setData();
        this.getList();
      },
      handleSelectionChange(val) {
        this.selectArr = val;
      },
      onClose() {
        Object.assign(this.$data.saveParams, this.$options.data().saveParams);
        this.$nextTick(function () {
          this.$refs.formData.clearValidate();
        });
      },
      getStatusArr() {
        return this.selectArr.every(item => {
          return item.status === '3';
        });
      },
      openModal(title, type, row) {
        const { platformOrderId: id } = row;
        this.rowData = row;
        if (!id && this.selectArr.length === 0) {
          return this.$message.error('请选择订单');
        }
        if (id) {
          this.saveParams.platformOrderIds = [id];
        } else {
          this.saveParams.platformOrderIds = this.selectArr.map(
            item => item.platformOrderId,
          );
        }
        this.title = title;
        this.type = type;
        if (type === 3) {
          this.billingMessageVisible = true;
        } else {
          this.visible = true;
        }
      },
      onOK() {
        this.$refs.formData.validate(valid => {
          if (!valid) return;
          const saveApi =
            this.type === 1 ? getPurchaseApplyList : refundToWithdrawalBatch;
          saveApi({
            ...this.saveParams,
            operatorName: this.username,
            ssoId: this.$store.state.user.userInfo.id,
          }).then(res => {
            // if (Array.isArray(res) && !this.isErr(res) && res.length) {
            //   this.$message.error(`${res}订单操作失败`);
            // } else this.$message.success('操作成功');
            if (Array.isArray(res) && res) {
              this.$message.success('操作成功');
            }
            this.timer = setTimeout(() => {
              this.getList();
            }, 1000);

            this.visible = false;
          });
        });
      },
      isErr(arr = []) {
        return arr.every(item => {
          return item == null;
        });
      },

      async handleRefund(val) {
        if (!val && this.selectArr.length === 0) {
          return this.$message.error('请选择订单');
        }
        // if (!val && !this.getStatusArr()) {
        //   return this.$message.error(
        //     '你选择了不可执行该操作的记录，请检查后重新选择再操作！',
        //   )
        // }
        let saveParamsArr = [];
        if (val) {
          saveParamsArr = [{ id: val.id, orderId: val.orderId }];
        } else {
          saveParamsArr = this.selectArr.map(item => ({
            id: item.id,
            orderId: item.orderId,
          }));
        }
        const res = await retryRefund(saveParamsArr);
        if (Array.isArray(res) && !this.isErr(res) && res.length) {
          return this.$message.error(`${res}订单退款失败`);
        } else this.$message.success('退款成功');
        this.getList();
      },
      async directRefund(val) {
        if (!val && this.selectArr.length === 0) {
          return this.$message.error('请选择订单');
        }
        const res = await directRefund(val.id);
        if (Array.isArray(res) && !this.isErr(res) && res.length) {
          return this.$message.error(`${res}订单退款失败`);
        } else this.$message.success('退款成功');
        this.getList();
      },
      onExport() {
        const params = this.getParams();
        console.log(params, 'paramsparams');
        exportExcel(
          params,
          '/api/pay-dashboard/order/refundOrder/export',
          'get',
        ).then(res => {
          downloadFile(res.data, '退款失败处理列表', 'csv');
        });
      },
      submitForm(formName) {
        this.$refs[formName].validate(async valid => {
          if (valid) {
            const { id } = this.rowData;
            let data = {
              ...this.ruleForm,
              id,
            };
            if (this.ruleForm.bankInfo?.bankLineNo) {
              const { bankName, bankLineNo } = this.ruleForm.bankInfo;
              data.bankName = bankName;
              data.bankCode = bankLineNo;
            }
            delete data.bankInfo;

            refundToPersonAcc(data)
              .then(res => {
                if (!res) {
                  this.getList(true);
                  this.$message.success('操作成功');
                  this.resetForm(formName);
                }
              })
              .catch(err => {
                console.log(err);
              });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      resetForm(formName) {
        this.billingMessageVisible = false;
        this.$refs[formName].resetFields();
      },
      async getSupportBankList() {
        const res = await supportBankList();
        if (res) {
          this.bankList = res;
        }
      },
    },
  };
</script>
<style lang="scss"></style>
