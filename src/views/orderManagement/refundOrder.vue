<!--
 * @Description: 
 * @Author: wuqingsong
 * @Date: 2022-08-09 16:30:38
 * @LastEditTime: 2022-09-28 13:47:15
 * @LastEditors: Please set LastEditors
 * @Reference: 
-->
<template>
  <div>
    <el-form :inline="true" :model="params">
      <el-form-item label="平台交易订单号:">
        <el-input v-model="params.orderId" placeholder="请输入平台交易订单号" />
      </el-form-item>
      <el-form-item label="平台退款订单号:">
        <el-input
          v-model="params.platformOrderId"
          placeholder="请输入平台退款订单号"
        />
      </el-form-item>
      <el-form-item label="平台支付订单号:">
        <el-input
          v-model="params.paymentOrderId"
          placeholder="请输入平台支付订单号"
        />
      </el-form-item>
      <el-form-item label="商户号:">
        <el-input v-model="params.merchantId" placeholder="请输入商户号" />
      </el-form-item>
      <el-form-item label="渠道:">
        <el-select v-model="params.channelId" clearable placeholder="渠道">
          <el-option
            v-for="(item, index) in channelIdList"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间:">
        <el-date-picker
          v-model="detailTime"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <ac-permission-button
          btn-text="查询"
          permission-key=""
          @click="search(true)"
        ></ac-permission-button>
        <el-button type="primary" @click="reset">重置</el-button>
        <ac-permission-button
          btn-text="导出"
          permission-key=""
          @click="doExport()"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column
        align="center"
        prop="id"
        label="序号"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="orderId"
        label="平台交易号"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="platformOrderId"
        label="平台退款订单号"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="paymentOrderId"
        label="平台支付订单号"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="tenantOrderId"
        label="租户订单号"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="memberId"
        label="会员Id"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="amount"
        label="交易金额"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="productionName"
        label="支付方式"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="channelName"
        label="渠道名称"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="paymentChannelMerchantId"
        label="支付渠道侧商户号"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="tenantRefundOrderId"
        label="租户退款单号"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="tenantId"
        label="租户id"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="statusName"
        label="状态"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="createdTime"
        label="创建时间"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="updatedTime"
        label="更新时间"
        min-width="100"
      ></el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page="pageNum"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>
<script>
  import { setInitData, downloadFile } from '@/utils';
  import { exportExcel } from '@/api/blob';
  import { getRefundOrderList, getPayList } from '@/api/refundManagement';

  export default {
    data() {
      return {
        params: {
          orderId: '',
          platformOrderId: '',
          paymentOrderId: '',
          merchantId: '',
          channelId: '',
          status: '',
          startTimeStr: '',
          endTimeStr: '',
        },
        detailTime: '',
        pageNum: 1,
        statusList: [
          { value: 1, label: '交易中' },
          { value: 2, label: '交易成功' },
          { value: 3, label: '交易失敗' },
          { value: 50, label: '冲正' },
        ],
        pageSize: 10,
        channelIdList: [], // 状态下拉框
        tableData: [],
        loading: false, // loading表格加载层
        pageNo: 1,
        limit: 10,
        total: 0,
      };
    },
    created() {
      this.detailTime = setInitData(7);
      this.initParams();
      this.getEnums();
      this.fetch();
    },
    methods: {
      initParams() {},
      async getEnums() {
        const res = await getPayList();
        if (res) {
          this.channelIdList = res;
          console.log(this.channelIdList, 'sssssss');
        }
      },
      async fetch() {
        if (this.detailTime && this.detailTime.length > 0) {
          this.params.startTimeStr = this.detailTime[0];
          this.params.endTimeStr = this.detailTime[1];
        } else {
          this.params.startTimeStr = null;
          this.params.endTimeStr = null;
        }
        const params = {
          limit: this.pageSize,
          pageNo: this.pageNum,
          ...this.params,
        };
        this.loading = true;
        try {
          const res = await getRefundOrderList(params);
          if (res) {
            this.tableData = res.records;
            this.total = res.total;
            this.loading = false;
          } else {
            this.loading = false;
          }
        } catch (error) {
          this.loading = false;
        }
      },
      doExport() {
        if (this.detailTime && this.detailTime.length > 0) {
          this.params.startTimeStr = this.detailTime[0];
          this.params.endTimeStr = this.detailTime[1];
        } else {
          this.params.startTimeStr = null;
          this.params.endTimeStr = null;
        }
        const params = {
          limit: this.pageSize,
          pageNo: this.pageNum,
          ...this.params,
        };
        const exportApi = '/order/refundOrder/export';
        exportExcel(params, `/api/pay-core/${exportApi}`, 'get').then(res => {
          if (res) {
            downloadFile(res.data, '退款单管理_', 'csv');
          } else {
            //
            this.$message.error('暂无数据');
          }
        });
      },
      search() {
        this.pageNum = 1;
        this.fetch();
      },
      reset() {
        Object.assign(this.$data.params, this.$options.data().params);
        this.pageNum = 1;
        this.pageSize = 10;
        this.detailTime = setInitData(7);
      },
      handleSizeChange(val) {
        this.pageNum = 1;
        this.pageSize = val;
        this.fetch();
      },
      handleCurrentChange(val) {
        this.pageNum = val;
        this.fetch();
      },
    },
  };
</script>
<style lang="scss"></style>
