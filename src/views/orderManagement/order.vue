<template>
  <div v-if="tab === 1">
    <el-form inline>
      <el-form-item label="">
        <el-input
          v-model="searchParams.orderNo"
          type="text"
          style="width: 18rem"
          width="64"
          placeholder="请输入单号"
          clearable
        />
      </el-form-item>

      <el-form-item label="">
        <el-date-picker
          v-model="searchDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="">
        <el-select
          v-model="searchParams.orderStatus"
          clearable
          placeholder="请选择支付状态"
        >
          <el-option
            v-for="item in statusList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <ac-permission-button
          btn-text="支付凭证查单"
          permission-key="payFile-upload"
          @click="showPayFileDialog = true"
        ></ac-permission-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="getList(true)">查询</el-button>
        <el-button type="danger" @click="onReset">重置</el-button>
        <el-button type="info" @click="onExport">导出</el-button>
      </el-form-item>
    </el-form>
    <el-dialog
      title="请复制图片"
      :visible.sync="showPayFileDialog"
      width="300px"
      @closed="onClose"
    >
      <div>
        <uploadImgCopy
          ref="uploadImg"
          list-type="image"
          :file-list="initFile"
          :is-copy="true"
          :max="1"
          @change="handleFileChange"
          @changeImage="changeImage"
          @onRemove="handleFileRemove"
        />
      </div>
      <div slot="footer">
        <el-button @click="cancelShowPayFile">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>
    <el-table
      v-loading="loading"
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      :data="list"
      header-row-class-name="titleClass"
      :header-cell-style="{ background: '#f7faff' }"
      show-overflow-tooltip="true"
      @row-click="handle"
    >
      <el-table-column type="expand">
        <template slot-scope="props">
          <div class="detail">
            <ul>
              <li>订单标题： {{ props.row.orderSimpleDetailVO.orderTitle }}</li>
              <li>租户： {{ props.row.orderSimpleDetailVO.tenantName }}</li>
              <li>
                租户订单号：{{ props.row.orderSimpleDetailVO.tenantOrderId }}
              </li>
              <li>
                已退金额：{{
                  props.row.orderSimpleDetailVO.orderRefundedAmount
                }}
              </li>
              <li>
                交易时间：{{ props.row.orderSimpleDetailVO.transactionTime }}
              </li>
              <li>
                支付手续费：{{ props.row.orderSimpleDetailVO.chargeAmount }}
              </li>
            </ul>
            <ul v-if="props.row.status == 2 || props.row.status > 3">
              <li>
                支付产品：{{ props.row.orderSimpleDetailVO.paymentProduction }}
              </li>
              <li>支付渠道：{{ props.row.orderSimpleDetailVO.channel }}</li>
              <li>支付方式：{{ props.row.orderSimpleDetailVO.payType }}</li>
              <li>
                商户号：{{
                  props.row.orderSimpleDetailVO.paymentChannelMerchantId
                }}
              </li>
              <li>
                支付渠道侧订单号：{{
                  props.row.orderSimpleDetailVO.channelOrderId
                }}
              </li>
            </ul>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="金额" width="120">
        <template slot-scope="scope">
          <span style="font-weight: bold">
            {{ scope.row.currencyUnit }}${{ scope.row.amount }}
          </span>
        </template>
      </el-table-column>
      <el-table-column width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == 2" type="success">
            {{ scope.row.tradeStatus }}
          </el-tag>
          <el-tag v-else-if="scope.row.status == 3" type="warning">
            {{ scope.row.tradeStatus }}
          </el-tag>
          <el-tag
            v-else-if="scope.row.status == 6 || scope.row.status > 5"
            type="danger"
          >
            {{ scope.row.tradeStatus }}
          </el-tag>
          <el-tag v-else type="info">{{ scope.row.tradeStatus }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="商城单号" prop="orderDesc"></el-table-column>
      <el-table-column label="支付单号" prop="orderId"></el-table-column>
      <el-table-column label="会员ID" prop="memberId"></el-table-column>
      <el-table-column label="" prop=""></el-table-column>
      <el-table-column
        label="下单日期"
        prop="transactionTime"
      ></el-table-column>
      <el-table-column fixed="right" label="" width="50">
        <template slot-scope="props">
          <el-dropdown class="detail" @command="handleCommand">
            <span class="el-dropdown-link">
              <i
                class="el-icon-caret-bottom el-icon-more"
                style="font-size: 1rem !important"
              ></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                class="detail"
                data-clipboard-text="copyValue"
                :command="composeValue('detail', props.row)"
              >
                查看详情
              </el-dropdown-item>
              <el-dropdown-item :command="composeValue('order', props.row)">
                <div
                  class="tag-copy"
                  :data-clipboard-text="props.row.orderId"
                  @click="onCopy($event, props.row.orderId)"
                >
                  复制支付订单号
                </div>
              </el-dropdown-item>
              <el-dropdown-item :command="composeValue('payment', props.row)">
                <div
                  class="tag-copy"
                  :data-clipboard-text="props.row.orderDesc"
                  @click="onCopy($event, props.row.orderDesc)"
                >
                  复制商城订单号
                </div>
              </el-dropdown-item>
              <el-dropdown-item :command="composeValue('member', props.row)">
                <div
                  class="tag-copy"
                  :data-clipboard-text="props.row.memberId"
                  @click="onCopy($event, props.row.memberId)"
                >
                  复制会员ID
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-if="pagination"
      ref="listPage"
      background
      :current-page.sync="pagination.pageSize"
      :page-size="pagination.pageLimit"
      :page-sizes="[15, 30, 50, 100, 200]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      @current-change="handleIndexChange"
      @size-change="handleSizeChange"
    ></el-pagination>
  </div>
  <div v-else>
    <div style="width: 96%; margin-left: 2%">
      <div class="header">
        <div class="htitle">
          <h1 class="title" style="width: 25%; display: inline-block">
            {{ paymentOrder.currencyUnit }}{{ paymentOrder.amount }}
            <el-tag v-if="paymentOrder.status == 2" type="success">
              {{ paymentOrder.tradeStatus }}
            </el-tag>
            <el-tag v-else-if="paymentOrder.status == 3" type="warning">
              {{ paymentOrder.tradeStatus }}
            </el-tag>
            <el-tag
              v-else-if="paymentOrder.status == 6 || paymentOrder.status > 5"
              type="danger"
            >
              {{ paymentOrder.tradeStatus }}
            </el-tag>
            <el-tag v-else type="info">{{ paymentOrder.tradeStatus }}</el-tag>
          </h1>
          <el-button
            class="back"
            style="margin-left: 70%; display: inline-block"
            plain
            @click="tab = 1"
          >
            返回
          </el-button>
        </div>
        <el-divider></el-divider>
        <el-row :gutter="42">
          <el-col :span="4">
            <div class="item">
              <p>日期</p>
              <p>{{ paymentOrder.transactionTime || '--' }}</p>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="item">
              <p>客户</p>
              <p style="color: #658be4">
                {{ paymentOrder.name }}({{ paymentOrder.mobile }})
              </p>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="item">
              <p>支付方式</p>
              <p>{{ paymentOrder.paymentProduction || '--' }}</p>
            </div>
          </el-col>
          <el-col :span="3">
            <div class="item">
              <p>租户</p>
              <p>{{ paymentOrder.tenantName || '--' }}</p>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="times">
        <h2>时间线</h2>
        <el-divider></el-divider>
        <el-timeline>
          <el-timeline-item
            v-for="(value, index) in eventLogs"
            :key="index"
            :icon="value.icon"
            :type="value.type"
            :color="value.color"
            :size="value.size"
            :timestamp="value.createdTime"
          >
            {{ value.title }}
            <el-tag v-if="value.status == 200" type="success">
              {{ value.status }} OK
            </el-tag>
          </el-timeline-item>
        </el-timeline>
      </div>

      <div class="trade-detail">
        <h2>交易详情</h2>
        <el-divider></el-divider>
        <ul>
          <li>订单标题</li>
          <li>租户</li>
          <li>租户订单号</li>
          <li>支付金额</li>
          <li>已退款金额</li>
          <li>会员ID</li>
          <li>订单描述</li>
        </ul>
        <ul>
          <li>{{ paymentOrder.orderTitle || '--' }}</li>
          <li>{{ paymentOrder.tenantName || '--' }}</li>
          <li>{{ paymentOrder.tenantOrderId || '--' }}</li>
          <li>
            {{ paymentOrder.amount || '--' }}{{ paymentOrder.currencyUnit }}
          </li>
          <li>
            {{ paymentOrder.orderRefundedAmount || '0'
            }}{{ paymentOrder.currencyUnit }}
          </li>
          <li>{{ paymentOrder.memberId || '--' }}</li>
          <li>{{ paymentOrder.orderDesc || '--' }}</li>
        </ul>
      </div>

      <div v-if="paymentOrder.channel" class="trade-detail">
        <h2>支付详情</h2>
        <el-divider></el-divider>
        <ul>
          <li>平台支付订单号</li>
          <li>支付产品</li>
          <li>支付渠道</li>
          <li>商户</li>
          <li>支付状态</li>
          <li>支付方式</li>
        </ul>
        <ul>
          <li>{{ paymentOrder.orderId || '--' }}</li>
          <li>{{ paymentOrder.paymentProduction || '--' }}</li>
          <li>{{ paymentOrder.channel || '--' }}</li>
          <li>{{ paymentOrder.merchant || '--' }}</li>
          <li>{{ paymentOrder.tradeStatus || '--' }}</li>
          <li>{{ paymentOrder.payType || '--' }}</li>
        </ul>
        <ul>
          <li>支付渠道侧商户号</li>
          <li>交易时间</li>
          <li>支付渠道侧订单号</li>
          <li>第三方渠道错误描述</li>
          <li>支付系统错误描述</li>
          <li>回调商城状态</li>
        </ul>
        <ul>
          <li>{{ paymentOrder.paymentChannelMerchantId || '--' }}</li>
          <li>{{ paymentOrder.transactionTime || '--' }}</li>
          <li>{{ paymentOrder.channelOrderId || '--' }}</li>
          <li>{{ paymentOrder.channelMessage || '--' }}</li>
          <li>{{ paymentOrder.systemMessage || '--' }}</li>
          <li>{{ callBackStatus || '--' }}</li>
        </ul>
      </div>

      <div v-if="refundNum > 0" class="refund-detail">
        <h2>退款洞察</h2>
        <el-divider></el-divider>
        <div class="refund-body">
          <div class="redetail">
            <ul>
              <li>退款单号</li>
              <li>会员ID</li>
              <li>支付金额</li>
              <li>退款笔数</li>
              <li>退款现状</li>
            </ul>
            <ul>
              <li>{{ paymentOrder.orderDesc || '--' }}</li>
              <li>{{ paymentOrder.memberId || '--' }}</li>
              <li>
                {{ paymentOrder.amount || '--' }}{{ paymentOrder.currencyUnit }}
              </li>
              <li>
                {{ refundNum || '0' }}
              </li>
              <li>{{ msg || '--' }}</li>
            </ul>
          </div>
          <div class="relist">
            <el-table :data="refundList" style="width: 100%">
              <el-table-column
                prop="tenantRefundOrderId"
                label="退款单号"
              ></el-table-column>
              <el-table-column
                prop="platformOrderId"
                label="退款流水号"
              ></el-table-column>
              <el-table-column
                prop="amount"
                width="50"
                label="金额"
              ></el-table-column>
              <el-table-column width="80">
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.status == 2" type="success">
                    {{ scope.row.statusName }}
                  </el-tag>
                  <el-tag v-else type="danger">
                    {{ scope.row.statusName }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column
                prop="channelName"
                label="渠道名称"
              ></el-table-column>
              <el-table-column
                prop="subStatusName"
                label="退款方向"
              ></el-table-column>
              <el-table-column
                prop="createdTime"
                label="申请时间"
              ></el-table-column>
              <el-table-column
                prop="channelError"
                label="渠道错误码"
              ></el-table-column>
              <el-table-column
                prop="channelMessage"
                label="支付渠道错误描述"
              ></el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <div class="about-pay">
        <h2>相关支付</h2>
        <el-divider></el-divider>
        <template>
          <el-table :data="aboutPay" style="width: 100%">
            <el-table-column width="100" label="金额">
              <template slot-scope="scope">
                <span style="font-weight: bold">
                  {{ scope.row.currencyUnit }}${{ scope.row.amount }}
                </span>
              </template>
            </el-table-column>
            <el-table-column width="80">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status == 2" type="success">
                  {{ scope.row.payStatusName }}
                </el-tag>
                <el-tag v-else type="danger">
                  {{ scope.row.payStatusName }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="platformOrderId"
              label="平台支付订单号"
              width="180"
            ></el-table-column>
            <el-table-column prop="channelName" label="渠道"></el-table-column>
            <el-table-column
              prop="payProduction"
              label="支付产品"
            ></el-table-column>
            <el-table-column
              prop="paymentChannelMerchantId"
              label="支付渠道侧商户号"
            ></el-table-column>
            <el-table-column
              prop="channelOrderId"
              label="支付渠道侧订单号"
            ></el-table-column>
            <el-table-column
              prop="channelError"
              label="渠道错误码"
            ></el-table-column>
            <el-table-column
              prop="channelMessage"
              label="支付渠道错误描述"
            ></el-table-column>
            <el-table-column prop="payType" label="支付方式"></el-table-column>
            <el-table-column prop="payTime" label="支付日期"></el-table-column>
          </el-table>
        </template>
      </div>
      <div class="eventLog">
        <h2>事件和日志</h2>
        <el-divider></el-divider>
        <div class="time-line">
          <el-timeline :reverse="true">
            <el-timeline-item
              v-for="(value, index) in eventLogs"
              :key="index"
              :icon="value.icon"
              :type="value.type"
              :color="value.color"
              :size="value.size"
              :timestamp="value.createdTime"
              :hide-timestamp="true"
              style="cursor: pointer"
              placement="top"
            >
              <el-card>
                <div @click.stop="showJson(value)">
                  <p style="font-size: 1rem">
                    {{ value.title }}
                    <el-tag v-if="value.status == 200" type="success">
                      {{ value.status }} OK
                    </el-tag>
                  </p>
                  <p style="color: #a8a8a8">{{ value.createdTime }}</p>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
        <div class="log-event">
          <div :class="{ 'put-away': true, 'turn-on': isTurn }">
            <p v-if="requestData.length > 0" style="user-select: none">
              请求参数
            </p>
            <div
              v-for="(requestItem, requestIndex) in requestData"
              :key="requestIndex"
            >
              <span class="first">1</span>
              {
              <span
                v-for="(requestItem1, key, requestIndex1) in requestItem"
                :key="key"
                style="margin-left: 20px; display: flex; line-height: 1.5"
              >
                <span class="first" style="user-select: none">
                  {{ requestIndex1 + 2 }}
                </span>
                <span class="key" style="user-select: none">“{{ key }}”</span>
                :
                <span class="value">“{{ requestItem1 }}”</span>
              </span>
              }
            </div>
            <p v-if="responseData.length > 0" style="user-select: none">
              响应正文
            </p>
            <div
              v-for="(responseItem, responseIndex) in responseData"
              :key="'response-' + responseIndex"
            >
              <span class="first" style="user-select: none">1</span>
              {
              <span
                v-for="(responseItem1, key1, responseIndex1) in responseItem"
                :key="'response-' + key1"
                style="margin-left: 20px; display: flex; line-height: 1.5"
              >
                <span class="first" style="user-select: none">
                  {{ responseIndex1 + 2 }}
                </span>
                <span class="key" style="user-select: none">“{{ key1 }}”</span>
                :
                <span class="value">“{{ responseItem1 }}”</span>
              </span>
              }
            </div>
            <div class="mask">
              <el-button class="btn" round @click="isTurn = !isTurn">
                {{ isTurn ? '隐藏行' : '查看所有' }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
      <!-- </div> -->
    </div>
  </div>
</template>
<script>
  import {
    tradeList,
    getConditions,
    listPaymentOrders,
    listRefundOrders,
    listOrderEvents,
    getSimpleDetail,
    getMallBackStatus,
    queryDify,
  } from '@/api/refundManagement';
  import { exportExcel } from '@/api/blob';
  import uploadImgCopy from '@/components/uploadImgCopy';
  import { downloadFile, parseTime } from '@/utils';
  import { mapGetters } from 'vuex';
  import Clipboard from 'clipboard';

  export default {
    components: {
      uploadImgCopy,
    },
    data() {
      return {
        requestData: [],
        responseData: [],
        copyValue: '',
        isTurn: false,
        dataLog: [],
        reverse: true,
        loading: true,
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: '',
        },
        visible: false,
        searchDate: '',
        title: '',
        initFile: [],
        fileUrl: '',
        payFileUrl: '',
        searchParams: {
          orderId: '',
          orderDesc: '',
          channelOrderId: '',
          orderNo: '',
          orderStatus: '',
          payFileUrl: '',
        },
        saveParams: {
          platformOrderIds: [],
          operatorRemark: '',
        },
        tab: 1,
        list: [],
        type: 1, // 1, 完成, 2, 退款至可提现
        selectArr: [],
        productionList: [],
        statusList: [],
        tenantIdList: [],
        pagination: {
          pageSize: 1,
          pageLimit: 15,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          mutiSelect: true,
        },
        showPayFileDialog: false,
        paymentOrder: {},
        aboutPay: [],
        refundList: [],
        refundNum: 0,
        refundedMoney: 0,
        refundedNum: 0,
        refundingMoney: 0,
        refundingNum: 0,
        eventLogs: [],
        callBackStatus: '',
        msg: '',
      };
    },

    computed: {
      ...mapGetters({
        username: 'user/username',
        userId: 'user/userId',
      }),
    },
    created() {
      this.setData();
      getConditions({}).then(res => {
        this.statusList = res;
      });
      this.getList(true);
    },

    methods: {
      async getMallBackStatusList(orderId) {
        getMallBackStatus({ orderId: orderId }).then(result => {
          // 回调通知状态 0:等待回调 1:回调成功 2:回调取消
          if (result.status == 0) {
            this.callBackStatus = '等待回调';
          } else if (result.status == 1) {
            this.callBackStatus = '回调成功';
          } else {
            this.callBackStatus = '回调取消';
          }
          console.log(result);
        });
      },
      showJson(item) {
        this.responseData = [];
        this.requestData = [];
        if (item.requestBody != null && item.requestBody.length > 0) {
          let obj = this.strToJson(item.requestBody);
          if (typeof obj == 'object') {
            if (Array.isArray(obj)) {
              this.requestData = obj;
            } else {
              this.requestData.push(obj);
            }
          } else {
            const data = JSON.parse(obj);
            if (Array.isArray(data)) {
              this.requestData = data;
            } else {
              this.requestData.push(data);
            }
          }
        }
        if (item.responseBody != null && item.responseBody.length > 0) {
          let obj = this.strToJson(item.responseBody);
          if (typeof obj == 'object') {
            if (Array.isArray(obj)) {
              this.responseData = obj;
            } else {
              this.responseData.push(obj);
            }
          } else {
            const data = JSON.parse(obj);
            if (Array.isArray(data)) {
              this.responseData = data;
            } else {
              this.responseData.push(data);
            }
          }
        }
      },
      handleFileChange(file, fileList) {
        this.searchParams.payFileUrl = file.file_url;
        this.initFile = fileList;
      },
      handleFileRemove(file, fileList) {
        this.searchParams.payFileUrl = '';
        this.initFile = [];
      },
      handleCommand(command) {
        let _this = this;
        let item = command.row;
        if (command.act == 'detail') {
          this.handle(command.row, {}, {}, {});
        } else if (command.act == 'order') {
          // window.clipboardData.setData('Text', command.act)
          // this.$message({
          //   message: '复制订单成功',
          //   type: 'success',
          // })
        } else if (command.act == 'payment') {
          // window.clipboardData.setData('Text', command.act)
          // this.$message({
          //   message: '复制订单成功',
          //   type: 'success',
          // })
        } else if (command.act == 'member') {
          // window.clipboardData.setData('Text', command.act)
          // this.$message({
          //   message: '复制订单成功',
          //   type: 'success',
          // })
        }
      },

      onCopy(e, text) {
        const clipboard = new Clipboard(e.target, { text: () => text });
        clipboard.on('success', e => {
          this.$message({ type: 'success', message: '复制成功' });
          // 释放内存
          clipboard.off('error');
          clipboard.off('success');
          clipboard.destroy();
        });

        clipboard.on('error', e => {
          // 不支持复制
          this.$message({ type: 'waning', message: '该浏览器不支持自动复制' });
          // 释放内存
          clipboard.off('error');
          clipboard.off('success');
          clipboard.destroy();
        });
        clipboard.onClick(e);
      },
      composeValue(item, row) {
        return {
          act: item,
          row: row,
        };
      },
      handle(row, column, event, cell) {
        this.tab === 1 ? (this.tab = 2) : (this.tab = 1);
        if (this.tab === 2 && row.orderId) {
          //获取支付基础信息
          getSimpleDetail({ orderId: row.orderId }).then(result => {
            this.paymentOrder = result;
          });
          //获取相关支付信息
          listPaymentOrders({ orderId: row.orderId }).then(res => {
            this.aboutPay = res;
          });
          this.listRefundOrderList(row.orderId);
          this.listOrderEventList(row.orderId);
          this.getMallBackStatusList(row.orderId);
        }
      },
      async listRefundOrderList(orderId) {
        //获取退款列表
        listRefundOrders({ orderId: orderId }).then(result => {
          this.refundList = result.list;
          this.refundNum = result.num;
          this.msg = result.msg;
          this.refundedNum = result.refundedNum;
          this.refundedMoney = result.refundedMoney;
          this.refundingMoney = result.refundingMoney;
        });
      },
      async listOrderEventList(orderId) {
        //获取日志和事件
        listOrderEvents({ orderId: orderId }).then(result => {
          this.eventLogs = result;
          if (this.eventLogs.length > 0) {
            this.showJson(this.eventLogs[0]);
          }
        });
      },

      strToJson(str) {
        var json = eval('(' + str + ')');
        return json;
      },
      handleCheckSelectable(row) {
        return row.status === '3';
      },
      setData() {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
        this.searchDate = [
          parseTime(start, '{y}-{m}-{d}'),
          parseTime(end, '{y}-{m}-{d}'),
        ];
      },
      getTenantTest(id) {
        let text = '';
        this.tenantIdList.forEach(item => {
          if (item.id === id) {
            text = item.name;
          }
        });
        return text;
      },

      getParams() {
        let start = parseTime(this.searchDate[0], '{y}-{m}-{d}') + ' 00:00:00';
        let end = parseTime(this.searchDate[1], '{y}-{m}-{d}') + ' 23:59:59';
        this.searchParams.tradeBeginTime = this.searchDate ? start : '';
        this.searchParams.tradeEndTime = this.searchDate ? end : '';
        if (this.fileUrl) {
          this.payFileUrl = fileUrl;
        }
        const params = {
          ...this.searchParams,
          page: this.pagination.pageSize,
          pageSize: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        this.loading = true;
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        if (this.searchDate.length < 1) {
          this.$message.error('时间必选，不能为空哦！');
          this.loading = false;
          return false;
        }
        // if (this.payFileUrl && this.payFileUrl != '') {
        //   const difyRes = await queryDify({
        //     inputs: {
        //       image_url: {
        //         transfer_method: 'remote_url',
        //         url: this.payFileUrl,
        //         type: 'image',
        //       },
        //     },
        //     response_mode: 'blocking',
        //     user: '2f013ef1-7dc8-4a51-be3c-be5a12a42189',
        //   });
        //   console.log('difyRes' + difyRes);
        // }

        const params = this.getParams();
        this.options.loading = true;
        const res = await tradeList(params);
        this.options.loading = false;
        this.list = res ? res.records : [];
        this.pagination.total = res ? res.total : 0;
        this.loading = false;
      },

      open(val) {
        if (val) {
          this.title = '修改';
          this.initFile = val.url;
        }
        this.visible = true;
      },
      confirm() {
        this.showPayFileDialog = false;
        this.setData();
        this.getList();
        this.$refs.uploadImg.clearFiles();
        this.searchParams.payFileUrl = '';
      },
      cancelShowPayFile() {
        this.searchParams.payFileUrl = '';
        this.showPayFileDialog = false;
        this.$refs.uploadImg.clearFiles();
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.setData();
        this.getList();
      },
      handleSelectionChange(val) {
        this.selectArr = val;
      },
      onClose() {
        Object.assign(this.$data.saveParams, this.$options.data().saveParams);
      },
      getStatusArr() {
        return this.selectArr.every(item => {
          return item.status === '3';
        });
      },

      onExport() {
        this.$btnPermission('order-export').then(res => {
          const params = this.getParams();
          exportExcel(
            params,
            '/api/pay-dashboard/orderManage/trade/exportTrade',
            'get',
          ).then(res => {
            downloadFile(res.data, '交易数据列表', 'csv');
          });
        });
      },
      changeImage(file) {
        this.searchParams.payFileUrl = file.file_url;
      },
      handleSizeChange(size) {
        // 切换每页显示的数量
        this.pagination.pageLimit = size;
        this.setData();
        this.getList();
      },
      handleIndexChange(current) {
        // 切换页码
        this.pagination.pageSize = current;
        this.setData();
        this.getList();
      },
      handleRowClick(row, event, column) {
        this.$emit('row-click', row, event, column);
      },
    },
  };
</script>
<style lang="scss">
  .item {
    border-right: 2px solid #bebebe;
    height: 3rem;
    margin-right: 5px;
    width: 100%;
  }

  .item > p:first-child {
    font-size: 1rem;
  }

  .detail {
    display: block;
    width: 100%;
  }

  .detail > ul {
    width: 25%;
    float: left;
    font-size: 0.8rem;
    line-height: 1.6rem;
    padding: 0;
  }

  .detail > ul > li {
    list-style-type: none;
    color: #656565;
    text-align: left;
  }

  .trade-detail {
    margin-top: 2rem;
    display: block;
    overflow: auto;
  }

  .trade-detail > ul {
    width: 20%;
    float: left;
    font-size: 0.8rem;
    line-height: 1.6rem;
    padding: 0;
  }

  .trade-detail > ul > li {
    list-style-type: none;
    color: #656565;
    text-align: left;
    line-height: 1.8rem;
  }

  .refund-detail {
    margin-top: 2rem;
    width: 100%;
    display: block;
  }

  .refund-detail > .refund-body {
    width: 100%;
    overflow: hidden;
  }

  .refund-body > .redetail > ul {
    width: 25%;
    float: left;
    list-style-type: none;
    font-size: 0.8rem;
    line-height: 2rem;
    padding: 0;
  }

  .refund-body > .redetail {
    width: 30%;
    float: left;
  }

  .refund-body > .relist {
    width: 70%;
    float: left;
  }

  td {
    cursor: pointer;
  }

  .titleClass {
    background-color: snow;
    font-weight: bold;
  }

  .titleClass .cell {
    font-weight: bold !important;
  }

  .align {
    text-align: right;
  }

  .times {
    margin-top: 4rem;
  }

  .el-timeline {
    padding: 0;
  }

  .about-pay {
    margin-top: 2rem;
  }

  .eventLog {
    overflow: hidden;
    width: 100%;
    margin-top: 2rem;
  }

  .eventLog > .time-line {
    float: left;
    overflow: auto;
    width: 48%;
    height: 40rem;
    margin-right: 3%;
  }

  .eventLog > .log-event {
    float: left;
    overflow: auto;
    height: 40rem;
    width: 45%;
    padding-left: 10px;
  }

  .put-away {
    height: 400px;
    overflow: hidden;
    position: relative;
    padding: 10px 0 10px 40px;
  }

  .turn-on {
    height: auto;
  }

  .btn {
    // width: 80px;
    // height: 30px;
    // display: flex;
    // align-items: center;
    // justify-content: center;
    // border: 1px solid #666;
    margin-left: 140px;
  }

  .first {
    position: absolute;
    left: 0;
    color: #999;
  }

  .last {
    position: absolute;
    left: 0;
  }

  .key {
    color: #466acc;
  }

  .value {
    color: #02966e;
  }

  .mask {
    width: 500px;
    height: 40px;
    display: flex;
    align-items: center;
    position: absolute;
    bottom: 0rem;
    left: 15rem;
  }
</style>
