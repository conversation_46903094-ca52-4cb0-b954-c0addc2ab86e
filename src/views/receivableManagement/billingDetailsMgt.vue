<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-06-20 10:39:00
 * @LastEditTime: 2022-07-18 15:55:50
 * @LastEditors: xuxiang
 * @Reference: 
-->

<template>
  <div>
    <el-form inline>
      <el-form-item label="计费单明细行号:">
        <el-input
          v-model="searchParams.id"
          clearable
          placeholder="请输入计费单明细行号"
        ></el-input>
      </el-form-item>
      <el-form-item label="关联计费单号:">
        <el-input
          v-model="searchParams.relatedChargeItemNo"
          placeholder="请输入关联计费单号"
          clearable
        />
      </el-form-item>
      <el-form-item label="po单号:">
        <el-input
          v-model="searchParams.poNo"
          clearable
          placeholder="请输入po单号"
        ></el-input>
      </el-form-item>
      <el-form-item label="原始单号:">
        <el-input
          v-model="searchParams.orderNo"
          clearable
          placeholder="请输入原始单号"
        ></el-input>
      </el-form-item>
      <el-form-item label="关联合同号:">
        <el-input
          v-model="searchParams.relatedContractNo"
          clearable
          placeholder="请输入关联合同号"
        ></el-input>
      </el-form-item>

      <el-form-item label="原始返利单号:">
        <el-input
          v-model="searchParams.tempRebateNo"
          clearable
          placeholder="请输入原始返利单号"
        ></el-input>
      </el-form-item>
      <el-form-item label="计费单号:">
        <el-input
          v-model="searchParams.chargeItemNo"
          clearable
          placeholder="请输入计费单号"
        ></el-input>
      </el-form-item>
      <el-form-item label="公司名称:">
        <el-select
          v-model="searchParams.companyCode"
          clearable
          filterable
          placeholder="请选择公司名称"
        >
          <el-option
            v-for="item in subjectDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="对方名称:">
        <el-select
          v-model="searchParams.otherSideCode"
          clearable
          filterable
          placeholder="请选择对方名称"
        >
          <el-option
            v-for="item in supplyDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="业务线:">
        <el-select
          v-model="searchParams.bizLine"
          clearable
          placeholder="请选择业务线"
        >
          <el-option
            v-for="item in bizLineId"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="条形码:">
        <el-input
          v-model="searchParams.goodsBarCode"
          clearable
          placeholder="请输入条形码"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <ac-permission-button
          btn-text="导出"
          permission-key=""
          @click="onExport"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="chargeItemNo" slot-scope="scope">
        <div
          style="color: blue; cursor: pointer"
          @click="toChargeItem(scope.row)"
        >
          {{ scope.row.chargeItemNo }}
        </div>
      </template>
    </dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import { getListSelector } from '@/api/copingManagement';
  import { newExportExcel } from '@/api/blob';
  import { downloadFile } from '@/utils';
  import {
    chargeItemDetailList,
    settleItemGetSelector,
  } from '@/api/receivableManagement';

  export default {
    name: 'BillingDetailsMgt',
    components: {
      dynamictable,
    },
    beforeRouteEnter(to, from, next) {
      if (to.matched && to.matched.length > 2)
        to.matched.splice(1, to.matched.length - 2);
      next();
    },
    data() {
      let columns = [
        {
          prop: 'id',
          label: '计费单明细行号',
        },
        {
          prop: 'chargeItemNo',
          label: '计费单号',
          scopedSlots: { customRender: 'chargeItemNo' },
        },
        {
          prop: 'poNo',
          label: 'po单号',
        },
        {
          prop: 'bizNo',
          label: '原始单号',
        },
        {
          prop: 'tempRebateNo',
          label: '原始返利单号',
        },
        {
          prop: 'companyName',
          label: '公司名称',
        },
        {
          prop: 'otherSideName',
          label: '对方名称',
        },
        {
          prop: 'bizLineDesc',
          label: '业务类型',
        },

        {
          prop: 'directionDesc',
          label: '收入类型',
        },
        {
          prop: 'rebateMethodDesc',
          label: '返利方式',
        },
        {
          prop: 'rebateInfo',
          label: '返点比例/金额',
        },
        {
          prop: 'adjustRebate',
          label: '调整返利金额/比例',
        },
        {
          prop: 'receivableTaxInclusive',
          label: '应收（含税）',
        },
        {
          prop: 'receivableTaxExcluded',
          label: '应收（未税）',
        },
        {
          prop: 'receivableTaxValue',
          label: '应收（税额）',
        },
        {
          prop: 'actualPurchaseQuantity',
          label: '实际采购数量',
        },
        {
          prop: 'actualReceiveQuantity',
          label: '实收数量',
        },
        // {
        //   prop: 'goodsCode',
        //   label: '商品编码',
        // },
        {
          prop: 'goodsBarCode',
          label: '条形码',
        },
        {
          prop: 'goodsName',
          label: '商品名称',
        },

        {
          prop: 'brandName',
          label: '品牌',
        },
        // {
        //   prop: 'brandCategory',
        //   label: '品类',
        // },
        // {
        //   prop: 'quantity',
        //   label: '数量',
        // },
        {
          prop: 'unitPrice',
          label: '单价(含税)',
        },
        {
          prop: 'adjustPrice',
          label: '调整单价',
        },
        {
          prop: 'taxRate',
          label: '税率',
        },
        // {
        //   prop: 'unitPriceTaxValue',
        //   label: '单价(税额)',
        // },
        // {
        //   prop: 'unitPriceTaxExcluded',
        //   label: '单价(未税)',
        // },
        // {
        //   prop: 'receivableTaxInclusive',
        //   label: '总额',
        // },
      ];

      return {
        searchParams: {
          id: '',
          orderNo: '',
          tempRebateNo: '',
          chargeItemNo: '',
          companyCode: '',
          otherSideCode: '',
          bizLine: '',
          goodsBarCode: '',
          poNo: '',
          relatedContractNo: '',
          relatedChargeItemNo: '',
        },
        list: [],
        supplyDict: [],
        subjectDict: [],
        bizLineId: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
        columns,
      };
    },
    created() {
      settleItemGetSelector().then(res => {
        if (res) {
          this.bizLineId = res.bizLineList;
        }
      });
      getListSelector({}).then(res => {
        if (res) {
          this.supplyDict = res.supplyDict;
          this.subjectDict = res.subjectDict;
        }
      });
      const { chargeItemNo } = this.$route.query;
      if (chargeItemNo) this.searchParams.chargeItemNo = chargeItemNo;

      this.getList(true);
    },
    methods: {
      onExport() {
        const params = this.getParams();
        this.$confirm('确定将该数据导出?', '导出', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          newExportExcel(
            params,
            '/api/finance-receivable/chargeItemDetail/exportList',
            'post',
          ).then(res => {
            downloadFile(res.data, '计费明细列表');
          });
        });
      },
      getParams() {
        const params = {
          ...this.searchParams,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await chargeItemDetailList(params);
        this.options.loading = false;

        if (res) {
          this.list = res ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        }
      },
      toChargeItem(row) {
        this.$router.push({
          path: './billingDocumentMgt',
          query: {
            chargeItemNo: row.chargeItemNo,
          },
        });
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },
    },
  };
</script>
<style lang="scss"></style>
