<template>
  <div>
    <div>
      <el-button
        style="font-size: 16px; margin-bottom: 10px"
        type="text"
        @click="handelJump()"
      >
        返回
      </el-button>
    </div>
    <div v-if="tabPage === 1">
      <el-tabs v-model="activeName" type="card" @tab-click="handleTab">
        <el-tab-pane label="明细信息" name="1" />
        <el-tab-pane label="操作日志" name="2" />
      </el-tabs>
      <div v-if="activeName === '1'">
        <search
          v-model="searchParams"
          :tab="tab"
          :date="billDate"
          :biz-line-id="bizLineId"
          :supply-dict="supplyDict"
          :subject-dict="subjectDict"
          @onSearch="onSearch"
          @onReset="onReset"
        ></search>
        <dynamictable
          :data-source="list"
          :columns="getColumns()"
          :options="options"
          :pagination="pagination"
          :fetch="getList"
        >
          <template slot="operation" slot-scope="scope">
            <ac-permission-button
              slot="reference"
              btn-text="查看"
              type="text"
              size="small"
              permission-key=""
              @click="handleDetails(scope.row)"
            ></ac-permission-button>
          </template>
        </dynamictable>
      </div>
      <div v-else>
        <dynamictable
          :data-source="list"
          :columns="getColumns()"
          :options="options"
          :fetch="getList"
        ></dynamictable>
      </div>
    </div>
    <div v-if="tabPage === 2">
      <div style="margin-bottom: 10px">计费单号：{{ row.chargeItemNo }}</div>
      <dynamictable
        :data-source="list"
        :columns="getColumns()"
        :options="options"
        :pagination="pagination"
        :fetch="getList"
      ></dynamictable>
    </div>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import search from './search';
  import {
    chargeItemList,
    settleItemOperateLog,
    chargeItemGetDetailList,
  } from '@/api/receivableManagement';
  import { parseTime, initSearchParams, debounce } from '@/utils';
  export default {
    components: {
      dynamictable,
      search,
    },
    props: {
      tab: {
        type: Number,
        default: 1,
      },
      currentRow: {
        type: Object,
        default: null,
      },
      bizLineId: {
        type: Array,
        default: () => [],
      },
      supplyDict: {
        type: Array,
        default: () => [],
      },
      subjectDict: {
        type: Array,
        default: () => [],
      },
    },

    data() {
      return {
        row: null,
        billDate: [], // 日期
        tabPage: 1, // 1计费单 ，2计费单详情
        activeName: '1',
        searchParams: {
          bizLine: '',
          chargeItemNo: '',
          chargeType: '',
          companyCode: '',
          createBillStatus: '',
          endChargeDate: '',
          otherSideCode: '',
          receiveMethod: '',
          receiveStatus: '',
          settleItemNo: '',
          startChargeDate: '',
        },
        list: [],
        options: {
          loading: false,
          border: true,
        },
        pageSize: 1,
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },
    created() {
      this.setSettleItemNo();
    },
    methods: {
      setSettleItemNo() {
        const { currentRow } = this;
        if (currentRow) {
          this.searchParams.settleItemNo = currentRow.settleItemNo;
        }
      },
      handleDetails(row) {
        this.tabPage = 2;
        this.pageSize = this.pagination.pageSize;
        this.row = row;
        Object.assign(this.$data.pagination, this.$options.data().pagination);
        this.getList(true);
      },
      handleTab() {
        Object.assign(this.$data.pagination, this.$options.data().pagination);
        this.getList(true);
      },
      handelJump() {
        if (this.tabPage === 2) {
          this.pagination.pageSize = this.pageSize;
          this.tabPage = 1;
          return;
        }
        this.$emit('goBack');
      },
      getParams() {
        const params = {
          ...this.searchParams,
          startChargeDate: this.billDate.length
            ? parseTime(this.billDate[0], '{y}-{m}-{d}')
            : '',
          endChargeDate: this.billDate.length
            ? parseTime(this.billDate[1], '{y}-{m}-{d}')
            : '',
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return initSearchParams(params);
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const { activeName, tabPage, row } = this;
        let params = {
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
          chargeItemNo: row && row.chargeItemNo,
        };
        if (activeName === '1' && tabPage === 1) {
          params = this.getParams();
        }

        this.options.loading = true;
        const getApiList =
          tabPage === 2
            ? chargeItemGetDetailList
            : activeName === '1'
            ? chargeItemList
            : settleItemOperateLog;
        const res = await getApiList(params);
        if (res) {
          this.list = res.records;
          this.pagination.total = res.total;
        }
        this.options.loading = false;
      },
      onSearch: debounce(function (date, tag) {
        this.billDate = date;
        this.getList(!!tag);
      }, 1000),
      // 重置
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.setSettleItemNo();
      },
      getColumns() {
        if (this.tabPage === 2) {
          return [
            // {
            //   prop: 'chargeItemNo',
            //   label: '计费单号',
            // },
            {
              prop: 'bizNo',
              label: '单据编号',
            },
            {
              prop: 'relatedContractNo',
              label: '关联合同编号',
            },
            {
              prop: 'companyName',
              label: '公司名称',
            },
            {
              prop: 'otherSideName',
              label: '对方名称',
            },
            {
              prop: 'bizLineDesc',
              label: '业务线',
            },
            // {
            //   prop: 'goodsCode',
            //   label: '收款类型',
            // },
            // {
            //   prop: 'goodsCode',
            //   label: '单据类型',
            // },
            // {
            //   prop: 'goodsCode',
            //   label: '金额类型',
            // },
            {
              prop: 'purchaseAmountTaxInclusive',
              label: '采购金额（含税）',
            },
            {
              prop: 'purchaseAmountTaxValue',
              label: '采购金额（税额）',
            },
            {
              prop: 'purchaseAmountTaxExcluded',
              label: '采购金额（未税）',
            },
            {
              prop: 'actualPurchaseQuantity',
              label: '实际采购数量',
            },
            {
              prop: 'actualReceiveQuantity',
              label: '实收数量',
            },
            // {
            //   prop: 'goodsCode',
            //   label: '订单状态',
            // },

            {
              prop: 'goodsCode',
              label: '商品编码',
            },
            {
              prop: 'goodsBarCode',
              label: '条形码',
            },
            {
              prop: 'goodsName',
              label: '商品名称',
            },
            {
              prop: 'brandName',
              label: '品牌',
            },
            {
              prop: 'brandCategory',
              label: '品类',
            },
            {
              prop: 'quantity',
              label: '数量',
            },
            {
              prop: 'unitPrice',
              label: '单价（含税）',
            },
            {
              prop: 'unitPriceTaxValue',
              label: '单价（税额）',
            },
            {
              prop: 'unitPriceTaxExcluded',
              label: '单价（未税）',
            },
            {
              prop: 'receivableTaxInclusive',
              label: '总额',
            },
            // {
            //   prop: 'taxRadioStr',
            //   label: '税率',
            // },
          ];
        }
        const columns = [
          {
            prop: 'chargeItemNo',
            label: '计费单编号',
          },
          {
            prop: 'relatedContractNo',
            label: '关联合同编号',
          },
          {
            prop: 'companyName',
            label: '公司名称',
          },
          {
            prop: 'otherSideName',
            label: '对方名称',
          },
          {
            prop: 'bizLineDesc',
            label: '业务线',
          },
          {
            prop: 'chargeTypeDesc',
            label: '收款类型',
          },
          {
            prop: 'receiveMethodDesc',
            label: '收款方式',
          },
          {
            prop: 'currency',
            label: '记账币种',
          },
          {
            prop: 'receivableTaxInclusive',
            label: '应收(含税)',
          },
          {
            prop: 'receivableTaxExcluded',
            label: '应收(未税)',
          },
          {
            prop: 'receivableTaxValue',
            label: '应收(税额)',
          },
          {
            prop: 'unreceiveTaxInclusive',
            label: '待收(含税)',
          },
          // {
          //   prop: 'unreceiveTaxExcluded',
          //   label: '待收(未税)',
          // },
          // {
          //   prop: 'unreceiveTaxValue',
          //   label: '待收(税额)',
          // },
          {
            prop: 'receiveTaxInclusive',
            label: '实收(含税)',
          },
          // {
          //   prop: 'receiveTaxValue',
          //   label: '实收(税额)',
          // },
          // {
          //   prop: 'receiveTaxExcluded',
          //   label: '实收(未税)',
          // },
          {
            prop: 'createTime',
            label: '创建时间',
          },
          {
            prop: 'receiveTime',
            label: '收款完成时间',
          },
          {
            prop: 'receiveStatusDesc',
            label: '收款状态',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '80',
            scopedSlots: { customRender: 'operation' },
          },
        ];
        const columns1 = [
          {
            prop: 'id',
            label: '操作ID',
          },
          {
            prop: 'operator',
            label: '操作人姓名',
          },
          {
            prop: 'action',
            label: '操作动作',
          },
          {
            prop: 'createTime',
            label: '操作时间',
          },
        ];
        return this.activeName === '1' ? columns : columns1;
      },
    },
  };
</script>

<style lang="scss"></style>
