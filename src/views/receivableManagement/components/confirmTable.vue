<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-09-16 15:52:13
 * @LastEditTime: 2022-09-20 11:09:48
 * @LastEditors: xuxiang
 * @Reference: 
-->

<template>
  <el-dialog
    :visible.sync="showDialog"
    :close-on-click-modal="false"
    width="1200px"
  >
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
    ></dynamictable>

    <div slot="footer" class="dialog-footer">
      <el-button @click="showDialog = false">取 消</el-button>
      <el-button type="primary" :loading="buttonLoading" @click="handleOK">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { debounce } from '@/utils';
  import dynamictable from '@/components/dynamic-table';

  export default {
    components: {
      dynamictable,
    },
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      list: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        buttonLoading: false,
        options: {
          loading: false,
          border: true,
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {},
    },
    created() {},
    methods: {
      handleOK: debounce(function () {
        this.$emit('onOK');
      }, 1000),
      btnLoading(flag) {
        this.buttonLoading = flag;
      },
      getColumns() {
        const columns = [
          {
            prop: 'contractNo',
            label: '合同编号',
          },

          {
            prop: 'purchaseSubjectName',
            label: '公司名称',
          },
          {
            prop: 'supplierSubjectName',
            label: '对方名称',
          },
          {
            prop: 'bizLineDesc',
            label: '业务类型',
          },
          {
            prop: 'chargeTypeDesc',
            label: '费用类型',
          },
          {
            prop: 'corporateMethodDesc',
            label: '合作方式',
          },

          {
            prop: 'performanceMethodDesc',
            label: '履约类型',
          },

          {
            prop: 'receiveMethodDesc',
            label: '收款方式',
          },
          {
            prop: 'directionDesc',
            label: '收入类型',
          },

          {
            prop: 'settleCurrency',
            label: '记账币种',
          },
          {
            prop: 'receivableTaxInclusive',
            label: '应收金额',
          },
          {
            prop: 'rate',
            label: '费用税率',
          },
          {
            prop: 'remark',
            label: '备注',
          },
        ];

        return columns;
      },
    },
  };
</script>
<style lang="scss"></style>
