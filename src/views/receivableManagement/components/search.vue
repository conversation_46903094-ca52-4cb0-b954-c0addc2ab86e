<template>
  <div class="searchParams">
    <el-form ref="search" inline :model="searchParams">
      <el-form-item label="账单日期:">
        <el-date-picker
          v-model="billDate"
          type="daterange"
          :clearable="false"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item v-if="tab === 1" label="结算单号:">
        <el-input
          v-model="searchParams.settleItemNo"
          placeholder="请输入结算单号"
          clearable
        />
      </el-form-item>
      <el-form-item v-if="tab === 1" label="关联结算单号:">
        <el-input
          v-model="searchParams.relatedSettleItemNo"
          placeholder="请输入关联结算单号"
          clearable
        />
      </el-form-item>
      <el-form-item v-if="tab === 1" label="账单单号:">
        <el-input
          v-model="searchParams.billNo"
          placeholder="请输入账单单号"
          clearable
        />
      </el-form-item>
      <el-form-item label="公司名称:">
        <el-select
          v-model="searchParams.companyCode"
          filterable
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in subjectDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="对方名称:">
        <el-select
          v-model="searchParams.otherSideCode"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in supplyDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="业务线:">
        <el-select
          v-model="searchParams.bizLine"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in bizLineId"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="收款类型:">
        <el-select
          v-model="searchParams.chargeType"
          multiple
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in contractStatusDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="收款方式:">
        <el-select
          v-model="searchParams.receiveMethod"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in receiveMethodList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="收款状态:">
        <el-select
          v-model="searchParams.receiveStatus"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in receiveStatusList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="账单状态:">
        <el-select
          v-model="searchParams.createBillStatus"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in billStatusList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>

        <!-- <el-button v-if="tab === 1" type="primary" @click="showModal = true">
          导入
        </el-button> -->

        <ac-permission-button
          v-if="tab === 1"
          btn-text="导出"
          permission-key="accountsReceivableSettlement-export"
          @click="onExport"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <serviceChargeModal
      v-model="showModal"
      @onDownload="onDownload"
      @onErrDownload="onErrDownload"
      @onGet="onSearch(true)"
    ></serviceChargeModal>
  </div>
</template>

<script>
  import serviceChargeModal from './serviceChargeModal.vue';
  import { setInitData, debounce } from '@/utils';

  export default {
    components: {
      serviceChargeModal,
    },
    model: {
      prop: 'searchParams',
      event: 'change',
    },
    props: {
      searchParams: {
        type: Object,
        default: () => {},
      },
      tab: {
        type: Number,
        default: 1,
      },
      date: {
        type: Array,
        default: () => [],
      },
      bizLineId: {
        type: Array,
        default: () => [],
      },
      supplyDict: {
        type: Array,
        default: () => [],
      },
      subjectDict: {
        type: Array,
        default: () => [],
      },
      billStatusList: {
        type: Array,
        default: () => [],
      },
      receiveStatusList: {
        type: Array,
        default: () => [],
      },
      receiveMethodList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        showModal: false,
        billDate: '',
      };
    },
    created() {
      if (this.date.length) {
        this.billDate = this.date;
      } else this.billDate = setInitData(30);
      this.onSearch(false);
    },
    methods: {
      onErrDownload: debounce(async function (data) {
        this.$emit('onErrDownload', data);
      }, 1000),
      onDownload: debounce(async function (data) {
        this.$emit('onDownload', data);
      }, 1000),

      onExport() {
        this.$emit('onExport', this.billDate);
      },
      onSearch(tag) {
        // tag 是否是点击搜索
        this.$emit('onSearch', this.billDate, tag);
      },
      onReset() {
        this.billDate = setInitData(30);
        this.$emit('onReset');
      },
    },
  };
</script>

<style lang="scss" scoped></style>
