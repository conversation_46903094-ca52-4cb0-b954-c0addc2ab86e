<template>
  <div>
    <el-form inline :model="searchParams">
      <el-form-item label="账单日期:">
        <el-date-picker
          v-model="billDate"
          :type="activeName === '1' ? 'daterange' : 'monthrange'"
          range-separator="至"
          :value-format="activeName === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
          :format="activeName === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="贸易主体:">
        <!-- <el-input
          v-model="searchParams.salesEntityCode"
          placeholder="请输入贸易主体"
        /> -->
        <el-select
          v-model="searchParams.salesEntityCode"
          filterable
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in subjectDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="收款主体:">
        <!-- <el-input
          v-model="searchParams.companyId"
          placeholder="请输入收款主体"
        /> -->
        <el-select
          v-model="searchParams.companyId"
          filterable
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in companyList"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="类型:">
        <el-select
          v-model="searchParams.dataType"
          clearable
          placeholder="请选择类型"
        >
          <el-option label="已支付" value="1"></el-option>
          <el-option label="已支付未签收" value="2"></el-option>
          <el-option label="已签收" value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <!-- <el-button type="primary" @click="down_excel">导出</el-button> -->
      </el-form-item>
    </el-form>
    <el-tabs v-model="activeName" type="card" @tab-click="handleTabClick">
      <el-tab-pane label="日汇总" name="1"></el-tab-pane>
      <el-tab-pane label="月汇总" name="2"></el-tab-pane>
    </el-tabs>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="明细"
          permission-key=""
          @click="go(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import { setInitData, parseTime } from '@/utils';

  import { getListSelector } from '@/api/copingManagement';
  import {
    advanceIncomeSummaryList,
    advanceIncomeItemSelector,
  } from '@/api/receivableManagement';
  import dayjs from 'dayjs';

  export default {
    name: 'AdvanceIncomeMgt',
    components: {
      dynamictable,
    },
    data() {
      return {
        billDate: '',
        activeName: '1',
        companyList: [],
        subjectDict: [],
        searchParams: {
          startDate: '',
          endDate: '',
          salesEntityCode: '',
          companyId: '',
          dataType: '',
          statType: 'D',
        },
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          index: true,
          // indexName: '',
        },
      };
    },

    created() {
      const { activeName = '1', month } = this.$route.query;
      this.activeName = activeName;
      // if (month) {
      //   const lastday = dayjs(month).endOf('month').format('YYYY-MM-DD');
      //   this.billDate = [parseTime(month, '{y}-{m}-01'), lastday];
      // } else this.billDate = setInitData(30);
      this.init();

      getListSelector({}).then(res => {
        if (res) {
          this.subjectDict = res.subjectDict;
        }
      });
      advanceIncomeItemSelector().then(res => {
        if (res) {
          this.companyList = res.company;
        }
      });

      this.getList();
    },
    methods: {
      init() {
        const {
          month,
          salesEntityCode = '',
          merchantSubjectCode = '',
        } = this.$route.query;
        this.searchParams.salesEntityCode = salesEntityCode;
        this.searchParams.companyId = merchantSubjectCode;

        const startTime = dayjs(month ? month : undefined).format('YYYY-MM-01');
        const endTime = dayjs().format('YYYY-MM-DD');
        // console.log(endTime, 'endTime');
        // const lastday = dayjs(month).endOf('month').format('YYYY-MM-DD');
        this.billDate = [startTime, endTime];
      },
      go(row) {
        console.log(row, 'aaa');
        // this.$router.push({
        //   path: `/receivableManagement/${
        //     this.activeName === '2'
        //       ? 'advanceIncomeDetails'
        //       : 'advanceIncomeItemDetails'
        //   }`,
        //   query: {
        //     dataType: row.dataType,
        //     billDate: row.billDate,
        //     salesEntityCode: row.salesEntityCode,
        //     companyId: row.companyId,
        //   },
        // });
        console.log({
          dataType: row.dataType,
          billDate: row.billDate,
          salesEntityCode: row.salesEntityCode,
          companyId: row.companyId,
          activeName: this.activeName,
        });
        var pathData = '';
        if (row.dataType == 1) {
          pathData = 'advanceIncomeDetails';
        } else if (row.dataType == 2) {
          pathData = 'advanceIncomeUnsignedDetails';
        } else if (row.dataType == 3) {
          pathData = 'advanceIncomeSignedInDetails';
        }
        this.$router.push({
          path: pathData,
          query: {
            dataType: row.dataType,
            billDate: row.billDate,
            salesEntityCode: row.salesEntityCode,
            companyId: row.companyId,
            activeName: this.activeName,
          },
        });
      },
      handleTabClick({ name }) {
        this.getList(true);
      },
      down_excel() {
        this.$message.success('导出成功');
      },
      getParams() {
        const { activeName, billDate } = this;
        const startDate = billDate
          ? parseTime(
              billDate[0],
              activeName === '1' ? '{y}-{m}-{d}' : '{y}-{m}-01',
            )
          : '';
        const lastday = dayjs(billDate[0]).endOf('month').format('YYYY-MM-DD');

        const endDate = billDate
          ? parseTime(billDate[1], activeName === '1' ? '{y}-{m}-{d}' : lastday)
          : '';
        this.searchParams.startDate = startDate;
        this.searchParams.endDate = endDate;
        const params = {
          ...this.searchParams,
          statType: activeName === '1' ? 'D' : 'M',
          page: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;

        const res = await advanceIncomeSummaryList(params);
        if (res) {
          this.list = res ? res.list : [];
          this.pagination.total = res ? res.total : 0;
        }
        this.options.loading = false;
      },
      onReset() {
        // if (month) {
        //   this.billDate = [month, month];
        // } else {
        //   this.billDate = setInitData(30);
        // }
        this.init();

        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },

      getColumns() {
        const columns = [
          {
            prop: 'billDate',
            label: '账期',
          },
          {
            prop: 'salesEntityName',
            label: '贸易主体',
          },
          {
            prop: 'companyName',
            label: '收款主体',
          },
          {
            prop: 'dataTypeName',
            label: '类型',
          },
          {
            prop: 'summaryAmount',
            label: '汇总金额',
          },
          {
            prop: 'positiveAmount',
            label: '正向金额',
          },
          {
            prop: 'reverseAmount',
            label: '逆向金额',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '200',
            scopedSlots: { customRender: 'operation' },
          },
        ];
        return columns;
      },
    },
  };
</script>
<style lang="scss"></style>
