<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-16 14:04:04
 * @LastEditTime: 2023-01-18 11:39:21
 * @LastEditors: dddd
 * @Reference: 支付未签明细
-->

<template>
  <div>
    <el-form inline>
      <el-form-item label="账单日期:">
        <el-date-picker
          v-model="searchParams.billDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="贸易主体:">
        <el-select
          v-model="searchParams.salesEntityCode"
          filterable
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in subjectDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="收款主体:">
        <el-select
          v-model="searchParams.companyId"
          filterable
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in companyList"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="支付渠道:">
        <el-select
          v-model="searchParams.channelId"
          clearable
          filterable
          placeholder="请选择支付渠道"
        >
          <el-option
            v-for="item in channelList"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="交易类型:">
        <el-select
          v-model="searchParams.docType"
          clearable
          placeholder="请选择交易类型"
        >
          <el-option
            v-for="item in orderType"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <template>
        <el-form-item label="品牌:">
          <el-select
            v-model="searchParams.brandId"
            filterable
            clearable
            placeholder="请选择品牌"
          >
            <el-option
              v-for="item in brandList"
              :key="item.brandId"
              :label="item.name"
              :value="item.brandId"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="SKUID:">
          <el-input v-model="searchParams.skuId" placeholder="请输入SKUID" />
        </el-form-item> -->
        <el-form-item label="商品编码:">
          <el-input
            v-model="searchParams.barcode"
            placeholder="请输入商品编码"
          />
        </el-form-item>
        <el-form-item label="商品名称:">
          <el-input
            v-model="searchParams.goodsName"
            placeholder="请输入商品名称"
          />
        </el-form-item>
      </template>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button type="warning" @click="$router.go(-1)">返回</el-button>
        <!-- <el-button type="success" @click="down_excel">导出</el-button> -->
      </el-form-item>
    </el-form>

    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="明细"
          permission-key=""
          @click="go(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import { getListSelector } from '@/api/copingManagement';
  import { setInitData } from '@/utils';
  import { parseTime } from '@/utils';

  import {
    advanceIncomeSummaryPaidPagePaidDaySum,
    advanceIncomeSummarySignpageSignedDaySum,
    advanceIncomeItemSelector,
    listBrand,
  } from '@/api/receivableManagement';
  export default {
    name: 'AdvanceIncomeDetails',
    components: {
      dynamictable,
    },

    data() {
      return {
        searchParams: {
          billDate: '',
          brandId: '',
          channelId: '',
          barcode: '',
          docType: '',
          goodsName: '',
          // skuId: '',
          salesEntityCode: '',
          companyId: '',
        },
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          index: true,
        },
        subjectDict: [], //贸易主体
        companyList: [], //收款主体
        channelList: [],
        brandList: [],
        orderType: [
          {
            key: 0,
            value: '全部',
          },
          {
            key: 6,
            value: '正向',
          },
          {
            key: 7,
            value: '逆向',
          },
        ],
      };
    },
    created() {
      listBrand({
        current: 1,
        size: 9999,
      }).then(res => {
        if (res) {
          this.brandList = res.records;
        }
      });
      getListSelector({}).then(res => {
        if (res) {
          this.subjectDict = res.subjectDict;
        }
      });
      advanceIncomeItemSelector().then(res => {
        if (res) {
          this.companyList = res.company;
          this.channelList = res.channel;
        }
      });
      // 通过明细进入,赋值主体与贸易主体值
      if (this.$route.query.dataType) {
        this.searchParams.salesEntityCode = this.$route.query.salesEntityCode;
        this.searchParams.companyId = this.$route.query.companyId;
      }
      this.getList(true);
    },
    methods: {
      go(row) {
        var dataParams = {};
        // 如果是菜单直接进入
        if (!this.$route.query.dataType) {
          dataParams = {
            dataType: '2',
            salesEntityCode: this.searchParams.salesEntityCode,
            companyId: this.searchParams.companyId,
            activeName: 1,
          };
        } else {
          dataParams = this.$route.query;
        }
        const {
          dataType = '',
          salesEntityCode = '',
          companyId = '',
          activeName = '1',
        } = dataParams;

        this.$router.push({
          path: 'advanceIncomeItemDetails',
          query: {
            dataType,
            salesEntityCode,
            companyId,
            billDate: row.billDate,
            docType: row.docType,
            channelId: row.channelId,
            userLevel: row.userLevel,
            warehouseName: row.warehouseName,
            countryCode: row.countryCode,
            skuId: row.skuId,
            activeName,
          },
        });
      },
      down_excel() {
        this.$message.success('导出成功');
      },
      getParams() {
        var dataParams = {};
        // 从菜单单独进入
        if (!this.$route.query.dataType) {
          if (!this.searchParams.billDate) {
            this.searchParams.billDate = setInitData(30, '{y}-{m}-{d}');
          }
          dataParams = {
            dataType: '2',
            billDate: this.searchParams.billDate,
            salesEntityCode: this.searchParams.salesEntityCode,
            companyId: this.searchParams.companyId,
            activeName: 1,
          };
        } else {
          // 获取url参数
          dataParams = this.$route.query;
        }
        const {
          dataType,
          billDate,
          salesEntityCode,
          companyId,
          activeName = '1',
        } = dataParams;
        let params = {
          // billDateMonth: parseTime(billDate, '{y}-{m}-01'),
          dataType,
          salesEntityCode,
          companyId,
          page: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
          ...this.searchParams,
          // 放置末尾，以实际搜索为准，第一次带过来的不生效
        };
        // 如果是明细跳转过来的
        if (this.$route.query.dataType) {
          if (activeName === '1') {
            params.billDateDay = parseTime(billDate, '{y}-{m}-{d}');
          } else {
            params.billDateMonth = parseTime(billDate, '{y}-{m}-01');
          }
        }
        if (
          params.billDate &&
          Array.isArray(params.billDate) &&
          params.billDate.length > 0
        ) {
          params.startDate = params.billDate[0];
          params.endDate = params.billDate[1];
        }
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();

        const api = advanceIncomeSummarySignpageSignedDaySum;
        this.options.loading = true;
        try {
          const res = await api(params);
          if (res) {
            this.list = res ? res.list : [];
            this.pagination.total = res ? res.total : 0;
          }
          this.options.loading = false;
        } catch (error) {
          this.options.loading = false;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },

      getColumns() {
        return [
          {
            prop: 'billDate',
            label: '账期',
          },
          {
            prop: 'gmv',
            label: 'GMV',
          },
          {
            prop: 'salesEntityName',
            label: '贸易主体',
          },
          {
            prop: 'companyName',
            label: '收款主体',
          },

          {
            prop: 'countryName',
            label: '收货国家',
          },

          {
            prop: 'channelName',
            label: '支付渠道',
          },
          {
            prop: 'docTypeName',
            label: '交易类型',
          },
          {
            prop: 'brandName',
            label: '品牌',
          },

          {
            prop: 'barcode',
            label: '商品编码',
          },
          {
            prop: 'goodsName',
            label: '商品名称',
          },
          {
            prop: 'salesTaxRate',
            label: '税率',
          },
          {
            prop: 'paidTotalAmount',
            label: '商品金额（含税）',
          },
          {
            prop: 'quantity',
            label: '数量',
          },
          {
            prop: 'paidShippingAmount',
            label: '运费（含税）',
          },
          {
            prop: 'paidAllFeeAmount',
            label: '税费（含税）',
          },
          {
            prop: 'paidTotalAmountNotTax',
            label: '商品金额（不含税）',
          },
          {
            prop: 'paidShippingAmountNotTax',
            label: '运费（不含税）',
          },
          {
            prop: 'paidAllFeeAmountNotTax',
            label: '税费（不含税）',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            minWidth: '100',
            maxWidth: '200',
            scopedSlots: { customRender: 'operation' },
          },
        ];
      },
    },
  };
</script>
<style lang="scss"></style>
