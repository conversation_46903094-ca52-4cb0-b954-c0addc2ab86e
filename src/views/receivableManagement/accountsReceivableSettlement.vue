<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-06-20 13:29:21
 * @LastEditTime: 2022-08-29 19:04:13
 * @LastEditors: xuxiang
 * @Reference: 
-->
<template>
  <div v-if="tab === 1">
    <search
      v-model="searchParams"
      :tab="tab"
      :date="billDate"
      :biz-line-id="bizLineId"
      :supply-dict="supplyDict"
      :subject-dict="subjectDict"
      :receive-method-list="receiveMethodList"
      :receive-status-list="receiveStatusList"
      :bill-status-list="billStatusList"
      @onSearch="onSearch"
      @onReset="onReset"
      @onExport="onExport"
      @onDownload="onDownload"
      @onErrDownload="onErrDownload"
    ></search>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :summary-method="summaryMethod"
      :fetch="getList"
    >
      <template slot="billNo" slot-scope="scope">
        <div style="color: blue; cursor: pointer" @click="toBill(scope.row)">
          {{ scope.row.billNo }}
        </div>
      </template>

      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          btn-text="明细"
          type="text"
          size="small"
          permission-key=""
          @click="handelJump(2, scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
  <accountsReceivableSettlementDetails
    v-else
    :current-row="currentRow"
    :tab="tab"
    :biz-line-id="bizLineId"
    :supply-dict="supplyDict"
    :subject-dict="subjectDict"
    @goBack="handelJump(1, null)"
  ></accountsReceivableSettlementDetails>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import search from './components/search.vue';
  import accountsReceivableSettlementDetails from './components/accountsReceivableSettlementDetails.vue';
  import {
    settleItemGetSelector,
    settleItemList,
    settleItemSumData,
  } from '@/api/receivableManagement';
  import { exportExcel } from '@/api/blob';
  import { getListSelector } from '@/api/copingManagement';
  import { debounce, downloadFile, initSearchParams, parseTime } from '@/utils';

  export default {
    name: 'AccountsReceivableSettlement',
    components: {
      dynamictable,
      search,
      accountsReceivableSettlementDetails,
    },

    data() {
      return {
        billDate: [],
        tab: 1,
        list: [],
        currentRow: null,
        searchParams: {
          bizLine: '',
          chargeType: '',
          companyCode: '',
          createBillStatus: '',
          endSettleDate: '',
          otherSideCode: '',
          receiveMethod: '',
          receiveStatus: '',
          settleItemNo: '',
          startSettleDate: '',
          billNo: '',
          relatedSettleItemNo: '',
        },
        bizLineId: [],
        supplyDict: [],
        subjectDict: [],
        receiveMethodList: [],
        receiveStatusList: [],
        billStatusList: [],
        sumData: {},
        options: {
          loading: false,
          border: true,
          showSummary: false,
          // index: true,
          // indexName: 'ID',
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },
    created() {
      const { billNo = '', settleItemNo } = this.$route.query;
      if (billNo) this.searchParams.billNo = billNo;
      if (settleItemNo) this.searchParams.settleItemNo = settleItemNo;

      settleItemGetSelector().then(res => {
        if (res) {
          this.receiveMethodList = res.receiveMethodList;
          this.receiveStatusList = res.receiveStatusList;
          this.billStatusList = res.billStatusList;
          this.bizLineId = res.bizLineList;
        }
      });
      // getSelector({}).then(res => {
      //   if (res) {
      //     this.bizLineId = res.bizLineId;
      //   }
      // });
      getListSelector({}).then(res => {
        if (res) {
          this.supplyDict = res.supplyDict;
          this.subjectDict = res.subjectDict;
        }
      });
    },
    methods: {
      summaryMethod({ columns, data }) {
        const sums = ['汇总'];
        columns.forEach((item, index) => {
          const values = data.map(item1 => Number(item1[item.property]));
          if ([7, 8, 9, 10, 11].includes(index)) {
            sums[index] = this.sumData[item.property];
            // sums[index] = values.reduce((prev, curr) => {
            //   const value = Number(curr);
            //   if (!isNaN(value)) {
            //     return `¥${prev + curr}`;
            //   } else {
            //     return `¥${prev}`;
            //   }
            // }, 0);
          }
        });
        return sums;
      },
      handelJump(tab, currentRow) {
        const { settleItemNo } = currentRow;
        this.$router.push({
          path: 'billingDocumentMgt',
          query: {
            settleItemNo,
          },
        });
        // this.currentRow = currentRow;
        // this.tab = tab;
      },
      getParams() {
        const params = {
          ...this.searchParams,
          startSettleDate: this.billDate.length
            ? parseTime(this.billDate[0], '{y}-{m}-{d}')
            : '',
          endSettleDate: this.billDate.length
            ? parseTime(this.billDate[1], '{y}-{m}-{d}')
            : '',
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return initSearchParams(params);
      },
      async settleItemSumData() {
        const params = this.getParams();
        const res = await settleItemSumData(params);
        if (res) {
          this.sumData = res.data;
        }
      },
      onErrDownload(fileId) {
        exportExcel(
          { fileId },
          '/api/finance-receivable/chargeItemDetail/downloadImportErrorExcel',
          'get',
        ).then(res => {
          downloadFile(res.data, '错误的excel文件');
        });
      },
      onDownload(data) {
        exportExcel(
          data,
          '/api/finance-receivable/chargeItemDetail/downloadDetailExcelTemplate',
          'get',
        ).then(res => {
          downloadFile(res.data, '增值服务费导入模版');
        });
      },
      onExport(date) {
        this.billDate = date;
        const params = this.getParams();
        this.$confirm('确定将该数据导出?', '导出', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          exportExcel(
            params,
            '/api/finance-receivable/settleItem/exportList',
            'post',
          ).then(res => {
            downloadFile(res.data, '结算单列表');
          });
        });
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        Promise.all([settleItemList(params)]).then(res => {
          if (res && res.length) {
            const data = res[0];
            // const totalData = res[1];
            this.list = data.records;
            // this.sumData = totalData;
            this.pagination.total = data.total;
          }
          this.options.loading = false;
        });
        // const res = await p(params);
        // if (res) {
        //   this.list = res.records;
        //   this.pagination.total = res.total;
        // }
        // this.options.loading = false;
      },
      onSearch: debounce(function (date, tag) {
        this.billDate = date;
        this.getList(!!tag);
      }, 1000),

      toBill(row) {
        let path = '/copingManagement/bill/billConsignment';

        if (row.corporateMethodDesc === '经销') {
          path = '/copingManagement/bill/billDistribution';
        }

        this.$router.push({
          path: path,
          query: {
            billNo: row.billNo,
          },
        });
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },
      getColumns() {
        return [
          {
            prop: 'settleItemNo',
            label: '应收结算单号',
          },
          {
            prop: 'createBillStatusDesc',
            label: '账单状态',
          },
          {
            prop: 'billNo',
            label: '账单单号',
            scopedSlots: { customRender: 'billNo' },
          },
          {
            prop: 'receiveStatusDesc',
            label: '收款状态',
          },
          {
            prop: 'relatedSettleItemNo',
            label: '关联结算单号',
          },
          {
            prop: 'companyName',
            label: '公司名称',
          },
          {
            prop: 'otherSideName',
            label: '对方名称',
          },
          {
            prop: 'bizLineDesc',
            label: '业务线',
          },
          {
            prop: 'corporateMethodDesc',
            label: '合作方式',
          },
          {
            prop: 'chargeTypeDesc',
            label: '收款类型',
          },
          {
            prop: 'receiveMethodDesc',
            label: '收款方式',
          },
          {
            prop: 'currency',
            label: '记账币种',
          },
          {
            prop: 'directionDesc',
            label: '收入类型',
          },
          {
            prop: 'receivableTaxInclusive',
            label: '应收(含税)',
            // render: ({ receivableTaxInclusive }) => (
            //   <span>¥{receivableTaxInclusive}</span>
            // ),
          },
          {
            prop: 'receivableTaxExcluded',
            label: '应收(未税)',
            // render: ({ receivableTaxExcluded }) => (
            //   <span>¥{receivableTaxExcluded}</span>
            // ),
          },
          {
            prop: 'receivableTaxValue',
            label: '应收(税额)',
            // render: ({ receivableTaxValue }) => (
            //   <span>¥{receivableTaxValue}</span>
            // ),
          },
          {
            prop: 'unReceiveTaxInclusive',
            label: '待收(含税)',
            // render: ({ unreceiveTaxInclusive }) => (
            //   <span>¥{unreceiveTaxInclusive}</span>
            // ),
          },
          {
            prop: 'receiveTaxInclusive',
            label: '实收(含税)',
            // render: ({ receiveTaxInclusive }) => (
            //   <span>¥{receiveTaxInclusive}</span>
            // ),
          },
          {
            prop: 'settleDate',
            label: '账期',
          },
          {
            prop: 'createTime',
            label: '创建时间',
          },
          {
            prop: 'receiveTime',
            label: '收款完成时间',
          },
          {
            prop: 'itemTypeDesc',
            label: '应收事项类型',
          },
          {
            prop: 'statusDesc',
            label: '结算单状态',
          },
          {
            prop: 'rejectReason',
            label: '拒绝原因',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '80',
            scopedSlots: { customRender: 'operation' },
          },
        ];
      },
    },
  };
</script>

<style lang="scss"></style>
