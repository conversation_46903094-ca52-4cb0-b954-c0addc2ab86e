<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-10 14:02:37
 * @LastEditTime: 2023-01-16 16:52:03
 * @LastEditors: dddd
 * @Reference: 
-->

<template>
  <div>
    <el-form inline>
      <el-form-item label="账单日期:">
        <el-date-picker
          v-model="applyDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :clearable="false"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="贸易主体:">
        <el-select
          v-model="searchParams.salesEntityCode"
          clearable
          filterable
          placeholder="请选择公司名称"
        >
          <el-option
            v-for="item in subjectDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="收款类型:">
        <el-select
          v-model="searchParams.dataType"
          clearable
          placeholder="请选择收款类型"
        >
          <el-option label="账扣" :value="1"></el-option>
          <el-option label="现金" :value="2"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <!-- <el-button type="primary" @click="down_excel">导出</el-button> -->
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="list" style="width: 100%">
      <el-table-column prop="id" label="序号" type="index"></el-table-column>
      <el-table-column prop="month" label="月度"></el-table-column>
      <el-table-column
        prop="salesEntityName"
        label="贸易主体"
      ></el-table-column>

      <el-table-column prop="beginBalance" label="期初余额"></el-table-column>
      <el-table-column label="已支付（增加）" align="center">
        <el-table-column prop="payAmount" label="当期支付">
          <template slot-scope="{ row }">
            <el-link type="primary" @click="jump(row)">
              {{ row.payAmount }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="refundAmount" label="当期退款">
          <template slot-scope="{ row }">
            <el-link type="primary" @click="jump(row)">
              {{ row.refundAmount }}
            </el-link>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="已签收（减少）" align="center">
        <el-table-column prop="paySignedAmount" label="当期签收">
          <template slot-scope="{ row }">
            <el-link type="primary" @click="jump(row)">
              {{ row.paySignedAmount }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="refundSignedAmount" label="当期退货">
          <template slot-scope="{ row }">
            <el-link type="primary" @click="jump(row)">
              {{ row.refundSignedAmount }}
            </el-link>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column prop="endBalance" label="计算期末余额"></el-table-column>
      <el-table-column
        prop="unsignedBalance"
        label="取值期末余额"
      ></el-table-column>
      <el-table-column
        prop="compareResult"
        label="期末余额状态"
      ></el-table-column>
    </el-table>
    <el-pagination
      ref="listPage"
      background
      :current-page.sync="pagination.pageSize"
      :page-size.sync="pagination.pageLimit"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      @current-change="pagination_currentChange"
      @size-change="pagination_sizeChange"
    ></el-pagination>
  </div>
</template>
<script>
  import { parseTime, setInitData } from '@/utils';
  import { getListSelector } from '@/api/copingManagement';
  import { advanceIncomeMonthCompareList } from '@/api/receivableManagement';
  import dayjs from 'dayjs';

  export default {
    name: 'AdvanceIncomeConsistent',
    data() {
      return {
        applyDate: '',
        searchParams: {
          startDate: '',
          endDate: '',
          salesEntityCode: '',
          // dataType: '',
        },
        list: [],
        subjectDict: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        loading: false,
      };
    },
    created() {
      // const endTime = dayjs();
      // console.log(dayjs().format('YYYY-MM-01'), 'endTime');
      // this.applyDate = setInitData(30);
      this.initDate();
      getListSelector({}).then(res => {
        if (res) {
          this.subjectDict = res.subjectDict;
        }
      });
      this.getList(true);
    },
    methods: {
      pagination_currentChange(val) {
        this.pagination.pageSize = val;
        this.getList();
      },

      pagination_sizeChange(val) {
        this.pagination.pageLimit = e;
        this.getList();
      },
      initDate() {
        const startTime = dayjs().format('YYYY-MM-01');
        const endTime = dayjs().format('YYYY-MM-DD');
        this.applyDate = [startTime, endTime];
      },
      down_excel() {
        this.$message.success('导出成功');
      },
      jump(row) {
        this.$router.push({
          path: 'advanceIncomeMgt',
          query: {
            activeName: '2',
            month: row.month,
            salesEntityCode: row.salesEntityCode,
            merchantSubjectCode: row.merchantSubjectCode,
          },
        });
      },
      getParams() {
        const { applyDate } = this;
        this.searchParams.startDate = applyDate
          ? parseTime(applyDate[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.endDate = applyDate
          ? parseTime(applyDate[1], '{y}-{m}-{d}')
          : '';
        const params = {
          ...this.searchParams,
          page: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.loading = true;
        const res = await advanceIncomeMonthCompareList(params);
        this.loading = false;

        if (res) {
          this.list = res ? res.list : [];
          this.pagination.total = res.total;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.initDate();
      },
    },
  };
</script>
<style lang="scss"></style>
