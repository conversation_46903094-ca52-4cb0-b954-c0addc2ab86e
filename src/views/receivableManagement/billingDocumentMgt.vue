<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-07 09:51:23
 * @LastEditTime: 2022-11-18 16:54:19
 * @LastEditors: dddd
 * @Reference: 
-->

<template>
  <div>
    <el-form inline>
      <el-form-item label="计费单号:">
        <el-input
          v-model="searchParams.chargeItemNo"
          clearable
          placeholder="请输入计费单号"
        ></el-input>
      </el-form-item>
      <el-form-item label="关联计费单号:">
        <el-input
          v-model="searchParams.relatedChargeItemNo"
          placeholder="请输入关联计费单号"
          clearable
        />
      </el-form-item>
      <el-form-item label="关联合同号:">
        <el-input
          v-model="searchParams.relatedContractNo"
          clearable
          placeholder="请输入关联合同号"
        ></el-input>
      </el-form-item>
      <el-form-item label="应收结算单号:">
        <el-input
          v-model="searchParams.settleItemNo"
          clearable
          placeholder="请输入应收结算单号"
        ></el-input>
      </el-form-item>

      <el-form-item label="公司名称:">
        <el-select
          v-model="searchParams.companyCode"
          clearable
          filterable
          placeholder="请选择公司名称"
        >
          <el-option
            v-for="item in subjectDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="对方名称:">
        <el-select
          v-model="searchParams.otherSideCode"
          clearable
          filterable
          placeholder="请选择对方名称"
        >
          <el-option
            v-for="item in supplyDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="单据类型:">
        <el-select
          v-model="searchParams.itemType"
          clearable
          placeholder="请选择单据类型"
        >
          <el-option
            v-for="item in itemTypeList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="收款方式:">
        <el-select
          v-model="searchParams.receiveMethod"
          clearable
          placeholder="请选择收款方式"
        >
          <el-option
            v-for="item in receiveMethodList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="收款状态:">
        <el-select
          v-model="searchParams.receiveStatus"
          clearable
          placeholder="请选择收款状态"
        >
          <el-option
            v-for="item in receiveStatusList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="结算周期:">
        <el-date-picker
          v-model="applyDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="完成日期:">
        <el-date-picker
          v-model="finishDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="费用类型:">
        <el-select
          v-model="searchParams.chargeType"
          clearable
          placeholder="请选择费用类型"
        >
          <el-option
            v-for="item in chargeTypeList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <!-- <el-button type="primary" @click="getList(true)">查询</el-button> -->
        <ac-permission-button
          btn-text="查询"
          permission-key="billingDocumentMgt-search"
          @click="getList(true)"
        ></ac-permission-button>
        <el-button type="primary" @click="onReset">重置</el-button>

        <ac-permission-button
          btn-text="导出"
          permission-key="billingDocumentMgt-export"
          @click="onExport"
        ></ac-permission-button>
        <ac-permission-button
          btn-text="导入"
          permission-key="billingDocumentMgt-import"
          @click="showModal = true"
        ></ac-permission-button>
        <el-button type="primary" @click="showLogModal = true">
          导入日志
        </el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="settleItemNo" slot-scope="scope">
        <div
          style="color: blue; cursor: pointer"
          @click="toSettleItem(scope.row)"
        >
          {{ scope.row.settleItemNo }}
        </div>
      </template>
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          btn-text="明细"
          type="text"
          size="small"
          permission-key=""
          @click="handelJump(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <!--isServeData false 取前端写死数据，true取后端数据  -->
    <importModal
      v-model="showModal"
      :is-serve-data="false"
      :options="importOptions"
      @onSuccess="onSuccess"
      @onDownload="onDownload"
    ></importModal>
    <confirmTable
      ref="confirmTable"
      v-model="showConfirmTable"
      :list="confirmObj.confirmImportOrderDTO"
      @onOK="onOK"
    ></confirmTable>
    <importLog v-model="showLogModal"></importLog>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import importModal from '../copingManagement/coping/components/importModal.vue';
  import importLog from './components/importLog.vue';
  import { newExportExcel } from '@/api/blob';
  import { downloadFile, parseTime } from '@/utils';
  import { getListSelector } from '@/api/copingManagement';
  import {
    chargeItemList,
    financePayableConfirmImportFile,
    financePayableConfirmSupplementImportFile,
    financePayableImportAdjustOrder,
    financePayableImportFile,
    financePayableImportSKUAdjustOrder,
    financePayableSupplementImportFile,
    importOrderdownloadExcelTemplate,
    settleItemGetSelector,
  } from '@/api/receivableManagement';
  import confirmTable from './components/confirmTable';

  export default {
    name: 'BillingDocumentMgt',
    components: {
      dynamictable,
      importModal,
      confirmTable,
      importLog,
    },
    beforeRouteEnter(to, from, next) {
      if (to.matched && to.matched.length > 2)
        to.matched.splice(1, to.matched.length - 2);
      next();
    },
    data() {
      let columns = [
        {
          prop: 'chargeItemNo',
          label: '计费单号',
        },
        {
          prop: 'settleItemNo',
          label: '应收结算单号',
          scopedSlots: { customRender: 'settleItemNo' },
        },
        {
          prop: 'settleStatusDesc',
          label: '结算状态',
        },
        {
          prop: 'receiveStatusDesc',
          label: '收款状态',
        },
        {
          prop: 'receiveTime',
          label: '收款完成时间',
        },
        {
          prop: 'relatedChargeItemNo',
          label: '关联计费单号',
        },
        {
          prop: 'relatedContractNo',
          label: '关联合同号',
        },
        {
          prop: 'companyName',
          label: '公司名称',
        },
        {
          prop: 'otherSideName',
          label: '对方名称',
        },
        {
          prop: 'bizLineDesc',
          label: '业务类型',
        },
        {
          prop: 'performanceMethod',
          label: '履约方式',
          render: ({ performanceMethod }) => (
            <span>
              {performanceMethod == '1'
                ? '自履约'
                : performanceMethod == '2'
                ? '非自履约'
                : ''}
            </span>
          ),
        },
        {
          prop: 'corporateMethodDesc',
          label: '合作方式',
        },
        {
          prop: 'chargeTypeDesc',
          label: '费用类型',
        },
        {
          prop: 'rebateMethodDesc',
          label: '返利方式',
        },
        {
          prop: 'receiveMethodDesc',
          label: '收款方式',
        },

        {
          prop: 'directionDesc',
          label: '收入类型',
        },
        {
          prop: 'currency',
          label: '记账币种',
        },
        {
          prop: 'receivableTaxInclusive',
          label: '应收(含税)',
        },
        {
          prop: 'receivableTaxExcluded',
          label: '应收(未税)',
        },
        {
          prop: 'receivableTaxValue',
          label: '应收(税额)',
        },

        {
          prop: 'rate',
          label: '费用税率',
        },
        {
          prop: 'itemTypeDesc',
          label: '单据类型',
        },
        {
          prop: 'sourceTypeDesc',
          label: '单据来源',
        },
        {
          prop: 'remark',
          label: '备注',
        },
        {
          prop: 'createTime',
          label: '创建时间',
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          scopedSlots: { customRender: 'operation' },
        },
      ];

      return {
        showLogModal: false,
        showConfirmTable: false,
        showModal: false,
        confirmObj: {
          fileId: '',
          confirmImportOrderDTO: [],
        },
        importOptions: {
          title: '计费单据管理',
          selectList: [],
          selectWebDataList: [
            { key: '12', value: '应收计费单导入模板' },
            { key: '20', value: '应收计费单导入模板（补录）' },
            { key: '17', value: '应收费用调整模板' },
            { key: '18', value: '应收返利调整模板（sku维度）' },
          ],
        },
        applyDate: '',
        finishDate: '',
        searchParams: {
          chargeItemNo: '',
          chargeType: '',
          itemType: '',
          companyCode: '',
          endChargeDate: '',
          endReceiveTime: '',
          otherSideCode: '',
          receiveMethod: '',
          receiveStatus: '',
          settleItemNo: '',
          startChargeDate: '',
          startReceiveTime: '',
          relatedContractNo: '',
          relatedChargeItemNo: '',
        },
        list: [],
        supplyDict: [],
        subjectDict: [],
        receiveMethodList: [],
        receiveStatusList: [],
        chargeTypeList: [],
        itemTypeList: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
        columns,
      };
    },
    created() {
      const { chargeItemNo, settleItemNo } = this.$route.query;
      if (chargeItemNo || settleItemNo) {
        this.searchParams.chargeItemNo = chargeItemNo;
        this.searchParams.settleItemNo = settleItemNo;
      } else {
        this.applyDate = this.setInitDate();
      }
      getListSelector({}).then(res => {
        if (res) {
          this.supplyDict = res.supplyDict;
          this.subjectDict = res.subjectDict;
        }
      });
      settleItemGetSelector().then(res => {
        if (res) {
          this.receiveMethodList = res.receiveMethodList;
          this.receiveStatusList = res.receiveStatusList;
          this.chargeTypeList = res.chargeTypeList;
          this.itemTypeList = res.itemTypeList;
          if (Array.isArray(res.importTypeList)) {
            this.importOptions.selectList = res.importTypeList.map(item => ({
              key: item.dictValue,
              value: item.dictDesc,
            }));
            this.importOptions.type = res.importTypeList[0]['dictValue'];
          }
        }
      });
      this.getList(true);
    },
    methods: {
      async onOK() {
        const { fileId, confirmImportOrderDTO, type } = this.confirmObj;
        const params = {
          fileId,
          importType: type,
          confirmImportOrderDTO: confirmImportOrderDTO,
        };
        const confirmImportFile =
          type == 12
            ? financePayableConfirmImportFile
            : financePayableConfirmSupplementImportFile;
        try {
          this.$refs['confirmTable'].btnLoading(true);
          const res = await confirmImportFile(params);
          this.$message({
            message: '确认成功',
            type: 'success',
          });
          this.$refs['confirmTable'].btnLoading(false);
          this.showConfirmTable = false;

          this.getList();
        } catch (error) {
          this.$refs['confirmTable'].btnLoading(false);
        }
      },
      async onSuccess(e, done, err, val) {
        try {
          const obj = {
            17: financePayableImportAdjustOrder,
            18: financePayableImportSKUAdjustOrder,
            12: financePayableImportFile,
            20: financePayableSupplementImportFile,
          };
          const importUpload = obj[val.chargeType];
          const params = { fileId: e.id, importType: val.chargeType };
          const res = await importUpload(params);
          console.log(
            res,
            val,
            ['12', '20'].includes(val.chargeType),
            res && Array.isArray(res),
            'valvalval',
          );
          if (['12', '20'].includes(val.chargeType)) {
            if (res && Array.isArray(res)) {
              this.confirmObj = {
                type: val.chargeType,
                fileId: e.id,
                confirmImportOrderDTO: res,
              };

              this.showModal = false;
              setTimeout(() => {
                this.showConfirmTable = true;
              }, 600);
              err();
            }
          } else if (val.chargeType == '18') {
            this.$notify({
              title: '提示',
              message: '稍后请在导入日志查看导入结果',
              duration: 0,
              type: 'success',
            });
            this.showModal = false;
            err();
            this.getList();
          } else {
            done();
            this.getList();
          }
        } catch (error) {
          console.log(error, 'error');
          err();
        }
      },
      async onDownload(val) {
        const obj = {
          17: 'adjust.fees.excel.model',
          18: 'adjust.goods.excel.model',
          12: 'import.fees.excel.model',
          20: 'supplement.import.fees.excel.model',
        };
        console.log(val, 'val');
        const res = await importOrderdownloadExcelTemplate();
        const url = res[obj[Number(val.key)]];
        window.location.href = url;

        // newExportExcel(
        //   { importType: val.key },
        //   '/api/finance-receivable/chargeItem/downloadExcelTemplate',
        //   'get',
        // ).then(res => {
        //   downloadFile(res.data, `导入${val.value}模版`);
        // });
      },
      handelJump(row) {
        const { chargeItemNo } = row;
        this.$router.push({
          path: 'billingDetailsMgt',
          query: {
            chargeItemNo,
          },
        });
      },
      onExport() {
        const params = this.getParams();
        this.$confirm('确定将该数据导出?', '导出', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          newExportExcel(
            params,
            '/api/finance-receivable/chargeItem/exportList',
            'post',
          ).then(res => {
            downloadFile(res.data, '计费单据列表');
          });
        });
      },
      getParams() {
        const { applyDate, finishDate } = this;
        this.searchParams.startChargeDate = applyDate
          ? parseTime(applyDate[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.endChargeDate = applyDate
          ? parseTime(applyDate[1], '{y}-{m}-{d}')
          : '';
        this.searchParams.startReceiveTime = finishDate
          ? parseTime(finishDate[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.endReceiveTime = finishDate
          ? parseTime(finishDate[1], '{y}-{m}-{d}')
          : '';
        const params = {
          ...this.searchParams,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await chargeItemList(params);
        this.options.loading = false;

        if (res) {
          this.list = res ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.applyDate = this.setInitDate();
      },
      toSettleItem(row) {
        this.$router.push({
          path: './accountsReceivableSettlement',
          query: {
            settleItemNo: row.settleItemNo,
          },
        });
      },
      setInitDate(cFormat) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
        end.setTime(end.getTime() + 3600 * 1000 * 24 * 30);
        return [
          parseTime(start, cFormat || '{y}-{m}-{d} 00:00:00'),
          parseTime(end, cFormat || '{y}-{m}-{d} 23:59:59'),
        ];
      },
    },
  };
</script>
<style lang="scss"></style>
