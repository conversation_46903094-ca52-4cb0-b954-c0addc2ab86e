<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-23 18:04:31
 * @LastEditTime: 2023-01-12 15:59:03
 * @LastEditors: dddd
 * @Reference: 
-->

<template>
  <div>
    <el-form inline>
      <el-form-item label="单据编号:">
        <el-input v-model="searchParams.orderSn" placeholder="请输入单据编号" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button type="warning" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>

    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import { advanceIncomeItemList } from '@/api/receivableManagement';
  import { parseTime } from '@/utils';
  export default {
    components: {
      dynamictable,
    },
    data() {
      return {
        searchParams: {
          dataType: '',
          orderSn: '',
          // billDate: '',
          companyId: '',
          salesEntityCode: '',
          userLevel: '',
          warehouseName: '',
          countryCode: '',
          skuId: '',
        },
        list: [],

        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          index: true,
        },
        orderType: [
          {
            key: 0,
            value: '正向',
          },
          {
            key: 1,
            value: '逆向',
          },
        ],
      };
    },
    created() {
      this.initSearch();
      this.getList(true);
    },
    methods: {
      initSearch() {
        const {
          dataType,
          billDate,
          companyId = '',
          salesEntityCode = '',
          docType = '',
          channelId = '',
          userLevel = '',
          warehouseName = '',
          countryCode = '',
          skuId = '',
          activeName,
        } = this.$route.query;
        this.searchParams.dataType = dataType;
        if (activeName === '1' || dataType === '1') {
          this.searchParams.billDateDay = billDate;
        } else {
          this.searchParams.billDateMonth = parseTime(billDate, '{y}-{m}-01');
        }
        // this.searchParams.billDate = billDate;
        this.searchParams.salesEntityCode = salesEntityCode;
        this.searchParams.companyId = companyId;
        this.searchParams.docType = docType;
        this.searchParams.channelId = channelId;
        this.searchParams.userLevel = userLevel;
        this.searchParams.warehouseName = warehouseName;
        this.searchParams.countryCode = countryCode;
        this.searchParams.skuId = skuId;
      },
      getParams() {
        // const { dataType, salesEntityCode, companyId } = this.$route.query;
        const params = {
          ...this.searchParams,
          page: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();

        this.options.loading = true;
        try {
          const res = await advanceIncomeItemList(params);
          if (res) {
            this.list = res ? res.list : [];
            this.pagination.total = res ? res.total : 0;
          }
          this.options.loading = false;
        } catch (error) {
          this.options.loading = false;
        }
      },
      onReset() {
        // Object.assign(
        //   this.$data.searchParams,
        //   this.$options.data().searchParams,
        // );
        this.searchParams.orderSn = '';
      },

      getColumns() {
        const columns1 = [
          // {
          //   prop: 'id',
          //   label: '序号',
          // },
          {
            prop: 'orderSn',
            label: '单据编号',
          },
          {
            prop: 'salesEntityName',
            label: '贸易主体',
          },
          {
            prop: 'companyName',
            label: '收款主体',
          },
          {
            prop: 'countryName',
            label: '收货国家',
          },
          {
            prop: 'channelName',
            label: '支付渠道',
          },
          {
            prop: 'docTypeName',
            label: '交易类型',
          },
          {
            prop: 'orderAmount',
            label: '订单金额（含税）',
          },
          {
            prop: 'discountedGoodsAmount',
            label: '商品金额（含税）',
          },
          {
            prop: 'quantity',
            label: '商品数量',
          },
          {
            prop: 'discountedShippingFeeAmount',
            label: '运费（含税）',
          },
          {
            prop: 'allFeeAmount',
            label: '税费（含税）',
          },

          {
            prop: 'paidAmount',
            label: '实际支付金额',
            render: ({ orderAmount = 0, chargeAmount = 0 }) => (
              <span>{orderAmount + chargeAmount}</span>
            ),
          },

          {
            prop: 'chargeAmount',
            label: '手续费金额',
          },
          {
            prop: 'createTime',
            label: '创建时间',
            render: ({ createTime }) => (
              <span>{createTime ? parseTime(createTime) : ''}</span>
            ),
          },
          {
            prop: 'paidTime',
            label: '支付时间',
            render: ({ paidTime }) => (
              <span>{paidTime ? parseTime(paidTime) : ''}</span>
            ),
          },
        ];
        const columns2 = [
          // {
          //   prop: 'id',
          //   label: '序号',
          // },
          {
            prop: 'orderSn',
            label: '单据编号',
          },
          {
            prop: 'originSalesNo',
            label: '关联原单号',
          },
          {
            prop: 'aftTypeDesc',
            label: '售后状态',
          },
          {
            prop: 'growMoneyAmt',
            label: '教育奖金',
          },
          {
            prop: 'saleRevenueAmt',
            label: '批发收益',
          },
          {
            prop: 'pushOrderSn',
            label: '推单号',
          },
          {
            prop: 'salesEntityName',
            label: '贸易主体',
          },
          {
            prop: 'companyName',
            label: '收款主体',
          },
          {
            prop: 'warehouseName',
            label: '仓库名称',
          },
          {
            prop: 'countryName',
            label: '收货国家',
          },
          {
            prop: 'userLevelName',
            label: '用户等级',
          },
          {
            prop: 'channelName',
            label: '支付渠道',
          },
          {
            prop: 'docTypeName',
            label: '交易类型',
          },
          {
            prop: 'orderAmount',
            label: '订单金额（含税）',
          },
          //   {
          //     prop: 'paidGoodsAmount',
          //     label: '订单金额（未税）',
          //   },
          {
            prop: 'brandName',
            label: '品牌',
          },

          {
            prop: 'barcode',
            label: '商品编码',
          },

          {
            prop: 'skuId',
            label: 'SKUID',
          },
          {
            prop: 'goodsName',
            label: '商品名称',
          },
          {
            prop: 'paidTotalAmount',
            label: '商品金额（含税）',
          },
          {
            prop: 'quantity',
            label: '商品数量',
          },
          {
            prop: 'paidShippingAmount',
            label: '运费（含税）',
          },
          {
            prop: 'paidAllFeeAmount',
            label: '税费（含税）',
          },

          {
            prop: 'orderStatusDesc',
            label: '订单状态',
          },
          {
            prop: 'refundTypeDesc',
            label: '退款类型',
          },

          {
            prop: 'createTime',
            label: '创建时间',
            render: ({ createTime }) => (
              <span>{createTime ? parseTime(createTime) : ''}</span>
            ),
          },
          {
            prop: 'paidTime',
            label: '支付时间',
            render: ({ paidTime }) => (
              <span>{paidTime ? parseTime(paidTime) : ''}</span>
            ),
          },
          {
            prop: 'shippingTime',
            label: '发货时间',
            render: ({ shippingTime }) => (
              <span>{shippingTime ? parseTime(shippingTime) : ''}</span>
            ),
          },
        ];
        const columns3 = [
          // {
          //   prop: 'id',
          //   label: '序号',
          // },
          {
            prop: 'orderSn',
            label: '单据编号',
          },
          {
            prop: 'originSalesNo',
            label: '关联原单号',
          },
          {
            prop: 'aftTypeDesc',
            label: '售后状态',
          },
          {
            prop: 'growMoneyAmt',
            label: '教育奖金',
          },
          {
            prop: 'saleRevenueAmt',
            label: '批发收益',
          },
          {
            prop: 'pushOrderSn',
            label: '推单号',
          },

          {
            prop: 'salesEntityName',
            label: '贸易主体',
          },
          {
            prop: 'companyName',
            label: '收款主体',
          },
          {
            prop: 'warehouseName',
            label: '仓库名称',
          },
          {
            prop: 'countryName',
            label: '收货国家',
          },
          {
            prop: 'userLevelName',
            label: '用户等级',
          },
          {
            prop: 'channelName',
            label: '支付渠道',
          },
          {
            prop: 'docTypeName',
            label: '交易类型',
          },
          {
            prop: 'orderAmount',
            label: '订单金额（含税）',
          },
          {
            prop: 'brandName',
            label: '品牌',
          },

          {
            prop: 'barcode',
            label: '商品编码',
          },
          {
            prop: 'skuId',
            label: 'SKUID',
          },
          {
            prop: 'goodsName',
            label: '商品名称',
          },
          {
            prop: 'paidTotalAmount',
            label: '商品金额（含税）',
          },
          {
            prop: 'paidTotalAmountNotTax',
            label: '商品金额（不含税）',
          },
          {
            prop: 'salesTaxRate',
            label: '税率',
          },
          {
            prop: 'paidTotalAmountTax',
            label: '税额',
          },
          {
            prop: 'quantity',
            label: '商品数量',
          },
          {
            prop: 'paidShippingAmount',
            label: '运费（含税）',
          },
          {
            prop: 'paidAllFeeAmount',
            label: '税费（含税）',
          },

          {
            prop: 'orderStatusDesc',
            label: '订单状态',
          },
          {
            prop: 'refundTypeDesc',
            label: '退款类型',
          },

          {
            prop: 'createTime',
            label: '创建时间',
            render: ({ createTime }) => (
              <span>{createTime ? parseTime(createTime) : ''}</span>
            ),
          },
          {
            prop: 'paidTime',
            label: '支付时间',
            render: ({ paidTime }) => (
              <span>{paidTime ? parseTime(paidTime) : ''}</span>
            ),
          },
          {
            prop: 'shippingTime',
            label: '发货时间',
            render: ({ shippingTime }) => (
              <span>{shippingTime ? parseTime(shippingTime) : ''}</span>
            ),
          },
          {
            prop: 'signedTime',
            label: '签收时间',
            render: ({ signedTime }) => (
              <span>{signedTime ? parseTime(signedTime) : ''}</span>
            ),
          },
          {
            prop: 'completedTime',
            label: '完成时间',
            render: ({ completedTime }) => (
              <span>{completedTime ? parseTime(completedTime) : ''}</span>
            ),
          },
          {
            prop: 'canceledTime',
            label: '取消时间',
            render: ({ canceledTime }) => (
              <span>{canceledTime ? parseTime(canceledTime) : ''}</span>
            ),
          },
        ];
        const { dataType } = this.$route.query;
        return [columns1, columns2, columns3][dataType - 1];
      },
    },
  };
</script>
<style lang="scss"></style>
