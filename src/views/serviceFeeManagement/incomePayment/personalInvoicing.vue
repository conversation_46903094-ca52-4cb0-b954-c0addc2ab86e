<!--
 * @Author: 七七
 * @Date: 2021-12-07 11:17:43
 * @LastEditors: xuxiang
 * @LastEditTime: 2022-05-19 17:08:12
 * @FilePath: /access-fmis-web/src/views/serviceFeeManagement/incomePayment/personalInvoicing.vue
-->
<template>
  <div>
    <el-form :model="search" :inline="true" class="demo-form-inline">
      <el-form-item label="申请时间：">
        <el-date-picker
          v-model="search.date"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="代理ID：">
        <el-input
          v-model="search.id"
          placeholder="请输入代理ID"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="手机号码：">
        <el-input
          v-model="search.mobile"
          placeholder="请输入手机号码"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="企业/个体名称：">
        <el-input
          v-model="search.name"
          placeholder="请输入企业/个体名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="认证类型：">
        <el-select v-model="search.certificationType" clearable>
          <el-option
            v-for="(item, index) in authTypeList"
            :key="index"
            :label="item.desc"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态：">
        <el-select v-model="search.status" clearable>
          <el-option
            v-for="item in statusList"
            :key="item.code"
            :label="item.desc"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发票类型：">
        <el-select v-model="search.invoiceType" clearable>
          <el-option
            v-for="item in invoiceTypeList"
            :key="item.code"
            :label="item.desc"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <ac-permission-button
          btn-text="查询"
          permission-key="personalInvoicing-search"
          @click="handleSearch"
        ></ac-permission-button>
        <!-- <el-button type="primary" @click="handleSearch">查询</el-button> -->
        <el-button type="primary" @click="reset">重置</el-button>
        <ac-permission-button
          btn-text="发票下载"
          permission-key="personalInvoicing-download"
          @click="batchDownloadInvoice"
        ></ac-permission-button>
        <ac-permission-button
          btn-text="导出"
          permission-key="personalInvoicing-export"
          @click="onExport"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      border
      :data="tableData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" fixed></el-table-column>
      <el-table-column label="申请单号" prop="applyNo"></el-table-column>
      <el-table-column label="申请时间" prop="applyTime"></el-table-column>
      <el-table-column label="代理ID" prop="idCode"></el-table-column>
      <el-table-column label="代理姓名" prop="userName"></el-table-column>
      <el-table-column label="手机号" prop="mobilePhone"></el-table-column>
      <el-table-column
        label="企业/个体名称"
        prop="organizationName"
        width="120px"
      ></el-table-column>
      <el-table-column
        label="社会信用代码/税号"
        prop="taxNo"
        width="150px"
      ></el-table-column>
      <el-table-column label="申请金额" prop="applyAmount"></el-table-column>
      <el-table-column
        label="发放-境内"
        prop="payInAmount"
        width="100px"
      ></el-table-column>
      <el-table-column
        label="发放-境外"
        prop="payOutAmount"
        width="100px"
      ></el-table-column>
      <el-table-column
        label="发票类型"
        prop="invoiceTypeDesc"
      ></el-table-column>

      <el-table-column label="开户名" prop="name"></el-table-column>
      <el-table-column label="银行卡" prop="bankCardNo"></el-table-column>
      <el-table-column label="开户行" prop="bankName"></el-table-column>

      <el-table-column label="申请时间" prop="applyTime"></el-table-column>
      <el-table-column label="审核状态" prop="statusDesc"></el-table-column>
      <el-table-column label="更新时间" prop="updateTime"></el-table-column>
      <el-table-column label="操作人" prop="approver"></el-table-column>
      <el-table-column label="操作" fixed="right">
        <template #default="{ row }">
          <ac-permission-button
            slot="reference"
            type="text"
            size="small"
            :btn-text="
              row.status === 'applying'
                ? '初审'
                : row.status === 'first_pass'
                ? '复审'
                : '查看'
            "
            :permission-key="
              row.status === 'applying'
                ? 'personalInvoicing-first'
                : row.status === 'first_pass'
                ? 'personalInvoicing-review'
                : ''
            "
            @click="showDetail(row)"
          ></ac-permission-button>

          <ac-permission-button
            slot="reference"
            type="text"
            size="small"
            btn-text="发票下载"
            permission-key="personalInvoicing-download"
            @click="downloadInvoice(row.applyNo)"
          ></ac-permission-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="page.pageNum"
      :page-sizes="[10, 20, 30, 50]"
      :page-size="page.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      style="margin-top: 10px"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    ></el-pagination>
    <examineDialog
      v-model="showDetailDialog"
      :title="dialogTitle"
      :apply-no="applyNo"
      :dialog-type="dialogStatus"
      @submit="loadData"
    />
  </div>
</template>
<script>
  import {
    queryExamineList,
    getExamineOption,
    invoiceDownload,
    batchInvoiceDownload,
    selfMakeOutInvoiceDownload,
  } from '@/api/serviceFeeManagement';
  import { exportExcel } from '@/api/blob';

  import examineDialog from './components/examineDialog.vue';
  import dayjs from 'dayjs';
  import injectHost from '@/utils/injectHost';
  import { replaceLocalDomain } from '@/utils/index.js';
  import { isNumber } from '@/utils/validate';

  const BASE_URL = injectHost().apiHost;

  export default {
    components: { examineDialog },
    data() {
      return {
        loading: false,
        tableData: [],
        search: {
          date: [],
        },
        searchCopy: {},
        statusList: [],
        invoiceTypeList: [],
        mobileList: [],
        authTypeList: [],
        selectData: [],
        showDetailDialog: false,
        applyNo: null, //单号
        dialogStatus: null, //1:初审 2:复审 3:查看 4:打款
        dialogTitle: '',
        // 分页
        page: {
          pageSize: 10,
          pageNum: 1,
        },
        total: 0,
      };
    },
    async mounted() {
      await this.loadOption();
      const endTime = dayjs();
      const startTime = endTime.subtract(1, 'month');
      this.search.date = [
        startTime.format('YYYY-MM-DD 00:00:00'),
        endTime.format('YYYY-MM-DD 23:59:59'),
      ];
      this.handleSearch();
    },
    methods: {
      getParams() {
        const body = {
          current: this.page.pageNum,
          size: this.page.pageSize,
        };
        if (this.searchCopy.date?.length > 0) {
          body.applyStartTime = this.searchCopy.date[0];
          body.applyEndTime = this.searchCopy.date[1];
        }
        if (this.searchCopy.id?.length > 0) {
          body.idCode = this.searchCopy.id;
          if (!isNumber(body.idCode)) {
            this.$message.warning('代理ID只能为数字');
            return false;
          }
        }
        if (this.searchCopy.mobile?.length > 0) {
          body.mobilePhone = this.searchCopy.mobile;
        }
        if (this.searchCopy.name?.length > 0) {
          body.organizationName = this.searchCopy.name;
        }
        if (this.searchCopy.certificationType?.length > 0) {
          body.authType = this.searchCopy.certificationType;
        }
        if (this.searchCopy.invoiceType?.length > 0) {
          body.invoiceType = this.searchCopy.invoiceType;
        }
        if (this.searchCopy.status?.length > 0) {
          body.status = this.searchCopy.status;
        }

        if (!body.mobilePhone && !body.applyStartTime && !body.applyEndTime) {
          this.$message.error('时间和电话号码不能同时为空');
          return;
        }
        return body;
      },
      async onExport() {
        const body = this.getParams();
        if (!body) return;
        const res = await selfMakeOutInvoiceDownload({
          ...body,
          taskContent: '服务费管理-收益打款管理-自开票提现审核-导出',
        });
        this.$message.success('任务正在处理中，请稍后到任务中心查看');
      },
      async loadData() {
        const body = this.getParams();
        if (!body) return;
        this.loading = true;
        try {
          const res = await queryExamineList(body);
          this.tableData = res.records ? res.records : [];
          this.total = res.total;
        } catch (e) {
          this.tableData = [];
          this.total = null;
        }

        this.loading = false;
      },
      handleSearch() {
        this.page.pageNum = 1;
        this.searchCopy = { ...this.search };
        this.loadData();
      },
      reset() {
        this.search = {
          date: [],
        };
        const endTime = dayjs();
        const startTime = endTime.subtract(1, 'month');
        this.search.date = [
          startTime.format('YYYY-MM-DD 00:00:00'),
          endTime.format('YYYY-MM-DD 23:59:59'),
        ];
        // this.handleSearch();
      },
      handleSelectionChange(val) {
        this.selectData = val;
      },
      showDetail(row) {
        // this.applyNo = val;
        // 根据审核状态传值
        // this.dialogStatus =
        if (row.status === 'applying') {
          this.dialogStatus = 1;
          this.dialogTitle = '初审';
        } else if (row.status === 'first_pass') {
          this.dialogStatus = 2;
          this.dialogTitle = '复审';
        } else {
          this.dialogStatus = 3;
          this.dialogTitle = '查看';
        }
        this.applyNo = row.applyNo;
        this.showDetailDialog = true;
      },
      async loadOption() {
        try {
          const res = await getExamineOption();
          this.statusList = res ? res.statusList : [];
          this.invoiceTypeList = res ? res.invoiceTypeList : [];
          this.authTypeList = res ? res.authTypeList : [];
        } catch (e) {}
      },
      batchDownloadInvoice() {
        if (this.selectData?.length > 0) {
          this.batchDownload();
        } else {
          this.$message.error('请选择发票！');
        }
      },
      async batchDownload() {
        try {
          const list = this.selectData.map(({ applyNo }) => applyNo);
          const url = `${window.location.protocol}//${replaceLocalDomain(
            BASE_URL,
          )}/api/financial-account/withdraw/pay/selfMakeOutInvoice/batchDownloadInvoice`;
          exportExcel(
            { applyNoList: list.join(',') },
            '/api/financial-account/withdraw/pay/selfMakeOutInvoice/batchDownloadInvoice',
            'get',
          ).then(res => {
            if (res.data) {
              window.location.href = `${url}?applyNoList=${list.join(
                ',',
              )}&appCode=${process.env.VUE_APP_LOGIN_APP_CODE}`;
            }
          });
        } catch (e) {}
      },
      async downloadInvoice(applyNo) {
        try {
          const url = `${window.location.protocol}//${replaceLocalDomain(
            BASE_URL,
          )}/api/financial-account/withdraw/pay/selfMakeOutInvoice/downloadInvoice`;
          exportExcel(
            { applyNo },
            '/api/financial-account/withdraw/pay/selfMakeOutInvoice/downloadInvoice',
            'get',
          ).then(res => {
            if (res.data) {
              window.location.href = `${url}?applyNo=${applyNo}&appCode=${process.env.VUE_APP_LOGIN_APP_CODE}`;
            }
          });
        } catch (e) {}
      },
      // 分页
      handleCurrentChange(page) {
        this.page.pageNum = page;
        this.loadData();
      },
      handleSizeChange(page) {
        this.page.pageNum = 1;
        this.page.pageSize = page;
        this.loadData();
      },
    },
  };
</script>
<style scoped></style>
