<template>
  <div>
    <div>
      <el-form ref="form" :model="form" label-width="80px" inline>
        <el-form-item label="提现周期:" prop="payCycle">
          <el-date-picker
            v-model="form.payCycle"
            value-format="yyyy-MM"
            type="month"
            placeholder="提现周期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="代理ID:" prop="idCode">
          <el-input v-model="form.idCode" placeholder="请输入代理ID"></el-input>
        </el-form-item>
        <el-form-item label="手机号码:" prop="mobilePhone">
          <el-input
            v-model="form.mobilePhone"
            placeholder="请输入手机号码"
          ></el-input>
        </el-form-item>
        <el-form-item label="合作方式:" prop="authType">
          <el-select v-model="form.authType" clearable>
            <el-option
              v-for="item in option.authTypeList"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发放状态:" prop="status">
          <el-select v-model="form.status" clearable>
            <el-option
              v-for="item in option.statusList"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="乐税开户状态:" prop="status" label-width="120">
          <el-select v-model="form.letaxLogout" clearable>
            <el-option
              v-for="item in option.letaxLogoutList"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="申请单号:" prop="applyNo">
          <el-input
            v-model="form.applyNo"
            placeholder="请输入申请单号"
            style="width: 250px"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div style="float: right; margin-bottom: 15px">
      <!-- <el-button type="primary" @click="download">境外打款下载</el-button>
      <el-upload
        style="display: inline-block; margin: 0 10px"
        :action="action"
        :data="uploadData"
        :headers="headers"
        accept=".xlsx"
        :on-success="onImportSuccess"
        :on-progress="onProgress"
        :on-error="onError"
      >
        <el-button :loading="uploadLoading" size="small" type="primary">
          境外打款回传
        </el-button>
      </el-upload> -->
      <ac-permission-button
        btn-text="查询"
        permission-key="cashWithdrawal-search"
        @click="search(1)"
      ></ac-permission-button>
      <!-- <el-button type="primary" @click="search(1)">查询</el-button> -->
      <el-button type="primary" @click="reset">重置</el-button>
      <ac-permission-button
        btn-text="导出"
        permission-key=""
        @click="onExport"
      ></ac-permission-button>
      <ac-permission-button
        btn-text="全量打款"
        permission-key="cashWithdrawal-full-payment"
        @click="allPayClick"
      ></ac-permission-button>

      <uoloadFile
        style="margin-left: 10px; display: initial"
        permission-key="cashWithdrawal-import"
        accept=".xlsx, .xls"
        btn-text="导入打款结果"
        :upload-data="null"
        upload-url="/api/financial-account/withdraw/pay/uploadPayResultExcel"
        @onSuccess="onSuccess"
      ></uoloadFile>
    </div>
    <div>
      <el-table
        :data="tableData"
        border
        :header-cell-style="{ 'text-align': 'center' }"
        :cell-style="{ 'text-align': 'center' }"
      >
        <el-table-column prop="applyNo" label="申请单号"></el-table-column>
        <el-table-column prop="payCycle" label="所属周期"></el-table-column>
        <el-table-column prop="idCode" label="代理ID"></el-table-column>
        <el-table-column prop="userName" label="代理姓名"></el-table-column>
        <el-table-column prop="mobilePhone" label="手机号"></el-table-column>
        <el-table-column prop="authTypeDesc" label="合作方式"></el-table-column>
        <el-table-column prop="applyAmount" label="申请金额"></el-table-column>
        <el-table-column prop="payAmount" label="应付金额"></el-table-column>
        <el-table-column
          min-width="100"
          prop="payInAmount"
          label="应付-境内"
        ></el-table-column>
        <el-table-column
          min-width="115"
          prop="payInStatusDesc"
          label="发放状态-境内"
        ></el-table-column>
        <el-table-column
          min-width="100"
          prop="payOutAmount"
          label="应付-境外"
        ></el-table-column>
        <el-table-column
          min-width="115"
          prop="payOutStatusDesc"
          label="发放状态-境外"
        ></el-table-column>
        <el-table-column prop="feeRatio" label="手续费率"></el-table-column>
        <el-table-column prop="feeAmount" label="手续费"></el-table-column>

        <el-table-column prop="name" label="开户名"></el-table-column>
        <el-table-column prop="bankCardNo" label="银行卡"></el-table-column>
        <el-table-column prop="bankName" label="开户行"></el-table-column>

        <el-table-column
          min-width="130"
          prop="statusDesc"
          label="发放状态-申请单"
        ></el-table-column>
        <el-table-column
          min-width="110"
          prop="letaxLogoutDesc"
          label="乐税开户状态"
        ></el-table-column>
        <el-table-column prop="remark" label="备注"></el-table-column>
        <el-table-column prop="applyTime" label="申请时间"></el-table-column>
        <el-table-column prop="updateTime" label="更新时间"></el-table-column>
        <el-table-column prop="approver" label="操作人"></el-table-column>
        <el-table-column
          min-width="100"
          max-width="300"
          fixed="right"
          label="操作"
        >
          <template slot-scope="{ row }">
            <el-button type="text" @click="view(row.applyNo)">详情</el-button>
            <ac-permission-button
              v-if="row.canOnlinePay"
              slot="reference"
              type="text"
              size="small"
              btn-text="打款"
              permission-key="cashWithdrawal-make-payment"
              @click="payApply(row.applyNo)"
            ></ac-permission-button>
            <ac-permission-button
              slot="reference"
              type="text"
              size="small"
              btn-text="变更状态"
              permission-key="cashWithdrawal-change-state"
              @click="statusChange(row)"
            ></ac-permission-button>
            <!-- 线下打款按钮先不做 -->
            <!-- <ac-permission-button
              slot="reference"
              type="text"
              size="small"
              btn-text="线下打款"
              permission-key="personalInvoicingPay-change-state"
              @click="statusChange(row)"
            ></ac-permission-button> -->
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page.current"
        :page-sizes="[10, 20, 50]"
        :page-size="page.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <el-dialog title="详情" :visible.sync="dialogVisible" width="60%">
      <div>
        <el-descriptions :column="2">
          <el-descriptions-item label="代理ID">
            {{ detailData.idCode }}
          </el-descriptions-item>
          <el-descriptions-item label="合作方式">
            {{ detailData.authTypeDesc }}
          </el-descriptions-item>
          <el-descriptions-item label="手机号">
            {{ detailData.mobilePhone }}
          </el-descriptions-item>
          <el-descriptions-item label="代理姓名">
            {{ detailData.userName }}
          </el-descriptions-item>
          <el-descriptions-item label="户名">
            {{ detailData.name }}
          </el-descriptions-item>
          <el-descriptions-item
            v-if="detailData.merchantName"
            label="个体工商户名称"
          >
            {{ detailData.merchantName }}
          </el-descriptions-item>
          <el-descriptions-item label="银行卡号">
            {{ detailData.bankCardNo }}
          </el-descriptions-item>
          <el-descriptions-item label="税号">
            {{ detailData.taxNo }}
          </el-descriptions-item>
          <el-descriptions-item label="开户行">
            {{ detailData.bankName }}
          </el-descriptions-item>
          <el-descriptions-item label="审核理由|拒绝理由">
            {{ detailData.remark }}
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">
            {{ detailData.applyTime }}
          </el-descriptions-item>
          <el-descriptions-item label="手续费计算">
            {{ detailData.feeCalculate }}
          </el-descriptions-item>
          <el-descriptions-item label="贸易主体">
            {{ detailData.subject }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <el-dialog
      title="审核确认"
      :visible.sync="payDialogVisiable"
      width="400px"
      @closed="onClose"
    >
      <div>
        <h4>确认打款？</h4>
        <div>
          <el-select
            v-model="applyPhone"
            style="width: 244px"
            placeholder="请选择手机号"
          >
            <el-option
              v-for="item in option.mobilePhoneList"
              :key="item.code"
              :label="item.desc"
              :value="item.code"
            ></el-option>
          </el-select>
          <ac-count-down
            :seconds="180"
            :turn-on="turnOn"
            style="margin-left: 20px; display: inline-block"
            @handelSend="getIdentifyingCode"
          >
            获取验证码
          </ac-count-down>
        </div>
        <div style="margin-top: 20px">
          <el-input
            v-model="identifyingCode"
            style="width: 244px"
            placeholder="请填写验证码"
          ></el-input>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="payDialogVisiable = false">取 消</el-button>
        <el-button
          :loading="loading"
          :disabled="!identifyingCode"
          type="primary"
          @click="confirmApply"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="全量打款确认"
      :visible.sync="allPayVisiable"
      width="500px"
      @closed="onClose"
    >
      <div>
        <h3>确认全量打款？</h3>
        <div>
          本次发放总笔数：{{ allPayData.totalNum }} 本次发放总金额：{{
            allPayData.totalAmount
          }}
        </div>
        <div style="margin: 10px 0">平安主体信息：</div>
        <div v-for="item in allPayData.subjectInfoList" :key="item.subjectName">
          <span>{{ item.subjectName }}:{{ item.subjectAmount }}</span>
          <span>{{ '   ' }}</span>
          <span>
            {{ item.subjectName + '渠道' }}:{{ item.subjectChannelBal }}
          </span>
        </div>
        <div>
          <el-select
            v-model="applyPhone"
            style="width: 244px"
            placeholder="请选择手机号"
          >
            <el-option
              v-for="item in option.mobilePhoneList"
              :key="item.code"
              :label="item.desc"
              :value="item.code"
            ></el-option>
          </el-select>
          <!-- <el-button
            type="primary"
            style="margin-left: 20px"
            @click="getIdentifyingCode"
          >
            获取验证码
            
          </el-button> -->
          <ac-count-down
            style="margin-left: 20px; display: inline-block"
            :seconds="180"
            :turn-on="turnOn"
            @handelSend="getIdentifyingCode"
          >
            获取验证码
          </ac-count-down>
        </div>
        <div style="margin-top: 20px">
          <el-input
            v-model="allPayIdentifyingCode"
            style="width: 244px"
            placeholder="请填写验证码"
          ></el-input>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="allPayVisiable = false">取 消</el-button>
        <el-button
          :loading="allPayLoading"
          type="primary"
          @click="allPayConfirm"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>
    <statusChangeDialog
      v-model="showDialog"
      :current-row="currentRow"
      @onGet="search"
    ></statusChangeDialog>
  </div>
</template>

<script>
  import {
    getPayList,
    getAllPayData,
    allPay,
    getPayDetail,
    pay,
    getPayComboBoxData,
    downloadPayOutExcel,
    sendIdentifyingCode,
    exportPayList,
  } from '@/api/replaceMakeOutInvoice';
  import {
    replaceLocalDomain,
    parseTime,
    getWithdrawalCycle,
  } from '@/utils/index.js';
  import statusChangeDialog from './components/statusChangeDialog.vue';
  import injectHost from '@/utils/injectHost';
  import { getCookie, appCode } from '@/utils/auth';
  import { isNumber } from '@/utils/validate';
  import uoloadFile from '@/components/uoloadFile';
  export default {
    name: 'CashWithdrawal',
    components: { statusChangeDialog, uoloadFile },
    data() {
      return {
        turnOn: false,
        form: {
          payCycle: getWithdrawalCycle(),
          idCode: '',
          mobilePhone: '',
          authType: '',
          applyNo: '',
          letaxLogout: '',
          status: '',
        },
        option: {
          authTypeList: [],
          statusList: [],
          letaxLogoutList: [],
          mobilePhoneList: [],
        },
        tableData: [],
        page: {
          current: 1,
          size: 10,
        },
        total: 0,
        dialogVisible: false,
        detailData: {}, //详情
        currentRow: null, // 当前数据列
        showDialog: false,
        payDialogVisiable: false, //审核确认弹窗
        applyPhone: '', //申请的手机
        applyNo: '', //申请的单号
        identifyingCode: '', //验证码
        loading: false, //确认按钮

        allPayVisiable: false, //全量打款弹窗
        allPayIdentifyingCode: '', //全量打款验证码
        allPayLoading: false, //全量打款loading
        allPayData: {
          subjectInfoList: [],
        }, //全量打款数据

        uploadLoading: false, //上传laoding
        action: '',
        uploadData: {
          appId: 'abmau',
          timeStamp: Date.now(),
        },
        headers: {
          token: getCookie(),
          appCode: appCode(),
          appId: 'abmau',
          userName: '',
          userId: '',
        },
      };
    },
    created() {
      this.fetch();
      this.search();
      let Base64 = require('js-base64').Base64;

      this.action =
        window.location.protocol +
        '//' +
        replaceLocalDomain(injectHost().apiHost) +
        '/api/financial-account/withdraw/pay/replaceMakeOutInvoice/uploadPayOutResultExcel';
      this.headers.userName = Base64.encode(this.$store.state.user.username);
      this.headers.userId = this.$store.state.user.userInfo.id;
    },
    methods: {
      async onExport() {
        const data = this.getParams();
        if (!data) return;
        const res = await exportPayList({
          ...data,
          taskContent: '服务费管理-收益打款管理-代开票提现打款-导出',
        });
        this.$message.success('任务正在处理中，请稍后到任务中心查看');
      },
      getParams() {
        const form = this.form;
        const data = {
          ...this.page,
          ...this.form,
        };
        if (!isNumber(data.idCode)) {
          this.$message.warning('代理ID只能为数字');
          return false;
        } else {
          return data;
        }
      },
      async onSuccess(e) {
        if (!e.success) {
          this.$message.error(e.msg || '导入失败');

          return;
        }
        this.$message.success('任务正在处理中，请稍后到任务中心查看');
      },
      statusChange(row) {
        this.showDialog = true;
        this.currentRow = row;
      },
      onClose() {
        this.$nextTick(function () {
          this.turnOn = false;
          this.applyPhone = '';
          this.identifyingCode = '';
          this.allPayIdentifyingCode = '';
        });
      },
      //获取下拉框
      async fetch() {
        const res = await getPayComboBoxData();
        this.option.authTypeList = res?.authTypeList || [];
        this.option.statusList = res?.statusList || [];
        this.option.letaxLogoutList = res?.letaxLogoutList || [];
        this.option.mobilePhoneList = res?.mobilePhoneList || [];
      },
      //查询
      async search(current = 0) {
        if (current) {
          this.page.current = current;
        }
        const data = {
          ...this.page,
          ...this.form,
        };
        if (!isNumber(data.idCode)) {
          this.$message.warning('代理ID只能为数字');
          return false;
        } else {
          const res = await getPayList(data);
          if (res) {
            this.tableData = res.records || [];
            this.total = res.total || 0;
          }
        }
      },
      //重置
      reset() {
        this.$refs['form'].resetFields();
      },
      //全量打款
      async allPayClick() {
        if (!this.form.payCycle) {
          this.$message.warning('请先选择提现周期');
          return;
        }
        this.applyPhone = '';
        const res = await getAllPayData({ payCycle: this.form.payCycle });
        this.allPayData = res;
        this.allPayVisiable = true;
      },
      //详情
      async view(applyNo) {
        const res = await getPayDetail({ applyNo });
        if (res) {
          this.detailData = res || {};
        }
        if (this.detailData) {
          this.dialogVisible = true;
        }
      },
      //点击审核通过
      payApply(applyNo) {
        this.applyPhone = '';
        this.payDialogVisiable = true;
        this.applyNo = applyNo;
      },
      //点击获取验证码
      async getIdentifyingCode() {
        if (!this.applyPhone) {
          this.$message.warning('请先选择手机号');
          return;
        }
        this.turnOn = true;
        const res = await sendIdentifyingCode({ mobilePhone: this.applyPhone });
        if (res === null) {
          this.$message.success('获取验证码成功');
        }
      },
      //确认审核通过
      async confirmApply() {
        if (!this.applyPhone) {
          this.$message.warning('请先选择手机号');
          return;
        }
        // this.loading = true;
        const data = {
          applyNo: this.applyNo,
          mobilePhone: this.applyPhone,
          identifyingCode: this.identifyingCode,
        };
        await pay(data);
        this.$message.success('打款成功');
        this.payDialogVisiable = false;
        this.search();
        // this.loading = false;
      },
      //全量打款通过
      async allPayConfirm() {
        if (!this.applyPhone) {
          this.$message.warning('请先选择手机号');
          return;
        }
        // this.allPayLoading = true;
        const data = {
          payCycle: this.form.payCycle,
          mobilePhone: this.applyPhone,
          identifyingCode: this.allPayIdentifyingCode,
        };
        await allPay(data);
        this.$message.success('任务正在处理中，请稍后到任务中心查看');
        this.allPayVisiable = false;
        this.search();
      },
      //下载
      download() {
        if (!this.form.payCycle) {
          this.$message.warning('请先选择提现周期');
          return;
        }
        downloadPayOutExcel({
          taskContent: '服务费管理-收益打款管理-代开票提现打款-境外打款下载',
          payCycle: this.form.payCycle,
        }).then(_ => {
          this.$message.success('任务正在处理中，请稍后到任务中心查看');
        });
      },
      onProgress() {
        this.uploadLoading = true;
      },
      //回传
      onImportSuccess(response) {
        this.uploadLoading = false;
        if (response.msg) {
          this.$message.error(response.msg);
        } else {
          this.$message.success('任务正在处理中，请稍后到任务中心查看');
        }
      },
      onError() {
        this.uploadLoading = false;
      },
      //分页
      handleCurrentChange(e) {
        this.page.current = e;
        this.search();
      },
      handleSizeChange(e) {
        this.page.size = e;
        this.handleCurrentChange(1);
      },
    },
  };
</script>

<style lang="scss" scoped>
  /deep/ .el-descriptions--small {
    font-size: 14px;
  }
  /deep/ .is_uploading {
    display: none !important;
  }
  /deep/ .el-upload-list {
    display: none !important;
  }
</style>
