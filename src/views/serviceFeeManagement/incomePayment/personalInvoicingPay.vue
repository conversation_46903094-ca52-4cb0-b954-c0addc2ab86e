<!--
 * @Author: 七七
 * @Date: 2021-12-07 11:17:43
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-11-15 09:59:22
 * @FilePath: /access-fmis-web/src/views/serviceFeeManagement/incomePayment/personalInvoicingPay.vue
-->
<template>
  <div>
    <el-form :model="search" :inline="true" class="demo-form-inline">
      <el-form-item label="申请时间：">
        <el-date-picker
          v-model="search.date"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="代理ID：">
        <el-input v-model="search.id" placeholder="请输入代理ID"></el-input>
      </el-form-item>
      <el-form-item label="手机号码：">
        <el-input
          v-model="search.mobile"
          placeholder="请输入手机号码"
        ></el-input>
      </el-form-item>
      <el-form-item label="企业/个体名称：">
        <el-input
          v-model="search.name"
          placeholder="请输入企业/个体名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="认证类型：">
        <el-select v-model="search.certificationType">
          <el-option
            v-for="(item, index) in authTypeList"
            :key="index"
            :label="item.desc"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发放状态：">
        <el-select v-model="search.status">
          <el-option
            v-for="item in statusList"
            :key="item.code"
            :label="item.desc"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发票类型：">
        <el-select v-model="search.invoiceType">
          <el-option
            v-for="item in invoiceTypeList"
            :key="item.code"
            :label="item.desc"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <ac-permission-button
          btn-text="查询"
          permission-key="personalInvoicingPay-search"
          @click="handleSearch"
        ></ac-permission-button>
        <!-- <el-button type="primary" @click="handleSearch">查询</el-button> -->
        <el-button type="primary" @click="reset">重置</el-button>
        <ac-permission-button
          btn-text="导出"
          permission-key=""
          @click="onExport"
        ></ac-permission-button>
        <!-- <el-button type="primary" @click="abroadDownload">
          境外打款下载
        </el-button>
        <el-upload
          style="display: inline-block; margin: 0 10px"
          :action="action"
          :data="uploadData"
          :headers="headers"
          accept=".xlsx"
          :on-success="onImportSuccess"
          :on-progress="onProgress"
          :on-error="onError"
        >
          <el-button :loading="uploadLoading" type="primary">
            境外打款回传
          </el-button>
        </el-upload> -->
        <!-- <el-button type="primary" @click="domesticPay">全量打款</el-button> -->
        <ac-permission-button
          btn-text="全量打款"
          permission-key="personalInvoicingPay-full-payment"
          @click="domesticPay"
        ></ac-permission-button>
        <uoloadFile
          style="margin-left: 10px; display: initial"
          permission-key="personalInvoicingPay-import"
          accept=".xlsx, .xls"
          btn-text="导入打款结果"
          :upload-data="null"
          upload-url="/api/financial-account/withdraw/pay/uploadPayResultExcel"
          @onSuccess="onSuccess"
        ></uoloadFile>
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      border
      :data="tableData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column label="申请单号" prop="applyNo"></el-table-column>
      <el-table-column label="申请时间" prop="applyTime"></el-table-column>
      <el-table-column label="代理ID" prop="idCode"></el-table-column>
      <el-table-column label="代理姓名" prop="userName"></el-table-column>
      <el-table-column label="手机号" prop="mobilePhone"></el-table-column>
      <el-table-column
        label="企业/个体名称"
        prop="organizationName"
        width="120px"
      ></el-table-column>
      <el-table-column
        label="社会信用代码/税号"
        prop="taxNo"
        width="150px"
      ></el-table-column>
      <el-table-column label="申请金额" prop="applyAmount"></el-table-column>
      <el-table-column
        label="发放-境内"
        prop="payInAmount"
        width="100px"
      ></el-table-column>
      <el-table-column
        label="发放状态-境内"
        width="120px"
        prop="payInStatusDesc"
      ></el-table-column>
      <el-table-column
        label="发放-境外"
        prop="payOutAmount"
        width="100px"
      ></el-table-column>
      <el-table-column
        label="发放状态-境外"
        width="120px"
        prop="payOutStatusDesc"
      ></el-table-column>
      <el-table-column
        label="发票类型"
        prop="invoiceTypeDesc"
      ></el-table-column>

      <el-table-column label="开户名" prop="name"></el-table-column>
      <el-table-column label="银行卡" prop="bankCardNo"></el-table-column>
      <el-table-column label="开户行" prop="bankName"></el-table-column>

      <el-table-column
        label="发放状态-申请单"
        width="140px"
        prop="statusDesc"
      ></el-table-column>
      <el-table-column label="更新时间" prop="updateTime"></el-table-column>
      <el-table-column label="申请时间" prop="applyTime"></el-table-column>
      <el-table-column label="操作人" prop="approver"></el-table-column>
      <el-table-column label="操作" fixed="right">
        <template #default="{ row }">
          <el-link
            v-if="!row.canOnlinePay"
            type="primary"
            @click="showDetail(row)"
          >
            查看
          </el-link>
          <ac-permission-button
            v-if="row.canOnlinePay"
            slot="reference"
            type="text"
            size="small"
            btn-text="打款"
            permission-key="personalInvoicingPay-make-payment"
            @click="showDetail(row, true)"
          ></ac-permission-button>
          <ac-permission-button
            slot="reference"
            type="text"
            size="small"
            btn-text="状态变更"
            permission-key="personalInvoicingPay-change-state"
            @click="statusChange(row)"
          ></ac-permission-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="page.pageNum"
      :page-sizes="[10, 20, 30, 50]"
      :page-size="page.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      style="margin-top: 10px"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    ></el-pagination>
    <examineDialog
      v-model="showDetailDialog"
      :apply-no="applyNo"
      :dialog-type="dialogStatus"
      :title="dialogTitle"
      :list="mobileList"
      @submit="loadData"
    />
    <allPayDialog
      v-model="showAllPayDialog"
      :date="search.date"
      :list="mobileList"
      @submit="loadData"
    />
    <statusChangeDialog
      v-model="showDialog"
      :current-row="currentRow"
      @onGet="loadData"
    ></statusChangeDialog>
  </div>
</template>
<script>
  import {
    queryPayList,
    getPayOption,
    payOutDownload,
    downloadPayList,
  } from '@/api/serviceFeeManagement';
  import { replaceLocalDomain } from '@/utils/index.js';
  import injectHost from '@/utils/injectHost';
  import { getCookie, appCode } from '@/utils/auth';
  import examineDialog from './components/examineDialog.vue';
  import allPayDialog from './components/allPayDialog.vue';
  import dayjs from 'dayjs';
  import { isNumber } from '@/utils/validate';
  import statusChangeDialog from './components/statusChangeDialog.vue';
  import uoloadFile from '@/components/uoloadFile';

  export default {
    components: { examineDialog, allPayDialog, statusChangeDialog, uoloadFile },
    data() {
      return {
        showDialog: false,
        currentRow: null,
        loading: false,
        tableData: [],
        search: {
          date: [],
        },
        searchCopy: {},
        statusList: [],
        invoiceTypeList: [],
        mobileList: [],
        authTypeList: [],
        selectData: [],
        showDetailDialog: false,
        showAllPayDialog: false,
        applyNo: null, //单号
        dialogStatus: null, //1:初审 2:复审 3:查看 4:打款
        dialogTitle: '',
        // 分页
        page: {
          pageSize: 10,
          pageNum: 1,
        },
        total: 0,
        uploadLoading: false, //上传laoding
        action: '',
        uploadData: {
          appId: 'abmau',
          timeStamp: Date.now(),
        },
        headers: {
          token: getCookie(),
          appCode: appCode(),
          appId: 'abmau',
          userName: '',
          userId: '',
        },
      };
    },
    created() {
      let Base64 = require('js-base64').Base64;
      this.action =
        window.location.protocol +
        '//' +
        replaceLocalDomain(injectHost().apiHost) +
        '/api/financial-account/withdraw/pay/selfMakeOutInvoice/uploadPayOutResultExcel';
      this.headers.userName = Base64.encode(this.$store.state.user.username);
      this.headers.userId = this.$store.state.user.userInfo.id;
    },
    async mounted() {
      await this.loadOption();
      const endTime = dayjs();
      const startTime = endTime.subtract(1, 'month');
      this.search.date = [
        startTime.format('YYYY-MM-DD 00:00:00'),
        endTime.format('YYYY-MM-DD 23:59:59'),
      ];
      this.handleSearch();
    },
    methods: {
      getParams() {
        const body = {
          current: this.page.pageNum,
          size: this.page.pageSize,
        };
        if (this.searchCopy.date?.length > 0) {
          body.applyStartTime = this.searchCopy.date[0];
          body.applyEndTime = this.searchCopy.date[1];
        }
        if (this.searchCopy.id?.length > 0) {
          body.idCode = this.searchCopy.id;
          if (!isNumber(body.idCode)) {
            this.$message.warning('代理ID只能为数字');
            return false;
          }
        }
        if (this.searchCopy.mobile?.length > 0) {
          body.mobilePhone = this.searchCopy.mobile;
        }
        if (this.searchCopy.name?.length > 0) {
          body.organizationName = this.searchCopy.name;
        }
        if (this.searchCopy.certificationType?.length > 0) {
          body.authType = this.searchCopy.certificationType;
        }
        if (this.searchCopy.invoiceType?.length > 0) {
          body.invoiceType = this.searchCopy.invoiceType;
        }
        if (this.searchCopy.status?.length > 0) {
          body.status = this.searchCopy.status;
        }

        if (!body.mobilePhone && !body.applyStartTime && !body.applyEndTime) {
          this.$message.error('时间和电话号码不能同时为空');
          return;
        }
        return body;
      },
      async onExport() {
        const body = this.getParams();
        if (!body) return;
        const res = await downloadPayList({
          ...body,
          taskContent: '服务费管理-收益打款管理-自开票提现打款-导出',
        });
        this.$message.success('任务正在处理中，请稍后到任务中心查看');
      },

      async onSuccess(e) {
        if (!e.success) {
          this.$message.error(e.msg || '导入失败');

          return;
        }
        this.$message.success('任务正在处理中，请稍后到任务中心查看');
      },
      statusChange(row) {
        this.showDialog = true;
        this.currentRow = row;
      },
      async loadData() {
        const body = {
          current: this.page.pageNum,
          size: this.page.pageSize,
        };
        if (this.searchCopy.date?.length > 0) {
          body.applyStartTime = this.searchCopy.date[0];
          body.applyEndTime = this.searchCopy.date[1];
        }
        if (this.searchCopy.id?.length > 0) {
          body.idCode = this.searchCopy.id;
          if (!isNumber(body.idCode)) {
            this.$message.warning('代理ID只能为数字');
            return false;
          }
        }
        if (this.searchCopy.mobile?.length > 0) {
          body.mobilePhone = this.searchCopy.mobile;
        }
        if (this.searchCopy.name?.length > 0) {
          body.organizationName = this.searchCopy.name;
        }
        if (this.searchCopy.certificationType?.length > 0) {
          body.authType = this.searchCopy.certificationType;
        }
        if (this.searchCopy.invoiceType?.length > 0) {
          body.invoiceType = this.searchCopy.invoiceType;
        }
        if (this.searchCopy.status?.length > 0) {
          body.status = this.searchCopy.status;
        }

        if (!body.mobilePhone && !body.applyStartTime && !body.applyEndTime) {
          this.$message.error('时间和电话号码不能同时为空');
          return;
        }
        this.loading = true;
        try {
          const res = await queryPayList(body);
          this.tableData = res.records ? res.records : [];
          this.total = res.total;
        } catch (e) {}

        this.loading = false;
      },
      handleSearch() {
        this.page.pageNum = 1;
        this.searchCopy = { ...this.search };
        this.loadData();
      },
      reset() {
        this.search = {
          date: [],
        };
        const endTime = dayjs();
        const startTime = endTime.subtract(1, 'month');
        this.search.date = [
          startTime.format('YYYY-MM-DD 00:00:00'),
          endTime.format('YYYY-MM-DD 23:59:59'),
        ];
        // this.handleSearch();
      },
      handleFullAudit() {},
      handleSelectionChange(val) {
        this.selectData = val;
      },
      async loadOption() {
        try {
          const res = await getPayOption();
          this.statusList = res ? res.statusList : [];
          this.invoiceTypeList = res ? res.invoiceTypeList : [];
          this.authTypeList = res ? res.authTypeList : [];
          this.mobileList = res ? res.mobilePhoneList : [];
        } catch (e) {}
      },
      showDetail(row, flag) {
        this.applyNo = row.applyNo;
        // 根据审核状态传值
        if (flag) {
          this.dialogStatus = 4;
          this.dialogTitle = '打款';
        } else {
          this.dialogStatus = 3;
          this.dialogTitle = '查看';
        }
        this.showDetailDialog = true;
      },
      async abroadDownload() {
        if (this.search.date?.length > 0) {
          const body = {
            applyStartTime: this.search.date[0],
            applyEndTime: this.search.date[1],
            taskContent: '服务费管理-收益打款管理-自开票提现打款-境外打款下载',
          };
          if (this.search.id?.length > 0) {
            body.idCode = his.search.idCode;
          }
          if (this.search.mobile?.length > 0) {
            body.mobilePhone = this.search.mobilePhone;
          }
          if (this.search.name?.length > 0) {
            body.organizationName = this.search.name;
          }
          if (this.searchCopy.certificationType?.length > 0) {
            body.authType = this.searchCopy.certificationType;
          }
          if (this.searchCopy.invoiceType?.length > 0) {
            body.invoiceType = this.searchCopy.invoiceType;
          }
          if (this.searchCopy.status?.length > 0) {
            body.status = this.searchCopy.status;
          }
          try {
            const res = await payOutDownload(body);
            if (res) {
              this.$message.success('任务正在处理中，请稍后到任务中心查看');
            }
          } catch (e) {}
        } else {
          this.$message.warning('请先选择申请时间');
          return;
        }
      },
      domesticPay() {
        if (this.search.date?.length > 0) {
          this.showAllPayDialog = true;
        } else {
          this.$message.error('请选择申请时间');
        }
      },
      onProgress() {
        this.uploadLoading = true;
      },
      //回传
      onImportSuccess(response) {
        this.uploadLoading = false;
        if (response.msg) {
          this.$message.error(response.msg);
        } else {
          this.$message.success('任务正在处理中，请稍后到任务中心查看');
        }
      },
      onError() {
        this.uploadLoading = false;
      },
      // 分页
      handleCurrentChange(page) {
        this.page.pageNum = page;
        this.loadData();
      },
      handleSizeChange(page) {
        this.page.pageNum = 1;
        this.page.pageSize = page;
        this.loadData();
      },
    },
  };
</script>
<style lang="scss" scoped>
  /deep/ .el-descriptions--small {
    font-size: 14px;
  }
  /deep/ .is_uploading {
    display: none !important;
  }
  /deep/ .el-upload-list {
    display: none !important;
  }
</style>
