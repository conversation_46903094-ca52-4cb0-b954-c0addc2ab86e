<!--
 * @Author: 七七
 * @Date: 2021-12-07 16:11:39
 * @LastEditors: 七七
 * @LastEditTime: 2022-01-04 14:02:11
 * @FilePath: /access-fmis-web/src/views/serviceFeeManagement/incomePayment/ components/examineDialog.vue
-->
<template>
  <el-dialog
    :visible.sync="showDialog"
    :title="title"
    width="60%"
    destroy-on-close
  >
    <div>
      <el-descriptions :column="2" size="medium">
        <el-descriptions-item label="代理ID">
          {{ info.idCode }}
        </el-descriptions-item>
        <el-descriptions-item label="合作方式">
          {{ info.authTypeDesc }}
        </el-descriptions-item>
        <el-descriptions-item label="手机号">
          {{ info.mobilePhone }}
        </el-descriptions-item>
        <el-descriptions-item label="代理姓名">
          {{ info.userName }}
        </el-descriptions-item>
        <el-descriptions-item label="户名">
          {{ info.name }}
        </el-descriptions-item>
        <el-descriptions-item label="企业/个体名称">
          {{ info.organizationName }}
        </el-descriptions-item>
        <el-descriptions-item label="银行账号">
          {{ info.bankCardNo }}
        </el-descriptions-item>
        <el-descriptions-item label="税号">
          {{ info.taxNo }}
        </el-descriptions-item>
        <el-descriptions-item label="开户行">
          {{ info.bankName }}
        </el-descriptions-item>
        <el-descriptions-item label="贸易主体">
          {{ info.subject }}
        </el-descriptions-item>
        <!-- <el-descriptions-item label="公司名称">
          {{ info.organizationName }}
        </el-descriptions-item> -->
        <el-descriptions-item label="申请时间">
          {{ info.applyTime }}
        </el-descriptions-item>
        <el-descriptions-item label="公司地址">
          {{ info.organizationAddress }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="dialogType === 1 || dialogType === 2"
          label="申请金额"
        >
          {{ `${info.applyAmount} 元` }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="dialogType === 1 || dialogType === 2"
          label="发放-境内"
        >
          {{ `${info.payInAmount} 元` }}
        </el-descriptions-item>

        <el-descriptions-item
          label="营业执照"
          :span="2"
          label-style="width:70px"
        >
          <el-image
            v-if="info.businessLicense"
            :src="info.businessLicense"
            style="width: 100px; height: 100px"
            fit="cover"
            :preview-src-list="[info.businessLicense]"
          />
        </el-descriptions-item>
        <el-descriptions-item label="发票类型">
          {{ info.invoiceTypeDesc }}
        </el-descriptions-item>
        <el-descriptions-item label="审核理由|拒绝理由">
          {{ info.reason }}
        </el-descriptions-item>
        <el-descriptions-item
          label="发票照片"
          :span="2"
          label-style="width:70px;flex-shrink: 0;"
        >
          <div
            style="
              display: flex;
              position: relative;
              flex-direction: row;
              flex-wrap: wrap;
            "
          >
            <div v-for="(item, index) in info.invoiceUrlList" :key="index">
              <el-image
                :src="item"
                class="mr-r-10"
                style="width: 100px; height: 100px"
                fit="cover"
                :preview-src-list="[item]"
              />
            </div>
          </div>
        </el-descriptions-item>

        <el-descriptions-item label="寄回快递单号">
          <div v-if="dialogType !== 2">{{ info.backExpressNo }}</div>
          <div
            v-else-if="backExpressNoDisabled && dialogType === 2"
            @click="backExpressNoDisabled = false"
          >
            (非必填，点击激活输入框)
          </div>
          <el-input v-else v-model="info.backExpressNo"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="快递单号">
          {{ info.expressNo }}
        </el-descriptions-item>
        <el-descriptions-item label="寄回快递公司">
          <div v-if="dialogType !== 2">{{ info.backExpressCompany }}</div>
          <div
            v-else-if="backExpressCompanyDisabled && dialogType === 2"
            @click="backExpressCompanyDisabled = false"
          >
            (非必填，点击激活输入框)
          </div>
          <el-input v-else v-model="info.backExpressCompany"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="快递公司">
          {{ info.expressCompany }}
        </el-descriptions-item>
      </el-descriptions>

      <el-form ref="form" :model="form" :rules="rules">
        <el-form-item v-if="dialogType === 4" prop="mobile">
          <el-select v-model="form.mobile">
            <el-option
              v-for="item in list"
              :key="item.code"
              :label="item.desc"
              :value="item.code"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="dialogType === 4" prop="checkCode">
          <el-input v-model="form.checkCode" style="width: 200px"></el-input>
          <el-button
            class="mr-l-10"
            type="primary"
            :disabled="codeSend"
            @click="getCode"
          >
            获取验证码
            <span v-if="codeSend">({{ count }}s)</span>
          </el-button>
        </el-form-item>
        <el-form-item
          v-if="dialogType === 1 || dialogType === 2"
          label="备注："
          :prop="reject ? 'remark' : null"
        >
          <el-input v-model="form.remark" style="width: 200px"></el-input>
        </el-form-item>
        <el-form-item v-if="dialogType !== 4">
          <el-button
            v-if="dialogType !== 3"
            type="primary"
            @click="handleSubmit(true)"
          >
            通过
          </el-button>
          <el-button
            v-if="dialogType !== 3"
            type="primary"
            class="mr-l-10"
            @click="handleSubmit(false)"
          >
            拒绝
          </el-button>
          <el-button type="primary" class="mr-l-10" @click="showDialog = false">
            取消
          </el-button>
        </el-form-item>
        <el-form-item v-if="dialogType === 4">
          <el-button type="primary" @click="clickPay">确定</el-button>
          <el-button class="mr-l-10" type="primary" @click="showDialog = false">
            取消
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-dialog>
</template>
<script>
  import {
    getDetail,
    getMobileCode,
    firstPass,
    firstReject,
    secPass,
    secReject,
    handlePay,
  } from '@/api/serviceFeeManagement';
  const TIME_COUNT = 180; //倒计时时间
  export default {
    components: {},
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: Boolean,
      title: {
        type: String,
        default: '查看',
      },
      //1:初审 2:复审 3:查看 4:打款
      dialogType: {
        type: Number,
        default: 1,
      },
      applyNo: {
        type: String,
        default: null,
      },
      list: {
        type: Array,
        default: () => {},
      },
    },
    data() {
      return {
        form: {},
        codeSend: false,
        timer: null,
        count: null,
        info: {},
        backExpressNoDisabled: true,
        backExpressCompanyDisabled: true,
        reject: false,
        invoiceUrlList: [],
        rules: {
          mobile: [
            { required: true, message: '请选择手机号', trigger: 'blur' },
          ],
          checkCode: [
            { required: true, message: '请填写验证码', trigger: 'blur' },
          ],
          remark: [
            { required: true, message: '请填写拒绝原因', trigger: 'blur' },
          ],
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {
        if (value) {
          //openDialog
          if (this.applyNo?.length > 0) {
            this.loadData();
          }
        } else {
          //closeDialog
          this.form = {};
          this.info = {};
          this.backExpressNoDisabled = true;
          this.backExpressCompanyDisabled = true;
          this.codeSend = false;
          clearInterval(this.timer);
          this.timer = null;
        }
      },
    },
    mounted() {},
    methods: {
      async loadData() {
        try {
          const res = await getDetail({ applyNo: this.applyNo });
          if (res) {
            if (!res.backExpressNo) {
              res.backExpressNo = '';
            }
            if (!res.backExpressCompany) {
              res.backExpressCompany = '';
            }
            this.info = { reason: '', ...res };
            if (this.dialogType === 1) {
              this.info.reason = '待审核';
            } else if (this.dialogType === 2) {
              this.info.reason = '初审通过';
            } else {
              if (this.info.remark?.length > 0) {
                this.info.reason = this.info.remark;
              } else {
                this.info.reason = '复审通过';
              }
            }
          }
        } catch (e) {}
      },
      handleSubmit(val) {
        this.reject = val;
        if (val) {
          if (this.dialogType === 1) {
            this.firstPass();
          } else {
            this.againPass();
          }
        } else {
          if (this.form.remark?.length > 0) {
            if (this.dialogType === 1) {
              this.firstReject();
            } else {
              this.againReject();
            }
          } else {
            this.$message.error('请填写拒绝原因');
          }
        }
      },
      async firstPass() {
        try {
          const res = await firstPass({ applyNo: this.applyNo });
          if (!res) {
            this.$message.success('操作成功');
            this.showDialog = false;
            this.$emit('submit');
          }
        } catch (e) {}
      },
      async againPass() {
        try {
          const res = await secPass({ applyNo: this.applyNo });
          if (!res) {
            this.$message.success('操作成功');
            this.showDialog = false;
            this.$emit('submit');
          }
        } catch (e) {}
      },
      async firstReject() {
        this.$refs['form'].validate(async valid => {
          if (!valid) {
            return false;
          } else {
            const body = {
              applyNo: this.applyNo,
              remark: this.form.remark,
            };
            const res = await firstReject(body);
            if (!res) {
              this.$message.success('操作成功');
              this.showDialog = false;
              this.$emit('submit');
            }
          }
        });
      },
      async againReject() {
        this.$refs['form'].validate(async valid => {
          if (!valid) {
            return false;
          } else {
            const body = {
              applyNo: this.applyNo,
              remark: this.form.remark,
            };
            if (this.info.backExpressNo?.length > 0) {
              body.backExpressNo = this.info.backExpressNo;
            }
            if (this.info.backExpressCompany?.length > 0) {
              body.backExpressCompany = this.info.backExpressCompany;
            }
            const res = await secReject(body);
            if (!res) {
              this.$message.success('操作成功');
              this.showDialog = false;
              this.$emit('submit');
            }
          }
        });
      },
      countDown() {
        if (!this.timer) {
          this.count = TIME_COUNT;
          this.codeSend = true;
          this.timer = setInterval(() => {
            if (this.count > 0 && this.count <= TIME_COUNT) {
              this.count--;
            } else {
              this.codeSend = false;
              clearInterval(this.timer);
              this.timer = null;
            }
          }, 1000);
        }
      },
      async getCode() {
        if (this.form.mobile?.length > 0) {
          try {
            const result = await getMobileCode({
              mobilePhone: this.form.mobile,
            });
            if (!result) {
              this.$message.success('验证码发送成功');
              this.countDown();
            }
          } catch (e) {}
        } else {
          this.$message.error('请选择手机号');
        }

        // console.log('err', err);
      },
      async clickPay() {
        this.$refs['form'].validate(async valid => {
          if (!valid) {
            return false;
          } else {
            const body = {
              applyNo: this.applyNo,
              mobilePhone: this.form.mobile,
              identifyingCode: this.form.checkCode,
            };
            const res = await handlePay(body);
            if (!res) {
              this.$message.success('操作成功');
              this.showDialog = false;
              this.$emit('submit');
            }
          }
        });
      },
    },
  };
</script>
<style lang="scss" scoped></style>
