<!--
 * @Author: 七七
 * @Date: 2021-12-17 14:11:41
 * @LastEditors: 七七
 * @LastEditTime: 2021-12-22 17:21:51
 * @FilePath: /access-fmis-web/src/views/serviceFeeManagement/incomePayment/ components/allPayDialog.vue
-->
<template>
  <el-dialog
    :visible.sync="showDialog"
    title="全量打款确认"
    width="40%"
    destroy-on-close
  >
    <h3>确认全量打款？</h3>
    <div>
      本次发放总笔数：{{ allPayData.totalNum }} 本次发放总金额：{{
        allPayData.totalAmount
      }}
    </div>
    <div style="margin: 10px 0">平安主体信息：</div>
    <div v-for="item in allPayData.subjectInfoList" :key="item.subjectName">
      <span>{{ item.subjectName }}:{{ item.subjectAmount }}</span>
      <span>{{ '   ' }}</span>
      <span>{{ item.subjectName + '渠道' }}:{{ item.subjectChannelBal }}</span>
    </div>
    <el-form ref="form" :model="form" :rules="rules">
      <el-form-item prop="mobile">
        <el-select v-model="form.mobile">
          <el-option
            v-for="item in list"
            :key="item.code"
            :label="item.desc"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="checkCode">
        <el-input v-model="form.checkCode" style="width: 200px"></el-input>
        <el-button
          class="mr-l-10"
          type="primary"
          :disabled="codeSend"
          @click="getCode"
        >
          获取验证码
          <span v-if="codeSend">({{ count }}s)</span>
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="clickPay">确定</el-button>
        <el-button class="mr-l-10" type="primary" @click="showDialog = false">
          取消
        </el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
<script>
  import {
    personalAllPayData,
    personalAllPay,
    getMobileCode,
    handlePay,
  } from '@/api/serviceFeeManagement';
  const TIME_COUNT = 180; //倒计时时间
  export default {
    components: {},
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: Boolean,
      list: {
        type: Array,
        default: () => {},
      },
      date: {
        type: Array,
        default: () => {},
      },
    },
    data() {
      return {
        form: {},
        allPayData: {},
        codeSend: false,
        timer: null,
        count: null,
        rules: {
          mobile: [
            { required: true, message: '请选择手机号', trigger: 'blur' },
          ],
          checkCode: [
            { required: true, message: '请填写验证码', trigger: 'blur' },
          ],
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {
        if (value) {
          //openDialog
          this.loadData();
        } else {
          //closeDialog
          this.codeSend = false;
          clearInterval(this.timer);
          this.timer = null;
          this.form = {};
          this.allPayData = {};
        }
      },
    },
    mounted() {},
    methods: {
      async loadData() {
        const body = {
          applyStartTime: this.date[0],
          applyEndTime: this.date[1],
        };
        try {
          const res = await personalAllPayData(body);
          if (res) {
            this.allPayData = { ...res };
          }
        } catch (e) {
          console.log('err', e);
        }
      },
      countDown() {
        if (!this.timer) {
          this.count = TIME_COUNT;
          this.codeSend = true;
          this.timer = setInterval(() => {
            if (this.count > 0 && this.count <= TIME_COUNT) {
              this.count--;
            } else {
              this.codeSend = false;
              clearInterval(this.timer);
              this.timer = null;
            }
          }, 1000);
        }
      },
      async getCode() {
        if (this.form.mobile?.length > 0) {
          try {
            const result = await getMobileCode({
              mobilePhone: this.form.mobile,
            });
            if (!result) {
              this.$message.success('验证码发送成功');
              this.countDown();
            }
          } catch (e) {}
        } else {
          this.$message.error('请选择手机号');
        }
        // console.log('err', err);
      },
      clickPay() {
        this.$refs['form'].validate(async valid => {
          if (!valid) {
            return false;
          } else {
            const body = {
              applyStartTime: this.date[0],
              applyEndTime: this.date[1],
              mobilePhone: this.form.mobile,
              identifyingCode: this.form.checkCode,
            };
            const res = await personalAllPay(body);
            if (!res) {
              this.$message.success('操作成功');
              this.showDialog = false;
              this.$emit('submit');
            }
          }
        });
      },
    },
  };
</script>
<style scoped></style>
