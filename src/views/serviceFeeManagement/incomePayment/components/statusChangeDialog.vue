<template>
  <div>
    <el-dialog
      width="800px"
      title="状态变更"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="saveForm"
        :rules="rulesForm"
        label-width="200px"
      >
        <el-form-item
          v-if="routerType !== 'overseasWithdrawalPay'"
          label="选择需要变更的打款渠道: "
          prop="borderType"
        >
          <el-select
            v-model="saveForm.borderType"
            style="width: 60%"
            placeholder="请选择需要变更的打款渠道"
          >
            <el-option label="境内" value="in"></el-option>
            <el-option label="境外" value="out"></el-option>
          </el-select>
          <span style="color: red; margin-left: 10px">
            当前状态：{{ currentRow && currentRow.statusDesc }}
          </span>
        </el-form-item>
        <el-form-item label="选择需要变更的状态类型: " prop="status">
          <el-select
            v-model="saveForm.status"
            style="width: 60%"
            placeholder="请选择需要变更的状态类型"
          >
            <el-option label="提现成功" value="success"></el-option>
            <el-option label="提现失败" value="failure"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="routerType !== 'overseasWithdrawalPay'"
          label="备注: "
          :required="false"
          prop="remark"
        >
          <el-input
            v-model="saveForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
            style="width: 60%"
          ></el-input>
        </el-form-item>
        <el-form-item v-else label="备注: " :required="false" prop="remark">
          <el-select v-model="saveForm.remark" clearable>
            <el-option
              v-for="item in remarkList"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="onOK">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { borderTypeUpdateStatus } from '@/api/replaceMakeOutInvoice';
  import { overseasUpdateStatus } from '@/api/serviceFeeManagement';
  import { debounce, initSearchParams } from '@/utils';

  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      currentRow: {
        type: Object,
        default: null,
      },
      routerType: {
        type: String,
        default: '',
      },
      remarkList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        rulesForm: {
          borderType: [
            {
              required: true,
              message: '请选择需要变更的打款渠道',
              trigger: 'blur',
            },
          ],
          status: [
            {
              required: true,
              message: '请选择需要变更的状态类型',
              trigger: 'blur',
            },
          ],
          remark: [
            {
              required: true,
              message: '请输入备注',
              trigger: 'blur',
              validator: (rule, value, callback) => {
                let err;
                // 门户成员时必须选择成员名称
                if (this.saveForm.status === 'failure' && !value) {
                  err = new Error('请输入备注');
                }
                callback(err);
              },
            },
          ],
        },
        saveForm: {
          applyNo: '',
          remark: '',
          status: '',
          borderType: '',
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(val) {
        if (val) {
          if (this.currentRow) {
            this.saveForm.applyNo = this.currentRow.applyNo;
          }
        } else {
          Object.assign(this.$data.saveForm, this.$options.data().saveForm);
          this.$nextTick(function () {
            this.$refs.formData.clearValidate();
          });
        }
      },
    },
    created() {},
    methods: {
      onOK: debounce(function () {
        this.$refs.formData.validate(async valid => {
          if (!valid) return;
          const api =
            this.routerType === 'overseasWithdrawalPay'
              ? overseasUpdateStatus
              : borderTypeUpdateStatus;

          api(initSearchParams(this.saveForm)).then(res => {
            this.$message.success('变更成功');
            this.showDialog = false;
            this.$emit('onGet');
          });
        });
      }, 800),
    },
  };
</script>

<style></style>
