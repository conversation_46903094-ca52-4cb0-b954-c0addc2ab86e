<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-30 09:50:13
 * @LastEditTime: 2024-07-18 17:33:38
 * @LastEditors: Please set LastEditors
 * @Reference: 
-->
<template>
  <div>
    <div>
      <el-form ref="form" :model="form" label-width="80px" inline>
        <el-form-item label="申请时间:" prop="applyTime">
          <el-date-picker
            v-model="form.applyTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="代理ID:" prop="idCode">
          <el-input v-model="form.idCode" placeholder="请输入代理ID"></el-input>
        </el-form-item>
        <el-form-item label="手机号码:" prop="mobilePhone">
          <el-input
            v-model="form.mobilePhone"
            placeholder="请输入手机号码"
          ></el-input>
        </el-form-item>
        <el-form-item label="单号:" prop="applyNo">
          <el-input
            v-model="form.applyNo"
            placeholder="请输入单号"
            style="width: 250px"
          ></el-input>
        </el-form-item>
        <el-form-item label="审核状态:" prop="status">
          <el-select v-model="form.status" clearable>
            <el-option
              v-for="item in option.statusList"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <ac-permission-button
            btn-text="查询"
            permission-key="overseasWithdrawalApplication-search"
            @click="search(1)"
          ></ac-permission-button>
          <el-button type="primary" @click="reset">重置</el-button>
          <ac-permission-button
            btn-text="全量审核"
            permission-key="overseasWithdrawalApplication-full-audit"
            @click="allPass"
          ></ac-permission-button>
          <ac-permission-button
            btn-text="导出"
            permission-key="overseasWithdrawalApplication-export"
            @click="onExport"
          ></ac-permission-button>
        </el-form-item>
      </el-form>
    </div>

    <div>
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        :header-cell-style="{ 'text-align': 'center' }"
        :cell-style="{ 'text-align': 'center' }"
      >
        <el-table-column type="index" label="序号"></el-table-column>
        <el-table-column prop="applyNo" label="单号"></el-table-column>
        <el-table-column prop="idCode" label="代理ID"></el-table-column>
        <el-table-column prop="name" label="开户姓名"></el-table-column>
        <el-table-column prop="mobilePhone" label="手机号码"></el-table-column>
        <el-table-column
          prop="beneficiaryCountryName"
          label="国家"
        ></el-table-column>
        <el-table-column
          prop="bankAccountCurrency"
          label="卡币种"
        ></el-table-column>
        <el-table-column prop="bankName" label="开户银行"></el-table-column>
        <el-table-column prop="bankCardNo" label="银行账号"></el-table-column>
        <el-table-column prop="applyAmount" label="申请金额"></el-table-column>
        <el-table-column prop="payAmount" label="实付金额"></el-table-column>
        <el-table-column
          min-width="100"
          prop="currencyPayAmount"
          label="结算金额_原币"
        ></el-table-column>
        <el-table-column
          min-width="100"
          prop="exchangeRate"
          label="汇率"
        ></el-table-column>
        <el-table-column prop="applyTime" label="申请时间"></el-table-column>
        <el-table-column prop="statusDesc" label="审核状态"></el-table-column>

        <el-table-column
          min-width="100"
          max-width="300"
          fixed="right"
          label="操作"
        >
          <template slot-scope="{ row }">
            <!-- <el-button type="text" @click="view(row)">详情</el-button> -->
            <ac-permission-button
              v-if="row.status === 'applying'"
              slot="reference"
              type="text"
              size="small"
              btn-text="审核通过"
              permission-key="overseasWithdrawalApplication-examination-passed"
              @click="passApply(row.applyNo)"
            ></ac-permission-button>
            <ac-permission-button
              v-if="row.status === 'applying'"
              slot="reference"
              type="text"
              size="small"
              btn-text="审核拒绝"
              permission-key="overseasWithdrawalApplication-review-rejection"
              @click="rejectApply(row.applyNo)"
            ></ac-permission-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page.current"
        :page-sizes="[10, 20, 50]"
        :page-size="page.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <el-dialog title="详情" :visible.sync="dialogVisible" width="60%">
      <div>
        <el-descriptions :column="2">
          <el-descriptions-item label="代理ID">
            {{ detailData.idCode }}
          </el-descriptions-item>
          <el-descriptions-item label="合作方式">
            {{ detailData.authTypeDesc }}
          </el-descriptions-item>
          <el-descriptions-item label="手机号">
            {{ detailData.mobilePhone }}
          </el-descriptions-item>
          <el-descriptions-item label="代理姓名">
            {{ detailData.userName }}
          </el-descriptions-item>
          <el-descriptions-item label="户名">
            {{ detailData.name }}
          </el-descriptions-item>
          <el-descriptions-item
            v-if="detailData.merchantName"
            label="个体工商户名称"
          >
            {{ detailData.merchantName }}
          </el-descriptions-item>
          <el-descriptions-item label="银行卡号">
            {{ detailData.bankCardNo }}
          </el-descriptions-item>
          <el-descriptions-item label="税号">
            {{ detailData.taxNo }}
          </el-descriptions-item>
          <el-descriptions-item label="开户行">
            {{ detailData.bankName }}
          </el-descriptions-item>
          <el-descriptions-item label="审核理由|拒绝理由">
            {{ detailData.remark }}
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">
            {{ detailData.applyTime }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    getOverseasReviewComboBoxData,
    getOverseasReviewList,
    downloadOverseasReviewList,
    getOverseasAllReviewData,
    overseasAllPass,
    overseasPass,
    overseasReject,
  } from '@/api/serviceFeeManagement';

  import { setInitData } from '@/utils';
  import { isNumber } from '@/utils/validate';
  export default {
    name: 'OverseasWithdrawalApplication',
    data() {
      return {
        form: {
          applyTime: setInitData(30, '{y}-{m}-{d} {h}:{i}:{s}'),
          idCode: '',
          mobilePhone: '',
          platformCode: 'ABM',
          applyNo: '',
          status: '',
        },
        option: { statusList: [] },
        tableData: [],
        page: {
          current: 1,
          size: 10,
        },
        total: 0,
        loading: false,
        dialogVisible: false,
        detailData: {}, //详情
      };
    },
    created() {
      this.fetch();
      this.search();
    },
    methods: {
      async getOverseasAllReviewData(cd) {
        const { applyTime = [] } = this.form;
        const res = await getOverseasAllReviewData({
          applyStartTime: applyTime[0],
          applyEndTime: applyTime[1],
          platformCode: 'ABM',
        });
        if (res) {
          cd && cd(res);
        }
      },
      async onExport() {
        const data = this.getParams();
        if (!data) return;
        const res = await downloadOverseasReviewList({
          ...data,
          taskContent: '服务费管理-收益打款管理-海外个人提现申请-导出',
        });
        this.$message.success('任务正在处理中，请稍后到任务中心查看');
      },
      //获取下拉框
      async fetch() {
        const res = await getOverseasReviewComboBoxData();
        this.option.statusList = res?.statusList || [];
      },
      getParams() {
        const { applyTime = [], ...form } = this.form;
        const data = {
          applyStartTime: Array.isArray(applyTime) ? applyTime[0] : '',
          applyEndTime: Array.isArray(applyTime) ? applyTime[1] : '',
          ...this.page,
          ...form,
        };
        if (!isNumber(data.idCode)) {
          this.$message.warning('代理ID只能为数字');
          return false;
        } else {
          return data;
        }
      },
      //查询
      async search(current = 0) {
        if (current) {
          this.page.current = current;
        }
        const data = this.getParams();
        if (!data) return;
        this.loading = true;
        const res = await getOverseasReviewList(data);
        this.loading = false;
        if (res) {
          this.tableData = res.records || [];
          this.total = res.total || 0;
        }
      },
      //重置
      reset() {
        this.$refs['form'].resetFields();
      },
      //全量审核
      async allPass() {
        console.log(this.form.applyTime);
        if (!this.form.applyTime) {
          this.$message.warning('请先选择申请时间');
          return;
        }
        this.getOverseasAllReviewData(res => {
          if (res) {
            const confirmText = [
              '确认全量审核？',
              `本次审核总笔数:${res.totalNum}  本次审核总金额:${res.totalAmount}`,
            ];
            const newDatas = [];
            const h = this.$createElement;
            for (const i in confirmText) {
              newDatas.push(h('p', null, confirmText[i]));
            }
            this.$confirm('全量审核确认', {
              title: '全量审核确认',
              message: h('div', null, newDatas),
              showCancelButton: true,
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            })
              .then(async () => {
                const { applyTime = [] } = this.form;
                await overseasAllPass({
                  applyStartTime: applyTime[0],
                  applyEndTime: applyTime[1],
                  platformCode: 'ABM',
                });
                this.$message.success('任务正在处理中，请稍后到任务中心查看');
              })
              .catch(() => {});
          }
        });
      },
      //详情
      async view(row) {
        // const res = await getReviewDetail({ applyNo: row.applyNo });
        if (row) {
          this.detailData = row || {};
        }
        if (this.detailData) {
          this.dialogVisible = true;
        }
      },
      //审核通过
      passApply(applyNo) {
        this.$confirm('确认审核通过？', '审核确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        })
          .then(async () => {
            await overseasPass({ applyNo });
            this.$message.success('审核通过成功');
            this.search();
          })
          .catch(() => {});
      },
      //审核拒绝
      rejectApply(applyNo) {
        this.$prompt('确认审核拒绝？', '审核确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPlaceholder: '拒绝原因(必填)',
          inputPattern: /^[\s\S]*.*[^\s][\s\S]*$/,
          inputErrorMessage: '拒绝原因必填',
        })
          .then(async ({ value }) => {
            const data = {
              applyNo,
              remark: value,
            };
            await overseasReject(data);
            this.$message.success('审核拒绝成功');
            this.search();
          })
          .catch(() => {});
      },
      //分页
      handleCurrentChange(e) {
        this.page.current = e;
        this.search();
      },
      handleSizeChange(e) {
        this.page.size = e;
        this.handleCurrentChange(1);
      },
    },
  };
</script>

<style lang="scss" scoped>
  /deep/ .el-descriptions--small {
    font-size: 14px;
  }
</style>
