<template>
  <div>
    <div>
      <el-form ref="form" :model="form" label-width="80px" inline>
        <el-form-item label="提现周期:" prop="payCycle">
          <el-date-picker
            v-model="form.payCycle"
            value-format="yyyy-MM"
            type="month"
            placeholder="提现周期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="代理ID:" prop="idCode">
          <el-input v-model="form.idCode" placeholder="请输入代理ID"></el-input>
        </el-form-item>
        <el-form-item label="手机号码:" prop="mobilePhone">
          <el-input
            v-model="form.mobilePhone"
            placeholder="请输入手机号码"
          ></el-input>
        </el-form-item>
        <el-form-item label="合作方式:" prop="authType">
          <el-select v-model="form.authType" clearable>
            <el-option
              v-for="item in option.authTypeList"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="申请单号:" prop="applyNo">
          <el-input
            v-model="form.applyNo"
            placeholder="请输入申请单号"
            style="width: 250px"
          ></el-input>
        </el-form-item>
        <el-form-item label="审核状态:" prop="status">
          <el-select v-model="form.status" clearable>
            <el-option
              v-for="item in option.statusList"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div style="float: right; margin-bottom: 15px">
      <!-- <el-button type="primary" @click="search(1)">查询</el-button> -->
      <ac-permission-button
        btn-text="查询"
        permission-key="approvalBilling-search"
        @click="search(1)"
      ></ac-permission-button>
      <el-button type="primary" @click="reset">重置</el-button>

      <ac-permission-button
        btn-text="全量审核"
        permission-key="approvalBilling-full-audit"
        @click="allPass"
      ></ac-permission-button>
      <ac-permission-button
        btn-text="导出"
        permission-key="approvalBilling-export"
        @click="onExport"
      ></ac-permission-button>
    </div>
    <div>
      <el-table
        :data="tableData"
        border
        :header-cell-style="{ 'text-align': 'center' }"
        :cell-style="{ 'text-align': 'center' }"
      >
        <el-table-column prop="applyNo" label="申请单"></el-table-column>
        <el-table-column prop="payCycle" label="所属周期"></el-table-column>
        <el-table-column prop="idCode" label="代理ID"></el-table-column>
        <el-table-column prop="userName" label="代理姓名"></el-table-column>
        <el-table-column prop="mobilePhone" label="手机号"></el-table-column>
        <el-table-column prop="authTypeDesc" label="合作方式"></el-table-column>
        <el-table-column prop="applyAmount" label="申请金额"></el-table-column>
        <el-table-column prop="payAmount" label="应付金额"></el-table-column>
        <el-table-column
          min-width="100"
          prop="payInAmount"
          label="应付-境内"
        ></el-table-column>
        <el-table-column
          min-width="100"
          prop="payOutAmount"
          label="应付-境外"
        ></el-table-column>
        <el-table-column prop="feeRatio" label="手续费率"></el-table-column>
        <el-table-column prop="feeAmount" label="手续费"></el-table-column>
        <el-table-column prop="name" label="开户名"></el-table-column>
        <el-table-column prop="bankCardNo" label="银行卡"></el-table-column>
        <el-table-column prop="bankName" label="开户行"></el-table-column>
        <el-table-column prop="statusDesc" label="审核状态"></el-table-column>
        <el-table-column
          min-width="110"
          prop="letaxLogoutDesc"
          label="乐税开户状态"
        ></el-table-column>
        <el-table-column prop="remark" label="备注"></el-table-column>
        <el-table-column prop="applyTime" label="申请时间"></el-table-column>
        <el-table-column prop="updateTime" label="更新时间"></el-table-column>
        <el-table-column prop="approver" label="操作人"></el-table-column>
        <el-table-column
          min-width="100"
          max-width="300"
          fixed="right"
          label="操作"
        >
          <template slot-scope="{ row }">
            <el-button type="text" @click="view(row.applyNo)">详情</el-button>
            <ac-permission-button
              v-if="row.status === 'applying'"
              slot="reference"
              type="text"
              size="small"
              btn-text="审核通过"
              permission-key="approvalBilling-examination-passed"
              @click="passApply(row.applyNo)"
            ></ac-permission-button>
            <ac-permission-button
              v-if="row.status === 'applying'"
              slot="reference"
              type="text"
              size="small"
              btn-text="审核拒绝"
              permission-key="approvalBilling-review-rejection"
              @click="rejectApply(row.applyNo)"
            ></ac-permission-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page.current"
        :page-sizes="[10, 20, 50]"
        :page-size="page.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <el-dialog title="详情" :visible.sync="dialogVisible" width="60%">
      <div>
        <el-descriptions :column="2">
          <el-descriptions-item label="代理ID">
            {{ detailData.idCode }}
          </el-descriptions-item>
          <el-descriptions-item label="合作方式">
            {{ detailData.authTypeDesc }}
          </el-descriptions-item>
          <el-descriptions-item label="手机号">
            {{ detailData.mobilePhone }}
          </el-descriptions-item>
          <el-descriptions-item label="代理姓名">
            {{ detailData.userName }}
          </el-descriptions-item>
          <el-descriptions-item label="户名">
            {{ detailData.name }}
          </el-descriptions-item>
          <el-descriptions-item
            v-if="detailData.merchantName"
            label="个体工商户名称"
          >
            {{ detailData.merchantName }}
          </el-descriptions-item>
          <el-descriptions-item label="银行卡号">
            {{ detailData.bankCardNo }}
          </el-descriptions-item>
          <el-descriptions-item label="税号">
            {{ detailData.taxNo }}
          </el-descriptions-item>
          <el-descriptions-item label="开户行">
            {{ detailData.bankName }}
          </el-descriptions-item>
          <el-descriptions-item label="审核理由|拒绝理由">
            {{ detailData.remark }}
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">
            {{ detailData.applyTime }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    getReviewList,
    getAllReviewData,
    allPass,
    getReviewDetail,
    reject,
    pass,
    getReviewComboBoxData,
    replaceMakeOutInvoiceDownload,
  } from '@/api/replaceMakeOutInvoice';
  import { parseTime, getWithdrawalCycle } from '@/utils';
  import { isNumber } from '@/utils/validate';
  export default {
    name: 'ApprovalBiling',
    data() {
      return {
        form: {
          payCycle: getWithdrawalCycle(),
          idCode: '',
          mobilePhone: '',
          authType: '',
          applyNo: '',
          status: '',
        },
        option: { authTypeList: [], statusList: [] },
        tableData: [],
        page: {
          current: 1,
          size: 10,
        },
        total: 0,
        dialogVisible: false,
        detailData: {}, //详情
      };
    },
    created() {
      this.fetch();
      this.search();
    },
    methods: {
      async onExport() {
        const data = this.getParams();
        if (!data) return;
        const res = await replaceMakeOutInvoiceDownload({
          ...data,
          taskContent: '服务费管理-收益打款管理-代开票提现审核-导出',
        });
        this.$message.success('任务正在处理中，请稍后到任务中心查看');
      },
      //获取下拉框
      async fetch() {
        const res = await getReviewComboBoxData();
        this.option.authTypeList = res?.authTypeList || [];
        this.option.statusList = res?.statusList || [];
      },
      getParams() {
        const form = this.form;
        const data = {
          ...this.page,
          ...this.form,
        };
        if (!isNumber(data.idCode)) {
          this.$message.warning('代理ID只能为数字');
          return false;
        } else {
          return data;
        }
      },
      //查询
      async search(current = 0) {
        if (current) {
          this.page.current = current;
        }
        const data = this.getParams();
        if (!data) return;
        const res = await getReviewList(data);
        if (res) {
          this.tableData = res.records || [];
          this.total = res.total || 0;
        }
      },
      //重置
      reset() {
        this.$refs['form'].resetFields();
      },
      //全量审核
      async allPass() {
        console.log(this.form.payCycle);
        if (!this.form.payCycle) {
          this.$message.warning('请先选择提现周期');
          return;
        }
        const res = await getAllReviewData({ payCycle: this.form.payCycle });
        if (res) {
          const confirmText = [
            '确认全量审核？',
            `本次审核总笔数:${res.totalNum}  本次审核总金额:${res.totalAmount}`,
          ];
          const newDatas = [];
          const h = this.$createElement;
          for (const i in confirmText) {
            newDatas.push(h('p', null, confirmText[i]));
          }
          this.$confirm('全量审核确认', {
            title: '全量审核确认',
            message: h('div', null, newDatas),
            showCancelButton: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(async () => {
              await allPass({ payCycle: this.form.payCycle });
              this.$message.success('任务正在处理中，请稍后到任务中心查看');
            })
            .catch(() => {});
        }
      },
      //详情
      async view(applyNo) {
        const res = await getReviewDetail({ applyNo });
        if (res) {
          this.detailData = res || {};
        }
        if (this.detailData) {
          this.dialogVisible = true;
        }
      },
      //审核通过
      passApply(applyNo) {
        this.$confirm('确认审核通过？', '审核确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        })
          .then(async () => {
            await pass({ applyNo });
            this.$message.success('审核通过成功');
            this.search();
          })
          .catch(() => {});
      },
      //审核拒绝
      rejectApply(applyNo) {
        this.$prompt('确认审核拒绝？', '审核确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPlaceholder: '拒绝原因(必填)',
          inputPattern: /^[\s\S]*.*[^\s][\s\S]*$/,
          inputErrorMessage: '拒绝原因必填',
        })
          .then(async ({ value }) => {
            const data = {
              applyNo,
              remark: value,
            };
            await reject(data);
            this.$message.success('审核拒绝成功');
            this.search();
          })
          .catch(() => {});
      },
      //分页
      handleCurrentChange(e) {
        this.page.current = e;
        this.search();
      },
      handleSizeChange(e) {
        this.page.size = e;
        this.handleCurrentChange(1);
      },
    },
  };
</script>

<style lang="scss" scoped>
  /deep/ .el-descriptions--small {
    font-size: 14px;
  }
</style>
