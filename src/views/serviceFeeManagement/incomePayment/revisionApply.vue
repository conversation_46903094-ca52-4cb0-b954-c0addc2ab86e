<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-27 16:30:38
 * @LastEditTime: 2025-05-06 16:35:34
 * @LastEditors: Please set LastEditors
 * @Reference: 
-->
<template>
  <div>
    <el-form ref="form" :inline="true" :model="params">
      <el-form-item label="申请流水号:">
        <el-input v-model="params.applyId" placeholder="请输入单号" />
      </el-form-item>
      <el-form-item label="审核状态:">
        <el-select v-model="params.applyStatus" clearable placeholder="请选择">
          <el-option
            v-for="(item, index) in applyStatusList"
            :key="index"
            :label="item.label"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="处理状态:">
        <el-select v-model="params.callStatus" clearable placeholder="请选择">
          <el-option
            v-for="(item, index) in callStatusList"
            :key="index"
            :label="item.label"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <ac-permission-button
          btn-text="查询"
          permission-key="revision-search"
          @click="search(true)"
        ></ac-permission-button>
        <el-button type="primary" @click="reset">重置</el-button>
        <ac-permission-button
          btn-text="新增申请"
          permission-key="revision-add"
          @click="showAddModal = true"
        ></ac-permission-button>
        <ac-permission-button
          btn-text="导入申请"
          permission-key="revision-import"
          @click="showImportModal = true"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :pagination="pagination"
      :fetch="fetch"
    ></dynamictable>

    <addAuditForm
      ref="form"
      v-model="showAddModal"
      type="add"
      @close="showAddModal = false"
      @saveForm="saveForm"
    ></addAuditForm>

    <addAuditForm
      ref="form"
      v-model="showImportModal"
      type="import"
      @close="showImportModal = false"
      @importForm="importForm"
    ></addAuditForm>
  </div>
</template>
<script>
  import { setInitData } from '@/utils';
  import {
    revisionApply,
    revisionQueryApply,
    checkRevisionApply,
    rejectRevisionApply,
    revisionImportExcel,
    batchCheckRevisionApply,
    batchRejectRevisionApply,
  } from '@/api/serviceFeeManagement';
  import dynamictable from '@/components/dynamic-table';
  import addAuditForm from './components/addAuditForm';
  import store from '@/store';

  export default {
    components: {
      dynamictable,
      addAuditForm,
    },
    data() {
      return {
        title: '新增订正申请',
        columns: [
          {
            prop: 'id',
            label: '序号',
          },
          {
            prop: 'applyId',
            label: '申请流水号',
          },
          {
            prop: 'idCode',
            label: '代理id',
            render: ({ postJsonData }) => (
              <span>{JSON.parse(postJsonData).idCode}</span>
            ),
          },
          {
            prop: 'accountEntityCode',
            label: '收益发放主体',
            render: ({ postJsonData }) => (
              <span>
                {
                  this.accountEntityDict[
                    JSON.parse(postJsonData).accountEntityCode
                  ]
                }
              </span>
            ),
          },
          {
            prop: 'money',
            label: '修复金额',
            render: ({ postJsonData }) => (
              <span>{JSON.parse(postJsonData).money}</span>
            ),
          },
          {
            prop: 'profitType',
            label: '修复收益类型',
            render: ({ postJsonData }) => (
              <span>
                {this.profitTypeJson[JSON.parse(postJsonData).profitType]}
              </span>
            ),
          },
          {
            prop: 'profitDes',
            label: '修复收益描述',
            render: ({ postJsonData }) => (
              <span>{JSON.parse(postJsonData).profitDes}</span>
            ),
          },
          {
            prop: 'applyStatus',
            label: '审核状态',
            render: ({ applyStatus }) => (
              <span>{this.applyStatusDict[applyStatus]}</span>
            ),
          },
          {
            prop: 'rejectMsg',
            label: '拒绝原因',
          },
          {
            prop: 'callStatus',
            label: '处理状态',
            render: ({ callStatus }) => (
              <span>{this.callStatusDict[callStatus]}</span>
            ),
          },
          {
            prop: 'failMsg',
            label: '失败原因',
          },
          {
            prop: 'createBy',
            label: '申请人',
          },
          {
            prop: 'createTime',
            label: '创建时间',
          },
          {
            prop: 'updateBy',
            label: '更新人',
          },
          {
            prop: 'updateTime',
            label: '更新时间',
          },
        ],
        list: [],
        accountTypeList: [
          { code: 1, label: 'ABM' },
          { code: 2, label: 'DT' },
        ],
        accountTypeDict: {
          1: 'ABM',
          2: 'DT',
        },
        accountEntityDict: {
          C0000001: 'HT-境内',
          C0000001_OUT: 'HT-境外',
          C0000002: 'WG-境内',
          C0000002_OUT: 'WG-境外',
          C0000003: 'HOUPU-境内',
          C0000003_OUT: 'HOUPU-境外',
          C0000004: 'ABM_AU-境内',
          C0000004_OUT: 'ABM_AU-境外',
          C0000005: 'ABM_NZ-境内',
          C0000005_OUT: 'ABM_NZ-境外',
          C0000006: 'SG-境内',
          C0000006_OUT: 'SG-境外',
          C0000007: 'LANTERN-境内',
          C0000007_OUT: 'LANTERN-境外',
          C0000008: 'CD-境内',
          C0000008_OUT: 'CD-境外',
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        modifyTypeList: [
          { code: 0, label: '支出' },
          { code: 1, label: '收益' },
        ],
        applyStatusList: [
          { code: 0, label: '待审核' },
          { code: 1, label: '审核处理中' },
          { code: 2, label: '审核成功' },
          { code: 3, label: '审核拒绝' },
        ],
        applyStatusDict: {
          0: '待审核',
          1: '审核处理中',
          2: '审核成功',
          3: '审核拒绝',
        },
        applyStatus: '',
        callStatusList: [
          { code: 0, label: '处理成功' },
          { code: 1, label: '处理失败' },
        ],
        callStatusDict: {
          0: '处理成功',
          1: '处理失败',
        },
        showAddModal: false,
        showImportModal: false,
        callStatus: '',
        selectoptions: [],
        showDialog: false,
        profitTypeJson: {
          6: '转入',
          7: '转出',
          14: '活动奖金入账',
          15: '活动奖金扣除',
        },
        profitTypeList: [
          { code: 6, label: '转入' },
          { code: 7, label: '转出' },
          { code: 14, label: '活动奖金入账' },
          { code: 15, label: '活动奖金扣除' },
        ],
        profitType: '',
        params: {
          applyId: '',
          mobilePhone: '',
          status: '',
          subjectCode: '',
          applyNo: '',
          formId: 1,
          applyTime: [], // 申请时间
          applyStartTime: null, // 申请开始时间
          applyEndTime: null, // 申请结束时间
        },
        type: '',
        batchIds: [],
        statusList: [], // 发放状态下拉框
        subjectList: [], // 主体下拉框
        tableData: [],
        showImportDialog: false,
        checkDialogVisiable: false,
        rejectDialogVisiable: false,
        loading: false, // loading表格加载层
        pageNum: 1,
        pageSize: 10,
        total: 0,
      };
    },
    created() {
      this.params.applyTime = setInitData(30);
      this.fetch();
    },
    methods: {
      async fetch() {
        let params = { ...this.params };
        params.limit = this.pagination.pageLimit;
        params.pageNo = this.pagination.pageSize;
        this.loading = true;
        try {
          const res = await revisionQueryApply(params);
          if (res) {
            this.list = res.records;
            this.pagination.total = res.total;
            this.loading = false;
          } else {
            this.loading = false;
          }
        } catch (error) {
          this.loading = false;
        }
      },
      async saveForm(data) {
        let params = {
          formId: '1',
          applyJson: data,
          createBy: store.state.user.username,
        };
        const res = await revisionApply(params);
        this.$message.success('操作成功');
        this.showDialog = false;
        this.fetch();
      },
      async checkRevisionApply() {
        const params = {
          updateBy: store.state.user.username,
          formId: '1',
          ...this.params,
        };
        try {
          const res = await checkRevisionApply(params);
        } catch (error) {}
        this.fetch();
      },
      async rejectRevisionApply() {
        const params = {
          updateBy: store.state.user.username,
          formId: '1',
          ...this.params,
        };
        try {
          const res = await rejectRevisionApply(params);
        } catch (error) {}
        this.fetch();
      },
      search() {
        this.pagination.pageSize = 1;
        this.fetch();
      },
      addRevision() {
        this.showDialog = true;
      },
      async importForm(e, saveParams) {
        const params = {
          accountType: saveParams.accountType,
          profitType: saveParams.profitType,
          url: e.url,
          fileName: e.fileName,
          operate: store.state.user.username,
          formId: 1,
        };
        try {
          const res = await revisionImportExcel(params);
        } catch (error) {}
        this.fetch();
      },
      importRevision() {
        this.showImportDialog = true;
      },
      showAuditDialog(type, applyId) {
        this.rejectDialogVisiable = true;
      },
      onClose() {
        this.checkDialogVisiable = false;
        this.rejectDialogVisiable = false;
      },
      reset() {
        Object.assign(this.params, this.$options.data.call(this).params);
        this.pagination.pageSize = 1;
        this.pagination.pageLimit = 10;
      },
      handleSizeChange(val) {
        this.pagination.pageSize = 1;
        this.pagination.pageLimit = val;
        this.fetch();
      },
      handleCurrentChange(val) {
        this.pageSize = val;
        this.fetch();
      },
    },
  };
</script>
<style lang="scss"></style>
