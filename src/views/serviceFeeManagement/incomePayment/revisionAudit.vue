<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-27 16:30:38
 * @LastEditTime: 2025-05-06 16:35:36
 * @LastEditors: Please set LastEditors
 * @Reference: 
-->
<template>
  <div>
    <el-form ref="form" :inline="true" :model="params">
      <el-form-item label="申请流水号:">
        <el-input v-model="params.applyId" placeholder="请输入单号" />
      </el-form-item>
      <el-form-item label="审核状态:">
        <el-select v-model="params.applyStatus" clearable placeholder="请选择">
          <el-option
            v-for="(item, index) in applyStatusList"
            :key="index"
            :label="item.label"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="处理状态:">
        <el-select v-model="params.callStatus" clearable placeholder="请选择">
          <el-option
            v-for="(item, index) in callStatusList"
            :key="index"
            :label="item.label"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <ac-permission-button
          btn-text="查询"
          permission-key="revision-search"
          @click="search(true)"
        ></ac-permission-button>
        <el-button type="primary" @click="reset">重置</el-button>
        <ac-permission-button
          btn-text="批量审核"
          permission-key="revision-audit"
          @click="batchHandle('pass')"
        ></ac-permission-button>
        <ac-permission-button
          btn-text="批量拒绝"
          permission-key="revision-reject"
          @click="batchHandle('reject')"
        ></ac-permission-button>
        <ac-permission-button
          btn-text="导入清零账户"
          permission-key="personalInvoicingPay-import"
          @click="onBtnImport"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :pagination="pagination"
      :fetch="fetch"
      :options="options"
      :check-selectable="handleCheckSelectable"
      @selection-change="selectionChange"
    >
      <template slot="operation" slot-scope="scope">
        <el-button
          slot="reference"
          type="text"
          size="mini"
          :disabled="scope.row.applyStatus == 0 ? false : true"
          @click="showAuditDialog('pass', scope.row.applyId)"
        >
          审核通过
        </el-button>
        <el-button
          slot="reference"
          type="text"
          size="mini"
          :disabled="scope.row.applyStatus == 0 ? false : true"
          @click="showAuditDialog('reject', scope.row.applyId)"
        >
          审核拒绝
        </el-button>
      </template>
    </dynamictable>
    <el-dialog
      title="提示"
      :visible.sync="checkDialogVisiable"
      width="400px"
      @closed="onClose"
    >
      <div>
        <div>确认审核通过？</div>
        <div slot="footer">
          <el-button @click="checkDialogVisiable = false">取 消</el-button>
          <el-button
            :loading="loading"
            type="primary"
            @click="checkRevisionApply"
          >
            确 定
          </el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      title="提示"
      :visible.sync="rejectDialogVisiable"
      width="400px"
      @closed="onClose"
    >
      <div>
        <el-input v-model="params.rejectMsg" placeholder="请填写拒绝原因" />
      </div>
      <div slot="footer">
        <el-button @click="rejectDialogVisiable = false">取 消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="rejectRevisionApply"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="提示"
      :visible.sync="batchCheckDialogVisiable"
      width="400px"
      @closed="onClose"
    >
      <div>
        <div>确认批量审核通过？</div>
        <div slot="footer">
          <el-button @click="batchCheckDialogVisiable = false">取 消</el-button>
          <el-button
            :loading="loading"
            type="primary"
            @click="batchCheckRevisionApply"
          >
            确 定
          </el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      title="提示"
      :visible.sync="batchRejectDialogVisiable"
      width="400px"
      @closed="onClose"
    >
      <div>
        <el-input v-model="params.rejectMsg" placeholder="请填写拒绝原因" />
      </div>
      <div slot="footer">
        <el-button @click="batchRejectDialogVisiable = false">取 消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="batchRejectRevisionApply"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      width="40%"
      :title="title"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
      @close="onClose"
    >
      <addAuditForm
        ref="form"
        @close="showDialog = false"
        @saveForm="saveForm"
      ></addAuditForm>
    </el-dialog>
    <el-dialog
      title="导入清零账户"
      :visible.sync="importFileDiolog"
      width="500px"
      @closed="onClose"
    >
      <div>
        <el-upload
          ref="upload"
          drag
          class="upload-demo"
          :limit="1"
          :headers="headers"
          :data="uploadData"
          :action="action"
          :on-preview="handlePreview"
          :on-remove="handleRemove"
          :on-success="onSuccess"
          :file-list="fileList"
          :auto-upload="false"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            <p>将文件拖到此处，或点击上传</p>
            <p>支持扩展名：.xlsx, .xls</p>
          </div>

          <div slot="tip" class="el-upload__tip">
            <p>将文件拖到此处，或点击上传</p>
          </div>
        </el-upload>
      </div>
      <div slot="footer">
        <el-button @click="importFileDiolog = false">取 消</el-button>
        <el-button type="primary" @click="importFileConfirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import { setInitData } from '@/utils';
  import {
    revisionApply,
    revisionQueryApply,
    checkRevisionApply,
    rejectRevisionApply,
    importCleanUser,
    batchCheckRevisionApply,
    batchRejectRevisionApply,
  } from '@/api/serviceFeeManagement';
  import dynamictable from '@/components/dynamic-table';
  import addAuditForm from './components/addAuditForm';
  import { getCookie } from '@/utils/auth';
  import injectHost from '@/utils/injectHost';
  import { replaceLocalDomain } from '@/utils/index.js';
  import store from '@/store';

  export default {
    components: {
      dynamictable,
      addAuditForm,
    },
    data() {
      return {
        title: '新增订正申请',
        columns: [
          {
            prop: 'id',
            label: '序号',
          },
          {
            prop: 'applyId',
            label: '申请流水号',
          },
          {
            prop: 'idCode',
            label: '代理id',
            render: ({ postJsonData }) => (
              <span>{JSON.parse(postJsonData).idCode}</span>
            ),
          },
          {
            prop: 'accountEntityCode',
            label: '收益发放主体',
            render: ({ postJsonData }) => (
              <span>
                {
                  this.accountEntityDict[
                    JSON.parse(postJsonData).accountEntityCode
                  ]
                }
              </span>
            ),
          },
          {
            prop: 'money',
            label: '修复金额',
            render: ({ postJsonData }) => (
              <span>{JSON.parse(postJsonData).money}</span>
            ),
          },
          {
            prop: 'profitType',
            label: '修复收益类型',
            render: ({ postJsonData }) => (
              <span>
                {this.profitTypeJson[JSON.parse(postJsonData).profitType]}
              </span>
            ),
          },
          {
            prop: 'profitDes',
            label: '修复收益描述',
            render: ({ postJsonData }) => (
              <span>{JSON.parse(postJsonData).profitDes}</span>
            ),
          },
          {
            prop: 'applyStatus',
            label: '审核状态',
            render: ({ applyStatus }) => (
              <span>{this.applyStatusDict[applyStatus]}</span>
            ),
          },
          {
            prop: 'rejectMsg',
            label: '拒绝原因',
          },
          {
            prop: 'callStatus',
            label: '处理状态',
            render: ({ callStatus }) => (
              <span>{this.callStatusDict[callStatus]}</span>
            ),
          },
          {
            prop: 'failMsg',
            label: '失败原因',
          },
          {
            prop: 'createBy',
            label: '申请人',
          },
          {
            prop: 'createTime',
            label: '创建时间',
          },
          {
            prop: 'updateBy',
            label: '更新人',
          },
          {
            prop: 'updateTime',
            label: '更新时间',
          },
          {
            prop: 'operation',
            label: '操作',
            width: '100px',
            fixed: 'right',
            scopedSlots: { customRender: 'operation' },
          },
        ],

        list: [],
        action: '',
        fileList: [],
        selectionIds: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        accountTypeList: [
          { code: 1, label: 'ABM' },
          { code: 2, label: 'DT' },
        ],
        accountTypeDict: {
          1: 'ABM',
          2: 'DT',
        },

        modifyTypeList: [
          { code: 0, label: '支出' },
          { code: 1, label: '收益' },
        ],
        applyStatusList: [
          { code: 0, label: '待审核' },
          { code: 1, label: '审核处理中' },
          { code: 2, label: '审核成功' },
          { code: 3, label: '审核拒绝' },
        ],
        accountEntityDict: {
          C0000001: 'HT-境内',
          C0000001_OUT: 'HT-境外',
          C0000002: 'WG-境内',
          C0000002_OUT: 'WG-境外',
          C0000003: 'HOUPU-境内',
          C0000003_OUT: 'HOUPU-境外',
          C0000004: 'ABM_AU-境内',
          C0000004_OUT: 'ABM_AU-境外',
          C0000005: 'ABM_NZ-境内',
          C0000005_OUT: 'ABM_NZ-境外',
          C0000006: 'SG-境内',
          C0000006_OUT: 'SG-境外',
          C0000007: 'LANTERN-境内',
          C0000007_OUT: 'LANTERN-境外',
          C0000008: 'CD-境内',
          C0000008_OUT: 'CD-境外',
        },
        applyStatusDict: {
          0: '待审核',
          1: '审核处理中',
          2: '审核成功',
          3: '审核拒绝',
        },
        applyStatus: '',
        importFileDiolog: false,
        callStatusList: [
          { code: 0, label: '处理成功' },
          { code: 1, label: '处理失败' },
        ],
        callStatusDict: {
          0: '处理成功',
          1: '处理失败',
        },
        callStatus: '',
        selectoptions: [],
        options: {
          loading: false,
          border: true,
          mutiSelect: true,
        },
        showDialog: false,
        batchCheckDialogVisiable: false,
        batchRejectDialogVisiable: false,
        profitTypeJson: {
          6: '转入',
          7: '转出',
          14: '活动奖金入账',
          15: '活动奖金扣除',
        },
        profitTypeList: [
          { code: 6, label: '转入' },
          { code: 7, label: '转出' },
          { code: 14, label: '活动奖金入账' },
          { code: 15, label: '活动奖金扣除' },
        ],
        profitType: '',
        applyId: '',
        params: {
          applyId: '',
          mobilePhone: '',
          status: '',
          formId: '1',
          subjectCode: '',
          rejectMsg: '',
          applyNo: '',
          applyTime: [], // 申请时间
          applyStartTime: null, // 申请开始时间
          applyEndTime: null, // 申请结束时间
        },
        batchIds: [],
        statusList: [], // 发放状态下拉框
        subjectList: [], // 主体下拉框
        tableData: [],
        checkDialogVisiable: false,
        rejectDialogVisiable: false,
        loading: false, // loading表格加载层
        pageNum: 1,
        pageSize: 10,
        total: 0,
        uploadData: {
          appId: 'abmau',
          timeStamp: Date.now(),
        },
        headers: {
          token: getCookie(),
          appCode: process.env.VUE_APP_LOGIN_APP_CODE,
        }, // 导入头部信息
      };
    },
    created() {
      this.params.applyTime = setInitData(30);
      // 上传地址
      this.action =
        window.location.protocol +
        '//' +
        replaceLocalDomain(injectHost().apiHost) +
        '/api/abmio/api/v1.0/upload';
      this.fetch();
    },
    methods: {
      async fetch() {
        let params = { ...this.params };
        params.limit = this.pagination.pageLimit;
        params.pageNo = this.pagination.pageSize;
        this.loading = true;
        try {
          const res = await revisionQueryApply(params);
          if (res) {
            this.list = res.records;
            this.pagination.total = res.total;
            this.loading = false;
          } else {
            this.loading = false;
          }
        } catch (error) {
          this.loading = false;
        }
      },
      async saveForm(data) {
        let params = {
          formId: '1',
          applyJson: data,
          createBy: store.state.user.username,
        };
        const res = await revisionApply(params);
        this.$message.success('操作成功');
        this.showDialog = false;
        this.fetch();
      },
      async checkRevisionApply() {
        const params = {
          formId: '1',
          applyId: this.applyId,
          updateBy: store.state.user.username,
        };
        try {
          const res = await checkRevisionApply(params);
          this.checkDialogVisiable = false;
        } catch (error) {
          this.checkDialogVisiable = false;
        }
        this.fetch();
      },
      async rejectRevisionApply() {
        const params = {
          updateBy: store.state.user.username,
          formId: '1',
          applyId: this.applyId,
          rejectMsg: this.params.rejectMsg,
        };
        try {
          const res = await rejectRevisionApply(params);
          this.rejectDialogVisiable = false;
        } catch (error) {
          this.rejectDialogVisiable = false;
        }
        this.fetch();
      },
      handleRemove(file, fileList) {
        console.log(file, fileList);
      },
      handlePreview(file) {
        console.log(file);
      },
      onBtnImport() {
        this.importFileDiolog = true;
      },
      importFileConfirm() {
        this.$refs.upload.submit();
      },

      async onSuccess(e) {
        if (!e.success) {
          this.$message.error(e.msg || '导入失败');
          return;
        }
        if (e.data && e.data.id) {
          console.log('导入=========' + e.data.id);
          const res = await importCleanUser({ fileId: e.data.id });

          if (!res) {
            this.$message.success('文件已导入，处理中');
            this.importFileDiolog = false;

            this.fileList = [];
            this.fetch();
          }
        }

        // if (!e.success) {
        //   this.$message.error(e.msg || '导入失败');
        //   return;
        // }
        // this.$message.success('导入成功');
        // this.fetch();
      },
      search() {
        this.pagination.pageSize = 1;
        this.fetch();
      },
      addRevision() {
        this.showDialog = true;
      },

      async onExport() {},
      async batchRejectRevisionApply() {
        if (this.selectionIds.length == 0) {
          this.$message.error('请选择需要操作的数据');
          return;
        }
        const params = {
          updateBy: store.state.user.username,
          formId: '1',
          batchIds: this.selectionIds,
          rejectMsg: this.params.rejectMsg,
        };
        const res = batchRejectRevisionApply(params);
        this.batchRejectDialogVisiable = false;
        this.$message.success('审核拒绝完成');
        this.fetch();
      },

      async batchCheckRevisionApply() {
        if (this.selectionIds.length == 0) {
          this.$message.error('请选择需要操作的数据');
          return;
        }
        const params = {
          updateBy: store.state.user.username,
          formId: '1',
          batchIds: this.selectionIds,
        };
        const res = batchCheckRevisionApply(params);
        this.batchCheckDialogVisiable = false;
        this.$message.success('审核拒绝完成');
        this.fetch();
      },

      showAuditDialog(type, applyId) {
        this.applyId = applyId;
        if (type == 'pass') {
          this.checkDialogVisiable = true;
        } else {
          this.rejectDialogVisiable = true;
        }
      },
      batchHandle(type) {
        if (this.selectionIds.length == 0) {
          this.$message.error('请选择需要操作的数据');
          return;
        }
        if (type == 'pass') {
          this.batchCheckDialogVisiable = true;
        } else {
          this.batchRejectDialogVisiable = true;
        }
      },
      handleCheckSelectable(row) {
        return row.applyStatus === 0;
      },
      // 勾选记录
      selectionChange(ids = []) {
        this.selectionIds = ids.map(item => item.id);
      },
      onClose() {
        this.checkDialogVisiable = false;
        this.rejectDialogVisiable = false;
      },
      reset() {
        Object.assign(this.params, this.$options.data.call(this).params);
        this.pagination.pageSize = 1;
        this.pagination.pageLimit = 10;
      },
      handleSizeChange(val) {
        this.pagination.pageSize = 1;
        this.pagination.pageLimit = val;
        this.fetch();
      },
      handleCurrentChange(val) {
        this.pagination.pageSize = val;
        this.fetch();
      },
    },
  };
</script>
<style lang="scss"></style>
