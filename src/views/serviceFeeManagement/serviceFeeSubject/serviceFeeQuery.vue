<template>
  <div>
    <el-form inline>
      <el-form-item label="申请时间:">
        <el-date-picker
          v-model="applicationDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="代理ID:">
        <el-input v-model="searchParams.platform" placeholder="请输入代理ID" />
      </el-form-item>
      <el-form-item label="手机号码:">
        <el-input
          v-model="searchParams.platform"
          placeholder="请输入手机号码"
        />
      </el-form-item>
      <el-form-item label="企业/个体名称:">
        <el-input
          v-model="searchParams.platform"
          placeholder="请输入企业/个体名称"
        />
      </el-form-item>

      <el-form-item label="认证类型:">
        <el-select
          v-model="searchParams.outAccountType"
          clearable
          placeholder="请选择"
        >
          <el-option label="平台子账户" :value="1"></el-option>
          <el-option label="会员子账户" :value="2"></el-option>
          <el-option label="银行账户" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发放状态:">
        <el-select
          v-model="searchParams.inAccountType"
          clearable
          placeholder="请选择"
        >
          <el-option label="平台子账户" :value="1"></el-option>
          <el-option label="会员子账户" :value="2"></el-option>
          <el-option label="银行账户" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发票类型:">
        <el-select
          v-model="searchParams.tradeType"
          clearable
          placeholder="请选择"
        >
          <el-option label="待审核" :value="1"></el-option>
          <el-option label="审核通过" :value="2"></el-option>
          <el-option label="审核不通过" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="主体:">
        <el-select
          v-model="searchParams.applyFrom"
          clearable
          placeholder="请选择"
        >
          <el-option label="一般转账" :value="1"></el-option>
          <el-option label="厦门资金链路转账" :value="2"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button type="primary" @click="onExport">主体导出</el-button>
        <el-button type="primary" @click="handleClick(1)">批量打款</el-button>
        <el-button type="primary" @click="handleClick(2)">全量打款</el-button>
        <el-button type="primary" @click="handleClick(3)">重新发放</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
      @selection-change="selectionChange"
    ></dynamictable>
    <makeMoneyModal v-model="showModal" :type="type"></makeMoneyModal>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import makeMoneyModal from './components/makeMoneyModal';

  import { debounce } from '@/utils';
  import {
    chargeDocItemsList,
    chargeDocList,
    creditAndPaymentDocCompositeList,
  } from '@/api/documentCenter';

  export default {
    components: {
      dynamictable,
      makeMoneyModal,
    },

    data() {
      return {
        showModal: false,
        applicationDate: '',
        type: 1,
        selectionIds: [],
        searchParams: {
          outAccountType: '',
          inAccountType: '',
          tradeType: '',
          applyFrom: '',
          reviewStatus: '',
        },
        list: [{ a: '222' }],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          mutiSelect: true,
        },
      };
    },
    created() {
      this.getList(true);
    },
    methods: {
      handleClick(type) {
        if ([1, 2].includes(type)) {
          const { selectionIds } = this;
          if (selectionIds.length === 0) {
            this.$message.error('请选择数据');
            return;
          }
          this.showModal = true;
          this.type = type;
        }
        if (type === 3) {
          this.$alert('已请求重新发放，请返回页面查看发放结果', {
            confirmButtonText: '确定',
            callback: action => {
              this.$message({
                type: 'info',
                message: `action: ${action}`,
              });
            },
          });
        }
      },
      selectionChange(ids = []) {
        this.selectionIds = ids.map(item => item.id);
      },
      onExport: debounce(function () {}, 1000),
      handleTransfer: debounce(function (row) {
        chargeDocItemsList({ id: row.id }).then(res => {
          this.$message.error('转帐成功');
        });
      }, 1000),
      getParams() {
        const applicationDate = this.applicationDate;
        this.searchParams.applyStartTime = applicationDate
          ? parseTime(applicationDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.applyEndTime = applicationDate
          ? parseTime(applicationDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        const params = {
          ...this.searchParams,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        try {
          const res = await chargeDocItemsList(params);
          this.options.loading = false;
          this.list = res ? res.list : [];
          this.pagination.total = res ? res.total : 0;
        } catch (error) {
          this.options.loading = false;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },
      getColumns() {
        const columns = [
          {
            prop: 'id',
            label: '序号',
          },
          {
            prop: 'applyTime',
            label: '所属周期',
          },
          {
            prop: 'outAccountType',
            label: '代理ID',
          },
          {
            prop: 'outAccountName',
            label: '代理姓名',
          },
          {
            prop: 'outAccount',
            label: '手机号',
          },
          {
            prop: 'inAccountType',
            label: '平台',
          },
          {
            prop: 'inAccountName',
            label: '主体',
          },
          {
            prop: 'inAccount',
            label: '企业/个体名称',
          },
          {
            prop: 'tradeType',
            label: '社会信用代码/税号',
          },
          {
            prop: 'amount',
            label: '户名',
          },
          {
            prop: 'remark',
            label: '银行卡号',
          },
          {
            prop: 'reviewStatusDesc',
            label: '开户行',
          },
          {
            prop: 'reviewStatusDesc',
            label: '申请金额',
          },
          {
            prop: 'reviewStatusDesc',
            label: '发票类型',
          },
          {
            prop: 'reviewStatusDesc',
            label: '申请时间',
          },
          {
            prop: 'reviewStatusDesc',
            label: '状态',
          },
          {
            prop: 'reviewStatusDesc',
            label: '更新时间',
          },
          {
            prop: 'reviewStatusDesc',
            label: '操作人',
          },
        ];
        return columns;
      },
    },
  };
</script>
<style lang="scss"></style>
