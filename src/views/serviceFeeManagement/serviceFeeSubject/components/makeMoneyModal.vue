<template>
  <div>
    <el-dialog
      width="500px"
      title="供应商签约"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="saveForm"
        :rules="rulesForm"
        label-width="140px"
      >
        <template v-if="type === 1">
          <el-form-item label-width="0">
            本次交易共选择10条记录，金额合计2.2222
          </el-form-item>
          <el-form-item label="ABM渠道账户余额: ">22</el-form-item>
          <el-form-item label="DT渠道账户余额: ">333</el-form-item>
        </template>
        <template v-else>
          <el-form-item label="提现周期" prop="applyDateTime">
            <el-date-picker
              v-model="saveForm.applyDateTime"
              type="date"
              value-format="yyyy-MM-dd "
              format="yyyy-MM-dd "
              placeholder="选择日期"
              style="width: 240px"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="合作方式" prop="legalGender">
            <el-select
              v-model="saveForm.legalGender"
              style="width: 240px"
              placeholder="请选择"
            >
              <el-option
                v-for="item in sexArray"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="onOK">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { debounce } from '@/utils';
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      currentRow: {
        type: Object,
        default: null,
      },
      type: {
        type: Number,
        default: 1,
      },
    },
    data() {
      return {
        rulesForm: {
          applyDateTime: [
            { required: true, message: '提现周期不能为空', trigger: 'blur' },
          ],
          legalGender: [
            {
              required: true,
              message: '合作方式不能为空',
              trigger: 'blur',
            },
          ],
        },
        sexArray: [
          { value: 'M', label: '男' },
          { value: 'F', label: '女' },
        ],
        saveForm: {},
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(val) {
        if (val) {
        } else {
          Object.assign(this.$data.saveForm, this.$options.data().saveForm);
          this.$nextTick(function () {
            this.$refs.formData.clearValidate();
          });
        }
      },
    },
    created() {},
    methods: {
      onOK: debounce(function () {
        this.$refs.formData.validate(async valid => {
          if (!valid) return;

          //   const saveApi = this.saveForm.id
          //     ? templateConfigUpdate
          //     : templateConfigCreate;

          //   saveApi({
          //     ...this.saveForm,
          //   }).then(res => {
          //     this.$message.success(this.saveForm.id ? '修改成功' : '添加成功');
          //     this.showDialog = false;
          //     this.$emit('onGet');
          //   });
        });
      }, 800),
    },
  };
</script>

<style></style>
