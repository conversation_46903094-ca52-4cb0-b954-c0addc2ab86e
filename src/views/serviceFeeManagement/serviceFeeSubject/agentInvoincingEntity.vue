<!--
 * @Description: 
 * @Author: 王嘉丽
 * @Date: 2021-12-07 19:28:23
 * @LastEditors: xuxiang
 * @LastEditTime: 2022-05-19 17:09:21
 * @FilePath: /access-fmis-web/src/views/serviceFeeManagement/serviceFeeSubject/agentInvoincingEntity.vue
-->
<template>
  <div>
    <el-form :inline="true" :model="params">
      <el-form-item label="所属周期:">
        <el-date-picker
          v-model="params.payCycle"
          type="month"
          value-format="yyyy-MM"
          placeholder="请选择所属周期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="代理ID:">
        <el-input v-model="params.idCode" placeholder="请输入代理ID" />
      </el-form-item>
      <el-form-item label="手机号码:">
        <el-input v-model="params.mobilePhone" placeholder="请输入手机号码" />
      </el-form-item>
      <el-form-item label="合作方式:">
        <el-select v-model="params.authType" clearable placeholder="请选择">
          <el-option
            v-for="(item, index) in authTypeList"
            :key="index"
            :label="item.desc"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发放状态:">
        <el-select v-model="params.status" clearable placeholder="请选择">
          <el-option
            v-for="(item, index) in statusList"
            :key="index"
            :label="item.desc"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="乐税开户状态:">
        <el-select v-model="params.letaxLogout" clearable placeholder="请选择">
          <el-option
            v-for="(item, index) in letaxLogoutList"
            :key="index"
            :label="item.desc"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="主体:">
        <el-select v-model="params.subjectCode" clearable placeholder="请选择">
          <el-option
            v-for="(item, index) in subjectList"
            :key="index"
            :label="item.desc"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <ac-permission-button
          btn-text="查询"
          permission-key="agentInvoincingEntity-search"
          @click="search(true)"
        ></ac-permission-button>
        <!-- <el-button type="primary" @click="search(true)">查询</el-button> -->
        <el-button type="primary" @click="reset">重置</el-button>
        <ac-permission-button
          btn-text="主体导出"
          permission-key="agentInvoincingEntity-export"
          @click="onExport"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column
        align="center"
        type="index"
        label="序号"
        width="55"
        fixed
      ></el-table-column>
      <el-table-column
        align="center"
        prop="applyNo"
        label="申请单号"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="payCycle"
        label="所属周期"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="idCode"
        label="代理ID"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="userName"
        label="代理姓名"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="mobilePhone"
        label="手机号"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="platform"
        label="平台"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="subjectName"
        label="主体"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="authTypeDesc"
        label="合作方式"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="name"
        label="户名"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="bankCardNo"
        label="银行卡号"
        min-width="150"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="bankName"
        label="开户行"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="idCardNo"
        label="身份证号"
        min-width="150"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="merchantName"
        label="个体工商名称"
        min-width="150"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="taxNo"
        label="税号"
        min-width="150"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="applyAmount"
        label="申请金额"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="payAmount"
        label="发放金额"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="feeRatio"
        label="手续费率"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="feeAmount"
        label="手续费"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="statusDesc"
        label="状态"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="remark"
        label="备注"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="letaxLogoutDesc"
        label="乐税开户状态"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="updateTime"
        label="更新时间"
        min-width="160"
      ></el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page="pageNum"
      :page-size="pageSize"
      :page-sizes="[10, 20, 30, 50, 100]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>
<script>
  import { getWithdrawalCycle } from '@/utils';
  import {
    getAgentInvoincingEntityList,
    getAgentInvoincingEntityEnums,
    exportAgentInvoincingEntityList,
  } from '@/api/serviceFeeManagement';

  export default {
    data() {
      return {
        params: {
          idCode: null, // 代理ID
          mobilePhone: null, // 手机号码
          authType: null, // 合作方式
          status: null, // 发放状态
          letaxLogout: null, // 乐税开户状态
          subjectCode: null, // 主体
          payCycle: getWithdrawalCycle(),
          // new Date(new Date().toLocaleDateString()).getFullYear() +
          // '-' +
          // (new Date(new Date().toLocaleDateString()).getMonth() + 1), // 所属周期默认当前月
        },
        authTypeList: [], // 合作方式下拉框
        statusList: [], // 发放状态下拉框
        letaxLogoutList: [], // 乐税开户下拉框
        subjectList: [], // 主体下拉框
        tableData: [],
        loading: false, // loading表格加载层
        pageNum: 1,
        pageSize: 10,
        total: 0,
      };
    },
    created() {
      this.getEnums();
      this.fetch();
    },
    methods: {
      async getEnums() {
        const res = await getAgentInvoincingEntityEnums();
        if (res) {
          this.authTypeList = res.authTypeList || [];
          this.statusList = res.statusList || [];
          this.letaxLogoutList = res.letaxLogoutList || [];
          this.subjectList = res.subjectList || [];
        }
      },
      async fetch() {
        let params = { ...this.params };
        params.size = this.pageSize;
        params.current = this.pageNum;
        if (!params.payCycle && !params.idCode && !params.mobilePhone) {
          this.$message.error('用户ID&电话号码&周期必须要选择一个');
          return;
        }
        this.loading = true;
        try {
          const res = await getAgentInvoincingEntityList(params);
          if (res) {
            this.tableData = res.records;
            this.total = res.total;
            this.loading = false;
          } else {
            this.loading = false;
          }
        } catch (error) {
          this.loading = false;
        }
      },
      search() {
        this.pageNum = 1;
        this.fetch();
      },
      async onExport() {
        console.log('导出');
        try {
          console.log(111, this.params.payCycle);
          const params = {
            ...this.params,
            taskContent: '服务费管理-服务费主体管理-代开票主体查询-主体导出',
          };
          const res = await exportAgentInvoincingEntityList(params);
          this.$message.success('任务正在处理中，请稍后到任务中心查看');
        } catch (error) {}
      },
      reset() {
        Object.assign(this.params, this.$options.data.call(this).params);
        this.pageNum = 1;
        this.pageSize = 10;
      },
      handleSizeChange(val) {
        this.pageNum = 1;
        this.pageSize = val;
        this.fetch();
      },
      handleCurrentChange(val) {
        this.pageNum = val;
        this.fetch();
      },
    },
  };
</script>
<style lang="scss"></style>
