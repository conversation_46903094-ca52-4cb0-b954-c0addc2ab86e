<!--
 * @Description: 
 * @Author: 王嘉丽
 * @Date: 2021-12-07 19:29:43
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-07-22 13:58:12
 * @FilePath: /access-fmis-web/src/views/serviceFeeManagement/taskCenter/taskCenterPage.vue
-->
<template>
  <div>
    <el-form :inline="true" :model="params">
      <el-form-item label="任务时间:">
        <el-date-picker
          v-model="params.createTime"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="任务编号:">
        <el-input v-model="params.taskNo" placeholder="请输入任务编号" />
      </el-form-item>
      <el-form-item label="任务状态:">
        <el-select v-model="params.status" clearable placeholder="请选择">
          <el-option
            v-for="(item, index) in statusList"
            :key="index"
            :label="item.desc"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search(true)">查询</el-button>
        <el-button type="primary" @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column
        align="center"
        prop="createTime"
        label="创建时间"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="taskNo"
        label="任务编号"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="taskContent"
        label="任务标识"
        min-width="150"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="statusDesc"
        label="任务状态"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="remark"
        label="描述"
        min-width="150"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="operator"
        label="申请人"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="updateTime"
        label="更新时间"
        min-width="120"
      ></el-table-column>
      <el-table-column align="center" label="操作" min-width="100">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.taskFileId !== '0'"
            type="text"
            @click="download(scope.row)"
          >
            下载
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page="pageNum"
      :page-size="pageSize"
      :page-sizes="[10, 20, 30, 50, 100]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>
<script>
  import { setInitData } from '@/utils';
  import {
    getTaskCenterList,
    getTaskCenterEnums,
    downloadTaskCenter,
  } from '@/api/serviceFeeManagement';

  export default {
    data() {
      return {
        params: {
          createTime: [], // 任务时间
          createStartTime: null, // 任务开始时间
          createEndTime: null, // 任务结束时间
          taskNo: null, // 任务编号
          status: null, // 任务状态
          platformCode: 'ABM',
        },
        statusList: [], // 任务状态下拉框
        tableData: [],
        loading: false, // loading表格加载层
        pageNum: 1,
        pageSize: 10,
        total: 0,
      };
    },
    created() {
      this.params.createTime = setInitData(30);
      this.getEnums();
      this.fetch();
    },
    methods: {
      async getEnums() {
        const res = await getTaskCenterEnums();
        if (res) {
          this.statusList = res.statusList || [];
        }
      },
      async fetch() {
        if (this.params.createTime && this.params.createTime.length > 0) {
          this.params.createStartTime = this.params.createTime[0];
          this.params.createEndTime = this.params.createTime[1];
        } else {
          this.params.createStartTime = null;
          this.params.createEndTime = null;
        }
        let params = { ...this.params };
        params.size = this.pageSize;
        params.current = this.pageNum;
        delete params.createTime;
        this.loading = true;
        try {
          const res = await getTaskCenterList(params);
          if (res) {
            this.tableData = res.records;
            this.total = res.total;
            this.loading = false;
          } else {
            this.loading = false;
          }
        } catch (error) {
          this.loading = false;
        }
      },
      async download(row) {
        try {
          let url = '';
          const res = await downloadTaskCenter({}, row.taskNo);
          url = res || '';
          if (url) {
            window.open(url, '_blank');
          }
        } catch (error) {}
      },
      search() {
        this.pageNum = 1;
        this.fetch();
      },
      reset() {
        Object.assign(this.params, this.$options.data.call(this).params);
        this.pageNum = 1;
        this.pageSize = 10;
        this.params.createTime = setInitData(30);
        // this.fetch();
      },
      handleSizeChange(val) {
        this.pageNum = 1;
        this.pageSize = val;
        this.fetch();
      },
      handleCurrentChange(val) {
        this.pageNum = val;
        this.fetch();
      },
    },
  };
</script>
<style lang="scss"></style>
