<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-27 16:30:38
 * @LastEditTime: 2022-06-15 10:55:10
 * @LastEditors: xuxiang
 * @Reference: 
-->
<template>
  <div>
    <el-form :inline="true" :model="params">
      <el-form-item label="代理ID:">
        <el-input v-model="params.idCode" placeholder="请输入代理ID" />
      </el-form-item>
      <el-form-item label="手机号码:">
        <el-input v-model="params.mobilePhone" placeholder="请输入手机号码" />
      </el-form-item>
      <el-form-item label="业务类型:">
        <el-select v-model="params.bizType" clearable placeholder="请选择">
          <el-option
            v-for="(item, index) in bizTypeList"
            :key="index"
            :label="item.desc"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间:">
        <el-date-picker
          v-model="params.detailTime"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <ac-permission-button
          btn-text="查询"
          permission-key=""
          @click="search(true)"
        ></ac-permission-button>
        <el-button type="primary" @click="reset">重置</el-button>
        <ac-permission-button
          btn-text="导出"
          permission-key=""
          @click="onExport"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column
        align="center"
        prop="idCode"
        label="代理ID"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="platform"
        label="平台"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="bizTypeDesc"
        label="业务类型"
        min-width="200"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="accountTypeDesc"
        label="账户类型"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="amount"
        label="发生金额"
        min-width="100"
      ></el-table-column>

      <el-table-column
        align="center"
        prop="afterAmount"
        label="余额"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="bizDesc"
        label="业务描述"
        min-width="150"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="createTime"
        label="创建时间"
        min-width="160"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="otherInfo"
        label="其它信息"
        min-width="250"
      ></el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page="pageNum"
      :page-size="pageSize"
      :page-sizes="[10, 20, 30, 50, 100]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>
<script>
  import { setInitData, downloadFile } from '@/utils';
  import { newExportExcel } from '@/api/blob';
  import {
    getFrozenAccountComboBoxData,
    getFrozenAccountDetailList,
  } from '@/api/serviceFeeManagement';

  export default {
    data() {
      return {
        params: {
          idCode: '',
          mobilePhone: '',
          bizType: '',
          platformCode: '',
          detailTime: null,
          detailStartTime: null, // 流水开始时间
          detailEndTime: null, // 流水结束时间
        },
        bizTypeList: [], // 发放状态下拉框
        tableData: [],
        loading: false, // loading表格加载层
        pageNum: 1,
        pageSize: 10,
        total: 0,
      };
    },
    created() {
      this.params.detailTime = setInitData(90);
      this.initParams();
      this.getEnums();
    },
    methods: {
      initParams() {
        if (!this.$route.query) {
          return;
        }
        const { idCode = '', platformCode = '' } = this.$route.query;
        this.params.idCode = idCode;
        this.params.platformCode = platformCode;
        if (idCode || platformCode) {
          this.fetch();
        }
      },
      async getEnums() {
        const res = await getFrozenAccountComboBoxData();
        if (res) {
          this.bizTypeList = res.bizTypeList || [];
        }
      },
      async fetch() {
        if (this.params.detailTime && this.params.detailTime.length > 0) {
          this.params.detailStartTime = this.params.detailTime[0];
          this.params.detailEndTime = this.params.detailTime[1];
        } else {
          this.params.detailStartTime = null;
          this.params.detailEndTime = null;
        }
        let params = { ...this.params };
        params.size = this.pageSize;
        params.current = this.pageNum;
        delete params.detailTime;
        if (!params.mobilePhone && !params.idCode) {
          this.$message.error('用户ID和电话号码不能同时为空');
          return;
        }
        if (!params.detailStartTime || !params.detailEndTime) {
          this.$message.error('时间不能为空');
          return;
        }
        this.loading = true;
        try {
          const res = await getFrozenAccountDetailList(params);
          if (res) {
            this.tableData = res.records;
            this.total = res.total;
            this.loading = false;
          } else {
            this.loading = false;
          }
        } catch (error) {
          this.loading = false;
        }
      },
      search() {
        this.pageNum = 1;
        this.params.platformCode = 'ABM';
        this.fetch();
      },
      reset() {
        Object.assign(this.params, this.$options.data.call(this).params);
        this.pageNum = 1;
        this.pageSize = 10;
        this.params.detailTime = setInitData(90);
      },
      async onExport() {
        if (this.params.detailTime && this.params.detailTime.length > 0) {
          this.params.detailStartTime = this.params.detailTime[0];
          this.params.detailEndTime = this.params.detailTime[1];
        } else {
          this.params.detailStartTime = null;
          this.params.detailEndTime = null;
        }
        let data = { ...this.params };
        data.size = this.pageSize;
        data.current = this.pageNum;
        delete data.detailTime;
        if (!data.mobilePhone && !data.idCode) {
          this.$message.error('用户ID和电话号码不能同时为空');
          return;
        }
        if (!data.detailStartTime || !data.detailEndTime) {
          this.$message.error('时间不能为空');
          return;
        }
        data.platformCode = 'ABM';
        data.accountCategory = 'frozenAccount';
        const params = { ...data };
        newExportExcel(
          params,
          '/api/financial-account/accountManage/accountDetailByCategoryExport',
          'post',
        ).then(res => {
          downloadFile(res.data, '冻结户流水导出结果');
        });
      },
      handleSizeChange(val) {
        this.pageNum = 1;
        this.pageSize = val;
        this.fetch();
      },
      handleCurrentChange(val) {
        this.pageNum = val;
        this.fetch();
      },
    },
  };
</script>
<style lang="scss"></style>
