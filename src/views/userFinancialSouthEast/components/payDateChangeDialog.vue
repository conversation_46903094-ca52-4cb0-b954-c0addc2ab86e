<!--
 * @Author: 王赛军
 * @Date: 2024-12-24 14:36:14
 * @LastEditors: 王赛军
 * @Description: file content
 * @FilePath: /access-fmis-web/src/views/userFinancialSouthEast/components/payDateChangeDialog.vue
-->
<template>
  <div>
    <el-dialog
      width="800px"
      title="编辑打款时间"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="saveForm"
        :rules="rulesForm"
        label-width="200px"
      >
        <el-form-item label="打款时间: " :required="false" prop="remark">
          <el-date-picker
            v-model="saveForm.payDate"
            type="datetime"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
          ></el-date-picker>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="onOK">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { updateOverseasPayTax } from '@/api/serviceFeeManagement';
  import { debounce, initSearchParams } from '@/utils';

  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      currentRow: {
        type: Object,
        default: null,
      },
      routerType: {
        type: String,
        default: '',
      },
      remarkList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        rulesForm: {
          borderType: [
            {
              required: true,
              message: '请选择需要变更的打款渠道',
              trigger: 'blur',
            },
          ],
          status: [
            {
              required: true,
              message: '请选择需要变更的状态类型',
              trigger: 'blur',
            },
          ],
          remark: [
            {
              required: true,
              message: '请输入备注',
              trigger: 'blur',
              validator: (rule, value, callback) => {
                let err;
                // 门户成员时必须选择成员名称
                if (this.saveForm.status === 'failure' && !value) {
                  err = new Error('请输入备注');
                }
                callback(err);
              },
            },
          ],
        },
        saveForm: {
          applyNo: '',
          remark: '',
          status: '',
          borderType: '',
          payDate: '',
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(val) {
        if (val) {
          if (this.currentRow) {
            this.saveForm.applyNo = this.currentRow.applyNo;
          }
        } else {
          Object.assign(this.$data.saveForm, this.$options.data().saveForm);
          this.$nextTick(function () {
            this.$refs.formData.clearValidate();
          });
        }
      },
    },
    created() {},
    methods: {
      onOK: debounce(function () {
        this.$refs.formData.validate(async valid => {
          if (!valid) return;
          const api = updateOverseasPayTax;

          api({
            ...initSearchParams(this.saveForm),
            operator: this.$store.state.user.userInfo.id,
          }).then(res => {
            this.$message.success('编辑成功');
            this.showDialog = false;
            this.$emit('onGet');
          });
        });
      }, 800),
    },
  };
</script>

<style></style>
