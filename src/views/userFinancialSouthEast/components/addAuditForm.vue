<!--
 * @Description: 
 * @Author: 陈雪磊
 * @Date: 2021-12-18 16:45:35
 * @LastEditTime: 2024-06-18 18:25:19
 * @LastEditors: Please set LastEditors
 * @Reference: 
-->
<template>
  <el-dialog :visible.sync="showDialog" title="新增订正申请" destroy-on-close>
    <div class="saleSubjectDialog">
      <el-form ref="saveParams" :model="saveParams" label-width="120px">
        <el-form-item
          v-if="['add'].includes(type)"
          prop="idCode"
          :rules="[
            {
              required: true,
              message: '请输入代理id',
              trigger: 'change',
            },
          ]"
          label="代理Id:"
        >
          <el-input v-model="saveParams.idCode" placeholder="请输入代理id" />
        </el-form-item>

        <el-form-item
          label="账户类型:"
          prop="accountType"
          :rules="[
            {
              required: true,
              message: '请选择账户类型',
              trigger: 'change',
            },
          ]"
        >
          <el-select
            v-model="saveParams.accountType"
            placeholder="请选择账户类型"
          >
            <el-option
              v-for="item in accountTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          label="发放收益主体"
          prop="accountEntityCode"
          :rules="[
            {
              required: true,
              message: '请选择发放收益主体',
              trigger: 'change',
            },
          ]"
        >
          <el-select
            v-model="saveParams.accountEntityCode"
            placeholder="请选择发放收益主体"
          >
            <el-option
              v-for="(item, index) in accountEntityCodeList"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          label="支出收益类型"
          prop="profitType"
          :rules="[
            {
              required: true,
              message: '请选择业务类型',
              trigger: 'change',
            },
          ]"
        >
          <el-select
            v-model="saveParams.profitType"
            placeholder="请选择业务类型"
          >
            <el-option
              v-for="(item, index) in profitTypeList"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="['add'].includes(type)"
          label="修复金额"
          prop="money"
          :rules="[
            {
              required: true,
              message: '请填写修复金额',
              trigger: 'change',
            },
            {
              validator: rules.validateAmount,
              trigger: 'change',
            },
          ]"
        >
          <el-input v-model="saveParams.money">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item
          v-if="['add'].includes(type)"
          prop="originCurrency"
          label="源交易币种:"
          :rules="[
            {
              required: true,
              message: '请输入源交易币种',
              trigger: 'change',
            },
          ]"
        >
          <el-input
            v-model="saveParams.originCurrency"
            placeholder="支出收益描述"
          />
        </el-form-item>
        <el-form-item
          v-if="['add'].includes(type)"
          label="源收益金额"
          prop="originAmount"
          :rules="[
            {
              required: true,
              message: '请填写源收益金额',
              trigger: 'change',
            },
            {
              validator: rules.validateAmount,
              trigger: 'change',
            },
          ]"
        >
          <el-input v-model="saveParams.originAmount">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item
          v-if="['add'].includes(type)"
          prop="profitDes"
          label="支出收益描述:"
          :rules="[
            {
              required: true,
              message: '请输入支出收益描述',
              trigger: 'change',
            },
          ]"
        >
          <el-input v-model="saveParams.profitDes" placeholder="支出收益描述" />
        </el-form-item>
        <el-form-item
          v-if="['import'].includes(type)"
          required
          :error="errorTip"
          label="订正数据导入:"
        >
          <el-upload
            ref="upload"
            drag
            :action="action"
            :headers="headers"
            :data="uploadData"
            accept=".xlsx, .xls"
            :auto-upload="false"
            style="text-align: center"
            :before-upload="beforeUpload"
            :on-success="onSuccess"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              <p>将文件拖到此处，或点击上传，支持扩展名：.xlsx, .xls</p>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item v-if="['add'].includes(type)" style="margin-left: 20px">
          <el-button @click="resetForm">取消</el-button>
          <el-button type="primary" @click="saveForm('saveParams')">
            保存
          </el-button>
        </el-form-item>
        <el-form-item
          v-if="['import'].includes(type)"
          style="margin-left: 20px"
        >
          <el-button @click="resetForm">取消</el-button>
          <el-button type="primary" @click="handleOK">保存</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-dialog>
</template>

<script>
  import { tenBitTimestamp } from '@/utils';
  import uploadImg from '@/components/uploadImg';
  import { debounce } from '@/utils';
  import { getCookie } from '@/utils/auth';
  import injectHost from '@/utils/injectHost';
  import { replaceLocalDomain } from '@/utils/index.js';
  export default {
    components: {},
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: Boolean,
      list: {
        type: Array,
        default: () => {},
      },
      type: {
        type: String,
        default: '',
      },
      uploadUrl: {
        type: String,
        default: '/api/abmio/api/v1.0/upload',
      },
      uploadData: {
        type: Object,
        default: () => {
          return {
            appId: 'abmau',
            timeStamp: Date.now(),
          };
        },
      },
      formData: {
        type: Object,
        default: () => {
          return {};
        },
      },
    },
    data() {
      //这里是自定义的开始时间规则
      var begindateRule = (rule, value, callback) => {
        if (!this.saveParams.status || this.saveParams.status == 0) {
          if (!value || Date.parse(value) > Date.parse(tenBitTimestamp())) {
            callback();
          } else {
            return callback(new Error('开始时间必须大于当前时间'));
          }
        } else {
          callback();
        }
      };

      const validateBsb = (rule, value, callback) => {
        const str = value.replace(/\s/g, '');
        if (str.length !== 6 && value) {
          callback(new Error('bsb长度必须为6位'));
        } else {
          callback();
        }
      };
      const validateAmount = (rule, value, callback) => {
        const reg = /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/;
        if (value && (!reg.test(value) || value == 0)) {
          callback(new Error('金额不得为空且金额需大于0.01'));
        } else {
          callback();
        }
      };

      const validateNumber = (rule, value, callback) => {
        if (value >= 0) {
          callback();
        } else {
          callback(new Error('填写金额有误！'));
        }
      };

      //这里是自定义的失效时间规则
      var enddateRule = (rule, value, callback) => {
        if (this.saveParams.beginTime && value) {
          if (Date.parse(this.saveParams.beginTime) < Date.parse(value)) {
            callback();
          } else {
            return callback(new Error('失效时间必须大于开始时间'));
          }
        } else {
          callback();
        }
      };
      return {
        headers: {
          token: getCookie(),
          appCode: process.env.VUE_APP_LOGIN_APP_CODE,
        }, // 导入头部信息
        action: '',
        rules: {
          validateBsb,
          validateAmount,
          validateNumber,
        },
        loading: false,
        initFileList: [],
        errorTip: '',
        saveParams: this.formData,
        accountTypeList: [
          {
            label: 'WLZ',
            value: '3',
          },
        ],
        accountEntityCodeList: [
          { value: 'Z0000001', label: 'WLZ-MY' },
          { value: 'Z0000002', label: 'WLZ-SG' },
        ],
        profitTypeList: [
          { value: 6, label: '转入' },
          { value: 7, label: '转出' },
          { value: 14, label: '活动奖金入账' },
          { value: 15, label: '活动奖金扣除' },
        ],
        rulesForm: {
          idCode: [
            { required: true, message: '代理id不能为空', trigger: 'change' },
          ],
          accountType: [
            { required: true, message: '账户类型不能为空', trigger: 'change' },
          ],
          accountEntityCode: [
            { required: true, message: '收益主体不能为空', trigger: 'change' },
          ],
          profitType: [
            {
              required: true,
              message: '支出收益类型不能为空',
              trigger: 'change',
            },
          ],
          money: [
            { required: true, message: '金额不能为空', trigger: 'change' },
          ],
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      formData: {
        handler(newObject, oldObject) {
          if (newObject) {
            this.saveParams = newObject;
          }
        },
        deep: true,
        immediate: true,
      },
    },
    created() {
      // headers加参数
      // 上传地址
      this.action =
        window.location.protocol +
        '//' +
        replaceLocalDomain(injectHost().apiHost) +
        this.uploadUrl;
    },
    mounted() {
      this.$nextTick(() => {
        this.$on('initData', e => {
          this.$refs.saveParams.resetFields();
          this.saveParams = e;
        });
      });
    },
    methods: {
      beforeUpload() {
        this.loading = true;
      },
      onSuccess(e, files) {
        if (!e.success) {
          this.$message.error(e.msg || '导入失败');
          this.loading = false;

          return;
        }
        // if (e.data && !e.data.importSuccess) {
        //   this.$message.error('excel文件错误');
        //   const uid = files.uid;

        //   const idx = this.$refs.upload.uploadFiles.findIndex(
        //     item => item.uid === uid,
        //   );
        //   this.$refs.upload.uploadFiles.splice(idx, 1); // 去除文件列表失败文件
        //   this.$emit('onErrDownload', e.data.errorFileId);
        //   return;
        // }
        const done = () => {
          this.$message.success('导入成功');

          this.loading = false;
        };
        const err = () => {
          this.loading = false;
        };
        this.$emit('importForm', e.data, this.saveParams);
        this.saveParams = {};
        this.showDialog = false;
      },

      saveForm(formName) {
        this.$refs[formName].validate(valid => {
          if (valid) {
            this.$emit('saveForm', JSON.parse(JSON.stringify(this.saveParams)));
            this.saveParams = {};
            this.showDialog = false;
          }
        });
      },
      handleOK: debounce(function () {
        this.$refs.upload.submit();
      }, 1000),

      resetForm() {
        this.saveParams = {};
        this.$emit('close');
      },
    },
  };
</script>

<style lang="scss" scoped>
  .saleSubjectDialog {
    .subTitle {
      font-weight: bold;
      font-size: 16px !important;
    }
    .timeItem {
      /deep/.el-form-item__label:before {
        content: '' !important;
      }
    }
    .el-select,
    .el-date-editor {
      width: 280px;
    }
  }
</style>
