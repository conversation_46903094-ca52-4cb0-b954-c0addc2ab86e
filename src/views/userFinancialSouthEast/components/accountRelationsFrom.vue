<!--
 * @Description: 
 * @Author: 陈雪磊
 * @Date: 2021-12-18 16:45:35
 * @LastEditTime: 2025-02-17 14:01:01
 * @LastEditors: Please set LastEditors
 * @Reference: 
-->
<template>
  <el-dialog :visible.sync="showDialog" title="新增关系" destroy-on-close>
    <div class="saleSubjectDialog">
      <el-form ref="saveParams" :model="saveParams" label-width="120px">
        <el-form-item
          prop="originalIdCode"
          :rules="[
            {
              required: true,
              message: '请输入原账号id',
              trigger: 'change',
            },
          ]"
          label="原账号idCode:"
        >
          <el-input
            v-model="saveParams.originalIdCode"
            placeholder="请输入原账号id"
          />
        </el-form-item>

        <el-form-item
          prop="newIdCode"
          :rules="[
            {
              required: true,
              message: '请输入新账号id',
              trigger: 'change',
            },
          ]"
          label="新账号idCode:"
        >
          <el-input
            v-model="saveParams.newIdCode"
            placeholder="请输入新账号id"
          />
        </el-form-item>

        <el-form-item v-if="['add'].includes(type)" style="margin-left: 20px">
          <el-button @click="resetForm">取消</el-button>
          <el-button type="primary" @click="saveForm('saveParams')">
            保存
          </el-button>
        </el-form-item>
        <el-form-item
          v-if="['import'].includes(type)"
          style="margin-left: 20px"
        >
          <el-button @click="resetForm">取消</el-button>
          <el-button type="primary" @click="handleOK">保存</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-dialog>
</template>

<script>
  import { tenBitTimestamp } from '@/utils';
  import { debounce } from '@/utils';
  import { getCookie } from '@/utils/auth';
  import injectHost from '@/utils/injectHost';
  import { replaceLocalDomain } from '@/utils/index.js';
  export default {
    components: {},
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: Boolean,
      list: {
        type: Array,
        default: () => {},
      },
      type: {
        type: String,
        default: '',
      },
      uploadUrl: {
        type: String,
        default: '/api/abmio/api/v1.0/upload',
      },
      uploadData: {
        type: Object,
        default: () => {
          return {
            appId: 'abmau',
            timeStamp: Date.now(),
          };
        },
      },
      formData: {
        type: Object,
        default: () => {
          return {};
        },
      },
    },
    data() {
      //这里是自定义的开始时间规则

      const validateBsb = (rule, value, callback) => {
        const str = value.replace(/\s/g, '');
        if (str.length !== 6 && value) {
          callback(new Error('bsb长度必须为6位'));
        } else {
          callback();
        }
      };
      const validateAmount = (rule, value, callback) => {
        const reg = /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/;
        if (value && (!reg.test(value) || value == 0)) {
          callback(new Error('金额不得为空且金额需大于0.01'));
        } else {
          callback();
        }
      };

      const validateNumber = (rule, value, callback) => {
        if (value >= 0) {
          callback();
        } else {
          callback(new Error('填写金额有误！'));
        }
      };

      //这里是自定义的失效时间规则
      var enddateRule = (rule, value, callback) => {
        if (this.saveParams.beginTime && value) {
          if (Date.parse(this.saveParams.beginTime) < Date.parse(value)) {
            callback();
          } else {
            return callback(new Error('失效时间必须大于开始时间'));
          }
        } else {
          callback();
        }
      };
      return {
        headers: {
          token: getCookie(),
          appCode: process.env.VUE_APP_LOGIN_APP_CODE,
        }, // 导入头部信息
        action: '',
        rules: {
          validateBsb,
          validateAmount,
          validateNumber,
        },
        loading: false,
        initFileList: [],
        errorTip: '',
        saveParams: this.formData,
        rulesForm: {
          originalIdCode: [
            { required: true, message: '原账号id不能为空', trigger: 'change' },
          ],
          newIdCode: [
            { required: true, message: '新账号id不能为空', trigger: 'change' },
          ],
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      formData: {
        handler(newObject, oldObject) {
          if (newObject) {
            this.saveParams = newObject;
          }
        },
        deep: true,
        immediate: true,
      },
    },
    created() {
      // headers加参数
      // 上传地址
      this.action =
        window.location.protocol +
        '//' +
        replaceLocalDomain(injectHost().apiHost) +
        this.uploadUrl;
    },
    mounted() {
      this.$nextTick(() => {
        this.$on('initData', e => {
          this.$refs.saveParams.resetFields();
          this.saveParams = e;
        });
      });
    },
    methods: {
      beforeUpload() {
        this.loading = true;
      },
      onSuccess(e, files) {
        if (!e.success) {
          this.$message.error(e.msg || '导入失败');
          this.loading = false;

          return;
        }
        // if (e.data && !e.data.importSuccess) {
        //   this.$message.error('excel文件错误');
        //   const uid = files.uid;

        //   const idx = this.$refs.upload.uploadFiles.findIndex(
        //     item => item.uid === uid,
        //   );
        //   this.$refs.upload.uploadFiles.splice(idx, 1); // 去除文件列表失败文件
        //   this.$emit('onErrDownload', e.data.errorFileId);
        //   return;
        // }
        const done = () => {
          this.$message.success('导入成功');

          this.loading = false;
        };
        const err = () => {
          this.loading = false;
        };
        this.$emit('importForm', e.data, this.saveParams);
        this.saveParams = {};
        this.showDialog = false;
      },

      saveForm(formName) {
        this.$refs[formName].validate(valid => {
          if (valid) {
            this.$emit('saveForm', JSON.parse(JSON.stringify(this.saveParams)));
            this.saveParams = {};
            this.showDialog = false;
          }
        });
      },
      handleOK: debounce(function () {
        this.$refs.upload.submit();
      }, 1000),

      resetForm() {
        this.saveParams = {};
        this.$emit('close');
      },
    },
  };
</script>

<style lang="scss" scoped>
  .saleSubjectDialog {
    .subTitle {
      font-weight: bold;
      font-size: 16px !important;
    }
    .timeItem {
      /deep/.el-form-item__label:before {
        content: '' !important;
      }
    }
    .el-select,
    .el-date-editor {
      width: 280px;
    }
  }
</style>
