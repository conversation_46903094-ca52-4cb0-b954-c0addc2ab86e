<!--
 * @Description: 东南亚用户余额管理
 * @Author: xuxiang
 * @Date: 2022-05-27 16:30:38
 * @LastEditTime: 2024-06-18 17:46:46
 * @LastEditors: Please set LastEditors
 * @Reference: 
-->
<template>
  <div>
    <el-form :inline="true" :model="params">
      <el-form-item label="代理ID:">
        <el-input v-model="params.idCode" placeholder="请输入代理ID" />
      </el-form-item>
      <el-form-item label="手机号码:">
        <el-input v-model="params.mobilePhone" placeholder="请输入手机号码" />
      </el-form-item>
      <!-- <el-form-item label="账户类型">
        <el-select v-model="params.platformCode" clearable placeholder="请选择">
          <el-option
            v-for="(item, index) in platformList"
            :key="index"
            :label="item.desc"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <ac-permission-button
          icon="el-icon-search"
          btn-text="查询"
          permission-key=""
          @click="search(true)"
        ></ac-permission-button>
        <el-button icon="el-icon-refresh" @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column
        align="center"
        prop="idCode"
        label="代理ID"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="userName"
        label="姓名"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="mobilePhone"
        label="手机号码"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="countryArea"
        label="国家地区"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="signSubjectName"
        label="签约主体"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="currencyUnit"
        label="本位币币种"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="accountTotalBalance"
        label="账户总余额"
        min-width="150"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="withdrawalAccount"
        label="可提现户"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="frozenAccount"
        label="冻结金额"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="withdrawalArrearsAccount"
        label="可提现欠费户"
        min-width="100"
      ></el-table-column>
      <!-- <el-table-column
        align="center"
        prop="fineAccount"
        label="罚金户"
        min-width="100"
      ></el-table-column> -->

      <el-table-column
        min-width="100"
        max-width="300"
        fixed="right"
        label="操作"
      >
        <template slot-scope="{ row }">
          <ac-permission-button
            v-if="row.hasDetail"
            slot="reference"
            type="text"
            size="small"
            btn-text="可提现户流水"
            permission-key=""
            @click="skipWithdrawalAccountFlow(row, 'withdrawalAccount')"
          ></ac-permission-button>
          <ac-permission-button
            v-if="row.hasDetail"
            slot="reference"
            type="text"
            size="small"
            btn-text="冻结户流水"
            permission-key=""
            @click="skipWithdrawalAccountFlow(row, 'frozenAccount')"
          ></ac-permission-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page="current"
      :page-size="size"
      :page-sizes="[10, 20, 30, 50, 100]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>
<script>
  import {
    getBalanceComboBoxData,
    getAccountBalanceList,
  } from '@/api/serviceFeeManagement';

  export default {
    data() {
      return {
        params: {
          idCode: '',
          mobilePhone: '',
          platformCode: 'WLZ',
        },
        platformList: [], // 发放状态下拉框
        tableData: [],
        loading: false, // loading表格加载层
        current: 1,
        size: 10,
        total: 0,
      };
    },
    created() {
      // this.getEnums();
    },
    methods: {
      async getEnums() {
        const res = await getBalanceComboBoxData();
        if (res) {
          this.platformList = res.platformList || [];
          if (this.platformList.length > 0) {
            this.params.platformCode = this.platformList[0].code;
          }
        }
      },
      async fetch() {
        let params = { ...this.params };
        if (!params.mobilePhone && !params.idCode) {
          this.$message.error('用户ID和电话号码不能同时为空');
          return;
        }
        this.loading = true;
        try {
          const res = await getAccountBalanceList(params);
          console.log(res, 'res');

          if (res) {
            this.tableData = res;
            this.total = res.total;
            this.loading = false;
          } else {
            this.loading = false;
          }
        } catch (error) {
          this.loading = false;
        }
      },
      search() {
        this.fetch();
      },
      reset() {
        Object.assign(this.params, this.$options.data.call(this).params);
        // this.fetch();
      },
      async skipWithdrawalAccountFlow(row, accountType) {
        this.$router.push({
          path:
            '/userFinancialManagementWLZ/userFinancialSouthEast/AccountFlow',
          query: {
            idCode: row.idCode,
            accountType,
          },
        });
      },
      handleSizeChange(val) {
        this.current = 1;
        this.size = val;
        this.fetch();
      },
      handleCurrentChange(val) {
        this.current = val;
        this.fetch();
      },
    },
  };
</script>
<style lang="scss"></style>
