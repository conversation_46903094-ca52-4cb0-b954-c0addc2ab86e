<!--
 * @Description: 主体结算管理
 * @Author: xuxiang
 * @Date: 2022-05-27 16:30:38
 * @LastEditTime: 2024-06-20 17:19:08
 * @LastEditors: Please set LastEditors
 * @Reference: 
-->
<template>
  <div>
    <el-form :inline="true" :model="params">
      <el-form-item label="结算单号:">
        <el-input v-model="params.settleNo" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="业务原单号:">
        <el-input v-model="params.bizNo" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="贸易主体:">
        <el-select
          v-model="params.saleSubjectCode"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="(item, index) in subjectList"
            :key="index"
            :value="item.code"
            :label="item.desc"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="签约主体:">
        <el-select
          v-model="params.signSubjectCode"
          clearable
          placeholder="请选择"
          filterable
        >
          <el-option
            v-for="(item, index) in subjectList"
            :key="index"
            :value="item.code"
            :label="item.desc"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间:">
        <el-date-picker
          v-model="params.detailTime"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <ac-permission-button
          icon="el-icon-search"
          btn-text="查询"
          permission-key=""
          @click="search(true)"
        ></ac-permission-button>
        <el-button icon="el-icon-refresh" @click="reset">重置</el-button>
        <ac-permission-button
          icon="el-icon-download"
          type="warning"
          btn-text="导出"
          permission-key=""
          @click="onExport"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column
        align="center"
        prop="settlementNo"
        label="结算单号"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="bizNo"
        label="业务原单号"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="tradeSubjectName"
        label="贸易主体"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="originalAmount"
        label="交易结算金额"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="originalCurrency"
        label="原交易币种"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="signSubjectName"
        label="签约主体"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="settlementAmount"
        label="签约结算金额"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="contractingCurrency"
        label="签约结算币种"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="exchangeRate"
        label="汇率"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="createTime"
        label="创建时间"
      ></el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page="current"
      :page-size="size"
      :page-sizes="[10, 20, 30, 50, 100]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>
<script>
  import { setInitData, downloadFile } from '@/utils';

  import {
    overseaSettleList,
    overseaSettleExport,
  } from '@/api/serviceFeeManagement';
  import { getComboBoxData } from '@/api/southEast/index.js';

  export default {
    data() {
      return {
        params: {
          detailTime: [],
          platformCode: 'WLZ',
          settleNo: '',
          bizNo: '',
          saleSubjectCode: '',
          signSubjectCode: '',
        },
        tableData: [],
        loading: false, // loading表格加载层
        current: 1,
        size: 10,
        total: 0,
        subjectList: [],
      };
    },
    created() {
      this.params.detailTime = setInitData(90);
      this.getEnums();
      this.search(true);
    },
    methods: {
      async getEnums() {
        const { res, err } = await getComboBoxData();
        if (!err) {
          this.subjectList = res.subjectList || [];
        }
      },
      async fetch() {
        if (this.params.detailTime && this.params.detailTime.length > 0) {
          this.params.startTime = this.params.detailTime[0];
          this.params.endTime = this.params.detailTime[1];
        } else {
          this.params.startTime = null;
          this.params.endTime = null;
        }

        let params = { ...this.params };
        params.size = this.size;
        params.current = this.current;

        delete params.detailTime;
        this.loading = true;
        try {
          const res = await overseaSettleList(params);
          if (res) {
            this.tableData = res.records;
            this.total = res.total;
            this.loading = false;
          } else {
            this.loading = false;
          }
        } catch (error) {
          this.loading = false;
        }
      },
      search() {
        this.pageNum = 1;
        this.fetch();
      },
      reset() {
        Object.assign(this.params, this.$options.data.call(this).params);
        this.pageNum = 1;
        this.pageSize = 10;
        this.params.detailTime = setInitData(90);
        this.fetch();
      },
      async onExport() {
        if (this.params.detailTime && this.params.detailTime.length > 0) {
          this.params.startTime = this.params.detailTime[0];
          this.params.endTime = this.params.detailTime[1];
        } else {
          this.params.startTime = null;
          this.params.endTime = null;
        }
        let data = { ...this.params };
        data.size = this.size;
        data.current = this.current;

        delete data.detailTime;
        const params = { ...data };
        await overseaSettleExport(params);
        this.$message.success('数据导入成功，请在任务中心查看!');
      },
      handleSizeChange(val) {
        this.current = 1;
        this.size = val;
        this.fetch();
      },
      handleCurrentChange(val) {
        this.current = val;
        this.fetch();
      },
    },
  };
</script>
<style lang="scss"></style>
