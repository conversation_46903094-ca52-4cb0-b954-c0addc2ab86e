<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-27 16:30:38
 * @LastEditTime: 2025-02-19 11:04:12
 * @LastEditors: 王赛军
 * @Reference: 
-->
<template>
  <div>
    <el-form ref="form" :inline="true" :model="params">
      <el-form-item label="原账号id:">
        <el-input
          v-model="params.originalIdCode"
          placeholder="请输入原账号id"
        />
      </el-form-item>
      <el-form-item label="新账号id:">
        <el-input v-model="params.newIdCode" placeholder="请输入原账号id" />
      </el-form-item>
      <el-form-item>
        <ac-permission-button
          btn-text="查询"
          permission-key="relations-search"
          @click="search(true)"
        ></ac-permission-button>
        <el-button type="primary" @click="reset">重置</el-button>
        <ac-permission-button
          btn-text="新增绑定"
          permission-key="relations-add"
          @click="showAddModal = true"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :pagination="pagination"
      :fetch="fetch"
    >
      <template slot="operation" slot-scope="scope">
        <el-button
          slot="reference"
          type="text"
          size="mini"
          :disabled="scope.row.status == 'N' ? false : true"
          @click="showStopDialog(scope.row.id)"
        >
          关系终止
        </el-button>
      </template>
    </dynamictable>

    <el-dialog
      title="提示"
      :visible.sync="stopDialogVisiable"
      width="400px"
      @closed="onClose"
    >
      <div>
        <div>确认终止关系？</div>
        <div slot="footer">
          <el-button @click="stopDialogVisiable = false">取 消</el-button>
          <el-button
            :loading="loading"
            type="primary"
            @click="stopAccountRelations"
          >
            确 定
          </el-button>
        </div>
      </div>
    </el-dialog>
    <accountRelationsFrom
      ref="form"
      v-model="showAddModal"
      type="add"
      @close="showAddModal = false"
      @saveForm="saveForm"
    ></accountRelationsFrom>
  </div>
</template>
<script>
  import { setInitData } from '@/utils';
  import {
    getAccountRelations,
    addAccountRelations,
    stopAccountRelations,
  } from '@/api/serviceFeeManagement';
  import dynamictable from '@/components/dynamic-table';
  import accountRelationsFrom from './components/accountRelationsFrom';
  import store from '@/store';

  export default {
    components: {
      dynamictable,
      accountRelationsFrom,
    },
    data() {
      return {
        title: '关系绑定管理',
        columns: [
          {
            prop: 'id',
            label: '序号',
          },
          {
            prop: 'originalIdCode',
            label: '原账号id',
          },
          {
            prop: 'originalSignSubject',
            label: '原账号签约主体',
          },
          {
            prop: 'newIdCode',
            label: '新账号id',
          },
          {
            prop: 'newSignSubject',
            label: '新账号签约主体',
          },
          {
            prop: 'createTime',
            label: '创建时间',
          },
          {
            prop: 'terminationTime',
            label: '终止时间',
          },
          {
            prop: 'creator',
            label: '创建人',
          },
          {
            prop: 'status',
            label: '状态',
            render: ({ status }) => <span>{this.statusDict[status]}</span>,
          },
        ],
        list: [],
        statusDict: {
          N: '正常',
          T: '终止',
        },
        pagination: {
          current: 1,
          size: 10,
          total: null,
        },
        showAddModal: false,
        showImportModal: false,
        id: '',
        params: {
          id: '',
          originalIdCode: '',
          newIdCode: '',
          creator: '',
        },
        showImportDialog: false,
        checkDialogVisiable: false,
        stopDialogVisiable: false,
        loading: false, // loading表格加载层
        current: 1,
        size: 10,
        total: 0,
      };
    },
    created() {
      this.fetch();
    },
    methods: {
      async fetch() {
        let params = { ...this.params };
        params.current = this.pagination.current;
        params.size = this.pagination.size;
        this.loading = true;
        try {
          const res = await getAccountRelations(params);
          if (res) {
            this.list = res.records;
            this.pagination.total = res.total;
            this.loading = false;
          } else {
            this.loading = false;
          }
        } catch (error) {
          this.loading = false;
        }
      },
      async saveForm(data) {
        let params = {
          originalIdCode: data.originalIdCode,
          newIdCode: data.newIdCode,
          creator: store.state.user.username,
        };
        const res = await addAccountRelations(params);
        this.$message.success('操作成功');
        this.showDialog = false;
        this.fetch();
      },

      search() {
        this.pagination.current = 1;
        this.fetch();
      },

      showStopDialog(id) {
        this.id = id;
        this.stopDialogVisiable = true;
      },
      async stopAccountRelations() {
        let params = {
          id: this.id,
          creator: store.state.user.username,
        };
        const res = await stopAccountRelations(params);
        this.$message.success('操作成功');
        this.stopDialogVisiable = false;
        this.fetch();
      },
      onClose() {
        this.checkDialogVisiable = false;
        this.stopDialogVisiable = false;
      },
      reset() {
        Object.assign(this.params, this.$options.data.call(this).params);
        this.pagination.current = 1;
        this.pagination.size = 10;
      },
      handleSizeChange(val) {
        this.pagination.current = 1;
        this.pagination.size = val;
        this.fetch();
      },
      handleCurrentChange(val) {
        this.pageSize = val;
        this.fetch();
      },
    },
  };
</script>
<style lang="scss"></style>
