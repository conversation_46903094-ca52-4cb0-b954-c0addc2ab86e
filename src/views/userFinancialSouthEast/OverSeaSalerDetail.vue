<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-27 16:30:38
 * @LastEditTime: 2024-07-25 11:42:26
 * @LastEditors: Please set LastEditors
 * @Reference: 
-->
<template>
  <div>
    <el-form :inline="true" :model="params">
      <el-form-item label="申请时间:">
        <el-date-picker
          v-model="params.applyTime"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="代理ID:">
        <el-input v-model="params.idCode" placeholder="请输入代理ID" />
      </el-form-item>
      <el-form-item label="手机号码:">
        <el-input v-model="params.mobilePhone" placeholder="请输入手机号码" />
      </el-form-item>
      <el-form-item label="单号:">
        <el-input v-model="params.applyNo" placeholder="请输入单号" />
      </el-form-item>
      <el-form-item label="审核状态:">
        <el-select v-model="params.status" clearable placeholder="请选择">
          <el-option
            v-for="(item, index) in statusList"
            :key="index"
            :label="item.desc"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <ac-permission-button
          btn-text="查询"
          permission-key="queryOverseasDistributors-search"
          @click="search(true)"
        ></ac-permission-button>
        <el-button type="primary" @click="reset">重置</el-button>
        <ac-permission-button
          btn-text="主体导出"
          permission-key="queryOverseasDistributors-export"
          @click="onExport"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column
        align="center"
        type="index"
        label="序号"
        width="55"
        fixed
      ></el-table-column>
      <el-table-column
        align="center"
        prop="subjectName"
        label="贸易主体"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="applyNo"
        label="单号"
        min-width="100"
      ></el-table-column>

      <el-table-column
        align="center"
        prop="idCode"
        label="代理ID"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="name"
        label="用户姓名"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="mobilePhone"
        label="手机号码"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="beneficiaryCountryName"
        label="国家"
        min-width="100"
      ></el-table-column>

      <el-table-column
        align="center"
        prop="bankAccountCurrency"
        label="卡币种"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="bankName"
        label="开户银行"
        min-width="150"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="bankCardNo"
        label="银行账号"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="applyAmount"
        label="申请金额"
        min-width="150"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="signSubjectName"
        label="签约主体"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="currencyUnit"
        label="本位币"
        min-width="120"
      ></el-table-column>
      <!-- <el-table-column
        align="center"
        prop="payAmount"
        label="实付金额"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="currencyPayAmount"
        label="结算金额_原币"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="exchangeRate"
        label="汇率"
        min-width="140"
      ></el-table-column> -->
      <el-table-column
        align="center"
        prop="applyTime"
        label="申请时间"
        min-width="160"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="statusDesc"
        label="状态"
        min-width="100"
      ></el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page="pageNum"
      :page-size="pageSize"
      :page-sizes="[10, 20, 30, 50, 100]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>
<script>
  import { setInitData } from '@/utils';
  import {
    getOverseasSubjectList,
    getOverseasSubjectComboBoxData,
    overseasDownloadSubjectList,
  } from '@/api/serviceFeeManagement';

  export default {
    data() {
      return {
        params: {
          idCode: '',
          mobilePhone: '',
          platformCode: 'WLZ',
          status: '',
          subjectCode: '',
          applyNo: '',
          applyTime: [], // 申请时间
          applyStartTime: null, // 申请开始时间
          applyEndTime: null, // 申请结束时间
        },
        statusList: [], // 发放状态下拉框
        subjectList: [], // 主体下拉框
        tableData: [],
        loading: false, // loading表格加载层
        pageNum: 1,
        pageSize: 10,
        total: 0,
      };
    },
    created() {
      this.params.applyTime = setInitData(30);
      this.getEnums();
      this.fetch();
    },
    methods: {
      async getEnums() {
        const res = await getOverseasSubjectComboBoxData();
        if (res) {
          this.statusList = res.statusList || [];
          this.subjectList = res.subjectList || [];
        }
      },
      async fetch() {
        if (this.params.applyTime && this.params.applyTime.length > 0) {
          this.params.applyStartTime = this.params.applyTime[0];
          this.params.applyEndTime = this.params.applyTime[1];
        } else {
          this.params.applyStartTime = null;
          this.params.applyEndTime = null;
        }
        let params = { ...this.params };
        params.size = this.pageSize;
        params.current = this.pageNum;
        delete params.applyTime;
        if (
          !params.mobilePhone &&
          !params.applyStartTime &&
          !params.applyEndTime
        ) {
          this.$message.error('时间和电话号码不能同时为空');
          return;
        }
        this.loading = true;
        try {
          const res = await getOverseasSubjectList(params);
          if (res) {
            this.tableData = res.records;
            this.total = res.total;
            this.loading = false;
          } else {
            this.loading = false;
          }
        } catch (error) {
          this.loading = false;
        }
      },
      search() {
        this.pageNum = 1;
        this.fetch();
      },
      async onExport() {
        if (this.params.applyTime && this.params.applyTime.length > 0) {
          this.params.applyStartTime = this.params.applyTime[0];
          this.params.applyEndTime = this.params.applyTime[1];
        } else {
          this.params.applyStartTime = null;
          this.params.applyEndTime = null;
        }
        let params = {
          ...this.params,
          taskContent: '服务费管理-服务费主体管理-海外经销商主体查询-主体导出',
        };
        delete params.applyTime;
        try {
          const res = await overseasDownloadSubjectList(params);
          this.$message.success('任务正在处理中，请稍后到任务中心查看');
        } catch (error) {}
      },
      reset() {
        Object.assign(this.params, this.$options.data.call(this).params);
        this.pageNum = 1;
        this.pageSize = 10;
        this.params.applyTime = setInitData(30);
        // this.fetch();
      },
      handleSizeChange(val) {
        this.pageNum = 1;
        this.pageSize = val;
        this.fetch();
      },
      handleCurrentChange(val) {
        this.pageNum = val;
        this.fetch();
      },
    },
  };
</script>
<style lang="scss"></style>
