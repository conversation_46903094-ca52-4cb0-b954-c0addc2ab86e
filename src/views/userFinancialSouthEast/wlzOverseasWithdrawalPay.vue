<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-30 09:50:13
 * @LastEditTime: 2025-04-08 17:55:40
 * @LastEditors: Please set LastEditors
 * @Reference: 
-->
<template>
  <div>
    <div>
      <el-form ref="form" :model="form" label-width="80px" inline>
        <el-form-item label="申请时间:" prop="applyTime">
          <el-date-picker
            v-model="form.applyTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="代理ID:" prop="idCode">
          <el-input v-model="form.idCode" placeholder="请输入代理ID"></el-input>
        </el-form-item>
        <el-form-item label="手机号码:" prop="mobilePhone">
          <el-input
            v-model="form.mobilePhone"
            placeholder="请输入手机号码"
          ></el-input>
        </el-form-item>
        <el-form-item label="单号:" prop="applyNo">
          <el-input
            v-model="form.applyNo"
            placeholder="请输入单号"
            style="width: 250px"
          ></el-input>
        </el-form-item>
        <el-form-item label="签约主体:">
          <el-select
            v-model="form.signSubjectCode"
            filterable
            clearable
            placeholder="请输入签约主体"
          >
            <el-option
              v-for="(item, index) in subjectList"
              :key="index"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="合作方式:" prop="authType">
          <el-select v-model="form.authType" clearable>
            <el-option
              v-for="item in option.authTypeList"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核状态:" prop="status">
          <el-select v-model="form.status" clearable>
            <el-option
              v-for="item in option.statusList"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <ac-permission-button
            btn-text="查询"
            permission-key="overseasWithdrawalApplication-search"
            @click="search(1)"
          ></ac-permission-button>
          <el-button type="primary" @click="reset">重置</el-button>
          <ac-permission-button
            btn-text="打款明细导出"
            permission-key=""
            @click="detailExport"
          ></ac-permission-button>
          <ac-permission-button
            btn-text="空中云汇打款导出"
            permission-key="overseasWithdrawalPay-export"
            @click="download('airwallex')"
          ></ac-permission-button>
          <ac-permission-button
            btn-text="大马银行打款导出"
            permission-key="overseasWithdrawalPay-export"
            @click="download('myBank')"
          ></ac-permission-button>
          <ac-permission-button
            btn-text="批量变更打款状态"
            permission-key="personalInvoicingPay-import"
            @click="onBtnImport"
          ></ac-permission-button>
        </el-form-item>
      </el-form>
    </div>

    <div>
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        :header-cell-style="{ 'text-align': 'center' }"
        :cell-style="{ 'text-align': 'center' }"
      >
        <el-table-column type="index" label="序号"></el-table-column>
        <el-table-column prop="applyNo" label="单号"></el-table-column>
        <el-table-column prop="idCode" label="代理ID"></el-table-column>
        <el-table-column prop="name" label="开户姓名"></el-table-column>
        <el-table-column prop="mobilePhone" label="手机号码"></el-table-column>
        <el-table-column
          prop="beneficiaryCountryName"
          label="国家"
        ></el-table-column>
        <el-table-column
          prop="signSubjectName"
          label="签约主体"
        ></el-table-column>
        <el-table-column prop="authType" label="合作方式"></el-table-column>
        <el-table-column prop="bankName" label="开户银行"></el-table-column>
        <el-table-column prop="bankCardNo" label="银行账号"></el-table-column>
        <el-table-column prop="applyAmount" label="申请金额"></el-table-column>
        <el-table-column prop="currencyUnit" label="本位币"></el-table-column>
        <el-table-column prop="feeAmount" label="手续费金额"></el-table-column>
        <el-table-column
          prop="taxFeeAmount"
          label="手续费税费"
        ></el-table-column>
        <el-table-column
          prop="withholdTaxAmount"
          label="预扣税"
        ></el-table-column>
        <el-table-column prop="payAmount" label="实付金额"></el-table-column>
        <el-table-column prop="applyTime" label="申请时间"></el-table-column>
        <el-table-column prop="statusDesc" label="审核状态"></el-table-column>
        <el-table-column prop="payDate" label="打款时间"></el-table-column>
        <el-table-column prop="disuse" label="账号状态"></el-table-column>
        <el-table-column
          min-width="100"
          max-width="300"
          fixed="right"
          label="操作"
        >
          <template slot-scope="{ row }">
            <ac-permission-button
              v-if="row.status === 'applying'"
              slot="reference"
              type="text"
              size="small"
              btn-text="审核通过"
              permission-key="overseasWithdrawalApplication-examination-passed"
              @click="passApply(row.applyNo)"
            ></ac-permission-button>
            <ac-permission-button
              v-if="row.status === 'applying'"
              slot="reference"
              type="text"
              size="small"
              btn-text="审核拒绝"
              permission-key="overseasWithdrawalApplication-review-rejection"
              @click="rejectApply(row.applyNo)"
            ></ac-permission-button>
            <ac-permission-button
              v-if="row.status === 'pass' || row.status === 'success'"
              slot="reference"
              type="text"
              size="small"
              btn-text="变更状态"
              permission-key="overseasWithdrawalPay-change-state"
              @click="statusChange(row)"
            ></ac-permission-button>
            <ac-permission-button
              v-if="row.status === 'success'"
              slot="reference"
              type="text"
              size="small"
              btn-text="编辑打款时间"
              permission-key="overseasWithdrawalPay-change-state"
              @click="payDateChange(row)"
            ></ac-permission-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page.current"
        :page-sizes="[10, 20, 50]"
        :page-size="page.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <el-dialog
      title="批量变更打款状态"
      :visible.sync="importFileDiolog"
      width="500px"
      @closed="onClose"
    >
      <div style="margin: 10px 0px">
        <a
          href="https://img.danchuangglobal.com/202412/5166f68370f645e8b147e3af6e946d70.xlsx"
          target="_blank"
        >
          下载变更模板
        </a>
      </div>
      <div>
        <el-upload
          ref="upload"
          drag
          class="upload-demo"
          :limit="1"
          :headers="headers"
          :data="uploadData"
          :action="action"
          :on-preview="handlePreview"
          :on-remove="handleRemove"
          :on-success="onSuccess"
          :file-list="fileList"
          :auto-upload="false"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            <p>将文件拖到此处，或点击上传</p>
            <p>支持扩展名：.xlsx, .xls</p>
          </div>

          <div slot="tip" class="el-upload__tip">
            <p>将文件拖到此处，或点击上传</p>
          </div>
        </el-upload>
      </div>
      <div slot="footer">
        <el-button @click="importFileDiolog = false">取 消</el-button>
        <el-button type="primary" @click="importFileConfirm">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="详情" :visible.sync="dialogVisible" width="60%">
      <div>
        <el-descriptions :column="2">
          <el-descriptions-item label="代理ID">
            {{ detailData.idCode }}
          </el-descriptions-item>
          <el-descriptions-item label="合作方式">
            {{ detailData.authTypeDesc }}
          </el-descriptions-item>
          <el-descriptions-item label="手机号">
            {{ detailData.mobilePhone }}
          </el-descriptions-item>
          <el-descriptions-item label="代理姓名">
            {{ detailData.userName }}
          </el-descriptions-item>
          <el-descriptions-item label="户名">
            {{ detailData.name }}
          </el-descriptions-item>
          <el-descriptions-item
            v-if="detailData.merchantName"
            label="个体工商户名称"
          >
            {{ detailData.merchantName }}
          </el-descriptions-item>
          <el-descriptions-item label="银行卡号">
            {{ detailData.bankCardNo }}
          </el-descriptions-item>
          <el-descriptions-item label="税号">
            {{ detailData.taxNo }}
          </el-descriptions-item>
          <el-descriptions-item label="开户行">
            {{ detailData.bankName }}
          </el-descriptions-item>
          <el-descriptions-item label="审核理由|拒绝理由">
            {{ detailData.remark }}
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">
            {{ detailData.applyTime }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <statusChangeDialog
      v-model="showDialog"
      :current-row="currentRow"
      :remark-list="option.remarkList"
      router-type="overseasWithdrawalPay"
      @onGet="search"
    ></statusChangeDialog>
    <PayDateChangeDialog
      v-model="showPayDateChangeDialog"
      :current-row="currentRow"
      :remark-list="option.remarkList"
      router-type="overseasWithdrawalPay"
      @onGet="search"
    ></PayDateChangeDialog>
  </div>
</template>

<script>
  // import uoloadFile from '@/components/uoloadFile';
  import {
    getOverseasPayComboBoxData,
    getOverseasPayList,
    overseasAllPayData,
    overseasAllPay,
    overseasPay,
    bankReturn,
    overseasDownloadPayList,
    importPayStatus,
    exportPaymentDetails,
  } from '@/api/serviceFeeManagement';
  import { getComboBoxData } from '@/api/southEast/index.js';
  import { setInitData } from '@/utils/index.js';
  import statusChangeDialog from './components/statusChangeDialog.vue';
  import { getCookie } from '@/utils/auth';
  import injectHost from '@/utils/injectHost';
  import { isNumber } from '@/utils/validate';
  import { replaceLocalDomain } from '@/utils/index.js';
  import PayDateChangeDialog from './components/payDateChangeDialog.vue';
  export default {
    name: 'OverseasWithdrawalPay',
    components: { statusChangeDialog, PayDateChangeDialog },
    // components: { statusChangeDialog, uoloadFile },
    data() {
      return {
        headers: {
          token: getCookie(),
          appCode: process.env.VUE_APP_LOGIN_APP_CODE,
        }, // 导入头部信息
        fileList: [],
        action: '',
        importFileDiolog: false,
        turnOn: false,
        form: {
          applyTime: setInitData(30, '{y}-{m}-{d} {h}:{i}:{s}'),
          idCode: '',
          mobilePhone: '',
          platformCode: 'WLZ',
          applyNo: '',
          status: '',
          signSubjectCode: '',
        },
        subjectList: [],
        option: {
          authTypeList: [],
          statusList: [],
          letaxLogoutList: [],
          mobilePhoneList: [],
          remarkList: [],
        },
        tableData: [],
        page: {
          current: 1,
          size: 10,
        },
        total: 0,
        dialogVisible: false,
        detailData: {}, //详情
        currentRow: null, // 当前数据列
        showDialog: false,
        payDialogVisiable: false, //审核确认弹窗
        applyPhone: '', //申请的手机
        applyNo: '', //申请的单号
        identifyingCode: '', //验证码
        loading: false, //确认按钮
        tableLoading: false,

        allPayVisiable: false, //全量打款弹窗
        allPayIdentifyingCode: '', //全量打款验证码
        allPayLoading: false, //全量打款loading
        allPayData: {
          subjectInfoList: [],
        }, //全量打款数据

        uploadLoading: false, //上传laoding

        uploadData: {
          appId: 'abmau',
          timeStamp: Date.now(),
        },
        showPayDateChangeDialog: false, // 打款时间修改弹窗
      };
    },
    created() {
      this.fetch();
      this.search();
      this.getEnums();
      // headers加参数
      // 上传地址
      this.action =
        window.location.protocol +
        '//' +
        replaceLocalDomain(injectHost().apiHost) +
        '/api/abmio/api/v1.0/upload';
    },
    methods: {
      async getEnums() {
        const { res, err } = await getComboBoxData();
        if (!err) {
          this.subjectList = res.subjectList || [];
        }
      },
      handleRemove(file, fileList) {
        console.log(file, fileList);
      },
      handlePreview(file) {
        console.log(file);
      },
      importFileConfirm() {
        this.$refs.upload.submit();
      },
      onBtnImport() {
        this.importFileDiolog = true;
      },
      async onSuccess(e) {
        if (!e.success) {
          this.$message.error(e.msg || '导入失败');
          return;
        }
        if (e.data && e.data.id) {
          const res = await importPayStatus({ fileId: e.data.id });

          if (!res) {
            this.$message.success('修改成功');
            this.importFileDiolog = false;

            this.fileList = [];
            this.fetch();
          }
        }

        // if (!e.success) {
        //   this.$message.error(e.msg || '导入失败');
        //   return;
        // }
        // this.$message.success('导入成功');
        // this.fetch();
      },
      statusChange(row) {
        this.showDialog = true;
        this.currentRow = row;
      },
      payDateChange(row) {
        console.log('🚀 ~ payDateChange ~ payDateChange:');
        this.showPayDateChangeDialog = true;
        this.currentRow = row;
      },
      onClose() {
        this.$nextTick(function () {
          this.turnOn = false;
          this.applyPhone = '';
          this.identifyingCode = '';
          this.allPayIdentifyingCode = '';
          this.fileList = [];
        });
      },
      //获取下拉框
      async fetch() {
        const res = await getOverseasPayComboBoxData();

        this.option.statusList = res?.statusList || [];

        this.option.authTypeList = res?.authTypeList || [];

        this.option.mobilePhoneList = res?.mobilePhoneList || [];

        this.option.remarkList = res?.overseasRemarkList || [];
      },
      //查询
      async search(current = 0) {
        if (current) {
          this.page.current = current;
        }
        const { applyTime = [], ...form } = this.form;
        const data = {
          applyStartTime: Array.isArray(applyTime) ? applyTime[0] : '',
          applyEndTime: Array.isArray(applyTime) ? applyTime[1] : '',
          ...this.page,
          ...form,
        };

        if (!isNumber(data.idCode)) {
          this.$message.warning('代理ID只能为数字');
          return false;
        } else {
          this.tableLoading = true;
          const res = await getOverseasPayList(data);
          this.tableLoading = false;
          if (res) {
            this.tableData = res.records || [];
            this.total = res.total || 0;
          }
        }
      },
      //重置
      reset() {
        this.$refs['form'].resetFields();
      },

      //详情
      async view(row) {
        // const res = await getPayDetail({ applyNo: row.applyNo });
        if (row) {
          this.detailData = row || {};
        }
        if (this.detailData) {
          this.dialogVisible = true;
        }
      },
      //点击审核通过
      payApply(applyNo) {
        this.applyPhone = '';
        this.payDialogVisiable = true;
        this.applyNo = applyNo;
      },
      //银行退票
      bankReturn(applyNo) {
        this.$confirm('确认银行退票？', '确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        })
          .then(async () => {
            await bankReturn({ applyNo });
            this.$message.success('银行退票成功');
            this.search();
          })
          .catch(() => {});
      },
      //点击获取验证码
      async getIdentifyingCode() {
        if (!this.applyPhone) {
          this.$message.warning('请先选择手机号');
          return;
        }
        this.turnOn = true;
        const res = await sendIdentifyingCode({ mobilePhone: this.applyPhone });
        if (res === null) {
          this.$message.success('获取验证码成功');
        }
      },
      //确认审核通过
      async confirmApply() {
        if (!this.applyPhone) {
          this.$message.warning('请先选择手机号');
          return;
        }
        // this.loading = true;
        const data = {
          applyNo: this.applyNo,
          mobilePhone: this.applyPhone,
          identifyingCode: this.identifyingCode,
        };
        await overseasPay(data);
        this.$message.success('打款成功');
        this.payDialogVisiable = false;
        this.search();
        // this.loading = false;
      },
      //全量打款通过
      async allPayConfirm() {
        const { applyTime } = this.form;
        if (!applyTime) {
          this.$message.warning('请先选择申请时间');
          return;
        }
        if (!this.applyPhone) {
          this.$message.warning('请先选择手机号');
          return;
        }
        // this.allPayLoading = true;
        const data = {
          applyStartTime: applyTime[0],
          applyEndTime: applyTime[1],
          mobilePhone: this.applyPhone,
          identifyingCode: this.allPayIdentifyingCode,
        };
        await overseasAllPay(data);
        this.$message.success('任务正在处理中，请稍后到任务中心查看');
        this.allPayVisiable = false;
        this.search();
      },
      //下载
      download(payChannel) {
        const { applyTime = [], ...form } = this.form;
        const data = {
          applyStartTime: Array.isArray(applyTime) ? applyTime[0] : '',
          applyEndTime: Array.isArray(applyTime) ? applyTime[1] : '',
          payChannel: payChannel,
          ...form,
        };
        overseasDownloadPayList({
          taskContent: '服务费管理-收益打款管理-海外个人提现打款-打款模板导出',
          ...data,
        }).then(_ => {
          this.$message.success('任务正在处理中，请稍后到任务中心查看');
        });
      },
      // 打款明细导出
      async detailExport() {
        const { applyTime = [], ...form } = this.form;
        const data = {
          applyStartTime: Array.isArray(applyTime) ? applyTime[0] : '',
          applyEndTime: Array.isArray(applyTime) ? applyTime[1] : '',
          ...form,
        };
        try {
          const params = {
            ...data,
            taskContent:
              '服务费管理-收益打款管理-海外个人提现打款-打款明细导出',
          };
          const res = await exportPaymentDetails(params);
          this.$message.success('任务正在处理中，请稍后到任务中心查看');
        } catch (error) {}
      },
      onProgress() {
        this.uploadLoading = true;
      },
      //回传
      onImportSuccess(response) {
        this.uploadLoading = false;
        if (response.msg) {
          this.$message.error(response.msg);
        } else {
          this.$message.success('任务正在处理中，请稍后到任务中心查看');
        }
      },
      onError() {
        this.uploadLoading = false;
      },
      //分页
      handleCurrentChange(e) {
        this.page.current = e;
        this.search();
      },
      handleSizeChange(e) {
        this.page.size = e;
        this.handleCurrentChange(1);
      },
    },
  };
</script>

<style lang="scss" scoped>
  /deep/ .el-descriptions--small {
    font-size: 14px;
  }
</style>
