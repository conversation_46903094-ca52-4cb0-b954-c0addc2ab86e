<template>
  <div>
    <!-- <el-form inline>
      <el-form-item label="金额:">
        <el-input v-model="searchParams.orderSn" placeholder="请输入金额" />
      </el-form-item>
      <el-form-item label="会员:">
        <el-input
          v-model="searchParams.mergedNumber"
          placeholder="请输入会员"
        />
      </el-form-item>
      <el-form-item label="账户类型:">
        <el-select v-model="searchParams.origin" placeholder="请选择">
          <el-option label="全部" :value="0"></el-option>
          <el-option label="人民币" :value="1"></el-option>
          <el-option label="澳币" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间:">
        <el-date-picker
          v-model="searchDate"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="审核状态:">
        <el-select v-model="searchParams.origin" placeholder="请选择">
          <el-option label="全部" :value="0"></el-option>
          <el-option label="待审核" :value="1"></el-option>
          <el-option label="已审核" :value="2"></el-option>
          <el-option label="已驳回" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button type="primary" @click="onExport">导出</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    ></dynamictable> -->
    <rechargeApplication router-type="rechargeAudit"></rechargeApplication>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import rechargeApplication from './rechargeApplication';
  import { getCategoryList } from '@/api/award';
  import { exportExcel } from '@/api/blob';
  import { downloadFile } from '@/utils';

  export default {
    components: {
      rechargeApplication,
    },
    data() {
      return {
        searchParams: {},
        searchDate: '',
        options: {
          loading: false,
          border: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        list: [],
        columns: [
          {
            prop: 'a',
            label: '编号',
          },
          {
            prop: 'b',
            label: '创建时间',
          },
          {
            prop: 'c',
            label: '申请人',
          },
          {
            prop: 'd',
            label: '金额',
          },
          {
            prop: 'e',
            label: '会员IDcode',
          },
          {
            prop: 'cd',
            label: '会员手机号',
          },
          {
            prop: 'we',
            label: '支付方式',
          },
        ],
      };
    },
    created() {
      // this.getList(true)
    },
    methods: {
      getParams() {
        this.searchParams.startTime = this.searchDate ? this.searchDate[0] : '';
        this.searchParams.endTime = this.searchDate ? this.searchDate[1] : '';
        const params = {
          ...this.searchParams,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },

      async getList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await getCategoryList(params);
        this.options.loading = false;
        this.list = res ? res.list : [];
        this.pagination.total = res ? res.total : 0;
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.searchDate = '';
        this.getList(true);
      },
      onExport() {
        const params = this.getParams();
        exportExcel(
          params,
          '/api/pay-dashboard/order/refundOrder/export',
          'get',
        ).then(res => {
          downloadFile(res.data, '退款失败处理列表', 'csv');
        });
      },
    },
  };
</script>
<style lang="scss" scoped></style>
