<template>
  <div>
    <el-form inline>
      <el-form-item label="会员ID_CODE:">
        <el-input
          v-model="searchParams.idCode"
          placeholder="请输入会员ID_CODE"
        />
      </el-form-item>
      <el-form-item label="订单ID:">
        <el-input v-model="searchParams.orderId" placeholder="请输入订单ID" />
      </el-form-item>
      <el-form-item label="其他信息:">
        <el-input
          v-model="searchParams.otherMessage"
          style="width: 300px"
          placeholder="请输入其他信息(订单/商品/支付/退款等ID)"
        />
      </el-form-item>
      <el-form-item label="支付时间:">
        <el-date-picker
          v-model="searchDate"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="备注:">
        <el-input v-model="searchParams.remarks" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <ac-permission-button
          btn-text="导出"
          permission-key="memberPaymentSearch-export"
          type="primary"
          @click="onExport"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import { membersPayQueryList } from '@/api/financialManagement';
  import { exportExcel } from '@/api/blob';
  import { downloadFile, initSearchParams, setInitData } from '@/utils';

  export default {
    components: {
      dynamictable,
    },
    data() {
      return {
        searchParams: {
          endAt: '',
          idCode: '',
          orderId: '',
          otherMessage: '',
          remarks: '',
          startAt: '',
        },
        searchDate: '',
        options: {
          loading: false,
          border: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        list: [],
        columns: [
          {
            prop: 'idCode',
            label: 'IDcode',
          },
          {
            prop: 'time',
            label: '时间',
          },
          {
            prop: 'beforeOperatingAmount',
            label: '操作前金额',
          },
          {
            prop: 'amount',
            label: '金额',
          },
          {
            prop: 'afterOperationAmount',
            label: '操作后金额',
          },
          {
            prop: 'otherMessage',
            label: '其他信息',
          },
          {
            prop: 'remarks',
            label: '备注',
          },
        ],
      };
    },
    created() {
      // this.searchDate = setInitData(30)
      this.getList(true);
    },
    methods: {
      getParams() {
        this.searchParams.startAt = this.searchDate ? this.searchDate[0] : '';
        this.searchParams.endAt = this.searchDate ? this.searchDate[1] : '';
        const params = {
          ...this.searchParams,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return initSearchParams(params);
      },

      async getList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        try {
          const res = await membersPayQueryList(params);
          this.options.loading = false;
          this.list = res && res.list ? res.list : [];
          this.pagination.total = res ? res.total : 0;
        } catch (err) {
          this.options.loading = false;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.searchDate = setInitData(30);
        this.getList(true);
      },
      onExport() {
        const params = this.getParams();
        exportExcel(params, '/api/cust-bus/membersPayQuery/export', 'get').then(
          res => {
            downloadFile(res.data, '会员支付列表');
          },
        );
      },
    },
  };
</script>
<style lang="scss" scoped></style>
