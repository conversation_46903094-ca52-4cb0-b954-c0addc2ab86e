<template>
  <div>
    <el-form inline>
      <el-form-item label="编号:">
        <el-input v-model="searchParams.id" placeholder="请输入编号" />
      </el-form-item>
      <el-form-item :label="type === 'refundPayment' ? '退款金额:' : '金额:'">
        <el-input
          v-model="searchParams.amount"
          :placeholder="
            type === 'refundPayment' ? '请输入退款金额' : '请输入金额'
          "
        />
      </el-form-item>
      <el-form-item
        v-if="['rechargeProfit', 'withdrawalRechargeAudit'].includes(type)"
        label="代理:"
      >
        <el-input
          v-model="searchParams.userQuery"
          placeholder="请输入idcode/手机号"
        />
      </el-form-item>
      <el-form-item
        v-if="!['rechargeProfit', 'withdrawalRechargeAudit'].includes(type)"
        label="idCode:"
      >
        <el-input v-model="searchParams.idCode" placeholder="请输入idcode" />
      </el-form-item>
      <el-form-item
        v-if="!['rechargeProfit', 'withdrawalRechargeAudit'].includes(type)"
        label="手机号:"
      >
        <el-input v-model="searchParams.mobile" placeholder="请输入手机号" />
      </el-form-item>
      <el-form-item v-if="type === 'refundPayment'" label="申请人:">
        <el-input
          v-model="searchParams.proposerName"
          placeholder="请输入申请人"
        />
      </el-form-item>
      <el-form-item
        :label="type === 'refundPayment' ? '创建时间:' : '申请时间:'"
      >
        <el-date-picker
          v-model="searchDate"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item
        v-if="['withdrawalRechargeAudit', 'rechargeProfit'].includes(type)"
        label="复审审核时间"
      >
        <el-date-picker
          v-model="reviewDate"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="审核状态:">
        <el-select v-model="searchParams.status" placeholder="请选择">
          <el-option
            v-for="item in reviewList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="业务类型:">
        <el-select v-model="searchParams.businessType" placeholder="请选择">
          <el-option
            v-for="item in businessList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch()">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <ac-permission-button
          v-if="['rechargeProfit', 'refundPayment'].includes(type)"
          :btn-text="type === 'rechargeProfit' ? '充值收益申请' : '退贷款申请'"
          :permission-key="
            type === 'rechargeProfit'
              ? 'rechargeProfit-add'
              : 'refundPayment-add'
          "
          type="primary"
          @click="onAdd"
        ></ac-permission-button>
        <ac-permission-button
          v-if="
            ['withdrawalRechargeAudit', 'refundPaymentCheck'].includes(type)
          "
          type="primary"
          btn-text="批量初审"
          :permission-key="
            type === 'refundPaymentCheck'
              ? 'refundPaymentCheck-batch-preliminary-review'
              : 'withdrawalRechargeAudit-batch-preliminary-review'
          "
          @click="handleBatch('first')"
        ></ac-permission-button>
        <ac-permission-button
          v-if="
            ['withdrawalRechargeAudit', 'refundPaymentCheck'].includes(type)
          "
          btn-text="批量复审"
          :permission-key="
            type === 'refundPaymentCheck'
              ? 'refundPaymentCheck-batch-review'
              : 'withdrawalRechargeAudit-batch-review'
          "
          type="primary"
          @click="handleBatch('tow')"
        ></ac-permission-button>
        <ac-permission-button
          v-if="
            [
              'withdrawalRechargeAudit',
              'refundPayment',
              'refundPaymentCheck',
            ].includes(type)
          "
          type="primary"
          btn-text="导出"
          :permission-key="exportPermissionKey"
          @click="onExport"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import { newExportExcel } from '@/api/blob';
  import { downloadFile, setInitData, initSearchParams } from '@/utils';

  export default {
    props: {
      type: {
        type: String,
        default: '',
      },
      exportUrl: {
        type: String,
        default: '',
      },
      reviewList: {
        type: Array,
        default: () => [],
      },
      exportMethod: {
        type: String,
        default: 'post',
      },
      businessList: {
        type: Array,
        default: () => [],
      },
      exportPermissionKey: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        searchParams: {
          amount: '',
          proposerName: '',
          mobile: '',
          idCode: '',
          businessType: '',
          endAt: '',
          startAt: '',
          id: '',
          status: '',
          userQuery: '',
          reviewEndAt: '',
          reviewStartAt: '',
        },
        reviewDate: '',
        searchDate: '',
      };
    },
    created() {
      this.onReset();
    },
    methods: {
      getParams() {
        this.searchParams.startAt = this.searchDate ? this.searchDate[0] : '';
        this.searchParams.endAt = this.searchDate ? this.searchDate[1] : '';
        if (['withdrawalRechargeAudit', 'rechargeProfit'].includes(this.type)) {
          this.searchParams.reviewStartAt = this.reviewDate
            ? this.reviewDate[0]
            : '';
          this.searchParams.reviewEndAt = this.reviewDate
            ? this.reviewDate[1]
            : '';
        }

        const params = {
          ...this.searchParams,
          businessType:
            ['withdrawalRechargeAudit', 'rechargeProfit'].includes(this.type) &&
            this.searchParams.businessType === ''
              ? 'all'
              : this.searchParams.businessType,
        };
        // 提现充值， 审核列表不需要 草稿状态
        if (
          ['withdrawalRechargeAudit', 'rechargeProfit'].includes(this.type) &&
          this.searchParams.status === ''
        ) {
          params.status =
            this.type === 'withdrawalRechargeAudit' ? 'check_all' : 'all';
        } else {
          params.status == this.searchParams.status;
        }
        return initSearchParams(params);
      },
      onSearch() {
        this.$emit('onSearch', this.getParams());
      },
      onAdd() {
        this.$emit('onAdd');
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.searchDate = setInitData(30);
        this.onSearch();
      },
      handleBatch(key) {
        this.$emit('onBatch', key);
      },
      onExport() {
        const allStatus = [0, 1, 2, 3, 4, 5]; // 退货款申请列表全部状态
        const allStatus1 = [0, 1, 2, 3, 5]; // 申请审核全部状态

        let params = this.getParams();

        // 退货款状态需要数组的形式传入， 申请列表不需要 草稿状态 4
        if (['refundPaymentCheck', 'refundPayment'].includes(this.type)) {
          params.status =
            params.status !== undefined
              ? [params.status]
              : this.type === 'refundPayment'
              ? allStatus
              : allStatus1;
        }

        newExportExcel(params, `/api${this.exportUrl}`, this.exportMethod).then(
          res => {
            downloadFile(res.data);
          },
        );
      },
    },
  };
</script>
<style lang="scss" scoped></style>
