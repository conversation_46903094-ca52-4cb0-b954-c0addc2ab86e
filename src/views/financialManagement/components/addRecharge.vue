<template>
  <div>
    <el-dialog
      width="520px"
      :title="title"
      :visible.sync="visible"
      @closed="onClose"
    >
      <el-form ref="formData" :model="saveParams" label-width="120px">
        <el-form-item
          prop="mobile"
          :rules="[
            {
              required: true,
              message: '请输入会员手机号',
              trigger: 'change',
            },
          ]"
          label="会员手机号:"
        >
          <el-input
            v-model="saveParams.mobile"
            placeholder="请输入会员手机号"
          />
        </el-form-item>
        <el-form-item
          prop="accountType"
          :rules="[
            {
              required: true,
              message: '请选择账户类型',
              trigger: 'change',
            },
          ]"
          label="账户类型"
        >
          <el-select v-model="saveParams.accountType" placeholder="请选择">
            <el-option label="人民币" :value="0"></el-option>
            <el-option label="澳币" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          prop="amount"
          :rules="[
            {
              required: true,
              message: '请输入充值金额',
              trigger: 'change',
            },
            {
              validator: rules.validateAmount,
              trigger: 'change',
            },
          ]"
          label="充值金额:"
        >
          <el-input v-model="saveParams.amount" placeholder="请输入充值金额" />
        </el-form-item>
        <el-form-item
          prop="paymentMethod"
          :rules="[
            {
              required: true,
              message: '请选择支付方式',
              trigger: 'change',
            },
          ]"
          label="支付方式"
        >
          <el-select v-model="saveParams.paymentMethod" placeholder="请选择">
            <el-option label="支付宝" :value="2"></el-option>
            <el-option label="银行卡" :value="5"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          prop="payTransactionId"
          :rules="[
            {
              required: true,
              message: '请输入支付流水号',
              trigger: 'change',
            },
          ]"
          label="支付流水号:"
        >
          <el-input
            v-model="saveParams.payTransactionId"
            placeholder="请输入支付流水号"
          />
        </el-form-item>
        <el-form-item required :error="errorTip" label="上传支付凭证:">
          <uploadImg
            ref="uploadImg"
            :init-file-list="initFileList"
            :max="1"
            @changeImage="changeImage"
            @onRemove="onRemove"
          />
        </el-form-item>
        <el-form-item label="备注:">
          <el-input
            v-model="saveParams.remarks"
            type="textarea"
            placeholder="请输入备注"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="visible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit()">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
  import uploadImg from '@/components/uploadImg';
  import {
    rechargeApplicationCreate,
    rechargeApplicationUpdate,
  } from '@/api/financialManagement';
  export default {
    components: {
      uploadImg,
    },
    data() {
      const validateAmount = (rule, value, callback) => {
        const reg = /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/;
        if (value && (!reg.test(value) || value == 0)) {
          callback(new Error('金额不得为空且金额需大于0.01'));
        } else {
          callback();
        }
      };
      return {
        visible: false,
        title: '添加',
        errorTip: '',
        initFileList: [],
        rules: {
          validateAmount,
        },
        saveParams: {
          amount: '',
          id: '',
          mobile: '',
          accountType: '',
          payTransactionId: '',
          paymentMethod: '',
          remarks: '',
          voucherId: '',
        },
      };
    },
    methods: {
      onClose() {
        Object.assign(this.$data.saveParams, this.$options.data().saveParams);
        this.$nextTick(function () {
          this.initFileList = [];
          this.$refs.formData.clearValidate();
          this.errorTip = '';
        });
      },
      open(val) {
        if (val) {
          this.title = '修改';
          this.saveParams = this.formatDetail(val);
          this.initFileList = [
            {
              url: val.screenshots,
            },
          ];
        }
        this.visible = true;
      },
      formatDetail(res) {
        return {
          amount: res.amount,
          id: res.id,
          mobile: res.mobile,
          accountType: res.moneyType,
          payTransactionId: res.payTransactionId,
          paymentMethod: res.payment,
          remarks: res.remarks,
          voucherId: res.imageId,
        };
      },
      onRemove() {
        this.saveParams.voucherId = '';
        this.errorTip = '请上传支付凭证';
      },
      changeImage(files) {
        if (files.id) {
          this.saveParams.voucherId = files.id;
          this.errorTip = '';
        }
      },
      handleSubmit() {
        this.$refs.formData.validate(async valid => {
          if (!this.saveParams.voucherId) {
            this.errorTip = '请上传支付凭证';
            return;
          }
          if (!valid) return;
          const saveApi = this.saveParams.id
            ? rechargeApplicationUpdate
            : rechargeApplicationCreate;
          await saveApi(this.saveParams);
          this.$message.success(this.saveParams.id ? '修改成功' : '添加成功');
          this.visible = false;
          this.$emit('onGet');
        });
      },
    },
  };
</script>

<style></style>
