<template>
  <div>
    <el-dialog
      width="520px"
      title="添加"
      :visible.sync="visible"
      @closed="onClose"
    >
      <el-form ref="formData" :model="saveParams" label-width="120px">
        <el-form-item
          prop="mobile"
          :rules="[
            {
              required: true,
              message: '请输入代理手机号',
              trigger: 'change',
            },
          ]"
          label="代理手机号:"
        >
          <el-input
            v-model="saveParams.mobile"
            placeholder="请输入代理手机号"
          />
        </el-form-item>
        <el-form-item
          prop="moneyType"
          :rules="[
            {
              required: true,
              message: '请选择账户类型',
              trigger: 'change',
            },
          ]"
          label="账户类型"
        >
          <el-select v-model="saveParams.moneyType" placeholder="请选择">
            <el-option label="人民币" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          prop="businessType"
          :rules="[
            {
              required: true,
              message: '请选择业务类型',
              trigger: 'change',
            },
          ]"
          label="业务类型"
        >
          <el-select v-model="saveParams.businessType" placeholder="请选择">
            <el-option label="提现失败" :value="0"></el-option>
            <el-option label="试用退款" :value="1"></el-option>
            <el-option label="售后" :value="2"></el-option>
            <el-option label="其他奖励" :value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          prop="amount"
          :rules="[
            {
              required: true,
              message: '请输入充值金额',
              trigger: 'change',
            },
            {
              validator: rules.validateAmount,
              trigger: 'change',
            },
          ]"
          label="充值金额"
        >
          <el-input v-model="saveParams.amount" placeholder="请输入退款金额" />
        </el-form-item>
        <el-form-item label="上传附件:">
          <uploadImg
            ref="uploadFile"
            btn-text="上传文件"
            list-type="text"
            :init-file-list="initFile"
            :max="1"
            @changeImage="changeFile"
            @onRemove="(a, b) => onRemove(a, b, 1)"
          />
        </el-form-item>
        <el-form-item required :error="errorTip" label="充值凭证:">
          <uploadImg
            ref="uploadImg"
            :max="9"
            :init-file-list="initFileList"
            @changeImage="changeImage"
            @onRemove="(a, b) => onRemove(a, b, 2)"
          />
        </el-form-item>
        <el-form-item label="备注:">
          <el-input
            v-model="saveParams.remarks"
            type="textarea"
            placeholder="请输入备注"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="visible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit()">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
  import uploadImg from '@/components/uploadImg';
  import {
    withdrawPayApplyForControllerCreate,
    withdrawPayApplyForControllerModify,
  } from '@/api/financialManagement';
  export default {
    components: {
      uploadImg,
    },
    data() {
      const validateAmount = (rule, value, callback) => {
        const reg = /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/;
        if (!reg.test(value) || value == 0) {
          callback(new Error('金额不得为空且金额需大于0.01'));
        } else {
          callback();
        }
      };
      return {
        visible: false,
        title: '添加',
        errorTip: '',
        initFileList: [],
        initFile: [],
        rules: {
          validateAmount,
        },
        saveParams: {
          amount: '',
          id: '',
          businessType: 0,
          fileId: '',
          mobile: '',
          moneyType: 1,
          remarks: '',
          voucher: [],
        },
      };
    },
    methods: {
      onClose() {
        Object.assign(this.$data.saveParams, this.$options.data().saveParams);
        this.$nextTick(function () {
          this.$refs.formData.clearValidate();
          this.errorTip = '';
          this.initFileList = [];
          this.initFile = [];
        });
      },
      onRemove(files, fileList, key) {
        if (key === 1) {
          this.saveParams.fileId = '';
          return;
        }
        this.saveParams.voucher = fileList.map(item => item.id);
        if (this.saveParams.voucher.length === 0) {
          this.errorTip = '请上传充值凭证';
        }
      },
      changeImage(files, fileList) {
        if (files.id) {
          this.saveParams.voucher = fileList.map(item => item.id);
          this.errorTip = '';
        }
      },
      changeFile(files) {
        if (files.id) {
          this.saveParams.fileId = files.id;
        }
      },
      handleSubmit() {
        this.$refs.formData.validate(async valid => {
          if (!this.saveParams.voucher.length) {
            this.errorTip = '请上传充值凭证';
            return;
          }
          if (!valid) return;
          const saveApi = this.saveParams.id
            ? withdrawPayApplyForControllerModify
            : withdrawPayApplyForControllerCreate;
          await saveApi(this.saveParams);
          this.$message.success(this.saveParams.id ? ' 修改成功' : '添加成功');
          this.visible = false;
          this.$emit('onGet');
        });
      },
      open(val) {
        if (val) {
          this.title = '修改';
          this.saveParams = this.formatDetail(val);
          const payVoucherIds = val.payVoucherIds
            ? JSON.parse(val.payVoucherIds)
            : [];
          const imgList = (val.payVoucher
            ? JSON.parse(val.payVoucher)
            : []
          ).map((item, index) => {
            return {
              url: item,
              id: payVoucherIds[index],
            };
          });
          if (val.file && val.fileId) {
            this.initFile = [
              {
                url: val.file,
                id: val.fileId,
                name: '文件',
              },
            ];
          }

          this.initFileList = imgList;
        }
        this.visible = true;
      },
      formatDetail(res) {
        return {
          amount: res.amount,
          businessType: Number(res.businessType) || 0,
          fileId: res.fileId,
          mobile: res.mobile,
          moneyType: Number(res.moneyType) || 1,
          remarks: res.remarks || '',
          voucher: res.payVoucherIds ? JSON.parse(res.payVoucherIds) : [],
          id: res.id,
        };
      },
    },
  };
</script>

<style></style>
