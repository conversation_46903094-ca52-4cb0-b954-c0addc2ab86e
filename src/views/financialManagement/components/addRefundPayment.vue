<template>
  <div>
    <el-dialog
      width="520px"
      title="添加"
      :visible.sync="visible"
      @closed="onClose"
    >
      <el-form ref="formData" :model="saveParams" label-width="120px">
        <el-form-item
          prop="mobile"
          :rules="[
            {
              required: true,
              message: '请输入代理手机号',
              trigger: 'change',
            },
          ]"
          label="代理手机号:"
        >
          <el-input
            v-model="saveParams.mobile"
            placeholder="请输入代理手机号"
          />
        </el-form-item>
        <el-form-item
          prop="type"
          :rules="[
            {
              required: true,
              message: '请选择账户类型',
              trigger: 'change',
            },
          ]"
          label="账户类型"
        >
          <el-select v-model="saveParams.type" placeholder="请选择">
            <el-option label="人民币" :value="1"></el-option>
            <el-option label="澳币" :value="2"></el-option>
            <el-option label="人民币老货款" :value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          prop="amount"
          :rules="[
            {
              required: true,
              message: '请输入退款金额',
              trigger: 'change',
            },
            {
              validator: rules.validateAmount,
              trigger: 'change',
            },
          ]"
          label="退款金额"
        >
          <el-input v-model="saveParams.amount" placeholder="请输入退款金额" />
        </el-form-item>
        <el-form-item label="上传附件:">
          <uploadImg
            ref="uploadFile"
            btn-text="上传文件"
            list-type="text"
            :init-file-list="initFile"
            :max="1"
            @changeImage="changeFile"
            @onRemove="(a, b) => onRemove(a, b, 1)"
          />
        </el-form-item>
        <el-form-item required :error="errorTip" label="退款凭证:">
          <uploadImg
            ref="uploadImg"
            :max="9"
            :init-file-list="initFileList"
            @onRemove="(a, b) => onRemove(a, b, 2)"
            @changeImage="changeImage"
          />
        </el-form-item>
        <el-form-item label="备注:">
          <el-input
            v-model="saveParams.remarks"
            type="textarea"
            placeholder="请输入备注"
          />
        </el-form-item>
        <el-form-item
          prop="businessType"
          :rules="[
            {
              required: true,
              message: '请选择业务类型',
              trigger: 'change',
            },
          ]"
          label="业务类型"
        >
          <el-select
            v-model="saveParams.businessType"
            placeholder="请选择"
            @change="selectBusinessType"
          >
            <el-option label="只退货款" :value="1"></el-option>
            <el-option label="货款转可提现" :value="2"></el-option>
            <!-- <el-option label="通联充值退款" :value="3"></el-option> -->
            <el-option label="线下银行转账" :value="4"></el-option>
            <el-option label="澳币充值退款" :value="5"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="[2].includes(saveParams.businessType)"
          prop="rechargeAmount"
          :rules="[
            {
              validator: rules.validateAmount,
              trigger: 'change',
            },
          ]"
          label="转可提现金额"
        >
          <el-input
            v-model="saveParams.rechargeAmount"
            placeholder="请输入转可提现金额"
          />
        </el-form-item>
        <!-- <el-form-item
          v-if="[3].includes(saveParams.businessType)"
          label="交易流水号"
        >
          <el-input
            v-model="saveParams.transactionId"
            placeholder="请输入交易流水号"
          />
        </el-form-item>
        <el-form-item
          v-if="[3].includes(saveParams.businessType)"
          label="手续费"
        >
          <el-input v-model="saveParams.changeFee" placeholder="请输入手续费" />
        </el-form-item> -->

        <el-form-item
          v-if="[4, 5].includes(saveParams.businessType)"
          prop="transferFee"
          :rules="[
            {
              required: true,
              message: '请输入转账金额',
              trigger: 'change',
            },
            {
              validator: rules.validateAmount,
              trigger: 'change',
            },
          ]"
          label="转账金额"
        >
          <el-input
            v-model="saveParams.transferFee"
            placeholder="请输入转账金额"
          />
        </el-form-item>
        <el-form-item
          v-if="[4, 5].includes(saveParams.businessType)"
          prop="receiverName"
          :rules="[
            {
              required: true,
              message: '请输入收款人姓名',
              trigger: 'change',
            },
          ]"
          label="收款人姓名"
        >
          <el-input
            v-model="saveParams.receiverName"
            placeholder="请输入收款人姓名"
          />
        </el-form-item>
        <el-form-item
          v-if="[4, 5].includes(saveParams.businessType)"
          prop="cardNo"
          :rules="[
            {
              required: true,
              message: '请输入银行卡号',
              trigger: 'change',
            },
          ]"
          label="银行卡号"
        >
          <el-input v-model="saveParams.cardNo" placeholder="请输入银行卡号" />
        </el-form-item>
        <el-form-item
          v-if="isShowBsb()"
          prop="bsb"
          :rules="[
            {
              required: true,
              message: '请输入bsb',
              trigger: 'change',
            },
            {
              validator: rules.validateBsb,
              trigger: 'change',
            },
          ]"
          label="bsb"
        >
          <el-input v-model="saveParams.bsb" placeholder="请输入bsb" />
        </el-form-item>

        <el-form-item
          v-if="[4].includes(saveParams.businessType) && saveParams.type !== 2"
          prop="bankName"
          :rules="[
            {
              required: true,
              message: '请选择开户行',
              trigger: 'change',
            },
          ]"
          label="开户行"
        >
          <el-select v-model="saveParams.bankName" placeholder="请选择开户行">
            <el-option
              v-for="item in BANK_LIST"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="[4].includes(saveParams.businessType) && saveParams.type !== 2"
          label="开户支行"
        >
          <el-input
            v-model="saveParams.subbranchName"
            placeholder="请输入开户支行"
          />
        </el-form-item>

        <el-form-item
          v-if="[5].includes(saveParams.businessType)"
          prop="rechargeTransaction"
          :rules="[
            {
              required: true,
              message: '请输入充值流水号',
              trigger: 'change',
            },
          ]"
          label="充值流水号"
        >
          <el-input
            v-model="saveParams.rechargeTransaction"
            placeholder="请输入充值流水号"
          />
        </el-form-item>

        <el-form-item>
          <el-button @click="visible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit()">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
  import uploadImg from '@/components/uploadImg';
  import { initSearchParams } from '@/utils';
  import { BANK_LIST } from '@/consts';
  import {
    returnPaymentApplicantCreate,
    returnPaymentApplicantModify,
  } from '@/api/financialManagement';
  export default {
    components: {
      uploadImg,
    },
    data() {
      const validateBsb = (rule, value, callback) => {
        const str = value.replace(/\s/g, '');
        if (str.length !== 6 && value) {
          callback(new Error('bsb长度必须为6位'));
        } else {
          callback();
        }
      };
      const validateAmount = (rule, value, callback) => {
        const reg = /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/;
        if (value && (!reg.test(value) || value == 0)) {
          callback(new Error('金额不得为空且金额需大于0.01'));
        } else {
          callback();
        }
      };
      return {
        visible: false,
        title: '添加',
        errorTip: '',
        initFileList: [],
        initFile: [],
        rules: {
          validateBsb,
          validateAmount,
        },
        BANK_LIST,
        saveParams: {
          album: [],
          amount: '',
          id: '',
          bankName: '',
          bsb: '',
          businessType: 1,
          cardNo: '',
          changeFee: '',
          imageId: '',
          mobile: '',
          receiverName: '',
          rechargeAmount: '',
          rechargeTransaction: '',
          remarks: '',
          subbranchName: '',
          transactionId: '',
          transferFee: '',
          type: 1,
        },
      };
    },

    methods: {
      isShowBsb() {
        const { businessType, type } = this.saveParams;
        if (businessType === 5) {
          return true;
        }

        return type === 2 && businessType === 4;
      },
      onClose() {
        Object.assign(this.$data.saveParams, this.$options.data().saveParams);
        this.$nextTick(function () {
          this.initFileList = [];
          this.initFile = [];
          this.$refs.formData.clearValidate();
        });
      },
      changeImage(files, fileList) {
        console.log(fileList, 'fileList');
        if (fileList.length) {
          this.saveParams.album = fileList.map(item => item.id);
          this.errorTip = '';
        }
      },
      changeFile(files) {
        if (files.id) {
          this.saveParams.imageId = files.id;
        }
      },
      onRemove(files, fileList, key) {
        if (key === 1) {
          this.saveParams.imageId = '';
          return;
        }
        this.saveParams.album = fileList.map(item => item.id);
        if (this.saveParams.album.length === 0) {
          this.errorTip = '至少上传一张凭证图';
        }
      },
      // selectType(key, value) {
      //   if (this.saveParams.businessType === 5 && key !== 2) {
      //     this.saveParams.businessType = 1
      //   }
      //   this.init()
      // },
      selectBusinessType(key, value) {
        this.init();
      },
      init() {
        this.saveParams = {
          ...this.saveParams,
          transferFee: '',
          receiverName: '',
          cardNo: '',
          bsb: '',
          rechargeAmount: '',
          transactionId: '',
          changeFee: '',
          bankName: '',
          subbranchName: '',
        };
      },
      handleSubmit() {
        this.$refs.formData.validate(async valid => {
          if (!this.saveParams.album.length) {
            this.errorTip = '至少上传一张凭证图';
            return;
          }
          if (!valid) return;
          const params = {
            ...this.saveParams,
            cardNo: this.saveParams.cardNo
              ? Number(this.saveParams.cardNo)
              : '',
          };
          const saveApi = params.id
            ? returnPaymentApplicantModify
            : returnPaymentApplicantCreate;
          await saveApi(initSearchParams(params));
          this.$message.success(params.id ? '修改成功' : '添加成功');
          this.visible = false;
          this.$emit('onGet');
        });
      },
      open(val) {
        console.log(val, 'val222');
        if (val) {
          this.title = '修改';
          this.saveParams = this.formatDetail(val);
          this.initFileList = val.proofRefunds ? val.proofRefunds : [];
          this.initFile = val.attachment
            ? [{ ...val.attachment, name: '文件' }]
            : [];
        }
        this.visible = true;
      },
      formatDetail(res) {
        console.log(res, 'oooooooooo');
        const album = (res.proofRefunds ? res.proofRefunds : []).map(
          item => item.id,
        );
        const imageId = res.attachment ? res.attachment.id : '';
        return {
          album,
          amount: res.amount,
          bankName: res.bankName,
          bsb: res.bankOrBsb,
          businessType: res.businessType,
          cardNo: res.cardNo,
          changeFee: res.changeFee,
          id: res.id,
          imageId,
          mobile: res.mobile,
          receiverName: res.receiverName,
          rechargeAmount: res.rechargeAmount,
          rechargeTransaction: res.rechargeTransaction,
          remarks: res.remarks,
          subbranchName: res.subbranchName,
          transactionId: res.transactionId,
          transferFee: res.transferAmount,
          type: res.accountType,
        };
      },
    },
  };
</script>

<style></style>
