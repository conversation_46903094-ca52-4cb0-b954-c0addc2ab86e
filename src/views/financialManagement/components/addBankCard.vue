<template>
  <div>
    <el-dialog
      width="520px"
      title="添加"
      :visible.sync="visible"
      @closed="onClose"
    >
      <el-form ref="formData" :model="saveParams" label-width="120px">
        <el-form-item
          prop="idCode"
          :rules="[
            {
              required: true,
              message: '请输入会员ID_Code',
              trigger: 'change',
            },
          ]"
          label="会员ID_Code:"
        >
          <el-input
            v-model="saveParams.idCode"
            placeholder="请输入会员ID_Code"
          />
        </el-form-item>
        <el-form-item
          prop="bankNo"
          :rules="[
            {
              required: true,
              message: '请输入银行卡号',
              trigger: 'change',
            },
          ]"
          label="银行卡号"
        >
          <el-input v-model="saveParams.bankNo" placeholder="请输入银行卡号" />
        </el-form-item>
        <el-form-item
          prop="bankName"
          :rules="[
            {
              required: true,
              message: '请输入开户行',
              trigger: 'change',
            },
          ]"
          label="开户行:"
        >
          <el-input v-model="saveParams.bankName" placeholder="请输入开户行" />
        </el-form-item>
        <el-form-item
          prop="opener"
          :rules="[
            {
              required: true,
              message: '请输入开户人',
              trigger: 'change',
            },
          ]"
          label="开户人:"
        >
          <el-input v-model="saveParams.opener" placeholder="请输入开户人" />
        </el-form-item>
        <el-form-item
          prop="idNumber"
          :rules="[
            {
              required: true,
              message: '请输入身份证/护照号',
              trigger: 'change',
            },
          ]"
          label="身份证/护照号:"
        >
          <el-input
            v-model="saveParams.idNumber"
            placeholder="请输入身份证/护照号"
          />
        </el-form-item>
        <el-form-item required :error="errorTip" label="银行卡照片:">
          <uploadImg
            ref="uploadImg"
            :max="1"
            @onRemove="onRemove"
            @changeImage="changeImage"
          />
        </el-form-item>
        <el-form-item label="备注:">
          <el-input
            v-model="saveParams.remarks"
            type="textarea"
            placeholder="请输入备注"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="visible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit()">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
  import uploadImg from '@/components/uploadImg';
  import { withdrawalBankCardCreate } from '@/api/financialManagement';
  export default {
    components: {
      uploadImg,
    },
    data() {
      return {
        visible: false,
        errorTip: '',
        saveParams: {
          bankCardImage: '',
          bankName: '',
          bankNo: '',
          from: 0,
          // id: '',
          idCode: '',
          idNumber: '',
          opener: '',
          operatorSSOId: '',
          remarks: '',
        },
      };
    },
    methods: {
      onClose() {
        Object.assign(this.$data.saveParams, this.$options.data().saveParams);
        this.$nextTick(function () {
          this.errorTip = '';
          this.$refs.formData.clearValidate();
          this.$refs.uploadImg.clearFiles();
        });
      },
      open() {
        this.visible = true;
      },
      changeImage(files, fileList) {
        this.saveParams.bankCardImage = files.id;
        this.errorTip = '';
      },
      onRemove() {
        this.saveParams.bankCardImage = '';
        this.errorTip = '请上传银行卡照片';
      },
      handleSubmit() {
        this.$refs.formData.validate(async valid => {
          if (!this.saveParams.bankCardImage) {
            this.errorTip = '请上传银行卡照片';
            return;
          }
          if (!valid) return;
          await withdrawalBankCardCreate({
            ...this.saveParams,
            operatorSSOId: this.$store.state.user.userInfo.id,
          });
          this.$message.success('添加成功');
          this.visible = false;
          this.$emit('onGet');
        });
      },
    },
  };
</script>

<style></style>
