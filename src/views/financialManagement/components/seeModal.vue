<template>
  <div class="see-modal">
    <el-dialog
      width="520px"
      :title="title"
      :visible.sync="visible"
      @closed="onClose"
    >
      <el-form
        ref="formData"
        :model="saveParams"
        label-width="200px"
        label-position="left"
      >
        <el-form-item
          v-for="(item, index) in list"
          v-show="!item.ishide"
          :key="index"
          prop="reason"
          :rules="[
            {
              required: item.required || false,
              message: '请输入',
              trigger: 'change',
            },
          ]"
          :label="`${item.name} :`"
        >
          <span v-if="item.type === 'img'">
            <span v-if="Array.isArray(item.value)">
              <a
                v-for="item1 in item.value"
                :key="isObj(item1) ? item1.url : item1"
                :href="isObj(item1) ? item1.url : item1"
                rel="noopener noreferrer"
                download=""
                target="_blank"
              >
                <img
                  style="width: 120px; height: 120px; margin-top: 20px"
                  :src="isObj(item1) ? item1.url : item1"
                />
              </a>
            </span>
            <span v-else>
              <a
                v-if="item.value"
                :href="item.value"
                rel="noopener noreferrer"
                download=""
                target="_blank"
              >
                <img
                  style="width: 120px; height: 120px; margin-top: 20px"
                  :src="item.value"
                />
              </a>
            </span>
          </span>

          <el-input
            v-else-if="item.type === 'input'"
            v-model="saveParams.reason"
            :placeholder="`请输入${item.name}`"
          />
          <a
            v-else-if="item.type === 'file'"
            :href="item.value"
            rel="noopener noreferrer"
            download=""
            target="_blank"
          >
            下载附件
          </a>
          <span v-else>{{ item.value }}</span>
        </el-form-item>
        <el-form-item v-if="showBtn" style="margin-top: 20px">
          <el-button type="danger" @click="onOK(0)">拒绝</el-button>
          <el-button @click="visible = false">关闭</el-button>
          <el-button type="primary" @click="onOK(1)">通过</el-button>
        </el-form-item>
        <el-form-item v-else style="margin-top: 20px">
          <el-button @click="visible = false">取消</el-button>
          <el-button type="primary" @click="visible = false">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
  import { downloadImage } from '@/utils/downloadImage';
  export default {
    props: {
      showBtn: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        saveParams: {
          reason: '',
        },
        list: [],
        title: '',
        visible: false,
      };
    },
    methods: {
      isObj(obj) {
        return Object.prototype.toString.call(obj) === '[object Object]';
      },
      open({ arr = [], title = '退货款申请查看' }) {
        this.list = arr;
        this.title = title;
        this.visible = true;
      },
      onClose() {
        this.saveParams.reason = '';
        this.$nextTick(function () {
          this.$refs.formData.clearValidate();
        });
      },
      downloadImg(url) {
        downloadImage(url);
      },
      async onOK(type) {
        if (type === 1) {
          const params = {
            refuseToReason: this.saveParams.reason,
            type,
          };
          this.$emit('onOk', params);
          this.visible = false;
          return;
        }
        this.$refs.formData.validate(async valid => {
          if (!valid) return;
          const params = {
            refuseToReason: this.saveParams.reason,
            type,
          };
          this.$emit('onOk', params);
          this.visible = false;
        });
      },
    },
  };
</script>

<style>
  .see-modal .el-form-item--small.el-form-item {
    margin-bottom: 0px;
  }
</style>
