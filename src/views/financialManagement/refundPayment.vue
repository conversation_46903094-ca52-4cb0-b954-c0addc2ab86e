<template>
  <div>
    <searchForm
      type="refundPayment"
      export-url="/cust-bus/returnPaymentApplicant/export"
      export-method="post"
      :review-list="rechargeAuditStatus"
      :business-list="refundPaymentBusinessTypeList"
      export-permission-key="refundPayment-export"
      @onSearch="onSearch"
      @onAdd="handleAddEdit"
    ></searchForm>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <el-button
          slot="reference"
          type="text"
          size="mini"
          @click="handleSee(scope.row)"
        >
          查看
        </el-button>

        <ac-permission-button
          slot-btn="reference"
          node-type="popconfirm"
          title="确定删除该退货款申请吗？"
          btn-text="删除"
          permission-key="refundPayment-delete"
          :disabled="![3, 4, 2].includes(scope.row.status)"
          type="text"
          size="mini"
          @click="handleSubmit(scope.row, '1')"
        ></ac-permission-button>

        <ac-permission-button
          node-type="popconfirm"
          title="确定提交该退货款申请吗？"
          btn-text="提交"
          permission-key="refundPayment-submit"
          slot-btn="reference"
          :disabled="![3, 4, 2].includes(scope.row.status)"
          type="text"
          size="mini"
          @click="handleSubmit(scope.row, '2')"
        ></ac-permission-button>
        <ac-permission-button
          slot="reference"
          :disabled="![3, 4, 2].includes(scope.row.status)"
          btn-text="修改"
          permission-key="refundPayment-edit"
          type="text"
          size="mini"
          @click="handleAddEdit(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <addRefundPayment
      ref="addRefundPayment"
      @onGet="getList()"
    ></addRefundPayment>
    <seeModal ref="seeModal"></seeModal>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import addRefundPayment from './components/addRefundPayment';
  import { formatDetail } from './js/refundPayment';
  import seeModal from './components/seeModal';
  import searchForm from './components/searchForm';
  import {
    returnPaymentApplicantList,
    returnPaymentApplicantDel,
    returnPaymentApplicantSubmit,
  } from '@/api/financialManagement';
  import { initSearchParams, parseTime } from '@/utils';
  import { rechargeAuditStatus, refundPaymentBusinessTypeList } from '@/consts';
  const statusText = [
    '待审核',
    '已审核',
    '已驳回',
    '已撤销',
    '草稿',
    '初审通过，待复审',
  ];
  const businessTypeText = [
    '只退货款',
    '货款转可提现',
    '通联充值退款',
    '线下银行转账',
    '澳币充值退款',
  ];

  export default {
    components: {
      dynamictable,
      addRefundPayment,
      seeModal,
      searchForm,
    },
    data() {
      return {
        rechargeAuditStatus,
        refundPaymentBusinessTypeList,
        search: {},
        options: {
          loading: false,
          border: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        list: [],
        columns: [
          {
            prop: 'id',
            label: '编号',
          },
          {
            prop: 'createdAt',
            label: '创建时间',
            render: ({ createdAt }) => (
              <span>{createdAt ? parseTime(createdAt) : ''}</span>
            ),
          },
          {
            prop: 'applyAdminName',
            label: '申请人',
          },
          {
            prop: 'accountType',
            label: '账户类型',
            render: ({ accountType }) => (
              <span>
                {accountType === 1
                  ? '人民币'
                  : accountType === 2
                  ? '澳币'
                  : '人民币老货款'}
              </span>
            ),
          },
          {
            prop: 'businessType',
            label: '业务类型',
            render: ({ businessType = 1 }) => (
              <span>{businessTypeText[businessType - 1]}</span>
            ),
          },
          {
            prop: 'amount',
            label: '退款金额',
          },
          {
            prop: 'rechargeAmount',
            label: '货款转可提现金额',
          },

          {
            prop: 'transferAmount',
            label: '转账金额',
          },
          {
            prop: 'receiverName',
            label: '开户人',
          },
          {
            prop: 'bankOrBsb',
            label: '开户行/BSB',
          },
          {
            prop: 'cardNo',
            label: '银行卡号',
          },
          {
            prop: 'idCode',
            label: '代理IDcode',
          },
          {
            prop: 'mobile',
            label: '代理手机号',
          },
          {
            prop: 'status',
            label: '状态',
            render: ({ status }) => <span>{statusText[status]}</span>,
          },
          {
            prop: 'checkAdminName',
            label: '审核人',
          },
          {
            prop: 'remarks',
            label: '备注',
          },
          {
            prop: 'operation',
            label: '操作',
            width: '100px',
            fixed: 'right',
            scopedSlots: { customRender: 'operation' },
          },
        ],
      };
    },
    methods: {
      onSearch(e) {
        this.search = e;
        this.getList(true);
      },
      getParams() {
        const status = this.search.status;
        const allStatus = [0, 1, 2, 3, 4, 5]; // 申请列表全部状态
        const params = {
          ...this.search,
          status: status !== undefined ? [status] : allStatus,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };

        return initSearchParams(params);
      },

      async getList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;

        try {
          const res = await returnPaymentApplicantList(params);
          this.options.loading = false;
          this.list = res && res.list ? res.list : [];
          this.pagination.total = res ? res.total : 0;
        } catch (err) {
          this.options.loading = false;
        }
      },
      handleSee(val) {
        console.log(val, 'valv');
        const obj = {
          ...val,
          accountType:
            val.accountType === 1
              ? '人民币'
              : val.accountType === 2
              ? '澳币'
              : '人民币老货款',
          businessType: businessTypeText[val.businessType - 1],
          status: statusText[val.status - 1],
        };
        let arr;
        console.log(val.businessType, 'businessType');
        if (val.businessType === 1) {
          arr = formatDetail(obj).refundPaymentDetails;
        } else {
          arr = formatDetail(obj)[
            `refundPaymentDetails${val.businessType - 1}`
          ];
        }

        this.$refs['seeModal'].open({
          arr,
          title: '退货款申请查看',
        });
      },
      handleAddEdit(val) {
        this.$refs['addRefundPayment'].open(val);
      },

      async handleSubmit({ id }, key) {
        const api =
          key === '1'
            ? returnPaymentApplicantDel
            : returnPaymentApplicantSubmit;
        await api({ id });
        this.$message.success(api === '1' ? '删除成功' : '提交成功');
        this.getList();
      },
    },
  };
</script>
<style lang="scss" scoped></style>
