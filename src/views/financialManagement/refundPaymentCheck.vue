<template>
  <div>
    <searchForm
      type="refundPaymentCheck"
      export-url="/cust-bus/returnPaymentApplicant/export"
      export-method="post"
      :review-list="withdrawalRechargeAuditStatus"
      :business-list="refundPaymentBusinessTypeList"
      export-permission-key="refundPaymentCheck-export"
      @onBatch="handleBatch"
      @onSearch="onSearch"
    ></searchForm>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
      @selection-change="handleSelectionChange"
    >
      <template slot="operation" slot-scope="scope">
        <el-button
          slot="reference"
          type="text"
          size="mini"
          @click="handleSee(scope.row)"
        >
          查看
        </el-button>
        <ac-permission-button
          slot="reference"
          :disabled="scope.row.status !== 5"
          type="text"
          size="mini"
          btn-text="复审"
          permission-key="refundPaymentCheck-review"
          @click="handleSee(scope.row, 1)"
        ></ac-permission-button>
        <ac-permission-button
          slot="reference"
          :disabled="scope.row.status !== 0"
          type="text"
          size="mini"
          btn-text="初审"
          permission-key="refundPaymentCheck-preliminary-review"
          @click="handleSee(scope.row, 2)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <seeModal ref="seeModal" :show-btn="showBtn" @onOk="onOK"></seeModal>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import { formatDetail } from './js/refundPayment';
  import seeModal from './components/seeModal';
  import searchForm from './components/searchForm';
  import {
    returnPaymentApplicantList,
    returnPaymentApplicantAudit,
    returnPaymentApplicantBatchAuditPass,
  } from '@/api/financialManagement';
  import { exportExcel } from '@/api/blob';
  import { downloadFile, parseTime } from '@/utils';
  import {
    withdrawalRechargeAuditStatus,
    refundPaymentBusinessTypeList,
  } from '@/consts';
  const statusText = {
    0: '待初审',
    5: '待复审',
    1: '已通过',
    '-1': '初审驳回',
    '-2': '复审驳回',
  };
  const businessTypeText = [
    '只退货款',
    '货款转可提现',
    '通联充值退款',
    '线下银行转账',
    '澳币充值退款',
  ];

  export default {
    components: {
      dynamictable,
      seeModal,
      searchForm,
    },

    data() {
      return {
        withdrawalRechargeAuditStatus,
        refundPaymentBusinessTypeList,
        search: {},
        showBtn: false,
        isFirst: true,
        options: {
          loading: false,
          border: true,
          mutiSelect: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        selectArr: [], // 选择的列表数据
        list: [],
        columns: [
          {
            prop: 'id',
            label: '编号',
          },
          {
            prop: 'createdAt',
            label: '创建时间',
            render: ({ createdAt }) => (
              <span>{createdAt ? parseTime(createdAt) : ''}</span>
            ),
          },
          {
            prop: 'applyAdminName',
            label: '申请人',
          },
          {
            prop: 'moneyType',
            label: '账户类型',
            render: ({ accountType }) => (
              <span>
                {accountType === 1
                  ? '人民币'
                  : accountType === 2
                  ? '澳币'
                  : '人民币老货款'}
              </span>
            ),
          },
          {
            prop: 'businessType',
            label: '业务类型',
            render: ({ businessType = 1 }) => (
              <span>{businessTypeText[businessType - 1]}</span>
            ),
          },
          {
            prop: 'amount',
            label: '退款金额',
          },
          {
            prop: 'rechargeAmount',
            label: '货款转可提现金额',
          },

          {
            prop: 'transferAmount',
            label: '转账金额',
          },
          {
            prop: 'toOpenAnAccountPerson',
            label: '开户人',
          },
          {
            prop: 'bankOrBsb',
            label: '开户行/BSB',
          },
          {
            prop: 'cardNo',
            label: '银行卡号',
          },
          {
            prop: 'idCode',
            label: '代理IDcode',
          },
          {
            prop: 'mobile',
            label: '代理手机号',
          },
          {
            prop: 'remarks',
            label: '备注',
          },
          {
            prop: 'checkAdminName',
            label: '初审审核人',
          },
          {
            prop: 'firstCheckAt',
            label: '初审时间',
            render: ({ firstCheckAt }) => (
              <span>{firstCheckAt ? parseTime(firstCheckAt) : ''}</span>
            ),
          },
          {
            prop: 'reviewAt',
            label: '复审时间',
            render: ({ reviewAt }) => (
              <span>{reviewAt ? parseTime(reviewAt) : ''}</span>
            ),
          },
          {
            prop: 'status',
            label: '审核状态',
            render: ({ status, reviewAt }) => (
              <span>
                {status === 2
                  ? reviewAt
                    ? '复审驳回'
                    : '初审驳回'
                  : statusText[status]}
              </span>
            ),
          },
          {
            prop: 'operation',
            label: '操作',
            width: '100px',
            fixed: 'right',
            scopedSlots: { customRender: 'operation' },
          },
        ],
      };
    },

    methods: {
      onSearch(e) {
        this.search = e;
        this.getList(true);
      },
      getParams() {
        const status = this.search.status;
        const allStatus = [0, 1, 2, 3, 5]; // 申请列表全部状态
        const params = {
          ...this.search,
          status: status !== undefined ? [status] : allStatus,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        try {
          const res = await returnPaymentApplicantList(params);
          this.options.loading = false;
          this.list = res && res.list ? res.list : [];
          this.pagination.total = res ? res.total : 0;
        } catch (err) {
          this.options.loading = false;
        }
      },
      handleSee(val, key) {
        const obj = {
          ...val,
          accountType:
            val.accountType === 1
              ? '人民币'
              : val.accountType === 2
              ? '澳币'
              : '人民币老货款',
          businessType: businessTypeText[val.businessType - 1],
          status:
            val.status === 2
              ? val.reviewAt
                ? '复审驳回'
                : '初审驳回'
              : statusText[val.status],
        };
        this.showBtn = false;
        this.isFirst = key === 1 ? false : true;
        this.id = val.id;
        let arr;
        if (val.businessType === 1) {
          arr = formatDetail(obj, key ? true : false).refundPaymentDetails;
        } else {
          arr = formatDetail(obj, key ? true : false)[
            `refundPaymentDetails${val.businessType - 1}`
          ];
        }

        if (key) {
          this.showBtn = true;
        }

        this.$refs['seeModal'].open({
          arr,
          title: '退货款审核',
        });
      },
      async onOK(params) {
        console.log(this.isFirst, 'this.isFirst111');
        const saveParams = {
          id: this.id,
          reason: params.refuseToReason,
          status: this.isFirst
            ? params.type == 1
              ? 5
              : 2
            : params.type == 1
            ? 1
            : 2,
        };
        await returnPaymentApplicantAudit(saveParams);
        this.$message({
          message: '操作成功',
          type: 'success',
        });
        this.getList();
      },
      getStatusArr() {
        return {
          isOperation: status => {
            return this.selectArr.every(item => {
              return item.status === status;
            });
          },
          getIds: status => {
            return this.selectArr
              .map(item => {
                if (item.status !== status) {
                  return item.id;
                }
              })
              .filter(i => !!i);
          },
        };
      },
      async handleBatch(key, id) {
        if (!id && this.selectArr.length === 0) {
          return this.$message.error('请选择订单');
        }
        const getStatusArr = this.getStatusArr();

        if (
          (key === 'first' && !getStatusArr.isOperation(0)) ||
          (key === 'tow' && !getStatusArr.isOperation(5))
        ) {
          const errArr =
            key === 'first' ? getStatusArr.getIds(0) : getStatusArr.getIds(5);
          this.$message.error(
            `${errArr}该笔退款申请记录状态已经改变，目前无需审核`,
          );
          return;
        }
        const ids = this.selectArr.map(item => item.id);
        const params = {
          status: key === 'first' ? 5 : 1,
          ids,
        };
        await returnPaymentApplicantBatchAuditPass(params);
        this.$message({
          message: '操作成功',
          type: 'success',
        });
        this.getList();
      },
      handleSelectionChange(val) {
        this.selectArr = val;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
