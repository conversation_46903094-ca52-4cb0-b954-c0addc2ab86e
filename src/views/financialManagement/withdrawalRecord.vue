<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-30 14:34:27
 * @LastEditTime: 2022-11-18 17:19:09
 * @LastEditors: dddd
 * @Reference: 
-->
<template>
  <div>
    <div>
      <el-form ref="form" :model="form" label-width="80px" inline>
        <el-form-item label="申请时间:" prop="applyTime">
          <el-date-picker
            v-model="form.applyTime"
            type="datetimerange"
            range-separator="至"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="['00:00:00', '23:59:59']"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="代理ID:" prop="userIdCode">
          <el-input
            v-model="form.userIdCode"
            placeholder="请输入代理ID"
          ></el-input>
        </el-form-item>
        <el-form-item label="卡号:" prop="bankNo">
          <el-input v-model="form.bankNo" placeholder="请输入卡号"></el-input>
        </el-form-item>
        <el-form-item label="手机号码:" prop="mobile">
          <el-input
            v-model="form.mobile"
            placeholder="请输入手机号码"
          ></el-input>
        </el-form-item>
        <el-form-item label="金额:" prop="amount">
          <el-input v-model="form.amount" placeholder="请输入金额"></el-input>
        </el-form-item>
        <el-form-item label="提现状态:" prop="withdrawalState">
          <el-select v-model="form.withdrawalState" clearable>
            <el-option
              v-for="item in statusList"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div style="float: right; margin-bottom: 15px">
      <ac-permission-button
        btn-text="查询"
        permission-key="overseasWithdrawalPay-search"
        @click="search(1)"
      ></ac-permission-button>
      <el-button type="primary" @click="reset">重置</el-button>
      <ac-permission-button
        btn-text="批量打款"
        permission-key="overseasWithdrawalPay-full-payment"
        @click="allPayClick"
      ></ac-permission-button>
      <ac-permission-button
        btn-text="导出"
        permission-key="overseasWithdrawalPay-export"
        @click="doExport"
      ></ac-permission-button>
    </div>
    <div>提现总金额：{{ totalAmount }} 批量处理剩余数量：{{ batchCount }}</div>
    <div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        :header-cell-style="{ 'text-align': 'center' }"
        :cell-style="{ 'text-align': 'center' }"
      >
        <el-table-column prop="id" label="提现ID"></el-table-column>
        <el-table-column prop="userId" label="掌柜ID"></el-table-column>
        <el-table-column prop="nickname" label="掌柜昵称"></el-table-column>
        <el-table-column prop="mobile" label="掌柜手机"></el-table-column>
        <el-table-column prop="bankName" label="开户银行"></el-table-column>
        <el-table-column
          prop="bankBranchName"
          label="开户支行"
        ></el-table-column>
        <el-table-column prop="name" label="户名"></el-table-column>
        <el-table-column prop="bankNo" label="卡号"></el-table-column>
        <el-table-column prop="amount" label="提现金额"></el-table-column>
        <el-table-column prop="queryRemark" label="交易结果"></el-table-column>
        <el-table-column prop="createdAt" label="申请时间"></el-table-column>
        <el-table-column prop="paidAt" label="打款时间"></el-table-column>
        <el-table-column prop="status" label="状态"></el-table-column>
        <el-table-column prop="checkAdminName" label="操作人"></el-table-column>
        <el-table-column
          min-width="100"
          max-width="300"
          fixed="right"
          label="操作"
        >
          <template slot-scope="{ row }">
            <ac-permission-button
              v-if="row.isShowToProcess"
              slot="reference"
              type="text"
              size="small"
              btn-text="处理"
              permission-key="overseasWithdrawalPay-make-payment"
              @click="payApply(row.id)"
            ></ac-permission-button>
            <ac-permission-button
              v-if="row.isShowToProcess"
              slot="reference"
              type="text"
              size="small"
              btn-text="驳回"
              permission-key="overseasWithdrawalPay-make-reject"
              @click="reject(row.id)"
            ></ac-permission-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page.pageNo"
        :page-sizes="[10, 20, 50]"
        :page-size="page.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <el-dialog
      title="交易确认"
      :visible.sync="playWithConfirmationDialog"
      width="400px"
      @closed="onClose"
    >
      <div>
        本次交易共选{{
          playWithConfirmationInfo.selectNumberOf
        }}条记录，金额合计{{ playWithConfirmationInfo.aggregateAmount }}
      </div>
      <div>
        渠道账户余额：{{ playWithConfirmationInfo.dtChannelAccountBalance }}
      </div>
      <div slot="footer">
        <el-button @click="playWithConfirmationDialog = false">取 消</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="showPayDialogVisiable"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="驳回确认"
      :visible.sync="rejectWithConfirmationDialog"
      width="400px"
      @closed="onClose"
    >
      <div style="margin-top: 20px">
        <span style="color: red">*</span>
        <span>驳回描述：</span>
        <el-input
          v-model="rejectRemark"
          style="width: 244px"
          placeholder="请填写驳回描述"
        ></el-input>
      </div>
      <div slot="footer">
        <el-button @click="rejectWithConfirmationDialog = false">
          取 消
        </el-button>
        <el-button :loading="loading" type="primary" @click="confirmReject">
          确 定
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="交易确认"
      :visible.sync="payDialogVisiable"
      width="400px"
      @closed="onClose"
    >
      <div>
        <h4>确认打款？</h4>
        <div>
          <el-select
            v-model="applyPhone"
            style="width: 244px"
            placeholder="请选择手机号"
          >
            <el-option
              v-for="item in mobilePhoneList"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>
          <ac-count-down
            :seconds="180"
            :turn-on="turnOn"
            style="margin-left: 20px; display: inline-block"
            @handelSend="getIdentifyingCode(5)"
          >
            获取验证码
          </ac-count-down>
        </div>
        <div style="margin-top: 20px">
          <el-input
            v-model="identifyingCode"
            style="width: 244px"
            placeholder="请填写验证码"
          ></el-input>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="payDialogVisiable = false">取 消</el-button>
        <el-button
          :loading="loading"
          :disabled="!identifyingCode"
          type="primary"
          @click="confirmApply"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="全量打款确认"
      :visible.sync="allPayVisiable"
      width="500px"
      @closed="onClose"
    >
      <div>
        <div>
          <el-select
            v-model="applyPhone"
            style="width: 244px"
            placeholder="请选择手机号"
          >
            <el-option
              v-for="item in mobilePhoneList"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>
          <!-- <el-button
            type="primary"
            style="margin-left: 20px"
            @click="getIdentifyingCode(6)"
          >
            获取验证码
            
          </el-button> -->
          <ac-count-down
            style="margin-left: 20px; display: inline-block"
            :seconds="180"
            :turn-on="turnOn2"
            @handelSend="getIdentifyingCode(6)"
          >
            获取验证码
          </ac-count-down>
        </div>
        <div style="margin-top: 20px">
          <el-input
            v-model="allPayIdentifyingCode"
            style="width: 244px"
            placeholder="请填写验证码"
          ></el-input>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="allPayVisiable = false">取 消</el-button>
        <el-button
          :loading="allPayLoading"
          type="primary"
          @click="allPayConfirm"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    accessToVerifyPhoneNumber,
    notSinceCertificationBatchSms,
    validationCode,
    playWithConfirmation,
    listWidthdraw,
    listConfig,
    withdrawalExport,
    rejectWidthdraw,
  } from '@/api/financialManagement';
  import { setInitData } from '@/utils/index.js';
  import { exportExcel } from '@/api/blob';
  import { isNumber } from '@/utils/validate';
  import { downloadFile } from '@/utils';

  import store from '@/store';

  export default {
    name: 'CashWithdrawal',

    data() {
      return {
        turnOn: false,
        turnOn2: false,
        form: {
          applyTime: setInitData(7, '{y}-{m}-{d} {h}:{i}:{s}'),
          userIdCode: '',
          mobile: '',
          amount: '',
          bankNo: '',
          withdrawalState: '',
        },
        statusList: [],
        mobilePhoneList: [],
        tableData: [],
        page: {
          pageNo: 1,
          limit: 10,
        },
        playWithConfirmationInfo: {
          //选中记录数量
          selectNumberOf: '', //金额合计
          aggregateAmount: '', //渠道账户余额
          abmChannelAccountBalance: '',
          dtChannelAccountBalance: '',
        },
        batchCount: 0,
        totalAmount: 0,
        total: 0,
        dialogVisible: false,
        rejectWithConfirmationDialog: false,
        type: '',
        rejectId: null,
        rejectRemark: '',
        withdrawalId: '',
        detailData: {}, //详情
        currentRow: null, // 当前数据列
        showDialog: false,
        payDialogVisiable: false, //审核确认弹窗
        applyPhone: '', //申请的手机
        applyNo: '', //申请的单号
        identifyingCode: '', //验证码
        loading: false, //确认按钮
        tableLoading: false,
        playWithConfirmationDialog: false,
        allPayVisiable: false, //全量打款弹窗
        allPayIdentifyingCode: '', //全量打款验证码
        allPayLoading: false, //全量打款loading
        allPayData: {
          subjectInfoList: [],
        }, //全量打款数据

        uploadLoading: false, //上传laoding

        uploadData: {
          appId: 'abmau',
          timeStamp: Date.now(),
        },
      };
    },
    created() {
      this.fetch();
      this.search();
    },
    methods: {
      statusChange(row) {
        this.showDialog = true;
        this.currentRow = row;
      },
      onClose() {
        this.$nextTick(function () {
          this.turnOn = false;
          this.turnOn2 = false;
          this.applyPhone = '';
          this.identifyingCode = '';
          this.allPayIdentifyingCode = '';
        });
      },
      //获取下拉框
      async fetch() {
        const params = {
          approvers: store.state.user.userInfo.id,
        };
        const res = await accessToVerifyPhoneNumber(params);
        if (res) {
          this.mobilePhoneList = res || [];
        }
        const { applyTime = [], ...form } = this.form;
        const data = {
          applyAtStart: applyTime ? applyTime[0] : '',
          applyAtEnd: applyTime ? applyTime[1] : '',
          ...form,
        };
        console.log(data, 'ssss');
        const result = await listConfig(data);
        if (result) {
          this.statusList = result.withdrawalStateList;
          this.batchCount = result.batchCount;
          this.totalAmount = result.totalAmount;
        }
      },
      //查询
      async search(pageNo = 0) {
        if (pageNo) {
          this.page.pageNo = pageNo;
        }
        const { applyTime = [], ...form } = this.form;
        const data = {
          applyAtStart: applyTime ? applyTime[0] : '',
          applyAtEnd: applyTime ? applyTime[1] : '',
          ssoUserId: store.state.user.userInfo.id,
          ...this.page,
          ...form,
        };
        try {
          this.tableLoading = true;
          const res = await listWidthdraw(data);
          this.tableLoading = false;
          if (res) {
            this.tableData = res.records || [];
            this.total = res.total || 0;
          }
        } catch (err) {
          this.tableLoading = false;
        }
        this.fetch();
      },
      //重置
      reset() {
        this.$refs['form'].resetFields();
      },

      async doExport() {
        const { applyTime = [], ...form } = this.form;
        const params = {
          applyAtStart: applyTime ? applyTime[0] : '',
          applyAtEnd: applyTime ? applyTime[1] : '',
          ssoUserId: store.state.user.userInfo.id,
          ...form,
        };
        const exportApi = '/widthdraw/listExport';
        exportExcel(params, `/api/cust-bus/${exportApi}`, 'get').then(res => {
          if (res) {
            downloadFile(res.data, '提现列表', 'csv');
          } else {
            // this.$message.error('暂无数据')
          }
        });
      },
      //全量打款
      async allPayClick() {
        const { applyTime } = this.form;
        if (!applyTime) {
          this.$message.warning('请先选择时间');
          return;
        }
        this.applyPhone = '';
        this.playWithConfirmation(6);
      },

      //点击审核通过
      async payApply(applyNo) {
        this.withdrawalId = applyNo;
        this.mobile = '';
        this.playWithConfirmation(5);
      },
      async playWithConfirmation(type) {
        const { applyTime = [], ...form } = this.form;
        if (!applyTime) {
          this.$message.warning('请先选择申请时间');
          return;
        }
        this.type = type;
        const params = {
          applyAtStart: applyTime ? applyTime[0] : '',
          applyAtEnd: applyTime ? applyTime[1] : '',
          type: type,
          withdrawId: this.withdrawalId,
          ssoUserId: store.state.user.userInfo.id,
          ...form,
        };
        try {
          const res = await playWithConfirmation(params);
          if (res) {
            this.playWithConfirmationInfo = res;
          }
          this.playWithConfirmationDialog = true;
        } catch (err) {}
      },
      //点击获取验证码
      async getIdentifyingCode(playWithConfirmationSendPhoneType) {
        if (!this.applyPhone) {
          this.$message.warning('请先选择手机号');
          return;
        }
        if (playWithConfirmationSendPhoneType == 5) {
          this.turnOn = true;
        } else {
          this.turnOn2 = true;
        }
        const params = {
          approvers: store.state.user.userInfo.id,
          approversName: store.state.user.userName,
          phone: this.applyPhone,
          type: playWithConfirmationSendPhoneType,
        };
        try {
          const res = await notSinceCertificationBatchSms(params);
          if (res === null) {
            this.$message.success('获取验证码成功');
          }
        } catch (err) {}
      },
      //确认审核通过
      async confirmApply() {
        if (!this.applyPhone) {
          this.$message.warning('请先选择手机号');
          return;
        }
        // this.loading = true;
        const data = {
          type: this.type,
          withdrawId: this.withdrawalId,
          approvers: store.state.user.userInfo.id,
          approversName: store.state.user.username,
          phone: this.applyPhone,
          withdrawalState: 0,
          code: this.identifyingCode,
        };
        const res = await validationCode(data);
        this.$message.success('打款成功');
        this.payDialogVisiable = false;
        this.search();
        // this.loading = false;
      },
      //全量打款通过
      async allPayConfirm() {
        const { applyTime = [], ...form } = this.form;
        if (!applyTime) {
          this.$message.warning('请先选择申请时间');
          return;
        }
        if (!this.applyPhone) {
          this.$message.warning('请先选择手机号');
          return;
        }
        // this.allPayLoading = true;
        const data = {
          applyAtStart: applyTime ? applyTime[0] : '',
          applyAtEnd: applyTime ? applyTime[1] : '',
          withdrawalState: 0,
          userIdCode: this.userIdCode ? this.userIdCode : '',
          mobile: this.mobile ? this.mobile : '',
          bankNo: this.bankNo ? this.bankNo : '',
          amount: this.amount ? this.amount : '',
          phone: this.applyPhone,
          type: this.type,
          approvers: store.state.user.userInfo.id,
          approversName: store.state.user.username,
          code: this.allPayIdentifyingCode,
        };
        await validationCode(data);
        this.$message.success('打款成功');
        this.allPayVisiable = false;
        this.search();
      },
      showPayDialogVisiable() {
        this.playWithConfirmationDialog = false;
        if (this.type == 5) {
          this.payDialogVisiable = true;
        }
        if (this.type == 6) {
          this.allPayVisiable = true;
        }
      },
      reject(id) {
        this.rejectId = id;
        this.rejectWithConfirmationDialog = true;
      },

      async confirmReject() {
        if (!this.rejectRemark) {
          this.$message.error('请先输入驳回备注');
          return false;
        }
        let info = { id: this.rejectId, remark: this.rejectRemark };
        const res = await rejectWidthdraw(info);

        this.$message.success('驳回成功');
        this.rejectWithConfirmationDialog = false;
        this.rejectId = null;
        this.rejectRemark = null;
        this.search();
      },
      onProgress() {
        this.uploadLoading = true;
      },
      //回传
      onImportSuccess(response) {
        this.uploadLoading = false;
        if (response.msg) {
          this.$message.error(response.msg);
        } else {
          this.$message.success('任务正在处理中，请稍后到任务中心查看');
        }
      },
      onError() {
        this.uploadLoading = false;
      },
      //分页
      handleCurrentChange(e) {
        this.page.pageNo = e;
        this.search();
      },
      handleSizeChange(e) {
        this.page.limit = e;
        this.handleCurrentChange(1);
      },
    },
  };
</script>

<style lang="scss" scoped>
  /deep/ .el-descriptions--small {
    font-size: 14px;
  }
  /deep/ .is_uploading {
    display: none !important;
  }
  /deep/ .el-upload-list {
    display: none !important;
  }
</style>
