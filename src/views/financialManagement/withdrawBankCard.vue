<template>
  <div>
    <el-form inline>
      <el-form-item label="会员ID_Code:">
        <el-input
          v-model="searchParams.idCode"
          placeholder="请输入会员ID_Code"
        />
      </el-form-item>
      <el-form-item label="会员手机号:">
        <el-input v-model="searchParams.phone" placeholder="请输入会员手机号" />
      </el-form-item>
      <el-form-item label="来源:">
        <el-select v-model="searchParams.from" placeholder="请选择">
          <el-option label="全部" value=""></el-option>
          <el-option label="用户绑卡" :value="1"></el-option>
          <el-option label="后台绑卡" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="银行卡号:">
        <el-input v-model="searchParams.bankNo" placeholder="请输入银行卡号" />
      </el-form-item>
      <el-form-item label="开户人:">
        <el-input v-model="searchParams.opener" placeholder="请输入开户人" />
      </el-form-item>
      <el-form-item label="身份证/护照号:">
        <el-input
          v-model="searchParams.idNumber"
          placeholder="请输入身份证/护照号"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <ac-permission-button
          btn-text="添加"
          permission-key="withdrawBankCard-add"
          type="primary"
          @click="onAdd"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          btn-text="备注"
          permission-key="withdrawBankCard-remark"
          type="text"
          size="mini"
          @click="addRemarks(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <addBankCard ref="addBankCard" @onGet="getList(true)"></addBankCard>
    <el-dialog
      width="400px"
      title="添加备注"
      :visible.sync="visible"
      @closed="onClose"
    >
      <el-form ref="formData" :model="addRemarkParams">
        <el-form-item label="备注">
          <el-input
            v-model="addRemarkParams.remarks"
            type="textarea"
            :rows="4"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" style="text-align: center" class="dialog-footer">
        <el-button type="primary" @click="onOK">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import addBankCard from './components/addBankCard';
  import {
    withdrawalBankCardList,
    withdrawalBankCardNote,
  } from '@/api/financialManagement';
  import { initSearchParams } from '@/utils';
  export default {
    components: {
      dynamictable,
      addBankCard,
    },
    data() {
      return {
        searchParams: {
          bankNo: '',
          from: '',
          idCode: '',
          idNumber: '',
          opener: '',
          phone: '',
        },
        addRemarkParams: {
          remarks: '',
          id: '',
        },
        visible: false,
        options: {
          loading: false,
          border: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        list: [],
        columns: [
          {
            prop: 'idCode',
            label: '会员ID_Code',
          },
          {
            prop: 'phone',
            label: '会员手机号',
          },
          {
            prop: 'bankNo',
            label: '银行卡号',
          },
          {
            prop: 'bankName',
            label: '开户银行',
          },
          {
            prop: 'bankCardPhoto',
            label: '银行卡照片',
            render: row => (
              <span>
                {row.bankCardPhoto ? (
                  <img
                    style="width: 120px; height: 120px; margin-top: 10px"
                    src={row.bankCardPhoto}
                  />
                ) : (
                  ''
                )}
              </span>
            ),
          },
          {
            prop: 'name',
            label: '开户人',
          },
          {
            prop: 'idCard',
            label: '身份证/护照',
          },
          {
            prop: 'from',
            label: '来源',
            // render: row => (
            //   <span>{row.from == 0 ? '用户绑卡' : '后台绑卡'}</span>
            // ),
          },
          {
            prop: 'remarks',
            label: '备注',
          },
          {
            prop: 'operator',
            label: '操作人',
          },
          {
            prop: 'operation',
            label: '操作',
            scopedSlots: { customRender: 'operation' },
          },
        ],
      };
    },

    created() {
      this.getList(true);
    },
    methods: {
      getParams() {
        return {
          ...this.searchParams,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
      },

      async getList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        try {
          const res = await withdrawalBankCardList(initSearchParams(params));
          this.options.loading = false;
          this.list = res && res.list ? res.list : [];
          this.pagination.total = res ? res.total : 0;
        } catch (err) {
          this.options.loading = false;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );

        this.getList(true);
      },
      onAdd() {
        this.$refs['addBankCard'].open();
      },
      onClose() {
        Object.assign(
          this.$data.addRemarkParams,
          this.$options.data().addRemarkParams,
        );
        this.$nextTick(function () {
          this.$refs.formData.clearValidate();
        });
      },
      addRemarks(info) {
        this.visible = true;
        this.addRemarkParams = { remarks: info.remarks, id: info.id };
      },
      async onOK() {
        await withdrawalBankCardNote(this.addRemarkParams);
        this.$message.success('添加成功');
        this.getList();
        this.visible = false;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
