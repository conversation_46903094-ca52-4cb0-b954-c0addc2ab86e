<template>
  <div>
    <searchForm
      type="withdrawalRechargeAudit"
      :review-list="withdrawalRechargeAuditStatus"
      :business-list="businessTypeList"
      export-method="get"
      export-url="/cust-bus/withdrawPayApplyFor/export"
      export-permission-key="withdrawalRechargeAudit-export"
      @onBatch="handleBatch"
      @onSearch="onSearch"
    ></searchForm>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
      @selection-change="handleSelectionChange"
    >
      <template slot="operation" slot-scope="scope">
        <el-button
          slot="reference"
          type="text"
          size="mini"
          @click="handleSee(scope.row)"
        >
          查看
        </el-button>
        <el-button
          slot="reference"
          :disabled="scope.row.status !== '0'"
          type="text"
          size="mini"
          @click="handleSee(scope.row, 1)"
        >
          初审
        </el-button>
        <el-button
          slot="reference"
          :disabled="scope.row.status !== '5'"
          type="text"
          size="mini"
          @click="handleSee(scope.row, 2)"
        >
          复审
        </el-button>
      </template>
    </dynamictable>
    <seeModal ref="seeModal" :show-btn="showBtn" @onOk="onOk"></seeModal>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import { formatDetail } from './js/index';
  import seeModal from './components/seeModal';
  import searchForm from './components/searchForm';
  import {
    withdrawPayApplyForControllerList,
    withdrawPayApplyForControllerBatchAuditPass,
    withdrawPayApplyForControllerBatchAudit,
  } from '@/api/financialManagement';
  import { withdrawalRechargeAuditStatus, businessTypeList } from '@/consts';

  const businessTypeText = [
    '提现失败',
    '试用退款',
    '售后',
    '其它奖励',
    '市场补贴服务费充值',
    '教育奖金服务费充值',
  ];

  const statusText = {
    0: '待初审',
    5: '待复审',
    1: '已通过',
    '-1': '初审驳回',
    '-2': '复审驳回',
  };

  export default {
    components: {
      dynamictable,
      seeModal,
      searchForm,
    },

    data() {
      return {
        withdrawalRechargeAuditStatus,
        businessTypeList,
        showBtn: false,
        isFirst: true, // 是否初审
        id: '', // 列表id
        search: {},
        options: {
          loading: false,
          border: true,
          mutiSelect: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        selectArr: [],
        list: [],
        columns: [
          {
            prop: 'id',
            label: '编号',
          },
          {
            prop: 'source',
            label: '订单来源',
            render: ({ source }) => (
              <span>{source === '1' ? '手动录入' : '系统生成'}</span>
            ),
          },
          {
            prop: 'createdAt',
            label: '创建时间',
          },
          {
            prop: 'applyAdminName',
            label: '申请人',
          },
          {
            prop: 'businessType',
            label: '业务类型',
            render: ({ businessType = 1 }) => (
              <span>{businessTypeText[businessType]}</span>
            ),
          },
          {
            prop: 'amount',
            label: '金额',
          },
          {
            prop: 'idCode',
            label: '代理IDcode',
          },

          {
            prop: 'mobile',
            label: '代理手机号',
          },
          {
            prop: 'refundAccount',
            label: '退款账户',
            render: ({ refundAccount }) => (
              <span>
                {refundAccount === 'xm'
                  ? '厦门银行存管账户'
                  : refundAccount === 'sy'
                  ? '可提现余额户'
                  : ''}
              </span>
            ),
          },
          {
            prop: 'remarks',
            label: '备注',
          },
          {
            prop: 'firstCheckAdminName',
            label: '初审审核人',
          },

          {
            prop: 'firstTime',
            label: '初审时间',
          },

          {
            prop: 'reviewTime',
            label: '复审时间',
          },

          {
            prop: 'status',
            label: '状态',
            render: ({ status, checkAdminName }) => (
              <span>{this.getStatusTest(status, checkAdminName)}</span>
            ),
          },

          {
            prop: 'operation',
            label: '操作',
            width: '100px',
            fixed: 'right',
            scopedSlots: { customRender: 'operation' },
          },
        ],
      };
    },
    methods: {
      getStatusTest(d, checkAdminName) {
        let x;
        switch (d) {
          case '0':
            x = '待初审';
            break;
          case '1':
            x = '已通过';
            break;
          case '2':
            if (!checkAdminName) {
              x = '初审驳回';
            } else {
              x = '复审驳回';
            }
            break;
          case '3':
            x = '已撤销';
            break;
          case '4':
            x = '草稿';
            break;
          case '5':
            x = '待复审';
            break;
        }
        return x;
      },
      onSearch(e) {
        this.search = e;
        this.getList(true);
      },
      getParams() {
        const params = {
          ...this.search,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },

      async getList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        try {
          const res = await withdrawPayApplyForControllerList(params);
          this.options.loading = false;
          this.list = res.list ? res.list : [];
          this.pagination.total = res ? res.total : 0;
        } catch (err) {
          this.options.loading = false;
        }
      },

      handleSee(val, key) {
        console.log(key, 'oooo');
        const obj = {
          ...val,
          moneyType: val.moneyType == 1 ? '人民币' : '澳币',
          businessType: businessTypeText[val.businessType],
          status: this.getStatusTest(val.status, val.checkAdminName),
        };
        this.showBtn = false;
        this.isFirst = key === 1 ? true : false;
        this.id = val.id;
        if (key) {
          this.showBtn = true;
        }
        let arr = formatDetail(
          obj,
          key ? 'input' : false,
          'withdrawalRechargeAudit',
        ).withdrawalRechargeAudit;
        this.$refs['seeModal'].open({
          arr,
          title: '充值收益申请查看',
        });
      },
      async onOk(params) {
        const saveParams = {
          id: this.id,
          reason: params.refuseToReason,
          status: this.isFirst ? params.type : params.type == 1 ? 2 : 3,
        };
        await withdrawPayApplyForControllerBatchAudit(saveParams);
        this.$message({
          message: '操作成功',
          type: 'success',
        });
        this.getList();
      },
      getStatusArr(id) {
        return this.selectArr.every(item => {
          return item.status === id;
        });
      },
      async handleBatch(key, id) {
        console.log(key, id, 'oooooooo');
        if (!id && this.selectArr.length === 0) {
          return this.$message.error('请选择订单');
        }
        if (
          (key === 'first' && !this.getStatusArr('0')) ||
          (key === 'tow' && !this.getStatusArr('5'))
        ) {
          this.$message.error(
            '你选择了不可执行该操作的记录，请检查后重新选择再操作！',
          );
          return;
        }
        const ids = this.selectArr.map(item => item.id);
        const params = {
          status: key === 'first' ? 1 : 2,
          ids,
        };
        await withdrawPayApplyForControllerBatchAuditPass(params);
        this.$message({
          message: '操作成功',
          type: 'success',
        });
        this.getList();
      },
      handleSelectionChange(val) {
        this.selectArr = val;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
