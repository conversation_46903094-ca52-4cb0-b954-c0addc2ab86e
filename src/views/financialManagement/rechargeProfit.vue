<template>
  <div>
    <searchForm
      type="rechargeProfit"
      :review-list="getRechargeAuditStatus()"
      :business-list="getBusinessTypeLists()"
      @onSearch="onSearch"
      @onAdd="handleAddEdit"
    ></searchForm>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <el-button
          slot="reference"
          type="text"
          size="mini"
          @click="handleSee(scope.row)"
        >
          查看
        </el-button>
        <ac-permission-button
          slot-btn="reference"
          node-type="popconfirm"
          title="确定删除提现充值申请吗？"
          btn-text="删除"
          permission-key="rechargeProfit-delete"
          :disabled="!['3', '4', '2'].includes(scope.row.status)"
          type="text"
          size="mini"
          @click="handleDel(scope.row)"
        ></ac-permission-button>
        <ac-permission-button
          node-type="popconfirm"
          title="确定提交提现充值申请吗？"
          btn-text="提交"
          permission-key="rechargeProfit-submit"
          slot-btn="reference"
          :disabled="!['3', '4', '2'].includes(scope.row.status)"
          type="text"
          size="mini"
          @click="handleSubmit(scope.row)"
        ></ac-permission-button>
        <ac-permission-button
          slot="reference"
          type="text"
          size="mini"
          btn-text="修改"
          permission-key="rechargeProfit-edit"
          :disabled="!['3', '4', '2'].includes(scope.row.status)"
          @click="handleAddEdit(scope.row)"
        >
          修改
        </ac-permission-button>
      </template>
    </dynamictable>
    <addRechargeProfit
      ref="addRechargeProfit"
      @onGet="getList()"
    ></addRechargeProfit>
    <seeModal ref="seeModal"></seeModal>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import addRechargeProfit from './components/addRechargeProfit';
  import searchForm from './components/searchForm';
  import { formatDetail } from './js/index';
  import seeModal from './components/seeModal';
  import { initSearchParams } from '@/utils';
  import { rechargeAuditStatus, businessTypeList } from '@/consts';
  import {
    withdrawPayApplyForControllerList,
    withdrawPayApplyForControllerDel,
    withdrawPayApplyForControllerSubmit,
  } from '@/api/financialManagement';
  const statusText = ['待审核', '已审核', '已驳回', '已撤销', '草稿'];
  const businessTypeText = ['提现失败', '试用退款', '售后', '其它奖励'];

  export default {
    components: {
      dynamictable,
      addRechargeProfit,
      seeModal,
      searchForm,
    },
    data() {
      return {
        rechargeAuditStatus,
        businessTypeList,
        search: {},
        options: {
          loading: false,
          border: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        list: [{ id: 1 }],
        columns: [
          {
            prop: 'id',
            label: '编号',
          },
          {
            prop: 'createdAt',
            label: '创建时间',
          },
          {
            prop: 'applyAdminName',
            label: '申请人',
          },
          {
            prop: 'moneyType',
            label: '账户类型',
            render: row => (
              <span>{row.moneyType === '1' ? '人民币' : '澳币'}</span>
            ),
          },
          {
            prop: 'businessType',
            label: '业务类型',
            render: ({ businessType }) => (
              <span>{businessTypeText[businessType]}</span>
            ),
          },
          {
            prop: 'amount',
            label: '金额',
          },
          {
            prop: 'idCode',
            label: '代理IDcode',
          },

          {
            prop: 'mobile',
            label: '代理手机号',
          },
          {
            prop: 'status',
            label: '状态',
            render: ({ status }) => <span>{statusText[status]}</span>,
          },
          {
            prop: 'checkAdminName',
            label: '审核人',
          },
          {
            prop: 'remarks',
            label: '备注',
          },
          {
            prop: 'operation',
            label: '操作',
            width: '100px',
            fixed: 'right',
            scopedSlots: { customRender: 'operation' },
          },
        ],
      };
    },
    methods: {
      getBusinessTypeLists() {
        return this.businessTypeList
          .map(item => {
            if (![4, 5].includes(item.value)) {
              return item;
            }
          })
          .filter(i => !!i);
      },
      getRechargeAuditStatus() {
        return this.rechargeAuditStatus
          .map(item => {
            if (![3].includes(item.value)) {
              return item;
            }
          })
          .filter(i => !!i);
      },
      onSearch(e) {
        this.search = e;
        this.getList(true);
      },
      getParams() {
        const params = {
          ...this.search,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return initSearchParams(params);
      },

      async getList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;

        try {
          const res = await withdrawPayApplyForControllerList(params);
          this.options.loading = false;
          this.list = res.list ? res.list : [];
          this.pagination.total = res ? res.total : 0;
        } catch (err) {
          this.options.loading = false;
        }
      },
      handleSee(val) {
        const obj = {
          ...val,
          moneyType: val.moneyType == 1 ? '人民币' : '澳币',
          businessType: businessTypeText[val.businessType],
          status: statusText[val.status],
        };
        const arr = formatDetail(obj, false, 'rechargeProfit')
          .withdrawalRechargeAudit;
        this.$refs['seeModal'].open({
          arr,
          title: '充值收益申请查看',
        });
      },
      handleAddEdit(val) {
        this.$refs['addRechargeProfit'].open(val);
      },
      async handleDel({ id }) {
        await withdrawPayApplyForControllerDel({ id });
        this.$message({
          message: '删除成功',
          type: 'success',
        });
        this.getList();
      },
      async handleSubmit({ id }) {
        await withdrawPayApplyForControllerSubmit({ id });
        this.$message({
          message: '提交成功',
          type: 'success',
        });
        this.getList();
      },
    },
  };
</script>
<style lang="scss" scoped></style>
