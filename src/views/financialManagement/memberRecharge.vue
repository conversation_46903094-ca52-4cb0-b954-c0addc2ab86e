<template>
  <div>
    <el-form inline>
      <el-form-item label="会员:">
        <el-input
          v-model="searchParams.idCode"
          placeholder="请输入会员ID_CODE"
        />
      </el-form-item>
      <el-form-item label="手机号:">
        <el-input v-model="searchParams.mobile" placeholder="请输入手机号" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          btn-text="可提现收益充值"
          permission-key="memberRecharge-withdraw"
          type="text"
          size="mini"
          @click="handleWithdrawal(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <el-dialog width="400px" title="充值" :visible.sync="visible">
      <el-form ref="formData" :model="saveParams" label-width="100px">
        <el-form-item label="会员姓名:">{{ saveParams.name }}</el-form-item>
        <el-form-item label="手机号码:">{{ saveParams.mobile }}</el-form-item>
        <!--        goodsPayment,-->
        <!--        oldGoodsPayment,-->
        <!--        audGoodsPayment,-->
        <!--        canWithdrawMoney,-->
        <!--        canWithdrawGoodsPayment,-->
        <!--        {-->
        <!--        prop: 'goodsPayment',-->
        <!--        label: '货款余额',-->
        <!--        },-->
        <!--        {-->
        <!--        prop: 'oldGoodsPayment',-->
        <!--        label: '老机制货款余额',-->
        <!--        },-->
        <!--        {-->
        <!--        prop: 'audGoodsPayment',-->
        <!--        label: '澳币货款余额',-->
        <!--        },-->
        <!--        {-->
        <!--        prop: 'canWithdrawMoney',-->
        <!--        label: '可提现收益',-->
        <!--        },-->
        <!--        {-->
        <!--        prop: 'canWithdrawGoodsPayment',-->
        <!--        label: '可提现货款',-->
        <!--        },-->
        <el-form-item label="货款余额:">
          {{ saveParams.goodsPayment }}
        </el-form-item>
        <el-form-item label="老机制货款余额:">
          {{ saveParams.oldGoodsPayment }}
        </el-form-item>
        <el-form-item label="澳币货款余额:">
          {{ saveParams.audGoodsPayment }}
        </el-form-item>
        <el-form-item label="可提现收益:">
          {{ saveParams.canWithdrawMoney }}
        </el-form-item>
        <el-form-item label="可提现货款:">
          {{ saveParams.canWithdrawGoodsPayment }}
        </el-form-item>
        <el-form-item
          prop="amount"
          :rules="[
            { required: true, message: '请输入充值金额', trigger: 'change' },
          ]"
          label="充值金额"
        >
          <el-input
            v-model="saveParams.amount"
            placeholder="请输入充值金额"
          ></el-input>
        </el-form-item>
        <el-form-item
          prop="describe"
          :rules="[
            { required: true, message: '请输入充值描述', trigger: 'change' },
          ]"
          label="充值描述"
        >
          <el-input
            v-model="saveParams.describe"
            placeholder="请输入充值描述"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" style="text-align: center" class="dialog-footer">
        <el-button type="primary" @click="onOK">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { initSearchParams } from '@/utils';
  import dynamictable from '@/components/dynamic-table';
  import { memberList, memberRecharge } from '@/api/financialManagement';

  export default {
    components: {
      dynamictable,
    },
    data() {
      return {
        searchParams: {
          idCode: '',
          mobile: '',
        },
        saveParams: {
          amount: '',
          describe: '',
          idCode: '',
        },
        visible: false,
        options: {
          loading: false,
          border: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        list: [],
        columns: [
          {
            prop: 'idCode',
            label: '会员ID_CODE',
          },
          {
            prop: 'name',
            label: '姓名',
          },
          {
            prop: 'nickname',
            label: '昵称',
          },
          {
            prop: 'mobile',
            label: '手机号',
          },
          {
            prop: 'goodsPayment',
            label: '货款余额',
          },
          {
            prop: 'oldGoodsPayment',
            label: '老机制货款余额',
          },
          {
            prop: 'audGoodsPayment',
            label: '澳币货款余额',
          },
          {
            prop: 'canWithdrawMoney',
            label: '可提现收益',
          },
          {
            prop: 'canWithdrawGoodsPayment',
            label: '可提现货款',
          },
          {
            prop: 'operation',
            label: '操作',
            scopedSlots: { customRender: 'operation' },
          },
        ],
      };
    },
    methods: {
      getParams() {
        const params = {
          ...this.searchParams,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },

      async getList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        if (!params.idCode && !params.mobile) {
          return this.$message.error('请输入电话号码或者会员ID Code');
        }
        this.options.loading = true;
        try {
          const res = await memberList(initSearchParams(params));
          this.options.loading = false;
          this.list = res ? res.list : [];
          this.pagination.total = res ? res.total : 0;
        } catch (err) {
          this.options.loading = false;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.getList(true);
      },
      handleWithdrawal({
        name,
        mobile,
        idCode,
        goodsPayment,
        oldGoodsPayment,
        audGoodsPayment,
        canWithdrawMoney,
        canWithdrawGoodsPayment,
      }) {
        this.saveParams = {
          ...this.saveParams,
          name,
          mobile,
          idCode,
          goodsPayment,
          oldGoodsPayment,
          audGoodsPayment,
          canWithdrawMoney,
          canWithdrawGoodsPayment,
        };
        this.visible = true;
      },
      onOK() {
        this.$refs.formData.validate(async valid => {
          if (!valid) return;
          const params = {
            amount: this.saveParams.amount,
            describe: this.saveParams.describe,
            idCode: this.saveParams.idCode,
          };
          await memberRecharge(params);
          this.getList();
          this.$message.success('充值成功!');
          this.visible = false;
          this.saveParams.amount = '';
          this.saveParams.describe = '';
        });
      },
    },
  };
</script>
<style lang="scss" scoped></style>
