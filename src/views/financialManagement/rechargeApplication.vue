<template>
  <div>
    <el-form inline>
      <el-form-item v-if="routerType === 'rechargeApplication'" label="流水号:">
        <el-input
          v-model="searchParams.payTransactionId"
          placeholder="请输入流水号"
        />
      </el-form-item>
      <el-form-item label="金额:">
        <el-input v-model="searchParams.amount" placeholder="请输入金额" />
      </el-form-item>
      <el-form-item label="idCode:">
        <el-input v-model="searchParams.idCode" placeholder="请输入idCode" />
      </el-form-item>
      <el-form-item label="手机号:">
        <el-input v-model="searchParams.mobile" placeholder="请输入手机号" />
      </el-form-item>
      <el-form-item label="账户类型:">
        <el-select v-model="searchParams.currencyType" placeholder="请选择">
          <el-option label="全部" value=""></el-option>
          <el-option label="人民币" :value="0"></el-option>
          <el-option label="澳币" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间:">
        <el-date-picker
          v-model="searchDate"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="审核状态:">
        <el-select v-model="searchParams.rechargeStatus" placeholder="请选择">
          <el-option
            v-for="item in getrechargeAuditStatus()"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <ac-permission-button
          v-if="routerType === 'rechargeApplication'"
          type="primary"
          btn-text="充值申请"
          permission-key="rechargeApplication-add"
          @click="handelAddEditRecharge(false)"
        ></ac-permission-button>
        <ac-permission-button
          btn-text="导出"
          :permission-key="
            routerType === 'rechargeApplication'
              ? 'rechargeApplication-export'
              : 'rechargeAudit-export'
          "
          type="primary"
          @click="onExport"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <el-button
          slot="reference"
          type="text"
          size="mini"
          @click="handleSee(scope.row)"
        >
          查看
        </el-button>
        <ac-permission-button
          v-if="routerType === 'rechargeAudit'"
          slot="reference"
          btn-text="审核"
          permission-key="rechargeAudit-review"
          :disabled="scope.row.status !== 0"
          type="text"
          size="mini"
          @click="handleSee(scope.row, 'examine')"
        ></ac-permission-button>

        <ac-permission-button
          v-if="routerType === 'rechargeApplication'"
          slot-btn="reference"
          node-type="popconfirm"
          title="确定撤销该充值申请吗？"
          btn-text="撤销"
          permission-key="rechargeApplication-revoke"
          :disabled="scope.row.status !== 0"
          type="text"
          size="mini"
          @click="handleRevoke(scope.row)"
        ></ac-permission-button>

        <ac-permission-button
          v-if="routerType === 'rechargeApplication'"
          node-type="popconfirm"
          title="确定提交该充值申请吗？"
          btn-text="提交"
          permission-key="rechargeApplication-submit"
          slot-btn="reference"
          :disabled="![2, 3, 4].includes(scope.row.status)"
          type="text"
          size="mini"
          @click="handleSubmit(scope.row)"
        ></ac-permission-button>

        <ac-permission-button
          v-if="routerType === 'rechargeApplication'"
          slot="reference"
          btn-text="修改"
          permission-key="rechargeApplication-edit"
          :disabled="![2, 3, 4].includes(scope.row.status)"
          type="text"
          size="mini"
          @click="handelAddEditRecharge(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <addRecharge ref="addRecharge" @onGet="getList()"></addRecharge>
    <seeModal ref="seeModal" :show-btn="showBtn" @onOk="onOK"></seeModal>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import addRecharge from './components/addRecharge';
  import seeModal from './components/seeModal';
  import { formatDetail } from './js/index';
  import {
    rechargeApplicationList,
    rechargeApplicationSubmit,
    rechargeApplicationDetails,
    rechargeApplicationRevert,
    rechargeApplicationAudit,
  } from '@/api/financialManagement';
  import { newExportExcel } from '@/api/blob';
  import { rechargeAuditStatus } from '@/consts';
  import {
    downloadFile,
    setInitData,
    initSearchParams,
    parseTime,
  } from '@/utils';
  const statusText = ['待审核', '已审核', '已驳回', '已撤销', '草稿'];
  const paymentText = [
    '国内微信',
    '国内支付宝',
    '国际微信',
    '国际支付宝',
    '银行卡',
    '通联',
  ];

  export default {
    components: {
      dynamictable,
      addRecharge,
      seeModal,
    },
    props: {
      routerType: {
        type: String,
        default: 'rechargeApplication',
      },
    },
    data() {
      console.log(this.routerType, 'routerType111');
      let columns = [
        {
          prop: 'id',
          label: '编号',
        },
        {
          prop: 'createdAt',
          label: '创建时间',
          render: ({ createdAt }) => (
            <span>{createdAt ? parseTime(createdAt) : ''}</span>
          ),
        },
        {
          prop: 'applyAdminName',
          label: '申请人',
        },
        {
          prop: 'amount',
          label: '金额',
        },
        {
          prop: 'idCode',
          label: '会员IDcode',
        },
        {
          prop: 'mobile',
          label: '会员手机号',
        },
        {
          prop: 'payment',
          label: '支付方式',
          render: ({ payment }) => <span>{paymentText[payment - 1]}</span>,
        },
        {
          prop: 'moneyType',
          label: '账户类型',
          render: row => <span>{row.moneyType == 0 ? '人民币' : '澳币'}</span>,
        },
        {
          prop: 'payTransactionId',
          label: '支付流水号',
        },
        {
          prop: 'status',
          label: '状态',
          render: ({ status }) => <span>{statusText[status]}</span>,
        },
        {
          prop: 'checkAdminName',
          label: '审核人',
        },
        {
          prop: 'screenshots',
          label: '截图',
          render: row => (
            <img style="width: 80px; height: 80px" src={row.screenshots} />
          ),
        },
        {
          prop: 'remarks',
          label: '备注',
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          scopedSlots: { customRender: 'operation' },
        },
      ];
      if (this.routerType === 'rechargeAudit') {
        columns.splice(11, 0, {
          prop: 'checkTime',
          label: '审核时间',
          render: ({ checkTime }) => (
            <span>{checkTime ? parseTime(checkTime) : ''}</span>
          ),
        });
      }
      return {
        rechargeAuditStatus,
        id: '',
        searchParams: {
          amount: '',
          idCode: '',
          mobile: '',
          endAt: '',
          currencyType: '',
          payTransactionId: '',
          rechargeStatus: '',
          startAt: '',
        },
        searchDate: '',
        showBtn: false,
        options: {
          loading: false,
          border: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        list: [],
        columns,
      };
    },
    created() {
      this.searchDate = setInitData(30);
      this.getList(true);
    },
    methods: {
      getrechargeAuditStatus() {
        if (this.routerType === 'rechargeApplication') {
          return this.rechargeAuditStatus;
        }
        return this.rechargeAuditStatus
          .map(item => {
            if (![3, 4].includes(item.value)) {
              return item;
            }
          })
          .filter(i => !!i);
      },
      getParams() {
        this.searchParams.startAt = this.searchDate ? this.searchDate[0] : '';
        this.searchParams.endAt = this.searchDate ? this.searchDate[1] : '';
        const rechargeStatus = this.searchParams.rechargeStatus;
        const allStatus = [0, 1, 2, 3, 4]; // 申请列表全部状态
        const allStatus1 = [0, 1, 2, 3]; // 审核列表显示的全部状态

        // 状态需要数组的形式传入， 审核列表不需要 草稿状态 4
        const params = {
          ...this.searchParams,
          rechargeStatus:
            rechargeStatus === ''
              ? this.routerType === 'rechargeApplication'
                ? allStatus
                : allStatus1
              : [rechargeStatus],
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return initSearchParams(params);
      },

      async getList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        try {
          const res = await rechargeApplicationList(params);
          this.options.loading = false;
          this.list = res && res.list ? res.list : [];
          this.pagination.total = res.total ? res.total : 0;
        } catch (err) {
          this.options.loading = false;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.searchDate = setInitData(30);
        this.getList(true);
      },
      onExport() {
        const params = this.getParams();
        newExportExcel(
          params,
          '/api/cust-bus/rechargeApplication/export',
          'post',
        ).then(res => {
          downloadFile(res.data, '充值申请列表');
        });
      },
      handleSee(val, key) {
        const obj = {
          ...val,
          moneyType: val.moneyType == 0 ? '人民币' : '澳币',
          payment: paymentText[val.payment - 1],
          status: statusText[val.status],
        };
        let arr = formatDetail(obj, false, this.routerType)
          .rechargeApplicationDetails;
        this.showBtn = false;
        this.id = val.id;
        if (key === 'examine') {
          arr = formatDetail(obj).toExamine;
          this.showBtn = true;
        }
        this.$refs['seeModal'].open({
          arr,
          title:
            this.routerType === 'rechargeApplication' ? '充值申请' : '充值审核',
        });
      },
      async onOK(params) {
        await rechargeApplicationAudit({
          ...params,
          id: this.id,
        });
        this.$message.success('操作成功');
        this.getList();
      },
      async handleRevoke({ id }) {
        await rechargeApplicationRevert({ id });
        this.$message.success('撤销成功');
        this.getList();
      },
      async handleSubmit({ id }) {
        await rechargeApplicationSubmit({ id });
        this.$message.success('提交成功');
        this.getList();
      },
      async handelAddEditRecharge(val) {
        let res;
        if (val) {
          res = await rechargeApplicationDetails({ id: val.id });
        }

        this.$refs['addRecharge'].open(res ? res : '');
      },
    },
  };
</script>
<style lang="scss" scoped></style>
