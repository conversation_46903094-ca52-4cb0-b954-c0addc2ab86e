<!--
 * @Description: 
 * @Author: wuqingsong
 * @Date: 2022-08-09 16:30:38
 * @LastEditTime: 2022-08-19 19:35:25
 * @LastEditors: Please set LastEditors
 * @Reference: 
-->
<template>
  <div>
    <div>
      <el-form ref="form" :model="form" label-width="80px" inline>
        <el-form-item label="代理ID:" prop="idCode">
          <el-input v-model="form.idCode" placeholder="请输入代理ID"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div style="float: right; margin-bottom: 15px">
      <ac-permission-button
        btn-text="查询"
        permission-key="overseasWithdrawalPay-search"
        @click="search(1)"
      ></ac-permission-button>
      <el-button type="primary" @click="reset">重置</el-button>
      <ac-permission-button
        btn-text="导出"
        permission-key="overseasWithdrawalPay-export"
        @click="doExport"
      ></ac-permission-button>
    </div>
    <div>
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
      >
        <el-table-column
          align="center"
          prop="idCode"
          label="用户idCode"
          min-width="100"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="canCashPayment"
          label="老货款可提现余额"
          min-width="100"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="canCashEarnings"
          label="老货款可提现收益余额"
          min-width="100"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="cashBalance"
          label="存管可提现余额"
          min-width="250"
        ></el-table-column>
        <el-table-column
          min-width="100"
          max-width="300"
          fixed="right"
          label="操作"
        >
          <template slot-scope="{ row }">
            <ac-permission-button
              slot="reference"
              type="text"
              size="small"
              btn-text="老货款可提转存管可提"
              permission-key="overseasWithdrawalPay-make-payment"
              @click="doOldPaymentCanBeWithdrawal(row.canCashPayment, 1)"
            ></ac-permission-button>
            <ac-permission-button
              slot="reference"
              type="text"
              size="small"
              btn-text="老货款可提收益转存管可提"
              permission-key="overseasWithdrawalPay-make-payment"
              @click="doOldPaymentCanBeWithdrawal(row.canCashEarnings, 2)"
            ></ac-permission-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pageNum"
        :page-sizes="[10, 20, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>
<script>
  import { exportExcel } from '@/api/blob';
  import { downloadFile } from '@/utils';
  import { tranfer2depo, queryUnionBalance } from '@/api/financialManagement';
  import store from '@/store';

  export default {
    data() {
      return {
        form: {
          idCode: '',
          money: '',
          type: '',
          remark: '',
          applyOrderNo: '',
        },
        idCode: '',
        tradeTypeList: [], // 状态下拉框
        tableData: [],
        loading: false, // loading表格加载层
        pageNo: 1,
        pageNum: 1,
        pageSize: 10,
        total: 1,
      };
    },
    created() {},
    methods: {
      async fetch() {
        const data = {
          idCode: this.form.idCode,
        };
        this.loading = true;
        try {
          const res = await queryUnionBalance(data);
          if (res) {
            this.tableData = [res];
            this.total = 1;
            this.idCode = res.idCode;
            this.loading = false;
          } else {
            this.loading = false;
          }
        } catch (error) {
          this.loading = false;
        }
      },
      search() {
        this.pageNum = 1;
        this.fetch();
      },
      //重置
      reset() {
        this.$refs['form'].resetFields();
      },
      async doOldPaymentCanBeWithdrawal(money, type) {
        const applyOrderNo = Date.parse(new Date()).toString();
        const params = {
          idCode: this.idCode,
          remark: store.state.user.username,
          applyOrderNo: applyOrderNo,
          money: money,
          type: type,
        };
        const res = await tranfer2depo(params);
        this.fetch();
      },
      doExport() {
        const params = {};
        const exportApi = '/widthdraw/haveToOpenAnAccountList';
        exportExcel(params, `/api/cust-bus/${exportApi}`, 'get').then(res => {
          if (res) {
            downloadFile(res.data, '老货款待转存管可提现记录', 'csv');
          } else {
            //
            this.$message.error('暂无数据');
          }
        });
      },
      handleSizeChange(val) {
        this.pageNum = 1;
        this.pageSize = val;
        this.fetch();
      },
      handleCurrentChange(val) {
        this.pageNum = val;
        this.fetch();
      },
    },
  };
</script>
<style lang="scss"></style>
