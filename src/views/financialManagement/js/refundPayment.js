export function formatDetail(res = {}, input) {
  const refundPaymentDetails = [
    {
      name: '代理手机号',
      value: res.mobile,
    },
    {
      name: '退款金额',
      value: res.amount,
    },
    {
      name: '账户类型',
      value: res.accountType,
    },
    {
      name: '业务类型',
      value: res.businessType,
    },
    {
      name: '该用户人民币账户余额',
      value: res.balance,
    },
    {
      name: '状态',
      value: res.status,
    },
    {
      name: '备注',
      value: res.remarks,
    },
    {
      name: '附件:(点击下载)',
      value: res.attachment ? res.attachment.url : '',
      type: 'file',
      ishide: res.attachment && res.attachment.url ? false : true,
    },
    {
      name: '拒绝原因',
      value: '',
      type: 'input',
      required: true,
      ishide: input ? false : true,
    },
    {
      name: '支付凭证:(点击图片下载)',
      value: res.proofRefunds,
      type: 'img',
    },
  ];
  const refundPaymentDetails1 = [
    {
      name: '代理手机号',
      value: res.mobile,
    },
    {
      name: '退款金额',
      value: res.amount,
    },
    {
      name: '账户类型',
      value: res.accountType,
    },
    {
      name: '业务类型',
      value: res.businessType,
    },
    {
      name: '该用户老货款账户余额',
      value: res.balance,
    },
    {
      name: '货款转可提现金额',
      value: res.rechargeAmount,
    },
    {
      name: '该用户可提现余额',
      value: res.withdrawBalance,
    },
    {
      name: '状态',
      value: res.status,
    },
    {
      name: '审核人',
      value: res.checkAdminName,
    },
    {
      name: '备注',
      value: res.remarks,
    },
    {
      name: '附件:(点击下载)',
      value: res.attachment ? res.attachment.url : '',
      type: 'file',
      ishide: res.attachment && res.attachment.url ? false : true,
    },
    {
      name: '拒绝原因',
      value: '',
      type: 'input',
      required: true,
      ishide: input ? false : true,
    },
    {
      name: '支付凭证:(点击图片下载)',
      value: res.proofRefunds,
      type: 'img',
    },
  ];

  const refundPaymentDetails2 = [
    {
      name: '代理手机号',
      value: res.mobile,
    },
    {
      name: '退款金额',
      value: res.amount,
    },
    {
      name: '账户类型',
      value: res.accountType,
    },
    {
      name: '业务类型',
      value: res.businessType,
    },
    {
      name: '该用户人民币账户余额',
      value: res.balance,
    },
    {
      name: '手续费',
      value: res.changeFee,
    },
    {
      name: '状态',
      value: res.status,
    },
    {
      name: '备注',
      value: res.remarks,
    },
    {
      name: '附件:(点击下载)',
      value: res.attachment ? res.attachment.url : '',
      type: 'file',
      ishide: res.attachment && res.attachment.url ? false : true,
    },
    {
      name: '拒绝原因',
      value: '',
      type: 'input',
      required: true,
      ishide: input ? false : true,
    },
    {
      name: '支付凭证:(点击图片下载)',
      value: res.proofRefunds,
      type: 'img',
    },
  ];
  const refundPaymentDetails3 = [
    {
      name: '代理手机号',
      value: res.mobile,
    },
    {
      name: '退款金额',
      value: res.amount,
    },
    {
      name: '账户类型',
      value: res.accountType,
    },
    {
      name: '业务类型',
      value: res.businessType,
    },
    {
      name: '该用户人民币账户余额',
      value: res.balance,
    },
    {
      name: '转账金额',
      value: res.transferAmount,
    },
    {
      name: '收款人姓名',
      value: res.receiverName,
    },
    {
      name: '银行卡号',
      value: res.cardNo,
    },
    {
      name: '开户行',
      value: res.bankName,
    },
    {
      name: '开户支行',
      value: res.subbranchName,
    },
    {
      name: '状态',
      value: res.status,
    },
    {
      name: '备注',
      value: res.remarks,
    },
    {
      name: '附件:(点击下载)',
      value: res.attachment ? res.attachment.url : '',
      type: 'file',
      ishide: res.attachment && res.attachment.url ? false : true,
    },
    {
      name: '拒绝原因',
      value: '',
      type: 'input',
      required: true,
      ishide: input ? false : true,
    },
    {
      name: '支付凭证:(点击图片下载)',
      value: res.proofRefunds,
      type: 'img',
    },
  ];

  const refundPaymentDetails4 = [
    {
      name: '代理手机号',
      value: res.mobile,
    },
    {
      name: '退款金额',
      value: res.amount,
    },
    {
      name: '账户类型',
      value: res.accountType,
    },
    {
      name: '业务类型',
      value: res.businessType,
    },
    {
      name: '该用户澳币账户余额',
      value: res.balance,
    },
    {
      name: '转账金额',
      value: res.transferAmount,
    },
    {
      name: '收款人姓名',
      value: res.receiverName,
    },
    {
      name: 'BSB',
      value: res.bankOrBsb,
    },
    {
      name: '银行卡号',
      value: res.cardNo,
    },
    {
      name: '充值流水号',
      value: res.rechargeTransaction,
    },
    {
      name: '状态',
      value: res.status,
    },
    {
      name: '审核人',
      value: res.checkAdminName,
    },
    {
      name: '备注',
      value: res.remarks,
    },
    {
      name: '附件:(点击下载)',
      value: res.attachment ? res.attachment.url : '',
      type: 'file',
      ishide: res.attachment && res.attachment.url ? false : true,
    },
    {
      name: '拒绝原因',
      value: '',
      type: 'input',
      required: true,
      ishide: input ? false : true,
    },
    {
      name: '支付凭证:(点击图片下载)',
      value: res.proofRefunds,
      type: 'img',
    },
  ];

  return {
    refundPaymentDetails,
    refundPaymentDetails2,
    refundPaymentDetails3,
    refundPaymentDetails4,
    refundPaymentDetails1,
  };
}
