export function formatDetail(res = {}, input, key) {
  console.log(res, 'oooooo');

  const rechargeApplicationDetails = [
    {
      name: '申请人',
      value: res.applyAdminName,
      ishide: key !== 'rechargeAudit',
    },
    {
      name: '会员手机号',
      value: res.mobile,
    },
    {
      name: '账户类型',
      value: res.moneyType,
    },
    {
      name: '充值金额',
      value: res.amount,
    },
    {
      name: `当前${res.moneyType}账户余额`,
      value: res.balance,
      ishide: key !== 'rechargeAudit',
    },
    {
      name: '支付方式',
      value: res.payment,
    },
    {
      name: '支付流水号',
      value: res.payTransactionId,
    },
    {
      name: '状态',
      value: res.status,
    },
    {
      name: '拒绝原因',
      value: res.reason || '无',
      ishide: !['已驳回'].includes(res.status),
    },
    {
      name: '审核人',
      value: res.checkAdminName,
      ishide: !['已驳回'].includes(res.status),
    },
    {
      name: '备注',
      value: res.remarks,
    },
    {
      name: '支付凭证:(点击图片下载)',
      value: res.screenshots,
      type: 'img',
    },
  ];

  const toExamine = [
    {
      name: '申请人',
      value: res.applyAdminName,
    },
    {
      name: '会员手机号',
      value: res.mobile,
    },
    {
      name: '账户类型',
      value: res.moneyType,
    },
    {
      name: '充值金额',
      value: res.amount,
    },
    {
      name: `当前${res.moneyType}账户余额`,
      value: res.balance,
    },
    {
      name: '支付方式',
      value: res.payment,
    },
    {
      name: '支付流水号',
      value: res.payTransactionId,
    },
    {
      name: '状态',
      value: res.status,
    },
    {
      name: '备注',
      value: res.remarks,
    },
    {
      name: '拒绝原因',
      value: '',
      type: 'input',
      required: true,
    },
  ];

  let withdrawalRechargeAudit = [
    {
      name: '申请人',
      value: res.applyAdminName,
      ishide: key !== 'withdrawalRechargeAudit',
    },
    {
      name: '代理手机号',
      value: res.mobile,
    },
    {
      name: '账户类型',
      value: res.moneyType,
    },
    {
      name: '业务类型',
      value: res.businessType,
    },
    {
      name: '充值金额',
      value: res.amount,
    },
    {
      name: '该用户可提现收益余额',
      value: res.userCanCashIncomeBalance,
    },
    {
      name: '状态',
      value: res.status,
    },
    {
      name: '初审人',
      value: res.firstCheckAdminName,
      ishide: !['待复审', '已通过', '复审驳回'].includes(res.status),
    },
    {
      name: '审核人',
      value: res.checkAdminName,
      ishide: !['已审核', '已驳回'].includes(res.status),
    },
    {
      name: '复审人',
      value: res.checkAdminName,
      ishide: !['待复审', '已通过', '复审驳回'].includes(res.status),
    },
    {
      name: '拒绝原因',
      value: res.reasons,
      ishide: !['已驳回', '初审驳回', '复审驳回'].includes(res.status),
    },
    {
      name: '备注',
      value: res.remarks,
    },
    {
      name: '附件:(点击下载)',
      value: res.file,
      type: 'file',
      ishide: res.file ? false : true,
    },
    {
      name: '拒绝原因',
      value: '',
      type: 'input',
      required: true,
      ishide: input ? false : true,
    },
    {
      name: '支付凭证:(点击图片下载)',
      value:
        res.proofRefund || res.payVoucher ? JSON.parse(res.payVoucher) : [],
      type: 'img',
    },
  ];

  return {
    rechargeApplicationDetails,
    toExamine,
    withdrawalRechargeAudit,
  };
}
