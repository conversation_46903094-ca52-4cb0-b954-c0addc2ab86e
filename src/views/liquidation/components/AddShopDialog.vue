<template lang="pug">
el-dialog(:visible.sync='showDialog' width='40%' :close-on-click-modal='false')
  el-form(
    ref='form'
    v-loading='loading'
    :model='form'
    :rules='rules'
    label-position='right'
    label-width='150px'
    label-suffix=':'
  )
    el-form-item(label='店铺名称' prop='name')
      el-input(v-model='form.name' placeholder='请输入店铺名称')
    el-form-item(label='关键词' prop='keyword')
      el-input(v-model='form.keyword' placeholder='请输入关键词')
    el-form-item(label='主体名称' prop='parentCode')
      el-select(
        v-model='form.parentCode'
        filterable
        remote
        placeholder='请输入主体名称'
        :remote-method='onSubjectSearch'
        :loading='subjectLoading'
        style='width: 100%'
      )
        el-option(
          v-for='subject in subjectList'
          :key='subject.code'
          :label='subject.name'
          :value='subject.code'
        )
  span.dialog-footer(slot='footer')
    el-button(@click='showDialog = false') 取消
    el-button(
      type='primary'
      :disabled='confirmDisable'
      :loading='saveLoading'
      @click='handleConfirmClick'
    ) {{ record != null ? "编辑" : "新增" }}
</template>
<script>
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      record: {
        type: Object,
        default: undefined,
      },
    },
    data() {
      return {
        loading: false,
        subjectLoading: false,
        subjectList: [],
        form: {
          parentCode: null,
        },
        rules: {
          name: [
            { required: true, message: '请输入店铺名称', trigger: 'blur' },
            { trigger: 'change' },
          ],
          // keyword: [
          //   { required: true, message: '请输入关键词', trigger: 'blur' },
          //   { trigger: 'change' },
          // ],
          parentCode: [
            { required: true, message: '请选择主体', trigger: 'change' },
          ],
        },
        saveLoading: false,
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          if (!value) {
            // 重置
            this.form = {
              parentCode: null,
            };
            this.subjectList = [];
            this.$refs.form.resetFields();
          }
          this.$emit('change', value);
        },
      },
      confirmDisable() {
        const { name, keyword } = this.form;
        if (name && keyword) {
          return false;
        }
        return true;
      },
    },
    watch: {
      show(value) {
        if (value) {
          if (this.record) {
            const {
              // code,
              name,
              // id,
              keyword,
              orgName,
              orgCode,
              orgType,
            } = this.record;
            this.form = {
              name,
              keyword,
              parentCode: null,
            };
            if (orgCode) {
              this.form.parentCode = orgCode;
              this.subjectList = [{ code: orgCode, name: orgName }];
            }
          }
        }
      },
    },
    methods: {
      handleConfirmClick() {
        this.saveData();
      },
      async saveData() {
        this.saveLoading = true;
        const isEdit = this.record != null;
        const body = this.form;
        if (body.parentCode) {
          body.parentCodeType = 1;
        }
        if (isEdit) {
          body.id = this.record.id;
        }
        const { err, res } = await this.$apis.liquidation[
          isEdit ? 'updateShop' : 'saveShop'
        ](body);
        if (!err) {
          this.showDialog = false;
          this.$message.success(isEdit ? '修改成功' : '保存成功');
          this.$emit('update');
        }
        this.saveLoading = false;
      },
      async onSubjectSearch(name) {
        if (this.subjectLoading) {
          return;
        }
        this.subjectLoading = true;
        const { err, res } = await this.$apis.liquidation.getSubjectByName(
          name,
        );
        if (!err) {
          this.subjectList = res;
        } else {
          this.subjectList = [];
        }
        console.log(res);

        this.subjectLoading = false;
      },
    },
  };
</script>
