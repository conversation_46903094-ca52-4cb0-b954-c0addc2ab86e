<template lang="pug">
el-dialog(:visible.sync='showDialog' width='40%' :close-on-click-modal='false')
  el-form(
    ref='form'
    :model='form'
    :rules='rules'
    label-position='right'
    label-width='150px'
    label-suffix=':'
  )
    el-form-item(label='父主体' prop='parentCode')
      el-select(
        v-model='form.parentCode'
        filterable
        remote
        placeholder='请输入父主体名称'
        :remote-method='onSubjectSearch'
        :loading='subjectLoading'
        style='width: 100%'
      )
        el-option(
          v-for='subject in subjectList'
          :key='subject.name'
          :label='subject.name'
          :value='subject.code'
        )
    el-form-item(
      v-for='item in formData'
      :label='item.title'
      :prop='item.key'
      :key='item.key'
    )
      el-input(
        v-model='form[item.key]'
        :placeholder='"请输入" + item.title'
        :disabled='item.key === "codePrefix" && record != null'
      )
  span.dialog-footer(slot='footer')
    el-button(@click='showDialog = false') 取消
    el-button(
      type='primary'
      :disabled='confirmDisable'
      :loading='saveLoading'
      @click='handleConfirmClick'
    ) {{ record ? "修改" : "保存" }}
</template>
<script>
  const FORM_DATA = [
    {
      key: 'name',
      title: '主体名称',
      isRequired: true,
    },
    {
      key: 'keyword',
      title: '关键词',
    },
    {
      key: 'adress',
      title: '所在地',
      isRequired: false,
    },
    {
      key: 'codePrefix',
      title: '编码前缀',
      isRequired: true,
    },
    {
      key: 'nsCustCode',
      title: 'NS客户编码',
      isRequired: false,
    },
    {
      key: 'nsSupplierCode',
      title: 'NS供应商编码',
      isRequired: false,
    },
  ];
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      record: {
        type: Object,
        default: undefined,
      },
    },
    data() {
      const rules = {};
      FORM_DATA.forEach(item => {
        rules[item.key] = [
          {
            required: item.isRequired,
            message: `请输入${item.title}`,
            trigger: 'blur',
          },
          {
            trigger: 'change',
          },
        ];
      });
      return {
        loading: false,
        form: {},
        formData: FORM_DATA,
        rules,
        saveLoading: false,
        subjectLoading: false,
        subjectList: [],
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          if (!value) {
            // 重置
            this.form = {};
            this.subjectList = [];
          }
          this.$emit('change', value);
        },
      },
      confirmDisable() {
        const { name, keyword, codePrefix } = this.form;
        if (name && (this.record || codePrefix)) {
          return false;
        }
        return true;
      },
    },
    watch: {
      show(value) {
        if (value) {
          if (this.record) {
            const {
              adress,
              code,
              codePrefix,
              keyword,
              name,
              nsCustCode,
              nsSupplierCode,
              parentCode,
              parentName,
            } = this.record;
            if (parentCode && parentName) {
              this.subjectList = [{ code: parentCode, name: parentName }];
            }

            this.form = {
              adress,
              code,
              codePrefix,
              keyword,
              name,
              nsCustCode,
              nsSupplierCode,
              parentCode,
              // parentName,
            };
          }
        }
      },
    },
    methods: {
      handleConfirmClick() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.saveData();
          }
        });
      },
      async saveData() {
        this.saveLoading = true;
        const isEdit = this.record != null;
        const body = this.form;
        body.abbreviation = 'test'; // TODO: remove this
        if (isEdit) {
          body.id = this.record.id;
        }
        const { err, res } = await this.$apis.liquidation[
          isEdit ? 'updateSubject' : 'saveSubject'
        ](body);
        if (!err) {
          this.showDialog = false;
          this.$message.success(isEdit ? '修改成功' : '保存成功');
          this.$emit('update');
        }
        this.saveLoading = false;
      },
      async onSubjectSearch(name) {
        if (this.subjectLoading) {
          return;
        }
        this.subjectLoading = true;
        const { err, res } = await this.$apis.liquidation.getSubjectByName(
          name,
        );
        if (!err) {
          this.subjectList = res;
        } else {
          this.subjectList = [];
        }
        console.log(res);

        this.subjectLoading = false;
      },
    },
  };
</script>
