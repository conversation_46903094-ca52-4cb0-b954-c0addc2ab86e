<template lang="pug">
el-dialog(:visible.sync='showDialog' width='80%' :close-on-click-modal='false')
  ac-permission-button(
    v-loading='onExportLoading'
    btn-text='导出'
    :permission-key='permissionKey'
    type='primary'
    @click='onExportClick'
  )
  el-table.margin-top-10(
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
  )
    el-table-column(prop='id' label='id')
    el-table-column(prop='accountName' label='账户名称')
    el-table-column(prop='sourceType' label='支付渠道')
      template(v-slot='{ row: { sourceType } }')
        span {{ getListData(payChannelList, sourceType) }}
    el-table-column(prop='serialNumber' label='渠道流水号' width='180px')
    el-table-column(prop='channelMerNo' label='商户号' width='180px')
    el-table-column(prop='transAmount' label='交易金额')
    el-table-column(prop='fee' label='手续费')
    el-table-column(prop='batchId' label='批次ID')
    el-table-column(prop='createTime' label='创建时间' width='170px')
  el-pagination.margin-top-10.margin-bottom-10(
    :current-page='m_current'
    :page-sizes='[10, 15, 20, 30]'
    :page-size='m_pageSize'
    layout='total, sizes, prev, pager, next, jumper'
    :total='m_total'
    background
    @size-change='m_handleSizeChange'
    @current-change='m_handleCurrentChange'
  )
</template>
<script>
  import { paginationMixin } from '@/mixins/tableMixin';
  import {
    PAY_CHANNEL_LIST,
    IMPORT_TYPE_LIST,
    STATUS_LIST,
  } from '@/consts/liquidation';

  export default {
    mixins: [paginationMixin],
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      currentId: {
        type: Number,
        default: undefined,
      },
      permissionKey: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        loading: false,
        list: [],
        onExportLoading: false,

        payChannelList: PAY_CHANNEL_LIST,
        importTypeList: IMPORT_TYPE_LIST,
        statusList: STATUS_LIST,
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          if (!value) {
            // 重置
            this.form = {};
          }
          this.$emit('change', value);
        },
      },
      confirmDisable() {
        return true;
      },
    },
    watch: {
      show(value) {
        if (value) {
          if (this.currentId) {
            this.m_loadData();
          }
        }
      },
    },
    methods: {
      async m_loadData() {
        this.loading = true;
        const {
          err,
          res,
        } = await this.$apis.liquidation.getChannelTurnoverDetail(
          this.currentId,
          this.m_current,
          this.m_pageSize,
        );
        if (!err) {
          const { total, records } = res;
          this.list = records;
          this.m_total = total;
        }

        this.loading = false;
      },
      getListData(list, index) {
        if (list && index >= 0 && index < list.length) {
          return list[index];
        }
        return '';
      },
      async onExportClick() {
        this.onExportLoading = true;
        const { res, err } = await this.$apis.liquidation.getChannelExportUrl(
          this.currentId,
        );
        if (!err) {
          console.log(res);
          this.downloadFile(res);
        }

        this.onExportLoading = false;
      },

      downloadFile(fileUrl) {
        // 创建隐藏的可下载链接
        var eleLink = document.createElement('a');
        // eleLink.download = true
        eleLink.style.display = 'none';
        // 字符内容转变成blob地址
        eleLink.href = fileUrl;
        // 触发点击
        document.body.appendChild(eleLink);
        eleLink.click();
        // 然后移除
        document.body.removeChild(eleLink);
      },
    },
  };
</script>
