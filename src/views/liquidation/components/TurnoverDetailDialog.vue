<template lang="pug">
el-dialog(:visible.sync='showDialog' width='95%' :close-on-click-modal='false')
  .horizontal.left.top
    .vertical.top.hcenter.in-horizontal.fill-horizontal.scroll-x
      .horizontal.left.vcenter.in-vertical.fill-horizontal
        .noshrink.margin-left-20 状态
        el-select.margin-left-10(
          v-model='status'
          placeholder='请选择'
          style='width: 100px'
        )
          el-option(
            v-for='(item, index) in statusList'
            :key='item'
            :label='item'
            :value='index'
          )
        el-button.margin-left-10(
          type='primary'
          icon='el-icon-search'
          @click='onSearchClick'
        ) 查询
      el-table.margin-top-20(
        v-loading='loading'
        :data='list'
        align='center'
        empty-text='暂无数据'
      )
        el-table-column(prop='id' label='id' width='80px')
        el-table-column(prop='transNo' label='交易流水号' width='180px')
        el-table-column(prop='transDate' label='交易日期' width='180px')
        //- el-table-column(prop='liquidationName' label='清算事项' width="100px")
        el-table-column(prop='debitAmount' label='借方发生额' width='100px')
        el-table-column(prop='creditAmount' label='贷方发生额' width='100px')
        el-table-column(prop='amount' label='交易后余额' width='100px')
        el-table-column(prop='transAccount' label='交易账号' width='100px')
          template(v-slot='{ row: { transAccount: { accountCode } } }')
            span {{ accountCode }}
        el-table-column(prop='transAccount' label='交易账户名' width='180px')
          template(v-slot='{ row: { transAccount: { accountName } } }')
            span {{ accountName }}
        el-table-column(prop='brief' label='摘要')
        el-table-column(prop='note' label='备注')
        el-table-column(prop='status' label='匹配状态' width='100')
          template(v-slot='{ row: { status } }')
            span {{ statusList[status] }}
        el-table-column(prop='msg' label='校验异常信息' width='200')
        el-table-column(prop='createTime' label='创建时间' width='180px')
      el-pagination.margin-top-10.margin-bottom-10(
        :current-page='detailPagination.offset'
        :page-sizes='[10, 15, 20, 30]'
        :page-size='detailPagination.limit'
        layout='total, sizes, prev, pager, next, jumper'
        :total='detailPagination.total'
        background
        @size-change='handleSizeChange'
        @current-change='handleCurrentChange'
      )
    //- div(style="width:20px")
    //- .vertical.top.hcenter
    //-   .horizontal.left.vcenter.in-vertical.fill-horizontal
    //-     div.noshrink 客户名称
    //-     el-input.margin-left-10(
    //-       v-model="counterPartyName"
    //-       style="width:150px"
    //-       placeholder="请输入客户名称"
    //-       clearable
    //-     )
    //-     el-button.margin-left-10(
    //-       type="primary"
    //-       icon="el-icon-search"
    //-       @click="onSearchCounterPartyClick"
    //-     ) 查询
    //-     el-button.margin-left-10(
    //-       icon="el-icon-plus"
    //-       @click="onAddCounterPartyClick"
    //-       v-loading="typeLoading"
    //-     ) 新增
    //-   el-table.margin-top-20(
    //-     v-loading="counterPartyLoading" 
    //-     :data="counterPartyList"
    //-     align="center"
    //-     style='width: 100%' 
    //-     empty-text="暂无数据"
    //-   )
    //-     el-table-column(prop='code' label='客户编码' width="80px")
    //-     el-table-column(prop='name' label='客户名称' width="180px")
    //-     el-table-column(prop='keyword' label='关键词')
    //-   el-pagination.margin-top-10.margin-bottom-10(
    //-     :current-page="counterPartyPagination.offset"
    //-     :page-sizes="[10, 15, 20, 30]"
    //-     :page-size="counterPartyPagination.limit"
    //-     layout="total, sizes, prev, next,jumper"
    //-     :total="counterPartyPagination.total"
    //-     background
    //-     @size-change="handleCounterPartySizeChange"
    //-     @current-change="handleCounterPartyCurrentChange"
    //-   )
  //- add-counterparty-dialog(
  //-   v-model="showAddCounterPartyDialog"
  //-   :type-options="typeOptions"
  //-   @update="loadCounterParty"
  //-   append-to-body
  //- )
</template>
<script>
  import AddCounterpartyDialog from './AddCounterpartyDialog';

  const STATUS_LIST = ['数据校验未通过', '对方机构校验未通过', '数据校验通过'];

  export default {
    components: {
      AddCounterpartyDialog,
    },
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      batchNo: {
        type: String,
        default: undefined,
      },
    },
    data() {
      return {
        loading: false,
        list: [],
        detailPagination: {
          limit: 10,
          offset: 1,
          total: 0,
        },
        statusList: STATUS_LIST,

        status: null,
        searchStatus: null,
        typeLoading: false,

        // counterPartyName: '',
        // counterPartyList: [],
        // counterPartyLoading: false,
        // counterPartyPagination: {
        //   limit: 10,
        //   offset: 1,
        //   total: 0,
        // },
        // typeOptions: [],

        // showAddCounterPartyDialog: false,

        // searchCounterPartyName: '',
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
      confirmDisable() {
        return true;
      },
    },
    watch: {
      show(value) {
        if (value) {
          if (this.batchNo) {
            this.loadData();
          }
        } else {
          this.list = [];
          this.status = null;
          this.searchStatus = null;
          this.detailPagination = {
            limit: 10,
            offset: 1,
            total: 0,
          };

          // this.searchCounterPartyName = ''
          // this.counterPartyName = ''
          this.counterPartyList = [];
          this.typeLoading = false;
          // this.counterPartyPagination = {
          //   limit: 10,
          //   offset: 1,
          //   total: 0,
          // }
        }
      },
    },
    methods: {
      async loadData() {
        this.loading = true;
        const params = {
          limit: this.detailPagination.limit,
          page: this.detailPagination.offset,
          batchNo: this.batchNo,
        };
        if (this.searchStatus != null) {
          params.status = this.searchStatus;
        }

        const { res, err } = await this.$apis.liquidation.getTurnoverlogDetail(
          params,
        );
        if (!err) {
          const { total, records } = res;
          this.list = records;
          this.detailPagination.total = total;
          this.list = records;
        }

        this.loading = false;
      },
      // async loadCounterParty() {
      //   this.counterPartyLoading = true
      //   const body = {
      //     pageNo: this.counterPartyPagination.offset,
      //     limit: this.counterPartyPagination.limit,
      //     // typeName: this.searchType,
      //   }
      //   if (
      //     this.searchCounterPartyName &&
      //     this.searchCounterPartyName.length > 0
      //   ) {
      //     body.searchName = this.searchCounterPartyName
      //   }
      //   const { err, res } = await this.$apis.liquidation.getCustomerList(body)
      //   if (!err) {
      //     const { total, pages, size, records } = res
      //     this.counterPartyList = records
      //     this.counterPartyPagination.total = total
      //   }
      //   this.counterPartyLoading = false
      // },
      onSearchClick() {
        this.detailPagination.offset = 1;
        this.searchStatus = this.status;
        this.loadData();
      },
      handleSizeChange(size) {
        this.detailPagination.limit = size;
        this.loadData();
      },
      handleCurrentChange(current) {
        this.detailPagination.offset = current;
        this.loadData();
      },
      // handleCounterPartySizeChange(size) {
      //   this.counterPartyPagination.limit = size
      //   this.loadCounterParty()
      // },
      // handleCounterPartyCurrentChange(current) {
      //   this.counterPartyPagination.offset = current
      //   this.loadCounterParty()
      // },
      // async onAddCounterPartyClick() {
      //   if (this.typeOptions || this.typeOptions.length == 0) {
      //     this.typeLoading = true
      //     const {
      //       err,
      //       res,
      //     } = await this.$apis.liquidation.getParamDictionaries(4)
      //     this.typeLoading = false
      //     if (!err) {
      //       this.typeOptions = res
      //     } else {
      //       return
      //     }
      //   }
      //   this.showAddCounterPartyDialog = true
      // },
      // onSearchCounterPartyClick() {
      //   this.searchCounterPartyName = this.counterPartyName
      //   this.loadCounterParty()
      // },
    },
  };
</script>
