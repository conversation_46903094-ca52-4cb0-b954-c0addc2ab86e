<template lang="pug">
el-dialog(
  :visible.sync='showDialog'
  width='80%'
  :close-on-click-modal='false'
  title='手动匹配'
)
  el-table(
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
  )
    el-table-column(prop='id' label='id')
    el-table-column(prop='capital' label='账户主体')
    el-table-column(prop='capitalValue' label='值')
    el-table-column(prop='account' label='交易账户')
    el-table-column(prop='accountValue' label='值')
    el-table-column(prop='debitLiquidationName' label='借方-清算事项')
    el-table-column(prop='creditLiquidationName' label='贷方-清算事项')
    el-table-column(label='操作')
      template(v-slot='{ $index, row }')
        a.clickable-text(@click='onMatchClick(row)') 匹配
  el-pagination.margin-top-10.margin-bottom-10(
    :current-page='m_current'
    :page-sizes='[10, 15, 20, 30]'
    :page-size='m_pageSize'
    layout='total, sizes, prev, pager, next, jumper'
    :total='m_total'
    background
    @size-change='m_handleSizeChange'
    @current-change='m_handleCurrentChange'
  )
</template>
<script>
  import { paginationMixin } from '@/mixins/tableMixin';

  export default {
    mixins: [paginationMixin],
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      currentId: {
        type: Number,
        default: undefined,
      },
    },
    data() {
      return {
        loading: false,
        list: [],
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {
        if (value) {
          if (this.currentId) {
            this.m_loadData();
          }
        }
      },
    },
    methods: {
      async m_loadData() {
        this.loading = true;
        const body = {
          size: this.m_pageSize,
          current: this.m_current,
        };
        const { err, res } = await this.$apis.liquidation.getLiquidationList(
          body,
        );
        if (!err) {
          const { total, records } = res;
          this.list = records;
          this.m_total = total;
        }

        this.loading = false;
      },
      async onMatchClick(row) {
        this.loading = true;
        const { err, res } = await this.$apis.liquidation.directMatchTurnover(
          this.currentId,
          row.id,
        );
        if (!err) {
          this.$message.success('操作成功');
          this.$emit('success');
          this.showDialog = false;
        }

        this.loading = false;
      },
    },
  };
</script>
