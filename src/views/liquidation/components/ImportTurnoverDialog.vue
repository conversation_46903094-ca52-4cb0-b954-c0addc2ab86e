<template lang="pug">
el-dialog(:visible.sync='showDialog' width='40%' :close-on-click-modal='false')
  el-form(
    ref='form'
    v-loading='loading'
    :model='form'
    :rules='rules'
    label-position='right'
    label-width='150px'
    label-suffix=':'
  )
    el-form-item(label='账户编码')
      div {{ accountCode }}
    el-form-item(label='账户类型')
      div {{ accountTypeName }}
    el-form-item(label='选择文件' prop='file')
      el-upload(
        :action='action'
        :limit='1'
        :headers='headers'
        :on-success='onUploadSuccess'
        :http-request='httpRequest'
      )
        el-button(size='small' type='primary') 点击上传

  //- span.dialog-footer(slot='footer')
  //-   el-button(@click='showDialog = false') 取消
  //-   el-button(
  //-     type='primary'
  //-     :disabled='confirmDisable'
  //-     :loading='saveLoading'
  //-     @click='handleConfirmClick'
  //-   ) 导入
</template>
<script>
  import { getCookie } from '@/utils/auth';
  import { service } from '@/utils/request';
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      accountCode: {
        type: String,
        default: undefined,
      },
      accountTypeName: {
        type: String,
        default: '',
      },
      bankCode: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        headers: {},
        loading: false,
        action:
          window.location.protocol +
          '//' +
          window.ACCESS_HOSTS.apiHost +
          '/api/recon/bill/upload',
        subjectLoading: false,
        subjectList: [],
        form: {
          file: null,
        },
        rules: {
          file: [
            { required: true, message: '请输入上传文件', trigger: 'change' },
          ],
        },
        saveLoading: false,
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          if (!value) {
            // 重置
            this.form = {};
          }
          this.$emit('change', value);
        },
      },
      confirmDisable() {
        return true;
      },
      // uploadData() {
      //   return {
      //     accountCode: this.accountCode,
      //   }
      // },
    },
    methods: {
      handleConfirmClick() {},
      onUploadSuccess(response, file, fileList) {
        // this.$message.success('导入完成')
        // console.log('response')
      },
      async httpRequest(request) {
        const { action, file, data, onError, onSuccess } = request;
        const formData = new FormData();
        formData.append('file', file);
        formData.append('accountCode', this.accountCode);
        formData.append('bankCode', this.bankCode);
        const { err, res } = await service.instance.http.post(action, formData);
        // onSuccess(res)
        if (err) {
          onError(err);
        } else {
          onSuccess(res);
          this.$message.success('导入成功');
          // todo 导入成功
          this.$router.push({
            path: 'turnoverlog',
          });
        }
      },
    },
  };
</script>
