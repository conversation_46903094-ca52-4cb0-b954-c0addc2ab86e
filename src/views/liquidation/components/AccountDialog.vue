<template lang="pug">
el-dialog(:visible.sync='showDialog' width='40%' :close-on-click-modal='false')
  el-form(
    ref='form'
    v-loading='loading'
    :model='form'
    :rules='rules'
    label-position='right'
    label-width='150px'
    label-suffix=':'
  )
    el-form-item(label='类型' prop='parentType')
      el-select(
        v-model='form.parentType'
        placeholder='请选择类型'
        style='width: 100%'
        disabled
      )
        el-option(
          v-for='item in typeOptions'
          :key='item.code'
          :label='item.name'
          :value='item.code'
        )
    //- el-form-item(label='机构名称' prop='orgName')
    //-   el-input(v-model='form.orgName' placeholder='请输入机构名称')
    el-form-item(label='机构名称' prop='parentCode')
      el-select(
        v-model='form.parentCode'
        filterable
        remote
        placeholder='请输入机构名称'
        :remote-method='onOrganizationSearch'
        :loading='searchLoading'
        style='width: 100%'
        disabled
      )
        //- :disabled="form.parentType == null"
        el-option(
          v-for='org in organizationList'
          :key='org.name'
          :label='org.name'
          :value='org.code'
        )
    el-form-item(label='账户类型' prop='accountType')
      el-select(
        v-model='form.accountType'
        placeholder='请选择账户类型'
        style='width: 100%'
      )
        el-option(
          v-for='item in accountTypeList'
          :label='item.name'
          :value='item.code'
          :key='item.code'
        )
    el-form-item(label='账户名称' prop='accountName')
      el-input(v-model='form.accountName' placeholder='请输入账户名称')
    //- 1 限定币种 2 多币种
    el-form-item(label='币种' prop='currencyType')
      .horizontal.left.vcenter
        el-select(
          v-model='form.currencyType'
          placeholder='请选择币种'
          :style='{ width: form.currencyType == 1 ? "200px" : "100%" }'
        )
          el-option(
            v-for='(item, index) in ["限定币种", "多币种"]'
            :key='item'
            :label='item'
            :value='index + 1'
          )
        el-input.margin-left-10(
          v-if='form.currencyType == 1'
          v-model='form.currency'
          placeholder='请输入币种简称'
        )
    el-form-item(label='类型' prop='bankType')
      el-select(
        v-model='form.bankType'
        placeholder='请选择类型'
        style='width: 100%'
        @change='onBankTypeChange'
      )
        el-option(
          v-for='(item, index) in ["境外银行", "境内银行", "境外支付公司", "境内支付公司"]'
          :key='item'
          :label='item'
          :value='index + 1'
        )
    template(v-if='form.bankType === 3 || form.bankType === 4')
      el-form-item(label='支付渠道' prop='bankCode')
        el-select(
          v-model='payChannel'
          placeholder='请选择支付渠道'
          style='width: 100%'
          value-key='code'
        )
          el-option(
            v-for='item in payChannelList'
            :key='item.code'
            :label='item.name'
            :value='item'
          )
      //- el-form-item(label='支付渠道代码' prop='bankCode')
      //-   el-input(v-model='form.bankCode' placeholder='请输入支付渠道代码')
      //- el-form-item(label='支付渠道名称' prop='bankName')
      //-   el-input(v-model='form.bankName' placeholder='请输入支付渠道名称')
      el-form-item(label='账号' prop='account')
        el-input(v-model='form.account' placeholder='请输入账号')
    template(v-else-if='form.bankType == 1 || form.bankType == 2')
      el-form-item(
        v-if='form.bankType == 1'
        label='银行代码类型'
        prop='bankCodeType'
      )
        el-select(
          v-model='form.bankCodeType'
          placeholder='请选择银行代码类型'
          style='width: 100%'
        )
          el-option(
            v-for='item in bankCodeOptions'
            :key='item.value'
            :label='item.label'
            :value='item.value'
          )
      el-form-item(label='银行代码' prop='bankCode')
        el-input(v-model='form.bankCode' placeholder='请输入银行代码')
      el-form-item(label='开户银行' prop='bankName')
        el-input(v-model='form.bankName' placeholder='请输入开户银行')
      el-form-item(
        label='支行'
        prop='branchBankName'
        :required='form.bankType == 2'
      )
        el-input(v-model='form.branchBankName' placeholder='请输入支行')
      el-form-item(label='银行简称' prop='bankAbbreviation')
        el-input(v-model='form.bankAbbreviation' placeholder='请输入银行简称')
      el-form-item(label='资金用途' prop='capitalPurpose')
        el-input(v-model='form.capitalPurpose' placeholder='请输入资金用途')
      el-form-item(label='银行账号' prop='account')
        el-input(v-model='form.account' placeholder='请输入银行账号')
      el-form-item(label='手续费' prop='serviceCharge')
        el-select(
          v-model='form.serviceCharge'
          placeholder='请选择手续费类型'
          style='width: 100%'
        )
          el-option(
            v-for='(item, index) in ["内扣", "外扣"]'
            :key='item'
            :label='item'
            :value='index + 1'
          )
      el-form-item(v-if='form.bankType == 1' label='公司地址' prop='companyAdress')
        el-input(v-model='form.companyAdress' placeholder='请输入公司地址')
      el-form-item(v-if='form.bankType == 1' label='银行地址' prop='bankAdress')
        el-input(v-model='form.bankAdress' placeholder='请输入银行地址')
  span.dialog-footer(slot='footer')
    el-button(@click='showDialog = false') 取消
    el-button(
      type='primary'
      :disabled='confirmDisable'
      :loading='saveLoading'
      @click='handleConfirmClick'
    ) {{ accountId != null ? "编辑" : "新增" }}
</template>
<script>
  import { mapActions } from 'vuex';
  // const PAY_CHANNELS = [
  //   { name: '支付宝', code: 'alipay' },
  //   { name: '微信', code: 'wxpay' },
  //   { name: '银联', code: 'unionpay' },
  //   { name: '通联', code: 'allinpay' },
  //   { name: '宝付', code: 'baofoo' },
  //   { name: '领客', code: 'paylinx' },
  // ]

  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      accountId: {
        type: Number,
        default: undefined,
      },
      accountTypeList: {
        type: Array,
        default: () => [],
      },
      typeOptions: {
        type: Array,
        default: () => [],
      },
      type: {
        type: String,
        default: '',
      },
      code: {
        type: String,
        default: undefined,
      },
      name: {
        type: String,
        default: undefined,
      },
    },
    data() {
      return {
        loading: false,
        searchLoading: false,
        organizationList: [],
        bankType: null,

        payChannelList: [],

        form: {
          bankCode: '',
          bankName: '',
        },
        bankCodeOptions: [
          {
            value: 1,
            label: 'SWIFT CODE',
          },
          {
            value: 2,
            label: 'ABA NO.',
          },
          {
            value: 3,
            label: 'IBAN NO.',
          },
          {
            value: 4,
            label: 'BSB NO.',
          },
          {
            value: 5,
            label: '其他',
          },
        ],
        rules: {
          bizCode: [
            { required: true, message: '请选择机构', trigger: 'change' },
          ],
          bankType: [
            { required: true, message: '请选择银行类型', trigger: 'change' },
          ],
          parentCode: [
            { required: true, message: '请选择机构', trigger: 'change' },
          ],
          bankCodeType: [
            {
              required: true,
              message: '请选择银行代码类型',
              trigger: 'change',
            },
          ],
          accountName: [
            { required: true, message: '请输入账户名称信息', trigger: 'blur' },
            { trigger: 'change' },
          ],
          // bankCode: [
          //   { required: true, message: '请输入信息', trigger: 'blur' },
          //   { trigger: 'change' },
          // ],
          bankName: [
            { required: true, message: '请输入信息', trigger: 'blur' },
            { trigger: 'change' },
          ],
          capitalPurpose: [
            { required: true, message: '请输入资金用途', trigger: 'blur' },
            { trigger: 'change' },
          ],
          account: [
            { required: true, message: '请输入账号', trigger: 'blur' },
            { trigger: 'change' },
          ],
          companyAdress: [
            { required: true, message: '请输入公司地址', trigger: 'blur' },
            { trigger: 'change' },
          ],
          bankAdress: [
            { required: true, message: '请输入银行地址', trigger: 'blur' },
            { trigger: 'change' },
          ],
          serviceCharge: [
            { required: true, message: '请选择手续费类型', trigger: 'change' },
          ],
        },
        saveLoading: false,
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          if (!value) {
            // 重置
            this.form = {
              bankCode: '',
              bankName: '',
            };
            this.$refs.form.resetFields();
            this.organizationList = [];
          }
          this.$emit('change', value);
        },
      },
      confirmDisable() {
        // return true
        return false;
      },
      payChannel: {
        get() {
          const { bankCode, bankName } = this.form;
          return {
            code: bankCode,
            name: bankName,
          };
        },
        set(value) {
          if (value) {
            const { code, name } = value;
            this.form.bankCode = code;
            this.form.bankName = name;
          }
        },
      },
    },
    watch: {
      show(value) {
        if (value) {
          if (this.accountId) {
            // 编辑
            this.loadData();
            // this.form = this.record
          } else if (this.type != 'all') {
            // 新增
            // 类型
            this.form.parentType = this.type;
            this.form.parentCode = this.code;
            this.organizationList = [
              {
                code: this.code,
                name: this.name,
              },
            ];
          }
        }
      },
    },
    async created() {
      this.payChannelList = await this.getPayChannelList();
    },
    methods: {
      ...mapActions({
        getPayChannelList: 'pay/getPayChannelList',
      }),
      initData() {},
      async loadData() {
        this.loading = true;
        const { err, res } = await this.$apis.liquidation.getAccountDetail(
          this.accountId,
        );
        if (!err) {
          const {
            orgType,
            orgName,
            orgCode,
            agencyName,
            accountName,
            accountTypeCode,
            currencyType,
            currency,
            bankCode,
            bankCodeType,
            bankName,
            bankType,
            bankAccount,
            subbranchName,
            bankAbbreviation,
            capitalPurpose,
            account,
            accountCode,
            serviceCharge,
            companyAdress,
            bankAdress,
            ...data
          } = res;
          this.organizationList = [
            {
              name: agencyName,
              code: orgCode,
            },
          ];
          this.form = {
            // ...data,
            currencyType,
            parentCode: orgCode,
            parentType: orgType,
            accountType: accountTypeCode,
            bankCode,
            bankType,
            bankCodeType,
            currencyType,
            companyAdress,
            currency,
            bankName,
            branchBankName: subbranchName,
            bankAbbreviation,
            capitalPurpose,
            bankAccount,
            account: bankAccount,
            originAccount: account,
            accountName,
            accountCode,
            serviceCharge,
            bankAdress,
          };
        }
        this.loading = false;
      },
      async handleConfirmClick() {
        this.saveLoading = true;
        const isEdit = this.accountId != null;
        const body = { ...this.form };
        if (body.currencyType == 2) {
          body.currency = '多币种';
        }
        if (isEdit) {
          body.id = this.accountId;
          const originAccount = body.originAccount;
          const bankAccount = body.bankAccount;
          if (body.account === bankAccount) {
            // 未改变
            body.account = originAccount;
          }
          delete body.originAccount;
          delete body.bankAccount;
          const { err, res } = await this.$apis.liquidation.updateAccount(body);
          if (!err) {
            this.$emit('update');
            this.showDialog = false;
            this.$message.success('修改账户成功');
          }
        } else {
          const { err, res } = await this.$apis.liquidation.saveAccount(body);
          if (!err) {
            this.$emit('update');
            this.showDialog = false;
            this.$message.success('新增账户成功');
          }
        }
        this.saveLoading = false;
      },

      async onOrganizationSearch(value) {
        if (this.searchLoading) {
          return;
        }
        this.searchLoading = true;
        const {
          err,
          res,
        } = await this.$apis.liquidation.searchSectionAttributes(
          this.form.parentType,
          value,
        );
        if (!err) {
          this.organizationList = res;
        } else {
          this.organizationList = [];
        }
        this.searchLoading = false;
      },
      onCurrencyTypeChange(type) {
        // if (type == 1) {
        //   this.form.currency = '多币种'
        // } else {
        //   this.form.currency = ''
        // }
        // this.$nextTick(() => {
        //   console.log(this.form)
        // })
      },
      onBankTypeChange() {
        this.form.bankCode = null;
        this.form.bankName = null;
      },
    },
  };
</script>
