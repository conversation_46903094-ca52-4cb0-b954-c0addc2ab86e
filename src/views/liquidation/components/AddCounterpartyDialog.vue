<template lang="pug">
el-dialog(
  :visible.sync='showDialog'
  width='40%'
  :append-to-body='appendToBody'
  :close-on-click-modal='false'
)
  el-form(
    ref='form'
    v-loading='loading'
    :model='form'
    :rules='rules'
    label-position='right'
    label-width='150px'
    label-suffix=':'
  )
    el-form-item(label='类型' prop='typeName')
      el-select(
        v-model='form.typeName'
        placeholder='请选择类型'
        style='width: 100%'
      )
        el-option(
          v-for='item in typeOptions'
          :key='item.code'
          :label='item.name'
          :value='item.code'
        )
    el-form-item(label='客户名称' prop='name')
      el-input(v-model='form.name' placeholder='请输入机构名称')
    el-form-item(label='客户关键词' prop='keyword')
      el-input(v-model='form.keyword' placeholder='请输入机构关键词')
    el-form-item(label='NS客户编码' prop='nsCustCode')
      el-input(v-model='form.nsCustCode' placeholder='请输入NS客户编码')
    el-form-item(label='NS供应商编码' prop='nsSupplierCode')
      el-input(v-model='form.nsSupplierCode' placeholder='请输入NS供应商编码')
  span.dialog-footer(slot='footer')
    el-button(@click='showDialog = false') 取消
    el-button(
      type='primary'
      :disabled='confirmDisable'
      :loading='saveLoading'
      @click='handleConfirmClick'
    ) {{ record != null ? "编辑" : "新增" }}
</template>
<script>
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      record: {
        type: Object,
        default: undefined,
      },
      typeOptions: {
        type: Array,
        default: () => [],
      },
      appendToBody: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        loading: false,
        subjectLoading: false,
        subjectList: [],
        form: {},
        rules: {
          typeName: [
            { required: true, message: '请输入机构名称', trigger: 'change' },
          ],
          name: [
            { required: true, message: '请输入机构名称', trigger: 'blur' },
            { trigger: 'change' },
          ],
        },
        saveLoading: false,
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          if (!value) {
            // 重置
            this.form = {};
            this.$refs.form.resetFields();
          }
          this.$emit('change', value);
        },
      },
      confirmDisable() {
        const { name, typeName } = this.form;
        if (name && typeName) {
          return false;
        }
        return true;
      },
    },
    watch: {
      show(value) {
        if (value) {
          if (this.record) {
            // 编辑 更新form
            const {
              id,
              name,
              typeName,
              nsCustCode,
              nsSupplierCode,
              keyword,
            } = this.record;
            this.form = {
              id,
              name,
              typeName,
              nsCustCode,
              nsSupplierCode,
              keyword,
            };
          }
        }
      },
    },
    methods: {
      handleConfirmClick() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.saveData();
          }
        });
      },
      async saveData() {
        this.saveLoading = true;
        const isEdit = this.record != null;
        const body = this.form;
        const { err, res } = await this.$apis.liquidation[
          isEdit ? 'updateCustomer' : 'saveCustomer'
        ](body);
        if (!err) {
          this.$message.success(isEdit ? '修改' : '新增' + '成功');
          this.showDialog = false;
          this.$emit('update');
        }
        this.saveLoading = false;
      },
    },
  };
</script>
