<template lang="pug">
.vertical.top.left.width-100
  .horizontal.left.vcenter.in-vertical.fill-horizontal
    .noshrink 机构名称
    el-input.margin-left-10(
      v-model='name'
      style='width: 200px'
      placeholder='请输入机构名称'
      clearable
    )
    el-select.margin-left-10(
      v-model='type'
      placeholder='请选择'
      style='width: 100px'
      :loading='typeLoading'
    )
      el-option(
        v-for='item in typeOptions'
        :key='item.code'
        :label='item.name'
        :value='item.code'
      )
    el-button.margin-left-10(
      type='primary'
      icon='el-icon-search'
      @click='onSearchClick'
    ) 查询
    ac-permission-button.margin-left-10(
      btn-text='新增'
      permission-key='counterparty-add'
      icon='el-icon-plus'
      @click='onAddClick'
      :disabled='typeOptions.length == 0'
    ) 
  el-table.margin-top-20(
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
  )
    el-table-column(prop='code' label='客户编码' width='150px')
    el-table-column(prop='name' label='客户名称')
    el-table-column(prop='typeName' label='客户类型')
      template(v-slot='{ $index, row }')
        span {{ typeOptionMap[row.typeName] }}

    el-table-column(prop='keyword' label='客户关键词')
    el-table-column(prop='nsCustCode' label='NS客户编码')
    el-table-column(prop='nsSupplierCode' label='NS供应商编码')
    el-table-column(prop='createAt' label='创建时间' width='160px')
    el-table-column(prop='updateAt' label='更新时间' width='160px')
    el-table-column(label='操作')
      template(v-slot='{ $index, row }')
        ac-permission-button(
          type='text'
          btn-text='编辑'
          permission-key='counterparty-edit'
          @click='onRowEdit($index, row)'
        )
        el-button.margin-left-10(
          type='text'
          @click='onAccountClick($index, row)'
        ) 账户
  el-pagination.margin-top-10.margin-bottom-10(
    :current-page='m_current'
    :page-sizes='[10, 15, 20, 30]'
    :page-size='m_pageSize'
    layout='total, sizes, prev, pager, next, jumper'
    :total='m_total'
    background
    @size-change='m_handleSizeChange'
    @current-change='m_handleCurrentChange'
  )
  add-counterparty-dialog(
    v-model='showDialog'
    :record='currentRecord'
    :type-options='typeOptions'
    @update='m_loadData'
  )
</template>
<script>
  import { paginationMixin } from '@/mixins/tableMixin';
  import AddCounterpartyDialog from './components/AddCounterpartyDialog';

  export default {
    components: {
      AddCounterpartyDialog,
    },
    mixins: [paginationMixin],
    data() {
      return {
        name: '',
        type: '',
        searchName: '',
        searchType: '',
        loading: false,
        showDialog: false,
        currentRecord: null,
        list: [],
        typeLoading: false,
        typeOptions: [],
      };
    },
    computed: {
      typeOptionMap() {
        if (this.typeOptions.length == 0) {
          return [];
        }
        const map = {};
        this.typeOptions.forEach(({ code, name }) => {
          map[code] = name;
        });
        return map;
      },
    },
    async created() {
      this.typeLoading = true;
      const { err, res } = await this.$apis.liquidation.getParamDictionaries(4);
      this.typeLoading = false;
      if (!err) {
        this.typeOptions = res;
        this.type = res[0].code;
        this.searchType = this.type;
      } else {
        return;
      }
      this.m_loadData();
    },
    methods: {
      async m_loadData() {
        this.loading = true;
        const body = {
          pageNo: this.m_current,
          limit: this.m_pageSize,
          typeName: this.searchType,
        };
        if (this.searchName && this.searchName.length > 0) {
          body.name = this.searchName;
        }
        const { err, res } = await this.$apis.liquidation.getCustomerList(body);
        if (!err) {
          const { total, pages, size, records } = res;
          this.list = records;
          this.m_total = total;
        }
        this.loading = false;
      },
      onSearchClick() {
        this.searchType = this.type;
        this.searchName = this.name;
        this.m_current = 1;
        this.m_loadData();
      },
      onAddClick() {
        this.currentRecord = null;
        this.showDialog = true;
      },
      onRowEdit(index, row) {
        console.log(index, row);
        this.currentRecord = row;
        this.showDialog = true;
      },
      onAccountClick(index, row) {
        console.log(index, row);
        this.$router.push({
          path: 'account',
          query: { type: 'CUST', code: row.code, name: row.name },
        });
      },
    },
  };
</script>
<style lang="scss" scoped></style>
