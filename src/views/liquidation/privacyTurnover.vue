<template lang="pug">
.vertical.top.left.width-100
  .horizontal.left.vcenter.in-vertical.fill-horizontal
    .noshrink 支付渠道
    el-select.margin-left-10(
      v-model='sourceType'
      placeholder='请选择'
      style='width: 200px'
    )
      el-option(
        v-for='(item, index) in ["全部", "初始状态", "银联", "微信", "支付宝", "宝付", "通联"]'
        :key='item'
        :label='item'
        :value='index - 1'
      )
    .noshrink.margin-left-20 商户号
    el-input.margin-left-10(
      v-model='channelMerNo'
      style='width: 200px'
      placeholder='请输入商户号'
      clearable
    )
    .noshrink.margin-left-20 交易日期
    el-date-picker.margin-left-10(
      v-model='tradingDate'
      type='daterange'
      range-separator='-'
      start-placeholder='开始日期'
      end-placeholder='结束日期'
      :picker-options='datePickerOptions'
      value-format='timestamp'
    )
    el-button.margin-left-10(
      type='primary'
      icon='el-icon-search'
      @click='onSearchClick'
    ) 查询
  el-table.margin-top-20(
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
  )
    el-table-column(prop='id' label='id')
    el-table-column(prop='accountName' label='账户名称')
    el-table-column(prop='sourceType' label='支付渠道')
      template(v-slot='{ row: { sourceType } }')
        span {{ getListData(payChannelList, sourceType) }}
    el-table-column(prop='channelMerNo' label='商户号')
    el-table-column(prop='accountDate' label='交易日期')
    el-table-column(prop='importType' label='导入方式')
      template(v-slot='{ row: { importType } }')
        span {{ getListData(importTypeList, importType) }}
    el-table-column(prop='status' label='状态')
      template(v-slot='{ row: { status } }')
        span {{ getListData(statusList, status) }}
    el-table-column(prop='createTime' label='创建时间' width='170px')
    el-table-column(prop='updateTime' label='更新时间' width='170px')
    el-table-column(label='操作')
      template(v-slot='{ $index, row }')
        a.clickable-text(@click='onRowClick(row, $index)') 明细
  el-pagination.margin-top-10.margin-bottom-10(
    :current-page='m_current'
    :page-sizes='[10, 15, 20, 30]'
    :page-size='m_pageSize'
    layout='total, sizes, prev, pager, next, jumper'
    :total='m_total'
    background
    @size-change='m_handleSizeChange'
    @current-change='m_handleCurrentChange'
  )
  channel-turnover-detail(
    v-model='showDetail'
    :current-id='currentID'
    permission-key='privacyTurnover-export'
  )
</template>
<script>
  import dayjs from 'dayjs';
  import { paginationMixin } from '@/mixins/tableMixin';
  import ChannelTurnoverDetail from './components/ChannelTurnoverDetail';
  import {
    PAY_CHANNEL_LIST,
    IMPORT_TYPE_LIST,
    STATUS_LIST,
  } from '@/consts/liquidation';

  export default {
    components: { ChannelTurnoverDetail },
    mixins: [paginationMixin],
    data() {
      return {
        channelMerNo: '',
        tradingDate: null,
        sourceType: -1,
        loading: false,
        showDetail: false,
        list: [],

        searchSourceType: -1,
        searchChannelMerNo: '',
        searchTradingDate: null,

        payChannelList: PAY_CHANNEL_LIST,
        importTypeList: IMPORT_TYPE_LIST,
        statusList: STATUS_LIST,

        currentID: null,

        datePickerOptions: {
          disabledDate(time) {
            return dayjs(time) >= dayjs().endOf('day');
          },
        },
      };
    },
    created() {
      this.m_loadData();
    },
    methods: {
      getListData(list, index) {
        if (list && index >= 0 && index < list.length) {
          return list[index];
        }
        return '';
      },
      async m_loadData() {
        this.loading = true;

        const body = {
          areaType: 0,
          current: this.m_current,
          size: this.m_pageSize,
        };
        if (this.searchSourceType > -1) {
          body.sourceType = this.searchSourceType;
        }
        if (this.searchChannelMerNo && this.searchChannelMerNo.length > 0) {
          body.channelMerNo = this.searchChannelMerNo;
        }
        if (this.searchTradingDate && this.searchTradingDate.length === 2) {
          body.startTime = this.searchTradingDate[0];
          body.endTime = this.searchTradingDate[1];
        }
        const {
          err,
          res,
        } = await this.$apis.liquidation.getChannelTurnoverList(body);
        if (!err) {
          const { total, pages, size, records } = res;
          this.list = records;
          this.m_total = total;
        }

        this.loading = false;
      },
      onRowClick(row, index) {
        this.currentID = row.id;
        this.showDetail = true;
      },
      onSearchClick() {
        this.m_current = 1;
        this.searchSourceType = this.sourceType;
        this.searchChannelMerNo = this.channelMerNo;
        this.searchTradingDate = this.tradingDate;
        this.m_loadData();
      },
    },
  };
</script>
<style lang="scss" scoped></style>
