<template lang="pug">
.vertical.top.left.width-100
  .horizontal.left.vcenter.in-vertical.fill-horizontal
    .noshrink 店铺名称
    el-input.margin-left-10(
      v-model='name'
      style='width: 200px'
      placeholder='请输入店铺名称'
      clearable
    )
    //- el-select.margin-left-10(
    //-   v-model="type"
    //-   placeholder="请选择"
    //-   style="width: 100px"
    //-   :loading="typeLoading"
    //- )
    //-   el-option(
    //-     v-for="item in typeOptions"
    //-     :key="item.code"
    //-     :label="item.name"
    //-     :value="item.code"
    //-   )
    el-button.margin-left-10(
      type='primary'
      icon='el-icon-search'
      @click='onSearchClick'
    ) 查询
    ac-permission-button.margin-left-10(
      btn-text='新增'
      permission-key='shop-add'
      icon='el-icon-plus'
      @click='onAddClick'
    ) 新增
  el-table.margin-top-20(
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
  )
    el-table-column(prop='code' label='店铺编码')
    el-table-column(prop='name' label='店铺名称')
    el-table-column(prop='keyword' label='关键词')
    el-table-column(prop='orgName' label='公司主体')
    el-table-column(prop='createAt' label='创建时间')
    el-table-column(prop='updateAt' label='更新时间')
    el-table-column(label='操作')
      template(v-slot='{ $index, row }')
        ac-permission-button(
          type='text'
          btn-text='编辑'
          permission-key='shop-edit'
          @click='onRowEdit($index, row)'
        )
        el-button.margin-left-10(
          type='text'
          @click='onAccountClick($index, row)'
        ) 账户
  el-pagination.margin-top-10.margin-bottom-10(
    :current-page='m_current'
    :page-sizes='[10, 15, 20, 30]'
    :page-size='m_pageSize'
    layout='total, sizes, prev, pager, next, jumper'
    :total='m_total'
    background
    @size-change='m_handleSizeChange'
    @current-change='m_handleCurrentChange'
  )
  add-shop-dialog(
    v-model='showDialog'
    :record='currentRecord'
    @update='m_loadData'
  )
</template>
<script>
  import { paginationMixin } from '@/mixins/tableMixin';
  import AddShopDialog from './components/AddShopDialog';

  export default {
    components: {
      AddShopDialog,
    },
    mixins: [paginationMixin],
    data() {
      return {
        name: '',
        searchName: '',
        loading: false,
        showDialog: false,
        currentRecord: null,
        list: [],
      };
    },
    async created() {
      this.m_loadData();
    },
    methods: {
      async m_loadData() {
        this.loading = true;

        const body = {
          pageNo: this.m_current,
          limit: this.m_pageSize,
          // typeName: this.type,
        };
        if (this.searchName && this.searchName.length > 0) {
          body.name = this.searchName;
        }
        const { err, res } = await this.$apis.liquidation.getShopList(body);
        if (!err) {
          const { total, pages, size, records } = res;
          this.list = records;
          this.m_total = total;
        }

        this.loading = false;
      },
      onSearchClick() {
        this.m_current = 1;
        this.searchName = this.name;
        this.m_loadData();
      },
      onAddClick() {
        this.currentRecord = null;
        this.showDialog = true;
      },
      onRowEdit(index, row) {
        this.currentRecord = row;
        this.showDialog = true;
      },
      onAccountClick(index, row) {
        console.log(index, row);
        this.$router.push({
          path: 'account',
          query: { type: 'SHOP', code: row.code, name: row.name },
        });
      },
    },
  };
</script>
<style lang="scss" scoped></style>
