<template lang="pug">
.vertical.top.left.width-100
  .horizontal.left.vcenter.in-vertical.fill-horizontal
    .noshrink 交易日期
    el-date-picker.margin-left-10(
      v-model='transDate'
      :picker-options='datePickerOptions'
      value-format='timestamp'
      type='daterange'
      range-separator='-'
      start-placeholder='开始日期'
      end-placeholder='结束日期'
    )
    .noshrink.margin-left-20 主体账户名称
    el-input.margin-left-10(
      v-model='accountName'
      style='width: 200px'
      placeholder='请输入主体账户名称'
      clearable
    )
    .noshrink.margin-left-20 账户
    el-input.margin-left-10(
      v-model='accountNo'
      style='width: 200px'
      placeholder='请输入账户'
      clearable
    )
    el-button.margin-left-10(
      type='primary'
      icon='el-icon-search'
      @click='onSearchClick'
    ) 查询
  el-table.margin-top-20(
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
  )
    el-table-column(prop='id' label='id')
    el-table-column(prop='transNo' label='交易流水号')
    el-table-column(prop='mainName' label='主体账户名称')
    el-table-column(prop='transAccountNo' label='账户')
    el-table-column(prop='mainName' label='主体账户名称')
    el-table-column(prop='liquidationName' label='清算事项')
    el-table-column(prop='creditAmount' label='贷方发生额')
    el-table-column(prop='debitAmount' label='借方发生额')
    el-table-column(prop='amount' label='交易后余额')
    el-table-column(prop='transDate' label='交易日期')
    el-table-column(prop='updateTime' label='账务日期')
    el-table-column(prop='createTime' label='创建时间')
  el-pagination.margin-top-10.margin-bottom-10(
    :current-page='m_current'
    :page-sizes='[10, 15, 20, 30]'
    :page-size='m_pageSize'
    layout='total, sizes, prev, pager, next, jumper'
    :total='m_total'
    background
    @size-change='m_handleSizeChange'
    @current-change='m_handleCurrentChange'
  )
</template>
<script>
  import dayjs from 'dayjs';
  import { paginationMixin } from '@/mixins/tableMixin';

  export default {
    components: {},
    mixins: [paginationMixin],
    data() {
      return {
        transDate: null,
        searchTransDate: null,

        accountName: '',
        searchAccountName: '',
        accountNo: '',
        searchAccountNo: '',
        loading: false,
        list: [],
        datePickerOptions: {
          disabledDate(time) {
            return dayjs(time) >= dayjs().endOf('day');
          },
        },
      };
    },
    created() {
      this.m_loadData();
    },
    methods: {
      async m_loadData() {
        this.loading = true;

        const body = {
          current: this.m_current,
          size: this.m_pageSize,
        };
        if (this.searchTransDate && this.searchTransDate.length == 2) {
          body.billDateStart = this.searchTransDate[0];
          body.billDateEnd = this.searchTransDate[1];
        }
        if (this.searchAccountName && this.searchAccountName.length > 0) {
          body.accountName = this.searchAccountName;
        }
        if (this.searchAccountNo && this.searchAccountNo.length > 0) {
          body.accountNo = this.searchAccountNo;
        }
        const { err, res } = await this.$apis.liquidation.getCapitalproofList(
          body,
        );
        if (!err) {
          const { total, pages, size, records } = res;
          this.list = records;
          this.m_total = total;
        }

        this.loading = false;
      },
      onSearchClick() {
        this.m_current = 1;
        this.searchTransDate = this.transDate;
        this.searchAccountName = this.accountName;
        this.searchAccountNo = this.accountNo;
        this.m_loadData();
      },
    },
  };
</script>
<style lang="scss" scoped></style>
