<template lang="pug">
.vertical.top.left.width-100
  .horizontal.left.vcenter.in-vertical.fill-horizontal
    el-upload(
      :action='action'
      :on-success='onUploadSuccess'
      :http-request='httpRequest'
      :show-file-list='false'
      :disabled='fileUploading'
    )
      el-button.margin-left-10(
        v-loading='fileUploading'
        icon='el-icon-upload'
        :disabled='fileUploading'
      ) 导入清算规则
  el-table.margin-top-20(
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
  )
    el-table-column(prop='id' label='id')
    el-table-column(prop='capital' label='账户主体')
    el-table-column(prop='capitalValue' label='值')
    el-table-column(prop='account' label='交易账户')
    el-table-column(prop='accountValue' label='值')
    el-table-column(prop='tradingSummary' label='交易摘要')
    el-table-column(prop='debitLiquidationName' label='借方-清算事项')
    el-table-column(prop='creditLiquidationName' label='贷方-清算事项')
    el-table-column(prop='createTime' label='创建时间')
    el-table-column(prop='creater' label='创建人')
    el-table-column(prop='updateTime' label='更新时间')
    el-table-column(label='操作')
      template(v-slot='{ $index, row }')
        ac-permission-button(
          v-loading='deleteLoading'
          type='text'
          btn-text='删除'
          permission-key='rule-delete'
          @click='onDeleteClick(row)'
        )
        //- el-popconfirm(
        //-   confirm-button-text='确定'
        //-   cancel-button-text='取消'
        //-   icon='el-icon-info'
        //-   icon-color='red'
        //-   title='确定删除吗？'
        //-   @confirm="onDeleteClick(row, $index)"
        //- )
  el-pagination.margin-top-10.margin-bottom-10(
    :current-page='m_current'
    :page-sizes='[10, 15, 20, 30]'
    :page-size='m_pageSize'
    layout='total, sizes, prev, pager, next, jumper'
    :total='m_total'
    background
    @size-change='m_handleSizeChange'
    @current-change='m_handleCurrentChange'
  )
</template>
<script>
  import dayjs from 'dayjs';
  import { paginationMixin } from '@/mixins/tableMixin';
  import { service } from '@/utils/request';

  export default {
    components: {},
    mixins: [paginationMixin],
    data() {
      return {
        fileUploading: false,
        accountingDate: [],
        account: '',

        action:
          window.location.protocol +
          '//' +
          window.ACCESS_HOSTS.apiHost +
          '/api/recon/liquidation/rule/fileUpload',

        loading: false,
        deleteLoading: false,
        showDialog: false,
        showDetail: false,
        currentID: null,
        list: [],
        datePickerOptions: {
          disabledDate(time) {
            return dayjs(time) >= dayjs().endOf('day');
          },
        },
      };
    },
    async created() {
      this.m_loadData();
    },
    methods: {
      async m_loadData() {
        this.loading = true;
        const body = {
          size: this.m_pageSize,
          current: this.m_current,
        };
        const { res, err } = await this.$apis.liquidation.getLiquidationList(
          body,
        );
        if (!err) {
          const { records, total } = res;
          this.m_total = total;
          this.list = records;
        }
        this.loading = false;
      },
      // onImportClick() {},
      onUploadSuccess() {},
      async httpRequest(request) {
        this.fileUploading = true;

        const { action, file, data, onError, onSuccess } = request;
        const formData = new FormData();
        formData.append('file', file);
        const { err, res } = await service.instance.http.post(
          action,
          formData,
          { timeout: 60 * 1000 }, // 增加超时
        );
        // onSuccess(res)
        if (err) {
          onError(err);
        } else {
          onSuccess(res);
          this.$message.success('导入成功');
          // todo 导入成功
          this.m_current = 1;
          this.m_loadData();
        }
        this.fileUploading = false;
      },
      onDeleteClick(row) {
        this.$confirm('此操作将删除该条记录，是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.deleteRecord(row.id);
          })
          .catch(() => {});
      },
      async deleteRecord(id) {
        this.deleteLoading = true;
        const { err, res } = await this.$apis.liquidation.deleteLiquidation(id);
        if (!err) {
          this.$message.success('删除成功');
          this.m_loadData();
          // this.list.splice(index, 1)
        }
        this.deleteLoading = false;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
