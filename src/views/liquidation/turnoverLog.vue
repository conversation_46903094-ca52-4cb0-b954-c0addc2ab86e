<template lang="pug">
.vertical.top.left.width-100
  .horizontal.left.vcenter.in-vertical.fill-horizontal
    .noshrink 账号
    el-input.margin-left-10(
      v-model='accountCode'
      style='width: 200px'
      placeholder='请输入账号'
      clearable
    )
    .noshrink.margin-left-20 银行账号
    el-input.margin-left-10(
      v-model='accountNoOrSuffix'
      style='width: 200px'
      placeholder='全量或尾号后四位'
      clearable
    )
    .noshrink.margin-left-20 账务日期
      el-date-picker.margin-left-10(
        v-model='selectDate'
        type='daterange'
        range-separator='-'
        start-placeholder='开始日期'
        end-placeholder='结束日期'
        :picker-options='datePickerOptions'
        value-format='timestamp'
      )

    el-button.margin-left-10(
      type='primary'
      icon='el-icon-search'
      @click='onSearchClick'
    ) 查询
    //- el-button.margin-left-10(
    //-   icon="el-icon-upload"
    //-   @click="onAddClick"
    //- ) 导入文件
  el-table.margin-top-20(
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
  )
    el-table-column(prop='id' label='id')
    el-table-column(prop='batchNo' label='批次编号')
    el-table-column(prop='accountCode' label='账户编码')
    el-table-column(prop='accountName' label='账户名称')
    el-table-column(prop='bankAccountNo' label='银行账号')
    el-table-column(prop='accountType' label='账户类型')
    el-table-column(prop='inputType' label='导入类型')
    el-table-column(prop='startTransDate' label='财务开始日期')
    el-table-column(prop='endTransDate' label='财务结束日期')
    el-table-column(prop='status' label='批次状态')
    el-table-column(prop='createTime' label='创建时间')
    el-table-column(prop='updateTime' label='更新时间')
    el-table-column(prop='inputUserName' label='操作人')
    el-table-column(label='操作')
      template(v-slot='{ $index, row }')
        el-button(type='text' @click='onRowDetailClick($index, row)') 明细查询
        ac-permission-button.margin-left-10(
          v-loading='rollbackLoading'
          type='text'
          btn-text='回滚'
          permission-key='turnoverLog-RollBACK'
          @click='onRollbackClick(row)'
        )
        //- el-popconfirm.margin-left-10(
        //-   confirm-button-text='确定'
        //-   cancel-button-text='取消'
        //-   icon='el-icon-info'
        //-   icon-color='red'
        //-   title='确定进行回滚操作么？'
        //-   @confirm="callRollback"
        //- )
        //-   a.clickable-text(slot='reference' v-loading="rollbackLoading" @click="onRollbackClick(row)" ) 回滚
  el-pagination.margin-top-10.margin-bottom-10(
    :current-page='m_current'
    :page-sizes='[10, 15, 20, 30]'
    :page-size='m_pageSize'
    layout='total, sizes, prev, pager, next, jumper'
    :total='m_total'
    background
    @size-change='m_handleSizeChange'
    @current-change='m_handleCurrentChange'
  )
  turnover-detail-dialog(v-model='showDetail' :batch-no='currentBatchNo')
</template>
<script>
  import dayjs from 'dayjs';
  import { paginationMixin } from '@/mixins/tableMixin';
  import TurnoverDetailDialog from './components/TurnoverDetailDialog';

  export default {
    components: {
      TurnoverDetailDialog,
    },
    mixins: [paginationMixin],
    data() {
      return {
        rollbackLoading: false,
        loading: false,
        accountCode: '',
        accountNoOrSuffix: '',
        selectDate: null,

        searchAccountCode: '',

        showDialog: false,
        showDetail: false,
        currentBatchNo: null,

        list: [],
        datePickerOptions: {
          disabledDate(time) {
            return dayjs(time) >= dayjs().endOf('day');
          },
        },
      };
    },
    created() {
      this.m_loadData();
    },
    methods: {
      async m_loadData() {
        this.loading = true;
        const params = {
          limit: this.m_pageSize,
          page: this.m_current,
        };
        if (this.searchAccountCode && this.searchAccountCode.length > 0) {
          params.accountCode = this.accountCode;
        }

        if (
          this.searchAccountNoOrSuffix &&
          this.searchAccountNoOrSuffix.length > 0
        ) {
          params.accountNoOrSuffix = this.searchAccountNoOrSuffix;
        }
        if (this.searchSelectDate && this.searchSelectDate.length == 2) {
          params.startDate = this.searchSelectDate[0];
          params.lastDate = this.searchSelectDate[1];
        }

        const { err, res } = await this.$apis.liquidation.getTurnoverLogList(
          params,
        );
        if (!err) {
          const { records, total } = res;
          this.m_total = total;
          this.list = records;
        }
        this.loading = false;
      },
      onSearchClick() {
        this.m_current = 1;
        this.searchAccountCode = this.accountCode;
        this.searchAccountNoOrSuffix = this.accountNoOrSuffix;
        this.searchSelectDate = this.selectDate;
        this.m_loadData();
      },
      onRowDetailClick(index, row) {
        this.currentBatchNo = row.batchNo;
        this.showDetail = true;
      },
      onRollbackClick(row) {
        // this.callRollback(row.batchNo)
        // this.currentBatchNo = row.batchNo
        this.$confirm('此操作将删除该条记录，是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.callRollback(row.batchNo);
          })
          .catch(() => {});
      },
      async callRollback(batchNo) {
        console.log('in callrollbakc', batchNo);
        this.rollbackLoading = true;
        const { err, res } = await this.$apis.liquidation.deleteTurnoverLog(
          batchNo,
        );
        if (!err) {
          this.$message.success('操作成功');
          this.m_loadData();
        }
        this.currentBatchNo = null;

        this.rollbackLoading = false;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
