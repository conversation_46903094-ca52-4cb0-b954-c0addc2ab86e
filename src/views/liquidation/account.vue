<template lang="pug">
.vertical.top.left.width-100
  .horizontal.left.vcenter.in-vertical.fill-horizontal
    .noshrink 机构编码
    el-input.margin-left-10(
      v-model='orgCode'
      style='width: 200px'
      placeholder='请输入机构编码'
      clearable
    )
    .noshrink.margin-left-20 账户名称
    el-input.margin-left-10(
      v-model='accountName'
      style='width: 200px'
      placeholder='请输入账户名称'
      clearable
    )
    .noshrink.margin-left-20 银行账户
    el-input.margin-left-10(
      v-model='bankAccount'
      style='width: 200px'
      placeholder='请输入银行账户'
      clearable
    )
    .noshrink.margin-left-20 开户银行
    el-input.margin-left-10(
      v-model='bankName'
      style='width: 200px'
      placeholder='请输入开户银行'
      clearable
    )
    .noshrink.margin-left-20 支行
    el-input.margin-left-10(
      v-model='subbranchName'
      style='width: 200px'
      placeholder='请输入支行'
      clearable
    )
    el-button.margin-left-10(
      type='primary'
      icon='el-icon-search'
      @click='onSearchClick'
    ) 查询
    el-button.margin-left-10(
      v-if='showAdd'
      icon='el-icon-plus'
      @click='onAddClick'
      :disabled='typeOptions.length == 0 || accountTypeList.length == 0'
    ) 新增
  .horizontal.left.vcenter.in-vertical.fill-horizontal.margin-top-20
    .noshrink 机构类型
    el-radio-group.margin-left-10(v-model='orgType')
      el-radio(label='all') 全部
      el-radio(
        v-for='option in typeOptions'
        :label='option.code'
        :key='option.code'
      ) {{ option.name }}
  .horizontal.left.vcenter.in-vertical.fill-horizontal.margin-top-20
    .noshrink 账户类型
    el-radio-group.margin-left-10(
      v-model='accountTypeCode'
      v-loading='typeLoading'
    )
      el-radio(label='all') 全部
      el-radio(
        v-for='type in accountTypeList'
        :label='type.code'
        :key='type.code'
      ) {{ type.name }}
  el-table.margin-top-20(
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
  )
    el-table-column(prop='orgCode' label='机构编码')
    el-table-column(prop='orgTypeName' label='机构类型')
    el-table-column(prop='accountCode' label='账户编码')
    el-table-column(prop='accountTypeName' label='账户类型')
    el-table-column(prop='accountName' label='账户名称')
    el-table-column(prop='agencyName' label='机构名称')
    el-table-column(prop='bankAccount' label='银行账号')
    el-table-column(prop='currency' label='币种')
    el-table-column(prop='bankName' label='开户银行')
    el-table-column(prop='subbranchName' label='支行')
    el-table-column(prop='capitalPurpose' label='资金用途')
    el-table-column(prop='serviceChargeName' label='手续费')
    el-table-column(prop='createAt' label='创建时间')
    el-table-column(prop='updateAt' label='更新时间')
    el-table-column(label='操作')
      template(v-slot='{ $index, row }')
        .vertical.vcenter.hcenter
          ac-permission-button(
            type='text'
            btn-text='编辑'
            permission-key='account-edit'
            @click='onRowEdit($index, row)'
          )
          ac-permission-button(
            type='text'
            btn-text='导入'
            permission-key='account-import'
            @click='onRowImport($index, row)'
          )
  el-pagination.margin-top-10.margin-bottom-10(
    :current-page='m_current'
    :page-sizes='[10, 15, 20, 30]'
    :page-size='m_pageSize'
    layout='total, sizes, prev, pager, next, jumper'
    :total='m_total'
    background
    @size-change='m_handleSizeChange'
    @current-change='m_handleCurrentChange'
  )
  account-dialog(
    v-model='showDialog'
    :account-id='currentID'
    :name='startName'
    :code='startCode'
    :account-type-list='accountTypeList'
    :type-options='typeOptions'
    :type='startOrgType'
    @update='m_loadData'
  )
  import-turnover-dialog(
    v-model='showUpload'
    :account-code='currentAccount'
    :account-type-name='currentAccountType'
    :bank-code='currentBankCode'
  )
</template>
<script>
  import { paginationMixin } from '@/mixins/tableMixin';
  import AccountDialog from './components/AccountDialog';
  import ImportTurnoverDialog from './components/ImportTurnoverDialog';

  export default {
    components: {
      ImportTurnoverDialog,
      AccountDialog,
    },
    mixins: [paginationMixin],
    data() {
      return {
        accountName: '',
        orgCode: '',
        bankAccount: '',
        bankName: '',
        subbranchName: '',
        orgType: 'all',
        startOrgType: 'all',
        accountTypeCode: 'all',
        showAdd: false,
        showUpload: false,
        currentAccount: null,
        currentAccountType: null,
        currentBankCode: null,

        name: '',
        startName: '',
        startCode: '',
        loading: false,
        showDialog: false,
        currentID: null,
        list: [],
        typeLoading: false,
        typeOptions: [],
        accountTypeList: [],
      };
    },
    async created() {
      const { type, code, name } = this.$route.query;
      if (['SUBJECT', 'CUST', 'SHOP'].includes(type)) {
        this.orgType = type;
        this.startOrgType = type;
        this.startName = name;
        this.startCode = code;
        this.orgCode = code;
        this.showAdd = true;
      }
      const checked = await this.loadAccountType();
      if (checked) {
        this.m_loadData();
      }
    },
    methods: {
      async m_loadData() {
        this.loading = true;
        const body = {
          pageNo: this.m_current,
          limit: this.m_pageSize,
          // typeName: this.type,
        };
        if (this.accountName) {
          body.accountName = this.accountName;
        }
        if (this.accountTypeCode != 'all') {
          body.accountTypeCode = this.accountTypeCode;
        }
        if (this.bankAccount) {
          body.bankAccount = this.bankAccount;
        }
        if (this.bankName) {
          body.bankName = this.bankName;
        }
        if (this.orgType != 'all') {
          // body.orgType = 1
          body.orgType = this.orgType;
        }
        if (this.orgCode) {
          body.orgCode = this.orgCode;
        }
        if (this.subbranchName) {
          body.subbranchName = this.subbranchName;
        }
        const { err, res } = await this.$apis.liquidation.getAccountList(body);
        if (!err) {
          const { total, pages, size, records } = res;
          this.list = records;
          this.m_total = total;
        }

        this.loading = false;
      },
      async loadAccountType() {
        this.typeLoading = true;
        const { err, res } = await this.$apis.liquidation.getParamDictionaries(
          1,
        );
        const result = await this.$apis.liquidation.getParamDictionaries(2);
        if (!result.err) {
          this.accountTypeList = result.res;
        } else {
          return false;
        }
        this.typeLoading = false;
        if (!err) {
          this.typeOptions = res;
        } else {
          return false;
        }
        return true;
      },
      onSearchClick() {
        this.m_current = 1;
        this.m_loadData();
      },
      onAddClick() {
        this.currentID = null;
        this.showDialog = true;
      },
      onRowEdit(index, row) {
        this.currentID = row.id;
        this.showDialog = true;
      },
      onRowImport(index, row) {
        this.currentAccount = row.accountCode;
        this.currentAccountType = row.accountTypeName;
        this.currentBankCode = row.bankCode;
        this.showUpload = true;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
