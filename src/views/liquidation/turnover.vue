<template lang="pug">
.vertical.top.left.width-100
  .horizontal.left.vcenter.in-vertical.fill-horizontal
    .noshrink 交易日期
    el-date-picker.margin-left-10(
      v-model='tradingDate'
      type='daterange'
      range-separator='-'
      start-placeholder='开始日期'
      end-placeholder='结束日期'
      :picker-options='datePickerOptions'
      value-format='timestamp'
    )
    .noshrink.margin-left-20 主体账户名称
    el-input.margin-left-10(
      v-model='transMainName'
      style='width: 200px'
      placeholder='请输入主体账户名称'
      clearable
    )
    .noshrink.margin-left-20 清算规则匹配状态
    el-select.margin-left-10(
      v-model='status'
      placeholder='请选择'
      style='width: 100px'
    )
      el-option(
        v-for='(item, index) in ["未匹配", "已匹配"]'
        :key='item'
        :label='item'
        :value='index'
      )
    el-button.margin-left-10(
      type='primary'
      icon='el-icon-search'
      @click='onSearchClick'
    ) 查询
  .horizontal.left.vcenter.in-vertical.fill-horizontal.margin-top-20
    .noshrink 批次编号
    el-input.margin-left-10(
      v-model='batchNo'
      style='width: 200px'
      placeholder='请输入批次编号'
      clearable
    )
    ac-permission-button.margin-left-10(
      v-loading='batchMatching'
      type='primary'
      btn-text='批量匹配'
      permission-key='subject-edit'
      @click='onBatchMatch'
      :disabled='!batchNo || batchNo.length == 0'
    )
  el-table.margin-top-20.vertical.top.left.in-vertical.fill-vertical(
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
  )
    el-table-column(prop='id' label='id')
    el-table-column(prop='transNo' label='交易流水号' width='120px')
    el-table-column(prop='batchNo' label='批次号' width='180px')
    el-table-column(prop='transDate' label='交易日期' width='180px')
    el-table-column(prop='transMainName' label='账户名称' width='120px')
    el-table-column(prop='transAccountNo' label='账号' width='150px')
    el-table-column(prop='liquidationName' label='清算事项' width='120px')
    el-table-column(prop='debitAmount' label='借方发生额' width='100px')
    el-table-column(prop='creditAmount' label='贷方发生额' width='100px')
    el-table-column(prop='amount' label='交易后余额' width='100px')
    el-table-column(prop='rivalAccountNo' label='对方账号' width='130px')
    el-table-column(prop='rivalMainName' label='对方账户名称' width='110px')
    el-table-column(prop='brief' label='摘要')
    el-table-column(prop='note' label='备注')
    el-table-column(prop='status' label='匹配状态')
      template(v-slot='{ row: { status } }')
        span {{ status == 1 ? "已匹配" : "未匹配" }}
    el-table-column(prop='createTime' label='创建时间' width='180px')
    el-table-column(label='操作' fixed='right')
      template(v-slot='{ $index, row }')
        ac-permission-button(
          v-if='row.status != 1'
          type='text'
          btn-text='匹配规则'
          permission-key='turnover-match-rule'
          @click='onRowClick(row, $index)'
        )
        //- a.clickable-text(v-if="row.status == 1" @click="onManualMatchClick(row, $index)") 手动匹配
  el-pagination.margin-top-10.margin-bottom-10(
    :current-page='m_current'
    :page-sizes='[10, 15, 20, 30]'
    :page-size='m_pageSize'
    layout='total, sizes, prev, pager, next, jumper'
    :total='m_total'
    background
    @size-change='m_handleSizeChange'
    @current-change='m_handleCurrentChange'
  )
  liquidation-rule-dialog(
    v-model='showManualMatchDialog'
    :current-id='currentID'
    @success='m_loadData'
  )
</template>
<script>
  import dayjs from 'dayjs';
  import { paginationMixin } from '@/mixins/tableMixin';
  import LiquidationRuleDialog from './components/LiquidationRuleDialog';

  export default {
    components: { LiquidationRuleDialog },
    mixins: [paginationMixin],
    data() {
      return {
        tradingDate: null,
        transMainName: '',
        status: null,

        batchNo: '',
        batchMatching: false,

        showManualMatchDialog: false,

        searchTrandingDate: null,
        searchTransMainName: '',
        searchStatus: null,

        loading: false,
        currentID: null,
        list: [],
        datePickerOptions: {
          disabledDate(time) {
            return dayjs(time) >= dayjs().endOf('day');
          },
        },
      };
    },
    created() {
      this.m_loadData();
    },
    methods: {
      async m_loadData() {
        this.loading = true;
        const params = {
          limit: this.m_pageSize,
          page: this.m_current,
        };
        if (this.searchTransMainName && this.searchTransMainName.length > 0) {
          params.transMainName = this.searchTransMainName;
        }

        if (this.searchStatus != null) {
          params.status = this.searchStatus;
        }
        if (this.searchTrandingDate && this.searchTrandingDate.length == 2) {
          params.startTransDate = this.searchTrandingDate[0];
          params.endTransDate = this.searchTrandingDate[1];
        }

        const { err, res } = await this.$apis.liquidation.getTurnoverList(
          params,
        );
        if (!err) {
          const { records, total } = res;
          this.m_total = total;
          this.list = records;
        }
        this.loading = false;
      },
      onSearchClick() {
        this.m_current = 1;
        this.searchTrandingDate = this.tradingDate;
        this.searchTransMainName = this.transMainName;
        this.searchStatus = this.status;

        this.m_loadData();
      },
      onRowClick(row, index) {
        this.rechecked(row.id);
      },
      async rechecked(id) {
        const { err, res } = await this.$apis.liquidation.recheckedTurnover(id);
        if (!err) {
          this.$message.success('操作成功');
          this.m_loadData();
        }
      },
      onManualMatchClick(row, index) {
        this.currentID = row.id;
        this.showManualMatchDialog = true;
      },
      onBatchMatch() {
        if (this.batchNo) {
          this.callBatchMatch(this.batchNo);
        }
      },
      async callBatchMatch(batchNo) {
        this.batchMatching = true;
        const { err, res } = await this.$apis.liquidation.batchMatchTurnover(
          batchNo,
        );
        if (!err) {
          this.$message.success('操作成功');
          this.m_loadData();
        }

        this.batchMatching = false;
      },
    },
  };
</script>
<style lang="scss" scoped>
  ::v-deep {
    .el-table__body-wrapper {
      flex: 1 1;
    }
  }
</style>
