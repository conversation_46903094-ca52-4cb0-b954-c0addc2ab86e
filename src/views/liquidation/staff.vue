<template lang="pug">
.vertical.top.left.width-100
  .horizontal.left.vcenter.in-vertical.fill-horizontal
    .noshrink 员工名称
    el-input.margin-left-10(
      v-model='name'
      style='width: 200px'
      placeholder='请输入员工名称'
      clearable
    )
    .noshrink.margin-left-20 部门名称
    el-input.margin-left-10(
      v-model='department'
      style='width: 200px'
      placeholder='请输入部门名称'
      clearable
    )
    el-button.margin-left-10(
      type='primary'
      icon='el-icon-search'
      @click='onSearchClick'
    ) 查询
    //- el-button.margin-left-10(
    //-   icon="el-icon-plus"
    //-   @click="onAddClick"
    //- ) 新增
  el-table.margin-top-20(
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
  )
    el-table-column(prop='code' label='员工编码')
    el-table-column(prop='name' label='员工名称')
    el-table-column(prop='cardNo' label='卡号')
    el-table-column(prop='department' label='部门')
    el-table-column(prop='subJectName' label='公司主体')
    el-table-column(prop='costCenter' label='成本中心')
    el-table-column(prop='costCenterCode' label='成本中心编码')
    el-table-column(prop='nsCustCode' label='NS客户编码 ')
    el-table-column(prop='nsSupplierCode' label='NS供应商编码')
    el-table-column(prop='createAt' label='创建时间' width='170px')
    el-table-column(prop='updateTime' label='更新时间' width='170px')
    //- el-table-column(label='操作')
    //-   template(v-slot="{ $index, row }")
    //-     a.clickable-text(@click="onRowEdit($index,row)") 编辑
  el-pagination.margin-top-10.margin-bottom-10(
    :current-page='m_current'
    :page-sizes='[10, 15, 20, 30]'
    :page-size='m_pageSize'
    layout='total, sizes, prev, pager, next, jumper'
    :total='m_total'
    background
    @size-change='m_handleSizeChange'
    @current-change='m_handleCurrentChange'
  )
</template>
<script>
  import { paginationMixin } from '@/mixins/tableMixin';

  export default {
    mixins: [paginationMixin],
    data() {
      return {
        name: '',
        department: '',
        searchName: '',
        searchDepartment: '',
        loading: false,
        showDialog: false,
        currentID: null,
        list: [],
      };
    },
    created() {
      this.m_loadData();
    },
    methods: {
      async m_loadData() {
        this.loading = true;
        const body = {
          pageNo: this.m_current,
          limit: this.m_pageSize,
        };
        if (this.searchName && this.searchName.length > 0) {
          body.name = this.searchName;
        }
        if (this.searchDepartment && this.searchDepartment.length > 0) {
          body.department = this.searchDepartment;
        }
        const { err, res } = await this.$apis.liquidation.getStaffList(body);
        if (!err) {
          const { total, pages, size, records } = res;
          this.list = records;
          this.m_total = total;
        }
        this.loading = false;
      },
      onSearchClick() {
        this.m_current = 1;
        this.searchName = this.name;
        this.searchDepartment = this.department;
        this.m_loadData();
      },
      onAddClick() {
        this.currentID = null;
        this.showDialog = true;
      },
      onRowEdit(index, row) {
        console.log(index, row);
        this.currentID = index;
        this.showDialog = true;
      },
      onAccountClick(index, row) {
        console.log(index, row);
      },
    },
  };
</script>
<style lang="scss" scoped></style>
