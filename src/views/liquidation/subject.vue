<template lang="pug">
.vertical.top.left.width-100
  .horizontal.left.vcenter.in-vertical.fill-horizontal
    .noshrink 主体名称
    el-input.margin-left-10(
      v-model='name'
      style='width: 200px'
      placeholder='支持模糊查询'
      clearable
    )
    el-button.margin-left-10(
      type='primary'
      icon='el-icon-search'
      @click='onSearchClick'
    ) 查询
    ac-permission-button.margin-left-10(
      btn-text='新增'
      permission-key='subject-add'
      icon='el-icon-plus'
      @click='onAddClick'
    ) 
  el-table.margin-top-20(
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
  )
    el-table-column(prop='code' label='主体编码')
    el-table-column(prop='name' label='主体名称')
    el-table-column(prop='keyword' label='关键词')
    el-table-column(prop='adress' label='所在地')
    el-table-column(prop='codePrefix' label='编码前缀')
    el-table-column(prop='nsCustCode' label='NS客户编码')
    el-table-column(prop='nsSupplierCode' label='NS供应商编码')
    el-table-column(prop='parentName' label='父主体')
    el-table-column(prop='createAt' label='创建时间')
    el-table-column(prop='updateAt' label='更新时间')
    el-table-column(label='操作')
      template(v-slot='{ $index, row }')
        ac-permission-button(
          type='text'
          btn-text='编辑'
          permission-key='subject-edit'
          @click='onRowEdit($index, row)'
        )
        el-button.margin-left-10(
          type='text'
          @click='onAccountClick($index, row)'
        ) 账户
  el-pagination.margin-top-10.margin-bottom-10(
    :current-page='m_current'
    :page-sizes='[10, 15, 20, 30]'
    :page-size='m_pageSize'
    layout='total, sizes, prev, pager, next, jumper'
    :total='m_total'
    background
    @size-change='m_handleSizeChange'
    @current-change='m_handleCurrentChange'
  )
  subject-dialog(
    v-model='showDialog'
    :record='currentRecord'
    @update='m_loadData'
  )
</template>
<script>
  import { paginationMixin } from '@/mixins/tableMixin';
  import SubjectDialog from './components/SubjectDialog';

  export default {
    components: {
      SubjectDialog,
    },
    mixins: [paginationMixin],
    data() {
      return {
        name: '',

        searchName: '',
        loading: false,
        list: [],
        showDialog: false,
        currentRecord: null,
      };
    },
    created() {
      this.m_loadData();
    },
    methods: {
      async m_loadData() {
        this.loading = true;
        const body = {
          pageNo: this.m_current,
          limit: this.m_pageSize,
        };
        if (this.searchName && this.searchName.length > 0) {
          body.name = this.searchName;
        }
        const { err, res } = await this.$apis.liquidation.getSubjectList(body);
        if (!err) {
          const { total, pages, size, records } = res;
          this.list = records;
          this.m_total = total;
        }
        this.loading = false;
      },
      onSearchClick() {
        this.m_current = 1;
        this.searchName = this.name;
        this.m_loadData();
      },
      onAddClick() {
        this.currentRecord = null;
        this.showDialog = true;
      },
      onRowEdit(index, row) {
        this.currentRecord = row;
        this.showDialog = true;
      },
      onAccountClick(index, row) {
        this.$router.push({
          path: 'account',
          query: { type: 'SUBJECT', code: row.code, name: row.name },
        });
      },
    },
  };
</script>
<style lang="scss" scoped></style>
