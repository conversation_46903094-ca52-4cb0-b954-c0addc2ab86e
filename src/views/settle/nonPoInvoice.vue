<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-11 15:53:42
 * @LastEditTime: 2022-11-16 11:20:44
 * @LastEditors: Please set LastEditors
 * @Reference: 
-->
<template>
  <div>
    <el-form inline>
      <el-form-item label="请款时间:">
        <el-date-picker
          v-model="applyDate"
          type="daterange"
          :clearable="true"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="付款主体:">
        <el-select
          v-model="searchParams.principalCode"
          clearable
          filterable
          placeholder="请选择公司名称"
        >
          <el-option
            v-for="item in principalCode"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="付款方式:">
        <el-select
          v-model="searchParams.paymentMethod"
          clearable
          filterable
          placeholder="请选择付款方式"
        >
          <el-option
            v-for="item in paymentMethod"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="OA支付流程号:">
        <el-input
          v-model="searchParams.oaPaymentRequisitionId"
          placeholder="请输入OA支付流程号"
        />
      </el-form-item>
      <el-form-item label="付款状态:">
        <el-select
          v-model="searchParams.paymentStatus"
          clearable
          placeholder="请选择付款款状态"
        >
          <el-option
            v-for="item in paymentStatus"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="供应商名称:">
        <el-input
          v-model="searchParams.supplierName"
          placeholder="供应商名称"
        />
      </el-form-item>
      <el-form-item label="请款币种:">
        <el-select
          v-model="searchParams.requisitionCurrency"
          placeholder="请输入请款币种"
        >
          <el-option
            v-for="item in requestCurrency"
            :key="item.key"
            :label="item.value"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="记账状态:">
        <el-select
          v-model="searchParams.recordVoucherStatus"
          clearable
          filterable
          placeholder="请选择记账状态"
        >
          <el-option
            v-for="item in recordVoucher"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button type="primary" @click="exportResult">导出</el-button>
        <!-- <el-button type="primary" @click="onExport">导出</el-button> -->
      </el-form-item>
    </el-form>
    <el-row>
      <div style="display: inline-block">
        <span v-text="`请款金额汇总：${total.amountTotal}`"></span>
        <!-- todo -->
      </div>
    </el-row>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          btn-text="查看明细"
          type="text"
          size="small"
          permission-key=""
          @click="viewDetails(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <!-- <poIncoveDetails v-model="showDialog"></poIncoveDetails> -->
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  // import poIncoveDetails from './components/poIncoveDetails';
  import { newExportExcel, exportExcel } from '@/api/blob';
  import { parseTime, setInitData, downloadFile } from '@/utils';

  import {
    getNpPaymentRequisitionPage,
    getNpPaymentRequisitionDic,
    getRequisitionAmountTotal,
    exportExcelFile,
  } from '@/api/settle';

  export default {
    name: 'NonPoInvoice',
    components: {
      dynamictable,
      // poIncoveDetails,
    },
    data() {
      let columns = [
        {
          prop: 'serialNo',
          label: '请款单号',
        },
        {
          prop: 'recordVoucherDescription',
          label: '记账状态',
        },
        {
          prop: 'oaPaymentRequisitionId',
          label: 'OA支付流程号',
        },
        {
          prop: 'requisitionCurrency',
          label: '请款币种',
        },
        {
          prop: 'supplierName',
          label: '供应商名称',
        },
        {
          prop: 'totalAmount',
          label: '请款金额',
        },
        {
          prop: 'requisitionDate',
          label: '请款时间',
        },
        {
          prop: 'principalName',
          label: '付款主体',
        },
        {
          prop: 'paymentMethodKey',
          label: '付款方式',
        },
        {
          prop: 'dueDate',
          label: '截至时间',
        },
        {
          prop: 'paymentStatusKey',
          label: '付款状态',
        },
        {
          prop: 'paidDate',
          label: '付款出账时间',
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          scopedSlots: { customRender: 'operation' },
        },
      ];

      return {
        applyDate: '',
        showDialog: false,
        currentRow: null,
        searchParams: {
          requisitionDate: {
            upper: '',
            lower: '',
          },
          principalCode: '',
          //付款方式：
          paymentMethod: '',
          //OA支付流程号：
          oaPaymentRequisitionId: '',
          //付款状态
          paymentStatus: '',
          //请款币种
          requisitionCurrency: '',
          //记账状态
          recordVoucherStatus: '',
          //供应商名称
          supplierName: '',
        },
        list: [],

        paymentMethod: [],
        paymentStatus: [],
        recordVoucher: [],
        principalCode: [],
        requestCurrency: [],
        tallyStatus: [],

        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
        columns,
        total: {
          amountTotal: '',
        },
      };
    },
    created() {
      this.defaultDate();
      const { oaPaymentRequisitionId = '' } = this.$route.query;
      if (oaPaymentRequisitionId) {
        this.searchParams.oaPaymentRequisitionId = oaPaymentRequisitionId;
        this.applyDate = null;
      }
      getNpPaymentRequisitionDic({ businessLine: 'aud-non-po' }).then(res => {
        if (res) {
          this.paymentMethod = res.paymentMethod;
          this.paymentStatus = res.paymentStatus;
          this.recordVoucher = res.recordVoucherStatus;
          this.principalCode = res.principalCode;
          this.requestCurrency = res.currency;
        }
      });
      this.getList(true);
    },
    methods: {
      viewDetails(row) {
        this.$router.push({
          path: '/accountManagement/settle/nonPoBill',
          query: {
            oaPaymentRequisitionId: row.oaPaymentRequisitionId,
          },
        });
      },
      onExport() {
        const params = this.getParams();
        this.$confirm('确定将该数据导出?', '导出', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          newExportExcel(
            params,
            '/api/finance-receivable/chargeItem/exportList',
            'post',
          ).then(res => {
            downloadFile(res.data, '计费单据列表');
          });
        });
      },
      exportResult() {
        const params = this.getParams();
        newExportExcel(
          params,
          '/api/finance-bill/np/payment-requisition/exportExcel',
          'post',
        ).then(res => {
          downloadFile(res.data, 'NON-PO请款单单查询结果');
        });
      },
      getParams() {
        const { applyDate } = this;

        const params = {
          ...this.searchParams,
          requisitionDate: applyDate
            ? {
                lower: parseTime(applyDate[0], '{y}-{m}-{d} 00:00:00'),
                upper: parseTime(applyDate[1], '{y}-{m}-{d} 23:59:59'),
              }
            : null,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await getNpPaymentRequisitionPage(params);
        this.options.loading = false;
        if (res) {
          this.list = res ? res.list : [];
          this.pagination.total = res ? res.total : 0;
        }
        const amountRe = await getRequisitionAmountTotal(params);
        this.total.amountTotal = amountRe ? amountRe.amountTotal : 0;
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.defaultDate();
      },
      defaultDate() {
        const end = new Date();
        const start = new Date();
        end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 31);
        this.applyDate = [
          parseTime(start, '{y}-{m}-{d} 00:00:00'),
          parseTime(end, '{y}-{m}-{d} 23:59:59'),
        ];
      },
    },
  };
</script>
<style lang="scss"></style>
