<template>
  <div>
    <el-form inline>
      <el-form-item label="付款主体:">
        <el-select v-model="form.payer" filterable clearable>
          <el-option
            v-for="item in PAY_MAIN"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态:">
        <el-select v-model="form.status" clearable>
          <el-option
            v-for="item in FUND_OUT_STATUS"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="日期范围:">
        <el-date-picker
          v-model="daterange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          clearable
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="批次号:">
        <el-input v-model="form.batchOrderId" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onGetList(true)">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table border :data="tableData">
      <el-table-column prop="id" label="出款批次号" min-width="160" />
      <el-table-column prop="payer" label="付款主体" width="120" />
      <el-table-column prop="payerCardNo" label="出款账户" width="120" />
      <el-table-column prop="companyOrPerson" label="转账类型" width="120" />
      <el-table-column prop="totalNum" label="总笔数" width="120" />
      <el-table-column prop="totalAmount" label="总金额（元）" width="120" />
      <el-table-column prop="remark" label="批次备注" width="160" />
      <el-table-column prop="createTime" label="提交时间" width="160" />
      <el-table-column prop="fundOutType" label="出款方式">
        <template slot-scope="scope">
          {{ formatFundOutType(scope.row.fundOutType) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          {{ formatFundOutStatus(scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="onDetail(scope.row)">
            查看明细
          </el-button>
          <ac-permission-button
            btn-text="导出"
            permission-key="settleBatch-export"
            type="text"
            @click="onExport(scope.row)"
          ></ac-permission-button>
          <ac-permission-button
            v-if="scope.row.status === 'INIT'"
            btn-text="撤销"
            permission-key="settleBatch-revoke"
            type="text"
            @click="onCancel(scope.row)"
          ></ac-permission-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="form.current"
      :page-sizes="[10, 20, 30, 50]"
      :page-size="form.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>
<script>
  import { getSettleBatchList, cancelSettleBatch } from '@/api/settle';
  import { getSettleDict } from '@/api/common';
  import { FUND_OUT_STATUS, FUND_OUT_TYPE } from '@/consts/liquidation';
  import { exportSettleBatch } from '@/api/blob';
  import { downloadFile } from '@/utils';
  export default {
    data() {
      return {
        PAY_MAIN: [],
        FUND_OUT_STATUS,
        form: {
          payer: '',
          status: '',
          batchOrderId: '',
          startDate: '',
          endDate: '',
          current: 1,
          size: 10,
        },
        daterange: [],
        tableData: [],
        total: 0,
      };
    },
    created() {
      this.onGetList(true);
      getSettleDict().then(res => {
        this.PAY_MAIN = res.payerDictList;
      });
    },
    methods: {
      onDetail(obj) {
        this.$router.push(`settleAccept?batchId=${obj.id}`);
      },
      onCancel(obj) {
        this.$confirm('确定要撤销?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          cancelSettleBatch(obj.id).then(() => {
            this.$message.success('撤销成功');
            this.onGetList(true);
          });
        });
      },
      onGetList(boolean = false) {
        if (boolean) {
          this.form.current = 1;
        }
        this.form.startDate = (this.daterange && this.daterange[0]) || '';
        this.form.endDate = (this.daterange && this.daterange[1]) || '';
        getSettleBatchList(this.form).then(res => {
          this.total = res.total;
          this.tableData = res.records || [];
        });
      },
      handleCurrentChange(e) {
        this.form.current = e;
        this.onGetList();
      },
      handleSizeChange(e) {
        this.form.size = e;
        this.onGetList();
      },
      onExport(obj) {
        exportSettleBatch(obj.id).then(res => {
          downloadFile(res.data, obj.id, 'zip');
          this.onGetList();
        });
      },
      formatFundOutType(type) {
        const obj = FUND_OUT_TYPE.filter(i => i.value === type)[0];
        return obj ? obj.label : '- -';
      },
      formatFundOutStatus(status) {
        const obj = FUND_OUT_STATUS.filter(i => i.value === status)[0];
        return obj ? obj.label : '- -';
      },
    },
  };
</script>
