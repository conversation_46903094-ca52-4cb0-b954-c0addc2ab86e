<template>
  <div>
    <el-form inline>
      <el-form-item label="付款主体:">
        <el-select v-model="form.payer" filterable clearable>
          <el-option
            v-for="item in payerList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="结算事项:">
        <el-select v-model="form.secondMatterId" clearable>
          <el-option
            v-for="item in allSecondMatterList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="结算事项:">
        <el-cascader
          v-model="secondMatterId"
          :props="matterProps"
          clearable
          :options="matterList"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="状态:">
        <el-select v-model="form.status" clearable>
          <el-option
            v-for="item in statusList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="收款客户名:">
        <el-input v-model="form.payee" clearable />
      </el-form-item>
      <el-form-item label="创建日期:">
        <el-date-picker
          v-model="daterange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          clearable
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="批次号:">
        <el-input v-model="form.batchId" clearable />
      </el-form-item>
      <el-form-item label="审批业务单号:">
        <el-input v-model="form.applyNo" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onGetList(true)">查询</el-button>
        <ac-permission-button
          type="success"
          btn-text="生成出款批次"
          permission-key="settleAccept-generate"
          @click="onShowDialog(null, 4)"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <el-table border :data="tableData">
      <el-table-column prop="settleOrderNo" label="结算单号" min-width="180" />
      <el-table-column prop="createTime" label="创建时间" min-width="160" />
      <el-table-column prop="planTime" label="预计付款时间" min-width="160" />
      <el-table-column prop="payer" label="付款主体" width="120" />
      <el-table-column prop="firstMatter" label="一级结算事项" width="120" />
      <el-table-column prop="secondMatter" label="二级结算事项" width="120" />
      <el-table-column prop="batchId" label="出款批次号" width="120" />
      <el-table-column prop="applyNo" label="审批单号" width="120" />
      <!-- <el-table-column prop="planTime" label="预计付款时间" width="160" /> -->
      <el-table-column prop="payerCardNo" label="出款账户" width="160" />
      <el-table-column prop="payee" label="收款客户名称" width="120" />
      <el-table-column prop="payeeBankName" label="收款银行" />
      <el-table-column prop="payeeCardNo" label="收款账号" width="160" />
      <el-table-column prop="currency" label="货币" width="100" />
      <el-table-column prop="originalAmount" label="原币种金额" width="100" />
      <el-table-column prop="amount" label="人民币金额" width="100" />
      <el-table-column prop="statusDesc" label="状态" />
      <el-table-column label="操作" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="onShowDialog(scope.row, 1)">
            详情
          </el-button>
          <ac-permission-button
            type="text"
            btn-text="修改"
            permission-key="settleAccept-edit"
            @click="onShowDialog(scope.row, 2)"
          ></ac-permission-button>
          <ac-permission-button
            type="text"
            btn-text="复制新增"
            permission-key="settleAccept-copy-add"
            @click="onShowDialog(scope.row, 3)"
          ></ac-permission-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="form.current"
      :page-sizes="[10, 20, 30, 50]"
      :page-size="form.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="600px"
      :before-close="
        () => {
          dialogType = 0;
        }
      "
    >
      <Detail v-if="dialogType === 1" ref="detail" :param="dialogObj" />
      <Edit
        v-if="dialogType === 2"
        ref="edit"
        :info="dialogObj"
        :status-list="statusList"
        :matter-list="matterList"
      />
      <Copy
        v-if="dialogType === 3"
        ref="copy"
        :param="dialogObj"
        :status-list="statusList"
        :matter-list="matterList"
      />
      <LoanBatch
        v-if="dialogType === 4"
        ref="generator"
        :matter-list="matterList"
        :payer-list="payerList"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogType = 0">取 消</el-button>
        <el-button type="primary" @click="onConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
  import Detail from './components/Detail';
  import Edit from './components/Edit';
  import Copy from './components/Copy';
  import LoanBatch from './components/LoanBatch';
  import {
    getSettleAcceptList,
    editSettleAcceptInfo,
    addSettleAcceptInfo,
    generateSettleAcceptInfo,
    getSecondMatterList,
  } from '@/api/settle';
  import { getSettleDict } from '@/api/common';
  import { objValueHaveFalse } from '@/utils';
  export default {
    components: { Detail, Edit, Copy, LoanBatch },
    data() {
      return {
        PAY_MAIN: [],
        matterList: [], // 事项列表
        payerList: [], // 付款主体列表
        statusList: [], // 状态列表
        allSecondMatterList: [],
        secondMatterId: '',
        form: {
          applyNo: '', // 业务审批单号
          batchId: '', // 批次号
          payer: '', // 付款主体
          payee: '', // 收款客户名
          secondMatterId: '', // 结算事项ID
          createStartTime: '', // 付款起始时间
          createEndTime: '',
          status: '',
          current: 1,
          size: 10,
        },
        daterange: [],
        tableData: [],
        total: 0,
        dialogType: 0,
        dialogObj: {},
        matterProps: {
          lazy: true,
          label: 'dictDesc',
          value: 'dictValue',
          lazyLoad(node, resolve) {
            if (node.level === 0) return;
            getSecondMatterList(node.value).then(res => {
              const arr = res.map(i => {
                return {
                  leaf: true,
                  ...i,
                };
              });
              resolve(arr);
            });
          },
        },
      };
    },
    computed: {
      dialogVisible() {
        return !!this.dialogType;
      },
    },
    watch: {
      secondMatterId(cur) {
        if (cur && cur.length > 0) {
          this.form.secondMatterId = cur[cur.length - 1];
        } else {
          this.form.secondMatterId = '';
        }
      },
    },
    created() {
      // 结算批次跳入会带一个batchId
      this.form.batchId = this.$route.query.batchId || '';
      // 默认其实日期为当月第一天
      if (!this.form.batchId) {
        const now = new Date();
        const month =
          now.getMonth() < 9 ? '0' + (now.getMonth() + 1) : now.getMonth() + 1;
        const thisMonthFirstDay = `${now.getFullYear()}-${month}-01`;
        const nowDate = `${now.getFullYear()}-${month}-${
          now.getDate() < 10 ? '0' + now.getDate() : now.getDate()
        }`;
        this.daterange = [thisMonthFirstDay, nowDate];
      }
      this.onGetList(true);
      getSettleDict().then(res => {
        this.matterList = res.firstMatterDictList;
        this.payerList = res.payerDictList;
        this.statusList = res.settleOrderStatusDictList;
        this.allSecondMatterList = res.secondMatterDictList;
      });
    },
    methods: {
      onGetList(boolean = false) {
        if (boolean) {
          this.form.current = 1;
        }
        this.form.createStartTime = (this.daterange && this.daterange[0]) || '';
        this.form.createEndTime = (this.daterange && this.daterange[1]) || '';
        getSettleAcceptList(this.form).then(res => {
          this.total = res.total;
          this.tableData = res.list;
        });
      },
      handleCurrentChange(e) {
        this.form.current = e;
        this.onGetList();
      },
      handleSizeChange(e) {
        this.form.size = e;
        this.onGetList();
      },
      /** 1-详情 2-修改 3-复制新增 4-生成出款批次 */
      onShowDialog(obj, type) {
        this.dialogType = type;
        this.dialogObj = obj;
      },
      onConfirm() {
        // this.dialogType = 0
        if (this.dialogType === 2) {
          // 编辑
          const info = this.$refs.edit.info;
          editSettleAcceptInfo({
            planTime: info.planTime,
            secondMatterId: info.secondMatterId,
            status: info.status,
            settleOrderNo: info.settleOrderNo,
          }).then(() => {
            this.$message.success('修改成功');
            this.dialogType = 0;
            this.onGetList();
          });
        } else if (this.dialogType === 3) {
          // 复制新增
          const info = this.$refs.copy.info;
          const obj = {
            amount: info.amount,
            originalAmount: info.originalAmount,
            currency: info.currency,
            planTime: info.planTime,
            secondMatterId: info.secondMatterId,
            payee: info.payee, // 收款客户名称
            payeeBankName: info.payeeBankName,
            payeeBankCode: info.payeeBankCode, // 收款银行编码
            payeeCardNo: info.payeeCardNo, // 收款账号
            payer: info.payer, // 付款主体
            payerCardNo: info.payerCardNo, // 付款账户
            applyNo: info.applyNo,
            empNo: info.empNo, // 员工号
          };
          if (objValueHaveFalse(obj)) {
            this.$message.error('请输入正确且完整的参数');
            return;
          }
          addSettleAcceptInfo(obj).then(() => {
            this.$message.success('复制新增成功');
            this.dialogType = 0;
            this.onGetList();
          });
        } else if (this.dialogType === 4) {
          const info = this.$refs.generator.info;
          if (objValueHaveFalse(info)) {
            this.$message.error('请输入正确且完整的参数');
            return;
          }
          generateSettleAcceptInfo(info).then(res => {
            this.$message.success('生成成功');
            this.dialogType = 0;
            this.onGetList();
          });
        }
      },
    },
  };
</script>
