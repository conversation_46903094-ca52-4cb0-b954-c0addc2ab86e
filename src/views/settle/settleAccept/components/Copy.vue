<template>
  <div class="settle-accept-copy">
    <el-form label-width="120px">
      <el-form-item label="一级结算事项:">
        <el-select
          v-model="info.firstMatterId"
          clearable
          @change="onFirstMatterChange"
        >
          <el-option
            v-for="item in matterList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="二级结算事项:">
        <el-select v-model="info.secondMatterId" clearable>
          <el-option
            v-for="item in secondMatterList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="预计付款时间:">
        <el-date-picker
          v-model="info.planTime"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetime"
          placeholder="选择预计付款时间"
        />
      </el-form-item>
      <el-form-item label="出款账户:">
        <!-- <el-input v-model="info.payerCardNo" /> -->
        <el-select v-model="info.payerCardNo">
          <el-option
            v-for="item in bankList"
            :key="item.dictValue"
            :value="item.dictValue"
            :label="item.dictDesc"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="收款客户名称:">
        <el-input v-model="info.payee" />
      </el-form-item>
      <el-form-item label="收款银行:">
        <el-input v-model="info.payeeBankName" />
        <!-- <el-input v-model="info.payeeBankCode" /> -->
      </el-form-item>
      <el-form-item label="收款账户:">
        <el-input v-model="info.payeeCardNo" />
      </el-form-item>
      <el-form-item label="货币:">
        <el-select v-model="info.currency">
          <el-option
            v-for="item in currencyList"
            :key="item.id"
            :value="item.currencyCode"
            :label="item.currencyChineseName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="人民币金额:">
        <el-input v-model="info.amount" />
      </el-form-item>
      <el-form-item label="原币种金额:">
        <el-input v-model="info.originalAmount" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import {
    getSecondMatterList,
    getSettleAcceptCopyInfo,
    getPayerAccountList,
  } from '@/api/settle';
  import { getCurrencyList } from '@/api/exchange';
  export default {
    props: {
      param: {
        type: Object,
        default: () => {},
      },
      matterList: {
        type: Array,
        default: () => [],
      },
      statusList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        secondMatterList: [],
        currencyList: [],
        bankList: [],
        info: {},
      };
    },
    mounted() {
      this.onGetSecondMatterList(this.param.firstMatterId);
      getSettleAcceptCopyInfo(this.param.settleOrderNo).then(res => {
        this.info = res;
        getPayerAccountList(this.info.payer).then(res => {
          this.bankList = res;
        });
      });
      getCurrencyList().then(res => {
        this.currencyList = res;
      });
    },
    methods: {
      onFirstMatterChange(e) {
        this.info.secondMatterId = '';
        if (!e) return;
        this.onGetSecondMatterList(e);
      },
      onGetSecondMatterList(e) {
        getSecondMatterList(e).then(res => {
          this.secondMatterList = res;
        });
      },
    },
  };
</script>
<style lang="scss">
  .settle-accept-copy {
    .el-input {
      width: 215px !important;
    }
  }
</style>
