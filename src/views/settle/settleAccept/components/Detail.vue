<template>
  <div class="settle-accept-detail">
    <table cellpadding="10" cellspacing="0" border="1">
      <tr>
        <td>结算单号</td>
        <td>{{ info.settleOrderNo }}</td>
      </tr>
      <tr>
        <td>二级结算事项</td>
        <td>{{ info.secondMatter }}</td>
      </tr>
      <tr>
        <td>受理单号</td>
        <td>{{ info.applyNo }}</td>
      </tr>
      <tr>
        <td>出款批次号</td>
        <td>{{ info.batchId }}</td>
      </tr>
      <tr>
        <td>受理时间</td>
        <td>{{ info.createTime }}</td>
      </tr>
      <tr>
        <td>预计付款时间</td>
        <td>{{ info.planTime }}</td>
      </tr>
      <tr>
        <td>实际出款时间</td>
        <td>{{ info.realTime }}</td>
      </tr>
      <tr>
        <td>勾兑时间</td>
        <td>{{ info.finishTime }}</td>
      </tr>
      <tr>
        <td>出款账户</td>
        <td>{{ info.payerCardNo }}</td>
      </tr>
      <tr>
        <td>收款客户名称</td>
        <td>{{ info.payee }}</td>
      </tr>
      <tr>
        <td>收款银行</td>
        <td>{{ info.payeeBankName }}</td>
      </tr>
      <tr>
        <td>收款账号</td>
        <td>{{ info.payeeCardNo }}</td>
      </tr>
      <tr>
        <td>币种</td>
        <td>{{ info.currency }}</td>
      </tr>
      <tr>
        <td>原币种金额</td>
        <td>{{ info.originalAmount }}</td>
      </tr>
      <tr>
        <td>人民币金额</td>
        <td>{{ info.amount }}</td>
      </tr>
      <tr>
        <td>状态</td>
        <td>{{ info.statusDesc }}</td>
      </tr>
      <tr>
        <td>摘要</td>
        <td>{{ info.remark }}</td>
      </tr>
      <tr>
        <td>返回结果</td>
        <td>{{ info.returnMsg }}</td>
      </tr>
    </table>
  </div>
</template>
<script>
  import { getSettleAcceptDetail } from '@/api/settle';
  export default {
    props: {
      param: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        info: {},
      };
    },

    mounted() {
      this.info = this.param;

      getSettleAcceptDetail(this.param.settleOrderNo).then(res => {
        this.info = res;
      });
    },
  };
</script>
<style scoped lang="scss">
  .settle-accept-detail {
    table {
      width: 100%;
      border-color: #ebebeb;
      border-collapse: collapse;
    }
    table tr td {
      border: 1px solid #ebebeb;
      min-width: 120px;
    }
  }
</style>
