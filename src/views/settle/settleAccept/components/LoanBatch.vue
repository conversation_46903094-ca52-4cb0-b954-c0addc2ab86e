<template>
  <div class="settle-loan-batch">
    <el-form label-width="150px">
      <el-form-item label="付款主体:" required>
        <el-select
          v-model="info.payer"
          filterable
          clearable
          @change="onPayerChange"
        >
          <el-option
            v-for="item in payerList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="付款账号:" required>
        <el-select
          v-model="info.payerCardNo"
          clearable
          placeholder="请先选择付款主体"
        >
          <el-option
            v-for="item in payerAccountList"
            :key="item.dictValue"
            :label="`${item.dictDesc}(${item.dictSubDesc})`"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="转账类型:" required>
        <el-select
          v-model="info.companyOrPerson"
          clearable
          placeholder="转账类型"
        >
          <el-option
            v-for="item in payerTyleList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="批次备注:" required>
        <el-input v-model="info.remark" maxlength="64" />
      </el-form-item>
      <el-form-item label="一级结算事项:" required>
        <el-select
          v-model="info.firstMatterId"
          clearable
          @change="onFirstMatterChange"
        >
          <el-option
            v-for="item in matterList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="二级结算事项:" required>
        <el-select
          v-model="info.matterIdList"
          clearable
          multiple
          placeholder="请先选择一级结算事项"
        >
          <el-option
            v-for="item in secondMatterList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import { getSecondMatterList, getPayerAccountList } from '@/api/settle';
  export default {
    props: {
      matterList: {
        default: () => [],
        type: Array,
      },
      payerList: {
        default: () => [],
        type: Array,
      },
    },
    data() {
      return {
        info: {
          matterIdList: [],
          payerCardNo: '',
          payer: '',
          firstMatterId: '',
          remark: '',
        },
        secondMatterList: [],
        payerAccountList: [], // 付款账户列表
        payerTyleList: [
          {
            dictDesc: '对公转账',
            dictValue: 'B',
          },
          {
            dictDesc: '对私转账',
            dictValue: 'C',
          },
        ],
      };
    },
    methods: {
      onFirstMatterChange(e) {
        this.info.matterIdList = [];
        if (!e) return;
        getSecondMatterList(e).then(res => {
          this.secondMatterList = res;
        });
      },
      onPayerChange(e) {
        this.info.payerCardNo = '';
        if (!e) return;
        // 查询付款主题下的付款账号列表
        getPayerAccountList(e).then(res => {
          this.payerAccountList = res;
        });
      },
    },
  };
</script>
<style lang="scss">
  .settle-loan-batch {
    .el-input {
      width: 300px !important;
    }
  }
</style>
