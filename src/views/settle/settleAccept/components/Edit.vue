<!--
 * @Author: dddd
 * @Date: 2022-10-17 16:14:04
 * @LastEditors: dddd
 * @LastEditTime: 2022-11-18 16:29:53
 * @FilePath: /fmis/src/views/settle/settleAccept/components/Edit.vue
 * @Description: 
-->
<template>
  <div class="settle-accept-edit">
    <el-form label-width="120px">
      <el-form-item label="结算单号:">{{ info.settleOrderNo }}</el-form-item>
      <el-form-item label="一级结算事项:">
        <el-select
          v-model="info.firstMatterId"
          clearable
          @change="onFirstMatterChange"
        >
          <el-option
            v-for="item in matterList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="二级结算事项:">
        <el-select v-model="info.secondMatterId" clearable>
          <el-option
            v-for="item in secondMatterList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="预计付款时间:">
        <el-date-picker
          v-model="info.planTime"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetime"
          placeholder="选择预计付款时间"
        />
      </el-form-item>
      <el-form-item label="状态:">
        <el-select v-model="info.status" clearable>
          <el-option
            v-for="item in statusList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import { getSecondMatterList } from '@/api/settle';
  export default {
    props: {
      info: {
        type: Object,
        default: () => {},
      },
      matterList: {
        type: Array,
        default: () => [],
      },
      statusList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        secondMatterList: [],
      };
    },
    mounted() {
      this.onGetSecondMatterList(this.info.firstMatterId);
    },
    methods: {
      onFirstMatterChange(e) {
        this.info.secondMatterId = '';
        this.onGetSecondMatterList(e);
      },
      onGetSecondMatterList(e) {
        // 去获取二级事项列表
        getSecondMatterList(e).then(res => {
          this.secondMatterList = res;
        });
      },
    },
  };
</script>
