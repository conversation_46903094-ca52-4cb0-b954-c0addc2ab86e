<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-11 14:37:29
 * @LastEditTime: 2022-11-16 11:20:49
 * @LastEditors: Please set LastEditors
 * @Reference: 
-->

<template>
  <div>
    <el-form inline>
      <el-form-item label="结算单时间:">
        <el-date-picker
          v-model="searchDate"
          type="daterange"
          :clearable="true"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="到期日:">
        <el-date-picker
          v-model="deadline"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="采购主体:">
        <el-select
          v-model="searchParams.principalCode"
          multiple="true"
          clearable
          filterable
          placeholder="请选择采购主体"
        >
          <el-option
            v-for="item in principalCode"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="供应商名称:">
        <el-input
          v-model="searchParams.supplierName"
          clearable
          placeholder="请输入供应商名称"
        />
      </el-form-item>
      <el-form-item label="付款状态:">
        <el-select
          v-model="searchParams.paymentStatus"
          multiple="true"
          clearable
          placeholder="请选择付款状态"
        >
          <el-option
            v-for="item in paymentStatus"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="币种:">
        <el-select
          v-model="searchParams.currency"
          clearable
          placeholder="请选择币种"
        >
          <el-option
            v-for="item in currency"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="结算单号:">
        <el-input
          v-model="searchParams.settlementNo"
          clearable
          placeholder="请输入结算单号"
        />
      </el-form-item>
      <el-form-item label="付款方式:">
        <el-select
          v-model="searchParams.paymentMethod"
          clearable
          placeholder="请选择付款方式"
        >
          <el-option
            v-for="item in paymentMethod"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发票号:">
        <el-input
          v-model="searchParams.invoiceCode"
          clearable
          placeholder="请输入发票号"
        />
      </el-form-item>
      <el-form-item label="NON-PO申请流程号:">
        <el-input
          v-model="searchParams.oaApplicationId"
          clearable
          placeholder="NON-PO申请流程号"
        />
      </el-form-item>
      <el-form-item label="OA支付流程号:">
        <el-input
          v-model="searchParams.oaPaymentRequisitionId"
          clearable
          placeholder="请输入OA支付流程号"
        />
      </el-form-item>
      <el-form-item label="主体标签:">
        <el-select
          v-model="searchParams.tenantTag"
          clearable
          filterable
          placeholder="请选择主体标签"
        >
          <el-option
            v-for="item in tenantTagList"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button type="primary" @click="exportResult">导出</el-button>
        <!-- <el-button type="primary" @click="onExport">导出</el-button> -->
        <el-button type="primary" @click="payApplication">支付申请</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <div style="display: inline-block">
        <span v-text="`结算单金额汇总：${total.amountTotal}`"></span>
      </div>
      <div style="display: inline-block; margin-left: 30px">
        <span v-text="`实付金额汇总：${total.practicalTotal}`"></span>
      </div>
    </el-row>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
      :check-selectable="handleCheckSelectable"
      @selection-change="handleSelectionChange"
    >
      <template slot="operation" slot-scope="scope">
        <el-button
          slot="reference"
          type="text"
          size="small"
          @click="viewInvoice(scope.row)"
        >
          下载发票
        </el-button>
      </template>
      <template slot="operationDeduction" slot-scope="scope">
        <el-button
          slot="reference"
          type="text"
          size="small"
          @click="deduction(scope.row)"
        >
          查看抵扣信息
        </el-button>
      </template>
      <template slot="requestion" slot-scope="scope">
        <el-button
          slot="reference"
          type="text"
          size="small"
          @click="request(scope.row)"
        >
          查看请款信息
        </el-button>
      </template>
    </dynamictable>
    <el-dialog
      title="提示"
      width="400px"
      :visible.sync="dialogVisible"
      @closed="onClose"
    >
      <div style="margin-bottom: 20px">
        <i
          class="el-icon-warning"
          style="color: #e6a23c; font-size: 24px; margin-right: 10px"
        ></i>
        是否确认发起支付申请?
      </div>
      <el-form ref="formData" :model="saveParams" label-width="120px">
        <el-form-item
          prop="scheduledPaymentDate"
          :rules="[
            {
              required: true,
              message: '请选择支付申请时间',
              trigger: 'change',
            },
          ]"
          label="支付申请时间: "
        >
          <el-date-picker
            v-model="saveParams.scheduledPaymentDate"
            type="datetime"
            placeholder="选择日期时间"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onOK">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="抵扣信息"
      :visible.sync="centerDialogVisible"
      width="30%"
      center
    >
      <dynamictable
        :data-source="deductions"
        :columns="getDeductionColumns()"
        :options="options"
      ></dynamictable>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="centerDialogVisible = false">
          取消
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import { newExportExcel } from '@/api/blob';
  import {
    getAmountTotal,
    getDeduction,
    getNpBillPage,
    getNpPaymentRequisitionDic,
    npPaymentRequisition,
  } from '@/api/settle';

  import { downloadFile, parseTime } from '@/utils';
  import { mapGetters } from 'vuex';

  export default {
    name: 'NonPoBill',
    components: {
      dynamictable,
    },

    data() {
      return {
        dialogVisible: false,
        searchDate: '',
        deadline: '',
        searchParams: {
          principalCode: [],
          supplierName: '',
          paymentStatus: [],
          currency: '',
          settlementNo: '',
          paymentMethod: '',
          invoiceCode: '',
          oaApplicationId: '',
          oaPaymentRequisitionId: '',
        },
        list: [],
        saveParams: {
          scheduledPaymentDate: '', // 支付申请时间
        },

        selectArr: [],
        paymentMethod: [],
        paymentStatus: [],
        principalCode: [],
        currency: [],

        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          mutiSelect: true,
        },
        deductions: [],
        centerDialogVisible: false,
        paramCode: '',
        total: {
          amountTotal: '',
          practicalTotal: '',
        },
        tenantTagList: [
          { key: 'VTN', value: 'VTN' },
          { key: 'WLZ', value: 'WLZ' },
        ],
      };
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        userId: 'user/userId',
      }),
    },
    created() {
      this.defaultDate();
      const { oaPaymentRequisitionId = '' } = this.$route.query;
      if (oaPaymentRequisitionId) {
        this.searchParams.oaPaymentRequisitionId = oaPaymentRequisitionId;
        this.searchDate = null;
      }

      getNpPaymentRequisitionDic({ businessLine: 'aud-non-po' }).then(res => {
        if (res) {
          this.paymentMethod = res.paymentMethod;
          this.paymentStatus = res.billStatus;
          this.principalCode = res.principalCode;
          this.currency = res.currency;
        }
      });
      this.getList(true);
    },

    methods: {
      handleCheckSelectable(row) {
        return row.billStatus == 1;
      },
      onClose() {
        Object.assign(this.$data.saveParams, this.$options.data().saveParams);
        this.$nextTick(function () {
          this.$refs.formData.clearValidate();
        });
      },
      async viewInvoice(val) {
        if (val && val.invoiceUrl) {
          const url = val.invoiceUrl.replace(
            /^http[s]?:\/\//,
            window.location.protocol + '//',
          );
          window.open(url, '_blank');
        }
      },
      async deduction(val) {
        try {
          const paramCode = {
            billCode: val.billNo,
          };
          const res = await getDeduction(paramCode);
          this.deductions = res ? res : [];
        } catch (error) {
          console.log(error);
        }
        this.centerDialogVisible = true;
      },
      request(row) {
        this.$router.push({
          path: '/accountManagement/settle/nonPoInvoice',
          query: {
            oaPaymentRequisitionId: row.oaPaymentRequisitionId,
          },
        });
      },

      // const url =
      //   'https://img.danchuangglobal.com/202206/53654da983cc4b2e9303cbc88c421090.png';
      // this.$alert(
      //   `<img src=${val.invoiceUrl} style="width: 200px; height: 200px;margin-left: 25%"  />`,
      //   '发票',
      //   {
      //     dangerouslyUseHTMLString: true,
      //   },
      // );

      getParams() {
        const { searchDate, deadline } = this;
        const params = {
          ...this.searchParams,
          settleDate: searchDate
            ? {
                lower: searchDate
                  ? parseTime(this.searchDate[0], '{y}-{m}-{d} 00:00:00')
                  : '',
                upper: searchDate
                  ? parseTime(this.searchDate[1], '{y}-{m}-{d} 23:59:59')
                  : '',
              }
            : null,
          dueDate: deadline
            ? {
                lower: deadline
                  ? parseTime(deadline[0], '{y}-{m}-{d} 00:00:00')
                  : '',
                upper: deadline
                  ? parseTime(deadline[1], '{y}-{m}-{d} 23:59:59')
                  : '',
              }
            : null,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        try {
          const res = await getNpBillPage(params);
          this.options.loading = false;
          this.list = res ? res.list : [];
          this.pagination.total = res ? res.total : 0;
          const totalRes = await getAmountTotal(params);
          this.total = totalRes;
        } catch (error) {
          this.options.loading = false;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.defaultDate();
      },
      handleSelectionChange(val = []) {
        this.selectArr = val;
      },

      async onOK() {
        this.$refs.formData.validate(async valid => {
          if (!valid) return;

          // const checkInvoiceType = this.checkInvoiceType(this.selectArr);
          // if (checkInvoiceType) return;
          const body = {
            billIds: this.selectArr.map(item => item.id),
            scheduledPaymentDate: parseTime(
              this.saveParams.scheduledPaymentDate,
            ),
          };
          const res = await npPaymentRequisition(body);
          this.$message({
            type: 'success',
            message: '申请成功!',
          });
          this.dialogVisible = false;
          this.getList();
        });
      },
      checkInvoiceType(arr) {
        let flag = false;

        arr.forEach(item => {
          if (item.invoiceType === 'Invoice' && item.settleAmount < 0) {
            flag = true;
            this.$message.error(
              `${item.oaPaymentRequisitionId}请款金额需要大于等于0`,
            );
          }
          if (item.invoiceType !== 'Invoice' && item.settleAmount >= 0) {
            flag = true;
            this.$message.error(
              `${item.oaPaymentRequisitionId}请款金额需要小于0`,
            );
          }
        });
        return flag;
      },

      payApplication() {
        if (this.selectArr.length === 0) {
          return this.$message.error('请选择结算单');
        }
        this.dialogVisible = true;
      },

      onExport() {
        const params = this.getParams();
        newExportExcel(
          params,
          '/api/pay-dashboard/order/refundOrder/export',
          'get',
        ).then(res => {
          downloadFile(res.data, 'NON-PO结算单列表');
        });
      },
      exportResult() {
        const params = this.getParams();
        newExportExcel(
          params,
          '/api/finance-bill/np/bill/exportExcel',
          'post',
        ).then(res => {
          downloadFile(res.data, 'NON-PO结算单查询结果');
        });
      },
      getColumns() {
        return [
          {
            prop: 'settleDate',
            label: '结算单时间',
          },
          {
            prop: 'billNo',
            label: '结算单号',
          },
          {
            prop: 'oaPayoutId',
            label: 'NON-PO申请流程号',
          },
          {
            prop: 'oaPaymentRequisitionId',
            label: 'OA支付流程号',
          },
          {
            prop: 'invoiceCode',
            label: '发票号',
          },
          {
            prop: 'dueDate',
            label: '到期日',
          },
          {
            prop: 'invoiceType',
            label: '发票类型',
          },
          {
            prop: 'category',
            label: '费用类型',
          },
          {
            prop: 'paymentMethodKey',
            label: '付款方式',
          },
          {
            prop: 'principalName',
            label: '付款主体',
          },
          {
            prop: 'supplierName',
            label: '供应商名称',
          },
          {
            prop: 'currency',
            label: '结算单币种',
          },
          {
            prop: 'settleAmount',
            label: '结算单金额',
          },
          {
            prop: 'operation',
            label: '抵扣信息',
            fixed: 'right',
            width: '100',
            scopedSlots: { customRender: 'operationDeduction' },
          },
          {
            prop: 'operation',
            label: '查看请款单',
            fixed: 'right',
            width: '100',
            scopedSlots: { customRender: 'requestion' },
          },
          {
            prop: 'paidCurrency',
            label: '实付币种',
          },
          {
            prop: 'paidAmount',
            label: '实付金额',
          },
          {
            prop: 'billStatusKey',
            label: '付款状态',
          },
          {
            prop: 'paidTime',
            label: '付款出账时间',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '200',
            scopedSlots: { customRender: 'operation' },
          },
        ];
      },
      getDeductionColumns() {
        return [
          {
            prop: 'creditNO',
            label: '抵扣CN号码',
          },
          {
            prop: 'creditAmount',
            label: '抵扣金额',
          },
        ];
      },
      defaultDate() {
        const end = new Date();
        const start = new Date();
        end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 31);
        this.searchDate = [
          parseTime(start, '{y}-{m}-{d} 00:00:00'),
          parseTime(end, '{y}-{m}-{d} 23:59:59'),
        ];
      },
    },
  };
</script>
<style lang="scss"></style>
