<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-13 14:02:52
 * @LastEditTime: 2022-05-13 14:12:22
 * @LastEditors: xuxiang
 * @Reference: 
-->
<template>
  <el-dialog width="1000px" :visible.sync="showDialog">
    <dynamictable
      :data-source="record"
      :columns="getColumns()"
      :options="options"
    ></dynamictable>
  </el-dialog>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  export default {
    components: {
      dynamictable,
    },
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      record: {
        // po明细
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {};
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {
        if (value) {
        }
      },
    },
    methods: {
      getColumns() {
        return [
          {
            prop: 'purchaseNo',
            label: '结算时间',
          },
          {
            prop: 'createTime',
            label: 'OA申请流程号',
          },
          {
            prop: 'goodsBarcode',
            label: '付款主体',
          },
          {
            prop: 'goodsTitle',
            label: '供应商名称',
          },
          {
            prop: 'purchaseQuantity',
            label: '结算币种',
          },
          {
            prop: 'purchasePriceTaxInclusive',
            label: '结算金额',
          },
          {
            prop: 'purchasePrice',
            label: '实付币种',
          },
          {
            prop: 'purchaseAmountTaxInclusive',
            label: '实付金额',
          },
          {
            prop: 'purchaseAmountTaxExclusive',
            label: '费用类型',
          },
          {
            prop: 'rate',
            label: '付款方式',
          },
          {
            prop: 'taxAmount',
            label: '发票类型',
          },
          {
            prop: 'remark',
            label: '发票号',
          },
          {
            prop: 'remark',
            label: '截至时间',
          },
          {
            prop: 'remark',
            label: '付款状态',
          },
          {
            prop: 'remark',
            label: '付款出账时间',
          },
        ];
      },
    },
  };
</script>
<style lang="scss"></style>
