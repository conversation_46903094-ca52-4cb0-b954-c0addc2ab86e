<template>
  <div>
    <el-dialog
        width="1000px"
        :title="dialogTitle"
        :visible.sync="showDialog"
        :close-on-click-modal="false"
    >
      <el-tabs v-if="!isDetails" v-model="tab" type="card">
        <el-tab-pane label="加成率" name="0"></el-tab-pane>
        <el-tab-pane label="操作日志" name="1"></el-tab-pane>
      </el-tabs>
      <div v-if="tab === '1'">
        <dynamictable
            :data-source="list"
            :columns="getColumns()"
            :options="options"
            :pagination="pagination"
            :fetch="loadLogs"
        ></dynamictable>
      </div>
      <el-form
          v-if="tab === '0'"
          ref="form"
          :model="saveForm"
          :rules="rulesForm"
          :inline="true"
          label-width="120px"
          :disabled="!isDetails"
      >
        <el-form-item label="采购方名称:" prop="firstPartyCode">
          <el-select
              v-model="saveForm.firstPartyCode"
              clearable
              filterable
              placeholder="请选择采购方名称"
              style="width: 220px"
              :disabled="!!currentRow"
              @change="loadBrandList"
          >
            <el-option
                v-for="(item, index) in firstPartyList"
                :key="index"
                :label="item.dictDesc"
                :value="item.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="供应商名称:" prop="secondPartyCode">
          <el-select
              v-model="saveForm.secondPartyCode"
              clearable
              filterable
              placeholder="请选择采购方名称"
              style="width: 220px"
              :disabled="!!currentRow"
              @change="loadBrandList"
          >
            <el-option
                v-for="(item, index) in secondPartyList"
                :key="index"
                :label="item.dictDesc"
                :value="item.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="生效日期:" prop="beginTime">
          <el-date-picker
              v-model="saveForm.beginTime"
              type="datetime"
              :picker-options="pickerOptions"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择日期"
              default-time="00:00:00"
              style="width: 220px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="失效日期:" prop="endTime">
          <el-date-picker
              v-model="saveForm.endTime"
              type="datetime"
              :picker-options="pickerOptions"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择日期"
              default-time="23:59:59"
              style="width: 220px"
          ></el-date-picker>
        </el-form-item>
        <el-alert type="info" :closable="false" effect="dark">
          品牌加成信息
        </el-alert>
        <uoloadFile
            style="margin-bottom: 10px; display: inline-block"
            accept=".xlsx, .xls, .xltx"
            :upload-url="`/api/contract-center/addition/${
            currentRow ? 'edit' : 'add'
          }/importExcel`"
            :upload-data="{
            additionNo: currentRow ? currentRow.additionNo : '',
            secondPartyCode: saveForm.secondPartyCode,
          }"
            @onSuccess="onSuccess"
            @beforeUpload="beforeUpload"
        ></uoloadFile>
        <!-- <el-button
          type="primary"
          class="mr-l-10"
          @click="handleDownloadTemplate"
        >
          下载模板
        </el-button> -->
        <el-button
            type="primary"
            class="mr-l-10"
            @click="downloadBrandAddition"
        >
          导出
        </el-button>
        <el-table
            v-loading="loading"
            :data="saveForm.brandList"
            border
            style="width: 100%"
            max-height="350"
        >
          <el-table-column prop="brandCode" label="品牌ID"></el-table-column>
          <el-table-column
              prop="brandName"
              label="品牌名称（中/英）"
          ></el-table-column>

          <el-table-column prop="proportion" label="加成比例">
            <template slot-scope="scope">
              <el-form-item
                  :prop="'brandList.' + scope.$index + '.proportion'"
                  :rules="rulesForm.proportion"
              >
                <el-input
                    v-model="scope.row.proportion"
                    class="line-input"
                    style="width: 150px"
                    placeholder="请输入内容"
                    @change="handleChangeProportion(scope.row)"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
        <!-- <el-alert type="info" :closable="false" effect="dark" class="mr-t-10">
          货品加成信息
        </el-alert>
        <el-table
          :data="saveForm.goodsList"
          border
          style="width: 100%"
          max-height="350"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60px"
          ></el-table-column>
          <el-table-column prop="goodsCode" label="货品编码"></el-table-column>
          <el-table-column prop="goodsName" label="货品名称"></el-table-column>
          <el-table-column prop="barCode" label="条形码"></el-table-column>
          <el-table-column prop="goodsUnit" label="规格"></el-table-column>
          <el-table-column prop="brandName" label="品牌"></el-table-column>
          <el-table-column
            prop="categoryTreeName	"
            label="类目"
          ></el-table-column>
          <el-table-column prop="proportion" label="加成比例" width="220">
            <template slot-scope="scope">
              <el-form-item :prop="'goodsList.' + scope.$index + '.proportion'">
                <el-input
                  v-model="scope.row.proportion"
                  class="line-input"
                  style="width: 150px"
                  placeholder="请输入内容"
                  :disabled="true"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>
        </el-table> -->
      </el-form>

      <div v-if="isDetails" slot="footer" class="dialog-footer">
        <el-button @click="showDialog = false">取 消</el-button>
        <el-button type="primary" :loading="btnLoading" @click="onOK">
          提 交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {debounce} from '@/utils';
import uoloadFile from '@/components/uoloadFile';
import dynamictable from '@/components/dynamic-table';
import {parseTime, downloadFile} from '@/utils';
import {exportExcel} from '@/api/blob';
import {replaceLocalDomain} from '@/utils/index.js';
import {
  queryBrandInfo,
  createBrandBonus,
  getBrandDetail,
  journalList,
  editBrandBonus,
  downloadTemplate,
} from '@/api/financeBrandBonus';

export default {
  components: {
    uoloadFile,
    dynamictable,
  },
  model: {
    prop: 'show',
    event: 'change',
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    currentRow: {
      type: Object,
      default: null,
    },
    firstPartyList: {
      type: Array,
      default: () => [],
    },
    secondPartyList: {
      type: Array,
      default: () => [],
    },
    isDetails: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    const validValue = (rule, value, callback) => {

      const reg = /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/;
      if (!reg.test(value) && value) {
        callback(new Error('请输入最多两位小数的数字'));
      } else {
        callback();
      }
    };
    const that = this;
    return {
      tab: '0',
      list: [],
      loading: false,
      btnLoading: false,
      dialogTitle: '新建品牌加成率',
      pickerOptions: {
        disabledDate(time) {
          return that.currentRow?.additionNo
              ? time.getTime() < Date.now() - 8.64e7
              : time.getTime() < Date.now() - 8.64e7;
        },
      },
      rulesForm: {
        firstPartyCode: [
          {
            required: true,
            message: '请选择采购方',
            trigger: 'change',
          },
        ],
        secondPartyCode: [
          {
            required: true,
            message: '请选择供应商',
            trigger: 'change',
          },
        ],
        proportion: [
          // {
          //   required: true,
          //   message: '请输入加成比例',
          //   trigger: 'change',
          // },
          {required: true, validator: validValue},
        ],
        beginTime: [
          {
            required: true,
            message: '请输入生效时间',
            trigger: 'change',
          },
        ],
        endTime: [
          {
            required: true,
            message: '请输入失效时间',
            trigger: 'change',
          },
        ],
      },
      saveForm: {
        firstPartyCode: '',
        secondPartyCode: '',
        beginTime: null,
        endTime: null,
        brandList: [],
        goodsList: [],
      },

      options: {
        loading: false,
        border: true,
      },
      pagination: {
        pageSize: 1,
        pageLimit: 10,
        total: null,
      },
    };
  },
  computed: {
    showDialog: {
      get() {
        return this.show;
      },
      set(value) {
        this.$emit('change', value);
      },
    },
  },
  watch: {
    show(val) {
      if (val) {
        if (this.currentRow?.additionNo?.length > 0) {
          this.dialogTitle = this.isDetails
              ? '编辑品牌加成率'
              : '查看品牌加成率';
          this.loadDetail(this.currentRow.additionNo);
          this.loadLogs(this.currentRow.additionNo);
        }
      } else {
        Object.assign(this.$data.saveForm, this.$options.data().saveForm);
        this.$nextTick(function () {
          this.$refs.form.clearValidate();
        });
        this.tab = '0';
        this.dialogTitle = '新建品牌加成率';
        this.saveForm = {
          firstPartyCode: '',
          secondPartyCode: '',
          beginTime: null,
          endTime: null,
          brandList: [],
          goodsList: [],
        };
      }
    },
  },
  created() {
  },
  methods: {
    downloadBrandAddition() {
      const {secondPartyCode, additionNo} = this.saveForm;
      if (!secondPartyCode) {
        this.$message.error('供应商必选');
        return false;
      }
      const api =
          this.currentRow?.additionNo?.length > 0
              ? '/addition/downloadBrandAdditionInEdit'
              : '/addition/downloadBrandAdditionInAdd';
      const body = {};
      if (this.currentRow?.additionNo?.length) {
        body.additionNo = additionNo;
      } else {
        body.supplierCode = secondPartyCode;
      }
      try {
        exportExcel({...body}, `/api/contract-center${api}`, 'get').then(
            res => {
              downloadFile(res.data, '品牌');
            },
        );
      } catch (err) {
        console.log(err, 'errerrerrerr');
      }
    },
    handleDownloadTemplate() {
      try {
        exportExcel(
            {},
            '/api/contract-center/addition/downloadImportTemplate',
            'get',
        ).then(res => {
          downloadFile(res.data, '品牌加成模版');
        });
      } catch (err) {
        console.log(err, 'errerrerrerr');
      }
    },
    handleChangeProportion(val) {
      this.saveForm.goodsList = this.saveForm.goodsList.map(item => {
        if (val.brandCode == item.brandCode) {
          return {
            ...item,
            proportion: val.proportion,
          };
        }
        return item;
      });
    },
    async loadDetail(id) {
      const {res, err} = await getBrandDetail({
        additionNo: id,
        flag: this.isDetails ? true : false,
      });
      if (!err) {
        this.saveForm = {
          ...res,
          beginTime: res.realBeginTime,
          endTime: res.realEndTime,
        };
      }
    },
    async loadLogs(id) {
      const body = {
        current: this.pagination.pageSize,
        size: this.pagination.pageLimit,
        additionNo: id,
      };
      const {res, err} = await journalList(body);
      if (res && !err) {
        this.list = res ? res.records : [];
        this.pagination.total = res ? res.total : 0;
      }
    },
    loadBrandList() {
      if (this.saveForm.secondPartyCode?.length > 0) {
        this.loadBrandGoodsList(this.saveForm.secondPartyCode);
      } else {
        return false;
      }
    },
    async loadBrandGoodsList(code) {
      this.loading = true;
      const {res, err} = await queryBrandInfo({secondPartyCode: code});
      if (res && !err) {
        // this.saveForm.goodsList = res.goodsList;
        this.saveForm.brandList = res;
      }
      this.loading = false;
    },
    beforeUpload() {
      const {secondPartyCode} = this.saveForm;
      if (!secondPartyCode) {
        this.$message.error('供应商必选');
        return false;
      }
      return true;
    },
    getGoodsBrandList(uploadList, list) {
      console.log(uploadList, list, 'kkkk');
      const arr = [...list];

      uploadList.forEach((item, index) => {
        if (item.proportion) {
          arr.splice(index, 1, item);
        }
      });

      return arr;
    },
    onSuccess(e) {
      if (!e.success) {
        this.$message.error(e.msg || '导入失败');

        return;
      }
      this.$message.success('导入成功');

      if (e.data) {
        const res = e.data;

        // this.saveForm.goodsList = this.getGoodsBrandList(
        //   res.goodsList,
        //   this.saveForm.goodsList,
        // );
        // this.saveForm.brandList = this.getGoodsBrandList(
        //   res.brandList,
        //   this.saveForm.brandList,
        // );
        this.saveForm.brandList = res.brandList;
      }
    },
    onOK: debounce(function (type) {
      this.$refs.form.validate(async valid => {
        if (valid) {
          console.log('this.no', this.saveForm.additionNo);
          /* this.saveForm.beginTime = parseTime(
             this.saveForm.beginTime,
             '{y}-{m}-{d} 00:00:00',
           );
           this.saveForm.endTime = parseTime(
             this.saveForm.endTime,
             '{y}-{m}-{d} 23:59:59',
           );*/
          const {goodsList, brandList, ...other} = this.saveForm;
          const brand = brandList.map(({brandLogo, ...others}) => ({
            ...others,
          }));
          const goods = goodsList.map(
              ({categoryTreeName, goodsUnit, brandName, ...oth}) => ({
                ...oth,
              }),
          );
          const body = {
            additionBase: {...other},
            goodsList: [...goods],
            brandList: [...brand],
          };

          try {
            this.btnLoading = true;
            const {res, err} = await (this.saveForm.additionNo?.length > 0
                ? editBrandBonus(body)
                : createBrandBonus(body));
            this.btnLoading = false;
            if (!err) {
              this.$message.success('操作成功');

              this.showDialog = false;
              this.$emit('submit');
            }
          } catch (e) {
            this.btnLoading = false;
          }
        }
      });
    }, 1000),
    getColumns() {
      return [
        {
          prop: 'createTime',
          label: '操作时间',
        },
        {
          prop: 'operateContent',
          label: '操作内容',
        },
        {
          prop: 'operator',
          label: '操作人',
        },
        {
          prop: 'remark',
          label: '备注',
        },
      ];
    },
  },
};
</script>

<style></style>
