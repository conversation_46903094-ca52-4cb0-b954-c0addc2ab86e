<template>
  <div>
    <el-form inline>
      <el-form-item label="加成编号:">
        <el-input
          v-model="searchParams.additionNo"
          clearable
          placeholder="请输入加成编号"
        />
      </el-form-item>
      <el-form-item label="采购方名称:">
        <el-select
          v-model="searchParams.firstPartyCode"
          clearable
          filterable
          placeholder="请选择采购方名称"
        >
          <el-option
            v-for="(item, index) in firstPartyList"
            :key="index"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="供应商名称:">
        <el-select
          v-model="searchParams.secondPartyCode"
          clearable
          filterable
          placeholder="请选择供应商名称"
        >
          <el-option
            v-for="(item, index) in secondPartyList"
            :key="index"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="加成状态:">
        <el-select
          v-model="searchParams.status"
          clearable
          placeholder="请选择加成状态"
        >
          <el-option
            v-for="(item, index) in statusList"
            :key="index"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建人:">
        <el-input
          v-model="searchParams.creatorName"
          clearable
          placeholder="请输入创建人"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button type="primary" @click="handleAdd(null, true)">
          新建
        </el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          v-if="[0, 2, 5, 6].includes(scope.row.status)"
          slot="reference"
          type="text"
          size="small"
          btn-text="编辑"
          permission-key=""
          @click="handleAdd(scope.row, true)"
        ></ac-permission-button>
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="查看"
          permission-key=""
          @click="handleAdd(scope.row, false)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="[0, 1, 5, 6].includes(scope.row.status)"
          slot="reference"
          type="text"
          size="small"
          btn-text="作废"
          permission-key=""
          @click="handleShowCancel(scope.row)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="scope.row.status === 2"
          slot="reference"
          type="text"
          size="small"
          btn-text="终止"
          permission-key=""
          @click="handleShowStop(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <BrandBonusDialog
      v-model="showDialog"
      :is-details="isDetails"
      :current-row="currentRow"
      :first-party-list="firstPartyList"
      :second-party-list="secondPartyList"
      @submit="getList(true)"
    />
    <el-dialog
      title="终止"
      :visible.sync="stopDialog"
      width="500px"
      @closed="onClose"
    >
      <p>是否确认终止品牌加成？</p>
      <el-form ref="form" :model="form" :rules="rulesForm" label-width="120px">
        <el-form-item label="申请终止日期:" prop="realEndTime">
          <el-date-picker
            v-model="form.realEndTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期"
            :picker-options="pickerOptions"
            style="width: 260px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="终止原因:" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="4"
            style="width: 260px"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="stopDialog = false">取 消</el-button>
        <el-button type="primary" @click="handleStop">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="作废"
      :visible.sync="cancelDialog"
      width="500px"
      @closed="onCancelClose"
    >
      <p>是否确认作废品牌加成？</p>
      <el-form ref="cancelForm" :model="cancelForm" label-width="100px">
        <el-form-item label="作废原因:" prop="remark">
          <el-input
            v-model="cancelForm.remark"
            type="textarea"
            :rows="4"
            style="width: 260px"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDialog = false">取 消</el-button>
        <el-button type="primary" @click="handleCancel">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';

  import { debounce, parseTime } from '@/utils';
  import BrandBonusDialog from './components/BrandBonusDialog';
  import dayjs from 'dayjs';
  import {
    QueryBrandList,
    getListOption,
    cancelBrand,
    stopBrand,
  } from '@/api/financeBrandBonus';
  export default {
    components: {
      dynamictable,
      BrandBonusDialog,
    },
    data() {
      let columns = [
        {
          prop: 'additionNo',
          label: '加成编号',
        },
        {
          prop: 'firstPartyName',
          label: '采购方名称',
        },
        {
          prop: 'secondPartyName',
          label: '供应商名称',
        },
        {
          prop: 'statusDesc',
          label: '加成状态',
        },
        {
          prop: 'creatorName',
          label: '创建人',
        },
        {
          prop: 'realBeginTime',
          label: '生效时间',
        },
        {
          prop: 'realEndTime',
          label: '失效时间',
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '100',
          scopedSlots: { customRender: 'operation' },
        },
      ];

      return {
        stopDialog: false,
        cancelDialog: false,
        showDialog: false,
        currentRow: null,
        stopTime: null,
        isDetails: true,
        form: {
          remark: '',
          realEndTime: '',
        },
        cancelForm: {
          remark: '',
        },
        firstPartyList: [],
        secondPartyList: [],
        statusList: [],
        searchParams: {
          additionNo: '',
          creatorName: '',
          firstPartyCode: '',
          secondPartyCode: '',
          status: null,
        },
        list: [],
        rulesForm: {
          realEndTime: [
            {
              required: true,
              message: '请选择申请终止日期',
              trigger: 'change',
            },
          ],
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          index: true,
          loading: false,
          border: true,
        },
        columns,
        pickerOptions: {
          disabledDate: current => {
            const currentDay = dayjs(current);
            const startTime = dayjs().startOf('h');
            const endTime = dayjs(this.stopTime).startOf('h');

            return (
              currentDay.isAfter(endTime) || currentDay.isBefore(startTime)
            );
          },
        },
      };
    },
    created() {
      this.loadOption();
      this.getList(true);
    },
    methods: {
      handleShowCancel(row) {
        this.cancelDialog = true;
        this.currentRow = row;
      },
      handleShowStop(row) {
        this.stopDialog = true;
        this.stopTime = row.realEndTime;
        this.currentRow = row;
      },
      handleCancel: debounce(function () {
        this.$refs.cancelForm.validate(async valid => {
          if (valid) {
            const { res, err } = await cancelBrand({
              additionNo: this.currentRow.additionNo,
              remark: this.cancelForm.remark,
            });
            if (!err) {
              this.cancelDialog = false;
              this.$message.success('作废操作成功');
              this.getList();
            }
          }
        });
      }, 1000),
      handleAdd(row, isDetails) {
        this.isDetails = isDetails;
        this.currentRow = row;
        this.showDialog = true;
      },
      handleStop: debounce(function () {
        this.$refs.form.validate(async valid => {
          if (valid) {
            const { res, err } = await stopBrand({
              additionNo: this.currentRow.additionNo,
              remark: this.form.remark,
              realEndTime: this.form.realEndTime,
            });
            if (!err) {
              this.stopDialog = false;
              this.$message.success('终止操作成功');
              this.getList();
            }
          }
        });
      }, 1000),
      async loadOption() {
        const { res, err } = await getListOption();
        if (res && !err) {
          this.firstPartyList = res.firstPartyList;
          this.secondPartyList = res.secondPartyList;
          this.statusList = res.statusList;
        }
      },
      getParams() {
        const params = {
          ...this.searchParams,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const { res, err } = await QueryBrandList(params);
        this.options.loading = false;
        if (res && !err) {
          this.list = res ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.getList(true);
      },
      onClose() {
        Object.assign(this.$data.form, this.$options.data().form);
        this.$nextTick(function () {
          this.$refs.form.clearValidate();
        });
      },
      onCancelClose() {
        Object.assign(this.$data.cancelForm, this.$options.data().cancelForm);
        this.$nextTick(function () {
          this.$refs.cancelForm.clearValidate();
        });
      },
    },
  };
</script>
<style lang="scss"></style>
