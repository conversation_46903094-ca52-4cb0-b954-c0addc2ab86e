<!-- 币种档案 -->
<template>
  <div class="container">
    <!-- <el-form :inline="true" size="small" :model="form" class="demo-form-inline">
      <el-form-item>
        <el-button type="primary" @click="onAdd">新增</el-button>
      </el-form-item>
    </el-form> -->
    <el-table :data="exchangeRateList" border style="width: 100%">
      <el-table-column prop="currencyCode" label="货币代码" align="center" />
      <el-table-column prop="id" label="数字代码" align="center" />
      <el-table-column
        prop="currencyChineseName"
        label="货币名称-中文"
        align="center"
      />
      <!-- <el-table-column prop="currencyEnglishName" label="货币名称-英文" align="center" /> -->
      <el-table-column prop="country" label="国家/地区/组织" align="center" />
    </el-table>
    <!-- <el-pagination
      class="pagination-block"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      background
      layout="total, sizes, prev, pager, next, jumper"
      :current-page="form.current"
      :page-sizes="[10,20,30,40]"
      :page-size="form.size"
      :total="total"
    />-->
    <el-dialog title="新增" :visible.sync="isShowAdd" width="500">
      <span>这是一段信息</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowAdd = false">取 消</el-button>
        <el-button type="primary" @click="onConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { getCurrencyList } from '@/api/exchange';
  export default {
    data() {
      return {
        exchangeRateList: [],
        form: {
          current: 1,
          size: 10,
        },
        total: 0,
        isShowAdd: false,
      };
    },
    created() {
      this.initData();
    },
    methods: {
      initData() {
        getCurrencyList(this.form).then(res => {
          this.exchangeRateList = res;
        });
      },
      handleSizeChange(val) {
        this.form.size = val;
        this.initData();
      },
      handleCurrentChange(val) {
        this.form.current = val;
        this.initData();
      },
      onAdd() {
        this.isShowAdd = true;
      },
      onConfirm() {
        this.isShowAdd = false;
      },
    },
  };
</script>
