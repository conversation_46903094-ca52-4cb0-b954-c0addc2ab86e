<!-- 汇率设置获取 -->
<template>
  <div class="container">
    <el-divider>新增币种汇设置</el-divider>
    <br />
    <br />
    <h4>设置币种汇率自动获取</h4>
    <el-divider></el-divider>
    <el-row class="fontSet">启动及账号维护：</el-row>
    <br />
    <el-form
      ref="ruleFormRef"
      :model="ruleForm"
      :rules="rules"
      label-width="100px"
      class="demo-ruleForm"
    >
      <el-form-item label="渠道:">
        <el-select v-model="ruleForm.channelId" @change="onChannelChange">
          <el-option
            v-for="item in channelList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="执行:">
        <el-radio-group v-model="ruleForm.executeType">
          <el-radio :label="1">立即</el-radio>
          <el-radio :label="2">定时</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="定时启动:" required>
        <el-date-picker
          v-model="ruleForm.executeTime"
          type="datetime"
          placeholder="选择日期时间"
          value-format="yyyy-MM-dd HH:mm:ss"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="时间范围:" required>
        <el-time-picker
          v-model="dateTimeRange"
          placeholder="选择时间范围"
          is-range
          value-format="HH:mm:ss"
        />
      </el-form-item>
      <el-form-item label="执行条件:">
        <el-radio-group v-model="ruleForm.executeCondition">
          <el-radio :label="1">每天</el-radio>
          <el-radio :label="2">工作日</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="银行url地址:" prop="dataUrl">
        <el-input v-model="ruleForm.dataUrl" type="textarea"></el-input>
      </el-form-item>
      <h4>选择币种信息</h4>
      <el-divider></el-divider>
      <br />
      <div class="btn-group">
        <ac-permission-button
          type="primary"
          btn-text="添加币种信息"
          permission-key="getExchangeRateSetters-add"
          plain
          @click="addCurrencyInfo()"
        ></ac-permission-button>
      </div>
      <br />
      <el-table
        class="listTable_ele"
        :data="currencyinfoTableList"
        stripe
        max-height="500"
        style="width: 100%"
      >
        <el-table-column type="index" label="批次" width="50"></el-table-column>
        <el-table-column
          prop="currencyCode"
          label="货币代码"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="currencyName"
          label="货币名称"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="country"
          label="国家"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="exchangeCurrencyCode"
          label="兑换币种代码"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="exchangeCurrencyName"
          label="兑换币种名称"
          align="center"
        ></el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click.native.prevent="
                deleteRow(scope.$index, currencyinfoTableList)
              "
            >
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <br />
      <el-form-item>
        <el-button type="primary" @click="returnPage()">返回</el-button>
        <ac-permission-button
          btn-text="提交"
          permission-key="getExchangeRateSetters-submit"
          type="primary"
          @click="submitForm('ruleForm')"
        ></ac-permission-button>
      </el-form-item>
    </el-form>

    <div>
      <h4>日志</h4>
      <el-divider></el-divider>
      <br />
      <div class="block">
        <el-timeline>
          <el-timeline-item
            v-for="item in ctrlInfoList"
            :key="item.id"
            :timestamp="item.createTime"
            placement="top"
          >
            <el-card>
              <h4 class="ctrlInfo">{{ item.content }}</h4>
              <p>
                <span class="ctrlName">{{ item.createUser }}</span>
                提交于
                <span calss="ctrlTime">{{ item.createTime }}</span>
              </p>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>

    <!-- 添加币种信息的对话框 -->
    <el-dialog
      title="新增币种信息"
      :visible.sync="isShowAdd"
      width="50%"
      :before-close="addDialogClosed"
    >
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="addFormRules"
        label-width="150px"
      >
        <el-form-item label="货币代码" prop="currencyCode">
          <el-select
            v-model="addForm.currencyCode"
            placeholder="货币代码"
            filterable
            @change="onCurrencyCodeChange"
          >
            <el-option
              v-for="item in exchangeRateList"
              :key="item.id"
              :label="item.currencyCode"
              :value="item.currencyCode"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="货币名称" prop="currencyName">
          <el-input v-model="addForm.currencyName" disabled></el-input>
        </el-form-item>
        <el-form-item label="国家" prop="country">
          <el-input v-model="addForm.country" disabled></el-input>
        </el-form-item>
        <el-form-item label="兑换币种代码" prop="exchangeCurrencyCode">
          <el-select
            v-model="addForm.exchangeCurrencyCode"
            placeholder="兑换币种代码"
            filterable
            @change="onExchangeCurrencyCodeChange"
          >
            <el-option
              v-for="item in exchangeRateList"
              :key="item.id"
              :label="item.currencyCode"
              :value="item.currencyCode"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="兑换币种名称" prop="exchangeCurrencyName">
          <el-input v-model="addForm.exchangeCurrencyName" disabled></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowAdd = false">取 消</el-button>
        <el-button type="primary" @click="addConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import {
    getExchangeRateConfig,
    getExchangeChannelList,
  } from '@/api/exchange';
  import { getCurrencyList } from '@/api/exchange';
  import { saveExchangeRateConfig } from '@/api/exchange';
  import { getLogList } from '@/api/exchange';
  export default {
    data() {
      return {
        currencyinfoTableList: [],
        channelList: [],
        exchangeRateList: [],
        ctrlInfoList: [],
        isShowAdd: false,
        ruleForm: {
          executeType: '',
          executeTime: '',
          startTime: '',
          endTime: '',
          executeCondition: '',
          dataUrl: '',
          channelId: '',
        },
        dateTimeRange: ['00:00:00', '00:00:00'], //定义时间范围数组
        rules: {
          executeType: [
            { required: false, message: '执行', trigger: 'change' },
          ],
          executeTime: [
            {
              type: 'date',
              required: true,
              message: '请选择日期',
              trigger: 'change',
            },
          ],
          startTime: [
            {
              type: 'date',
              required: true,
              message: '请选择开始时间',
              trigger: 'change',
            },
          ],
          endTime: [
            {
              type: 'date',
              required: true,
              message: '请选择结束时间',
              trigger: 'change',
            },
          ],
          executeCondition: [
            { required: true, message: '执行条件', trigger: 'change' },
          ],
          dataUrl: [
            { required: true, message: '请填写银行url地址', trigger: 'blur' },
          ],
        },
        addFormModal: {
          // 添加用户的表单数据
          country: '',
          currencyCode: '',
          currencyName: '',
          exchangeCurrencyCode: '',
          exchangeCurrencyName: '',
        },
        addForm: {}, // 添加用户的表单具体数据
        addFormRules: {
          // 添加表单的验证规则对象
          currencyCode: [
            { required: true, message: '请选择货币代码', trigger: 'change' },
            { required: true, message: '不能为空', trigger: 'change' },
          ],
          exchangeCurrencyCode: [
            { required: true, message: '请选择货币代码', trigger: 'change' },
            { required: true, message: '不能为空', trigger: 'change' },
          ],
        },
      };
    },
    created() {
      this.initData();
      getExchangeChannelList().then(list => {
        this.channelList = list;
      });
    },
    methods: {
      onChannelChange() {
        this.initData();
      },
      initData() {
        //获取form表单信息
        getExchangeRateConfig({
          channelId: this.ruleForm.channelId,
        }).then(res => {
          if (!res) return;
          const {
            executeType,
            executeTime,
            startTime,
            endTime,
            executeCondition,
            dataUrl,
            channelId,
            id,
          } = res;
          this.ruleForm = {
            executeType,
            executeTime,
            startTime,
            endTime,
            executeCondition,
            dataUrl,
            channelId,
          };
          if (res.startTime) {
            this.dateTimeRange = [res.startTime, res.endTime]; //开始时间 结束时间 数据回填
          }
          this.currencyinfoTableList = res.details;
          //获取币种信息中的 币种代码
          getCurrencyList({
            id: id,
          }).then(list => {
            this.exchangeRateList = list;
          });
          //获取日志列表信息
          getLogList({ id: id }).then(infoData => {
            this.ctrlInfoList = infoData;
          });
        });
      },
      //汇率设置获取信息 表单修改提交
      submitForm(formName) {
        this.$refs.ruleFormRef.validate(valid => {
          this.ruleForm.startTime = this.dateTimeRange[0];
          this.ruleForm.endTime = this.dateTimeRange[1];
          const submitData = {
            ...this.ruleForm,
            details: this.currencyinfoTableList,
          };
          if (valid) {
            saveExchangeRateConfig(submitData).then(() => {
              this.$message.success('提交成功！');
              //提交成功之后刷新日志
              this.initData();
            });
          } else {
            return false;
          }
        });
      },
      //返回
      returnPage() {
        this.$router.go(-1);
      },
      //移除 币种信息列表中的数据
      deleteRow(index, rows) {
        rows.splice(index, 1);
      },
      //点击添加 币种信息
      addCurrencyInfo() {
        this.isShowAdd = true;
        this.addForm = JSON.parse(JSON.stringify(this.addFormModal));
        // this.$refs.addFormRef.resetFields() //重置表单
      },
      // 监听添加用户对话框的关闭事件
      addDialogClosed() {
        this.addForm = this.addFormModal;
        this.isShowAdd = false;
      },
      //新增币种信息页面 货币代码 联动 货币名称和国家
      onCurrencyCodeChange(e) {
        const obj = this.exchangeRateList.filter(i => i.currencyCode === e)[0];
        this.addForm.currencyName = obj.currencyChineseName;
        this.addForm.country = obj.country;
      },
      //新增币种信息页面 兑换币种代码 联动 兑换币种名称
      onExchangeCurrencyCodeChange(e) {
        const obj = this.exchangeRateList.filter(i => i.currencyCode === e)[0];
        this.addForm.exchangeCurrencyName = obj.currencyChineseName;
      },
      //新增币种信息的 确定 按钮事件
      addConfirm() {
        this.$refs.addFormRef.validate(valid => {
          if (valid) {
            this.currencyinfoTableList.push(
              JSON.parse(JSON.stringify(this.addForm)),
            ); //添加新的数据进表格
            this.isShowAdd = false;
            this.$message.success('添加币种信息成功！');
            console.log(this.currencyinfoTableList);
            this.addForm = JSON.parse(JSON.stringify(this.addFormModal));
          } else {
            return false;
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
