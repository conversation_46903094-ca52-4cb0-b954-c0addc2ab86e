<!-- 汇率查询 -->
<template>
  <div class="container exchange-search">
    <el-form :inline="true" size="small" :model="form" class="demo-form-inline">
      <el-form-item label="日期范围">
        <el-date-picker
          v-model="searchDate"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="渠道:">
        <el-select v-model="form.channelId" clearable>
          <el-option
            v-for="item in channelList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="initData(true)">
          查询
        </el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <!--  excel表格上传  -->
      </el-form-item>
      <el-form-item>
        <el-upload
          :action="action"
          :headers="headers"
          :on-success="onSuccess"
          :before-upload="onUploading"
          :on-error="onError"
          accept=".xlsx"
        >
          <el-button
            slot="trigger"
            size="small"
            type="primary"
            :loading="loading"
          >
            导入
          </el-button>
        </el-upload>
      </el-form-item>
    </el-form>

    <!--  上传的excel表格预览  -->
    <div class="preview-excel">
      <el-table
        v-loading="tableLoading"
        :data="listTable"
        style="width: 100%"
        border
      >
        <el-table-column prop="publishTimeStr" label="日期" />
        <el-table-column prop="version" label="版本" />
        <el-table-column prop="channelId" label="渠道">
          <template slot-scope="{ row }">
            {{ formatChannel(row.channelId) }}
          </template>
        </el-table-column>
        <el-table-column prop="foreignMoneyUnit" label="单元（元）" />
        <el-table-column prop="foreignCurrencyCode" label="外币种" />
        <!-- <el-table-column prop="buyingRate" label="单位（元）" /> -->
        <el-table-column prop="standardMoneyCode" label="本币种" />
        <el-table-column prop="buyingRate" label="现汇买入价" />
        <el-table-column prop="cashBuyingRate" label="现钞买入价" />
        <el-table-column prop="cashSellingRate" label="现钞卖出价" />
        <el-table-column prop="middleRate" label="中间价" />
        <el-table-column prop="sellingRate" label="现汇卖出价" />
      </el-table>
      <el-pagination
        class="pagination-block"
        background
        :current-page="form.pageNo"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="form.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
  import { getExchangeList, getExchangeChannelList } from '@/api/exchange';
  import { getCookie } from '@/utils/auth';
  import injectHost from '@/utils/injectHost';
  export default {
    data() {
      return {
        listTable: [],
        channelList: [],
        form: {
          startTime: '',
          endTime: '',
          channelId: '',
          pageNo: 1,
          limit: 10,
        },
        searchDate: [],
        total: 0,
        action: '',
        headers: {
          token: getCookie(),
        },
        loading: false,
        tableLoading: false,
      };
    },
    created() {
      this.initData();
      getExchangeChannelList().then(res => {
        this.channelList = res;
      });
      this.action =
        window.location.protocol +
        '//' +
        injectHost().apiHost +
        '/api/exchange_rate/import';
    },
    methods: {
      initData(boolean = false) {
        if (boolean) {
          this.form.pageNo = 1;
        }
        this.form.startTime = this.searchDate[0] || '';
        this.form.endTime = this.searchDate[1] || '';
        this.tableLoading = true;
        getExchangeList(this.form)
          .then(res => {
            this.listTable = res.records;
            this.total = res.total;
          })
          .finally(() => {
            this.tableLoading = false;
          });
      },
      onReset() {
        this.form.pageNo = 1;
        this.searchDate = [];
        this.initData();
      },
      onSuccess(e) {
        if (!e.success) {
          this.$message.error('导入失败');
          this.loading = false;
          return;
        }
        this.$message.success('导入成功');
        this.loading = false;
        this.initData(true);
      },
      onUploading() {
        this.loading = true;
      },
      onError() {
        this.$message.error('导入失败');
        this.loading = false;
      },
      handleCurrentChange(val) {
        this.form.pageNo = val;
        this.initData();
      },
      handleSizeChange(e) {
        this.form.limit = e;
        this.initData();
      },
      formatChannel(value) {
        const obj = this.channelList.filter(i => i.value === value)[0];
        return obj ? obj.name : '- -';
      },
    },
  };
</script>

<style lang="scss">
  .exchange-search {
    .el-upload-list {
      display: none;
    }
  }
</style>
