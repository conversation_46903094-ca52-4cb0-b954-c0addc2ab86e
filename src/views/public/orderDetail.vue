<template>
  <div class="journal">
    <el-form
      ref="searchForm"
      :model="searchForm"
      label-width="40px"
      inline
      style="font-size: 12px; padding: 0"
    >
      <el-form-item label="创建日期：" prop="depotId" class="long-label">
        <el-date-picker
          v-model="searchDate"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="渠道：" prop="channelCode" class="long-label">
        <el-select
          v-model="searchForm.channelCode"
          placeholder="请选择"
          clearable
          filterable
          @change="getOptionList"
        >
          <el-option
            v-for="(item, index) in channelList"
            :key="index"
            :label="item.channelName"
            :value="item.channelCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="店铺：" prop="shopCode" class="long-label">
        <el-select
          v-model="searchForm.shopCode"
          placeholder="请选择"
          clearable
          filterable
          @change="getOptionList"
        >
          <el-option
            v-for="(item, index) in shopList"
            :key="index"
            :label="item.shopName"
            :value="item.shopCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="作废：" prop="isDeleted" class="long-label">
        <el-select
          v-model="searchForm.isDeleted"
          placeholder="请选择"
          clearable
          filterable
          @clear="searchForm.isDeleted = null"
        >
          <el-option
            v-for="(item, index) in isDeletedList"
            :key="index"
            :label="item.key"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="文件ID：" prop="fileId" class="long-label">
        <el-input v-model="searchForm.fileId" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="子订单号：" class="long-label">
        <el-input
          v-model="searchForm.subOrderId"
          placeholder="请输入"
        ></el-input>
      </el-form-item>

      <el-form-item style="margin-left: 20px">
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="income_detail" slot-scope="scope">
        <el-popover placement="right" width="600" trigger="click">
          <div>
            <el-table
              :data="formattedJson(scope.row.income_detail)"
              style="width: 100%"
            >
              <el-table-column
                prop="key"
                label="名称"
                width="300"
              ></el-table-column>
              <el-table-column
                prop="value"
                label="金额"
                width="300"
              ></el-table-column>
            </el-table>
          </div>
          <el-button slot="reference">查看</el-button>
        </el-popover>
      </template>
      <template slot="cost_detail" slot-scope="scope">
        <el-popover placement="right" width="600" trigger="click">
          <div>
            <el-table
              :data="formattedJson(scope.row.cost_detail)"
              style="width: 100%"
            >
              <el-table-column
                prop="key"
                label="名称"
                width="300"
              ></el-table-column>
              <el-table-column
                prop="value"
                label="金额"
                width="300"
              ></el-table-column>
            </el-table>
          </div>
          <el-button slot="reference">查看</el-button>
        </el-popover>
      </template>
      <template slot="fileId" slot-scope="scope">
        <div
          style="color: blue; cursor: pointer"
          @click="toImportRecord(scope.row)"
        >
          {{ scope.row.file_id }}
        </div>
      </template>
    </dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';

  import { exportExcel } from '@/api/blob';
  import { downloadFile } from '@/utils';
  import {
    getOptionList,
    pageOrderDetail,
    pageShopFlow,
  } from '@/api/publicShop';
  import AcPermissionButton from '@/components/ac-permission-button/index.vue';
  import importModal from './components/importModal';

  export default {
    name: 'OrderDetail',
    components: {
      importModal,
      AcPermissionButton,
      dynamictable,
    },

    data() {
      let columns = [
        {
          prop: 'id',
          label: 'ID',
        },
        {
          prop: 'file_id',
          label: '文件ID',
          width: '100px',
          scopedSlots: { customRender: 'fileId' },
        },
        {
          prop: 'order_time',
          label: '订单时间',
          render: ({ order_time }) => (
            <span>{order_time.replace('T', ' ')}</span>
          ),
          width: '115px',
        },
        {
          prop: 'payment_time',
          label: '支付时间',
          render: ({ payment_time }) => (
            <span>{payment_time ? payment_time.replace('T', ' ') : ''}</span>
          ),
          width: '115px',
        },
        {
          prop: 'channel_name',
          label: '渠道',
        },
        {
          prop: 'merchant_code',
          label: '商户号',
          width: '100px',
        },
        {
          prop: 'shop_name',
          label: '店铺',
          width: '120px',
        },
        {
          prop: 'brand_name',
          label: '品牌',
        },
        {
          prop: 'sub_order_id',
          label: '子订单号',
          width: '120px',
        },
        {
          prop: 'main_order_id',
          label: '父订单号',
          width: '120px',
        },
        {
          prop: 'product_quantity',
          label: '商品数量',
        },
        {
          prop: 'product_id',
          label: '商品ID',
          width: '120px',
        },
        {
          prop: 'order_payable_amount',
          label: '订单金额',
          render: ({ order_payable_amount }) => {
            return (
              <span>
                {order_payable_amount
                  ? parseFloat(order_payable_amount).toFixed(2)
                  : ''}
              </span>
            );
          },
        },
        {
          prop: 'shipping_fee',
          label: '运费',
          render: ({ shipping_fee }) => {
            return <span>{parseFloat(shipping_fee).toFixed(2)}</span>;
          },
        },
        {
          prop: 'tax_fee',
          label: '税费',
          render: ({ tax_fee }) => {
            return <span>{parseFloat(tax_fee).toFixed(2)}</span>;
          },
        },
        {
          prop: 'total_discount',
          label: '优惠总金额',
          render: ({ total_discount }) => {
            return (
              <span>
                {total_discount ? parseFloat(total_discount).toFixed(2) : ''}
              </span>
            );
          },
          width: '100px',
        },
        {
          prop: 'platform_discount',
          label: '平台优惠',
          render: ({ platform_discount }) => {
            return (
              <span>
                {platform_discount
                  ? parseFloat(platform_discount).toFixed(2)
                  : ''}
              </span>
            );
          },
        },
        {
          prop: 'merchant_discount',
          label: '商家优惠',
          render: ({ merchant_discount }) => {
            return (
              <span>
                {merchant_discount
                  ? parseFloat(merchant_discount).toFixed(2)
                  : ''}
              </span>
            );
          },
        },
        {
          prop: 'influencer_discount',
          label: '达人优惠',
          render: ({ influencer_discount }) => {
            return (
              <span>
                {influencer_discount
                  ? parseFloat(influencer_discount).toFixed(2)
                  : ''}
              </span>
            );
          },
        },
        {
          prop: 'payment_discount',
          label: '支付优惠',
          render: ({ payment_discount }) => {
            return (
              <span>
                {payment_discount
                  ? parseFloat(payment_discount).toFixed(2)
                  : ''}
              </span>
            );
          },
        },
        {
          prop: 'red_packet_deduction',
          label: '红包抵扣',
          render: ({ red_packet_deduction }) => {
            return (
              <span>
                {red_packet_deduction
                  ? parseFloat(red_packet_deduction).toFixed(2)
                  : ''}
              </span>
            );
          },
        },
        {
          prop: 'service_fee',
          label: '手续费',
          render: ({ service_fee }) => {
            return (
              <span>
                {service_fee ? parseFloat(service_fee).toFixed(2) : ''}
              </span>
            );
          },
        },

        {
          prop: 'create_time',
          label: '导入时间',
          render: ({ create_time }) => (
            <span>{create_time.replace('T', ' ')}</span>
          ),
          width: '115px',
        },
        {
          prop: 'is_deleted',
          label: '是否作废',
          render: ({ is_deleted }) => (
            <span>
              {this.isDeletedList.find(item => item.value === is_deleted)
                ?.key || ''}
            </span>
          ),
        },
      ];
      return {
        showModal: false,
        exportDate: '',
        searchDate: null,
        searchForm: {
          fileId: '',
          shopCode: '',
          channelCode: '',
          endCreateTime: '', //截止创建时间
          startCreateTime: '', //开始创建时间
          isDeleted: null,
          subOrderId: '',
        },

        shopList: [],
        channelList: [],
        channelFileTypeList: [],
        isDeletedList: [
          { key: '是', value: 1 },
          { key: '否', value: 0 },
        ],
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: false,
          maxHeight: 600,
        },
        columns,
      };
    },
    created() {
      this.searchForm.isDeleted = 0;
      this.searchForm.fileId = this.$route.query.fileId;
      this.getOptionList();
      this.getList(true);
    },
    methods: {
      getOptionParams() {
        return {
          channelCode: this.searchForm.channelCode,
          shopCode: this.searchForm.shopCode,
          fileTypeCode: this.searchForm.fileTypeCode,
        };
      },
      formattedJson(jsonString) {
        try {
          if (jsonString) {
            const jsonObj = JSON.parse(jsonString);
            // 将 JSON 对象转换为键值对数组
            return Object.entries(jsonObj).map(([key, value]) => ({
              key: key,
              value: JSON.stringify(value, null, 2), // 这里也可以格式化值
            }));
          } else {
            return {};
          }
        } catch (e) {
          console.error(e);
          return [{ key: 'Error', value: 'Invalid JSON' }];
        }
      },
      toImportRecord(row) {
        this.$router.push({
          path: './importRecord',
          query: {
            fileId: row.file_id,
          },
        });
      },
      onExport() {
        const { exportDate } = this;
        if (!exportDate) {
          this.$message({
            message: '请选择导出时间',
            type: 'warning',
          });
          return;
        }
        const params = {
          from: exportDate ? exportDate[0] : '',
          to: exportDate ? exportDate[1] : '',
        };
        exportExcel(params, '/api/magpie-bridge/invoice/download', 'get').then(
          res => {
            downloadFile(res.data, '凭证列表');
          },
        );
      },
      // 获取公司主体名称
      async getOptionList() {
        const res = await getOptionList(this.getOptionParams());
        this.channelList = res.channelList;
        this.shopList = res.shopList;
        this.channelFileTypeList = res.channelFileTypeList;
        this.importOptions.shopList = res.shopList;
        this.importOptions.channelFileTypeList = res.channelFileTypeList;
      },

      getParams() {
        this.searchForm.startCreateTime = this.searchDate
          ? this.searchDate[0]
          : '';
        this.searchForm.endCreateTime = this.searchDate
          ? this.searchDate[1]
          : '';
        return {
          ...this.searchForm,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        console.log(params);
        this.options.loading = true;
        const res = await pageOrderDetail(params);
        this.options.loading = false;
        this.list = res ? res.records : [];
        this.pagination.total = res ? res.total : 0;
      },
      // 重置
      resetForm() {
        this.searchDate = null;
        this.$refs.searchForm.resetFields();
        this.searchForm.fileId = '';
        this.getList(true);
        this.getOptionList();
      },
    },
  };
</script>
<style>
  /* 在你的 CSS 文件或<style>标签中添加以下样式 */
  .long-label .el-form-item__label {
    width: auto !important;
    min-width: 40px; /* 设置最小宽度 */
    white-space: nowrap; /* 防止文字换行 */
  }
</style>
