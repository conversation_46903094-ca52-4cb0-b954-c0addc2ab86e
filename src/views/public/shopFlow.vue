<template>
  <div class="journal">
    <el-form
      ref="searchForm"
      :model="searchForm"
      label-width="40px"
      inline
      style="font-size: 12px; padding: 0"
    >
      <el-form-item label="渠道：" prop="channelCode" class="long-label">
        <el-select
          v-model="searchForm.channelCode"
          placeholder="请选择"
          clearable
          filterable
          @change="getOptionList"
        >
          <el-option
            v-for="(item, index) in channelList"
            :key="index"
            :label="item.channelName"
            :value="item.channelCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="店铺：" prop="shopCode" class="long-label">
        <el-select
          v-model="searchForm.shopCode"
          placeholder="请选择"
          clearable
          filterable
          @change="getOptionList"
        >
          <el-option
            v-for="(item, index) in shopList"
            :key="index"
            :label="item.shopName"
            :value="item.shopCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="作废：" prop="isDeleted" class="long-label">
        <el-select
          v-model="searchForm.isDeleted"
          placeholder="请选择"
          clearable
          filterable
          @clear="searchForm.isDeleted = null"
        >
          <el-option
            v-for="(item, index) in isDeletedList"
            :key="index"
            :label="item.key"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="文件ID:" prop="fileId" class="long-label">
        <el-input v-model="searchForm.fileId" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="流水单:" class="long-label">
        <el-input
          v-model="searchForm.flowNumber"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="关联单:" class="long-label">
        <el-input
          v-model="searchForm.relatedFlowNumber"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="创建日期：" prop="depotId" class="long-label">
        <el-date-picker
          v-model="searchDate"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="流水时间：" prop="flowTimeRange" class="long-label">
        <el-date-picker
          v-model="flowTimeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="订单时间：" prop="orderTimeRange" class="long-label">
        <el-date-picker
          v-model="orderTimeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="订单号:" prop="orderNumber" class="long-label">
        <el-input
          v-model="searchForm.orderNumber"
          placeholder="请输入订单号"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="父订单号:"
        prop="parentOrderNumber"
        class="long-label"
      >
        <el-input
          v-model="searchForm.parentOrderNumber"
          placeholder="请输入父订单号"
        ></el-input>
      </el-form-item>

      <el-form-item style="margin-left: 20px">
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="income_detail" slot-scope="scope">
        <el-popover placement="right" width="600" trigger="click">
          <div>
            <el-table
              :data="formattedJson(scope.row.income_detail)"
              style="width: 100%"
            >
              <el-table-column
                prop="key"
                label="名称"
                width="300"
              ></el-table-column>
              <el-table-column
                prop="value"
                label="金额"
                width="300"
              ></el-table-column>
            </el-table>
          </div>
          <el-button slot="reference">查看</el-button>
        </el-popover>
      </template>
      <template slot="cost_detail" slot-scope="scope">
        <el-popover placement="right" width="600" trigger="click">
          <div>
            <el-table
              :data="formattedJson(scope.row.cost_detail)"
              style="width: 100%"
            >
              <el-table-column
                prop="key"
                label="名称"
                width="300"
              ></el-table-column>
              <el-table-column
                prop="value"
                label="金额"
                width="300"
              ></el-table-column>
            </el-table>
          </div>
          <el-button slot="reference">查看</el-button>
        </el-popover>
      </template>
      <template slot="trade_info" slot-scope="scope">
        <el-popover placement="right" width="600" trigger="click">
          <div>
            <el-table
              :data="formattedJson(scope.row.trade_info)"
              style="width: 100%"
            >
              <el-table-column
                prop="key"
                label="名称"
                width="300"
              ></el-table-column>
              <el-table-column
                prop="value"
                label="金额"
                width="300"
              ></el-table-column>
            </el-table>
          </div>
          <el-button slot="reference">查看</el-button>
        </el-popover>
      </template>
      <template slot="fileId" slot-scope="scope">
        <div
          style="color: blue; cursor: pointer"
          @click="toImportRecord(scope.row)"
        >
          {{ scope.row.file_id }}
        </div>
      </template>
    </dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';

  import { getOptionList, pageShopFlow } from '@/api/publicShop';

  export default {
    name: 'ShopFlow',
    components: {
      dynamictable,
    },

    data() {
      let columns = [
        {
          prop: 'id',
          label: 'ID',
        },
        {
          prop: 'file_id',
          label: '文件ID',
          width: '100px',
          scopedSlots: { customRender: 'fileId' },
        },
        {
          prop: 'flow_type',
          label: '流水类型',
        },
        {
          prop: 'flow_number',
          label: '流水单号',
          width: '150px',
        },

        {
          prop: 'flow_time',
          label: '流水时间',
          render: ({ flow_time }) => <span>{flow_time.replace('T', ' ')}</span>,
          width: '115px',
        },
        {
          prop: 'order_time',
          label: '订单时间',
          render: ({ order_time }) => (
            <span>{order_time ? order_time.replace('T', ' ') : ''}</span>
          ),
          width: '115px',
        },
        {
          prop: 'channel_name',
          label: '渠道',
        },
        {
          prop: 'shop_name',
          label: '店铺',
          width: '120px',
        },
        {
          prop: 'brand_name',
          label: '品牌',
        },
        {
          prop: 'order_number',
          label: '订单号',
          width: '150px',
        },
        {
          prop: 'parent_order_number',
          label: '父订单号',
          width: '150px',
        },
        {
          prop: 'return_number',
          label: '退款单号',
        },
        {
          prop: 'product_id',
          label: '商品ID',
          width: '120px',
        },
        {
          prop: 'currency',
          label: '币种',
        },
        {
          prop: 'expense_type',
          label: '费用类型',
          width: '120px',
        },
        {
          prop: 'flow_amount',
          label: '流水金额',
          render: ({ flow_amount }) => {
            return <span>{parseFloat(flow_amount).toFixed(2)}</span>;
          },
        },
        {
          prop: 'order_amount',
          label: '订单金额',
          render: ({ order_amount }) => {
            return (
              <span>
                {order_amount ? parseFloat(order_amount).toFixed(2) : ''}
              </span>
            );
          },
        },
        {
          prop: 'refund_amount',
          label: '退款金额',
          render: ({ refund_amount }) => {
            return (
              <span>
                {refund_amount ? parseFloat(refund_amount).toFixed(2) : ''}
              </span>
            );
          },
        },
        {
          prop: 'income_amount',
          label: '收入金额',
          render: ({ income_amount }) => {
            return (
              <span>
                {income_amount ? parseFloat(income_amount).toFixed(2) : ''}
              </span>
            );
          },
        },

        {
          prop: 'cost_amount',
          label: '费用金额',
          render: ({ cost_amount }) => {
            return <span>{parseFloat(cost_amount).toFixed(2)}</span>;
          },
        },
        {
          prop: 'income_detail',
          label: '收入明细',
          scopedSlots: { customRender: 'income_detail' },
        },
        {
          prop: 'cost_detail',
          label: '费用明细',
          scopedSlots: { customRender: 'cost_detail' },
        },
        {
          prop: 'trade_info',
          label: '交易信息',
          scopedSlots: { customRender: 'trade_info' },
        },
        {
          prop: 'file_type_name',
          label: '文件类型',
          width: '120px',
        },
        {
          prop: 'create_time',
          label: '时间',
          render: ({ create_time }) => (
            <span>{create_time.replace('T', ' ')}</span>
          ),
          width: '115px',
        },
        {
          prop: 'is_deleted',
          label: '是否作废',
          render: ({ is_deleted }) => (
            <span>
              {this.isDeletedList.find(item => item.value === is_deleted)
                ?.key || ''}
            </span>
          ),
        },
      ];
      return {
        showModal: false,
        exportDate: '',
        searchDate: null,
        flowTimeRange: null,
        orderTimeRange: null,
        searchForm: {
          fileId: '',
          shopCode: '',
          channelCode: '',
          endCreateTime: '', //截止创建时间
          startCreateTime: '', //开始创建时间
          flowNumber: '',
          relatedFlowNumber: '',
          orderNumber: '', // 订单号
          parentOrderNumber: '', // 父订单号
          startFlowTime: '', // 开始流水时间
          endFlowTime: '', // 结束流水时间
          startOrderTime: '', // 开始订单时间
          endOrderTime: '', // 结束订单时间
          isDeleted: null,
        },

        shopList: [],
        channelList: [],
        channelFileTypeList: [],
        isDeletedList: [
          { key: '是', value: 1 },
          { key: '否', value: 0 },
        ],
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: false,
          maxHeight: 600,
        },
        columns,
      };
    },
    created() {
      this.searchForm.isDeleted = 0;
      this.searchForm.fileId = this.$route.query.fileId;
      this.getOptionList();
      this.getList(true);
    },
    methods: {
      getOptionParams() {
        return {
          channelCode: this.searchForm.channelCode,
          shopCode: this.searchForm.shopCode,
          fileTypeCode: this.searchForm.fileTypeCode,
        };
      },
      formattedJson(jsonString) {
        try {
          if (jsonString) {
            const jsonObj = JSON.parse(jsonString);
            // 将 JSON 对象转换为键值对数组
            return Object.entries(jsonObj).map(([key, value]) => ({
              key: key,
              value: JSON.stringify(value, null, 2), // 这里也可以格式化值
            }));
          } else {
            return {};
          }
        } catch (e) {
          console.error(e);
          return [{ key: 'Error', value: 'Invalid JSON' }];
        }
      },
      toImportRecord(row) {
        this.$router.push({
          path: './importRecord',
          query: {
            fileId: row.file_id,
          },
        });
      },
      // 获取公司主体名称
      async getOptionList() {
        const res = await getOptionList(this.getOptionParams());
        this.channelList = res.channelList;
        this.shopList = res.shopList;
        this.channelFileTypeList = res.channelFileTypeList;
        this.importOptions.shopList = res.shopList;
        this.importOptions.channelFileTypeList = res.channelFileTypeList;
      },

      getParams() {
        this.searchForm.startCreateTime = this.searchDate
          ? this.searchDate[0]
          : '';
        this.searchForm.endCreateTime = this.searchDate
          ? this.searchDate[1]
          : '';
        this.searchForm.startFlowTime = this.flowTimeRange
          ? this.flowTimeRange[0]
          : '';
        this.searchForm.endFlowTime = this.flowTimeRange
          ? this.flowTimeRange[1]
          : '';
        this.searchForm.startOrderTime = this.orderTimeRange
          ? this.orderTimeRange[0]
          : '';
        this.searchForm.endOrderTime = this.orderTimeRange
          ? this.orderTimeRange[1]
          : '';
        return {
          ...this.searchForm,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        console.log(params);
        this.options.loading = true;
        const res = await pageShopFlow(params);
        this.options.loading = false;
        this.list = res ? res.records : [];
        this.pagination.total = res ? res.total : 0;
      },
      // 重置
      resetForm() {
        this.searchDate = null;
        this.flowTimeRange = null;
        this.orderTimeRange = null;
        this.$refs.searchForm.resetFields();
        this.searchForm.fileId = '';
        this.searchForm.orderNumber = '';
        this.searchForm.parentOrderNumber = '';
        this.searchForm.startFlowTime = '';
        this.searchForm.endFlowTime = '';
        this.searchForm.startOrderTime = '';
        this.searchForm.endOrderTime = '';
        this.getList(true);
        this.getOptionList();
      },
    },
  };
</script>
<style>
  /* 在你的 CSS 文件或<style>标签中添加以下样式 */
  .long-label .el-form-item__label {
    width: auto !important;
    min-width: 40px; /* 设置最小宽度 */
    white-space: nowrap; /* 防止文字换行 */
  }
</style>
