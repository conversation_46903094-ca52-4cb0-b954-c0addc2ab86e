<template>
  <div class="journal">
    <!-- 快捷标签 -->
    <div class="quick-tags">
      <el-tag
        v-for="tag in quickTags"
        :key="tag.value"
        :type="tag.active ? 'primary' : 'info'"
        @click="handleQuickTagClick(tag)"
      >
        {{ tag.label }}
        <span
          style="font-weight: bold; font-size: 16px"
          :style="{ color: tag.count > 0 ? '#ff6b35' : '#909399' }"
        >
          ({{ tag.count }})
        </span>
      </el-tag>
    </div>

    <el-form
      ref="searchForm"
      :model="searchForm"
      label-width="40px"
      inline
      style="font-size: 12px; padding: 0"
    >
      <el-form-item label="创建日期：" prop="depotId" class="long-label">
        <el-date-picker
          v-model="searchDate"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="渠道：" prop="channelCode" class="long-label">
        <el-select
          v-model="searchForm.channelCode"
          placeholder="请选择"
          clearable
          filterable
          @change="getOptionList"
        >
          <el-option
            v-for="(item, index) in channelList"
            :key="index"
            :label="item.channelName"
            :value="item.channelCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="店铺：" prop="shopCode" class="long-label">
        <el-select
          v-model="searchForm.shopCode"
          placeholder="请选择"
          clearable
          filterable
          @change="getOptionList"
        >
          <el-option
            v-for="(item, index) in shopList"
            :key="index"
            :label="item.shopName"
            :value="item.shopCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="类型：" prop="fileTypeCode" class="long-label">
        <el-select
          v-model="searchForm.fileTypeCode"
          placeholder="请选择"
          clearable
          filterable
          @change="getOptionList"
        >
          <el-option
            v-for="(item, index) in fileTypeList"
            :key="index"
            :label="item.fileTypeName"
            :value="item.fileTypeCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="文件ID：" prop="fileId" class="long-label">
        <el-input v-model="searchForm.fileId" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="主体：" prop="entityName" class="long-label">
        <el-input v-model="searchForm.entityName" placeholder="请输入主体名称" clearable></el-input>
      </el-form-item>

      <el-form-item label="标签：" prop="tenantTag" class="long-label">
        <el-select
          v-model="searchForm.tenantTag"
          placeholder="请选择"
          clearable
          filterable
        >
          <el-option
            v-for="(item, index) in tenantTagList"
            :key="index"
            :label="item.key"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态：" prop="status" class="long-label">
        <el-select
          v-model="searchForm.status"
          placeholder="请选择"
          clearable
          filterable
          @clear="searchForm.status = null"
        >
          <el-option
            v-for="(item, index) in statusList"
            :key="index"
            :label="item.key"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="作废：" prop="isDeleted" class="long-label">
        <el-select
          v-model="searchForm.isDeleted"
          placeholder="请选择"
          clearable
          filterable
          @clear="searchForm.isDeleted = null"
        >
          <el-option
            v-for="(item, index) in isDeletedList"
            :key="index"
            :label="item.key"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item style="margin-left: 20px">
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
        <el-button type="success" @click="exportData()">下载</el-button>
        <ac-permission-button
          btn-text="导入"
          permission-key="payableStatementDistribution-import"
          @click="showModal = true"
        ></ac-permission-button>
      </el-form-item>
    </el-form>

    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="memo" slot-scope="scope">
        <el-popover placement="right" width="600" trigger="click">
          <div>
            <el-table :data="formattedJson(scope.row.memo)" style="width: 100%">
              <el-table-column
                prop="time"
                label="时间"
                width="200"
              ></el-table-column>
              <el-table-column
                prop="user"
                label="操作人"
                width="200"
              ></el-table-column>
              <el-table-column
                prop="title"
                label="备注"
                width="200"
              ></el-table-column>
            </el-table>
          </div>
          <el-button slot="reference">查看</el-button>
        </el-popover>
      </template>

      <template slot="operation" slot-scope="scope">
        <el-button
          v-if="!scope.row.channel_file_type_code.includes('_DD_')"
          type="text"
          size="small"
          @click="toDetail(scope.row)"
        >
          资金明细
        </el-button>
        <el-button
          v-if="scope.row.channel_file_type_code.includes('_DD_')"
          type="text"
          size="small"
          @click="toOrderDetail(scope.row)"
        >
          订单明细
        </el-button>
        <el-button
          v-if="scope.row.status !== 5"
          type="text"
          size="small"
          @click="toNsDetail(scope.row)"
        >
          NS明细
        </el-button>
        <el-button type="text" size="small" @click="downLoadFile(scope.row)">
          下载文件
        </el-button>
        <el-button
          v-if="[6, 7, 8].includes(scope.row.status)"
          type="text"
          size="small"
          @click="retryForFailed(scope.row)"
        >
          重试
        </el-button>
        <el-button
          v-if="[3].includes(scope.row.status)"
          type="text"
          size="small"
          @click="retrySyncNs(scope.row)"
        >
          重推
        </el-button>
        <el-button
          v-if="![4].includes(scope.row.status)"
          type="text"
          size="small"
          @click="invalidFile(scope.row)"
        >
          作废
        </el-button>
        <el-button type="text" size="small" @click="displayLog(scope.row)">
          日志
        </el-button>
      </template>
    </dynamictable>
    <importModal
      v-model="showModal"
      :options="importOptions"
      :is-serve-data="false"
      @onSuccess="onSuccess"
    ></importModal>
    <import-log v-model="logDialogVisible" :log-data="logData"></import-log>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';

  import { exportExcel } from '@/api/blob';
  import { downloadFile } from '@/utils';
  import {
    getOptionList,
    importFile,
    invalidFile,
    pageImportRecord,
    retryForFailed,
    retrySyncNs,
    getQuickTagCounts,
    exportImportRecord,
  } from '@/api/publicShop';
  import AcPermissionButton from '@/components/ac-permission-button/index.vue';
  import importModal from './components/importModal';
  import importLog from './components/importLog.vue';

  export default {
    name: 'ImportRecord',
    components: {
      importModal,
      importLog,
      AcPermissionButton,
      dynamictable,
    },

    data() {
      let columns = [
        {
          prop: 'id',
          label: 'ID',
        },
        {
          prop: 'file_id',
          label: '文件ID',
          width: '100px',
        },
        {
          prop: 'account_period',
          label: '账期',
          // width: '150px',
        },

        {
          prop: 'tenant_tag',
          label: '标签',
          // width: '150px',
        },
        {
          prop: 'file_type_name',
          label: '类型',
          // width: '150px',
        },
        {
          prop: 'entity_name',
          label: '主体',
          width: '150px',
        },

        {
          prop: 'shop_name',
          label: '店铺',
          width: '150px',
        },
        {
          prop: 'shop_code',
          label: '店铺编码',
          width: '150px',
        },
        {
          prop: 'brand_name',
          label: '品牌',
          // width: '150px',
        },
        {
          prop: 'channel_name',
          label: '渠道',
          // width: '150px',
        },
        {
          prop: 'currency',
          label: '币种',
        },
        {
          prop: 'incurred_amount',
          label: '金额',

          render: ({ incurred_amount }) => {
            return <span>{parseFloat(incurred_amount).toFixed(2)}</span>;
          },
          width: '100px',
        },
        {
          prop: 'operator',
          label: '操作人',
          width: '140px',
        },
        // {
        //   prop: 'memo',
        //   label: '备注',
        //   scopedSlots: { customRender: 'memo' },
        // },
        {
          prop: 'create_time',
          label: '时间',
          render: ({ create_time }) => (
            <span>{create_time.replace('T', ' ')}</span>
          ),
          width: '115px',
        },

        {
          prop: 'status',
          label: '状态',
          fixed: 'right',
          render: ({ status }) => {
            return (
              <span>
                {this.statusList.find(item => item.value === status).key}
              </span>
            );
          },
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '200',
          scopedSlots: { customRender: 'operation' },
        },
      ];
      return {
        logDialogVisible: false, // 控制日志弹窗的显示
        logData: [], // 用于存储日志数据
        showModal: false,
        exportDate: '',
        searchDate: null,
        // 快捷标签数据
        quickTags: [
          { label: '待导入', value: 0, count: 0, active: false },
          { label: '待建单', value: 1, count: 0, active: false },
          { label: '待同步', value: 2, count: 0, active: false },
          { label: '已失败(导入)', value: 6, count: 0, active: false },
          { label: '已失败(建单)', value: 7, count: 0, active: false },
          { label: '已失败(同步)', value: 8, count: 0, active: false },
        ],
        searchForm: {
          shopCode: '',
          fileId: '',
          channelCode: '',
          fileTypeCode: '',
          endCreateTime: '',
          startCreateTime: '',
          tenantTag: '',
          isDeleted: null,
          status: null,
          entityName: '',
        },
        importOptions: {
          title: '公域文件',
          // shopList: this.shopList,
          // channelFileTypeList: this.channelFileTypeList,
        },
        isDeletedList: [
          { key: '是', value: 1 },
          { key: '否', value: 0 },
        ],

        statusList: [
          { key: '待导入', value: 0 },
          { key: '待建单', value: 1 },
          { key: '待同步', value: 2 },
          { key: '已同步', value: 3 },
          { key: '已作废', value: 4 },
          { key: '已导入', value: 5 },
          { key: '已失败(导入)', value: 6 },
          { key: '已失败(建单)', value: 7 },
          { key: '已失败(同步)', value: 8 },
        ],

        shopList: [],
        channelList: [],
        fileTypeList: [],
        channelFileTypeList: [],
        tenantTagList: [
          { key: 'VTN', value: 'VTN' },
          { key: 'WLZ', value: 'WLZ' },
        ],
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: false,
          maxHeight: 600,
        },
        columns,
      };
    },

    created() {
      this.searchForm.isDeleted = 0;
      this.searchForm.fileId = this.$route.query.fileId;
      this.getOptionList(this.getOptionParams());
      this.getList(true);
      this.getQuickTagCounts();
    },
    methods: {
      getOptionParams() {
        return {
          channelCode: this.searchForm.channelCode,
          shopCode: this.searchForm.shopCode,
          fileTypeCode: this.searchForm.fileTypeCode,
        };
      },
      // 获取快捷标签数量
      async getQuickTagCounts() {
        try {
          console.log('getQuickTagCounts');
          const res = await getQuickTagCounts();
          if (res) {
            this.quickTags.forEach(tag => {
              tag.count = res[tag.value] || 0;
            });
          }
        } catch (error) {
          console.error('获取快捷标签数量失败:', error);
        }
      },
      // 处理快捷标签点击
      handleQuickTagClick(tag) {
        // 重置所有标签的激活状态
        this.quickTags.forEach(t => (t.active = false));
        // 激活当前点击的标签
        tag.active = true;

        // 重置其他筛选条件，只保留状态筛选
        this.searchForm.channelCode = '';
        this.searchForm.shopCode = '';
        this.searchForm.fileTypeCode = '';
        this.searchForm.fileId = '';
        this.searchForm.entityName = '';
        this.searchForm.tenantTag = '';
        this.searchDate = null;
        this.searchForm.startCreateTime = '';
        this.searchForm.endCreateTime = '';

        // 设置状态筛选条件
        this.searchForm.status = tag.value;

        // 执行查询
        this.getList(true);
      },

      displayLog(row) {
        // 模拟获取日志数据（替换为真实的数据处理逻辑）
        this.logData = this.formattedJson(row.memo);
        // 打开弹窗
        this.logDialogVisible = true;
      },
      formattedJson(jsonString) {
        return JSON.parse(jsonString);
      },
      onExport() {
        const { exportDate } = this;
        if (!exportDate) {
          this.$message({
            message: '请选择导出时间',
            type: 'warning',
          });
          return;
        }
        const params = {
          from: exportDate ? exportDate[0] : '',
          to: exportDate ? exportDate[1] : '',
        };
        exportExcel(params, '/api/magpie-bridge/invoice/download', 'get').then(
          res => {
            downloadFile(res.data, '凭证列表');
          },
        );
      },
      // 导出当前筛选条件的数据
      async exportData() {
        try {
          this.$message({
            message: '正在导出数据，请稍候...',
            type: 'info',
            duration: 1000,
          });
          
          // 使用当前的筛选条件
          const params = this.getParams();
          // 移除分页参数，导出所有数据
          delete params.current;
          delete params.size;
          
          await exportImportRecord(params);
          
          this.$message({
            message: '导出成功',
            type: 'success',
          });
        } catch (error) {
          console.error('导出失败:', error);
          this.$message({
            message: '导出失败，请重试',
            type: 'error',
          });
        }
      },
      // 获取公司主体名称
      async getOptionList() {
        const res = await getOptionList(this.getOptionParams());
        this.channelList = res.channelList;
        this.shopList = res.shopList;
        this.channelFileTypeList = res.channelFileTypeList;
        this.fileTypeList = res.fileTypeList;
        // this.importOptions.shopList = res.shopList;
        // this.importOptions.channelFileTypeList = res.channelFileTypeList;
      },

      getParams() {
        this.searchForm.startCreateTime = this.searchDate
          ? this.searchDate[0]
          : '';
        this.searchForm.endCreateTime = this.searchDate
          ? this.searchDate[1]
          : '';
        const params = {
          ...this.searchForm,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        console.log(params);
        this.options.loading = true;
        const res = await pageImportRecord(params);
        this.options.loading = false;
        this.list = res ? res.records : [];
        this.pagination.total = res ? res.total : 0;
      },
      // 重置
      resetForm() {
        this.searchDate = null;
        this.$refs.searchForm.resetFields();
        this.searchForm.fileId = '';
        this.searchForm.entityName = '';
        // 重置快捷标签激活状态
        this.quickTags.forEach(tag => (tag.active = false));
        this.getList(true);
        this.getOptionList();
      },

      toDetail(row) {
        this.$router.push({
          path: './shopFlow',
          query: {
            fileId: row.file_id,
          },
        });
      },
      toOrderDetail(row) {
        this.$router.push({
          path: './orderDetail',
          query: {
            fileId: row.file_id,
          },
        });
      },
      toNsDetail(row) {
        this.$router.push({
          path: './nsCost',
          query: {
            fileId: row.file_id,
          },
        });
      },

      downLoadFile(row) {
        // 从URL中提取文件名
        const file_url = row.file_url;
        // 构建下载文件名称
        const fileName =
          row.account_period +
          '_' +
          row.channel_code +
          '_' +
          row.file_type_code +
          '_' +
          row.currency +
          '_' +
          row.shop_code +
          file_url.substring(file_url.lastIndexOf('.'));

        fetch(file_url)
          .then(response => response.blob())
          .then(blob => {
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            // 设置下载时的文件名
            link.download = fileName;
            // 模拟点击下载
            document.body.appendChild(link);
            link.click();
            // 清理DOM
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href);
          })
          .catch(error => {
            console.error('下载文件失败:', error);
          });
      },

      invalidFile(row) {
        const confirmed = window.confirm('是否确认删除文件？');
        if (confirmed) {
          invalidFile({ id: row.id });
          setTimeout(() => {
            this.getList(true);
          }, 1000); // 1秒后刷新页面
        } else {
          // 用户选择取消，可以在这里执行取消操作的逻辑
          console.log('用户取消标记文件');
        }
      },

      retryForFailed(row) {
        const confirmed = window.confirm('是否确认重试？');
        if (confirmed) {
          retryForFailed({ id: row.id });
          setTimeout(() => {
            this.getList(true);
          }, 1000); // 1秒后刷新页面
        } else {
          // 用户选择取消，可以在这里执行取消操作的逻辑
          console.log('用户取消标记文件');
        }
      },

      retrySyncNs(row) {
        const confirmed = window.confirm('是否确认重推？');
        if (confirmed) {
          retrySyncNs({ id: row.id });
          setTimeout(() => {
            this.getList(true);
          }, 1000); // 1秒后刷新页面
        } else {
          // 用户选择取消，可以在这里执行取消操作的逻辑
          console.log('用户取消标记文件');
        }
      },

      async onSuccess(e, done, err, data) {
        try {
          data['fileId'] = e.id;
          const res = await importFile({
            shopCode: data.shopCode,
            fileId: e.id,
            channelFileTypeCode: data.channelFileTypeCode,
            incurredAmount: data.incurredAmount,
            accountPeriod: data.accountPeriod,
          });
          this.showModal = false;
          setTimeout(() => {
            this.getList(true);
          }, 500);
          done();
        } catch (error) {
          err();
        }
      },
    },
  };
</script>
<style>
  /* 在你的 CSS 文件或<style>标签中添加以下样式 */
  .long-label .el-form-item__label {
    width: auto !important;
    min-width: 40px; /* 设置最小宽度 */
    white-space: nowrap; /* 防止文字换行 */
  }

  /* 快捷标签样式 */
  .quick-tags {
    margin-bottom: 20px;
    padding: 16px 20px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
  }

  /* 覆盖Element UI的默认样式 */
  .quick-tags .el-tag {
    margin: 0 !important;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    padding: 8px 16px !important;
    border-radius: 6px !important;
    border: 1px solid #dcdfe6 !important;
    background: #f5f7fa !important;
    color: #606266 !important;
    user-select: none;
    display: inline-flex !important;
    align-items: center !important;
    height: auto !important;
    line-height: 1 !important;
    box-sizing: border-box !important;
  }

  .quick-tags .el-tag:hover {
    border-color: #c0c4cc !important;
    background: #f0f2f5 !important;
  }

  .quick-tags .el-tag:active {
    transform: scale(0.98);
  }

  .quick-tags .el-tag.el-tag--primary {
    background: #409eff !important;
    border-color: #409eff !important;
    color: white !important;
  }

  .quick-tags .el-tag.el-tag--primary:hover {
    background: #337ecc !important;
    border-color: #337ecc !important;
  }

  .quick-tags .el-tag.el-tag--info {
    background: #f5f7fa !important;
    border-color: #dcdfe6 !important;
    color: #606266 !important;
  }

  .quick-tags .el-tag.el-tag--info:hover {
    background: #f0f2f5 !important;
    border-color: #c0c4cc !important;
  }

  /* 标签内容样式 */
  .quick-tags .tag-content {
    display: flex !important;
    align-items: center !important;
    gap: 6px;
    font-weight: 500;
    width: 100% !important;
    height: 100% !important;
  }

  .quick-tags .tag-icon {
    font-size: 14px;
    flex-shrink: 0;
  }

  .quick-tags .tag-label {
    font-size: 14px;
    flex-shrink: 0;
  }

  .quick-tags .tag-count {
    background: rgba(255, 255, 255, 0.2);
    color: inherit;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 600;
    min-width: 20px;
    text-align: center;
    flex-shrink: 0;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .quick-tags {
      padding: 12px 16px;
      gap: 8px;
    }

    .quick-tag {
      padding: 6px 12px;
      font-size: 13px;
    }
  }

  /* 动画关键帧 */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  /* 应用动画 */
  .journal {
    animation: fadeInUp 0.6s ease-out;
  }

  .quick-tags {
    animation: slideInLeft 0.6s ease-out;
  }

  .quick-tag:nth-child(1) {
    animation: slideInLeft 0.6s ease-out;
    animation-delay: 0.1s;
  }

  .quick-tag:nth-child(2) {
    animation: slideInLeft 0.6s ease-out;
    animation-delay: 0.2s;
  }

  .quick-tag:nth-child(3) {
    animation: slideInLeft 0.6s ease-out;
    animation-delay: 0.3s;
  }
</style>
