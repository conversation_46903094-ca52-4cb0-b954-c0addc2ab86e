<template>
  <div>
    <el-form
      ref="searchForm"
      :model="searchForm"
      label-width="40px"
      inline
      style="font-size: 12px; padding: 0"
    >
      <el-form-item
        label="创建日期："
        prop="depotId"
        class="long-label"
        style="margin-bottom: 5px"
      >
        <el-date-picker
          v-model="searchDate"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="渠道：" prop="channelCode" class="long-label">
        <el-select
          v-model="searchForm.channelCode"
          placeholder="请选择"
          clearable
          filterable
          @change="getOptionList"
        >
          <el-option
            v-for="(item, index) in channelList"
            :key="index"
            :label="item.channelName"
            :value="item.channelCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="店铺：" prop="shopCode" class="long-label">
        <el-select
          v-model="searchForm.shopCode"
          placeholder="请选择"
          clearable
          filterable
          @change="getOptionList"
        >
          <el-option
            v-for="(item, index) in shopList"
            :key="index"
            :label="item.shopName"
            :value="item.shopCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="作废：" prop="isDeleted" class="long-label">
        <el-select
          v-model="searchForm.isDeleted"
          placeholder="请选择"
          clearable
          filterable
          @clear="searchForm.isDeleted = null"
        >
          <el-option
            v-for="(item, index) in isDeletedList"
            :key="index"
            :label="item.key"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="文件ID：" prop="fileId" class="long-label">
        <el-input v-model="searchForm.fileId" placeholder="请输入"></el-input>
      </el-form-item>

      <el-form-item label="唯一单号：" class="long-label">
        <el-input
          v-model="searchForm.virtualId"
          placeholder="请输入"
        ></el-input>
      </el-form-item>

      <el-form-item style="margin-left: 20px">
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
      </el-form-item>

      <el-form-item style="margin-left: 20px">
        <span>结算总计：{{ totalSettlementAmount }}</span>
      </el-form-item>
    </el-form>
    <!--    <div class="summary-row">-->
    <!--      <span>总计：</span>-->
    <!--      <span>结算金额: {{ totalSettlementAmount }}</span>-->
    <!--    </div>-->
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="fileId" slot-scope="scope">
        <div
          style="color: blue; cursor: pointer"
          @click="toImportRecord(scope.row)"
        >
          {{ scope.row.fileId }}
        </div>
      </template>
    </dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table/index.vue';
  import { getOptionList, pageNsCost } from '@/api/publicShop';
  import AcPermissionButton from '@/components/ac-permission-button/index.vue';
  import importModal from './components/importModal';

  export default {
    name: 'NsCost',
    components: {
      importModal,
      AcPermissionButton,
      dynamictable,
    },

    data() {
      let columns = [
        {
          prop: 'id',
          // label: 'ID',
          type: 'index',
        },
        {
          prop: 'fileId',
          label: '文件ID',
          width: '100',
          scopedSlots: { customRender: 'fileId' },
        },
        {
          prop: 'tenantTag',
          label: 'NS标签',
        },
        {
          prop: 'virtualId',
          label: '唯一单号',
          width: '250',
        },

        {
          prop: 'docType',
          label: '单据类型',
        },
        {
          prop: 'settlementTotalAmount',
          label: '结算金额',
          width: '100',
        },
        {
          prop: 'settlementCurr',
          label: '结算币种',
        },
        {
          prop: 'orderTotalAmount',
          label: '交易金额',
          render: ({ orderTotalAmount }) => (
            <span> {orderTotalAmount != null ? orderTotalAmount : '-'}</span>
          ),
        },
        {
          prop: 'orderCurr',
          label: '交易币种',
          render: ({ orderCurr }) => (
            <span> {orderCurr != null ? orderCurr : '-'}</span>
          ),
        },
        {
          prop: 'exchangeRate',
          label: '汇率',
          render: ({ exchangeRate }) => (
            <span> {exchangeRate != null ? exchangeRate : '-'}</span>
          ),
        },
        {
          prop: 'expensesType',
          label: '费用类型',
          width: '120',
        },
        {
          prop: 'cePaymentAccount',
          label: '结算账户',
          width: '200',
        },
        {
          prop: 'date',
          label: '日期',
          width: '110',
        },
        {
          prop: 'customerCode',
          label: '店铺编码',
        },
        {
          prop: 'collectEntity',
          label: '主体编码',
          width: '100',
        },
        {
          prop: 'channel',
          label: '渠道编码',
        },
        {
          prop: 'brand',
          label: '品牌编码',
        },
        {
          prop: 'memo',
          label: '备注',
          showOverflowTooltip: true,
        },
        {
          prop: 'createTime',
          label: '时间',
          render: ({ createTime }) => (
            <span>{createTime.replace('T', ' ')}</span>
          ),
          width: '115px',
        },
        {
          prop: 'isDeleted',
          label: '是否作废',
          render: ({ isDeleted }) => (
            <span>
              {this.isDeletedList.find(item => item.value === isDeleted)?.key ||
                ''}
            </span>
          ),
        },
      ];
      return {
        showModal: false,
        exportDate: '',
        searchDate: null,
        searchForm: {
          fileId: '',
          virtualId: '',
          shopCode: '',
          channelCode: '',
          endCreateTime: '', //截止创建时间
          startCreateTime: '', //开始创建时间
          isDeleted: null,
        },

        shopList: [],
        channelList: [],
        channelFileTypeList: [],
        isDeletedList: [
          { key: '是', value: 1 },
          { key: '否', value: 0 },
        ],
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: false,
          maxHeight: 600,
        },
        columns,
      };
    },
    computed: {
      totalSettlementAmount() {
        return this.list
          .reduce((total, item) => {
            const amount =
              item.expensesType === '费用'
                ? -Number(item.settlementTotalAmount || 0)
                : Number(item.settlementTotalAmount || 0);
            return total + amount;
          }, 0)
          .toFixed(2);
      },
    },
    created() {
      this.searchForm.isDeleted = 0;
      this.searchForm.fileId = this.$route.query.fileId;
      this.getOptionList();
      this.getList(true);
    },
    methods: {
      getOptionParams() {
        return {
          channelCode: this.searchForm.channelCode,
          shopCode: this.searchForm.shopCode,
          fileTypeCode: this.searchForm.fileTypeCode,
        };
      },
      formattedJson(jsonString) {
        try {
          const jsonObj = JSON.parse(jsonString);
          // 将 JSON 对象转换为键值对数组
          return Object.entries(jsonObj).map(([key, value]) => ({
            key: key,
            value: JSON.stringify(value, null, 2), // 这里也可以格式化值
          }));
        } catch (e) {
          console.error(e);
          return [{ key: 'Error', value: 'Invalid JSON' }];
        }
      },
      toImportRecord(row) {
        this.$router.push({
          path: './importRecord',
          query: {
            fileId: row.fileId,
          },
        });
      },
      // 获取公司主体名称
      async getOptionList() {
        const res = await getOptionList(this.getOptionParams());
        this.channelList = res.channelList;
        this.shopList = res.shopList;
        this.channelFileTypeList = res.channelFileTypeList;
        this.importOptions.shopList = res.shopList;
        this.importOptions.channelFileTypeList = res.channelFileTypeList;
      },

      getParams() {
        this.searchForm.startCreateTime = this.searchDate
          ? this.searchDate[0]
          : '';
        this.searchForm.endCreateTime = this.searchDate
          ? this.searchDate[1]
          : '';
        return {
          ...this.searchForm,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
      },

      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        console.log(params);
        this.options.loading = true;
        const res = await pageNsCost(params);
        this.options.loading = false;
        this.list = res ? res.records : [];
        this.pagination.total = res ? res.total : 0;
      },
      // 重置
      resetForm() {
        this.searchDate = null;
        this.$refs.searchForm.resetFields();
        this.searchForm.fileId = '';
        this.getList(true);
        this.getOptionList();
      },
    },
  };
</script>
<style>
  /* 在你的 CSS 文件或<style>标签中添加以下样式 */
  .long-label .el-form-item__label {
    width: auto !important;
    min-width: 40px; /* 设置最小宽度 */
    white-space: nowrap; /* 防止文字换行 */
  }

  .summary-row {
    margin-top: 10px;
    font-weight: bold;
  }
</style>
