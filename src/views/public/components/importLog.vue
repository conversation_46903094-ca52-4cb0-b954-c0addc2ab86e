<!-- 日志弹窗 -->
<template>
  <el-dialog
    title="日志详情"
    :visible.sync="showDialog"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-table :data="logData" style="width: 100%">
      <el-table-column prop="time" label="时间" width="200"></el-table-column>
      <el-table-column prop="user" label="操作人" width="200"></el-table-column>
      <el-table-column
        prop="title"
        label="日志内容"
        width="200"
      ></el-table-column>
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button @click="showDialog = false">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      logData: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        loading: false,
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {
        if (!value) {
          // this.$nextTick(function () {
          //   this.$refs.log.clearFiles();
          // });
        }
        if (value) {
          if (this.options && this.options.type) {
            this.saveForm.chargeType = this.options.type;
          }
        }
      },
    },
    created() {},
    methods: {},
  };
</script>
