<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-06-07 10:15:57
 * @LastEditTime: 2022-11-18 17:08:11
 * @LastEditors: dddd
 * @Reference: 
-->
<template>
  <el-dialog
    :title="options.title"
    :visible.sync="showDialog"
    :close-on-click-modal="false"
    width="600px"
  >
    <el-form ref="formData" label-width="160px" label-position="right">
      <el-form-item v-if="true" label="店铺:">
        <el-select
          v-model="saveForm.shopCode"
          placeholder="请选择"
          @change="getOptionList"
          clearable
        >
          <el-option
            v-for="item in shopList"
            :key="item.shopCode"
            :label="item.shopName"
            :value="item.shopCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="true" label="文件类型:">
        <el-select
          v-model="saveForm.channelFileTypeCode"
          placeholder="请选择"
          @change="getOptionList"
          clearable
        >
          <el-option
            v-for="item in channelFileTypeList"
            :key="item.channelFileTypeCode"
            :label="item.channelFileTypeName"
            :value="item.channelFileTypeCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="!saveForm.channelFileTypeCode.includes('_DD_')"
        label="文件发生额:"
      >
        <el-input
          v-model="saveForm.incurredAmount"
          placeholder="请输入"
          style="width: 47%"
        ></el-input>
      </el-form-item>
      <el-form-item
        v-if="containsAccountPeriodString(saveForm.channelFileTypeCode)"
        label="账期:"
      >
        <el-date-picker
          v-model="saveForm.accountPeriod"
          type="month"
          placeholder="选择月"
          value-format="yyyy-MM"
        ></el-date-picker>
      </el-form-item>

      <el-upload
        ref="upload"
        drag
        :action="action"
        :headers="headers"
        :data="uploadData"
        :on-success="onSuccess"
        accept=".xlsx, .xls, .csv, .zip"
        :auto-upload="false"
        style="text-align: center"
        :before-upload="beforeUpload"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          <p>将文件拖到此处，或点击上传</p>
          <p>支持扩展名：.xlsx, .xls, .csv, .zip</p>
        </div>
      </el-upload>
      <el-divider></el-divider>
      <div>
        <h3>注意事项：</h3>
        <p>
          •
          上传后系统会进行解析处理，处理结果将会以邮件通知，处理完成后方体现在列表中。
        </p>
        <p>• 处理时间随上传文档数据量而上升，请耐心等待。</p>
        <p>
          • 系统会对导入内容进行校验，如有错误将通过邮件形式推送错误信息清单。
        </p>
        <p>• 导入进度在本页内可直观查看，读条状态下请勿关闭页面／刷新页面。</p>
        <p>
          • 导入完成后，请点击‘确定’提交任务至系统，如关闭／取消将视为放弃操作。
        </p>
      </div>

      <el-divider></el-divider>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="showDialog = false">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="handleOK">
        确 认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { debounce } from '@/utils';
  import { getCookie } from '@/utils/auth';
  import injectHost from '@/utils/injectHost';
  import { replaceLocalDomain } from '@/utils/index.js';
  import { getOptionList } from '@/api/publicShop';

  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      isServeData: {
        type: Boolean,
        default: false,
      },

      uploadUrl: {
        type: String,
        default: '/api/abmio/api/v1.0/upload',
      },
      uploadData: {
        type: Object,
        default: () => {
          return {
            appId: 'abmau',
            timeStamp: Date.now(),
          };
        },
      },
      options: {
        type: Object,
        default: () => ({
          title: '导入',
        }),
      },
    },
    data() {
      return {
        loading: false,
        saveForm: {
          shopCode: '',
          channelFileTypeCode: '',
          incurredAmount: null,
          accountPeriod: null,
        },
        shopList: [],
        channelList: [],
        fileTypeList: [],
        channelFileTypeList: [],
        headers: {
          token: getCookie(),
          appCode: process.env.VUE_APP_LOGIN_APP_CODE,
        }, // 导入头部信息
        action: '', // 导入接口
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {
        this.getOptionList();
        this.setDefaultAccountPeriod();
        if (!value) {
          this.$nextTick(function () {
            this.$refs.upload.clearFiles();
          });
        }
        if (value) {
          if (this.options && this.options.type) {
            this.saveForm.chargeType = this.options.type;
          }
        }
      },
    },
    created() {
      let Base64 = require('js-base64').Base64;
      this.headers['userName'] = Base64.encode(this.$store.state.user.username);
      this.headers['userId'] = this.$store.state.user.userInfo.id;
      // 上传地址
      this.action =
        window.location.protocol +
        '//' +
        replaceLocalDomain(injectHost().apiHost) +
        this.uploadUrl;
    },
    methods: {
      getOptionParams() {
        return {
          shopCode: this.saveForm.shopCode,
          channelFileTypeCode: this.saveForm.channelFileTypeCode,
        };
      },
      // 获取公司主体名称
      async getOptionList() {
        const res = await getOptionList(this.getOptionParams());
        this.channelList = res.channelList;
        this.shopList = res.shopList;
        this.channelFileTypeList = res.channelFileTypeList;
        this.fileTypeList = res.fileTypeList;
      },

      setDefaultAccountPeriod() {
        const now = new Date();
        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1); // 获取上个月的日期
        this.saveForm.accountPeriod = this.formatDate(lastMonth, 'yyyy-MM'); // 格式化日期为 YYYY-MM
      },
      formatDate(date, format) {
        const yyyy = date.getFullYear();
        const MM = `0${date.getMonth() + 1}`.slice(-2); // 月份从0开始，需要加1，并且补零
        return format.replace('yyyy', yyyy).replace('MM', MM);
      },

      containsAccountPeriodString(key) {
        return true;
        // const substrings = ['TM-G_DJS', 'TM-G_JS'];
        // return substrings.some(substring => key.includes(substring));
      },

      beforeUpload() {
        this.loading = true;
      },
      onSuccess(e, files) {
        if (!e.success) {
          this.$message.error(e.msg || '导入失败');
          this.loading = false;

          return;
        }
        const done = () => {
          this.$message.success('导入成功');
          this.showDialog = false;
          this.loading = false;
        };
        const err = () => {
          this.loading = false;
        };

        this.$emit('onSuccess', e.data, done, err, this.saveForm);
      },
      onDownload(val) {
        this.$emit('onDownload', val);
      },
      handleOK: debounce(function () {
        this.$refs.upload.submit();
      }, 1000),
    },
  };
</script>
<style lang="scss"></style>
