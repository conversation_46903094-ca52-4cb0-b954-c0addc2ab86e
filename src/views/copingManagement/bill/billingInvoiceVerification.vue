<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-06-07 17:54:37
 * @LastEditTime: 2022-09-20 10:45:16
 * @LastEditors: xuxiang
 * @Reference: 
-->
<template>
  <div v-if="tab === 1">
    <el-form inline>
      <el-form-item label="账单生成时间:">
        <el-date-picker
          v-model="createTime"
          type="daterange"
          clearable
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="核销时间:">
        <el-date-picker
          v-model="deadline"
          type="daterange"
          clearable
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="账期:">
        <el-date-picker
          v-model="searchParams.billPeriod"
          clearable
          type="month"
          value-format="yyyy-MM"
          format="yyyy-MM"
          placeholder="选择日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="采购主体:">
        <el-select
          v-model="searchParams.companyCodeList"
          filterable
          multiple
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in subjectDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="供应商名称:">
        <el-select
          v-model="searchParams.supplierCodeList"
          filterable
          multiple
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in supplyDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="结算方式:">
        <el-select
          v-model="searchParams.billType"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in settleTypeList"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="核销状态:">
        <el-select v-model="searchParams.无" clearable placeholder="请选择">
          <el-option
            v-for="item in billStatus"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item> -->

      <el-form-item label="对账单号:">
        <el-input
          v-model="searchParams.billNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <!-- <el-form-item label="发票号:">
        <el-input
          v-model="searchParams.invoiceNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item> -->
      <el-form-item label="业务类型:">
        <el-select
          v-model="searchParams.bizLine"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in bizLineId"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="0金额账单:">
        <el-checkbox v-model="searchParams.zeroBill">展示</el-checkbox>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
      :check-selectable="handleCheckSelectable"
      @selection-change="handleSelectionChange"
    >
      <template slot="operation" slot-scope="props">
        <ac-permission-button
          v-if="
            props.row.writeOffVO &&
            props.row.writeOffVO.needWrittenOff &&
            props.row.writeOffVO.notWrittenOffTaxInclusive > 0
          "
          slot="reference"
          btn-text="发票核销"
          type="text"
          size="small"
          permission-key=""
          @click="billWriteOff(props.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <applicationPaymentModal
      v-model="showModal"
      :supply-dict="supplyDict"
      :subject-dict="subjectDict"
      :biz-line-id="bizLineId"
      @confirm="getList"
    ></applicationPaymentModal>
    <billWriteOffModal
      v-model="showBillWriteOffModal"
      :record="record"
      :supply-dict="supplyDict"
      :subject-dict="subjectDict"
      @confirm="getList"
    ></billWriteOffModal>
    <uploadVoucherModal
      v-model="showUploadVoucherModal"
      :record="record"
      :subject-dict="subjectDict"
      :is-edit="isEdit"
      :current-info-list="currentInfoList"
      @confirm="getList"
    ></uploadVoucherModal>

    <settlementStatementModal
      v-model="showSettleModal"
      :modal-type="1"
      :supplier-info="supplierSettleInfo"
    ></settlementStatementModal>
  </div>
  <billQueryDetails
    v-else
    :bill-no="billNo"
    :bill-id="billId"
    :record="record"
    :settlement-type="settlementType"
    :detail-tab="detailTab"
    @goBack="handelJump(1)"
  ></billQueryDetails>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import billQueryDetails from './components/billQueryDetails';
  import applicationPaymentModal from './components/applicationPaymentModal';
  import uploadVoucherModal from './components/uploadVoucherModal';
  import settlementStatementModal from './components/settlementStatementModal';
  import billWriteOffModal from './components/billWriteOffModal';

  import {
    getBillList,
    getSupplier,
    getSelector,
    getBillDetail,
    getDicBill,
  } from '@/api/billManagement';
  import { creditAndPaymentDocCompositeShowVoucher } from '@/api/documentCenter';
  import {
    getSecondPartInformationById,
    querySettleType,
  } from '@/api/copingManagement';
  import { exportExcel } from '@/api/blob';
  import {
    downloadBlob,
    downloadFile,
    parseTime,
    debounce,
    initSearchParams,
  } from '@/utils';
  import injectHost from '@/utils/injectHost';
  import { replaceLocalDomain } from '@/utils/index.js';
  const BASE_URL = injectHost().apiHost;

  export default {
    name: 'BillingInvoiceVerification',
    components: {
      dynamictable,
      billQueryDetails,
      applicationPaymentModal,
      uploadVoucherModal,
      settlementStatementModal,
      billWriteOffModal,
    },
    beforeRouteEnter(to, from, next) {
      if (to.matched && to.matched.length > 2)
        to.matched.splice(1, to.matched.length - 2);
      next();
    },
    data() {
      return {
        tab: 1,
        createTime: '',
        deadline: '',
        showModal: false,
        showBillWriteOffModal: false,
        currentInfoList: [], // 详情凭证List
        settleTypeList: [], // 结算方式列表
        record: null, // 当前列信息
        isEdit: true,
        bathBillnos: '', // 批量下载订单号
        showUploadVoucherModal: false,
        searchParams: {
          billPeriod: '',
          serialNo: '', // 入库单号
          billNo: '', // 账单号
          billStatus: '', // 账单状态
          bizLine: '', // 业务类型
          companyCodeList: [], // 采购主体列表，多个使用,号分割
          createBeginDate: '', // 账单生成开始日期
          createEndDate: '', // 账单生成结束日期
          expireBeginDate: '', // 付款截止开始日期
          expireEndDate: '', // 付款截止结束日期
          billType: '', // 结算方式
          settlementCurrency: '', // 结算币种
          supplierCodeList: [], // 供应商列表，多个使用,号分割
          zeroBill: false, //是否展示0元单
        },
        list: [],

        subjectDict: [], // 签约主体
        supplyDict: [], // 供应商
        billStatus: [], // 账单状态

        currency: [], // 结算币种
        bizLineId: [], // 业务类型
        options: {
          loading: false,
          border: true,
          mutiSelect: false,
        },
        pageSize: 1,
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },

        billNo: null, // 详情billNo
        billId: null, // 详情billId
        detailTab: '1', // 打开详情tab
        settlementType: null, // 账单类型
        showSettleModal: false,
        supplierSettleInfo: {},
      };
    },
    created() {
      const { billNo } = this.$route.query;
      if (billNo) this.searchParams.billNo = billNo;

      this.getSupplier();
      this.getSelector();
      this.getDicBill();
      // this.getQuerySettleType();
      this.getList(true);
    },
    methods: {
      async getDicBill() {
        let res = await getDicBill();
        if (res) {
          this.settleTypeList = res.billType;
        }
      },
      isShowBtn(row) {
        if (row.cooperationWay == '经销') {
          return row.billTypeKey == '102' && row.settlementType == 'P';
        }
        return row.settlementType == 'P';
      },
      isShowUploadBtn(row) {
        if (row.cooperationWay == '经销' && row.billTypeKey) {
          return (
            row.billTypeKey == '102' &&
            row.settlementType == 'C' &&
            row.billStatus != '已收款'
          );
        }
        return row.settlementType == 'C' && row.billStatus != '已收款';
      },
      isShowSeeBtn(row) {
        if (row.cooperationWay == '经销' && row.billTypeKey) {
          return (
            row.billTypeKey == '102' &&
            row.settlementType == 'C' &&
            ['已收款', '部分收款'].includes(row.billStatus)
          );
        }
        return (
          row.settlementType == 'C' &&
          ['已收款', '部分收款'].includes(row.billStatus)
        );
      },
      handleCheckSelectable(row) {
        return row.settlementType == 'P';
      },
      handleSelectionChange(e = []) {
        const billNoList = e.map(item => item.billNo);
        this.bathBillnos = billNoList.join(',');
      },
      async uploadVoucher(record, isEdit) {
        const res = await creditAndPaymentDocCompositeShowVoucher({
          limit: 100,
          pageNo: 1,
          serialNo: record.itemNo,
          type: record.settlementType,
        });
        this.showUploadVoucherModal = true;
        this.record = record;
        this.currentInfoList = res ? res.list : [];
        this.isEdit = isEdit;
      },

      statementDownload: debounce(async function () {
        const billNos = this.bathBillnos;
        if (!billNos) {
          return this.$message.error('请选择账单');
        }
        const url = `${window.location.protocol}//${replaceLocalDomain(
          BASE_URL,
        )}/api/finance-bill/bill/download/multi`;

        window.location.href = `${url}?billNos=${billNos}&appCode=${process.env.VUE_APP_LOGIN_APP_CODE}`;
      }, 3000),

      handleShowModal() {
        this.showModal = true;
      },

      handelJump(val, row, tab) {
        this.tab = val;
        if (val === 1) {
          // 返回记住页数
          this.pagination.pageSize = this.pageSize;
          this.record = null;
          this.getList();
        } else {
          this.billNo = row.billNo;
          this.billId = row.billId;
          this.settlementType = row.settlementType;
          this.detailTab = tab;
          this.record = row;
        }
      },

      init() {
        this.pageSize = this.pagination.pageSize;
        Object.assign(this.$data.pagination, this.$options.data().pagination);
      },

      getParams() {
        this.searchParams.createBeginDate = this.createTime
          ? parseTime(this.createTime[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.createEndDate = this.createTime
          ? parseTime(this.createTime[1], '{y}-{m}-{d}')
          : '';
        this.searchParams.expireBeginDate = this.deadline
          ? parseTime(this.deadline[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.expireEndDate = this.deadline
          ? parseTime(this.deadline[1], '{y}-{m}-{d}')
          : '';

        const params = {
          ...this.searchParams,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };

        params.companyCodeList = params.companyCodeList.join(',');
        params.supplierCodeList = params.supplierCodeList.join(',');
        return params;
      },

      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        try {
          const res = await getBillList(initSearchParams(params));
          this.options.loading = false;
          if (res) {
            this.list = res && res.records ? res.records : [];
            this.pagination.total = res ? res.total : 0;
          }
        } catch (error) {
          this.options.loading = false;
        }
      },

      billWriteOff(record) {
        this.record = record;
        this.showBillWriteOffModal = true;
      },

      // 供应商结算信息
      async showSettle(row) {
        let res = await getSecondPartInformationById({
          supplierId: row.supplierCode,
        });

        if (res) {
          this.supplierSettleInfo = {
            secondPartyName: res.accountName,
            secondPartyCardNo: res.cardNo,
            secondPartyBankName: res.bankName,
            secondPartyAddress: res.address,
            secondPartyBankAddress: res.bankAddress,
          };
          this.showSettleModal = true;
        }
      },

      // 供应商和主体列表
      async getSupplier() {
        let res = await getSupplier();
        if (res) {
          this.supplyDict = res.supplyDict;
          this.subjectDict = res.subjectDict;
        }
      },
      // 结算方式列表
      async getQuerySettleType() {
        let res = await querySettleType();
        if (res) {
          const list = Object.keys(res).map(item => {
            return {
              key: item,
              value: res[item],
            };
          });
          this.settleTypeList = list;
        }
      },

      // 其他下拉列表
      async getSelector() {
        let res = await getSelector();

        if (res) {
          this.billStatus = res.billStatus;

          this.currency = res.currency;
          this.bizLineId = res.bizLineId;
        }
      },

      onExport: debounce(async function ({ billNo }) {
        exportExcel({ billNo }, '/api/finance-bill/bill/download', 'get').then(
          res => {
            downloadFile(res.data, '账单列表');
          },
        );
      }, 3000),

      onSearch: debounce(function () {
        this.getList(true);
      }, 1000),

      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.createTime = '';
        this.deadline = '';

        // this.getList();
      },

      getColumns() {
        const columns = [
          {
            prop: 'billPeriod',
            label: '账期',
          },
          {
            prop: 'billNo',
            label: '对账单号',
            render: ({ billNo, billTypeKey }) => (
              <div>
                <div>{billNo}</div>
                {billTypeKey === '106' ? (
                  <el-tag type="warning" effect="dark" class="contract-tag">
                    尾款结算
                  </el-tag>
                ) : (
                  ''
                )}
              </div>
            ),
          },
          {
            prop: 'companyName',
            label: '采购主体',
            minWidth: 140,
          },
          {
            prop: 'supplierName',
            label: '供应商名称',
            minWidth: 140,
          },
          {
            prop: 'bizLine',
            label: '业务类型',
          },
          {
            prop: 'performanceMethod',
            label: '履约方式',
            render: ({ performanceMethod }) => (
              <span>
                {performanceMethod == '1'
                  ? '自履约'
                  : performanceMethod == '2'
                  ? '非自履约'
                  : ''}
              </span>
            ),
          },
          {
            prop: 'billPayType',
            label: '账单类型',
          },
          // {
          //   prop: 'cooperationWay',
          //   label: '合作方式',
          // },
          {
            prop: 'billType',
            label: '结算方式',
          },
          {
            prop: 'settlementCurrency',
            label: '结算币种',
          },
          {
            prop: 'settlementAmount',
            label: '结算金额（含税）',
          },
          {
            prop: 'settlementTaxExclusive',
            label: '结算金额（未税）',
          },
          {
            prop: 'settlementTax',
            label: '税额',
          },

          {
            prop: 'billStatus',
            label: '账单状态',
          },
          {
            prop: 'writeOffVO.writeOffTaxInclusive',
            label: '已核销金额',
            render: ({ writeOffVO, billTypeKey }) => (
              <span>
                {['102', '200'].includes(billTypeKey)
                  ? writeOffVO.writeOffTaxInclusive
                  : 0}
              </span>
            ),
          },
          {
            prop: 'writeOffVO.notWrittenOffTaxInclusive',
            label: '未核销金额',
            render: ({ writeOffVO, billTypeKey }) => (
              <span>
                {['102', '200'].includes(billTypeKey)
                  ? writeOffVO.notWrittenOffTaxInclusive
                  : 0}
              </span>
            ),
          },
          {
            prop: 'completeTime',
            label: '付款完成时间',
          },
          {
            prop: 'createTime',
            label: '账单生成时间',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            minWidth: '200px',
            maxWidth: '400px',
            scopedSlots: { customRender: 'operation' },
          },
        ];

        return columns;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
