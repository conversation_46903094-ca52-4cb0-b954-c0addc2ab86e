<template>
  <div>
    <div v-if="tab === 1">
      <search
        :tab="tab"
        :search="search"
        @onSearch="onSearch"
        @onExport="onExport"
      ></search>
      <dynamictable
        :data-source="list"
        :columns="getColumns()"
        :options="options"
        :pagination="pagination"
        :fetch="getList"
      >
        <template slot="operation" slot-scope="props">
          <ac-permission-button
            slot="reference"
            btn-text="查看明细"
            type="text"
            size="small"
            permission-key=""
            @click="handelJump(2, '1', props.row)"
          ></ac-permission-button>
          <ac-permission-button
            slot="reference"
            btn-text="查看日志"
            type="text"
            size="small"
            permission-key=""
            @click="handelJump(2, '2', props.row)"
          ></ac-permission-button>
          <ac-permission-button
            v-if="props.row.status === '待付款'"
            slot="reference"
            btn-text="确认付款"
            type="text"
            size="small"
            permission-key=""
            @click="confirmPay(props.row)"
          ></ac-permission-button>
        </template>
      </dynamictable>
    </div>
    <div v-else>
      <please-pay-detail
        :detail-type="detailType"
        :current-row="currentRow"
        @goBack="handelJump(1)"
      />
    </div>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import search from './components/search';
  import pleasePayDetail from './components/pleasePayDetail.vue';
  import { getRequisitionList, requisitionPaid } from '@/api/billManagement';
  import { parseTime, debounce, initSearchParams, downloadFile } from '@/utils';
  import { newExportExcel } from '@/api/blob';
  export default {
    name: 'PleasePayQuery',
    components: {
      dynamictable,
      search,
      pleasePayDetail,
    },
    beforeRouteEnter(to, from, next) {
      if (to.matched && to.matched.length > 2)
        to.matched.splice(1, to.matched.length - 2);
      next();
    },
    data() {
      return {
        tab: 1,
        detailType: '1',
        search: {},
        list: [],
        currentRow: null,
        options: {
          loading: false,
          border: true,
        },
        pageSize: 1,
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },
    created() {},
    methods: {
      confirmPay(row) {
        this.$confirm('点击确认表示此款项已付，请选择', '付款确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          await requisitionPaid({ reqId: row.reqId });
          this.$message({
            type: 'success',
            message: '确认成功!',
          });
          this.getList();
        });
      },
      handelJump(val, type, row) {
        this.tab = val;
        this.detailType = type;
        this.currentRow = row;
      },
      init() {
        this.pageSize = this.pagination.pageSize;
        Object.assign(this.$data.pagination, this.$options.data().pagination);
      },

      getParams() {
        const params = {
          ...this.search,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },

      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await getRequisitionList(initSearchParams(params));
        this.options.loading = false;
        if (res) {
          this.list = res && res.records ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        }
      },

      exportExcel() {
        const params = this.getParams();
        newExportExcel(
          params,
          '/api/finance-bill/requisition/manageQuery/exportExcel',
          'post',
        ).then(res => {
          downloadFile(res.data, '请款管理查询结果');
        });
      },

      onSearch: debounce(function (e) {
        this.search = e;
        this.getList(true);
      }, 1000),
      onExport: debounce(function (e) {
        this.search = e;
        this.exportExcel();
      }, 1000),

      getColumns() {
        return [
          {
            prop: 'billPeriod',
            label: '请款单生成时间',
          },
          {
            prop: 'requisitionNo',
            label: '请款单号',
          },
          {
            prop: 'bizLine',
            label: '业务线',
          },
          {
            prop: 'companyName',
            label: '采购主体',
          },
          {
            prop: 'settlementCurrency',
            label: '结算币种',
          },
          {
            prop: 'requisitionAmount',
            label: '结算金额（含税）',
          },
          {
            prop: 'accountCurrency',
            label: '记账币种',
          },
          {
            prop: 'accountAmount',
            label: '记账金额（含税）',
          },
          {
            prop: 'exchangeRate',
            label: '汇率',
          },
          {
            prop: 'status',
            label: '单据状态',
          },
          {
            prop: 'paymentExpireTime',
            label: '付款截止时间',
            render: ({ paymentExpireTime }) => (
              <span>
                {paymentExpireTime ? parseTime(paymentExpireTime) : ''}
              </span>
            ),
          },
          {
            prop: 'paymentCompleteTime',
            label: '付款完成时间',
            render: ({ paymentCompleteTime }) => (
              <span>
                {paymentCompleteTime ? parseTime(paymentCompleteTime) : ''}
              </span>
            ),
          },
          {
            prop: 'oaCreateSerialNo',
            label: 'oa流程单号',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            minWidth: '200px',
            maxWidth: '400px',
            scopedSlots: { customRender: 'operation' },
          },
        ];
      },
    },
  };
</script>
<style lang="scss" scoped></style>
