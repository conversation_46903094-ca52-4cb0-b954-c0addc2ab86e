<!--
 * @Description:
 * @Author: xuxiang
 * @Date: 2022-06-07 14:36:20
 * @LastEditTime: 2022-09-16 14:43:13
 * @LastEditors: xuxiang
 * @Reference:
-->
<template>
  <div v-if="tab === 1">
    <el-form inline>
      <el-form-item label="账单生成时间:">
        <el-date-picker
          v-model="createTime"
          type="daterange"
          clearable
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="付款截止时间:">
        <el-date-picker
          v-model="deadline"
          type="daterange"
          clearable
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="账期:">
        <el-date-picker
          v-model="searchParams.billPeriod"
          clearable
          type="month"
          value-format="yyyy-MM"
          format="yyyy-MM"
          placeholder="选择日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="采购主体:">
        <el-select
          v-model="searchParams.companyCodeList"
          filterable
          multiple
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in subjectDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="供应商名称:">
        <el-select
          v-model="searchParams.supplierCodeList"
          filterable
          multiple
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in supplyDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="账单状态:">
        <el-select
          v-model="searchParams.billStatus"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in billStatus"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="结算方式:">
        <el-select
          v-model="searchParams.billType"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in settleTypeList"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="结算币种:">
        <el-select
          v-model="searchParams.settlementCurrency"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in currency"
            :key="item.key"
            :label="item.key"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="对账单号:">
        <el-input
          v-model="searchParams.billNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="采购单号:">
        <el-input v-model="searchParams.poNo" clearable placeholder="请输入" />
      </el-form-item>
      <el-form-item label="业务类型:">
        <el-select
          v-model="searchParams.bizLine"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in bizLineId"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="0金额账单:">
        <el-checkbox v-model="searchParams.zeroBill">展示</el-checkbox>
      </el-form-item>

      <el-form-item>
        <ac-permission-button
          type="primary"
          btn-text="查询"
          permission-key="billConsignment-search"
          @click="onSearch"
        ></ac-permission-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <ac-permission-button
          type="primary"
          btn-text="请款申请"
          permission-key=""
          @click="handleShowModal"
        ></ac-permission-button>
        <ac-permission-button
          type="primary"
          btn-text="批量核销"
          permission-key=""
          @click="billWriteOff"
        ></ac-permission-button>
        <ac-permission-button
          type="primary"
          btn-text="导出对账单列表"
          permission-key=""
          @click="statementExport"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <div style="margin-bottom: 10px; color: red">
      <!--      合计金额：{{ totalAmount }}-->
    </div>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
      :check-selectable="handleCheckSelectable"
      @selection-change="handleSelectionChange"
    >
      <template slot="operation" slot-scope="props">
        <!-- settlementType P应付  C应付收-->
        <ac-permission-button
          slot="reference"
          btn-text="查看明细"
          type="text"
          size="small"
          permission-key=""
          @click="handelJump(2, props.row, '1')"
        ></ac-permission-button>
        <ac-permission-button
          slot="reference"
          btn-text="对账单下载"
          type="text"
          size="small"
          permission-key=""
          @click="onExportUrl(props.row)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="isShowUploadBtn(props.row)"
          slot="reference"
          btn-text="上传凭证"
          type="text"
          size="small"
          permission-key=""
          @click="uploadVoucher(props.row, true)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="isShowSeeBtn(props.row)"
          slot="reference"
          btn-text="查看凭证"
          type="text"
          size="small"
          permission-key=""
          @click="uploadVoucher(props.row, false)"
        ></ac-permission-button>
        <ac-permission-button
          slot="reference"
          btn-text="查看日志"
          type="text"
          size="small"
          permission-key=""
          @click="handelJump(2, props.row, '3')"
        ></ac-permission-button>
        <ac-permission-button
          v-if="
            ['0', '107', '108'].includes(props.row.billTypeKey) &&
            props.row.billStatusKey === 'REQUEST_PENDING'
          "
          slot="reference"
          btn-text="确认收款"
          type="text"
          size="small"
          permission-key=""
          @click="confirmReceipt(props.row)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="props.row.billStatusKey === 'REJECTED'"
          slot="reference"
          btn-text="账单重组"
          type="text"
          size="small"
          permission-key=""
          @click="billRestructuring(props.row)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="props.row.billStatusKey === 'PENDING_AUDIT'"
          slot="reference"
          btn-text="bd人员账单审核"
          type="text"
          size="small"
          permission-key="billConsignment-bd-billReview"
          @click="handelJump(2, props.row, '1', '1')"
        ></ac-permission-button>
        <ac-permission-button
          v-if="props.row.billStatusKey === 'BD_APPROVED'"
          slot="reference"
          btn-text="财务人员账单审核"
          type="text"
          size="small"
          permission-key="billConsignment-finance-billReview"
          @click="handelJump(2, props.row, '1', '2')"
        ></ac-permission-button>
        <ac-permission-button
          v-if="
            [
              'PENDING_AUDIT',
              'BD_APPROVED',
              'FINANCIAL_APPROVED',
              'WAITING_INVOICE',
              'REQUEST_PENDING',
              'PENDING_RECEIPT',
              'REQUISITION_REJECTED',
            ].includes(props.row.billStatusKey)
          "
          slot="reference"
          btn-text="关闭账单"
          type="text"
          size="small"
          permission-key="billConsignment-close"
          @click="closeBill(props.row)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="
            [
              'FINANCIAL_APPROVED',
              'WAITING_INVOICE',
              'REQUEST_PENDING',
            ].includes(props.row.billStatusKey)
          "
          slot="reference"
          btn-text="回退到待审核"
          type="text"
          size="small"
          permission-key="billConsignment-rollback"
          @click="rollbackPendingAudit(props.row)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="props.row.billStatusKey === 'INVOICE_AUDITING'"
          slot="reference"
          btn-text="发票审核"
          type="text"
          size="small"
          permission-key=""
          @click="handelJump(2, props.row, '2', '', '1')"
        ></ac-permission-button>
        <ac-permission-button
          v-if="props.row.writeOffVO && props.row.writeOffVO.needWrittenOff"
          slot="reference"
          btn-text="发票核销"
          type="text"
          size="small"
          permission-key=""
          @click="billWriteOff(props.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <applicationPaymentModal
      v-model="showModal"
      :supply-dict="supplyDict"
      :subject-dict="subjectDict"
      :biz-line-id="bizLineId"
      :bill-status="billStatus"
      @confirm="getList"
    ></applicationPaymentModal>
    <billWriteOffNewModal
      v-model="showBillWriteOffNewModal"
      :bill-no-list="billNoList"
      :supply-dict="supplyDict"
      :subject-dict="subjectDict"
      :corporate-method="searchParams.cooperationMethod"
      :bill-invoice-type-list="billInvoiceTypeList"
      @confirm="getList"
    ></billWriteOffNewModal>
    <uploadVoucherModal
      v-model="showUploadVoucherModal"
      :record="record"
      :subject-dict="subjectDict"
      :is-edit="isEdit"
      :current-info-list="currentInfoList"
      @confirm="getList"
    ></uploadVoucherModal>

    <settlementStatementModal
      v-model="showSettleModal"
      :modal-type="1"
      :supplier-info="supplierSettleInfo"
    ></settlementStatementModal>
  </div>
  <billQueryDetails
    v-else
    :bill-no="billNo"
    :bill-id="billId"
    :record="record"
    :settlement-type="settlementType"
    :detail-tab="detailTab"
    :corporate-method="2"
    @goBack="handelJump(1)"
  ></billQueryDetails>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import billQueryDetails from './components/billQueryDetails';
  import applicationPaymentModal from './components/applicationPaymentModal';
  import uploadVoucherModal from './components/uploadVoucherModal';
  import settlementStatementModal from './components/settlementStatementModal';
  import billWriteOffModal from './components/billWriteOffModal';

  import {
    getBillList,
    getSupplier,
    getSelector,
    billReceived,
    getDicBill,
    billRebuild,
    billSummary,
    onExportUrl,
    closeBill,
    rollbackPendingAudit,
  } from '@/api/billManagement';
  import { creditAndPaymentDocCompositeShowVoucher } from '@/api/documentCenter';
  import {
    getSecondPartInformationById,
    querySettleType,
  } from '@/api/copingManagement';
  import { exportExcel, newExportExcel } from '@/api/blob';
  import { downloadFile, parseTime, debounce, initSearchParams } from '@/utils';
  import injectHost from '@/utils/injectHost';
  import { replaceLocalDomain } from '@/utils/index.js';
  import billWriteOffNewModal from '@/views/copingManagement/bill/components/billWriteOffNewModal.vue';
  const BASE_URL = injectHost().apiHost;

  export default {
    name: 'BillConsignment',
    components: {
      billWriteOffNewModal,
      dynamictable,
      billQueryDetails,
      applicationPaymentModal,
      uploadVoucherModal,
      settlementStatementModal,
    },
    beforeRouteEnter(to, from, next) {
      if (to.matched && to.matched.length > 2)
        to.matched.splice(1, to.matched.length - 2);
      next();
    },
    data() {
      return {
        tab: 1,
        totalAmount: '',
        statusObj: {
          0: '待审核',
          1: '已通过',
          '-1': '已驳回',
        },
        createTime: '',
        deadline: '',
        showModal: false,
        showBillWriteOffModal: false,
        showBillWriteOffNewModal: false,
        currentInfoList: [], // 详情凭证List
        settleTypeList: [], // 结算方式列表
        billInvoiceTypeList: [], // 账单发票类型列表

        record: null, // 当前列信息
        isEdit: true,
        billNoList: [],
        selectBillList: [],
        bathBillnos: '', // 批量下载订单号
        showUploadVoucherModal: false,
        searchParams: {
          serialNo: '', // 入库单号
          billNo: '', // 账单号
          poNo: '', // 采购单号
          billStatus: '', // 账单状态
          bizLine: '', // 业务类型
          companyCodeList: [], // 采购主体列表，多个使用,号分割
          createBeginDate: '', // 账单生成开始日期
          createEndDate: '', // 账单生成结束日期
          expireBeginDate: '', // 付款截止开始日期
          expireEndDate: '', // 付款截止结束日期
          billType: '', // 结算方式
          settlementCurrency: '', // 结算币种
          supplierCodeList: [], // 供应商列表，多个使用,号分割
          cooperationMethod: 2,
          billPeriod: '',
          zeroBill: false,
        },
        list: [],

        subjectDict: [], // 签约主体
        supplyDict: [], // 供应商
        billStatus: [], // 账单状态

        currency: [], // 结算币种
        bizLineId: [], // 业务类型
        options: {
          loading: false,
          border: true,
          mutiSelect: true,
        },
        pageSize: 1,
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },

        billNo: null, // 详情billNo
        billId: null, // 详情billId
        detailTab: '1', // 打开详情tab
        settlementType: null, // 账单类型
        showSettleModal: false,
        supplierSettleInfo: {},
      };
    },
    created() {
      const { billNo } = this.$route.query;
      if (billNo) this.searchParams.billNo = billNo;

      this.getSupplier();
      this.getSelector();
      this.getDicBill();

      this.getList(true);
    },
    methods: {
      async billSummary() {
        const params = this.getParams();
        try {
          const res = await billSummary(initSearchParams(params));

          this.totalAmount = res;
        } catch (error) {}
      },
      statementExport() {
        const params = this.getParams();
        this.$confirm('确定将该数据导出?', '导出', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          newExportExcel(
            params,
            '/api/finance-bill/bill/download/billManage',
            'post',
          ).then(res => {
            downloadFile(res.data, '寄售账单列表');
          });
        });
      },
      async closeBill(row) {
        this.$confirm('确定关闭账单?', '关闭', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          await closeBill({ billId: row.billId }).then(() => {
            this.$message({
              message: '关闭成功',
              type: 'success',
            });
            this.getList();
          });
        });
      },
      async rollbackPendingAudit(row) {
        this.$confirm('确定回退待审核?', '回退', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          await rollbackPendingAudit({ billId: row.billId });
          this.$message({
            message: '回退成功',
            type: 'success',
          });
          this.getList();
        });
      },
      async billRestructuring(row) {
        await billRebuild({ billId: row.billId });
        this.$message({
          message: '重组成功',
          type: 'success',
        });
        this.getList();
      },
      async confirmReceipt(row) {
        this.$confirm('点击确认表示此款项已付，请选择！', '收款确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          await billReceived({ billNo: row.billNo });
          this.getList();
          this.$router.push({
            path: '/documentCenter/documentQuery/receiveBill',
            query: {
              billNo: row.billNo,
            },
          });
        });
      },
      isShowBtn(row) {
        if (row.cooperationWay == '经销') {
          return row.billTypeKey == '102' && row.settlementType == 'P';
        }
        return row.settlementType == 'P';
      },
      isShowUploadBtn(row) {
        if (row.cooperationWay == '经销' && row.billTypeKey) {
          return (
            row.billTypeKey == '102' &&
            row.settlementType == 'C' &&
            row.billStatus != '已收款'
          );
        }
        return row.settlementType == 'C' && row.billStatus != '已收款';
      },
      isShowSeeBtn(row) {
        if (row.cooperationWay == '经销' && row.billTypeKey) {
          return (
            row.billTypeKey == '102' &&
            row.settlementType == 'C' &&
            ['已收款', '部分收款'].includes(row.billStatus)
          );
        }
        return (
          row.settlementType == 'C' &&
          ['已收款', '部分收款'].includes(row.billStatus)
        );
      },
      handleCheckSelectable(row) {
        return row.settlementType == 'P';
      },
      handleSelectionChange(e = []) {
        this.billNoList = e.map(item => item.billNo);
        this.selectBillList = e;
        this.bathBillnos = this.billNoList.join(',');
      },
      async uploadVoucher(record, isEdit) {
        const res = await creditAndPaymentDocCompositeShowVoucher({
          limit: 100,
          pageNo: 1,
          serialNo: record.itemNo,
          type: record.settlementType,
        });
        this.showUploadVoucherModal = true;
        this.record = record;
        this.currentInfoList = res ? res.list : [];
        this.isEdit = isEdit;
      },

      statementDownload: debounce(async function () {
        const billNos = this.bathBillnos;
        if (!billNos) {
          return this.$message.error('请选择账单');
        }
        const url = `${window.location.protocol}//${replaceLocalDomain(
          BASE_URL,
        )}/api/finance-bill/bill/download/multi`;

        window.location.href = `${url}?billNos=${billNos}&appCode=${process.env.VUE_APP_LOGIN_APP_CODE}`;
      }, 3000),

      handleShowModal() {
        this.showModal = true;
      },
      // reviewerType 是否显示账单审核，invoiceReview 是否显示发票审核
      handelJump(val, row, tab, reviewerType, invoiceReview) {
        this.tab = val;
        if (val === 1) {
          // 返回记住页数
          this.pagination.pageSize = this.pageSize;
          this.record = null;
          this.getList();
        } else {
          this.billNo = row.billNo;
          this.billId = row.billId;
          this.settlementType = row.settlementType;
          this.detailTab = tab;
          this.record = { ...row, reviewerType, invoiceReview };
        }
      },

      init() {
        this.pageSize = this.pagination.pageSize;
        Object.assign(this.$data.pagination, this.$options.data().pagination);
      },

      getParams() {
        this.searchParams.createBeginDate = this.createTime
          ? parseTime(this.createTime[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.createEndDate = this.createTime
          ? parseTime(this.createTime[1], '{y}-{m}-{d}')
          : '';
        this.searchParams.expireBeginDate = this.deadline
          ? parseTime(this.deadline[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.expireEndDate = this.deadline
          ? parseTime(this.deadline[1], '{y}-{m}-{d}')
          : '';

        const params = {
          ...this.searchParams,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };

        params.companyCodeList = params.companyCodeList.join(',');
        params.supplierCodeList = params.supplierCodeList.join(',');
        return params;
      },

      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        try {
          const res = await getBillList(initSearchParams(params));
          this.options.loading = false;
          if (res) {
            this.list = res && res.records ? res.records : [];
            this.pagination.total = res ? res.total : 0;
          }
        } catch (error) {
          this.options.loading = false;
        }
      },

      billWriteOff(row) {
        if (row) {
          if (
            !(
              row.writeOffVO?.writtenOffStatus === '待核销' ||
              row.writeOffVO?.writtenOffStatus === '部分核销'
            )
          ) {
            return this.$message.error('账单' + row.billNo + '状态无法核销');
          }
          this.billNoList = Array.of(row.billNo);
        } else {
          if (this.selectBillList.length === 0) {
            return this.$message.error('请选择账单');
          }

          let hasError = false;

          this.selectBillList.forEach(item => {
            if (
              !(
                item.writeOffVO?.writtenOffStatus === '待核销' ||
                item.writeOffVO?.writtenOffStatus === '部分核销'
              )
            ) {
              hasError = true;
              this.$message.error('账单' + item.billNo + '状态无法核销');
            }
          });
          if (hasError) {
            return;
          }
          this.billNoList = this.selectBillList.map(item => item.billNo);
        }
        this.showBillWriteOffNewModal = true;
      },

      // 供应商结算信息
      async showSettle(row) {
        let res = await getSecondPartInformationById({
          supplierId: row.supplierCode,
        });

        if (res) {
          this.supplierSettleInfo = {
            secondPartyName: res.accountName,
            secondPartyCardNo: res.cardNo,
            secondPartyBankName: res.bankName,
            secondPartyAddress: res.address,
            secondPartyBankAddress: res.bankAddress,
          };
          this.showSettleModal = true;
        }
      },

      // 供应商和主体列表
      async getSupplier() {
        let res = await getSupplier();
        if (res) {
          this.supplyDict = res.supplyDict;
          this.subjectDict = res.subjectDict;
        }
      },

      // 其他下拉列表
      async getSelector() {
        let res = await getSelector();

        if (res) {
          this.currency = res.currency;
          this.bizLineId = res.bizLineId;
        }
      },
      async getDicBill() {
        let res = await getDicBill();
        if (res) {
          this.billStatus = res.billStatus;
          this.settleTypeList = res.billType;
          this.billInvoiceTypeList = res.billInvoiceType;
        }
      },
      async onExportUrl(row) {
        let res = await onExportUrl({ billNo: row.billNo });

        if (res) {
          window.open(res, '_blank');
        } else {
          this.$message.error('导出对账单下载失败' + row.billNo);
        }
      },
      // onExport: debounce(async function ({ billNo }) {
      //   exportExcel(
      //     { billNo },
      //     '/api/finance-bill/bill/downloadUrl',
      //     'get',
      //   ).then(res => {
      //     downloadFile(res.data, '账单列表');
      //   });
      // }, 3000),

      onSearch: debounce(function () {
        this.getList(true);
        if (this.searchParams.settlementCurrency) {
          this.billSummary();
        } else {
          this.totalAmount = '';
        }
      }, 1000),

      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.createTime = '';
        this.deadline = '';
        this.totalAmount = '';
        // this.getList();
      },

      getColumns() {
        const columns = [
          {
            prop: 'billPeriod',
            label: '账期',
          },
          {
            prop: 'billNo',
            label: '对账单号',
            render: ({ billNo, billTypeKey }) => (
              <div>
                <div>{billNo}</div>
                {billTypeKey === '106' ? (
                  <el-tag type="warning" effect="dark" class="contract-tag">
                    尾款结算
                  </el-tag>
                ) : (
                  ''
                )}
              </div>
            ),
          },
          {
            prop: 'billStatus',
            label: '账单状态',
          },
          {
            prop: 'writeOffStatus',
            label: '核销状态',
            render: ({ writeOffVO, billTypeKey }) => (
              <el-tag
                type={
                  writeOffVO.writtenOffStatus === '已核销'
                    ? 'success'
                    : writeOffVO.writtenOffStatus === '待核销' ||
                      writeOffVO.writtenOffStatus === '部分核销'
                    ? 'primary'
                    : writeOffVO.writtenOffStatus === '待审核'
                    ? 'warning'
                    : 'info'
                }
                effect="dark"
              >
                {writeOffVO.writtenOffStatus}
              </el-tag>
            ),
          },

          {
            prop: 'companyName',
            label: '采购主体',
            minWidth: 140,
          },
          {
            prop: 'supplierName',
            label: '供应商名称',
            minWidth: 140,
          },
          {
            prop: 'bizLine',
            label: '业务类型',
          },
          {
            prop: 'performanceMethod',
            label: '履约方式',
            render: ({ performanceMethod }) => (
              <span>
                {performanceMethod === '1'
                  ? '自履约'
                  : performanceMethod === '2'
                  ? '非自履约'
                  : ''}
              </span>
            ),
          },
          {
            prop: 'billPayType',
            label: '账单类型',
          },
          // {
          //   prop: 'cooperationWay',
          //   label: '合作方式',
          // },
          {
            prop: 'billType',
            label: '结算方式',
          },
          {
            prop: 'settlementCurrency',
            label: '结算币种',
          },
          {
            prop: 'settlementAmount',
            label: '结算金额（含税）',
          },
          {
            prop: 'settlementTaxExclusive',
            label: '结算金额（未税）',
          },
          {
            prop: 'settlementTax',
            label: '税额',
          },
          {
            prop: 'writeOffVO.writeOffTaxInclusive',
            label: '已核销金额',
            render: ({ writeOffVO, billTypeKey }) => (
              <span>
                {['102', '200', '100001'].includes(billTypeKey)
                  ? writeOffVO.writeOffTaxInclusive
                  : 0}
              </span>
            ),
          },
          {
            prop: 'writeOffVO.notWrittenOffTaxInclusive',
            label: '未核销金额',
            render: ({ writeOffVO, billTypeKey }) => (
              <span>
                {['102', '200', '100001'].includes(billTypeKey)
                  ? writeOffVO.notWrittenOffTaxInclusive
                  : 0}
              </span>
            ),
          },
          // {
          //   prop: 'a',
          //   label: '记账币种',
          // },
          // {
          //   prop: 'b',
          //   label: '记账金额（含税）',
          // },
          // {
          //   prop: 'c',
          //   label: '记账金额（未税）',
          // },
          // {
          //   prop: 'exchangeRate',
          //   label: '汇率',
          // },
          {
            prop: 'invoiceAttachment',
            label: '发票状态',
            render: ({ invoiceAttachment }) => (
              <span>
                {invoiceAttachment
                  ? this.statusObj[invoiceAttachment.status]
                  : ''}
              </span>
            ),
          },
          {
            prop: 'invoiceAttachment',
            label: '发票类型',
            render: ({ invoiceAttachment }) => (
              <span>
                {invoiceAttachment
                  ? invoiceAttachment.invoiceType === 0
                    ? '电子（形式）发票'
                    : '纸质发票'
                  : ''}
              </span>
            ),
          },
          {
            prop: 'completeTime',
            label: '付款完成时间',
          },
          {
            prop: 'createTime',
            label: '账单生成时间',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            minWidth: '200px',
            maxWidth: '400px',
            scopedSlots: { customRender: 'operation' },
          },
        ];

        return columns;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
