<!--
 * @Description:
 * @Author: wangkai
 * @Date: 2024-02-26 11:09:41
 * @LastEditors: wangkai
 * @Reference:
-->
<template>
  <div>
    <el-dialog
      class="applicationPaymentModal"
      width="1500px"
      :visible.sync="showDialog"
    >
      <div style="margin-bottom: 25px">
        <span>
          <el-button type="primary" @click="addRow">新增发票</el-button>
        </span>
        <span style="margin-left: 5px">
          <el-button type="primary" :loading="saving" @click="submit">
            核销
          </el-button>
        </span>
      </div>
      <div style="margin-bottom: 20px">
        <span>币种:</span>
        <span style="font-size: 20px; color: green">
          {{ currency }}
        </span>
        <span style="margin-left: 20px">已核销开票金额:</span>
        <span style="font-size: 20px">
          {{ writeOffInvoice }}
        </span>
        <span style="margin-left: 20px">已核销非开票金额:</span>
        <span style="font-size: 20px">
          {{ writeOffNoInvoice }}
        </span>
        <span style="margin-left: 20px">未核销开票金额：</span>
        <span style="font-size: 20px">
          {{ notWrittenOffInvoice }}
        </span>
        <span style="margin-left: 20px">未核销非开票金额：</span>
        <span style="font-size: 20px">
          {{ notWrittenOffNoInvoice }}
        </span>
        <span style="margin-left: 20px">已选开票金额：</span>
        <span style="font-size: 20px; color: red">
          {{ chooseWriteOffInvoice }}
        </span>
        <span style="margin-left: 20px">已选开票税额：</span>
        <span style="font-size: 20px; color: red">
          {{ chooseWriteOffInvoiceTax }}
        </span>
        <span style="margin-left: 20px">已选非开票金额：</span>
        <span style="font-size: 20px; color: orange">
          {{ chooseWriteOffNoInvoice }}
        </span>
        <span style="margin-left: 20px">发票总额：</span>
        <span style="font-size: 20px; color: red">
          {{ sumInvoiceAmount }}
        </span>
      </div>

      <div style="width: 1450px">
        <el-table :data="invoiceData" border>
          <el-table-column label="操作" :width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="mini"
                @click="removeRow(scope.$index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>

          <el-table-column prop="invoiceType" label="发票类型">
            <template slot-scope="scope">
              <el-select v-model="scope.row.invoiceType" placeholder="请选择">
                <el-option
                  v-for="item in billInvoiceTypeList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>

          <el-table-column prop="invoiceNo" label="发票号">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.invoiceNo"
                @blur="checkInvoiceNo(scope.row)"
              ></el-input>
            </template>
          </el-table-column>

          <el-table-column prop="invoiceAmountNotTaxed" label="发票未税金额">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.invoiceAmountNotTaxed"
                @change="checkInvoiceAmountNotTaxedChange(scope.row)"
              ></el-input>
            </template>
          </el-table-column>

          <el-table-column prop="invoiceTax" label="发票税额">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.invoiceTax"
                @change="checkInvoiceTaxChange(scope.row)"
              ></el-input>
            </template>
          </el-table-column>

          <el-table-column prop="invoiceAmount" label="发票含税金额">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.invoiceAmount"
                @input="handleInvoiceAmountChange"
                @change="checkInvoiceAmountChange(scope.row)"
              ></el-input>
            </template>
          </el-table-column>

          <el-table-column prop="invoiceDate" label="发票日期" :width="250">
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.invoiceDate"
                :clearable="false"
                type="date"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column
            prop="invoiceDueDate"
            label="发票截止日期"
            :width="250"
          >
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.invoiceDueDate"
                :clearable="false"
                type="date"
              ></el-date-picker>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <dynamictable
        :data-source="list"
        :columns="getColumns()"
        :options="{
          ...options,
          // record &&
          // record.cooperationWay !== '寄售' &&
          // !['200'].includes(record.billTypeKey),
        }"
        style="height: 800px; overflow: scroll; margin-top: 10px"
        :check-selectable="handleCheckSelectable"
        @selection-change="handleSelectionChange"
      >
        <template slot="currentWriteOffSettleQuantity" slot-scope="scope">
          <el-form
            v-if="distribution"
            ref="form"
            :model="scope.row"
            :inline="true"
          >
            <el-form-item
              v-if="isSku(scope.row)"
              :prop="'currentWriteOffSettleQuantity'"
              :rules="[
                {
                  required: true,
                  message: '请输入核销数量',
                  trigger: 'blur',
                },
                {
                  validator: (rule, value, callback) => {
                    if (value > scope.row.settleQuantity) {
                      callback('核销数量超限');
                    } else {
                      callback();
                    }
                  },
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                v-model="scope.row.currentWriteOffSettleQuantity"
                style="width: 60px"
                placeholder="请输入核销数量"
                @input="changeCurrentWriteOffSettleQuantity(scope.row)"
              ></el-input>
            </el-form-item>
          </el-form>
          <span v-if="!distribution">
            {{ scope.row.currentWriteOffSettleQuantity }}
          </span>
        </template>
      </dynamictable>
    </el-dialog>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import { detailList } from '@/api/billManagement';
  import { settleOrderItemWriteOff } from '@/api/copingManagement';
  import { add } from '@/utils/math';
  import bigDecimal from 'js-big-decimal';

  export default {
    components: {
      dynamictable,
    },
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },

      supplyDict: {
        type: Array,
        default: _ => [],
      },

      subjectDict: {
        type: Array,
        default: _ => [],
      },

      billNoList: {
        type: Array,
        default: _ => [],
      },
      billInvoiceTypeList: {
        type: Array,
        default: _ => [],
      },
      corporateMethod: {
        type: Number,
        default: 1,
      },
    },
    data() {
      const validValue = (rule, value, callback) => {
        const { selectData } = this.saveForm;
        const index = rule.field?.split('.')[1] || 0;
        // console.log(index, selectData, value, rule, 'selectData');
        const item = selectData.length ? selectData[index] : {};
        // if (!item.unwrittenOffQuantity || item.unwrittenOffQuantity == 0) {
        //   callback(new Error('当前需要核销的数量为0'));
        // } else if (value > item.unwrittenOffQuantity || value < 0) {
        //   callback(
        //     new Error(`结算数量大于等0小于等于${item.unwrittenOffQuantity}`),
        //   );
        // } else {
        //   const reg = /^([1-9]\d*|[0]{1,1})$/;
        //   if (!reg.test(value)) {
        //     callback(new Error('结算数量应为整数'));
        //   } else {
        //     callback();
        //   }
        // }
        if (value > item.unwrittenOffQuantity || value < 0) {
          callback(
            new Error(`结算数量大于等0小于等于${item.unwrittenOffQuantity}`),
          );
        } else {
          const reg = /^([1-9]\d*|[0]{1,1})$/;
          if (!reg.test(value)) {
            callback(new Error('结算数量应为整数'));
          } else {
            callback();
          }
        }
      };

      return {
        visible: false,
        selectbatchData: [], // 批量选择的数据
        saveForm: {
          selectData: [], // 选中的数据
        },

        error: '',
        searchParams: {
          skuCode: '',
        },
        invoiceData: [],
        list: [],
        resList: [],
        options: {
          loading: false,
          border: true,
          mutiSelect: true,
        },
        saving: false,
        currency: '',
        sumInvoiceAmount: 0,
        distribution: true,
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
      notWrittenOffInvoice() {
        let total = this.list
          .filter(next => next.invoiceSummation)
          .reduce((sum, next) => {
            if (this.distribution) {
              return add(sum, +next?.writeOff?.notWrittenOffTaxInclusive);
            } else {
              return add(sum, +next?.currentWriteOffAmount);
            }
          }, 0);
        return bigDecimal.round(total, 2, bigDecimal.RoundingModes.HALF_UP);
      },
      notWrittenOffNoInvoice() {
        let total = this.list
          .filter(next => !next.invoiceSummation)
          .reduce((sum, next) => {
            if (this.distribution) {
              return add(sum, +next?.writeOff?.notWrittenOffTaxInclusive);
            } else {
              return add(sum, +next?.currentWriteOffAmount);
            }
          }, 0);
        return bigDecimal.round(total, 2, bigDecimal.RoundingModes.HALF_UP);
      },
      writeOffInvoice() {
        let total = this.resList
          .filter(next => next.invoiceSummation)
          .reduce((sum, next) => {
            if (this.distribution) {
              return add(sum, +next?.writeOff?.writeOffTaxInclusive);
            } else {
              return 0;
            }
          }, 0);
        console.log('totle', total);
        return bigDecimal.round(total, 2, bigDecimal.RoundingModes.HALF_UP);
      },
      writeOffNoInvoice() {
        let total = this.resList
          .filter(next => !next.invoiceSummation)
          .reduce((sum, next) => {
            if (this.distribution) {
              return add(sum, +next?.writeOff?.writeOffTaxInclusive);
            } else {
              return 0;
            }
          }, 0);
        return bigDecimal.round(total, 2, bigDecimal.RoundingModes.HALF_UP);
      },
      chooseWriteOffInvoice() {
        let total = this.selectbatchData
          .filter(next => next.invoiceSummation)
          .reduce((sum, next) => {
            return add(sum, +next?.currentWriteOffAmount);
          }, 0);
        return bigDecimal.round(total, 2, bigDecimal.RoundingModes.HALF_UP);
      },
      chooseWriteOffInvoiceTax() {
        let total = this.selectbatchData
          .filter(next => next.invoiceSummation)
          .reduce((sum, next) => {
            return add(sum, +next?.currentWriteOffTax);
          }, 0);
        return bigDecimal.round(total, 2, bigDecimal.RoundingModes.HALF_UP);
      },
      chooseWriteOffNoInvoice() {
        let total = this.selectbatchData
          .filter(next => !next.invoiceSummation)
          .reduce((sum, next) => {
            return add(sum, +next?.currentWriteOffAmount);
          }, 0);
        return bigDecimal.round(total, 2, bigDecimal.RoundingModes.HALF_UP);
      },
    },
    watch: {
      show(value) {
        if (value) {
          this.saveForm.selectData = [];
          this.list = [];
          this.getList();
          this.distribution = this.corporateMethod === 1;
        }
      },
    },

    methods: {
      validValue(rule, value, callback) {
        const item = this.form;
        if (value > item.unwrittenOffQuantity || value < 0) {
          callback(
            new Error(`结算数量大于等0小于等于${item.unwrittenOffQuantity}`),
          );
        } else {
          const reg = /^([1-9]\d*|[0]{1,1})$/;
          if (!reg.test(value)) {
            callback(new Error('结算数量应为整数'));
          } else {
            callback();
          }
        }
      },
      // 禁止选择不符合的数据
      handleCheckSelectable(row) {
        // return row.writeOff.needWrittenOff && row.unwrittenOffQuantity != 0;
        return row.writeOff.needWrittenOff;
      },
      onSearch() {
        this.getList();
      },

      changeCurrentWriteOffSettleQuantity(row) {
        this.calWriteOff(row);
        this.selectbatchData = this.list
          .filter(item => item.select)
          .map(item => ({
            ...item,
          }));
      },

      handleSelectionChange(e = []) {
        this.list.forEach(item => {
          item.currentWriteOffAmount = '';
          item.currentWriteOffTax = '';
          item.select = false;
        });
        e.forEach(item => {
          item.select = true;
          this.calWriteOff(item);
        });
        this.selectbatchData = e.map(item => ({
          ...item,
        }));
      },

      calWriteOff(item) {
        item.currentWriteOffAmount = this.isSku(item)
          ? bigDecimal.round(
              bigDecimal.multiply(
                item.purchasePriceTaxInclusive,
                item.currentWriteOffSettleQuantity,
              ),
              2,
              bigDecimal.RoundingModes.HALF_UP,
            )
          : this.distribution
          ? item.writeOff.notWrittenOffTaxInclusive
          : item.invoiceSummation
          ? item.invoiceAmount
          : item.settleAmount;
        item.currentWriteOffTax = this.isSku(item)
          ? bigDecimal.subtract(
              bigDecimal.round(
                bigDecimal.multiply(
                  item.purchasePriceTaxInclusive,
                  item.currentWriteOffSettleQuantity,
                ),
                2,
                bigDecimal.RoundingModes.HALF_UP,
              ),
              bigDecimal.round(
                bigDecimal.multiply(
                  item.purchasePriceTaxExclusive,
                  item.currentWriteOffSettleQuantity,
                ),
                2,
                bigDecimal.RoundingModes.HALF_UP,
              ),
            )
          : item.invoiceTax;
      },

      handleInvoiceAmountChange() {
        this.calSumInvoiceAmount();
      },

      checkInvoiceNo(row) {
        if (!/^[^\u4e00-\u9fa5]{0,50}$/.test(row.invoiceNo)) {
          row.invoiceNo = '';
          return this.$message.error(
            '请输入正确发票号码。【不允许中文/长度50字符以内】',
          );
        }
      },

      checkInvoiceAmountChange(row) {
        if (!/^-?\d+(\.\d{0,2})?$/.test(row.invoiceAmount)) {
          row.invoiceAmount = '';
          return this.$message.error('请输入正确发票金额');
        }
        this.calSumInvoiceAmount();
      },

      checkInvoiceAmountNotTaxedChange(row) {
        if (!/^-?\d+(\.\d{0,2})?$/.test(row.invoiceAmountNotTaxed)) {
          console.log('请输入正确发票未税额');

          row.invoiceAmountNotTaxed = '';
          return this.$message.error('请输入正确发票未税额');
        }
      },

      checkInvoiceTaxChange(row) {
        if (!/^-?\d+(\.\d{0,2})?$/.test(row.invoiceTax)) {
          row.invoiceTax = '';
          return this.$message.error('请输入正确税额');
        }
      },

      writeOff(row) {
        this.saveForm.selectData = [
          {
            ...row,
            settleQuantity: row.unwrittenOffQuantity,
            // settleAmount: row.writeOff.notWrittenOffTaxInclusive,
          },
        ];
        setTimeout(() => {
          this.visible = true;
        }, 0);
      },

      isSku(row) {
        return !row.expenseItem && row.goodsBarcode !== '开票合计';
      },

      addRow() {
        this.invoiceData.push({
          invoiceNo: '',
          invoiceAmount: '',
          invoiceTax: '',
          invoiceDate: new Date(),
          invoiceDueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          invoiceType: 'VAT_SPECIAL',
        });
      },
      removeRow(index) {
        if (this.invoiceData.length > 1) {
          this.invoiceData.splice(index, 1);
        }
        this.calSumInvoiceAmount();
      },

      calSumInvoiceAmount() {
        this.sumInvoiceAmount = this.invoiceData.reduce((sum, next) => {
          return add(sum, +next?.invoiceAmount);
        }, 0);
      },

      calMap() {
        // 使用 reduce 函数对原始数据进行聚合，并返回聚合后的 Map 对象
        return Object.values(this.list).reduce((acc, value) => {
          let billNo = value.billNo;
          let groupKey = billNo.toString();

          if (!acc.has(groupKey)) {
            acc.set(groupKey, {
              billNo: billNo,
              writeOffInvoice: 0,
              writeOffNoInvoice: 0,
              notWrittenOffInvoice: 0,
              notWrittenOffNoInvoice: 0,
              chooseWriteOffInvoice: 0,
              chooseWriteOffNoInvoice: 0,
            });
          }

          let group = acc.get(groupKey);

          group.writeOffInvoice = add(
            group.writeOffInvoice,
            value.select
              ? value.invoiceSummation
                ? this.distribution
                  ? value?.writeOff?.writeOffTaxInclusive
                  : 0
                : 0
              : 0,
          );

          group.writeOffNoInvoice = add(
            group.writeOffNoInvoice,
            value.select
              ? value.invoiceSummation
                ? 0
                : this.distribution
                ? value?.writeOff?.writeOffTaxInclusive
                : 0
              : 0,
          );

          group.notWrittenOffInvoice = add(
            group.notWrittenOffInvoice,
            value.invoiceSummation
              ? this.distribution
                ? value?.writeOff?.notWrittenOffTaxInclusive
                : value?.currentWriteOffAmount
              : 0,
          );
          group.notWrittenOffNoInvoice = add(
            group.notWrittenOffNoInvoice,
            value.invoiceSummation
              ? 0
              : this.distribution
              ? value?.writeOff?.notWrittenOffTaxInclusive
              : value?.currentWriteOffAmount,
          );

          group.chooseWriteOffInvoice = add(
            group.chooseWriteOffInvoice,
            value.select
              ? value.invoiceSummation
                ? value.currentWriteOffAmount
                : 0
              : 0,
          );

          group.chooseWriteOffNoInvoice = add(
            group.chooseWriteOffNoInvoice,
            value.select
              ? value.invoiceSummation
                ? 0
                : value.currentWriteOffAmount
              : 0,
          );
          return acc;
        }, new Map());
      },

      async submit() {
        if (this.invoiceData.length === 0) {
          return this.$message.error('请填写发票信息');
        }
        for (let i = 0; i < this.invoiceData.length; i++) {
          let item = this.invoiceData[i];

          this.checkInvoiceNo(item);
          this.checkInvoiceAmountChange(item);
          this.checkInvoiceAmountNotTaxedChange(item);
          this.checkInvoiceTaxChange(item);

          console.log(
            parseFloat(item.invoiceAmount),
            parseFloat(add(item.invoiceAmountNotTaxed, item.invoiceTax)),
          );
          if (
            parseFloat(item.invoiceAmount) !==
            parseFloat(add(item.invoiceAmountNotTaxed, item.invoiceTax))
          ) {
            return this.$message.error(
              '发票' + item.invoiceNo + '含税金额 不等于 未税金额 + 税额',
            );
          }

          if (item.invoiceNo == null || item.invoiceNo === '') {
            return this.$message.error('发票号不能为空');
          }
          if (item.invoiceAmount == null || item.invoiceAmount === '') {
            return this.$message.error('发票金额不能为空');
          }
          if (item.invoiceTax == null || item.invoiceTax === '') {
            return this.$message.error('发票税额不能为空');
          }
          if (item.invoiceDate == null || item.invoiceDate === '') {
            return this.$message.error('发票日期不能为空');
          }
          if (item.invoiceDueDate == null || item.invoiceDueDate === '') {
            return this.$message.error('发票截止日期不能为空');
          }
        }
        if (this.selectbatchData.length === 0) {
          return this.$message.error('请选择核销明细');
        }
        this.calSumInvoiceAmount();

        let sumInvoiceAmount1 = this.sumInvoiceAmount;

        let sumInvoiceAmount2 = this.selectbatchData
          .filter(item => item.invoiceSummation)
          .reduce((sum, next) => {
            return add(sum, next.currentWriteOffAmount);
          }, 0);

        if (parseFloat(sumInvoiceAmount1) !== parseFloat(sumInvoiceAmount2)) {
          return this.$message.error(
            sumInvoiceAmount1 +
              '/' +
              sumInvoiceAmount2 +
              '发票金额不一致，无法核销',
          );
        }

        let isValid = true;

        if (this.distribution) {
          await this.$refs.form.validate(async valid => {
            isValid = valid;
          });
        }

        if (isValid) {
          const body = {
            corporateMethod: this.corporateMethod,
            invoiceList: this.invoiceData,

            productList: this.selectbatchData.map(item => ({
              billSerialNo: item.billNo,
              productCode: item.goodsBarcode,
              productName: item.goodsTitle,
              purchasePriceTaxInclusive: item.purchasePriceTaxInclusive,
              writeOffQuantity: item.currentWriteOffSettleQuantity,
              amount: item.currentWriteOffAmount,
              taxAmount: item.currentWriteOffTax,
              isInvoice: item.invoiceSummation,
              isExpense: item.expenseItem,
              rate: item.rate,
            })),

            writeOffInvoice: this.writeOffInvoice,
            writeOffNoInvoice: this.writeOffNoInvoice,
            notWrittenOffInvoice: this.notWrittenOffInvoice,
            notWrittenOffNoInvoice: this.notWrittenOffNoInvoice,
            chooseWriteOffInvoice: this.chooseWriteOffInvoice,
            chooseWriteOffNoInvoice: this.chooseWriteOffNoInvoice,
            chooseWriteOffInvoiceTax: this.chooseWriteOffInvoiceTax,
            sumInvoiceAmount: this.sumInvoiceAmount,
            billWriteOffList: Array.from(this.calMap().values()),
          };

          try {
            this.saving = true;
            const res = await settleOrderItemWriteOff(body);
            this.visible = false;
            this.showDialog = false;
            this.$message.success('操作成功');
            this.$emit('confirm');
            this.invoiceData = [];
            this.sumInvoiceAmount = 0;
            this.saving = false;
          } catch (e) {
            this.saving = false;
          }
        }
      },

      async getList() {
        this.options.loading = true;
        const res = await detailList(this.billNoList);
        this.options.loading = false;
        if (this.invoiceData.length === 0) {
          this.addRow();
        }

        if (res) {
          // this.corporateMethod = res.corporateMethod;
          this.options.mutiSelect = this.distribution;

          this.currency = res.settlementCurrency;
          this.resList =
            res && res.settlementDetailList
              ? res.settlementDetailList
                  .filter(item => item.goodsBarcode !== '开票合计')
                  .map((item, index) => ({
                    ...item,
                    index: index, // 将索引添加到对象中
                    select: false,
                    writeOff: item.writeOff ? item.writeOff : {},
                    currentWriteOffSettleQuantity: this.distribution
                      ? item.settleQuantity - item.writeOff?.writtenOffQuantity
                      : item.settleQuantity,
                    // item.settleQuantity - this.distribution
                    //   ? item.writeOff?.writtenOffQuantity
                    //   : 0,
                    settleCurrency: res.settlementCurrency,
                    invoiceSummationName: item.invoiceSummation ? '是' : '否',
                  }))
              : [];
        }

        this.list = this.resList
          .filter(item => item.needWrittenOff)
          .map(item => ({
            ...item,
          }));

        this.list.forEach(item => {
          this.calWriteOff(item);
        });

        if (!this.distribution) {
          this.handleSelectionChange(this.list);
        }
      },

      getColumns() {
        return [
          {
            prop: 'billNo',
            label: '账单号',
          },
          {
            prop: 'goodsBarcode',
            label: '产品编码',
          },
          {
            prop: 'goodsTitle',
            label: '产品名称',
          },
          {
            prop: 'invoiceSummationName',
            label: '是否开票',
          },
          {
            prop: 'writeOff.writeOffTaxInclusive',
            label: '已核销金额',
            hide: !this.distribution,
          },
          {
            prop: 'writeOff.notWrittenOffTaxInclusive',
            label: '未核销金额',
            hide: !this.distribution,
          },
          {
            prop: 'settleAmount',
            label: '结算金额（含税）',
          },
          {
            prop: 'invoiceAmount',
            label: '开票金额（含税）',
          },
          {
            prop: 'rate',
            label: '税率',
          },
          {
            prop: 'invoiceTax',
            label: '税额',
          },
          {
            prop: 'settleCurrency',
            label: '结算币种',
            hide: !this.distribution,
          },

          {
            prop: 'currentWriteOffAmount',
            label: '当前核销金额',
          },
          {
            prop: 'currentWriteOffTax',
            label: '当前核销税额',
          },
          {
            prop: 'currentWriteOffSettleQuantity',
            label: '当前核销数量',
            scopedSlots: { customRender: 'currentWriteOffSettleQuantity' },
          },
          {
            prop: 'writeOff.writtenOffQuantity',
            label: '已核销数量',
            hide: !this.distribution,
          },
          {
            prop: 'settleQuantity',
            label: '结算数量',
          },
          {
            prop: 'settlePriceTaxInclusive',
            label: '结算单价（含税）',
          },
          {
            prop: 'settlePriceTaxExclusive',
            label: '结算单价（未税）',
          },
          {
            prop: 'purchasePriceTaxInclusive',
            label: '采购单价（含税）',
          },
          {
            prop: 'purchasePriceTaxExclusive',
            label: '采购单价（未税）',
          },
          {
            prop: 'receivedQuantity',
            label: '到货数量',
            hide: !this.distribution,
          },
          {
            prop: 'goodQuantity',
            label: '良品数量',
            // hide: this.corporateMethod === '2',
          },
          {
            prop: 'returnQuantity',
            label: '退供数量',
            // hide: this.corporateMethod === '2',
          },
        ];
      },
    },
  };
</script>
<style lang="scss" scoped>
  .applicationPaymentModal {
    .tip {
      margin-left: 20px;
      font-size: 18px;
      color: #606266;
    }

    .form {
      padding: 10px 10px 0;
      border: 1px solid #dcdfe6;
    }
  }
</style>
