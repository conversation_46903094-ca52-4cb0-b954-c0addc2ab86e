<template>
  <div>
    <el-dialog
      class="applicationPaymentModal"
      width="1200px"
      title="请款申请"
      :visible.sync="showDialog"
    >
      <el-form inline class="form" :model="searchParams">
        <el-form-item label="账单生成时间:">
          <el-date-picker
            v-model="createTime"
            type="daterange"
            :clearable="false"
            range-separator="至"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="供应商名称:">
          <el-select
            v-model="searchParams.supplierCodeList"
            filterable
            multiple
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in supplyDict"
              :key="item.dictValue"
              :label="item.dictDesc"
              :value="item.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="采购主体:">
          <el-select
            v-model="searchParams.companyCodeList"
            filterable
            multiple
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in subjectDict"
              :key="item.dictValue"
              :label="item.dictDesc"
              :value="item.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="付款截止时间:">
          <el-date-picker
            v-model="deadline"
            type="daterange"
            :clearable="false"
            range-separator="至"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="业务类型:">
          <el-select
            v-model="searchParams.bizLine"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in bizLineId"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="履约方式:">
          <el-select
            v-model="searchParams.performanceMethod"
            clearable
            placeholder="请选择"
          >
            <el-option label="自履约" value="1"></el-option>
            <el-option label="非自履约" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账单状态:">
          <el-select
            v-model="searchParams.billStatus"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in billStatus"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="合作方式:">
          <el-select
            v-model="searchParams.cooperationMethod"
            clearable
            placeholder="请选择"
          >
            <el-option label="经销" value="1"></el-option>
            <el-option label="寄售" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="0金额账单:">
          <el-checkbox v-model="searchParams.zeroBill">展示</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="amount">
        <span>请款总金额：</span>
        <span>{{ totalAmount }}</span>
        <span class="tip">选择需要请款的账单数据</span>
      </div>
      <dynamictable
        :data-source="list"
        :columns="getColumns(1)"
        :options="options"
        style="height: 500px; overflow: auto"
        @selection-change="handleSelectionChange"
      ></dynamictable>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showDialog = false">取 消</el-button>
        <el-button type="primary" :loading="submitting" @click="preview">
          提交审批
        </el-button>
      </div>
    </el-dialog>
    <el-dialog width="1200px" title="请款汇总" :visible.sync="visible">
      <div v-if="unselectedNegativeBillList.length" class="warning-tip">
        <i class="el-icon-warning"></i>
        存在应收账单未选择
      </div>
      <div
        v-if="unselectedNegativeBillList.length"
        style="margin-bottom: 20px"
        height="300px"
        overflow-y="auto"
      >
        <dynamictable
          :data-source="unselectedNegativeBillList"
          :columns="getUnselectedColumns()"
          :options="{ ...options, mutiSelect: false }"
          style="height: 200px; overflow: auto"
        ></dynamictable>
      </div>
      <el-divider
        v-if="unselectedNegativeBillList.length"
        style="margin: 10px 0"
      />
      <div class="warning-tip success">
        <i class="el-icon-success"></i>
        已选择
      </div>
      <dynamictable
        :data-source="selectData"
        :columns="getColumns(2)"
        :options="{ ...options, mutiSelect: false }"
        style="height: 200px; overflow: auto"
      ></dynamictable>
      <el-divider style="margin: 10px 0" />
      <div class="remark-section">
        <el-input
          v-model="remark"
          type="textarea"
          :maxlength="500"
          show-word-limit
          placeholder="请输入备注信息（最多500字）"
          :rows="3"
        ></el-input>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
        <el-button type="primary" :loading="saving" @click="submit">
          确 认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    getBillRequisition,
    createRequisition,
    previewRequisition,
  } from '@/api/billManagement';
  import { parseTime } from '@/utils';
  import { keepTwoDecimalFull, add } from '@/utils/math';
  import { size } from 'lodash';

  export default {
    components: {
      dynamictable,
    },
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },

      supplyDict: {
        type: Array,
        default: _ => [],
      },

      subjectDict: {
        type: Array,
        default: _ => [],
      },
      bizLineId: {
        type: Array,
        default: _ => [],
      },
      billStatus: {
        type: Array,
        default: _ => [],
      },

      record: {
        type: Object,
        default: null,
      },
    },
    data() {
      return {
        visible: false,
        createTime: '',
        deadline: '',
        remark: '',
        unselectedBillsTip: '',
        unselectedNegativeBillList: [],
        searchParams: {
          // billNo: '', // 账单号
          // billStatus: '', // 账单状态
          // businessType: '', // 业务线
          bizLine: '',
          companyCodeList: [], // 采购主体列表，多个使用,号分割
          createBeginDate: '', // 账单生成开始日期
          createEndDate: '', // 账单生成结束日期
          expireBeginDate: '', // 付款截止开始日期
          expireEndDate: '', // 付款截止结束日期
          // settlementCurrency: '', // 结算币种
          supplierCodeList: [], // 供应商列表，多个使用,号分割
          zeroBill: false, //是否展示0元单
        },
        list: [],
        options: {
          loading: false,
          border: true,
          mutiSelect: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 200,
          total: null,
        },
        selectData: [], // 选中的数据
        submitting: false,
        saving: false,
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },

      totalAmount() {
        let total = this.selectData.reduce((sum, next) => {
          // 丢失精度
          return add(+sum, +next.standardAmount);
        }, 0);

        return keepTwoDecimalFull(total);
      },
    },
    watch: {
      show(value) {
        if (value) {
          this.selectData = [];
          this.list = [];

          this.getList();
        }
      },
    },
    methods: {
      onSearch() {
        this.getList();
      },

      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.createTime = '';
        this.deadline = '';
        // this.getList();
      },

      // 预览
      async preview() {
        // this.$alert('<p>不同业务线不能一起请款</p>', '提交失败', {
        //   dangerouslyUseHTMLString: true,
        // });
        // return;

        let billTypeKeyList = this.selectData
          .map(_ => _.billTypeKey === '106' && _.billNo)
          .filter(i => !!i);
        console.log(billTypeKeyList, 'billTypeKeyList');
        if (billTypeKeyList.length) {
          this.$confirm(
            `本次请款包含系统生成尾款数据，账单号${billTypeKeyList}，请确认是否合并请款?`,
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            },
          )
            .then(() => {
              this.showPreviewRequisition();
            })
            .catch(() => {});
        } else {
          this.showPreviewRequisition();
        }
      },
      async showPreviewRequisition() {
        let billList = this.selectData.map(_ => _.billNo);
        this.submitting = true;
        this.unselectedBillsTip = '';
        this.unselectedNegativeBillList = [];

        try {
          const res = await previewRequisition({ billNoList: billList });
          this.submitting = false;
          this.visible = true;
          // 如果接口返回了未选中的应收账单信息，显示提示
          if (
            res &&
            res.unselectedNegativeBillList &&
            res.unselectedNegativeBillList.length > 0
          ) {
            this.unselectedNegativeBillList = res.unselectedNegativeBillList;
          }
        } catch (e) {
          this.submitting = false;
        }
      },

      //
      async submit() {
        let billList = this.selectData.map(_ => _.billNo);
        this.saving = true;

        try {
          const res = await createRequisition({
            billNoList: billList,
            remark: this.remark,
          });
          this.saving = false;
          this.visible = false;
          this.showDialog = false;
          this.$message.success('操作成功');
          this.$emit('confirm');
        } catch (e) {
          this.saving = false;
        }
      },

      handleSelectionChange(e) {
        this.selectData = [...e];
      },

      getParams() {
        this.searchParams.createBeginDate = this.createTime
          ? parseTime(this.createTime[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.createEndDate = this.createTime
          ? parseTime(this.createTime[1], '{y}-{m}-{d}')
          : '';
        this.searchParams.expireBeginDate = this.deadline
          ? parseTime(this.deadline[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.expireEndDate = this.deadline
          ? parseTime(this.deadline[1], '{y}-{m}-{d}')
          : '';
        const params = {
          ...this.searchParams,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };

        params.companyCodeList = params.companyCodeList.join(',');
        params.supplierCodeList = params.supplierCodeList.join(',');
        return params;
      },

      async getList() {
        const params = this.getParams();
        this.options.loading = true;

        const res = await getBillRequisition(params);
        if (res) {
          this.options.loading = false;
          this.list = res && res.records ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        } else {
          this.options.loading = false;
        }
      },

      getColumns(type) {
        const columns1 = [
          {
            prop: 'billPeriod',
            label: '账期',
          },
          {
            prop: 'billNo',
            label: '对账单号',
            width: '120px',
            render: ({ billNo, billTypeKey }) => (
              <div>
                <div>{billNo}</div>
                {billTypeKey === '106' ? (
                  <el-tag type="warning" effect="dark" class="contract-tag">
                    尾款结算
                  </el-tag>
                ) : (
                  ''
                )}
              </div>
            ),
          },
          {
            prop: 'companyName',
            label: '采购主体',
          },
          {
            prop: 'supplierName',
            label: '供应商名称',
          },
          {
            prop: 'bizLine',
            label: '业务类型',
          },
          {
            prop: 'cooperationWay',
            label: '合作方式',
          },
          {
            prop: 'performanceMethod',
            label: '履约方式',
            render: ({ performanceMethod }) => (
              <span>
                {performanceMethod == '1'
                  ? '自履约'
                  : performanceMethod == '2'
                  ? '非自履约'
                  : ''}
              </span>
            ),
          },
          {
            prop: 'billType',
            label: '结算方式',
          },
          {
            prop: 'billStatus',
            label: '账单状态',
          },
          {
            prop: 'settlementCurrency',
            label: '结算币种',
          },
          {
            prop: 'settlementAmount',
            label: '结算金额（含税）',
          },
          {
            prop: 'settlementTax',
            label: '税额',
          },
          {
            prop: 'settlementTaxExclusive',
            label: '未税金额',
          },
          {
            prop: 'standardCurrency',
            label: '本位币种',
          },
          {
            prop: 'standardAmount',
            label: '本位金额（含税）',
          },
          {
            prop: 'paymentExpireTime',
            label: '付款截止时间',
          },
          {
            prop: 'createTime',
            label: '账单生成时间',
          },
        ];
        const columns2 = [
          {
            prop: 'companyName',
            label: '采购主体',
          },
          {
            prop: 'supplierName',
            label: '供应商名称',
          },
          {
            prop: 'settlementCurrency',
            label: '结算币种',
          },
          {
            prop: 'settlementAmount',
            label: '结算金额（含税）',
          },
          {
            prop: 'standardCurrency',
            label: '本位币种',
          },
          {
            prop: 'standardAmount',
            label: '本位金额（含税）',
          },
        ];
        return type === 1 ? columns1 : columns2;
      },
      getUnselectedColumns() {
        return [
          {
            prop: 'companyName',
            label: '采购主体',
          },
          {
            prop: 'supplierName',
            label: '供应商名称',
          },
          {
            prop: 'billNo',
            label: '账单号',
          },
          {
            prop: 'billType',
            label: '账单类型',
          },
          {
            prop: 'billPeriod',
            label: '账单日期',
          },
          {
            prop: 'status',
            label: '状态',
          },
          {
            prop: 'settlementCurrency',
            label: '币种',
          },
          {
            prop: 'settlementAmount',
            label: '含税金额',
          },
        ];
      },
    },
  };
</script>
<style lang="scss" scoped>
  .applicationPaymentModal {
    .tip {
      margin-left: 20px;
      font-size: 18px;
      color: #606266;
    }
    .amount {
      margin: 10px 0;
    }
    .form {
      padding: 10px 10px 0;
      border: 1px solid #dcdfe6;
    }
    .remark-section {
      margin: 20px 0;
      padding: 0 20px;
    }
  }

  .warning-tip {
    color: #e6a23c;
    margin: 0 0 20px 0;
    padding: 10px 20px;
    background-color: #fdf6ec;
    border-radius: 4px;
    display: flex;
    align-items: center;
    i {
      margin-right: 8px;
      font-size: 16px;
    }
    &.success {
      color: #67c23a;
      background-color: #f0f9eb;
    }
  }
</style>
