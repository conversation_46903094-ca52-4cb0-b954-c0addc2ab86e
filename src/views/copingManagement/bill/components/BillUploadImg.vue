<template>
  <div>
    <el-upload
      ref="upload"
      class="upload"
      :accept="accept"
      :name="fileName"
      :action="uploadUrl"
      :data="uploadData"
      :headers="headers"
      :list-type="listType"
      :file-list="initFileList"
      :limit="max"
      :multiple="multiple"
      :before-upload="beforeAvatarUpload"
      :on-preview="handlePictureCardPreview"
      :on-success="handleSuccess"
      :on-remove="handleRemove"
      :on-exceed="handleExceed"
    >
      <i v-if="listType === 'picture-card'" class="el-icon-plus"></i>
      <el-button v-else size="small" type="primary">{{ btnText }}</el-button>
    </el-upload>
    <el-dialog
      v-if="listType == 'picture-card'"
      :visible.sync="dialogVisible"
      append-to-body
    >
      <img v-if="!isPDF" width="100%" :src="dialogImageUrl" alt />
      <iframe v-else width="100%" height="600px" :src="dialogImageUrl"></iframe>
    </el-dialog>
  </div>
</template>
<script>
  import { appCode, getCookie } from '@/utils/auth';

  const uploadUrl =
    'https://' + window.ACCESS_HOSTS.apiHost + '/api/abmio/api/v1.0/upload';
  export default {
    name: 'UploadImg',
    props: {
      typeFile: {
        type: Array,
        default: () => {
          return [
            'image/png',
            'image/gif',
            'image/jpg',
            'image/jpeg',
            'application/pdf',
          ];
        },
      },
      // 图片限制大小
      size: {
        type: Number,
        default: 20,
      },
      // 图片限制大小
      multiple: {
        type: Boolean,
        default: true,
      },
      accept: {
        type: String,
        default: '',
      },
      fileName: {
        type: String,
        default: '',
      },
      listType: {
        type: String,
        default: 'picture-card',
      },
      btnText: {
        type: String,
        default: '上传图片',
      },
      max: {
        type: Number,
        default: 5,
      },
      initFileList: {
        type: Array,
        default: () => [],
      },
      uploadData: {
        type: Object,
        default: () => {
          return {
            fileName: name,
            appId: 'abmau',
            folder: 'settle-bill',
            timeStamp: Date.now(),
          };
        },
      },
    },
    data() {
      return {
        uploadUrl,
        dialogImageUrl: '',
        dialogVisible: false,
        isPDF: false,
        headers: {
          token: getCookie(),
          appCode: appCode(),
        },
      };
    },
    methods: {
      clearFiles() {
        this.$refs.upload.clearFiles();
      },
      handleRemove(file, fileList) {
        const filesArr = this.change(file, fileList);
        this.$emit(
          'onRemove',
          {
            id: file.response ? file.response.data.id : file.id,
            file_url: file.response ? file.response.data.url : file.url,
            uid: file.uid,
            name: file.name,
          },
          filesArr,
        );
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url;
        console.log(file);
        this.isPDF = file.raw.type === 'application/pdf';
        this.dialogVisible = true;
      },
      beforeAvatarUpload(file) {
        const typeFile = this.typeFile;
        const isFileType = typeFile.length
          ? typeFile.includes(file.type)
          : true;
        const isLt = file.size / 1024 / 1024 < this.size;

        if (!isFileType) {
          this.$message.error(`只能上传 ${typeFile} 格式!`);
        }
        if (!isLt) {
          this.$message.error(`上传大小不能超过 ${this.size}MB!`);
        }
        return isFileType && isLt;
      },
      handleExceed(files, fileList) {
        this.$message.info(
          '最多只能上传' + this.max + '个文件，您可以试试删除后再上传',
        );
      },
      handleSuccess(response, file, fileList) {
        if (!response.success) {
          let uid = file.uid; // 关键作用代码，去除文件列表失败文件
          let idx = this.initFileList.findIndex(item => item.uid === uid); // 关键作用代码，去除文件列表失败文件
          this.initFileList.splice(idx, 1); // 关键作用代码，去除文件列表失败文件
          return this.$message.error('图片上传失败');
        }
        const filesArr = this.change(file, fileList);
        this.$emit(
          'changeImage',
          {
            id: file.response.data.id,
            file_url: file.response.data.url,
            uid: file.uid,
            name: file.name,
          },
          filesArr,
        );
      },
      change(file, fileList) {
        const filesArr = fileList.map(item => {
          if (item.response) {
            return {
              id: item.response.data.id,
              file_url: item.response.data.url,
              uid: item.uid,
              name: item.name,
            };
          }
        });
        return filesArr;
      },
    },
  };
</script>
