<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-08-27 10:02:38
 * @LastEditors: xuxiang
 * @LastEditTime: 2022-09-15 18:06:53
 * @FilePath: /access-fmis-web/src/views/copingManagement/bill/components/pleasePayDetail.vue
 * @Description: 请款详情
-->
<template>
  <div>
    <el-button
      style="font-size: 16px; margin-bottom: 10px"
      type="text"
      @click="handelJump"
    >
      返回
    </el-button>

    <el-tabs v-model="activeName" type="card" @tab-click="handleTab">
      <el-tab-pane label="明细信息" name="1" />
      <el-tab-pane label="日志查询" name="2" />
    </el-tabs>

    <template v-if="activeName == '1'">
      <el-form ref="search" inline :model="search">
        <el-form-item label="付款截止时间:">
          <el-date-picker
            v-model="search.payTime"
            type="daterange"
            :clearable="false"
            range-separator="至"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="供应商名称:">
          <el-select
            v-model="search.supplierCodeList"
            filterable
            multiple
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in supplyDict"
              :key="item.dictValue"
              :label="item.dictDesc"
              :value="item.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="结算方式:">
          <el-select v-model="search.billType" clearable placeholder="请选择">
            <el-option
              v-for="item in billTypeList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="结算币种:">
          <el-select
            v-model="search.settlementCurrency"
            clearable
            filterable
            placeholder="请选择"
          >
            <el-option
              v-for="item in currency"
              :key="item.key"
              :label="item.key"
              :value="item.key"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="单据状态:">
          <el-select v-model="search.billStatus" clearable placeholder="请选择">
            <!-- <el-option
              v-for="item in billStatus"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            ></el-option> -->
            <template v-for="item in billStatus">
              <el-option
                v-if="![1, 2, 3].includes(item.key)"
                :key="item.key"
                :label="item.value"
                :value="item.key"
              ></el-option>
            </template>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>

      <dynamictable
        :data-source="list"
        :columns="getColumns()"
        :options="options"
        :fetch="getList"
        :pagination="pagination"
      ></dynamictable>
    </template>

    <template v-else>
      <dynamictable
        :data-source="logList"
        :columns="getColumns()"
        :options="options"
        :fetch="getList"
        :pagination="pagination"
      ></dynamictable>
    </template>

    <settlementStatementModal
      v-model="showSettleModal"
      :modal-type="1"
      :supplier-info="supplierSettleInfo"
    ></settlementStatementModal>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import settlementStatementModal from './settlementStatementModal';

  import {
    getRequisitionDetail,
    getSupplier,
    getSelector,
    getDicBill,
  } from '@/api/billManagement';
  import {
    getSecondPartInformationById,
    querySettleType,
  } from '@/api/copingManagement';

  import { parseTime } from '@/utils';

  export default {
    components: { dynamictable, settlementStatementModal },

    props: {
      detailType: {
        type: String,
        default: '',
      },
      currentRow: {
        type: Object,
        default: () => {},
      },
    },

    data() {
      return {
        activeName: '1',
        search: {
          billNo: '', //账单号
          billStatus: '', //账单状态
          payTime: '',
          expireBeginDate: '', //付款截止开始日期
          expireEndDate: '', //付款截止结束日期
          billType: '', //结算方式
          settlementCurrency: '', //结算币种
          supplierCodeList: [], // 供应商列表，多个使用,号分割
        },
        supplyDict: [], // 供应商
        billTypeList: [], // 支付方式
        billStatus: [], // 账单状态
        currency: [], // 结算币种
        list: [],
        options: {
          loading: false,
          border: true,
          // showSummary: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        logList: [], // 日志列表
        showSettleModal: false,
        supplierSettleInfo: {},
      };
    },

    created() {
      this.activeName = this.detailType;
      if (this.currentRow) {
        this.search.requisitionNo = this.currentRow.requisitionNo;
        this.search.settlementCurrency = this.currentRow.settlementCurrency;
      }

      this.getSupplier();
      this.getSelector();
      this.getDicBill();
      this.getList();
    },

    methods: {
      async getDicBill() {
        let res = await getDicBill();
        if (res) {
          this.billTypeList = res.billType;
        }
      },

      handelJump() {
        this.$emit('goBack');
      },

      // 切换tab
      handleTab() {},

      // 供应商结算信息
      async showSettle(row) {
        let res = await getSecondPartInformationById({
          supplierId: row.supplierCode,
        });

        if (res) {
          this.supplierSettleInfo = {
            secondPartyName: res.accountName,
            secondPartyCardNo: res.cardNo,
            secondPartyBankName: res.bankName,
            secondPartyAddress: res.address,
            secondPartyBankAddress: res.bankAddress,
          };
          this.showSettleModal = true;
        }
      },

      getColumns() {
        const columns = [
          {
            prop: 'billPeriod',
            label: '账期',
          },
          {
            prop: 'billNo',
            label: '账单号',
          },
          // {
          //   prop: 'billNo',
          //   label: '对账单号',
          // },
          {
            prop: 'companyName',
            label: '采购主体',
          },
          {
            prop: 'supplierName',
            label: '供应商名称',
            minWidth: 140,
          },
          {
            prop: 'billType',
            label: '结算方式',
          },
          {
            prop: 'settlementCurrency',
            label: '结算币种',
          },
          {
            prop: 'settlementAmount',
            label: '结算金额（含税）',
          },
          {
            prop: 'standardAmount',
            label: '结算金额_本位币（含税）',
          },
          {
            prop: 'settlementTax',
            label: '税额',
          },
          {
            prop: 'settlementTaxExclusive',
            label: '未税金额',
          },

          {
            prop: 'billStatus',
            label: '账单状态',
          },
          {
            prop: 'paymentExpireTime',
            label: '付款截止时间',
            render: ({ paymentExpireTime }) => (
              <span>
                {paymentExpireTime ? parseTime(paymentExpireTime) : ''}
              </span>
            ),
          },
          {
            prop: 'createTime',
            label: '账单生成时间',
            render: ({ createTime }) => (
              <span>{createTime ? parseTime(createTime) : ''}</span>
            ),
          },
        ];

        const columnsLog = [
          {
            prop: 'accountPeriod',
            label: '操作人',
          },
          {
            prop: 'purchaseSubjectName',
            label: '操作时间',
          },
          {
            prop: 'bizLineName',
            label: '操作内容',
          },
          {
            prop: 'settleCurrency',
            label: '备注',
          },
        ];

        return this.activeName == '1' ? columns : columnsLog;
      },

      // 供应商和主体列表
      async getSupplier() {
        let res = await getSupplier();
        if (res) {
          this.supplyDict = res.supplyDict;
        }
      },

      // 其他下拉列表
      async getSelector() {
        let res = await getSelector();

        if (res) {
          this.billStatus = res.billStatus;
          this.currency = res.currency;
        }
      },

      getParams() {
        this.search.expireBeginDate = this.payTime
          ? parseTime(this.payTime[0], '{y}-{m}-{d}')
          : '';
        this.search.expireEndDate = this.payTime
          ? parseTime(this.payTime[1], '{y}-{m}-{d}')
          : '';

        const params = {
          ...this.search,
          requisitionNo: this.currentRow?.requisitionNo, // 请款单号
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };

        params.supplierCodeList = params.supplierCodeList.join(',');

        let { payTime, ...par } = params;

        return par;
      },

      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await getRequisitionDetail(params);
        if (res) {
          this.options.loading = false;
          this.list = res && res.records ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        }
      },

      onSearch() {
        this.getList();
      },

      onReset() {
        Object.assign(this.$data.search, this.$options.data().search);
        if (this.currentRow)
          this.search.requisitionNo = this.currentRow.requisitionNo;
        // this.getList();
      },

      // 查看日志
      checkLog(row) {},
    },
  };
</script>
