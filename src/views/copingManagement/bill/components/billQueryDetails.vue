<template>
  <div v-loading="loading">
    <div>
      <el-button
        style="font-size: 16px; margin-bottom: 10px"
        type="text"
        @click="handelJump()"
      >
        返回
      </el-button>
    </div>
    <el-tabs v-model="activeName" type="card" @tab-click="handleTab">
      <el-tab-pane v-if="settlementType == 'P'" label="账单明细" name="1" />
      <el-tab-pane v-if="settlementType == 'P'" label="开票明细" name="2" />
      <el-tab-pane label="日志查询" name="3" />
    </el-tabs>
    <el-form v-if="['1', '2'].includes(activeName)" inline>
      <el-row>
        <el-col :span="6">
          <el-form-item label="对账单号:">{{ info.billNo }}</el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="账单生成时间:">
            {{ info.createTime }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="账单状态:">{{ info.billStatus }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="采购主体:">
            {{ info.companyName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="结算币种:">
            {{ info.settlementCurrency }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="供应商名称:">
            {{ info.supplierName }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div v-if="activeName === '2'">
      <el-divider content-position="left">开票明细信息</el-divider>
      <div>
        <h1 style="text-align: center">
          {{ info.supplierName }}{{ info.billPeriod }}开票明细单
        </h1>
        <div>
          供应商名称：

          {{ info.supplierName }}
        </div>
        <div style="margin: 10px 0">采购商名称：{{ info.companyName }}</div>
        <dynamictable
          :data-source="info.settlementDetailList"
          style="margin-bottom: 30px"
          :columns="getDeliveryColumns()"
          :options="options"
        ></dynamictable>
      </div>
      <el-card v-if="record && record.invoiceAttachment" class="box-card">
        <el-form ref="invoiceForm" :model="invoiceForm" label-width="180px">
          <el-row>
            <el-form-item label="请选择发票种类">
              <el-select
                v-model="invoiceForm.invoiceType"
                placeholder="请选择发票种类"
                disabled
                style="width: 300px"
              >
                <el-option label="电子（形式）发票" :value="0"></el-option>
                <el-option label="纸质发票" :value="1"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <template v-for="(item, index) in invoiceForm.files">
                <el-image
                  v-if="!isPDF(item)"
                  :key="index"
                  style="width: 100px; height: 100px; margin-right: 10px"
                  :src="item"
                  :preview-src-list="[item]"
                ></el-image>
                <div
                  v-else
                  :key="index"
                  style="
                    width: 100px;
                    height: 100px;
                    margin-right: 10px;
                    display: inline-block;
                    text-align: center;
                    cursor: pointer;
                  "
                  @click="previewPDF(item)"
                >
                  <i class="el-icon-document" style="font-size: 40px"></i>
                  <div style="font-size: 12px; margin-top: 5px">PDF文件</div>
                </div>
              </template>
            </el-form-item>

            <el-form-item v-if="invoiceForm.invoiceType === 1" label="快递单号">
              <el-input
                v-model="invoiceForm.expressNumber"
                disabled
                style="width: 300px"
              ></el-input>
            </el-form-item>
            <!-- <el-form-item
              v-if="invoiceForm.invoiceType === 1"
              label="收件人信息"
            >
              <el-input
                v-model="invoiceForm.address"
                disabled
                type="textarea"
                :rows="7"
                style="width: 300px"
              ></el-input>
            </el-form-item> -->
          </el-row>
          <el-form-item v-if="record && record.invoiceReview">
            <el-button
              type="primary"
              :loading="btnLoading"
              @click="invoiceReview('0')"
            >
              审核通过
            </el-button>
            <el-button
              type="danger"
              :loading="btnLoading"
              @click="invoiceReview('1')"
            >
              审核驳回
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card
        v-if="
          record && !record.invoiceAttachment && record.bizLine === '品牌采购'
        "
        class="box-card"
      >
        <el-alert
          title="发票图片种类、大小限制；仅可上传1次，请谨慎提交"
          type="warning"
        ></el-alert>

        <el-form ref="invoiceForm" :model="invoiceForm" label-width="180px">
          <el-form-item
            prop="invoiceType"
            :rules="[
              {
                required: true,
                message: '请选择发票种类',
                trigger: 'change',
              },
            ]"
            label="请选择发票种类"
          >
            <el-select
              v-model="invoiceForm.invoiceType"
              placeholder="请选择发票种类"
              style="width: 300px"
            >
              <el-option label="电子（形式）发票" :value="0"></el-option>
              <el-option label="纸质发票" :value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="record"
            prop="invoiceFileUrls"
            :rules="[
              {
                required: true,
                message: '请上传发票',
                trigger: 'change',
              },
            ]"
            label="上传发票"
          >
            <BillUploadImg
              :max="100"
              @changeImage="changeImage"
            ></BillUploadImg>
          </el-form-item>
          <el-form-item v-if="record">
            <el-button
              type="primary"
              :loading="btnLoading"
              @click="handleSubmit"
            >
              确定
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
    <div v-else-if="activeName === '1'">
      <h3 style="margin-bottom: 10px">账单汇总</h3>
      <dynamictable
        :data-source="info.settlementList"
        style="margin-bottom: 30px"
        :columns="getColumns()"
        :options="options"
      >
        <template slot="settlementNo" slot-scope="scope">
          <div
            style="color: blue; cursor: pointer"
            @click="jumpToSettle(scope.row)"
          >
            {{ scope.row.settlementNo }}
          </div>
        </template>

        <template slot="operation" slot-scope="props">
          <ac-permission-button
            v-if="
              props.row.settlementNo &&
              ['100', '101', '102'].includes(record.billTypeKey)
            "
            slot="reference"
            btn-text="查看明细"
            type="text"
            size="small"
            permission-key=""
            @click="viewDetails(props.row)"
          ></ac-permission-button>
        </template>
      </dynamictable>
      <div
        v-if="record && record.reviewerType"
        style="display: flex; justify-content: end"
      >
        <el-button type="primary" :loading="btnLoading" @click="passed">
          审核通过
        </el-button>
        <el-button type="danger" :loading="btnLoading" @click="visible = true">
          审核驳回
        </el-button>
      </div>
    </div>
    <div v-else>
      <dynamictable
        :data-source="logList"
        :columns="getColumns()"
        :options="optionsLog"
        :pagination="pagination"
        :fetch="getLog"
      ></dynamictable>
    </div>
    <settlementStatementModal
      v-model="showModal"
      :modal-type="modalType"
      :supplier-info="info.supplierSettlement || {}"
      :record="poList"
    ></settlementStatementModal>
    <el-dialog
      width="1000px"
      title="请选择驳回的结算单并填写驳回原因"
      :visible.sync="visible"
      @closed="onClose"
    >
      <dynamictable
        :data-source="info.settlementList"
        :columns="getColumns()"
        :options="{ ...options, mutiSelect: true }"
        style="height: 400px; overflow: scroll"
        :check-selectable="handleCheckSelectable"
        @selection-change="handleSelectionChange"
      >
        <template slot="operation" slot-scope="scope">
          <el-input
            v-if="scope.row.settlementNo"
            v-model="scope.row.remark"
            class="line-input"
            style="width: 150px; padding-bottom: 10px"
            placeholder="驳回原因"
          ></el-input>
        </template>
      </dynamictable>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="btnLoading" @click="handleBack">
          确定驳回
        </el-button>
        <el-button type="danger" :loading="btnLoading" @click="visible = false">
          取消驳回
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="返利明细"
      width="200px"
      :visible.sync="rebateDetailsVisible"
      @closed="onRebateDetailsClose"
    >
      <el-table :data="rebateSettlementNos">
        <el-table-column
          property="rebateSettlementNo"
          label="结算单号"
          width="150"
        >
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="jump(scope.row)">
              {{ scope.row.rebateSettlementNo }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import BillUploadImg from '@/views/copingManagement/bill/components/BillUploadImg';
  import {
    billBdApproved,
    billBdRejected,
    billFinanceApproved,
    billFinanceRejected,
    getBillDetail,
    getBillOperateLog,
    getPODetail,
    invoiceApprove,
    invoiceReject,
    invoiceUpload,
  } from '@/api/billManagement';
  import settlementStatementModal from './settlementStatementModal';

  export default {
    components: {
      dynamictable,
      settlementStatementModal,
      BillUploadImg,
    },

    props: {
      billNo: {
        type: String,
        default: null,
      },
      settlementType: {
        type: String,
        default: null,
      },
      billId: {
        type: Number,
        default: null,
      },
      detailTab: {
        type: String,
        default: null,
      },
      record: {
        type: Object,
        default: null,
      },
      corporateMethod: {
        type: Number,
        default: 1,
      },
    },

    data() {
      return {
        loading: false,
        btnLoading: false,
        rebateDetailsVisible: false,
        rebateDetailsList: [],
        rebateSettlementNos: [],
        invoiceForm: {
          files: [],
          invoiceType: '',
          invoiceFileUrls: null,
          expressNumber: '',
          address: '',
        },
        visible: false,
        showModal: false,
        modalType: 1,
        activeName: '1',
        info: {
          supplierSettlement: {},
          settlementList: [],
          settlementDetailList: [],
        }, // 详情信息
        list: [],
        logList: [],
        poList: [],
        selectData: [], // 驳回的数据
        options: {
          loading: false,
          border: true,
        },
        optionsLog: {
          loading: false,
          border: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },
    created() {
      this.activeName = this.detailTab;

      if (['1', '2'].includes(this.activeName)) {
        this.fetch();
      } else {
        this.getLog();
      }
    },
    methods: {
      handleSubmit() {
        this.$refs.invoiceForm.validate(async valid => {
          if (!valid) return;
          this.btnLoading = true;
          try {
            await invoiceUpload({
              ...this.invoiceForm,
              billId: this.billId,
            });
            this.$message.success('发票上传成功');
            this.handelJump();
          } catch (error) {
            this.$message.error('发票上传失败');
          } finally {
            this.btnLoading = false;
          }
        });
      },
      jumpToSettle(row) {
        let path = '';
        if (row.settlementType === '应付') {
          if (row.cooperationWay === '经销') {
            path = '/copingManagement/coping/payableStatementDistribution';
          } else {
            path = '/copingManagement/coping/payableStatementConsignment';
          }
        } else {
          path =
            '/copingManagement/receivableManagement/accountsReceivableSettlement';
        }
        this.$router.push({
          path: path,
          query: {
            settleItemNo: row.settlementNo,
          },
        });
      },
      jump(row) {
        this.$router.push({
          path:
            'copingManagement/receivableManagement/accountsReceivableSettlement',
          query: { settleSerialNo: row.rebateSettlementNo },
        });
      },
      onRebateDetailsClose() {
        this.$nextTick(function () {
          this.rebateSettlementNos = [];
        });
      },
      onClose() {},
      async viewDetails(row) {
        const record = this.record || {};
        const path =
          row.settlementType === '应付'
            ? '/copingManagement/coping/payableStatementDetails'
            : '/copingManagement/receivableManagement/billingDocumentMgt';
        const query = {
          contractNo: row.contractNo,
          settleSerialNo: row.settlementNo,
          poSerialNo: row.originPurchaseDocNumber,
          corporateMethod: record.cooperationWay === '经销' ? 1 : 2,
          performanceMethod: record.performanceMethod,
          createTime: record.createTime,
          purchaseSubjectName: record.companyName,
          supplierSubjectName: record.supplierName,
        };

        this.$router.push({
          path,
          query:
            row.settlementType === '应付'
              ? query
              : { settlementNo: row.settlementNo },
        });
      },
      handleCheckSelectable(row) {
        return row.settlementNo && row.settlementDocType != '调整单';
      },
      changeImage(file, fileList) {
        // 批量上传uoload的时候，获取不到url报错
        setTimeout(() => {
          this.invoiceForm.invoiceFileUrls = fileList.map(
            item => item?.file_url,
          );
        });
      },
      handleSelectionChange(e) {
        this.selectData = [...e];
      },
      checkReason(list) {
        let flag = false;
        list.forEach(element => {
          if (!element.remark) {
            flag = true;
          }
        });
        return flag;
      },
      // 确定驳回
      async handleBack() {
        if (this.selectData.length === 0) {
          this.$message({
            message: '请选择需要驳回的数据',
            type: 'warning',
          });
          return;
        }
        if (this.checkReason(this.selectData)) {
          this.$message({
            message: '驳回原因必填',
            type: 'warning',
          });
          return;
        }
        const reviewerType = this.record.reviewerType;
        const rejected =
          reviewerType === '1' ? billBdRejected : billFinanceRejected;
        this.btnLoading = true;
        try {
          await rejected({
            billId: this.billId,
            records: this.selectData.map(item => ({
              reason: item.remark,
              settlementNo: item.settlementNo,
            })),
          });
          this.btnLoading = false;
          this.$message({
            message: '驳回成功',
            type: 'success',
          });
          this.visible = false;
          this.handelJump();
        } catch (error) {
          this.btnLoading = false;
        }
      },
      async createSubmit(params) {
        this.btnLoading = true;
        const invoiceApis = params.reason ? invoiceReject : invoiceApprove;
        try {
          const res = await invoiceApis(params);
          this.btnLoading = false;
          this.$message({
            message: '操作成功',
            type: 'success',
          });
          this.handelJump();
        } catch (error) {
          this.btnLoading = false;
        }
      },
      // 账单审核
      invoiceReview(val) {
        let params = {
          billId: this.billId,
        };
        if (val === '0') {
          this.createSubmit(params);
          return;
        }

        this.$prompt('请输入驳回原因', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputType: 'textarea',
        }).then(({ value }) => {
          if (!value) {
            this.$message({
              message: '备注不能为空',
              type: 'warning',
            });
            return;
          }
          params.reason = value;
          this.createSubmit(params);
        });
      },
      // 账单审核通过
      async passed() {
        const reviewerType = this.record.reviewerType;
        const approved =
          reviewerType === '1' ? billBdApproved : billFinanceApproved;
        this.btnLoading = true;
        try {
          await approved({ billId: this.record.billId });
          this.btnLoading = false;
          this.$message({
            message: '审核通过',
            type: 'success',
          });
          this.handelJump();
        } catch (error) {
          this.btnLoading = false;
        }
      },
      handleShowModal(type) {
        this.modalType = type;
        this.showModal = true;
      },
      handleTab() {
        if (['1', '2'].includes(this.activeName)) {
          this.fetch();
        } else {
          this.getLog();
        }
      },
      handelJump() {
        this.$emit('goBack');
      },

      // po单明细
      async getPODetail(row) {
        let res = await getPODetail({
          purchaseNo: row.purchaseNo,
          goodsBarcode: row.goodsBarcode,
        });
        this.poList = res;

        this.handleShowModal(2);
      },

      // 查询日志
      async getLog() {
        this.options.loading = true;
        const res = await getBillOperateLog({
          relationId: this.billId,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        });
        if (res) {
          this.logList = res.records;
          this.pagination.total = res.total;
        }
        this.options.loading = false;
      },

      async fetch() {
        this.loading = true;
        const res = await getBillDetail({ billNo: this.billNo });
        if (res) {
          this.info = res;
          this.invoiceForm = res.invoiceAttachment || {
            files: [],
            invoiceType: '',
            invoiceFileUrls: null,
            expressNumber: '',
            address: '',
          };
        }
        this.loading = false;
      },

      getDeliveryColumns() {
        const { billTypeKey, cooperationWay } = this.record;
        const columns = [
          {
            prop: 'goodsBarcode',
            label: '产品编码',
            render: row => <span>{row.goodsBarcode}</span>,
          },
          {
            prop: 'goodsTitle',
            label: '产品名称',
          },
          {
            prop: 'settleCurrency',
            label: '结算币种',
          },
          // {
          //   prop: 'settlePriceTaxInclusive',
          //   label: '结算单价（含税）',
          // },
          // {
          //   prop: 'settlePriceTaxExclusive',
          //   label: '结算单价（未税）',
          // },
          {
            prop: 'purchasePriceTaxInclusive',
            label: '采购单价（含税）',
          },
          {
            prop: 'purchasePriceTaxExclusive',
            label: '采购单价（未税）',
          },

          // {
          //   prop: 'purchaseQuantity',
          //   label: '采购入库数量',
          // },
          {
            prop: 'receivedQuantity',
            label: '入库数量',
          },
          {
            prop: 'goodQuantity',
            label: '良品数量',
          },
          {
            prop: 'returnQuantity',
            label: '采购退库数量',
          },
          {
            prop: 'settleQuantity',
            label: '结算数量',
          },
          {
            prop: 'rebateAmount',
            label: '返利金额',
            render: ({ rebateAmount, rebateSettlementNos = [] }) => (
              <el-link
                type="primary"
                onClick={() => {
                  this.$router.push({
                    path:
                      '/copingManagement/receivableManagement/accountsReceivableSettlement',
                    query: { billNo: this.record.billNo },
                  });
                }}
              >
                {rebateAmount}
              </el-link>
            ),
          },
          // {
          //   prop: 'settleAmount',
          //   label: '产品结算金额',
          // },
          {
            prop: 'invoiceAmount',
            label: '开票金额(含税)',
          },
          {
            prop: 'invoiceAmountTaxExclude',
            label: '开票金额(未税)',
          },
          {
            prop: 'invoiceTax',
            label: '开票税额',
          },
          {
            prop: 'rate',
            label: '税率',
          },
          {
            prop: 'taxAmount',
            label: '税额',
          },
          {
            prop: 'billPeriod',
            label: '账单期间',
          },
          {
            prop: 'remark',
            label: '备注信息',
          },
        ];
        const columns1 = [
          {
            prop: 'goodsBarcode',
            label: '产品编码',
          },
          {
            prop: 'goodsTitle',
            label: '产品名称',
          },
          {
            prop: 'settleCurrency',
            label: '结算币种',
          },
          // {
          //   prop: 'settlePriceTaxInclusive',
          //   label: '结算单价（含税）',
          // },
          // {
          //   prop: 'settlePriceTaxExclusive',
          //   label: '结算单价（未税）',
          // },
          {
            prop: 'purchasePriceTaxInclusive',
            label: '采购单价（含税）',
          },
          {
            prop: 'purchasePriceTaxExclusive',
            label: '采购单价（未税）',
          },

          {
            prop: 'outboundQuantity',
            label: '入库数量',
          },
          {
            prop: 'returnQuantity',
            label: '采购退库数量',
          },
          {
            prop: 'settleQuantity',
            label: '结算数量',
          },
          {
            prop: 'rebateAmount',
            label: '返利金额',
            render: ({ rebateAmount, rebateSettlementNos = [] }) => (
              <el-link
                type="primary"
                onClick={() => {
                  this.$router.push({
                    path:
                      '/copingManagement/receivableManagement/accountsReceivableSettlement',
                    query: { billNo: this.record.billNo },
                  });
                }}
              >
                {rebateAmount}
              </el-link>
            ),
          },
          // {
          //   prop: 'settleAmount',
          //   label: '结算金额',
          // },
          {
            prop: 'invoiceAmount',
            label: '开票金额(含税)',
          },
          {
            prop: 'invoiceAmountTaxExclude',
            label: '开票金额(未税)',
          },
          {
            prop: 'invoiceTax',
            label: '开票税额',
          },
          {
            prop: 'rate',
            label: '税率',
          },
          {
            prop: 'taxAmount',
            label: '税额',
          },
          {
            prop: 'billPeriod',
            label: '账单期间',
          },
          {
            prop: 'remark',
            label: '备注信息',
          },
        ];

        return ['200', '201', '202'].includes(billTypeKey) ? columns1 : columns;
      },
      getColumns() {
        const { corporateMethod } = this;
        const columns = [
          {
            prop: 'settlementNo',
            label: '结算单号',
            width: '160px',
            scopedSlots: { customRender: 'settlementNo' },
          },
          {
            prop: 'settlementDocType',
            label: '单据类型',
          },
          {
            prop: 'originPurchaseDocNumber',
            label: '原始采购单号',
            hide: corporateMethod === 2,
          },
          {
            prop: 'originPurchaseDocRelateDocNumber',
            label: '原始采购单关联单号',
            hide: corporateMethod === 2,
          },
          {
            prop: 'originDocType',
            label: '原始单据类型',
          },
          {
            prop: 'settlementType',
            label: '收付类型',
          },
          {
            prop: 'cooperationWay',
            label: '合作方式',
          },

          {
            prop: 'chargeType',
            label: '结算类型',
          },
          {
            prop: 'settlementCurrency',
            label: '结算币种',
          },
          {
            prop: 'settlementTaxInclusive',
            label: '结算金额（含税）',
          },
          // {
          //   prop: '',
          //   label: '记账币种',
          // },
          // {
          //   prop: '',
          //   label: '记账金额',
          // },
          {
            prop: 'taxAmount',
            label: '税额',
          },
          {
            prop: 'settlementTaxExclusive',
            label: '未税金额',
          },
          {
            prop: 'status',
            label: '结算单状态',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            minWidth: '200px',
            maxWidth: '400px',
            scopedSlots: { customRender: 'operation' },
          },
        ];
        const columns1 = [
          {
            prop: 'operator',
            label: '操作人',
          },
          {
            prop: 'createTime',
            label: '操作时间',
          },
          {
            prop: 'type',
            label: '操作内容',
          },
          {
            prop: 'remark',
            label: '备注',
          },
        ];
        return this.activeName === '1' ? columns : columns1;
      },
      isPDF(url) {
        return url.toLowerCase().endsWith('.pdf');
      },
      previewPDF(url) {
        window.open(url, '_blank');
      },
    },
  };
</script>

<style lang="scss" scoped>
  /deep/ .el-table {
    max-height: 1000px !important;
  }
</style>
