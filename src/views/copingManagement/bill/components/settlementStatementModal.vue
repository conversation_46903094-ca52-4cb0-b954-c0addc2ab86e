<template>
  <el-dialog
    class="pricing-modal"
    width="1000px"
    :title="modalType === 1 ? '供应商结算信息' : 'PO明细'"
    :visible.sync="showDialog"
  >
    <el-form v-if="modalType === 1" label-width="180px">
      <el-form-item label="收款账号名称:">
        {{ supplierInfo.secondPartyName }}
      </el-form-item>
      <el-form-item label="银行账号:">
        {{ supplierInfo.secondPartyCardNo }}
      </el-form-item>
      <el-form-item label="开户银行:">
        {{ supplierInfo.secondPartyBankName }}
      </el-form-item>
      <el-form-item label="公司地址:">
        {{ supplierInfo.secondPartyAddress }}
      </el-form-item>
      <el-form-item label="银行地址:">
        {{ supplierInfo.secondPartyBankAddress }}
      </el-form-item>
    </el-form>
    <dynamictable
      v-else
      :data-source="record"
      :columns="getColumns()"
      :options="options"
    ></dynamictable>
  </el-dialog>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  export default {
    components: {
      dynamictable,
    },
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      supplierInfo: {
        type: Object,
        default: () => {},
      },
      record: {
        // po明细
        type: Array,
        default: () => [],
      },
      modalType: {
        type: Number,
        default: 1,
      },
    },
    data() {
      return {
        options: {
          loading: false,
          border: true,
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {
        if (value) {
        }
      },
    },
    methods: {
      getColumns() {
        return [
          {
            prop: 'purchaseNo',
            label: 'PO单号',
          },
          {
            prop: 'createTime',
            label: '采购时间',
          },
          {
            prop: 'goodsBarcode',
            label: '商品编码',
          },
          {
            prop: 'goodsTitle',
            label: '商品中文名称',
          },
          {
            prop: 'purchaseQuantity',
            label: '采购数量',
          },
          {
            prop: 'purchasePriceTaxInclusive',
            label: '采购单价（含税）',
          },
          {
            prop: 'purchasePrice',
            label: '采购单价（未税）',
          },
          {
            prop: 'purchaseAmountTaxInclusive',
            label: '采购金额（含税）',
          },
          {
            prop: 'purchaseAmountTaxExclusive',
            label: '采购金额（未税）',
          },
          {
            prop: 'rate',
            label: '税率',
          },
          {
            prop: 'taxAmount',
            label: '税额',
          },
          {
            prop: 'remark',
            label: '备注',
          },
        ];
      },
    },
  };
</script>
<style lang="scss">
  .pricing-modal {
    .modal-top {
      padding: 20px;
      margin-bottom: 10px;
      border: 1px solid #dcdfe6;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .el-table .is-group tr:nth-child(2) {
      display: none;
    }
  }
</style>
