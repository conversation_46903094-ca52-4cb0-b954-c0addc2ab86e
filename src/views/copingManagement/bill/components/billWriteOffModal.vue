<!--
 * @Description:
 * @Author: xuxiang
 * @Date: 2022-03-29 11:09:41
 * @LastEditTime: 2022-09-23 10:17:20
 * @LastEditors: xuxiang
 * @Reference:
-->
<template>
  <div>
    <el-dialog
      class="applicationPaymentModal"
      width="1200px"
      :visible.sync="showDialog"
    >
      <el-form inline class="form" :model="searchParams">
        <el-form-item label="产品编码:">
          <el-input
            v-model="searchParams.skuCode"
            clearable
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="batchWriteOff">核销</el-button>
        </el-form-item>
      </el-form>
      <div class="amount">
        <span>已核销金额: {{ writeOffTaxInclusive }}</span>
        <span style="margin-left: 20px">
          <span style="color: red">未核销金额</span>
          : {{ notWrittenOffTaxInclusive }}
          <span style="color: red">已选金额</span>
          : {{ chooseWriteOffTaxInclusive }}
        </span>
      </div>
      <dynamictable
        :data-source="list"
        :columns="getColumns(1)"
        :options="{
          ...options,
          mutiSelect:
            record &&
            record.cooperationWay !== '寄售' &&
            !['100001', '200'].includes(record.billTypeKey),
        }"
        style="height: 800px; overflow: scroll"
        :check-selectable="handleCheckSelectable"
        @selection-change="handleSelectionChange"
      >
        <template slot="operation" slot-scope="props">
          <ac-permission-button
            v-if="props.row.writeOff.needWrittenOff"
            slot="reference"
            btn-text="核销"
            type="text"
            size="small"
            permission-key=""
            @click="writeOff(props.row)"
          ></ac-permission-button>
        </template>
      </dynamictable>
    </el-dialog>
    <el-dialog width="1200px" :visible.sync="visible">
      <el-form
        ref="form"
        :model="saveForm"
        :rules="rulesForm"
        :inline="true"
        label-width="120px"
      >
        <dynamictable
          :data-source="saveForm.selectData"
          :columns="getColumns(2)"
          :options="options"
        >
          <template slot="settleQuantity" slot-scope="scope">
            <el-form-item
              :prop="'selectData.' + scope.$index + '.settleQuantity'"
              :rules="rulesForm.settleQuantity"
            >
              <el-input
                v-model="scope.row.settleQuantity"
                class="line-input"
                style="width: 150px,padding-bottom: 10px"
                placeholder="请输入结算数量"
                @change="handleSettleQuantity(scope.row)"
              ></el-input>
            </el-form-item>
          </template>
          <template slot="invoiceNo" slot-scope="scope">
            <el-form-item
              :prop="'selectData.' + scope.$index + '.invoiceNo'"
              :rules="rulesForm.invoiceNo"
            >
              <el-input
                v-model="scope.row.invoiceNo"
                class="line-input"
                style="width: 150px,padding-bottom: 10px"
                placeholder="请输入发票号"
              ></el-input>
            </el-form-item>
          </template>
          <template slot="invoiceDate" slot-scope="scope">
            <el-form-item
              :prop="'selectData.' + scope.$index + '.invoiceDate'"
              :rules="rulesForm.invoiceDate"
            >
              <el-date-picker
                v-model="scope.row.invoiceDate"
                type="date"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                placeholder="选择日期"
                style="width: 180px"
              ></el-date-picker>
            </el-form-item>
          </template>
          <template slot="invoiceDueDate" slot-scope="scope">
            <el-form-item
              :prop="'selectData.' + scope.$index + '.invoiceDueDate'"
              :rules="rulesForm.invoiceDueDate"
            >
              <el-date-picker
                v-model="scope.row.invoiceDueDate"
                type="date"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                placeholder="选择日期"
                style="width: 180px"
              ></el-date-picker>
            </el-form-item>
          </template>
        </dynamictable>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
        <el-button type="primary" :loading="saving" @click="submit">
          核 销
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import { getBillDetail } from '@/api/billManagement';
  import { settleOrderItemWriteOff } from '@/api/copingManagement';
  import { parseTime, initSearchParams } from '@/utils';
  import { keepTwoDecimalFull } from '@/utils/math';

  export default {
    components: {
      dynamictable,
    },
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },

      supplyDict: {
        type: Array,
        default: _ => [],
      },

      subjectDict: {
        type: Array,
        default: _ => [],
      },

      record: {
        type: Object,
        default: null,
      },
    },
    data() {
      const validValue = (rule, value, callback) => {
        const { selectData } = this.saveForm;
        const index = rule.field?.split('.')[1] || 0;
        // console.log(index, selectData, value, rule, 'selectData');
        const item = selectData.length ? selectData[index] : {};
        // if (!item.unwrittenOffQuantity || item.unwrittenOffQuantity == 0) {
        //   callback(new Error('当前需要核销的数量为0'));
        // } else if (value > item.unwrittenOffQuantity || value < 0) {
        //   callback(
        //     new Error(`结算数量大于等0小于等于${item.unwrittenOffQuantity}`),
        //   );
        // } else {
        //   const reg = /^([1-9]\d*|[0]{1,1})$/;
        //   if (!reg.test(value)) {
        //     callback(new Error('结算数量应为整数'));
        //   } else {
        //     callback();
        //   }
        // }
        if (value > item.unwrittenOffQuantity || value < 0) {
          callback(
            new Error(`结算数量大于等0小于等于${item.unwrittenOffQuantity}`),
          );
        } else {
          const reg = /^([1-9]\d*|[0]{1,1})$/;
          if (!reg.test(value)) {
            callback(new Error('结算数量应为整数'));
          } else {
            callback();
          }
        }
      };

      return {
        visible: false,
        settlementNoList: [], // 结算单号列表
        selectbatchData: [], // 批量选择的数据
        saveForm: {
          selectData: [], // 选中的数据
        },
        rulesForm: {
          settleQuantity: [
            {
              required: true,
              message: '请输入结算数量',
              trigger: 'change',
            },
            { required: true, validator: validValue },
          ],
        },
        error: '',
        searchParams: {
          skuCode: '',
        },
        list: [],
        options: {
          loading: false,
          border: true,
        },
        saving: false,
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
      notWrittenOffTaxInclusive() {
        let total = this.list.reduce((sum, next) => {
          return sum + +next?.writeOff?.notWrittenOffTaxInclusive;
        }, 0);
        return keepTwoDecimalFull(total);
      },
      writeOffTaxInclusive() {
        let total = this.list.reduce((sum, next) => {
          return sum + +next?.writeOff?.writeOffTaxInclusive;
        }, 0);
        return keepTwoDecimalFull(total);
      },
      chooseWriteOffTaxInclusive() {
        let total = this.selectbatchData.reduce((sum, next) => {
          return sum + +next?.writeOff?.notWrittenOffTaxInclusive;
        }, 0);
        return keepTwoDecimalFull(total);
      },
    },
    watch: {
      show(value) {
        if (value) {
          this.saveForm.selectData = [];
          this.list = [];
          this.getList();
        }
      },
    },
    methods: {
      // 禁止选择不符合的数据
      handleCheckSelectable(row) {
        // return row.writeOff.needWrittenOff && row.unwrittenOffQuantity != 0;
        return row.writeOff.needWrittenOff;
      },
      handleSettleQuantity(val = {}) {
        const { selectData } = this.saveForm;
        const data = selectData.map(item => {
          const notWrittenOffTaxInclusive =
            item.settleQuantity * item.purchasePriceTaxInclusive;
          console.log(notWrittenOffTaxInclusive, 'notWrittenOffTaxInclusive');
          return {
            ...item,
            writeOff: {
              ...item.writeOff,
              notWrittenOffTaxInclusive: keepTwoDecimalFull(
                notWrittenOffTaxInclusive,
              ),
            },
          };
        });

        this.saveForm.selectData = data;
      },
      onSearch() {
        this.getList();
      },
      async batchWriteOff() {
        console.log(this.record, 'kkk');
        const { selectbatchData, record } = this;
        if (
          record.cooperationWay === '寄售' ||
          ['100001', '200'].includes(record.billTypeKey)
        ) {
          await settleOrderItemWriteOff({
            billSerialNo: record?.billNo,
            corporateMethod: record?.cooperationWay === '经销' ? 1 : 2,
            settlementNoList: this.settlementNoList,
            productList: [],
          });
          this.showDialog = false;
          this.$message.success('操作成功');
          this.$emit('confirm');
          return;
        }

        if (!selectbatchData.length) {
          this.$message.error('请选择需要核销的数据');
          return;
        }
        this.saveForm.selectData = selectbatchData.map(item => ({
          ...item,
          settleQuantity: item.unwrittenOffQuantity,
        }));
        this.visible = true;
      },

      handleSelectionChange(e = []) {
        this.selectbatchData = e.map(item => ({
          ...item,
          settleQuantity: item.unwrittenOffQuantity,
          // settleAmount: item.writeOff.notWrittenOffTaxInclusive,
        }));
      },

      writeOff(row) {
        this.saveForm.selectData = [
          {
            ...row,
            settleQuantity: row.unwrittenOffQuantity,
            // settleAmount: row.writeOff.notWrittenOffTaxInclusive,
          },
        ];
        setTimeout(() => {
          this.visible = true;
        }, 0);
      },

      async submit() {
        this.$refs.form.validate(async valid => {
          if (valid) {
            const { selectData } = this.saveForm;
            const body = {
              billSerialNo: this.record?.billNo,
              corporateMethod: this.record?.cooperationWay,
              settlementNoList: this.settlementNoList,
              productList: selectData.map(item => ({
                productCode: item.goodsBarcode,
                purchasePriceTaxInclusive: item.purchasePriceTaxInclusive,
                writeOffQuantity: item.settleQuantity,
                invoiceDate: item.invoiceDate,
                invoiceDueDate: item.invoiceDueDate,
                invoiceNo: item.invoiceNo,
              })),
            };

            try {
              const res = await settleOrderItemWriteOff(body);
              this.saving = false;
              this.visible = false;
              this.showDialog = false;
              this.$message.success('操作成功');
              this.$emit('confirm');
            } catch (e) {
              this.saving = false;
            }
          }
        });
      },

      async getList() {
        this.options.loading = true;
        const body = {
          ...this.searchParams,
          billNo: this.record?.billNo,
        };
        initSearchParams;
        const res = await getBillDetail(initSearchParams(body));
        this.options.loading = false;
        if (res) {
          console.log(res.settlementList, 'res.settlementList');
          this.settlementNoList = res.settlementList
            ? res.settlementList
                .map(_ => _.settlementType == '应付' && _.settlementNo)
                .filter(i => !!i)
            : [];
          this.list =
            res && res.settlementDetailList
              ? res.settlementDetailList.map(item => ({
                  ...item,
                  writeOff: item.writeOff ? item.writeOff : {},
                }))
              : [];
        }
      },

      getColumns(type) {
        const billTypeKey = this.record?.billTypeKey;
        const columns1 = [
          {
            prop: 'goodsBarcode',
            label: '产品编码',
          },
          {
            prop: 'goodsTitle',
            label: '产品名称',
          },
          {
            prop: 'settleCurrency',
            label: '结算币种',
          },
          {
            prop: 'settlePriceTaxInclusive',
            label: '结算单价（含税）',
          },
          {
            prop: 'settlePriceTaxExclusive',
            label: '结算单价（未税）',
          },
          {
            prop: 'purchasePriceTaxInclusive',
            label: '采购单价（含税）',
          },
          {
            prop: 'purchasePriceTaxExclusive',
            label: '采购单价（未税）',
          },
          {
            prop: 'purchaseQuantity',
            label: '采购入库数量',
          },
          {
            prop: 'returnQuantity',
            label: '采购退库数量',
            hide: !['200', '201', '202'].includes(billTypeKey),
          },

          {
            prop: 'receivedQuantity',
            label: '到货数量',
            hide: ['200', '201', '202'].includes(billTypeKey),
          },

          {
            prop: 'goodQuantity',
            label: '良品数量',
            hide: ['200', '201', '202'].includes(billTypeKey),
          },
          {
            prop: 'returnQuantity',
            label: '采购退库数量',
            hide: ['200', '201', '202'].includes(billTypeKey),
          },

          {
            prop: 'settleQuantity',
            label: '结算数量',
          },
          {
            prop: 'settleAmount',
            label: '结算金额（含税）',
          },
          {
            prop: 'rate',
            label: '税率',
          },
          {
            prop: 'taxAmount',
            label: '税额',
          },
          {
            prop: 'billPeriod',
            label: '账单期间',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '160',
            scopedSlots: { customRender: 'operation' },
          },
        ];
        const columns2 = [
          {
            prop: 'goodsBarcode',
            label: '产品编码',
          },
          {
            prop: 'goodsTitle',
            label: '产品名称',
          },
          {
            prop: 'settleCurrency',
            label: '结算币种',
          },
          {
            prop: 'settlePriceTaxInclusive',
            label: '结算单价（含税）',
          },
          {
            prop: 'settlePriceTaxExclusive',
            label: '结算单价（未税）',
          },
          {
            prop: 'purchasePriceTaxInclusive',
            label: '采购单价（含税）',
          },
          {
            prop: 'purchasePriceTaxExclusive',
            label: '采购单价（未税）',
          },

          {
            prop: 'purchaseQuantity',
            label: '采购入库数量',
          },
          {
            prop: 'returnQuantity',
            label: '采购退库数量',
            hide: !['200', '201', '202'].includes(billTypeKey),
          },

          {
            prop: 'receivedQuantity',
            label: '到货数量',
            hide: ['200', '201', '202'].includes(billTypeKey),
          },
          {
            prop: 'goodQuantity',
            label: '良品数量',
            hide: ['200', '201', '202'].includes(billTypeKey),
          },
          {
            prop: 'returnQuantity',
            label: '采购退库数量',
            hide: ['200', '201', '202'].includes(billTypeKey),
          },

          {
            prop: 'settleQuantity',
            label: '结算数量',
            width: '200',
            scopedSlots: { customRender: 'settleQuantity' },
          },
          {
            prop: 'writeOff.writtenOffQuantity',
            label: '核销数量',
          },
          {
            prop: 'settleAmount',
            label: '结算金额（含税）',
          },
          {
            prop: 'writeOff.writeOffTaxInclusive',
            label: '已核销金额',
            render: ({ writeOffVO, billTypeKey }) => (
              <span>
                {billTypeKey == '102' ? writeOffVO.writeOffTaxInclusive : 0}
              </span>
            ),
          },
          {
            prop: 'writeOff.notWrittenOffTaxInclusive',
            label: '核销金额',
          },
          {
            prop: 'rate',
            label: '税率',
          },
          {
            prop: 'taxAmount',
            label: '税额',
          },
          {
            prop: 'billPeriod',
            label: '对账单期间',
          },
          {
            prop: 'invoiceNo',
            label: '发票号',
            width: 200,
            scopedSlots: { customRender: 'invoiceNo' },
          },
          {
            prop: 'invoiceDate',
            label: '发票日期',
            width: 200,
            scopedSlots: { customRender: 'invoiceDate' },
          },
          {
            prop: 'invoiceDueDate',
            label: '发票到期日',
            width: 200,
            scopedSlots: { customRender: 'invoiceDueDate' },
          },
        ];
        return type === 1 ? columns1 : columns2;
      },
    },
  };
</script>
<style lang="scss" scoped>
  .applicationPaymentModal {
    .tip {
      margin-left: 20px;
      font-size: 18px;
      color: #606266;
    }
    .amount {
      margin: 10px 0;
    }
    .form {
      padding: 10px 10px 0;
      border: 1px solid #dcdfe6;
    }
  }
</style>
