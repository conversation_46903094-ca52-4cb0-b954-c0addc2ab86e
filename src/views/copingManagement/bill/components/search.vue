<template>
  <div class="searchParams">
    <el-form ref="search" inline :model="searchParams">
      <el-form-item label="请款单生成时间:">
        <el-date-picker
          v-model="searchParams.createDate"
          type="daterange"
          clearable
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="采购主体:">
        <el-select
          v-model="searchParams.companyCodeList"
          filterable
          multiple
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in subjectDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="请款单号:">
        <el-input
          v-model="searchParams.requisitionNo"
          clearable
          placeholder="请输入请款单号"
        />
      </el-form-item>

      <el-form-item label="业务线:">
        <el-select
          v-model="searchParams.bizLine"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in bizLineId"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="付款截止时间:">
        <el-date-picker
          v-model="searchParams.payDate"
          type="daterange"
          clearable
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="单据状态:">
        <el-select
          v-model="searchParams.requisitionStatusCode"
          clearable
          placeholder="请选择"
        >
          <template v-for="item in billStatus">
            <el-option
              v-if="![3].includes(item.key)"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            ></el-option>
          </template>
        </el-select>
      </el-form-item>
      <el-form-item label="结算币种:">
        <el-select
          v-model="searchParams.settlementCurrency"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in currency"
            :key="item.key"
            :label="item.key"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="0金额账单:">
        <el-checkbox v-model="searchParams.zeroBill">展示</el-checkbox>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button type="primary" @click="onExport">导出</el-button>
        <!-- <ac-permission-button
          type="primary"
          btn-text="导出"
          permission-key=""
          @click="onExport"
        ></ac-permission-button> -->
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import { parseTime, initSearchParams } from '@/utils';
  import { getSupplier, getSelector, getDicBill } from '@/api/billManagement';
  import { exportExcel } from '@/api/blob';
  export default {
    props: {
      tab: {
        type: Number,
        default: 1,
      },
      search: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        searchParams: {
          bizLine: '', // 业务线
          companyCodeList: [], // 采购主体列表，多个使用,号分割
          createBeginDate: '', // 请款单生成开始日期
          createEndDate: '', // 请款单生成结束日期
          expireBeginDate: '', // 付款截止开始日期
          expireEndDate: '', // 付款截止结束日期
          requisitionNo: '', // 请款单号
          requisitionStatusCode: '', // 单据状态
          settlementCurrency: '', // 结算币种
          // zeroBill: false, //是否展示0元单
        },
        subjectDict: [], // 签约主体
        supplyDict: [], // 供应商
        billStatus: [], // 账单状态
        currency: [], // 结算币种
        bizLineId: [], // 业务线
      };
    },

    created() {
      const { requisitionNo } = this.$route.query;
      if (requisitionNo) this.searchParams.requisitionNo = requisitionNo;
      this.searchParams = {
        ...this.searchParams,
        ...initSearchParams(this.search),
      };
      this.getDicBill();
      this.getSupplier();
      this.getSelector();
      this.onSearch();
    },

    methods: {
      // 供应商和主体列表
      async getSupplier() {
        let res = await getSupplier();
        if (res) {
          this.supplyDict = res.supplyDict;
          this.subjectDict = res.subjectDict;
        }
      },

      // 其他下拉列表
      async getSelector() {
        let res = await getSelector();

        if (res) {
          // this.billStatus = res.requisitionStatus;
          this.currency = res.currency;
          this.bizLineId = res.bizLineId;
        }
      },
      async getDicBill() {
        let res = await getDicBill();
        if (res) {
          this.billStatus = res.paymentStatus;
        }
      },

      getParams() {
        this.searchParams.createBeginDate = this.searchParams.createDate
          ? parseTime(this.searchParams.createDate[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.createEndDate = this.searchParams.createDate
          ? parseTime(this.searchParams.createDate[1], '{y}-{m}-{d}')
          : '';

        this.searchParams.expireBeginDate = this.searchParams.payDate
          ? parseTime(this.searchParams.payDate[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.expireEndDate = this.searchParams.payDate
          ? parseTime(this.searchParams.payDate[1], '{y}-{m}-{d}')
          : '';

        let { createDate, payDate, ...par } = this.searchParams;

        par.companyCodeList = par.companyCodeList.join(',');

        return par;
      },

      onSearch() {
        this.$emit('onSearch', this.getParams());
      },

      onExport() {
        this.$emit('onExport', this.getParams());
      },

      onReset() {
        this.searchParams = Object.assign(
          {},
          this.$options.data().searchParams,
        );
        // this.$emit('onSearch', this.getParams());
      },
    },
  };
</script>

<style lang="scss" scoped></style>
