<template>
  <el-dialog
    class="upload-voucher-modal"
    width="740px"
    title="收款信息"
    :visible.sync="showDialog"
  >
    <div v-for="(formItem, index) in formList" :key="index">
      <el-form
        :ref="`form_${index}`"
        inline
        :model="formItem"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item prop="companyName" label="收款名称:">
          <!-- <el-select
            v-model="formItem.companyName"
            :disabled="isEdit ? index != formList.length - 1 : !isEdit"
            placeholder="请选择"
            @change="handleChange"
          >
            <el-option
              v-for="item in subjectDict"
              :key="item.dictValue"
              :label="item.dictDesc"
              :value="item.dictValue"
            ></el-option>
          </el-select> -->
          <el-input
            v-model="formItem.companyName"
            :disabled="true"
            clearable
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item prop="bankOpenName" label="收款银行:">
          <el-input
            v-model="formItem.bankOpenName"
            :disabled="true"
            clearable
            placeholder="选择收款名称自动带出"
          />
        </el-form-item>
        <el-form-item prop="bankNo" label="收款账号:">
          <el-input
            v-model.trim="formItem.bankNo"
            :disabled="true"
            clearable
            placeholder="选择收款名称自动带出"
          />
        </el-form-item>
        <el-form-item prop="amount" label="打款金额:">
          <el-input
            v-model="formItem.amount"
            :disabled="isEdit ? index != formList.length - 1 : !isEdit"
            clearable
            placeholder="请输入金额"
          />
        </el-form-item>
        <el-form-item prop="date" label="打款日期:">
          <el-date-picker
            v-model="formItem.date"
            :disabled="isEdit ? index != formList.length - 1 : !isEdit"
            clearable
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择"
          ></el-date-picker>
        </el-form-item>
        <el-form-item prop="otherBankName" label="对方户名:">
          <el-input
            v-model="formItem.otherBankName"
            :disabled="isEdit ? index != formList.length - 1 : !isEdit"
            clearable
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item prop="otherBankOpenName" label="对方银行:">
          <el-input
            v-model="formItem.otherBankOpenName"
            :disabled="isEdit ? index != formList.length - 1 : !isEdit"
            clearable
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item prop="otherBankNo" label="对方账号:">
          <el-input
            v-model.trim="formItem.otherBankNo"
            :disabled="isEdit ? index != formList.length - 1 : !isEdit"
            clearable
            placeholder="请输入"
          />
        </el-form-item>
        <div>
          <el-form-item
            :ref="`voucher_${index}`"
            required
            :error="index === formList.length - 1 ? errorTip : ''"
            label="上传凭证:"
          >
            <uploadImg
              v-if="isEdit ? index != formList.length - 1 : !isEdit"
              :ref="`uploadImg_${index}`"
              :disabled="isEdit ? index != formList.length - 1 : !isEdit"
              accept="image/png,image/jpeg"
              :max="1"
              :init-file-list="formItem.images"
              @onRemove="onRemove"
              @changeImage="changeImage"
            />
            <uploadImg
              v-else
              :ref="`uploadImg_${index}`"
              :disabled="isEdit ? index != formList.length - 1 : !isEdit"
              accept="image/png,image/jpeg"
              :max="1"
              @onRemove="onRemove"
              @changeImage="changeImage"
            />

            <div>上传格式(JPG/PNG)，单张大小不超过2M</div>
          </el-form-item>
        </div>
      </el-form>
      <el-divider v-if="index !== formList.length - 1"></el-divider>
    </div>

    <div>
      <el-button
        v-if="isEdit"
        type="primary"
        :loading="addLoading"
        @click="addForm"
      >
        +添加打款信息
      </el-button>
    </div>

    <div slot="footer">
      <el-button @click="showDialog = false">取消</el-button>
      <el-button type="primary" :loading="saving" @click="handleSubmit()">
        {{ isEdit ? '保 存' : '确 定' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import uploadImg from '@/components/uploadImg';
  import {
    uploadVoucher,
    getFirstPartyInformationByCode,
  } from '@/api/billManagement';
  import { isNum } from '@/utils/validate';

  export default {
    components: {
      uploadImg,
    },
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      isEdit: {
        type: Boolean,
        default: true,
      },

      record: {
        type: Object,
        default: null,
      },
      successTips: {
        type: String,
        default: '',
      },
      subjectDict: {
        type: Array,
        default: () => [],
      },
      currentInfoList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      const validateNum = (rule, value, callback) => {
        if (!isNum(value) && value) {
          callback(new Error('数字且最多2位小数'));
        } else {
          callback();
        }
      };
      return {
        formList: [
          {
            amount: '', // 打款金额
            bankNo: '', // 收款账号
            bankOpenName: '', // 收款银行
            companyName: '', // 收款名称
            date: '', // 打款日期
            otherBankName: '', // 对方户名
            otherBankNo: '', // 对方账号
            otherBankOpenName: '', // 对方银行
            processableNo: '', // 应收应付事项单号(账单系统上传必传)
            voucher: [], // 凭证文件id列表
          },
        ],
        errorTip: '',
        rules: {
          amount: [
            { required: true, message: '请输入金额', trigger: 'blur' },
            {
              validator: validateNum,
              trigger: 'change',
            },
          ],
          bankNo: [
            { required: true, message: '请输入收款账号', trigger: 'blur' },
          ],
          bankOpenName: [
            { required: true, message: '请输入收款银行', trigger: 'blur' },
          ],
          companyName: [
            { required: true, message: '请输入收款名称', trigger: 'blur' },
          ],
          date: [
            { required: true, message: '请选择打款日期', trigger: 'blur' },
          ],
          otherBankName: [
            { required: true, message: '请输入对方户名', trigger: 'blur' },
          ],
          otherBankNo: [
            { required: true, message: '请输入对方账号', trigger: 'blur' },
          ],
          otherBankOpenName: [
            { required: true, message: '请输入对方银行', trigger: 'blur' },
          ],
          voucher: [{ required: true, message: '请上传凭证' }],
        },
        saving: false,
        addLoading: false,
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {
        const { record } = this;
        if (value && record) {
          this.handleChange(record.companyCode);
        }
        if (!value) {
          this.$nextTick(function () {
            this.$refs[`form_${this.formList.length - 1}`][0].clearValidate();
            this.formList = [{}];
            this.$refs[`uploadImg_${this.formList.length - 1}`][0].clearFiles();
            this.errorTip = '';
          });
        }
        if (value && Array.isArray(this.currentInfoList)) {
          if (this.currentInfoList.length) {
            this.formList = this.currentInfoList.map(item => {
              const { images = [], ...valus } = item;
              return {
                ...valus,
                images: images.map(item => ({
                  url: item,
                })),
              };
            });
            if (this.isEdit) {
              this.formList.push({});
            }
          }
        }
      },
    },
    methods: {
      trim(str) {
        let result;
        if (str) {
          result = str.replace(/\s/g, ''); // 去除字符串全部空格
        }
        return result ? result : '';
      },

      async handleChange(code) {
        if (code) {
          const res = await getFirstPartyInformationByCode({ code });
          if (res) {
            this.formList = this.formList.map((item, index) => {
              if (index === this.formList.length - 1) {
                return {
                  ...item,
                  companyName: res.name,
                  bankOpenName: res.bankName,
                  bankNo: res.bankAccount,
                };
              }
              return item;
            });
          }
        }
      },
      // 添加打款信息
      addForm() {
        this.handleSubmit(
          success => {
            const record = this.record;
            this.handleChange(record.companyCode);
            this.formList.push({});
          },
          err => {},
        );
      },

      // 提交
      handleSubmit(success, err) {
        // 查看详情状态下
        if (!this.isEdit) {
          this.showDialog = false;
          return;
        }
        const index = this.formList.length - 1;

        this.$refs[`form_${this.formList.length - 1}`][0].validate(
          async valid => {
            if (
              !this.formList[index].voucher ||
              (Array.isArray(this.formList[index].voucher) &&
                this.formList[index].voucher.length === 0)
            ) {
              this.errorTip = '请上传凭证';
              return;
            }
            if (valid) {
              if (success) {
                this.addLoading = true;
              } else this.saving = true;
              try {
                await uploadVoucher({
                  ...this.formList[this.formList.length - 1],
                  processableNo: this.record.itemNo,
                });
                if (success) {
                  success();
                } else this.showDialog = false;
                this.$message.success(this.successTips || '上传凭证成功');
                this.$emit('confirm');
              } catch (e) {
                err && err();
              }
              this.saving = false;
              this.addLoading = false;
            }
          },
        );
      },
      onRemove() {
        const index = this.formList.length - 1;
        this.formList[index].voucher = [];

        if (
          !this.formList[index].voucher ||
          (Array.isArray(this.formList[index].voucher) &&
            this.formList[index].voucher.length === 0)
        ) {
          this.errorTip = '请上传凭证';
        }
      },
      changeImage(e) {
        const index = this.formList.length - 1;
        if (e) {
          this.formList[index].voucher = [e.id];
          this.errorTip = '';
        }
      },
    },
  };
</script>
<style lang="scss" scoped></style>
