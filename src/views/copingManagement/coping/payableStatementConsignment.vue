<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-05 13:42:46
 * @LastEditTime: 2022-11-18 17:09:01
 * @LastEditors: dddd
 * @Reference: 
-->
<template>
  <div>
    <el-form inline>
      <el-form-item label="PO单号:">
        <el-input
          v-model="searchParams.poSerialNo"
          placeholder="请输入PO单号"
        ></el-input>
      </el-form-item>
      <el-form-item label="应付结算单号:">
        <el-input
          v-model="searchParams.settleSerialNo"
          placeholder="请输入应付结算单号"
        ></el-input>
      </el-form-item>
      <el-form-item label="原始单据号:">
        <el-input
          v-model="searchParams.bizSerialNo"
          placeholder="请输入原始单据号"
        ></el-input>
      </el-form-item>
      <el-form-item label="合同号:">
        <el-input
          v-model="searchParams.contractNo"
          placeholder="请输入合同号"
        ></el-input>
      </el-form-item>
      <!-- <el-form-item label="对账单号:">
        <el-input
          v-model="searchParams.billSerialNo"
          placeholder="请输入对账单号"
        ></el-input>
      </el-form-item> -->
      <el-form-item label="采购主体:">
        <el-select
          v-model="searchParams.purchaseSubjectCode"
          clearable
          filterable
          placeholder="请选择采购主体"
        >
          <el-option
            v-for="item in subjectDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="供应商:">
        <el-select
          v-model="searchParams.supplierSubjectCode"
          clearable
          filterable
          placeholder="请选择供应商"
        >
          <el-option
            v-for="item in supplyDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="业务线:">
        <el-select
          v-model="searchParams.bizLineId"
          clearable
          placeholder="请选择业务线"
        >
          <el-option
            v-for="item in bizLineId"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="履约方式:">
        <el-select
          v-model="searchParams.performanceMethod"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option label="自履约" value="1"></el-option>
          <el-option label="非自履约" value="2"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="结算周期:">
        <el-date-picker
          v-model="searchDate"
          :clearable="false"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>

      <el-form-item>
        <ac-permission-button
          btn-text="查询"
          permission-key="payableStatementConsignment-search"
          @click="getList(true)"
        ></ac-permission-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <ac-permission-button
          btn-text="导入计费单"
          permission-key="payableStatementConsignment-import"
          @click="handlShowModal(1)"
        ></ac-permission-button>
        <ac-permission-button
          btn-text="导入调整单"
          permission-key=""
          @click="handlShowModal(2)"
        ></ac-permission-button>
        <ac-permission-button
          btn-text="货款调整日志"
          permission-key=""
          @click="showLogModal = true"
        ></ac-permission-button>
        <!-- <ac-permission-button
          btn-text="导出"
          permission-key=""
          @click="onExport"
        ></ac-permission-button> -->
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="props">
        <ac-permission-button
          slot="reference"
          btn-text="查看明细"
          type="text"
          size="small"
          permission-key=""
          @click="handelJump(props.row)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="props.row.isDel"
          slot="reference"
          btn-text="删除"
          type="text"
          size="small"
          permission-key=""
          @click="handelDel(props.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <!--  isServeData false 前端 true 取后端返回-->
    <importModal
      v-model="showModal"
      :options="importOptions"
      :is-serve-data="false"
      @onSuccess="onSuccess"
      @onDownload="onDownload"
    ></importModal>
    <adjustmentLogModal v-model="showLogModal"></adjustmentLogModal>
    <confirmTable
      ref="confirmTable"
      v-model="showConfirmTable"
      :list="confirmObj.confirmImportOrderDTO"
      :type="confirmObj.type"
      @onOK="onOK"
    ></confirmTable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import { downloadFile, parseTime } from '@/utils';
  import { getSelector } from '@/api/billManagement';
  import {
    getListSelector,
    importAdjustmentOrderFeesUpload,
    importAdjustmentOrderGoodsUpload,
    importOrderConfirmImportFile,
    importOrderConfirmSupplementImportFile,
    importOrderDelete,
    importOrderImportFile,
    importOrderSupplementImportFile,
    listExcelModel,
    queryPageSub,
  } from '@/api/copingManagement';
  import { exportExcel } from '@/api/blob';
  import importModal from './components/importModal';
  import adjustmentLogModal from './components/adjustmentLogModal';
  import confirmTable from './components/confirmTable';

  export default {
    name: 'PayableStatementConsignment',
    components: {
      dynamictable,
      importModal,
      adjustmentLogModal,
      confirmTable,
    },
    beforeRouteEnter(to, from, next) {
      if (to.matched && to.matched.length > 2)
        to.matched.splice(1, to.matched.length - 2);
      next();
    },
    data() {
      let columns = [
        {
          prop: 'settleSerialNo',
          label: '结算单号',
        },
        {
          prop: 'poSerialNo',
          label: 'PO单号',
        },
        {
          prop: 'bizSerialNo',
          label: '原始单据号',
        },
        {
          prop: 'contractNo',
          label: '合同编号',
        },
        {
          prop: 'billSerialNo',
          label: '对账单号',
        },
        {
          prop: 'relatedSettleSerialNo',
          label: '关联单号',
        },
        {
          prop: 'billStatus.name',
          label: '出账状态',
          // render: ({ billStatus }) => (
          //   <span>{['未出账', '', '已出账', '出账失败'][billStatus]}</span>
          // ),
        },
        {
          prop: 'remark',
          label: '拒绝理由',
        },
        {
          prop: 'settleTypeDesc',
          label: '结算节点',
        },
        {
          prop: 'payStatus',
          label: '付款状态',
          render: ({ payStatus }) => (
            <span>
              {['未付款', '部分付款', '全部付款', '付款失败'][payStatus]}
            </span>
          ),
        },
        {
          prop: 'corporateMethod',
          label: '合作方式',
          render: ({ corporateMethod }) => (
            <span>{['经销', '寄售'][corporateMethod - 1]}</span>
          ),
        },
        {
          prop: 'performanceMethod',
          label: '履约方式',
          render: ({ performanceMethod }) => (
            <span>{['自履约', '非自履约'][performanceMethod - 1]}</span>
          ),
        },
        // {
        //   prop: 'settleCurrency',
        //   label: '结算币种',
        // },
        {
          prop: 'bizLineName',
          label: '业务线',
        },
        {
          prop: 'purchaseSubjectName',
          label: '采购主体',
        },
        {
          prop: 'supplierSubjectName',
          label: '供应商',
        },
        {
          prop: 'settleCurrency',
          label: '结算币种',
        },
        {
          prop: 'payableTaxInclusive',
          label: '应付结算金额（含税）',
        },
        {
          prop: 'payableTaxExcluded',
          label: '应付结算金额（未税）',
        },
        {
          prop: 'payableTaxValue',
          label: '应付结算金额（税额）',
        },
        {
          prop: 'payTaxInclusive',
          label: '实付结算金额（含税）',
        },
        {
          prop: 'createTime',
          label: '创建时间',
        },
        {
          prop: 'paySuccessTime',
          label: '付款完成时间',
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          minWidth: '200px',
          maxWidth: '400px',
          scopedSlots: { customRender: 'operation' },
        },
      ];

      return {
        showLogModal: false,
        showModal: false,
        showConfirmTable: false,
        importType: 1,
        importOptions: {
          title: '寄售-应付计费单',
        },
        confirmObj: {
          fileId: '',
          confirmImportOrderDTO: [],
        },
        searchDate: '',
        searchParams: {
          billStatus: '',
          bizLineId: '',
          contractNo: '',
          payStatus: '',
          poSerialNo: '',
          purchaseSubjectCode: '',
          settleCurrency: '',
          settleSerialNo: '',
          settleTimeEnd: '',
          settleTimeStart: '',
          supplierSubjectCode: '',
          billSerialNo: '',
          bizSerialNo: '',
        },
        list: [],
        supplyDict: [],
        subjectDict: [],
        bizLineId: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          pageSize: [10, 20, 50, 100, 200, 300, 400, 500],
        },
        columns,
      };
    },
    created() {
      const { settleItemNo } = this.$route.query;
      if (settleItemNo) {
        this.searchParams.settleSerialNo = settleItemNo;
      } else {
        this.searchDate = this.setInitDate();
      }

      getListSelector({}).then(res => {
        if (res) {
          this.supplyDict = res.supplyDict;
          this.subjectDict = res.subjectDict;
        }
      });
      getSelector({}).then(res => {
        if (res) {
          this.currency = res.currency;
          this.bizLineId = res.bizLineId;
        }
      });

      this.getList(true);
    },
    methods: {
      handlShowModal(type) {
        this.importType = type;
        if (type === 2) {
          this.importOptions.selectList = [
            {
              key: '1',
              value: '应付寄售费用调整',
            },
            { key: '2', value: '应付寄售货款调整（sku维度）' },
          ];
          this.importOptions.selectWebDataList = [
            {
              key: '1',
              value: '应付寄售费用调整模板',
            },
            { key: '2', value: '应付寄售货款调整模板（sku维度）' },
          ];
        } else {
          this.importOptions.selectList = [
            {
              key: '1',
              value: '应付计费单导入',
            },
            {
              key: '2',
              value: '应付寄售计费单导入（补录）',
            },
          ];
          this.importOptions.selectWebDataList = [
            {
              key: '1',
              value: '应付计费单导入模板',
            },
            {
              key: '2',
              value: '应付寄售计费单导入模板（补录）',
            },
          ];
        }
        this.importOptions.type = '1';
        this.showModal = true;
      },
      onExport() {
        const params = this.getParams();
        this.$confirm('确定将该数据导出?', '导出', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          exportExcel(
            params,
            '/api/finance-receivable/settleItem/exportList',
            'post',
          ).then(res => {
            downloadFile(res.data, '结算单列表');
          });
        });
      },
      async onDownload(form) {
        const res = await listExcelModel();

        if (res) {
          const a = [
            'import.fees.excel.model',
            'supplement.import.fees.excel.model',
          ];
          const b = ['adjust.fees.excel.model', 'adjust.goods.excel.model'];
          const url =
            this.importType == 1
              ? res[a[Number(form.key) - 1]]
              : res[b[Number(form.key) - 1]];
          window.location.href = url;
        }
      },
      async onOK() {
        const { fileId, confirmImportOrderDTO, type } = this.confirmObj;
        const params = {
          fileId,
          confirmImportOrderDTO: confirmImportOrderDTO.map(
            ({ bizLine, orderType, ...item }) => ({
              ...item,
              bizLineId: bizLine.bizLineId,
              orderTypeCode: orderType.code,
            }),
          ),
        };
        if (this.importType == 1 && type == '1') {
          params.corporateMethod = 2;
        }
        const confirmImportFile =
          type == 1
            ? importOrderConfirmImportFile
            : importOrderConfirmSupplementImportFile;
        try {
          this.$refs['confirmTable'].btnLoading(true);
          const res = await confirmImportFile(params);
          this.$message({
            message: '确认成功',
            type: 'success',
          });
          this.$refs['confirmTable'].btnLoading(false);
          this.showConfirmTable = false;
          this.getList();
        } catch (error) {
          this.$refs['confirmTable'].btnLoading(false);
        }
      },
      async onSuccess(e, done, err, form) {
        try {
          const a = [importOrderImportFile, importOrderSupplementImportFile];
          const b = [
            importAdjustmentOrderFeesUpload,
            importAdjustmentOrderGoodsUpload,
          ];
          const importUpload =
            this.importType == 1
              ? a[Number(form.chargeType) - 1]
              : b[Number(form.chargeType) - 1];

          const params = { fileId: e.id };
          if (this.importType == 1 && form.chargeType == '1') {
            params.corporateMethod = 2;
          }

          const res = await importUpload(params);

          if (this.importType == 1) {
            if (res && Array.isArray(res)) {
              this.confirmObj = {
                type: form.chargeType,
                fileId: e.id,
                confirmImportOrderDTO: res,
              };

              this.showModal = false;
              setTimeout(() => {
                this.showConfirmTable = true;
              }, 600);

              err();
            }
          } else {
            if (form.chargeType == 1) {
              done();
              this.getList();
            }
            if (form.chargeType == 2) {
              this.$notify({
                title: '提示',
                message: '稍后请在货款调整日志查看导入结果',
                duration: 0,
                type: 'success',
              });
              this.showModal = false;
              err();
              this.getList();
            }
          }
        } catch (error) {
          err();
        }
      },
      async handelDel(row) {
        await importOrderDelete({ settleSerialNo: row.settleSerialNo });
        this.$message.success('删除成功');
        this.getList();
      },
      handelJump(row) {
        const {
          contractNo,
          poSerialNo,
          settleSerialNo,
          corporateMethod,
          performanceMethod,
          createTime,
          purchaseSubjectName,
          supplierSubjectName,
          remark,
        } = row;
        this.$router.push({
          path: 'payableStatementDetails',
          query: {
            contractNo,
            poSerialNo,
            settleSerialNo,
            corporateMethod,
            performanceMethod,
            createTime,
            purchaseSubjectName,
            supplierSubjectName,
            remark,
          },
        });
      },
      getParams() {
        const searchDate = this.searchDate;
        this.searchParams.settleTimeStart = searchDate
          ? parseTime(searchDate[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.settleTimeEnd = searchDate
          ? parseTime(searchDate[1], '{y}-{m}-{d}')
          : '';
        const params = {
          ...this.searchParams,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await queryPageSub(params);
        this.options.loading = false;

        if (res) {
          this.list = res ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.searchDate = this.setInitDate(30);
      },
      setInitDate(cFormat) {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
        end.setTime(end.getTime() + 3600 * 1000 * 24 * 30);
        return [
          parseTime(start, cFormat || '{y}-{m}-{d} 00:00:00'),
          parseTime(end, cFormat || '{y}-{m}-{d} 23:59:59'),
        ];
      },
    },
  };
</script>
<style lang="scss"></style>
