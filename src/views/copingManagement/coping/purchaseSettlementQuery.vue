<template>
  <div>
    <div v-if="tab === 2">
      <el-button
        style="font-size: 16px; margin-bottom: 10px"
        type="text"
        @click="handelJump(1, null)"
      >
        返回
      </el-button>
    </div>
    <search
      :tab="tab"
      :subject-dict="subjectDict"
      :supply-dict="supplyDict"
      :biz-line-id="bizLineId"
      :pay-model-type="payModelType"
      :settle-type-list="settleTypeList"
      :currency="currency"
      :current-row="currentRow"
      @onSearch="onSearch"
    ></search>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="{ ...options, showSummary: false }"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="props">
        <ac-permission-button
          v-if="props.row.accountPeriod != '汇总'"
          slot="reference"
          :btn-text="tab === 1 ? '查看明细' : '详情'"
          type="text"
          size="small"
          permission-key="purchaseAaleAgreementMgt-edit"
          @click="handelJump(2, props.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <settlementStatementModal
      v-model="showModal"
      :modal-type="1"
      :supplier-info="supplierInfo"
    ></settlementStatementModal>
    <el-dialog width="1000px" title="单据明细" :visible.sync="dialogVisible">
      <dynamictable
        :data-source="poList"
        :columns="getPoColumns()"
        :options="{ ...options, showSummary: false }"
        :pagination="poPagination"
        :fetch="settleOrderPageItem"
      ></dynamictable>
    </el-dialog>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import settlementStatementModal from '../bill/components/settlementStatementModal';
  import search from './components/search';
  import {
    getSettleOrderList,
    getSettleOrderSummary,
    getListSelector,
    getSecondPartInformationById,
    settleOrderPageItem,
    querySettleType,
  } from '@/api/copingManagement';
  import { getSelector } from '@/api/billManagement';
  import { debounce, parseTime } from '@/utils';
  import { changeTwoDecimal } from '@/utils/math';
  export default {
    name: 'PurchaseSettlementQuery',
    components: {
      dynamictable,
      search,
      settlementStatementModal,
    },
    beforeRouteEnter(to, from, next) {
      if (to.matched && to.matched.length > 2)
        to.matched.splice(1, to.matched.length - 2);
      next();
    },
    data() {
      return {
        tab: 1,
        search: {},
        dialogVisible: false,
        supplierInfo: {},
        list: [],
        poList: [],
        currency: [],
        bizLineId: [],
        supplyDict: [],
        subjectDict: [],
        payModelType: [],
        settleTypeList: [], // 结算方式列表
        showModal: false,
        currentRow: null,
        currentDetailsRow: null,
        options: {
          loading: false,
          border: true,
          showSummary: true,
        },
        pageSize: 1,
        poPagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },
    created() {
      getListSelector({}).then(res => {
        if (res) {
          this.supplyDict = res.supplyDict;
          this.subjectDict = res.subjectDict;
        }
      });
      getSelector({}).then(res => {
        if (res) {
          this.currency = res.currency;
          this.bizLineId = res.bizLineId;
          this.payModelType = res.payModelType;
        }
      });
      this.getQuerySettleType();
    },
    methods: {
      // 结算方式列表
      async getQuerySettleType() {
        let res = await querySettleType();
        if (res) {
          const list = Object.keys(res).map(item => {
            return {
              key: item,
              value: res[item],
            };
          });
          this.settleTypeList = list;
        }
      },
      settleOrderPageItem() {
        const { currentDetailsRow } = this;
        const params = {
          settleSerialNo: currentDetailsRow && currentDetailsRow.settleSerialNo,
          current: this.poPagination.pageSize,
          size: this.poPagination.pageLimit,
        };
        settleOrderPageItem(params).then(res => {
          if (res) {
            this.poList = res && res.records ? res.records : [];
            this.poPagination.total = res ? res.total : 0;
          }
        });
      },
      getText(val, list) {
        let text = '';
        list.map(item => {
          if (item.key == val) {
            text = item.value;
          }
        });
        return text;
      },
      summaryMethod({ columns, data }) {
        const sums = ['汇总'];
        columns.forEach((item, index) => {
          const values = data.map(item1 => Number(item1[item.property]));
          if ([5, 6, 7, 8, 9].includes(index)) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
          }
        });
        return sums.map(item => changeTwoDecimal(item));
      },
      handleShowModal(supplierId) {
        getSecondPartInformationById({ supplierId }).then(res => {
          if (res) {
            this.showModal = true;
            this.supplierInfo = {
              secondPartyName: res.accountName,
              secondPartyCardNo: res.cardNo,
              secondPartyBankName: res.bankName,
              secondPartyAddress: res.address,
              secondPartyBankAddress: res.bankAddress,
            };
          }
        });
      },
      handelJump(val, row) {
        if (this.tab === 2 && val === 2) {
          this.currentDetailsRow = row;
          this.settleOrderPageItem();
          this.dialogVisible = true;
          return;
        }
        this.tab = val;
        if (val === 1) {
          // 返回记住页数
          this.pagination.pageSize = this.pageSize;
          this.currentRow = null;
        } else {
          this.init(row);
        }
      },
      init(row) {
        this.currentRow = row;
        this.pageSize = this.pagination.pageSize;
        Object.assign(this.$data.pagination, this.$options.data().pagination);
      },
      getParams() {
        const params = {
          ...this.search,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const getApiList =
          this.tab === 1 ? getSettleOrderSummary : getSettleOrderList;
        try {
          const res = await getApiList(params);
          this.options.loading = false;
          if (res) {
            this.pagination.total = res ? res.total : 0;
            this.list =
              this.tab === 1 ? this.getSumList(res.records) : res.records;
          }
        } catch (error) {
          this.options.loading = false;
        }
      },
      getSummary(list, item, key) {
        let num = 0;
        list.forEach(el => {
          if (el.settleCurrency === item) {
            num = num + el[key];
          }
        });
        return changeTwoDecimal(num);
      },
      getSumList(list) {
        if (!Array.isArray(list) || !list.length) return [];
        const arr = [];
        list.forEach(item => {
          if (!arr.includes(item.settleCurrency)) {
            arr.push(item.settleCurrency);
          }
        });
        const sumList = arr.map(item => {
          return {
            accountPeriod: '汇总',
            settleCurrency: item,
            payableTaxInclusive: this.getSummary(
              list,
              item,
              'payableTaxInclusive',
            ),
            payableTaxExcluded: this.getSummary(
              list,
              item,
              'payableTaxExcluded',
            ),
            payableTaxValue: this.getSummary(list, item, 'payableTaxValue'),
            payTaxInclusive: this.getSummary(list, item, 'payTaxInclusive'),
            unpayTaxInclusive: this.getSummary(list, item, 'unpayTaxInclusive'),
          };
        });
        return list.concat(sumList);
      },
      onSearch: debounce(function (e) {
        this.search = e;
        this.getList(true);
      }, 1000),
      getPoColumns() {
        return [
          {
            prop: 'poSerialNo',
            label: 'PO单号',
          },
          {
            prop: 'productCode',
            label: '条形码',
          },
          {
            prop: 'productName',
            label: '商品中文名称',
          },
          {
            prop: 'purchaseCount',
            label: '采购数量',
          },
          {
            prop: 'purchasePriceTaxInclusive',
            label: '采购单价（含税）',
          },
          {
            prop: 'purchasePriceTaxExcluded',
            label: '采购单价（未税）',
          },
          {
            prop: 'purchaseTaxInclusive',
            label: '采购金额（含税）',
          },
          {
            prop: 'purchaseTaxExcluded',
            label: '采购金额（未税）',
          },
          {
            prop: 'rate',
            label: '税率',
          },
          {
            prop: 'taxValue',
            label: '税额',
          },
          {
            prop: 'remark',
            label: '备注',
          },
        ];
      },
      getColumns() {
        const columns = [
          // {
          //   prop: 'accountPeriod',
          //   label: '日期',
          // },
          {
            prop: 'purchaseSubjectName',
            label: '采购主体',
          },
          {
            prop: 'supplierSubjectName',
            label: '供应商名称',
          },
          {
            prop: 'bizLineName',
            label: '业务线',
          },
          {
            prop: 'corporateMethod',
            label: '合作方式',
            render: ({ corporateMethod }) => (
              <span>{['经销', '寄售'][corporateMethod - 1]}</span>
            ),
          },
          {
            prop: 'performanceMethod',
            label: '履约方式',
            render: ({ performanceMethod }) => (
              <span>{['自履约', '非自履约'][performanceMethod - 1]}</span>
            ),
          },
          {
            prop: 'settleCurrency',
            label: '结算币种',
          },
          {
            prop: 'payableTaxInclusive',
            label: '应付（含税）',
          },
          {
            prop: 'payableTaxExcluded',
            label: '应付（未税）',
          },
          {
            prop: 'payableTaxValue',
            label: '应付税额',
          },
          {
            prop: 'payTaxInclusive',
            label: '实付（含税）',
          },
          {
            prop: 'unpayTaxInclusive',
            label: '未付（含税）',
          },
          // {
          //   prop: 'operation',
          //   label: '操作',
          //   fixed: 'right',
          //   width: '140',
          //   scopedSlots: { customRender: 'operation' },
          // },
        ];
        const columns1 = [
          {
            prop: 'poSerialNo',
            label: 'PO单号',
          },
          {
            prop: 'settleSerialNo',
            label: '结算单编号',
          },
          {
            prop: 'contractNo',
            label: '关联合同编号',
          },
          {
            prop: 'purchaseSubjectName',
            label: '采购主体',
          },
          {
            prop: 'supplierSubjectName',
            label: '供应商名称',
            render: ({ supplierSubjectName, supplierSubjectCode }) => (
              <a
                style="cursor:pointer"
                onClick={() => this.handleShowModal(supplierSubjectCode)}
              >
                {supplierSubjectName}
              </a>
            ),
          },
          // {
          //   prop: 'fundsDirection',
          //   label: '收支类型',
          //   render: ({ fundsDirection }) => (
          //     <span>{fundsDirection === 1 ? '支出' : '收入'}</span>
          //   ),
          // },
          {
            prop: 'bizLineName',
            label: '业务线',
          },
          // {
          //   prop: 'corporateMethod',
          //   label: '合作方式',
          //   render: ({ corporateMethod }) => (
          //     <span>{corporateMethod === 1 ? '经销' : '代转经'}</span>
          //   ),
          // },
          {
            prop: 'payModel',
            label: '付款方式',
            render: ({ payModel }) => (
              <span>{this.getText(payModel, this.settleTypeList)}</span>
            ),
          },
          // {
          //   prop: 'payType',
          //   label: '付款类型',
          //   render: ({ payType }) => (
          //     <span>{['全款', '预付款', '尾款'][payType]}</span>
          //   ),
          // },
          {
            prop: 'settleCurrency',
            label: '结算币种',
          },
          {
            prop: 'payableTaxInclusive',
            label: '应付(含税)',
          },
          {
            prop: 'payableTaxExcluded',
            label: '应付(未税)',
          },
          {
            prop: 'payableTaxValue',
            label: '应付税额',
          },
          {
            prop: 'payTaxInclusive',
            label: '实付(含税)',
          },
          {
            prop: 'unpayTaxInclusive',
            label: '未付(含税)',
          },
          {
            prop: 'createTime',
            label: '创建时间',
          },
          {
            prop: 'paySuccessTime',
            label: '付款完成时间',
          },
          {
            prop: 'payStatusStr',
            label: '付款状态',
            render: ({ payStatus }) => (
              <span>
                {['未付款', '部分付款', '全部付款', '付款失败'][payStatus]}
              </span>
            ),
          },
          // {
          //   prop: 'billStatusStr',
          //   label: '出账状态',
          //   render: ({ billStatus }) => (
          //     <span>
          //       {billStatus == 0
          //         ? '未出账'
          //         : billStatus == 2
          //         ? '已出账'
          //         : '出账失败'}
          //     </span>
          //   ),
          // },
          // {
          //   prop: 'operation',
          //   label: '操作',
          //   fixed: 'right',
          //   width: '140',
          //   scopedSlots: { customRender: 'operation' },
          // },
        ];
        return this.tab === 1 ? columns : columns1;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
