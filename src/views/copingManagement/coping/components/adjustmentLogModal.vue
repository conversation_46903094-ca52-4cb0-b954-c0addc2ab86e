<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-07-18 14:10:01
 * @LastEditTime: 2022-07-19 11:03:56
 * @LastEditors: xuxiang
 * @Reference: 
-->
<template>
  <el-dialog
    :title="options.title"
    :visible.sync="showDialog"
    :close-on-click-modal="false"
    width="1000px"
  >
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="errorMsg" slot-scope="scope">
        <el-popover
          placement="top"
          trigger="hover"
          :content="scope.row.errorMsg"
        >
          <el-button
            slot="reference"
            class="ellipsis"
            type="text"
            style="width: 200px"
          >
            {{ scope.row.errorMsg }}
          </el-button>
        </el-popover>
      </template>
      <template slot="request" slot-scope="scope">
        <el-popover
          placement="top"
          trigger="hover"
          :content="scope.row.request"
        >
          <el-button
            slot="reference"
            class="ellipsis"
            type="text"
            style="width: 200px"
          >
            {{ scope.row.request }}
          </el-button>
        </el-popover>
      </template>
    </dynamictable>
  </el-dialog>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import { pageAdjustLog } from '@/api/copingManagement';
  export default {
    components: {
      dynamictable,
    },
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        columns: [
          {
            prop: 'operator',
            label: '操作人',
            render: ({ request }) => (
              <span>{JSON.parse(request).userName}</span>
            ),
          },
          {
            prop: 'bizType',
            label: '业务类型',
          },
          {
            prop: 'createTime',
            label: '创建时间',
          },
          {
            prop: 'errorMsg',
            label: '错误信息',
            width: 220,
            scopedSlots: { customRender: 'errorMsg' },
          },
          {
            prop: 'id',
            label: '日志ID',
          },
          {
            prop: 'request',
            label: '参数',
            width: 220,
            scopedSlots: { customRender: 'request' },
          },
          {
            prop: 'status',
            label: '状态',
          },
          {
            prop: 'updateTime',
            label: '更新时间',
          },
        ],
        list: [],
        options: {
          loading: false,
          border: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {
        if (value) {
          this.getList();
        }
      },
    },
    created() {},
    methods: {
      async getList() {
        const { pageSize, pageLimit } = this.pagination;
        this.options.loading = true;
        try {
          const res = await pageAdjustLog({
            current: pageSize,
            size: pageLimit,
          });
          this.options.loading = false;

          if (res) {
            this.list = res ? res.records : [];
            this.pagination.total = res ? res.total : 0;
          }
        } catch (error) {
          this.options.loading = false;
        }
      },
    },
  };
</script>
<style lang="scss">
  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
