<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-06-28 14:35:13
 * @LastEditTime: 2022-09-26 14:33:26
 * @LastEditors: xuxiang
 * @Reference: 
-->
<template>
  <el-dialog
    :visible.sync="showDialog"
    :close-on-click-modal="false"
    width="1400px"
  >
    <el-form
      ref="form"
      :model="saveForm"
      :rules="rulesForm"
      :inline="true"
      label-width="120px"
    >
      <h2>调减前结算单明细</h2>
      <el-row>
        <el-col :span="6">
          <el-form-item label="采购单号:">
            {{ currentRow.poSerialNo }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="结算单号:">
            {{ currentRow.settleSerialNo }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="合同号:">
            {{ currentRow.contractNo }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="合作方式:">
            {{ ['经销', '寄售'][currentRow.corporateMethod - 1] }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="履约方式:">
            {{ ['自履约', '非自履约'][currentRow.performanceMethod - 1] }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="订单生成时间:">
            {{ currentRow.createTime }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="采购主体:">
            {{ currentRow.purchaseSubjectName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="供应商:">
            {{ currentRow.supplierSubjectName }}
          </el-form-item>
        </el-col>
      </el-row>
      <dynamictable
        :data-source="list"
        :columns="getColumns(1)"
        :options="options"
      ></dynamictable>

      <h2>调减结算单明细</h2>
      <dynamictable
        :data-source="saveForm.adjustBillsList"
        :columns="getColumns(2)"
        :options="options"
      >
        <template slot="adjustPrice" slot-scope="scope">
          <el-form-item
            :prop="'adjustBillsList.' + scope.$index + '.adjustPrice'"
            :rules="rulesForm.adjustPrice"
          >
            <el-input
              v-model="scope.row.adjustPrice"
              class="line-input"
              style="width: 150px,padding-bottom: 10px"
              placeholder="请输入调整采购单价（含税）"
              @change="handleAdjustPrice(scope.row)"
            ></el-input>
          </el-form-item>
        </template>
      </dynamictable>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="showDialog = false">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="handleOK">
        提交
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { querySettleOrderItem, adjustGoods } from '@/api/copingManagement';
  import { debounce } from '@/utils';
  import dynamictable from '@/components/dynamic-table';
  import { keepTwoDecimalFull } from '@/utils/math';

  export default {
    components: {
      dynamictable,
    },
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      currentRow: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      const validAdjustPrice = (rule, value, callback) => {
        const reg = /^[-]{0,1}(\d+)$|^[-]{0,1}(\d+\.\d+)$/;
        if (!reg.test(value)) {
          callback(new Error('调整采购单价错误'));
        } else {
          callback();
        }
      };
      return {
        list: [],
        saveForm: {
          adjustBillsList: [],
        },
        rulesForm: {
          adjustPrice: [
            {
              required: true,
              message: '请输入调整采购单价（含税)',
              trigger: 'change',
            },
            { required: true, validator: validAdjustPrice },
          ],
        },
        options: {
          loading: false,
          border: true,
        },
        loading: false,
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {
        if (value) {
          if (this.currentRow) {
            this.getList(this.currentRow);
          }
        }
      },
    },
    created() {},
    methods: {
      handleAdjustPrice() {
        const { adjustBillsList } = this.saveForm;
        const data = adjustBillsList.map(item => {
          const adjustTaxExcluded =
            Number(item.adjustPrice) / (1 + Number(item.rate));
          const adjustAmount =
            Number(item.adjustPrice) *
            Number(item.settleCount) *
            Number(item.settlePercent);
          return {
            ...item,
            adjustTaxExcluded: keepTwoDecimalFull(adjustTaxExcluded),
            adjustAmount: keepTwoDecimalFull(adjustAmount),
          };
        });

        this.saveForm.adjustBillsList = data;
      },
      handleOK: debounce(function () {
        this.$refs.form.validate(async valid => {
          if (valid) {
            const body = {
              settleSerialNo: this.currentRow.settleSerialNo,
              itemList: this.saveForm.adjustBillsList.map(item => ({
                adjustPrice: item.adjustPrice,
                settleItemId: item.id,
              })),
            };
            this.$confirm('是否确认提交?', '', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }).then(async () => {
              await adjustGoods(body);
              this.$message.success('调整成功');
              this.showDialog = false;
            });
          }
        });
      }, 1000),
      async getList(row) {
        const { poSerialNo, settleSerialNo } = row;
        const body = { poSerialNo, settleSerialNo };
        this.options.loading = true;
        const res = await querySettleOrderItem(body);
        this.options.loading = false;

        if (res) {
          this.list =
            res && res.settleOderPoItemVOList ? res.settleOderPoItemVOList : [];
          this.saveForm.adjustBillsList =
            res && res.settleOderPoItemVOList
              ? res.settleOderPoItemVOList.map(item => ({
                  ...item,
                  adjustPrice: item.adjustPrice || 0,
                }))
              : [];
        }
      },
      getColumns(type) {
        const columns1 = [
          {
            prop: 'id',
            label: '采购明细行号',
          },
          {
            prop: 'poSerialNo',
            label: '关联单号',
          },
          {
            prop: 'productCode',
            label: '商品条码',
          },
          {
            prop: 'productName',
            label: '商品名称',
          },
          {
            prop: 'settleCurrency',
            label: '币种',
          },
          {
            prop: 'settleCount',
            label: '采购结算数量',
          },
          {
            prop: 'purchasePriceTaxInclusive',
            label: '采购单价（含税）',
          },
          {
            prop: 'purchasePriceTaxValue',
            label: '采购单价（税额）',
          },
          {
            prop: 'payableTaxInclusive',
            label: '应付结算金额（含税）',
          },
          {
            prop: 'payableTaxExcluded',
            label: '应付结算金额（未税）',
          },
          {
            prop: 'payableTaxValue',
            label: '应付结算金额（税额）',
          },
          {
            prop: 'settleStatus',
            label: '结算状态',
          },
        ];

        const columns2 = [
          {
            prop: 'id',
            label: '采购明细行号',
          },
          {
            prop: 'poSerialNo',
            label: '关联单号',
          },
          {
            prop: 'productCode',
            label: '商品条码',
          },

          {
            prop: 'productName',
            label: '商品名称',
          },
          {
            prop: 'settleCurrency',
            label: '币种',
          },
          {
            prop: 'adjustPrice',
            label: '调整采购单价（含税）',
            width: '240',
            scopedSlots: { customRender: 'adjustPrice' },
          },

          {
            prop: 'adjustTaxExcluded',
            label: '调整采购单价（未税）',
          },
          {
            prop: 'adjustAmount',
            label: '调整单金额（含税）',
          },
        ];
        return type === 1 ? columns1 : columns2;
      },
    },
  };
</script>
<style lang="scss"></style>
