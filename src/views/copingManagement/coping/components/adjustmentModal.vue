<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-06-28 10:08:41
 * @LastEditTime: 2022-09-26 14:33:48
 * @LastEditors: xuxiang
 * @Reference: 
-->
<template>
  <el-dialog
    :visible.sync="showDialog"
    :close-on-click-modal="false"
    width="1200px"
  >
    <el-form
      ref="form"
      :model="saveForm"
      :rules="rulesForm"
      :inline="true"
      label-width="120px"
    >
      <h2>调减前计费单</h2>

      <dynamictable
        :data-source="[{ ...currentRow }]"
        :columns="getColumns(1)"
        :options="options"
      ></dynamictable>
      <h2>调减计费单</h2>
      <dynamictable
        :data-source="saveForm.adjustBillsList"
        :columns="getColumns(2)"
        :options="options"
      >
        <template slot="adjustTaxInclusive" slot-scope="scope">
          <el-form-item
            :prop="'adjustBillsList.' + scope.$index + '.adjustTaxInclusive'"
            :rules="rulesForm.adjustTaxInclusive"
          >
            <el-input
              v-model="scope.row.adjustTaxInclusive"
              class="line-input"
              style="width: 150px,padding-bottom: 10px"
              placeholder="请输入调减含税金额"
            ></el-input>
          </el-form-item>
        </template>
      </dynamictable>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="showDialog = false">取 消</el-button>
      <el-button type="primary" @click="handleOK">提交</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { adjustFees } from '@/api/copingManagement';
  import { debounce } from '@/utils';
  import dynamictable from '@/components/dynamic-table';

  export default {
    components: {
      dynamictable,
    },
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      currentRow: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        saveForm: {
          adjustBillsList: [],
        },
        rulesForm: {
          adjustTaxInclusive: [
            {
              required: true,
              message: '请输入调减含税金额',
              trigger: 'change',
            },
          ],
        },
        options: {
          loading: false,
          border: true,
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {
        if (!value) {
          this.$nextTick(function () {});
        }
        if (this.currentRow) {
          this.saveForm.adjustBillsList = [{ ...this.currentRow }];
        }
      },
    },
    created() {},
    methods: {
      handleOK: debounce(function () {
        this.$refs.form.validate(async valid => {
          if (valid) {
            this.$confirm('是否确认提交?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            })
              .then(async () => {
                const { adjustBillsList = [] } = this.saveForm;
                await adjustFees({
                  adjustTaxInclusive: adjustBillsList.length
                    ? adjustBillsList[0]['adjustTaxInclusive']
                    : '',
                  settleSerialNo: this.currentRow.settleSerialNo,
                });
                this.$message.success('调减成功');
                this.showDialog = false;
              })
              .catch(() => {});
          }
        });
      }, 1000),
      getColumns(type) {
        const columns1 = [
          {
            prop: 'operator',
            label: '操作人',
          },
          {
            prop: 'createTime',
            label: '创建时间',
          },
          {
            prop: 'settleSerialNo',
            label: '结算单号',
          },

          {
            prop: 'corporateMethod',
            label: '合作方式',
            render: ({ corporateMethod }) => (
              <span>{['经销', '寄售'][corporateMethod - 1]}</span>
            ),
          },
          {
            prop: 'performanceMethod',
            label: '履约类型',
            render: ({ performanceMethod }) => (
              <span>{['自履约', '非自履约'][performanceMethod - 1]}</span>
            ),
          },
          {
            prop: 'orderTypeName',
            label: '原始单据类型',
          },

          {
            prop: 'bizLineName',
            label: '业务线',
          },
          {
            prop: 'purchaseSubjectName',
            label: '采购主体',
          },
          {
            prop: 'supplierSubjectName',
            label: '供应商',
          },

          {
            prop: 'settleCurrency',
            label: '结算币种',
          },
          {
            prop: 'payableTaxInclusive',
            label: '含税金额',
          },
          {
            prop: 'payableTaxExcluded',
            label: '不含税金额',
          },
          {
            prop: 'payableTaxValue',
            label: '税额',
          },
          {
            prop: 'bizSerialNo',
            label: '原始单据号',
          },
          {
            prop: 'contractNo',
            label: '合同编号',
          },
          {
            prop: 'poSerialNo',
            label: 'po单号',
          },
          {
            prop: 'billSerialNo',
            label: '对账单号',
          },
          // {
          //   prop: 'remark',
          //   label: '备注',
          // },
        ];

        const columns2 = [
          {
            prop: 'a',
            label: '操作人',
          },
          {
            prop: 'b',
            label: '创建时间',
          },
          {
            prop: 'c',
            label: '结算单号',
          },

          {
            prop: 'corporateMethod',
            label: '合作方式',
            render: ({ corporateMethod }) => (
              <span>{['经销', '寄售'][corporateMethod - 1]}</span>
            ),
          },
          {
            prop: 'performanceMethod',
            label: '履约类型',
            render: ({ performanceMethod }) => (
              <span>{['自履约', '非自履约'][performanceMethod - 1]}</span>
            ),
          },
          {
            prop: 'orderTypeName',
            label: '原始单据类型',
          },

          {
            prop: 'bizLineName',
            label: '业务线',
          },
          {
            prop: 'purchaseSubjectName',
            label: '采购主体',
          },
          {
            prop: 'supplierSubjectName',
            label: '供应商',
          },

          {
            prop: 'settleCurrency',
            label: '结算币种',
          },
          {
            prop: 'adjustTaxInclusive',
            label: '调减含税金额',
            width: '240',
            scopedSlots: { customRender: 'adjustTaxInclusive' },
          },
          // {
          //   prop: 'payableTaxExcluded',
          //   label: '调整不含税金额',
          // },
          // {
          //   prop: 'payableTaxValue',
          //   label: '调整税额',
          // },
          {
            prop: 'bizSerialNo',
            label: '原始单据号',
          },
          {
            prop: 'contractNo',
            label: '合同编号',
          },
          {
            prop: 'poSerialNo',
            label: 'po单号',
          },
          {
            prop: 'billSerialNo',
            label: '对账单号',
          },
          // {
          //   prop: 'remark',
          //   label: '备注',
          // },
        ];
        return type === 1 ? columns1 : columns2;
      },
    },
  };
</script>
<style lang="scss"></style>
