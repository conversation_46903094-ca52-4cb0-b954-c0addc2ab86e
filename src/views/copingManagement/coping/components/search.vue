<template>
  <div class="searchParams">
    <el-form ref="search" inline :model="searchParams">
      <template v-if="tab === 2">
        <el-form-item label="结算周期:">
          <el-date-picker
            v-model="detailsSearchDate"
            type="daterange"
            :clearable="false"
            range-separator="至"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="PO单号:">
          <el-input
            v-model="searchParams.poSerialNo"
            clearable
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item label="结算单号:">
          <el-input
            v-model="searchParams.settleSerialNo"
            clearable
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item label="合同编号:">
          <el-input
            v-model="searchParams.contractNo"
            clearable
            placeholder="请输入"
          />
        </el-form-item>
      </template>
      <template v-if="tab === 1">
        <el-form-item label="结算周期:">
          <el-date-picker
            v-model="searchDate"
            type="daterange"
            :clearable="false"
            range-separator="至"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="采购主体:">
          <el-select
            v-model="searchParams.purchaseSubjectCode"
            clearable
            filterable
            placeholder="请选择"
          >
            <el-option
              v-for="item in subjectDict"
              :key="item.dictValue"
              :label="item.dictDesc"
              :value="item.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="供应商名称:">
          <el-select
            v-model="searchParams.supplierSubjectCodeList"
            clearable
            filterable
            multiple
            placeholder="请选择"
          >
            <el-option
              v-for="item in supplyDict"
              :key="item.dictValue"
              :label="item.dictDesc"
              :value="item.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="合作方式:">
          <el-select
            v-model="searchParams.corporateMethod"
            clearable
            filterable
            placeholder="请选择"
          >
            <el-option label="经销" value="1"></el-option>
            <el-option label="寄售" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="履约方式:">
          <el-select
            v-model="searchParams.performanceMethod"
            clearable
            filterable
            placeholder="请选择"
          >
            <el-option label="自履约" value="1"></el-option>
            <el-option label="非自履约" value="2"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="业务线:">
          <el-select
            v-model="searchParams.bizLineId"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in bizLineId"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            ></el-option>
          </el-select>
        </el-form-item>
      </template>

      <el-form-item v-if="tab === 2" label="状态:">
        <el-select
          v-model="searchParams.payStatus"
          clearable
          placeholder="请选择"
        >
          <el-option label="未付款" value="0"></el-option>
          <el-option label="部分付款" value="1"></el-option>
          <el-option label="全部付款" value="2"></el-option>
          <el-option label="付款失败" value="3"></el-option>
        </el-select>
      </el-form-item>

      <!-- <el-form-item v-if="tab === 2" label="出账状态:">
        <el-select
          v-model="searchParams.billStatus"
          clearable
          placeholder="请选择"
        >
          <el-option label="未出账" value="0"></el-option>
          <el-option label="已出账" value="1"></el-option>
          <el-option label="出账失败" value="2"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item v-if="tab === 2" label="付款方式:">
        <el-select
          v-model="searchParams.payType"
          clearable
          placeholder="请选择"
        >
          <!-- <el-option label="分期付款" value="0"></el-option>
          <el-option label="货到付款" value="1"></el-option>
          <el-option label="实销月结" value="2"></el-option> -->
          <el-option
            v-for="item in settleTypeList"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="结算币种:">
        <el-select
          v-model="searchParams.settleCurrency"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in currency"
            :key="item.key"
            :label="item.key"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import { parseTime, initSearchParams, setInitData } from '@/utils';
  export default {
    props: {
      tab: {
        type: Number,
        default: 1,
      },
      currentRow: {
        type: Object,
        default: null,
      },
      settleTypeList: {
        type: Array,
        default: () => [],
      },
      subjectDict: {
        type: Array,
        default: () => [],
      },
      supplyDict: {
        type: Array,
        default: () => [],
      },
      bizLineId: {
        type: Array,
        default: () => [],
      },
      currency: {
        type: Array,
        default: () => [],
      },
      payModelType: {
        type: Array,
        default: () => [],
      },
    },

    data() {
      return {
        searchDate: '',
        detailsSearchDate: '',
        detailsParams: {},
        searchParams: {
          corporateMethod: '',
          performanceMethod: '',
          billStatus: '',
          bizLineId: '',
          contractNo: '',
          payStatus: '',
          payType: '',
          poSerialNo: '',
          purchaseSubjectCode: '',
          settleCurrency: '',
          settleSerialNo: '',
          settleTimeEnd: '',
          settleTimeStart: '',
          supplierSubjectCodeList: [],
        },
      };
    },
    watch: {
      currentRow: {
        handler(val, oldval) {
          if (val) {
            this.detailsSearchDate = this.searchDate;
            // this.detailsParams.settleTimeEnd = val.settleTimeEnd;
            // this.detailsParams.settleTimeStart = val.settleTimeStart;
            this.detailsParams.purchaseSubjectCode = val.purchaseSubjectCode;
            this.detailsParams.supplierSubjectCode = val.supplierSubjectCode;
            this.detailsParams.bizLineId = val.bizLineId;
          } else {
            this.detailsParams = {};
          }
          this.onSearch();
        },
        // 深度观察监听
        deep: true,
      },
    },

    created() {
      this.searchDate = setInitData(30);
      this.onSearch();
    },
    methods: {
      getParams() {
        const date = this.tab === 2 ? this.detailsSearchDate : this.searchDate;
        this.searchParams.settleTimeStart = date
          ? parseTime(date[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.settleTimeEnd = date
          ? parseTime(date[1], '{y}-{m}-{d}')
          : '';
        return initSearchParams({
          ...this.searchParams,
          ...this.detailsParams,
        });
      },
      onSearch() {
        const { supplierSubjectCodeList, ...params } = this.getParams();
        let body = {
          ...params,
        };
        if (!this.currentRow) {
          body.supplierSubjectCodeList = supplierSubjectCodeList;
        }
        this.$emit('onSearch', body);
      },
      onExport() {
        this.$emit('onExport');
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        if (this.tab === 2) {
          this.detailsSearchDate = this.searchDate;
        } else this.searchDate = setInitData(30);
      },
    },
  };
</script>

<style lang="scss" scoped></style>
