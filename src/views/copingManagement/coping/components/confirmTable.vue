<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-09-08 10:26:47
 * @LastEditTime: 2022-09-21 17:52:08
 * @LastEditors: xuxiang
 * @Reference: 
-->

<template>
  <el-dialog
    :visible.sync="showDialog"
    :close-on-click-modal="false"
    width="1200px"
  >
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
    ></dynamictable>

    <div slot="footer" class="dialog-footer">
      <el-button @click="showDialog = false">取 消</el-button>
      <el-button type="primary" :loading="buttonLoading" @click="handleOK">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { debounce } from '@/utils';
  import dynamictable from '@/components/dynamic-table';

  export default {
    components: {
      dynamictable,
    },
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      type: {
        type: String,
        default: '',
      },
      list: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        buttonLoading: false,
        options: {
          loading: false,
          border: true,
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {},
    },
    created() {},
    methods: {
      handleOK: debounce(function () {
        this.$emit('onOK');
      }, 1000),
      btnLoading(flag) {
        this.buttonLoading = flag;
      },
      getColumns() {
        console.log(this.type, ';this.type');
        const columns = [
          {
            prop: 'orderType.desc',
            label: '单据类型',
          },
          {
            prop: 'contractNo',
            label: '合同编号',
          },
          {
            prop: 'contractVersion',
            label: '合同版本',
          },

          {
            prop: 'corporateMethod',
            label: '合作方式',
            render: ({ corporateMethod }) => (
              <span>{['经销', '寄售'][corporateMethod - 1]}</span>
            ),
          },
          {
            prop: 'bizLine.message',
            label: '业务线',
          },
          {
            prop: 'performanceMethod',
            label: '履约类型',
            render: ({ performanceMethod }) => (
              <span>{['自履约', '非自履约'][performanceMethod - 1]}</span>
            ),
          },

          {
            prop: 'purchaseSubjectName',
            label: '采购主体名称',
          },
          {
            prop: 'supplierSubjectName',
            label: '供应商名称',
          },

          {
            prop: 'settleCurrency',
            label: '结算币种',
          },
          {
            prop: 'payableTaxInclusive',
            label: this.type == '1' ? '含税金额' : '调整含税金额',
          },

          {
            prop: 'rate',
            label: '税率',
          },
          {
            prop: 'poSerialNo',
            label: 'PO单号',
          },
          {
            prop: 'remark',
            label: '备注',
          },
          {
            prop: 'billSerialNo',
            label: '关联对账单号',
            hide: this.type == '1',
          },
        ];

        return columns;
      },
    },
  };
</script>
<style lang="scss"></style>
