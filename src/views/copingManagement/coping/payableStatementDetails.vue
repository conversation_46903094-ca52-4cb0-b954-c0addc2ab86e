<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-05 10:21:54
 * @LastEditTime: 2022-09-21 13:57:35
 * @LastEditors: xuxiang
 * @Reference: 
-->

<template>
  <div>
    <el-button
      style="font-size: 16px; margin-bottom: 10px"
      type="text"
      @click="$router.go(-1)"
    >
      返回
    </el-button>
    <el-form inline>
      <el-row>
        <el-col :span="6">
          <el-form-item label="采购单号:">
            {{ currentRow.poSerialNo }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="结算单号:">
            {{ currentRow.settleSerialNo }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="合同号:">
            {{ currentRow.contractNo }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="合作方式:">
            {{ ['经销', '寄售'][currentRow.corporateMethod - 1] }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="履约方式:">
            {{ ['自履约', '非自履约'][currentRow.performanceMethod - 1] }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="订单生成时间:">
            {{ currentRow.createTime }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="采购主体:">
            {{ currentRow.purchaseSubjectName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="供应商:">
            {{ currentRow.supplierSubjectName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="拒绝理由:">
            {{ currentRow.remark }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="备注:">
            {{ remark }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';

  import { querySettleOrderItem } from '@/api/copingManagement';

  export default {
    components: {
      dynamictable,
    },
    data() {
      let columns = [
        {
          prop: 'id',
          label: '采购明细行号',
        },
        {
          prop: 'poSerialNo',
          label: '关联单号',
        },
        {
          prop: 'productCode',
          label: '商品条码',
        },
        {
          prop: 'productName',
          label: '商品名称',
        },
        {
          prop: 'settleCurrency',
          label: '币种',
        },
        {
          prop: 'settleCount',
          label: '采购结算数量',
        },
        {
          prop: 'purchasePriceTaxInclusive',
          label: '采购单价（含税）',
        },
        {
          prop: 'purchasePriceTaxValue',
          label: '采购单价（税额）',
        },
        {
          prop: 'payableTaxInclusive',
          label: '应付结算金额（含税）',
        },
        {
          prop: 'payableTaxExcluded',
          label: '应付结算金额（未税）',
        },
        {
          prop: 'payableTaxValue',
          label: '应付结算金额（税额）',
        },
        {
          prop: 'settleStatus',
          label: '结算状态',
        },
      ];

      return {
        currentRow: {},
        searchDate: '',
        remark: '',
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
        columns,
      };
    },
    created() {
      this.currentRow = this.$route.query;
      this.getList();
    },
    methods: {
      handelJump() {},

      async getList() {
        const { poSerialNo, settleSerialNo } = this.$route.query;
        const body = { poSerialNo, settleSerialNo };
        this.options.loading = true;
        const res = await querySettleOrderItem(body);
        this.options.loading = false;

        if (res) {
          this.remark = res.remark;
          this.list =
            res && res.settleOderPoItemVOList ? res.settleOderPoItemVOList : [];
        }
      },
    },
  };
</script>
<style lang="scss"></style>
