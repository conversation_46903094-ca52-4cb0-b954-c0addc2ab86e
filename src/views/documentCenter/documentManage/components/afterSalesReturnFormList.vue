<!--
 * @Author: 七七
 * @Date: 2022-03-16 13:54:58
 * @LastEditors: 七七
 * @LastEditTime: 2022-03-30 18:33:27
 * @FilePath: /access-fmis-web/src/views/documentCenter/documentManage/components/afterSalesReturnFormList.vue
-->
<template>
  <div>
    <el-form :model="search" inline>
      <el-form-item label="退供订单编号：">
        <el-input v-model="search.orderCode" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="出库仓库：">
        <el-select v-model="search.deliveryWarehouseCode">
          <el-option
            v-for="(item, index) in warHouseList"
            :key="index"
            :value="item.key"
            :label="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="采购主体：">
        <el-input
          v-model="search.companyOwnerName"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="供应商：">
        <el-input
          v-model="search.supplierOwnerName"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="业务域：">
        <el-select v-model="search.businessLineId">
          <el-option
            v-for="(item, index) in businessList"
            :key="index"
            :value="item.key"
            :label="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="订单状态：">
        <el-select v-model="search.status">
          <el-option
            v-for="(item, index) in statusList"
            :key="index"
            :value="item.key"
            :label="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="订单类型：">
        <el-select v-model="search.orderType">
          <el-option :value="10" label="公司间交易退供"></el-option>
          <el-option :value="20" label="外部采购退供"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="结算状态：" multiple>
        <el-select v-model="search.settlementStatus">
          <el-option
            v-for="(item, index) in settlementList"
            :key="index"
            :value="item.key"
            :label="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button type="primary" @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="查看"
          @click="handleDetail(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    returnFormOption,
    getReturnFormList,
  } from '@/api/compensationDocumentManagement';
  const BUSINESS = [
    { key: 1, value: '综合采购' },
    { key: 2, value: '澳洲采购' },
    { key: 3, value: '福三采购' },
    { key: 4, value: '品牌采购' },
  ];
  const SETTLEMENT = [
    { key: 0, value: '未结算' },
    { key: 1, value: '部分结算' },
    { key: 2, value: '全部结算' },
    { key: 3, value: '结算失败' },
    { key: 4, value: '预结算' },
  ];
  const STATUS = [
    { key: 10, value: '待提交' },
    { key: 20, value: '待出库' },
    { key: 30, value: '已出库' },
    { key: 40, value: '已关闭' },
  ];
  export default {
    components: { dynamictable },
    data() {
      let columns = [
        { prop: 'orderCode', label: '退供订单编号' },
        { prop: 'orderName', label: '退供订单名称' },
        { prop: 'companyOwnerName', label: '采购主体' },
        { prop: 'supplierOwnerName', label: '供应商' },
        { prop: 'deliveryWarehouseName', label: '出库仓库' },
        { prop: 'businessLineName', label: '业务域' },
        { prop: 'orderTypeName', label: '订单类型' },
        { prop: 'statusName', label: '订单状态' },
        { prop: 'settlementStatusName', label: '结算状态' },
        { prop: 'amountTaxInclusive', label: '含税总金额' },
        { prop: 'taxAmount', label: '税额' },
        { prop: 'amountTaxExclusive', label: '未税总金额' },
        { prop: 'creatorIdName', label: '下单人' },
        { prop: 'orderCreateTime', label: '下单时间' },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '80',
          scopedSlots: { customRender: 'operation' },
        },
      ];
      return {
        search: {},
        copySearch: {},
        businessList: BUSINESS,
        settlementList: SETTLEMENT,
        statusList: STATUS,
        warHouseList: [],
        loading: false,
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        list: [],
        options: {
          loading: false,
          border: true,
          mutiSelect: false,
        },
        columns,
      };
    },
    mounted() {
      this.queryOptions();
      this.getList();
    },
    methods: {
      async queryOptions() {
        try {
          const res = await returnFormOption();
          if (res) {
            this.warHouseList = res.deliveryWarehouseCodeList
              ? res.deliveryWarehouseCodeList
              : [];
          }
        } catch (e) {}
      },
      getParams() {
        const body = {
          page: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        const {
          orderCode,
          deliveryWarehouseCode,
          companyOwnerName,
          supplierOwnerName,
          relatedPoNo,
          businessLineId,
          status,
          orderType,
          settlementStatus,
        } = this.copySearch;
        if (orderCode?.length > 0) {
          body.orderCode = orderCode;
        }
        if (deliveryWarehouseCode?.length > 0) {
          body.deliveryWarehouseCode = deliveryWarehouseCode;
        }
        if (companyOwnerName?.length > 0) {
          body.companyOwnerName = companyOwnerName;
        }
        if (supplierOwnerName?.length > 0) {
          body.supplierOwnerName = supplierOwnerName;
        }
        if (relatedPoNo?.length > 0) {
          body.relatedPoNo = relatedPoNo;
        }
        if (businessLineId && businessLineId > 0) {
          body.businessLineId = businessLineId;
        }
        if (status && status > 0) {
          body.status = status;
        }
        if (orderType && orderType > 0) {
          body.orderType = orderType;
        }
        if (settlementStatus === 0 || settlementStatus > 0) {
          body.settlementStatus = settlementStatus;
        }
        return body;
      },
      async getList() {
        const body = this.getParams();
        if (!body) return;
        this.loading = true;
        const res = await getReturnFormList(body);
        if (res) {
          this.list = res.records ? res.records : [];
          this.pagination.total = res.total;
        }
        this.loading = false;
      },
      handleSearch() {
        this.pagination.pageSize = 1;
        this.copySearch = { ...this.search };
        this.getList();
      },
      reset() {
        this.search = {};
        this.handleSearch();
      },
      handleDetail(row) {
        this.$emit('showDetail', row);
      },
    },
  };
</script>
<style scoped></style>
