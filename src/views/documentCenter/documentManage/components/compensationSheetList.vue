<!--
 * @Author: 七七
 * @Date: 2022-04-13 15:37:21
 * @LastEditors: 七七
 * @LastEditTime: 2022-04-27 11:31:20
 * @FilePath: /access-fmis-web/src/views/documentCenter/documentManage/components/compensationSheetList.vue
-->
<template>
  <div>
    <el-form :model="search" inline>
      <el-form-item label="赔偿单号：">
        <el-input
          v-model="search.compensationSn"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="单据结算状态：">
        <el-select v-model="search.settled">
          <el-option
            v-for="(item, index) in statusList"
            :key="index"
            :value="item.key"
            :label="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="赔偿责任方：">
        <el-select v-model="search.responsibilityIdList" multiple>
          <el-option
            v-for="item in responsibilityList"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="申请人：">
        <el-input
          v-model="search.adminUserName"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="订单编号：">
        <el-input v-model="search.orderSn" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="赔付类型：">
        <el-select v-model="search.compensationTypeList" multiple>
          <el-option
            v-for="(item, index) in typeList"
            :key="index"
            :value="item.key"
            :label="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="申请日期">
        <el-date-picker
          v-model="search.date"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button type="primary" @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="查看"
          @click="handleDetail(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    getCompensationSheetList,
    getResponsiblePartyList,
  } from '@/api/compensationDocumentManagement';
  import { getCookie } from '@/utils/auth';
  import dayjs from 'dayjs';
  const TYPE = [
    { key: 1, value: '现金' },
    { key: 4, value: '红包' },
    { key: 5, value: '优惠券' },
  ];
  const STATUS = [
    { key: 0, value: '未结算' },
    { key: 1, value: '已结算' },
  ];
  export default {
    components: { dynamictable },
    data() {
      let columns = [
        { prop: 'compensationSn', label: '赔偿单号' },
        { prop: 'orderSn', label: '订单编号' },
        { prop: 'responsibilityName', label: '赔偿责任方' },
        { prop: 'responsibilityForCompensationName', label: '责任承担主体' },
        { prop: 'compensationReasonName', label: '问题类型' },
        { prop: 'compensationTypeName', label: '赔付类型' },
        { prop: 'amount', label: '赔付值' },
        {
          prop: 'settled',
          label: '结算状态',
          render: ({ settled }) => <span>{settled ? '已结算' : '未结算'}</span>,
        },
        { prop: 'adminUserName', label: '申请人' },
        { prop: 'originCreateTime', label: '申请时间' },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '80',
          scopedSlots: { customRender: 'operation' },
        },
      ];
      return {
        search: {
          responsibilityIdList: [],
          date: [],
          compensationTypeList: [],
        },
        copySearch: {},
        typeList: TYPE,
        statusList: STATUS,
        responsibilityList: [],
        loading: false,
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        list: [],
        options: {
          loading: false,
          border: true,
          mutiSelect: false,
        },
        columns,
      };
    },
    mounted() {
      const endTime = dayjs();
      const startTime = endTime.subtract(1, 'month');
      this.search.date = [
        startTime.format('YYYY-MM-DD HH:mm:ss'),
        endTime.format('YYYY-MM-DD HH:mm:ss'),
      ];
      this.queryOptions();
      this.handleSearch();
    },
    methods: {
      async queryOptions() {
        try {
          const token = getCookie();
          const res = await getResponsiblePartyList(token);
          if (res) {
            this.responsibilityList = res ? res : [];
          }
        } catch (e) {}
      },
      getParams() {
        const body = {
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        const {
          compensationSn,
          settled,
          responsibilityIdList,
          adminUserName,
          orderSn,
          compensationTypeList,
          date,
        } = this.copySearch;
        if (compensationSn?.length > 0) {
          body.compensationSn = compensationSn;
        }
        if (settled === 1 || settled === 0) {
          body.settled = settled;
        }
        if (responsibilityIdList?.length > 0) {
          body.responsibilityIdList = responsibilityIdList.join(',');
        }
        if (adminUserName?.length > 0) {
          body.adminUserName = adminUserName;
        }
        if (orderSn?.length > 0) {
          body.orderSn = orderSn;
        }
        if (compensationTypeList?.length > 0) {
          body.compensationTypeList = compensationTypeList.join(',');
        }
        if (date?.length > 0) {
          body.originCreateTimeStart = date[0];
          body.originCreateTimeEnd = date[1];
        }
        return body;
      },
      async getList() {
        const body = this.getParams();
        if (!body) return;
        this.loading = true;
        const res = await getCompensationSheetList(body);
        if (res) {
          this.list = res.list ? res.list : [];
          this.pagination.total = res.total;
        }
        this.loading = false;
      },
      handleSearch() {
        this.pagination.pageSize = 1;
        this.copySearch = { ...this.search };
        this.getList();
      },
      reset() {
        this.search = {
          responsibilityIdList: [],
          date: [],
          compensationTypeList: [],
        };
        this.handleSearch();
      },
      handleDetail(row) {
        this.$emit('showDetail', row);
      },
    },
  };
</script>
<style scoped></style>
