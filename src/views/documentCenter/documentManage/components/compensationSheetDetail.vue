<!--
 * @Author: 七七
 * @Date: 2022-04-13 15:38:10
 * @LastEditors: 七七
 * @LastEditTime: 2022-04-27 17:33:59
 * @FilePath: /access-fmis-web/src/views/documentCenter/documentManage/components/compensationSheetDetail.vue
-->
<template>
  <div>
    <div>
      <el-button
        style="font-size: 16px; margin-bottom: 10px"
        type="text"
        @click="goBack()"
      >
        返回
      </el-button>
    </div>
    <el-form>
      <el-divider content-position="left"><h2>赔付单信息</h2></el-divider>
      <el-row>
        <el-col :span="12">
          <el-form-item label="赔偿单号:">
            {{ info.compensationSn }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="订单编号:">
            {{ info.orderSn }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider content-position="left"><h2>赔付商品明细</h2></el-divider>
      <dynamictable
        :data-source="list"
        :columns="columns"
        :options="options"
        :pagination="pagination"
        :fetch="getList"
      ></dynamictable>
      <div style="height: 20px; width: 20px"></div>
      <el-divider content-position="left">
        <h2>赔付责任信息</h2>
      </el-divider>
      <el-row>
        <el-col :span="6">
          <el-form-item label="问题类型:">
            {{ info.compensationReasonName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="责任承担方:">
            {{ info.responsibilityName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="责任承担方名称:">
            {{ info.responsibilityForCompensationName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="赔偿类型:">
            {{ info.compensationTypeName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="赔偿金额:">{{ info.amount }}</el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';

  export default {
    components: { dynamictable },
    props: {
      info: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        list: [],
        options: {
          loading: false,
          border: true,
          mutiSelect: false,
        },
        columns: [
          { prop: 'goodsId', label: '商品ID' },
          { prop: 'skuId', label: 'SKUID' },
          { prop: 'goodsName', label: '商品名称' },
          { prop: 'applyCount', label: '申请数量' },
          { prop: 'goodsBarcode', label: '货品ID' },
          { prop: 'commodityName', label: '货品名称' },
          { prop: 'brandName', label: '品牌名称' },
          { prop: 'singleRaxFee', label: '税费（元/件）' },
          { prop: 'shippingFee', label: '运费（元/件）' },
          { prop: 'payFee', label: '实付价（元/件）' },
        ],
      };
    },
    watch: {
      info: {
        handler: function () {
          this.getList();
        },
        immediate: true,
      },
    },
    mounted() {},
    methods: {
      getList() {
        if (this.info.compensationDetailList?.length > 0) {
          this.list = [...this.info.compensationDetailList];
        } else {
          this.list = [];
        }
      },
      goBack() {
        this.$emit('return');
      },
    },
  };
</script>
<style scoped></style>
