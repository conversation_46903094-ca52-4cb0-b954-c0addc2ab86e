<!--
 * @Author: 七七
 * @Date: 2022-03-21 17:56:27
 * @LastEditors: 七七
 * @LastEditTime: 2022-03-30 15:46:51
 * @FilePath: /access-fmis-web/src/views/documentCenter/documentManage/components/returnFormOrderDetail.vue
-->
<template>
  <div>
    <el-descriptions title="退供订单详情" :column="5" size="medium">
      <template slot="extra">
        <el-button type="primary" size="small" @click="goBack">返回</el-button>
      </template>
      <el-descriptions-item
        label="退供订单编号"
        label-style="width:90px;flex-shrink: 0;"
      >
        {{ info.orderCode }}
      </el-descriptions-item>
      <el-descriptions-item label="采购主体">
        {{ info.companyOwnerName }}
      </el-descriptions-item>
      <el-descriptions-item label="供应商">
        {{ info.supplierOwnerName }}
      </el-descriptions-item>
      <el-descriptions-item label="退供原因">
        {{ info.refundOrderReasonName }}
      </el-descriptions-item>
      <el-descriptions-item label="业务域">
        {{ info.businessLineName }}
      </el-descriptions-item>
      <el-descriptions-item label="订单状态">
        {{ info.statusName }}
      </el-descriptions-item>
      <el-descriptions-item label="订单类型">
        {{ info.orderTypeName }}
      </el-descriptions-item>
      <el-descriptions-item
        label="退供单生成时间"
        label-style="width:110px;flex-shrink: 0;"
      >
        {{ info.orderCreateTime }}
      </el-descriptions-item>
      <el-descriptions-item label="结算状态">
        {{ info.settlementStatusName }}
      </el-descriptions-item>
      <el-descriptions-item label="付款状态">
        <!-- {{ info.refundDocItemDList.paymentStatusName }} -->
      </el-descriptions-item>
    </el-descriptions>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="查看"
          permission-key=""
          @click="handleDetail(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  export default {
    components: { dynamictable },
    props: {
      info: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      let columns = [
        { prop: 'relatedPoNo', label: '采购订单编号' },
        { prop: 'relatedPoTitle', label: '采购订单名称' },
        { prop: 'relatedSoNo', label: '关联销售订单' },
        { prop: 'goodsTitle', label: '商品名称' },
        { prop: 'brandName', label: '品牌' },
        { prop: 'goodsBarcode', label: '条形码' },
        { prop: 'actualPurchaseQuantity', label: '实际采购数量' },
        { prop: 'deliveryNum', label: '出库数量' },
        { prop: 'inventoryBatchNo', label: '库存批次号' },
        { prop: 'goodProductNum', label: '退供良品数量' },
        { prop: 'defectiveNum', label: '退供残次数量' },
        { prop: 'purchasePriceTaxExclusive', label: '未税单价' },
        { prop: 'accountCurrency', label: '结算币种' },
        { prop: 'refundAmountTaxExclusive', label: '退供未税金额' },
        { prop: 'refundAmountTaxInclusive', label: '退供含税金额' },
        { prop: 'refundTaxAmount', label: '退供税额' },
        { prop: 'settleQuantity', label: '应结算数量' },
        { prop: 'settleAmountTaxInclusive', label: '应结算金额（含税）' },
        { prop: 'settledQuantity', label: '已结算数量' },
        { prop: 'settledAmountTaxInclusive', label: '已结算金额（含税）' },
        { prop: 'settlementStatusName', label: '结算状态' },
        { prop: 'paymentStatusName', label: '付款状态' },
        {
          prop: 'operation',
          label: '操作',
          width: '80',
          fixed: 'right',
          scopedSlots: { customRender: 'operation' },
        },
      ];
      return {
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        list: [],
        options: {
          loading: false,
          border: true,
          mutiSelect: false,
        },
        columns,
      };
    },
    watch: {
      info: {
        handler: function () {
          this.getList();
        },
        immediate: true,
      },
    },
    mounted() {},
    methods: {
      getList() {
        if (this.info.refundDocItemDList?.length > 0) {
          this.list = this.info.refundDocItemDList;
        } else {
          this.list = [];
        }
      },
      handleDetail(row) {
        this.$emit('checkDetail', row);
      },
      goBack() {
        this.$emit('return');
      },
    },
  };
</script>
<style scoped></style>
