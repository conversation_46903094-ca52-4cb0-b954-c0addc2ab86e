<!--
 * @Author: 七七
 * @Date: 2022-03-22 02:40:15
 * @LastEditors: 七七
 * @LastEditTime: 2022-03-31 15:26:42
 * @FilePath: /access-fmis-web/src/views/documentCenter/documentManage/components/returnFormPODetail.vue
-->
<template>
  <div>
    <div>
      <el-button
        style="font-size: 16px; margin-bottom: 10px"
        type="text"
        @click="goBack()"
      >
        返回
      </el-button>
    </div>
    <el-form>
      <el-divider content-position="left"><h2>购销方信息</h2></el-divider>
      <el-row>
        <el-col :span="6">
          <el-form-item label="采购主体:">
            {{ info.companyOwnerName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="供应商名称:">
            {{ info.supplierOwnerName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider content-position="left"><h2>订单信息</h2></el-divider>
      <el-row>
        <el-col :span="6">
          <el-form-item label="订单编号:">
            {{ listData.relatedPoNo }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="订单名称:">
            {{ listData.relatedPoTitle }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="订单类型:">
            {{ listData.relatedPoDocumentType }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="品牌名称:">
            {{ listData.brandName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="结算币种:">
            {{ listData.settleCurrency }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="记账币种:">
            {{ listData.accountCurrency }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="订单状态:">
            {{ listData.relatedPoStatus }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="渠道:">
            {{ listData.relatedPoChannel }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="下单时间:">
            {{ listData.relatedPoOrderTime }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="下单人:">
            {{ listData.relatedPoOrderName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider content-position="left"><h2>收发货信息</h2></el-divider>
      <el-row>
        <el-col :span="6">
          <el-form-item label="发货状态:">全部发货</el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="收货状态:">全部收货</el-form-item>
        </el-col>
      </el-row>
      <el-divider content-position="left"><h2>财务状态</h2></el-divider>
      <el-row>
        <el-form-item label="结算状态:">
          {{ listData.settlementStatusName }}
        </el-form-item>
        <el-row>
          <el-col :span="6">
            <el-form-item label="应结算数量:">
              {{ listData.settleQuantity }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="应结算金额(含税):">
              {{ listData.settleAmountTaxInclusive }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="实际结算数量:">
              {{ listData.settledQuantity }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="实际结算金额(含税):">
              {{ listData.settledAmountTaxInclusive }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="未结算数量:">
              {{ listData.unsettledQuantity }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="未结算金额(含税):">
              {{ listData.unsettledAmountTaxInclusive }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="付款状态:">
          {{ listData.paymentStatusName }}
        </el-form-item>
        <el-form-item label="支付金额:">
          {{ listData.paidAmount }}
        </el-form-item>
      </el-row>
    </el-form>
    <el-divider content-position="left"><h2>退供明细</h2></el-divider>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  export default {
    components: { dynamictable },
    props: {
      info: {
        type: Object,
        default: () => {},
      },
      listData: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      let columns = [
        { prop: 'id', label: '明细行号' },
        { prop: 'goodsBarcode', label: '商品条码' },
        { prop: 'goodsTitle', label: '商品中文名称' },
        { prop: 'goodProductNum', label: '退供良品数量' },
        { prop: 'defectiveNum', label: '退供残次数量' },
        { prop: 'deliveryGoodProductNum', label: '实际出库良品数量' },
        { prop: 'deliveryDefectiveNum', label: '实际出库次品数量' },
        { prop: 'purchasePriceTaxInclusive', label: '退供单价（含税）' },
        { prop: 'refundAmountTaxInclusive', label: '退供金额（含税）' },
        { prop: 'purchasePriceTaxExclusive', label: '退供单价（未税）' },
        { prop: 'refundAmountTaxExclusive', label: '退供金额（未税）' },
        { prop: 'refundTaxAmount', label: '税额' },
      ];
      return {
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        list: [],
        options: {
          loading: false,
          border: true,
          mutiSelect: false,
        },
        columns,
      };
    },
    watch: {
      info: {
        handler: function () {
          this.getList();
        },
        immediate: true,
      },
    },
    mounted() {},
    methods: {
      getList() {
        if (this.listData) {
          this.list = [this.listData];
        } else {
          this.list = [];
        }
      },
      goBack() {
        this.$emit('return');
      },
    },
  };
</script>
<style scoped></style>
