<!--
 * @Author: 七七
 * @Date: 2022-03-21 17:55:41
 * @LastEditors: 七七
 * @LastEditTime: 2022-03-22 19:57:05
 * @FilePath: /access-fmis-web/src/views/documentCenter/documentManage/afterSalesReturnForm.vue
-->
<template>
  <div>
    <returnFormList v-if="tab === 1" @showDetail="showDetail" />
    <returnFormOrderDetail
      v-if="tab === 2"
      :info="formData"
      @checkDetail="checkPODetail"
      @return="goBack"
    />
    <returnFormPODetail
      v-if="tab === 3"
      :info="formData"
      :list-data="listData"
      @return="goBack"
    />
  </div>
</template>
<script>
  import returnFormList from './components/afterSalesReturnFormList.vue';
  import returnFormOrderDetail from './components/returnFormOrderDetail.vue';
  import returnFormPODetail from './components/returnFormPODetail.vue';
  export default {
    components: { returnFormList, returnFormOrderDetail, returnFormPODetail },
    data() {
      return {
        tab: 1,
        formData: {},
        listData: {},
      };
    },
    mounted() {},
    methods: {
      showDetail(row) {
        this.formData = row;
        this.tab = 2;
      },
      goBack() {
        this.tab = this.tab - 1;
      },
      checkPODetail(row) {
        this.listData = row;
        this.tab = 3;
      },
    },
  };
</script>
<style scoped></style>
