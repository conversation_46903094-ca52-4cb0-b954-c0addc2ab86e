<!--
 * @Author: 七七
 * @Date: 2022-04-13 15:35:48
 * @LastEditors: 七七
 * @LastEditTime: 2022-04-14 16:24:41
 * @FilePath: /access-fmis-web/src/views/documentCenter/documentManage/afterSalesCompensation.vue
-->
// 售后赔付单
<template>
  <div>
    <compensationSheetList v-if="tab === 1" @showDetail="showDetail" />
    <compensationSheetDetail
      v-if="tab === 2"
      :info="listData"
      @return="goBack"
    />
  </div>
</template>
<script>
  import compensationSheetDetail from './components/compensationSheetDetail.vue';
  import compensationSheetList from './components/compensationSheetList.vue';

  export default {
    components: { compensationSheetDetail, compensationSheetList },
    data() {
      return {
        tab: 1,
        listData: {},
      };
    },
    mounted() {},
    methods: {
      showDetail(row) {
        this.listData = row;
        this.tab = 2;
      },
      goBack() {
        this.tab = this.tab - 1;
      },
    },
  };
</script>
<style scoped></style>
