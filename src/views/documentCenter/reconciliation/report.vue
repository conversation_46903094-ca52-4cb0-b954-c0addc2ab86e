<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-04-21 17:43:24
 * @LastEditTime: 2022-04-24 18:08:53
 * @LastEditors: xuxiang
 * @Reference: 
-->
<template>
  <div>
    <el-form inline>
      <el-form-item label="日期:">
        <el-date-picker
          v-model="searchDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="单据类型:">
        <el-select
          v-model="searchParams.type"
          clearable
          placeholder="请选择单据类型"
        >
          <el-option
            v-for="item in typeList"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="异常单号:">
        <el-input
          v-model="searchParams.number"
          placeholder="请输入异常单号"
        ></el-input>
      </el-form-item>
      <el-form-item label="异常原因:">
        <el-select
          v-model="searchParams.reason"
          clearable
          placeholder="请选择异常原因"
        >
          <el-option
            v-for="item in reasonList"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';

  import { parseTime, setInitData } from '@/utils';
  import {
    docDataCheckDropdownList,
    docDataCheckPage,
  } from '@/api/documentCenter';

  export default {
    components: {
      dynamictable,
    },
    data() {
      let columns = [
        {
          prop: 'id',
          label: '编号',
        },
        {
          prop: 'createTime',
          label: '日期',
        },
        {
          prop: 'type',
          label: '单据类型',
        },
        {
          prop: 'number',
          label: '异常单号',
        },
        {
          prop: 'field',
          label: '异常字段',
        },
        {
          prop: 'reason',
          label: '异常原因',
        },
        {
          prop: 'remark',
          label: '备注',
        },
        {
          prop: 'status',
          label: '状态',
          render: ({ status }) => (
            <span>
              {status === 'U' ? '待处理' : status === 'S' ? '已处理' : ''}
            </span>
          ),
        },

        {
          prop: 'updateTime',
          label: '操作时间',
        },
      ];

      return {
        searchDate: '',
        searchParams: {
          startTime: '',
          endTime: '',
          type: '',
          number: '',
          reason: '',
        },
        list: [],
        typeList: [],
        reasonList: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
        columns,
      };
    },
    created() {
      // this.searchDate = setInitData(30);
      docDataCheckDropdownList().then(res => {
        if (res) {
          this.typeList = res.typeList;
          this.reasonList = res.reasonList;
        }
      });
      this.getList(true);
    },
    methods: {
      getParams() {
        const searchDate = this.searchDate;
        this.searchParams.startTime = searchDate
          ? parseTime(searchDate[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.endTime = searchDate
          ? parseTime(searchDate[1], '{y}-{m}-{d}')
          : '';
        const params = {
          ...this.searchParams,
          page: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await docDataCheckPage(params);
        this.options.loading = false;

        if (res) {
          this.list = res ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        // this.searchDate = setInitData(30);
      },
    },
  };
</script>
<style lang="scss"></style>
