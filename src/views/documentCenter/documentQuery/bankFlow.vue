<template>
  <div>
    <el-form inline>
      <el-form-item label="银行名称:">
        <el-select
          v-model="searchParams.bankName"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in BANK_FLOW"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="户名:">
        <el-input
          v-model="searchParams.accountName"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="账号:">
        <el-input
          v-model="searchParams.accountNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="交易对手银行:">
        <el-input
          v-model="searchParams.counterpartiesBankName"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="交易对手户名:">
        <el-input
          v-model="searchParams.counterpartiesAccountname"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="交易对手账户:">
        <el-input
          v-model="searchParams.counterpartiesAccountNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="交易日期:">
        <el-date-picker
          v-model="searchDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="银行流水号:">
        <el-input
          v-model="searchParams.bankSerialNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="唯一约束号:">
        <el-input
          v-model="searchParams.uniqueSeqNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="资金方向:">
        <el-select
          v-model="searchParams.directionMoney"
          clearable
          placeholder="请选择"
        >
          <el-option label="收(入金)" value="C"></el-option>
          <el-option label="支(出金)" value="P"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="对账状态:">
        <el-select
          v-model="searchParams.checkStatus"
          clearable
          placeholder="请选择"
        >
          <el-option label="未对账" value="U"></el-option>
          <el-option label="已对账" value="S"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button type="primary" @click="onExport">导入流水</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    ></dynamictable>
    <el-dialog
      title="上传银行流水"
      width="700px"
      :visible.sync="visible"
      @closed="onClose"
    >
      <el-form
        ref="formData"
        :model="form"
        label-position="left"
        label-width="120px"
      >
        <el-form-item
          prop="bankType"
          :rules="[
            {
              required: true,
              message: '请选择银行',
              trigger: 'change',
            },
          ]"
          label="选择银行:"
        >
          <el-select
            v-model="form.bankType"
            clearable
            filterable
            placeholder="请选择"
          >
            <el-option
              v-for="item in BANK_FLOW"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item required :error="errorTip" label="银行流水文件: ">
          <uploadImg
            ref="uploadImg"
            :max="1"
            :size="1"
            :type-file="[]"
            list-type="text"
            btn-text="上 传"
            accept=".xlsx, .xls, .csv"
            @onRemove="onRemove"
            @changeImage="changeImage"
          />
          <div>
            <h3 style="margin-bottom: 0px">注意：</h3>
            <div>
              请选择对应的银行流水文件，且文件中不包含已经上传的记录，否则将上传失败
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import uploadImg from '@/components/uploadImg';
  import {
    bankImportRecordList,
    bankImportWaterImport,
  } from '@/api/documentCenter';
  import { parseTime, debounce, setInitData } from '@/utils';
  import { BANK_FLOW } from '@/consts';
  export default {
    components: {
      dynamictable,
      uploadImg,
    },
    data() {
      return {
        BANK_FLOW,
        searchDate: '',
        visible: false,
        form: {
          bankType: '',
          fileId: '',
          fileName: '',
          fileType: '',
        },
        errorTip: '',
        searchParams: {
          accountName: '',
          accountNo: '',
          bankName: '',
          bankSerialNo: '',
          checkStatus: '',
          counterpartiesAccountNo: '',
          counterpartiesAccountname: '',
          counterpartiesBankName: '',
          directionMoney: '',
          transDateEnd: '',
          transDateStart: '',
          uniqueSeqNo: '',
        },
        list: [],
        options: {
          loading: false,
          border: true,
          // showSummary: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },

    created() {
      this.searchDate = setInitData(90);
      this.getList(true);
    },
    methods: {
      onExport() {
        this.visible = true;
      },
      onRemove() {
        this.errorTip = '请上银行流水文件';
        this.form.fileId = '';
        this.form.fileName = '';
        this.form.fileType = '';
      },
      changeImage(file) {
        this.form.fileId = file.id;
        if (file.name) {
          this.form.fileName = file.name.split('.')[0];
        }

        this.form.fileType = file.extName;
        this.errorTip = '';
      },
      handleSubmit: debounce(function () {
        this.$refs.formData.validate(async valid => {
          if (!this.form.fileId) {
            this.errorTip = '请上银行流水文件';
            return;
          }
          if (!valid) return;

          const res = await bankImportWaterImport(this.form);
          this.$message.success(res || '导入成功');
          this.visible = false;
          this.getList(true);
        });
      }, 1000),
      onClose() {
        Object.assign(this.$data.form, this.$options.data().form);
        this.$nextTick(function () {
          this.errorTip = '';
          this.$refs.formData.clearValidate();
          this.$refs.uploadImg.clearFiles();
        });
      },

      getParams() {
        this.searchParams.transDateStart = this.searchDate
          ? parseTime(this.searchDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.transDateEnd = this.searchDate
          ? parseTime(this.searchDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        const params = {
          ...this.searchParams,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await bankImportRecordList(params);
        this.options.loading = false;
        this.list = res && res.list ? res.list : [];
        this.pagination.total = res ? res.total : 0;
      },
      onSearch: debounce(function () {
        this.getList(true);
      }, 1000),
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.searchDate = setInitData(90);
      },
      getColumns() {
        const columns = [
          {
            prop: 'bankName',
            label: '银行名称',
          },
          {
            prop: 'accountName',
            label: '户名',
          },
          {
            prop: 'accountNo',
            label: '账号',
          },
          {
            prop: 'directionMoney',
            label: '资金方向',
            render: ({ directionMoney }) => (
              <span>{directionMoney === 'C' ? '收(入金)' : '支(出金)'}</span>
            ),
          },
          {
            prop: 'currency',
            label: '结算币种',
          },
          {
            prop: 'transAmount',
            label: '交易金额',
          },
          {
            prop: 'afterAmount',
            label: '交易后余额',
          },
          {
            prop: 'counterpartiesBankName',
            label: '交易对手银行',
          },
          {
            prop: 'counterpartiesAccountname',
            label: '交易对手户名',
          },
          {
            prop: 'counterpartiesAccountNo',
            label: '交易对手账户',
          },
          {
            prop: 'fee',
            label: '手续费',
          },
          {
            prop: 'transDate',
            label: '交易日期',
          },
          {
            prop: 'accDate',
            label: '记账日期',
          },
          {
            prop: 'accTime',
            label: '记账时间',
          },
          {
            prop: 'bankSerialNo',
            label: '银行流水号',
          },
          {
            prop: 'uniqueSeqNo',
            label: '唯一约束号',
          },
          {
            prop: 'receiptName',
            label: '回单名称',
          },
          {
            prop: 'purpose',
            label: '用途',
          },
          {
            prop: 'checkStatus',
            label: '对账状态',
            render: ({ checkStatus }) => (
              <span>{checkStatus === 'U' ? '未对账' : '已对账'}</span>
            ),
          },
        ];

        return columns;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
