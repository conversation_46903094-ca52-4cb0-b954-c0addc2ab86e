<template>
  <div>
    <div v-if="tab === 2">
      <el-button
        style="font-size: 16px; margin-bottom: 10px"
        type="text"
        @click="handelJump(1, null)"
      >
        返回
      </el-button>
    </div>
    <el-form ref="search" inline :model="searchParams">
      <el-form-item
        prop="payId"
        :rules="[
          {
            validator: rules.validatePayId,
            trigger: 'change',
          },
        ]"
        label="支付单号:"
      >
        <el-input
          v-model="searchParams.payId"
          clearable
          placeholder="请输入支付单编号"
        />
      </el-form-item>
      <el-form-item v-if="tab === 2" label="支付流水号:">
        <el-input
          v-model="searchParams.paymentId"
          clearable
          placeholder="请输入支付流水号"
        />
      </el-form-item>
      <el-form-item label="销售下单时间:">
        <el-date-picker
          v-model="transactionDate"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="创建时间:">
        <el-date-picker
          v-model="payCreatedDate"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :picker-options="pickerOptions"
        ></el-date-picker>
      </el-form-item>
      <el-form-item v-if="tab === 1" label="支付单状态:">
        <el-select
          v-model="searchParams.orderStatusList"
          clearable
          multiple
          placeholder="请选择"
        >
          <el-option :value="0" label="预订单"></el-option>
          <el-option :value="1" label="支付中"></el-option>
          <el-option :value="2" label="支付成功"></el-option>
          <el-option :value="3" label="支付失败"></el-option>
          <el-option :value="4" label="商户关单"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="tab === 2" label="订单状态:">
        <el-select
          v-model="searchParams.statusList"
          clearable
          multiple
          placeholder="请选择"
        >
          <el-option :value="1" label="交易中"></el-option>
          <el-option :value="2" label="交易成功"></el-option>
          <el-option :value="3" label="交易失败"></el-option>
          <el-option :value="50" label="冲正"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="tab === 1" label="退款状态:">
        <el-select
          v-model="searchParams.refundStatusList"
          clearable
          multiple
          placeholder="请选择"
        >
          <el-option :value="0" label="无退款"></el-option>
          <el-option :value="1" label="部分退款"></el-option>
          <el-option :value="2" label="全部退款"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="{ ...options }"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="props">
        <ac-permission-button
          slot="reference"
          btn-text="查看支付流水"
          type="text"
          size="small"
          permission-key=""
          @click="handelJump(2, props.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import { getPayList, getPaymentList } from '@/api/documentCenter';
  import { debounce, parseTime, initSearchParams } from '@/utils';
  import { isNumber } from '@/utils/validate';
  export default {
    components: {
      dynamictable,
    },
    data() {
      const validatePayId = (rule, value, callback) => {
        const val = value ? value.trim() : '';
        if (!isNumber(val)) {
          callback(new Error('支付单号必须为数字'));
        } else {
          callback();
        }
      };
      return {
        tab: 1,
        list: [],
        transactionDate: '',
        payCreatedDate: '',
        rules: {
          validatePayId,
        },
        searchParams: {
          payId: '',
          paymentId: '',
          statusList: [],
          // transactionTimeStart: '',
          // transactionTimeEnd: '',
          // paymentCreatetimeStart: '',
          // paymentCreatetimeEnd: '',
          orderStatusList: [],
          refundStatusList: [],
        },
        search: {}, // 缓存搜索条件
        options: {
          loading: false,
          border: true,
        },
        pageSize: 1, // 缓存页数
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        copyDate: '',
        pickerOptions: {
          onPick: ({ maxDate, minDate }) => {
            this.copyDate = minDate.getTime();
            if (maxDate) {
              this.copyDate = '';
            }
          },
          disabledDate: time => {
            if (this.copyDate !== '') {
              const one = 30 * 24 * 3600 * 1000;
              const minTime = this.copyDate - one;
              const maxTime = this.copyDate + one;
              return time.getTime() < minTime || time.getTime() > maxTime;
            }
          },
        },
      };
    },
    created() {
      // this.getList();
    },
    methods: {
      handelJump(val, row) {
        this.tab = val;
        this.list = [];
        if (val === 1) {
          this.getCacheData();
        } else {
          this.setCacheData(row);
        }
        this.getList();
      },
      getCacheData() {
        this.pagination.total = null;
        // 返回记住页数
        this.pagination.pageSize = this.pageSize;
        const {
          transactionTimeStart,
          transactionTimeEnd,
          paymentCreatetimeStart,
          paymentCreatetimeEnd,
          payCreatedTimeStart,
          payCreatedTimeEnd,
        } = this.search;
        this.transactionDate = [transactionTimeStart, transactionTimeEnd];
        this.payCreatedDate = [
          this.tab === 1 ? payCreatedTimeStart : paymentCreatetimeStart,
          this.tab === 1 ? payCreatedTimeEnd : paymentCreatetimeEnd,
        ];
        this.searchParams = { ...this.search };
      },
      setCacheData(row) {
        this.pageSize = this.pagination.pageSize;
        this.search = { ...this.searchParams };
        this.searchParams = {
          payId: row.payId,
        };
        this.transactionDate = '';
        this.payCreatedDate = '';
        Object.assign(this.$data.pagination, this.$options.data().pagination);
      },
      getParams() {
        this.searchParams.transactionTimeStart = this.transactionDate
          ? parseTime(this.transactionDate[0], '{y}-{m}-{d} {h}:{i}:{s}')
          : '';
        this.searchParams.transactionTimeEnd = this.transactionDate
          ? parseTime(this.transactionDate[1], '{y}-{m}-{d} {h}:{i}:{s}')
          : '';
        this.searchParams[
          this.tab === 1 ? 'payCreatedTimeStart' : 'paymentCreatetimeStart'
        ] = this.payCreatedDate
          ? parseTime(this.payCreatedDate[0], '{y}-{m}-{d} {h}:{i}:{s}')
          : '';
        this.searchParams[
          this.tab === 1 ? 'payCreatedTimeEnd' : 'paymentCreatetimeEnd'
        ] = this.payCreatedDate
          ? parseTime(this.payCreatedDate[1], '{y}-{m}-{d} {h}:{i}:{s}')
          : '';
        const {
          orderStatusList = [],
          refundStatusList = [],
          statusList = [],
        } = this.searchParams;

        const params = {
          ...this.searchParams,
          orderStatusList: orderStatusList.length
            ? orderStatusList.join(',')
            : '',
          refundStatusList: refundStatusList.length
            ? refundStatusList.join(',')
            : '',
          statusList: statusList.length ? statusList.join(',') : '',
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const getApiList = this.tab === 1 ? getPayList : getPaymentList;

        try {
          const res = await getApiList(params);
          this.options.loading = false;
          if (res) {
            this.list = res && res.list ? res.list : [];
            this.pagination.total = res ? res.total : 0;
          }
        } catch (error) {
          this.options.loading = false;
        }
      },
      onSearch: debounce(function (e) {
        const searchParams = initSearchParams(this.getParams());
        if (Object.keys(searchParams).length === 2) {
          return this.$message.warning('请最少选择一个查询条件');
        }
        this.$refs.search.validate(async valid => {
          if (!valid) return;
          this.getList(true);
        });
      }, 1000),
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.transactionDate = '';
        this.payCreatedDate = '';
      },
      getColumns() {
        const columns = [
          {
            prop: 'payId',
            label: '支付单号',
          },
          {
            prop: 'paySn',
            label: '租户订单号',
          },
          {
            prop: 'memberId',
            label: '会员ID',
          },
          {
            prop: 'tenantId',
            label: '租户ID',
          },
          {
            prop: 'tenantName',
            label: '租户名称',
          },
          {
            prop: 'currencyUnit',
            label: '币种',
          },
          {
            prop: 'orderTitle',
            label: '订单标题',
          },
          {
            prop: 'orderSn',
            label: '销售单号',
          },
          {
            prop: 'amount',
            label: '销售单金额(CNY)',
          },
          // {
          //   prop: 'orderRefundingAmount',
          //   label: '退款中金额',
          // },
          {
            prop: 'orderRefundedAmount',
            label: '已退款金额',
            render: ({ orderRefundedAmount, payId }) => (
              <div
                onClick={() => {
                  this.$router.push({
                    path: 'payRefundOrder',
                    query: {
                      payId,
                    },
                  });
                }}
              >
                <a style="cursor:pointer">{orderRefundedAmount}</a>
              </div>
            ),
          },
          {
            prop: 'orderStatusName',
            label: '支付单状态',
          },
          {
            prop: 'refundStatusName',
            label: '退款状态',
          },
          {
            prop: 'applicationName',
            label: '客户端来源',
          },
          {
            prop: 'transactionTime',
            label: '销售下单时间',
          },
          {
            prop: 'payCreatedTime',
            label: '创建时间',
          },
          {
            prop: 'payUpdatedTime',
            label: '更新时间',
          },
          {
            prop: 'payCloseTime',
            label: '关单时间',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            minWidth: '80px',
            maxWidth: '140px',
            scopedSlots: { customRender: 'operation' },
          },
        ];
        const columns1 = [
          {
            prop: 'paymentId',
            label: '支付流水号',
          },
          {
            prop: 'payId',
            label: '支付单号',
          },
          {
            prop: 'paySn',
            label: '租户订单号',
          },
          {
            prop: 'memberId',
            label: '会员ID',
          },
          {
            prop: 'companyName',
            label: '收单主体名称',
          },
          {
            prop: 'tenantId',
            label: '租户ID',
          },
          {
            prop: 'tenantName',
            label: '租户名称',
          },
          // {
          //   prop: 'settleCurrency',
          //   label: '商户ID',
          // },
          // {
          //   prop: 'payableTaxInclusive',
          //   label: '商户名称',
          // },
          {
            prop: 'currencyUnit',
            label: '币种',
          },
          {
            prop: 'rate',
            label: '汇率',
          },
          {
            prop: 'amount',
            label: '支付金额',
          },
          {
            prop: 'chargeAmount',
            label: '支付手续费',
          },
          // {
          //   prop: 'orderTitle',
          //   label: '订单标题',
          // },
          {
            prop: 'orderSn',
            label: '销售单号',
          },
          {
            prop: 'payAmount',
            label: '销售单金额(CNY)',
          },
          {
            prop: 'statusName',
            label: '订单状态',
          },
          {
            prop: 'subStatusName',
            label: '子状态',
          },
          {
            prop: 'paymentProductionName',
            label: '支付产品',
          },
          {
            prop: 'channelName',
            label: '支付渠道',
          },
          {
            prop: 'payTypeName',
            label: '支付方式',
          },
          {
            prop: 'transactionTime',
            label: '销售下单时间',
          },
          {
            prop: 'createTime',
            label: '创建时间',
          },
          {
            prop: 'updateTime',
            label: '更新时间',
          },
          {
            prop: 'closeTime',
            label: '关单时间',
          },
        ];
        return this.tab === 1 ? columns : columns1;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
