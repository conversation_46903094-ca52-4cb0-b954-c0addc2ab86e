<template>
  <div v-if="tab === 1">
    <el-form ref="search" inline :model="searchParams">
      <el-form-item label="单据状态:">
        <el-select
          v-model="searchParams.auditStatus"
          clearable
          placeholder="请选择"
        >
          <!-- <el-option label="失败" :value="0"></el-option> -->
          <el-option label="成功" :value="1"></el-option>
          <!-- <el-option label="关闭" :value="2"></el-option>
          <el-option label="待处理" :value="3"></el-option> -->
        </el-select>
      </el-form-item>
      <el-form-item label="调整类型:">
        <el-select v-model="searchParams.type" clearable placeholder="请选择">
          <el-option
            v-for="item in INVENTORY_ADJUSTMENT_TYPE"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="调整单号:">
        <el-input
          v-model="searchParams.adjustOrderNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="货主名称:">
        <el-input
          v-model="searchParams.ownerName"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="仓库名称:">
        <el-select
          v-model="searchParams.warehouseId"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in shippingWarehouse"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="创建时间:">
        <el-date-picker
          v-model="searchParams.gmtCreate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="审核时间:">
        <el-date-picker
          v-model="searchParams.auditTime"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="更新时间:">
        <el-date-picker
          v-model="searchParams.gmtModify"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :key="tab"
      :data-source="list"
      :columns="getColumns()"
      :options="{ ...options }"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="adjustNum" slot-scope="props">
        <el-popover placement="top" trigger="hover">
          <dynamictable
            :data-source="props.row.adjustOrderDetailList || []"
            :columns="getChildColumns()"
            :options="options"
          />
          <el-button slot="reference" type="text">
            {{ props.row.adjustNum }}
          </el-button>
        </el-popover>
      </template>
      <template slot="operation" slot-scope="props">
        <ac-permission-button
          slot="reference"
          btn-text="详情"
          type="text"
          size="small"
          permission-key=""
          @click="handelJump(2, props.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
  <div v-else>
    <div>
      <el-button
        style="font-size: 16px; margin-bottom: 10px"
        type="text"
        @click="handelJump(1)"
      >
        返回
      </el-button>
    </div>
    <el-form>
      <el-divider content-position="left"><h2>基本信息</h2></el-divider>
      <el-row>
        <el-col :span="6">
          <el-form-item label="货主名称:">
            {{ currentRow.ownerName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="仓库名称:">
            {{ currentRow.warehouseName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="调整单号:">
            {{ currentRow.adjustOrderNo }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="外部单号:">
            {{ currentRow.thirdOrderNo }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="单据状态:">
            {{ ['失败', '成功', '关闭', '待处理'][currentRow.auditStatus] }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="单据类型:">
            {{ ['库存转移单', '盘点单'][currentRow.orderType] }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="创建时间:">
            {{ currentRow.gmtCreate }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="审核时间:">
            {{ currentRow.auditTime }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="更新时间:">
            {{ currentRow.gmtModify }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="调整类型:">
            {{
              getText(
                currentRow.type,
                INVENTORY_ADJUSTMENT_TYPE,
                'value',
                'label',
              )
            }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="原商品类型:">
            {{ ['良品', '残次品', '冻结品'][currentRow.originalGoodType - 1] }}
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="现商品类型:">
            {{ ['良品', '残次品', '冻结品'][currentRow.newGoodType - 1] }}
          </el-form-item>
        </el-col>
        <!-- <el-col :span="6">
          <el-form-item label="调整原因:">
            {{ currentRow.adjustCause }}
          </el-form-item>
        </el-col> -->

        <el-col :span="6">
          <el-form-item label="创建人名称:">
            {{ currentRow.createrName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="审核人名称:">
            {{ currentRow.auditName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="备注:">
            {{ currentRow.remark }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-divider content-position="left"><h2>调整详情</h2></el-divider>
    <dynamictable
      :key="tab"
      :data-source="currentRow.adjustOrderDetailList || []"
      :columns="getColumns()"
      :options="options"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import { getAdjustOrderList, getDicValues } from '@/api/documentCenter';
  import { parseTime, debounce, initSearchParams } from '@/utils';
  import { INVENTORY_ADJUSTMENT_TYPE } from '@/consts';

  export default {
    components: {
      dynamictable,
    },
    data() {
      return {
        tab: 1, // 1 列表  2，详情页
        searchParams: {
          auditStatus: '',
          type: '',
          adjustOrderNo: '',
          ownerName: '',
          warehouseId: '',
          gmtCreate: '',
          auditTime: '',
          gmtModify: '',
        },
        shippingWarehouse: [],
        INVENTORY_ADJUSTMENT_TYPE,
        list: [],
        currentRow: null,
        options: {
          loading: false,
          border: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },
    created() {
      getDicValues({}).then(res => {
        if (res) {
          this.shippingWarehouse = res.shippingWarehouse;
        }
      });
      // this.getList(true);
    },
    methods: {
      getText(val, list, key = 'key', valKeys = 'values') {
        let text = '';
        list.map(item => {
          console.log(item[key], val, ';mmm');
          if (item[key] == val) {
            text = item[valKeys];
          }
        });
        return text;
      },
      handelJump(val, row) {
        this.tab = val;
        if (val === 1) {
          this.currentRow = null;
        } else {
          this.currentRow = row;
        }
      },
      getParams() {
        const gmtCreateDate = this.searchParams.gmtCreate;
        const auditTimeDate = this.searchParams.auditTime;
        const gmtModifyDate = this.searchParams.gmtModify;
        this.searchParams.gmtCreateStart = gmtCreateDate
          ? parseTime(gmtCreateDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.gmtCreateEnd = gmtCreateDate
          ? parseTime(gmtCreateDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.auditTimeStart = auditTimeDate
          ? parseTime(auditTimeDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.auditTimeEnd = auditTimeDate
          ? parseTime(auditTimeDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.gmtModifyStart = gmtModifyDate
          ? parseTime(gmtModifyDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.gmtModifyEnd = gmtModifyDate
          ? parseTime(gmtModifyDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        const {
          gmtCreate,
          auditTime,
          gmtModify,
          ...params
        } = this.searchParams;
        const query = {
          ...params,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return query;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        try {
          const res = await getAdjustOrderList(params);
          this.options.loading = false;
          if (res) {
            this.list = res && res.list ? res.list : [];
            this.pagination.total = res ? res.total : 0;
          }
        } catch (error) {
          this.options.loading = false;
        }
      },
      onSearch: debounce(function () {
        const searchParams = initSearchParams(this.getParams());
        if (Object.keys(searchParams).length === 2) {
          return this.$message.warning('请最少选择一个查询条件');
        }
        this.getList(true);
      }, 1000),
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },

      getChildColumns() {
        return [
          {
            prop: 'adjustOrderNo',
            label: '调整单号',
          },
          {
            prop: 'fillingNo',
            label: '备案货号',
          },
          {
            prop: 'goodsBarcode',
            label: '商品条码',
          },
          {
            prop: 'goodsName',
            label: '商品名称',
          },
          {
            prop: 'goodsAliasName',
            label: '英文名称',
          },
          {
            prop: 'brandCode',
            label: '品牌CODE',
          },
          {
            prop: 'brandName',
            label: '品牌名称',
          },
          {
            prop: 'adjustNum',
            label: '调整数量',
          },
          {
            prop: 'productDate',
            label: '生产日期',
          },
          {
            prop: 'expireDate',
            label: '失效日期',
          },

          {
            prop: 'inventoryType',
            label: '库存类型',
          },
          {
            prop: 'adjustType',
            label: '调整类型',
            render: ({ adjustType }) => (
              <span>
                {this.getText(
                  adjustType,
                  INVENTORY_ADJUSTMENT_TYPE,
                  'value',
                  'label',
                )}
              </span>
            ),
          },
          {
            prop: 'adjustCause',
            label: '调整原因',
          },
          {
            prop: 'batchCode',
            label: '批次库存CODE',
          },
        ];
      },

      getColumns() {
        const columns = [
          {
            prop: 'ownerName',
            label: '货主名称',
          },
          {
            prop: 'adjustOrderNo',
            label: '调整单号',
          },
          {
            prop: 'type',
            label: '调整类型',
            render: ({ type }) => (
              <span>
                {this.getText(
                  type,
                  INVENTORY_ADJUSTMENT_TYPE,
                  'value',
                  'label',
                )}
              </span>
            ),
          },
          {
            prop: 'adjustNum',
            label: '调整数量',
            scopedSlots: { customRender: 'adjustNum' },
          },
          {
            prop: 'originalGoodType',
            label: '原商品类型',
            render: ({ originalGoodType }) => (
              <span>{['良品', '残次品', '冻结品'][originalGoodType - 1]}</span>
            ),
          },
          {
            prop: 'newGoodType',
            label: '现商品类型',
            render: ({ newGoodType }) => (
              <span>{['良品', '残次品', '冻结品'][newGoodType - 1]}</span>
            ),
          },
          {
            prop: 'orderType',
            label: '单据类型',
            render: ({ orderType }) => (
              <span>{['库存转移单', '盘点单'][orderType]}</span>
            ),
          },
          {
            prop: 'warehouseName',
            label: '仓库名称',
          },
          {
            prop: 'auditStatus',
            label: '单据状态',
            render: ({ auditStatus }) => (
              <span>{['失败', '成功', '关闭', '待处理'][auditStatus]}</span>
            ),
          },
          {
            prop: 'gmtCreate',
            label: '创建时间',
          },
          {
            prop: 'auditTime',
            label: '审核时间',
          },
          {
            prop: 'gmtModify',
            label: '更新时间',
          },
          {
            prop: 'transferReason',
            label: '转移原因',
            render: ({ transferReason }) => (
              <span>{['破损', '临期'][transferReason - 1]}</span>
            ),
          },
          {
            prop: 'remark',
            label: '备注',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            minWidth: '80px',
            maxWidth: '200px',
            scopedSlots: { customRender: 'operation' },
          },
        ];
        const columns1 = [
          {
            prop: 'adjustOrderNo',
            label: '调整单号',
          },
          {
            prop: 'fillingNo',
            label: '备案货号',
          },
          {
            prop: 'goodsBarcode',
            label: '商品条码',
          },
          {
            prop: 'goodsName',
            label: '商品名称',
          },
          {
            prop: 'goodsAliasName',
            label: '英文名称',
          },
          {
            prop: 'brandCode',
            label: '品牌CODE',
          },
          {
            prop: 'brandName',
            label: '品牌名称',
          },
          {
            prop: 'adjustNum',
            label: '调整数量',
          },
          {
            prop: 'productDate',
            label: '生产日期',
          },
          {
            prop: 'expireDate',
            label: '失效日期',
          },
          {
            prop: 'inventoryType',
            label: '库存类型',
          },
          {
            prop: 'adjustType',
            label: '调整类型',
            render: ({ adjustType }) => (
              <span>
                {this.getText(
                  adjustType,
                  INVENTORY_ADJUSTMENT_TYPE,
                  'value',
                  'label',
                )}
              </span>
            ),
          },
          {
            prop: 'adjustCause',
            label: '调整原因',
          },
          {
            prop: 'batchCode',
            label: '批次库存CODE',
          },
        ];
        return this.tab === 1 ? columns : columns1;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
