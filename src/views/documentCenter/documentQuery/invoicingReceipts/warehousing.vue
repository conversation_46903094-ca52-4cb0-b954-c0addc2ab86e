<template>
  <div v-if="tab === 1">
    <el-form ref="search" inline :model="searchParams">
      <el-form-item label="单据状态:">
        <el-select
          v-model="searchParams.orderStatus"
          clearable
          placeholder="请选择"
        >
          <el-option label="已完成" :value="8"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="入库类型:">
        <el-select
          v-model="searchParams.purchaseOrderType"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in purchaseOrderTypeList"
            :key="item.businessOrderTypeCode"
            :label="item.businessOrderTypeName"
            :value="item.businessOrderTypeCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="入库单号:">
        <el-input
          v-model="searchParams.storageOrderCode"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="货主名称:">
        <el-input
          v-model="searchParams.ownerName"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="仓库名称:">
        <el-select
          v-model="searchParams.warehouseId"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in shippingWarehouse"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="创建时间:">
        <el-date-picker
          v-model="searchParams.gmtCreate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="仓库实际入库时间:">
        <el-date-picker
          v-model="searchParams.gmtActualStorage"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="实际到仓时间:">
        <el-date-picker
          v-model="searchParams.gmtActualArrivalWarehouse"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :key="tab"
      :data-source="list"
      :columns="getColumns()"
      :options="{ ...options }"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="inboundQuantity" slot-scope="props">
        <el-popover placement="top" trigger="hover">
          <dynamictable
            :data-source="props.row.inboundOrderDetailList || []"
            :columns="getChildColumns()"
            :options="options"
          />
          <el-button slot="reference" type="text">
            {{ props.row.inboundQuantity }}
          </el-button>
        </el-popover>
      </template>

      <template slot="operation" slot-scope="props">
        <ac-permission-button
          slot="reference"
          btn-text="详情"
          type="text"
          size="small"
          permission-key=""
          @click="handelJump(2, props.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
  <div v-else>
    <div>
      <el-button
        style="font-size: 16px; margin-bottom: 10px"
        type="text"
        @click="handelJump(1)"
      >
        返回
      </el-button>
    </div>
    <el-form>
      <el-divider content-position="left"><h2>基本信息</h2></el-divider>
      <el-row>
        <el-col :span="6">
          <el-form-item label="货主名称:">
            {{ currentRow.ownerName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="仓库名称:">
            {{ currentRow.warehouseName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="入库单号:">
            {{ currentRow.storageOrderCode }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="入库类型:">
            {{
              getText(
                currentRow.purchaseOrderType,
                purchaseOrderTypeList,
                'businessOrderTypeCode',
                'businessOrderTypeName',
              )
            }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="单据状态:">
            {{ currentRow.orderStatus == 8 ? '已完成 ' : '' }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="库存类型:">
            {{
              currentRow.inventoryType == 1
                ? '正品'
                : currentRow.inventoryType == 2
                ? '次品'
                : ''
            }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="创建时间:">
            {{ currentRow.gmtCreate }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="仓库实际入库时间:">
            {{ currentRow.gmtActualStorage }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="起始预计到货时间:">
            {{ currentRow.gmtExpectedArrivalStart }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="截止预计到货时间:">
            {{ currentRow.gmtExpectedArrivalEnd }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="更新时间:">
            {{ currentRow.gmtModify }}
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="创建人名称:">
            {{ currentRow.createrName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="修改人名称:">
            {{ currentRow.modifierName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="备注:">
            {{ currentRow.remark }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider content-position="left"><h2>其它相关单号</h2></el-divider>
      <el-row>
        <el-col :span="6">
          <el-form-item label="仓库反馈单号:">
            {{ currentRow.entryOrderId }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="订舱单号:">
            {{ currentRow.bookingCode }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="物流批次号:">
            {{ currentRow.batchNum }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="报关单号:">
            {{ currentRow.customsClearanceCode }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="报检单号:">
            {{ currentRow.inspectionCode }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="销退入库请求打单号:">
            {{ currentRow.returnOrderRequestCode }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-divider content-position="left"><h2>收货详情</h2></el-divider>
    <dynamictable
      :key="tab"
      :data-source="currentRow.inboundOrderDetailList || []"
      :columns="getColumns()"
      :options="options"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    getInboundOrderList,
    getDeliveryOrderType,
    getDicValues,
  } from '@/api/documentCenter';
  import { parseTime, debounce, initSearchParams } from '@/utils';
  import { DEFECTIVE_TYPE } from '@/consts';

  export default {
    components: {
      dynamictable,
    },
    data() {
      return {
        tab: 1, // 1 列表  2，详情页
        searchParams: {
          orderStatus: '',
          purchaseOrderType: '',
          storageOrderCode: '',
          ownerName: '',
          warehouseId: '',
          gmtCreate: '',
          gmtActualStorage: '',
          gmtActualArrivalWarehouse: '',
        },
        shippingWarehouse: [],
        purchaseOrderTypeList: [],
        list: [],
        currentRow: null,
        options: {
          loading: false,
          border: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },
    created() {
      getDeliveryOrderType({
        businessType: 1,
        // importFlag: 0,
        // orderStatus: 0,
        // isCreate: true,
      }).then(res => {
        if (res) {
          this.purchaseOrderTypeList = res.concat([
            {
              businessOrderTypeCode: 'yjdfrk',
              businessOrderTypeName: '一件代发',
            },
            {
              businessOrderTypeCode: 'companyTraderk',
              businessOrderTypeName: '公司间交易',
            },
          ]);
        }
      });
      getDicValues({}).then(res => {
        if (res) {
          this.shippingWarehouse = res.shippingWarehouse;
        }
      });
      // this.getList(true);
    },
    methods: {
      getText(val, list, key = 'key', valKeys = 'values') {
        let text = '';
        list.map(item => {
          console.log(item[key], val, ';mmm');
          if (item[key] == val) {
            text = item[valKeys];
          }
        });
        return text;
      },
      handelJump(val, row) {
        this.tab = val;
        if (val === 1) {
          this.currentRow = null;
        } else {
          this.currentRow = row;
        }
      },
      getParams() {
        const gmtCreateDate = this.searchParams.gmtCreate;
        const gmtActualStorageDate = this.searchParams.gmtActualStorage;
        const gmtActualArrivalWarehouseDate = this.searchParams
          .gmtActualArrivalWarehouse;
        this.searchParams.gmtCreateStart = gmtCreateDate
          ? parseTime(gmtCreateDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.gmtCreateEnd = gmtCreateDate
          ? parseTime(gmtCreateDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.gmtActualStorageStart = gmtActualStorageDate
          ? parseTime(gmtActualStorageDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.gmtActualStorageEnd = gmtActualStorageDate
          ? parseTime(gmtActualStorageDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.gmtActualArrivalWarehouseStart = gmtActualArrivalWarehouseDate
          ? parseTime(gmtActualArrivalWarehouseDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.gmtActualArrivalWarehouseEnd = gmtActualArrivalWarehouseDate
          ? parseTime(gmtActualArrivalWarehouseDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        const {
          gmtCreate,
          gmtActualStorage,
          gmtActualArrivalWarehouse,
          ...params
        } = this.searchParams;
        const query = {
          ...params,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return query;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        try {
          const res = await getInboundOrderList(params);
          this.options.loading = false;
          if (res) {
            this.list = res && res.list ? res.list : [];
            this.pagination.total = res ? res.total : 0;
          }
        } catch (error) {
          this.options.loading = false;
        }
      },
      onSearch: debounce(function () {
        const searchParams = initSearchParams(this.getParams());
        if (Object.keys(searchParams).length === 2) {
          return this.$message.warning('请最少选择一个查询条件');
        }
        this.getList(true);
      }, 1000),
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },

      getChildColumns() {
        return [
          {
            prop: 'storageOrderCode',
            label: '入库单号',
          },

          {
            prop: 'goodsBarcode',
            label: '商品条码',
          },
          {
            prop: 'goodsName',
            label: '商品名称',
          },
          {
            prop: 'goodsAliasName',
            label: '英文名称',
          },
          {
            prop: 'brandCode',
            label: '品牌CODE',
          },
          {
            prop: 'brandName',
            label: '品牌名称',
          },
          {
            prop: 'purchaseQuantity',
            label: '采购数量',
          },
          {
            prop: 'actualQuantity',
            label: '实际上架数量(正品)',
          },
          {
            prop: 'defectiveQuantity',
            label: '次品数量',
          },
          {
            prop: 'lessQuantity',
            label: '少货数量',
          },
          {
            prop: 'inventoryType',
            label: '库存类型',
            render: ({ inventoryType }) => (
              <span>
                {inventoryType == 1 ? '正品' : inventoryType == 2 ? '次品' : ''}
              </span>
            ),
          },
          {
            prop: 'defectiveType',
            label: '次品类型',
            render: ({ defectiveType }) => (
              <span>
                {this.getText(defectiveType, DEFECTIVE_TYPE, 'value', 'label')}
              </span>
            ),
          },
          {
            prop: 'productTime',
            label: '商品生产日期',
          },
          {
            prop: 'batchCode',
            label: '批次库存CODE',
          },
          {
            prop: 'defectiveDescription',
            label: '次品描述',
          },
        ];
      },

      getColumns() {
        const columns = [
          {
            prop: 'ownerName',
            label: '货主名称',
          },
          {
            prop: 'storageOrderCode',
            label: '入库单号',
          },
          {
            prop: 'purchaseOrderType',
            label: '入库类型',
            render: ({ purchaseOrderType }) => (
              <span>
                {this.getText(
                  purchaseOrderType,
                  this.purchaseOrderTypeList,
                  'businessOrderTypeCode',
                  'businessOrderTypeName',
                )}
              </span>
            ),
          },
          {
            prop: 'inboundQuantity',
            label: '入库数量',
            scopedSlots: { customRender: 'inboundQuantity' },
          },
          {
            prop: 'warehouseName',
            label: '仓库名称',
          },
          {
            prop: 'orderStatus',
            label: '单据状态',
            render: ({ orderStatus }) => (
              <span>{orderStatus == 8 ? '已完成 ' : ''}</span>
            ),
          },
          {
            prop: 'gmtCreate',
            label: '创建时间',
          },
          // {
          //   prop: 'gmtActualArrivalWarehouse',
          //   label: '实际到仓时间',
          // },
          {
            prop: 'gmtActualStorage',
            label: '仓库实际入库时间',
          },

          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            minWidth: '80px',
            maxWidth: '200px',
            scopedSlots: { customRender: 'operation' },
          },
        ];
        const columns1 = [
          {
            prop: 'storageOrderCode',
            label: '入库单号',
          },

          {
            prop: 'goodsBarcode',
            label: '商品条码',
          },
          {
            prop: 'goodsName',
            label: '商品名称',
          },
          {
            prop: 'goodsAliasName',
            label: '英文名称',
          },
          {
            prop: 'brandCode',
            label: '品牌CODE',
          },
          {
            prop: 'brandName',
            label: '品牌名称',
          },
          {
            prop: 'purchaseQuantity',
            label: '采购数量',
          },
          {
            prop: 'actualQuantity',
            label: '实际上架数量(正品)',
          },
          {
            prop: 'defectiveQuantity',
            label: '次品数量',
          },
          {
            prop: 'lessQuantity',
            label: '少货数量',
          },
          {
            prop: 'inventoryType',
            label: '库存类型',
            render: ({ inventoryType }) => (
              <span>
                {inventoryType == 1 ? '正品' : inventoryType == 2 ? '次品' : ''}
              </span>
            ),
          },
          {
            prop: 'defectiveType',
            label: '次品类型',
            render: ({ defectiveType }) => (
              <span>
                {this.getText(defectiveType, DEFECTIVE_TYPE, 'value', 'label')}
              </span>
            ),
          },
          {
            prop: 'productTime',
            label: '商品生产日期',
          },
          {
            prop: 'batchCode',
            label: '批次库存CODE',
          },
          {
            prop: 'defectiveDescription',
            label: '次品描述',
          },
        ];
        return this.tab === 1 ? columns : columns1;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
