<template>
  <div>
    <el-row>
      <el-col :span="6">
        <el-form-item label="货主名称:">
          {{ currentRow.ownerName }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="调出仓库名称:">
          {{
            tab === 2
              ? currentRow.deliveryWarehouseName
              : currentRow.outBoundWarehouseName
          }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="调入仓库名称:">
          {{
            tab === 2
              ? currentRow.storageWarehouseName
              : currentRow.inBoundWarehouseName
          }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="单据状态:">
          {{
            tab == 2
              ? currentRow.orderStatus == 3 || currentRow.orderStatus == 7
                ? '已完成'
                : ''
              : currentRow.orderStatus == 4
              ? '已收货'
              : ''
          }}
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="6">
        <el-form-item label="调拨单号:">
          {{
            tab === 2 ? currentRow.transferOrderCode : currentRow.transferCode
          }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="调拨出库单号:">
          {{
            tab === 2
              ? currentRow.transferDeliveryOrderCode
              : currentRow.transferOutCode
          }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="调拨入库单号:">
          {{
            tab === 2
              ? currentRow.transferStorageOrderCode
              : currentRow.transferInCode
          }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="单据类型:">
          {{
            tab == 2
              ? currentRow.transferType == 2
                ? '调拨入库单'
                : currentRow.transferType == 3
                ? '调拨出库单'
                : ''
              : ['越库调拨', '主动调拨', '被动调拨', '手动导入'][
                  currentRow.transferType - 1
                ]
          }}
        </el-form-item>
      </el-col>
    </el-row>
    <el-row v-if="tab === 2">
      <el-col :span="6">
        <el-form-item label="物流公司:">
          {{ currentRow.logisticsCompanyName }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="仓库反馈出库单号:">
          {{ currentRow.deliveryOrderId }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="仓库反馈入库单号:">
          {{ currentRow.entryOrderId }}
        </el-form-item>
      </el-col>
    </el-row>
    <el-row v-if="tab === 2">
      <el-col :span="6">
        <el-form-item label="创建时间:">
          {{ currentRow.gmtCreate }}
        </el-form-item>
      </el-col>

      <el-col :span="6">
        <el-form-item label="入库单完成时间:">
          {{ currentRow.gmtStorageComplete }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="出库单完成时间:">
          {{ currentRow.gmtDeliveryComplete }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="创建人名称:">
          {{ currentRow.createrName }}
        </el-form-item>
      </el-col>
    </el-row>
    <el-row v-if="tab === 2">
      <el-col :span="6">
        <el-form-item label="更新时间:">
          {{ currentRow.gmtModify }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="实际发货时间:">
          {{ currentRow.gmtActualDelivery }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="实际入库时间:">
          {{ currentRow.gmtActualStorage }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="修改人名称:">
          {{ currentRow.modifierName }}
        </el-form-item>
      </el-col>
    </el-row>
    <el-row v-if="tab === 3">
      <el-col :span="6">
        <el-form-item label="物流公司:">
          {{ currentRow.logisticsCompanyName }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="物流单号:">
          {{ currentRow.logisticsOrderCode }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="关联调拨需求单号:">
          {{ currentRow.transferDemandCode }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="创建人名称:">
          {{ currentRow.createrName }}
        </el-form-item>
      </el-col>
    </el-row>
    <el-row v-if="tab === 3">
      <el-col :span="6">
        <el-form-item label="创建时间:">
          {{ currentRow.gmtCreate }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="实际发货时间:">
          {{ currentRow.gmtActualDelivery }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="实际入库时间:">
          {{ currentRow.gmtActualStorage }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="修改人名称:">
          {{ currentRow.modefierName }}
        </el-form-item>
      </el-col>
    </el-row>
    <el-row v-if="tab === 3">
      <el-col :span="6">
        <el-form-item label="业务线:">
          {{
            ['私域调拨', '调往公域', '公域调回'][currentRow.businessType - 1]
          }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="业务类型:">
          {{
            currentRow.tradeType == 1
              ? '库存调拨'
              : currentRow.tradeType == 2
              ? '跨境调拨'
              : ''
          }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="更新时间:">
          {{ currentRow.gmtModify }}
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="6">
        <el-form-item label="备注:">
          {{ currentRow.remark }}
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>
<script>
  export default {
    props: {
      currentRow: {
        type: Object,
        default: () => {},
      },
      tab: {
        type: Number,
        default: 1,
      },
    },
    data() {
      return {};
    },
    created() {},
    methods: {},
  };
</script>
<style lang="scss" scoped></style>
