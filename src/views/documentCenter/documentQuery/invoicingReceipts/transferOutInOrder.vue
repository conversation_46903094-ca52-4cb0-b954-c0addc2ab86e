<template>
  <div v-if="tab === 1" class="transferOutInOrder">
    <el-form ref="search" inline :model="searchParams">
      <el-form-item label="单据状态:">
        <el-select
          v-model="searchParams.orderStatus"
          clearable
          placeholder="请选择"
        >
          <el-option label="已完成" value="3,7"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="单据类型:">
        <el-select
          v-model="searchParams.transferType"
          clearable
          placeholder="请选择"
        >
          <el-option label="调拨入库单" :value="2"></el-option>
          <el-option label="调拨出库单" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="调拨单号:">
        <el-input
          v-model="searchParams.transferOrderCode"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="货主名称:">
        <el-input
          v-model="searchParams.ownerName"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="调入仓库名称:">
        <el-select
          v-model="searchParams.storageWarehouseId"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in shippingWarehouse"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="调出仓库名称:">
        <el-select
          v-model="searchParams.deliveryWarehouseId"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in shippingWarehouse"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="创建时间:">
        <el-date-picker
          v-model="searchParams.gmtCreate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="出库单完成时间:">
        <el-date-picker
          v-model="searchParams.gmtDeliveryComplete"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="入库单完成时间:">
        <el-date-picker
          v-model="searchParams.gmtStorageComplete"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :key="tab"
      :data-source="list"
      :columns="getColumns()"
      :options="{ ...options }"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="transferNum" slot-scope="props">
        <el-popover placement="top" trigger="hover">
          <dynamictable
            :data-source="props.row.transferOutInOrderDetailList || []"
            :columns="getChildColumns()"
            :options="options"
            style="width: 1000px"
          />
          <el-button slot="reference" type="text">
            {{ props.row.transferNum }}
          </el-button>
        </el-popover>
      </template>
      <template slot="operation" slot-scope="props">
        <ac-permission-button
          slot="reference"
          btn-text="详情"
          type="text"
          size="small"
          permission-key=""
          @click="handelJump(2, props.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
  <div v-else-if="tab === 2">
    <div>
      <el-button
        style="font-size: 16px; margin-bottom: 10px"
        type="text"
        @click="handelJump(1)"
      >
        返回
      </el-button>
    </div>
    <el-form>
      <el-divider content-position="left"><h2>基本信息</h2></el-divider>
      <basicTransferOutInOrder
        :current-row="currentRow"
        :tab="tab"
      ></basicTransferOutInOrder>

      <el-divider content-position="left"><h2>收货方信息</h2></el-divider>
      <el-row>
        <el-col :span="6">
          <el-form-item label="收货人:">
            {{ currentRow.receiver }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="收货人国家:">
            {{ currentRow.receiveCountry }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="省:">
            {{ currentRow.receiveProvince }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="市:">
            {{ currentRow.receiveCity }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="区:">
            {{ currentRow.receiveArea }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="街道:">
            {{ currentRow.receiveStreet }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="详细地址:">
            {{ currentRow.receiveAddress }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-divider content-position="left"><h2>调拨详情</h2></el-divider>
    <dynamictable
      :key="tab"
      :data-source="currentRow.transferOutInOrderDetailList || []"
      :columns="getColumns()"
      :options="options"
    ></dynamictable>
  </div>
  <div v-else>
    <div>
      <el-button
        style="font-size: 16px; margin-bottom: 10px"
        type="text"
        @click="handelJump(1)"
      >
        返回
      </el-button>
    </div>
    <el-form>
      <el-divider content-position="left"><h2>基本信息</h2></el-divider>
      <basicTransferOutInOrder
        :current-row="transferDetails"
        :tab="tab"
      ></basicTransferOutInOrder>
    </el-form>
    <el-divider content-position="left"><h2>调拨需求详情</h2></el-divider>
    <dynamictable
      :key="tab"
      :data-source="transferDetails.transferOrderDetailList || []"
      :columns="getColumns()"
      :options="options"
    ></dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    getTransferOutInOrderList,
    getTransferOrderQuery,
    getDicValues,
  } from '@/api/documentCenter';
  import basicTransferOutInOrder from './basicTransferOutInOrder';
  import { parseTime, debounce, initSearchParams } from '@/utils';

  export default {
    components: {
      dynamictable,
      basicTransferOutInOrder,
    },
    data() {
      return {
        tab: 1, // 1 列表  2，详情页
        searchParams: {
          orderStatus: '',
          transferType: '',
          transferOrderCode: '',
          ownerName: '',
          storageWarehouseId: '',
          deliveryWarehouseId: '',
          gmtCreate: '',
          gmtDeliveryComplete: '',
          gmtStorageComplete: '',
        },
        list: [],
        shippingWarehouse: [],
        currentRow: null,
        transferDetails: {},
        options: {
          loading: false,
          border: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },
    created() {
      getDicValues({}).then(res => {
        if (res) {
          this.shippingWarehouse = res.shippingWarehouse;
        }
      });
      // this.getList(true);
    },
    methods: {
      handelJump(val, row) {
        this.tab = val;
        if (val === 1) {
          this.currentRow = null;
        } else {
          this.currentRow = row;
        }
        if (val === 3) {
          this.getTransferOrderQuery();
        }
      },
      getParams() {
        const gmtCreateDate = this.searchParams.gmtCreate;
        const gmtDeliveryCompleteDate = this.searchParams.gmtDeliveryComplete;
        const gmtStorageCompleteDate = this.searchParams.gmtStorageComplete;
        this.searchParams.gmtCreateStart = gmtCreateDate
          ? parseTime(gmtCreateDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.gmtCreateEnd = gmtCreateDate
          ? parseTime(gmtCreateDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.gmtDeliveryCompleteStart = gmtDeliveryCompleteDate
          ? parseTime(gmtDeliveryCompleteDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.gmtDeliveryCompleteEnd = gmtDeliveryCompleteDate
          ? parseTime(gmtDeliveryCompleteDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.gmtStorageCompleteStart = gmtStorageCompleteDate
          ? parseTime(gmtStorageCompleteDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.gmtStorageCompleteEnd = gmtStorageCompleteDate
          ? parseTime(gmtStorageCompleteDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        const {
          gmtCreate,
          gmtDeliveryComplete,
          gmtStorageComplete,
          ...params
        } = this.searchParams;
        const query = {
          ...params,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return query;
      },
      async getTransferOrderQuery() {
        try {
          const { currentRow } = this;
          const res = await getTransferOrderQuery({
            transferCode: currentRow.transferOrderCode,
          });

          if (res) {
            this.transferDetails = res ? res : {};
          }
        } catch (error) {}
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        try {
          const res = await getTransferOutInOrderList(params);
          this.options.loading = false;
          if (res) {
            this.list = res && res.list ? res.list : [];
            this.pagination.total = res ? res.total : 0;
          }
        } catch (error) {
          this.options.loading = false;
        }
      },
      onSearch: debounce(function () {
        const searchParams = initSearchParams(this.getParams());
        if (Object.keys(searchParams).length === 2) {
          return this.$message.warning('请最少选择一个查询条件');
        }

        this.getList(true);
      }, 1000),
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },

      getChildColumns() {
        return [
          {
            prop: 'transferOrderCode',
            label: '调拨单号',
          },
          {
            prop: 'transferStorageOrderCode',
            label: '调拨入库单号',
          },
          {
            prop: 'transferDeliveryOrderCode',
            label: '调拨出库单号',
          },
          {
            prop: 'goodsBarcode',
            label: '商品条码',
          },
          {
            prop: 'goodsName',
            label: '商品名称',
          },
          {
            prop: 'goodsAliasName',
            label: '英文名称',
          },
          {
            prop: 'brandCode',
            label: '品牌CODE',
          },
          {
            prop: 'brandName',
            label: '品牌名称',
          },
          {
            prop: 'planTransferDeliveryQuantity',
            label: '计划调拨出库数量',
          },
          {
            prop: 'actualTransferDeliveryQuantity',
            label: '实际调拨出库数量',
          },
          {
            prop: 'transferStorageQuantity',
            label: '调拨入库数量',
          },

          {
            prop: 'actualQuantity',
            label: '实际上架数量（正品）',
          },
          {
            prop: 'defectiveQuantity',
            label: '次品数量',
          },
          {
            prop: 'lessQuantity',
            label: '少货数量',
          },
          {
            prop: 'inventoryType',
            label: '库存类型',
            render: ({ inventoryType }) => (
              <span>
                {inventoryType == 1 ? '正品' : inventoryType == 2 ? '次品' : ''}
              </span>
            ),
          },
          {
            prop: 'batchId',
            label: '批次属性ID',
          },
          {
            prop: 'batchCode',
            label: '批次库存CODE',
          },
          {
            prop: 'defectiveDescription',
            label: '次品描述',
          },
        ];
      },

      getColumns() {
        const columns = [
          {
            prop: 'ownerName',
            label: '货主名称',
          },
          {
            prop: 'transferOrderCode',
            label: '调拨单号',
            render: row => (
              <a
                onClick={() => {
                  this.handelJump(3, row);
                }}
              >
                {row.transferOrderCode}
              </a>
            ),
          },
          {
            prop: 'transferType',
            label: '单据类型',
            render: ({ transferType }) => (
              <span>
                {transferType == 2
                  ? '调拨入库单'
                  : transferType == 3
                  ? '调拨出库单'
                  : ''}
              </span>
            ),
          },
          {
            prop: 'transferNum',
            label: '调拨数量（良品）',
            scopedSlots: { customRender: 'transferNum' },
          },
          {
            prop: 'waybillCode',
            label: '运单号',
          },
          {
            prop: 'transferStorageOrderCode',
            label: '调拨入库单号',
          },

          {
            prop: 'transferDeliveryOrderCode',
            label: '调拨出库单号',
          },
          {
            prop: 'deliveryWarehouseName',
            label: '调出仓名称',
          },
          {
            prop: 'storageWarehouseName',
            label: '调入仓名称',
          },
          {
            prop: 'orderStatus',
            label: '单据状态',
            render: ({ orderStatus }) => (
              <span>
                {orderStatus == 3 || orderStatus == 7 ? '已完成' : ''}
              </span>
            ),
          },
          {
            prop: 'gmtCreate',
            label: '创建时间',
          },
          {
            prop: 'gmtDeliveryComplete',
            label: '出库单完成时间',
          },
          {
            prop: 'gmtStorageComplete',
            label: '入库单完成时间',
          },
          {
            prop: 'gmtActualDelivery',
            label: '仓库实际出库时间',
          },
          {
            prop: 'gmtActualStorage',
            label: '仓库实际入库时间',
          },
          {
            prop: 'remark',
            label: '备注',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            minWidth: '80px',
            maxWidth: '200px',
            scopedSlots: { customRender: 'operation' },
          },
        ];
        const { tab } = this;
        const columns1 = [
          {
            prop: 'transferOrderCode',
            label: '调拨单号',
          },
          {
            prop: 'transferStorageOrderCode',
            label: '调拨入库单号',
            hide: tab === 3,
          },
          {
            prop: 'transferDeliveryOrderCode',
            label: '调拨出库单号',
            hide: tab === 3,
          },
          {
            prop: 'goodsBarcode',
            label: '商品条码',
          },
          {
            prop: 'goodsName',
            label: '商品名称',
          },
          {
            prop: 'goodsAliasName',
            label: '英文名称',
          },
          {
            prop: 'brandCode',
            label: '品牌CODE',
          },
          {
            prop: 'brandName',
            label: '品牌名称',
          },
          {
            prop:
              tab === 3 ? 'planTransferNum' : 'planTransferDeliveryQuantity',
            label: tab === 3 ? '计划调拨数量' : '计划调拨出库数量',
          },
          {
            prop:
              tab === 3
                ? 'actualTransferNum'
                : 'actualTransferDeliveryQuantity',
            label: tab === 3 ? '实际调拨数量' : '实际调拨出库数量',
          },
          {
            prop: 'transferStorageQuantity',
            label: '调拨入库数量',
            hide: tab === 3,
          },
          {
            prop: 'actualQuantity',
            label: '实际上架数量（正品）',
            hide: tab === 3,
          },
          {
            prop: 'totalReceivedZpNum',
            label: '收货总数量（正品）',
            hide: tab === 2,
          },
          {
            prop: 'defectiveQuantity',
            label: '次品数量',
            hide: tab === 3,
          },
          {
            prop: 'lessQuantity',
            label: '少货数量',
            hide: tab === 3,
          },
          {
            prop: 'inventoryType',
            label: '库存类型',
            render: ({ inventoryType }) => (
              <span>
                {inventoryType == 1 ? '正品' : inventoryType == 2 ? '次品' : ''}
              </span>
            ),
          },
          {
            prop: 'batchId',
            label: '批次属性ID',
            hide: tab === 3,
          },
          {
            prop: 'batchCode',
            label: '批次库存CODE',
            hide: tab === 3,
          },
          {
            prop: 'defectiveDescription',
            label: '次品描述',
            hide: tab === 3,
          },
          {
            prop: 'demandOrderCode',
            label: '库存需求单号',
            hide: tab === 2,
          },
        ];
        return this.tab === 1 ? columns : columns1;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
