<template>
  <div v-if="tab === 1">
    <el-form ref="search" inline :model="searchParams">
      <el-form-item label="单据状态:">
        <el-select
          v-model="searchParams.orderStatus"
          clearable
          placeholder="请选择"
        >
          <el-option label="出库完成" value="2,5,20"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="出库类型:">
        <el-select
          v-model="searchParams.deliveryType"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in purchaseOrderTypeList"
            :key="item.businessOrderTypeCode"
            :label="item.businessOrderTypeName"
            :value="item.businessOrderTypeCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="出库单号:">
        <el-input
          v-model="searchParams.deliveryOrderCode"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="货主名称:">
        <el-input
          v-model="searchParams.ownerName"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="仓库名称:">
        <el-select
          v-model="searchParams.warehouseId"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in shippingWarehouse"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="创建时间:">
        <el-date-picker
          v-model="searchParams.gmtCreate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="实际发货时间:">
        <el-date-picker
          v-model="searchParams.gmtActualDelivery"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :key="tab"
      :data-source="list"
      :columns="getColumns()"
      :options="{ ...options }"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="deliveryNum" slot-scope="props">
        <el-popover placement="top" trigger="hover">
          <dynamictable
            :data-source="props.row.outboundOrderDetailList || []"
            :columns="getChildColumns()"
            :options="options"
          />
          <el-button slot="reference" type="text">
            {{ props.row.deliveryNum }}
          </el-button>
        </el-popover>
      </template>
      <template slot="operation" slot-scope="props">
        <ac-permission-button
          slot="reference"
          btn-text="详情"
          type="text"
          size="small"
          permission-key=""
          @click="handelJump(2, props.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
  <div v-else>
    <div>
      <el-button
        style="font-size: 16px; margin-bottom: 10px"
        type="text"
        @click="handelJump(1)"
      >
        返回
      </el-button>
    </div>
    <el-form>
      <el-divider content-position="left"><h2>基本信息</h2></el-divider>
      <el-row>
        <el-col :span="6">
          <el-form-item label="货主名称:">
            {{ currentRow.ownerName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="供应商名称:">
            {{ currentRow.carrierName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="出库单号:">
            {{ currentRow.deliveryOrderCode }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="出库类型:">
            {{
              getText(
                currentRow.deliveryType,
                purchaseOrderTypeList,
                'businessOrderTypeCode',
                'businessOrderTypeName',
              )
            }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="单据状态:">
            {{
              currentRow.orderStatus == 2 ||
              currentRow.orderStatus == 5 ||
              currentRow.orderStatus == 20
                ? '出库完成'
                : ''
            }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="库存类型:">
            {{
              currentRow.inventoryType == 1
                ? '正品'
                : currentRow.inventoryType == 2
                ? '次品'
                : ''
            }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="创建时间:">
            {{ currentRow.gmtCreate }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="实际发货时间:">
            {{ currentRow.gmtActualDelivery }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="计划发货起始时间:">
            {{ currentRow.gmtPlanSendStart }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="计划发货截止时间:">
            {{ currentRow.gmtPlanSendEnd }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="更新时间:">
            {{ currentRow.gmtModify }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="物流方式:">
            {{ currentRow.logisticsModeCode }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="创建人名称:">
            {{ currentRow.createrName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="修改人名称:">
            {{ currentRow.modifierName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="备注:">
            {{ currentRow.remark }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider content-position="left"><h2>收货方信息</h2></el-divider>
      <el-row>
        <el-col :span="6">
          <el-form-item label="收货人:">
            {{ currentRow.receivePerson }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="收货人国家:">
            {{ currentRow.receiveCountry }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="省:">
            {{ currentRow.receiveProvince }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="市:">
            {{ currentRow.receiveCity }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="区:">
            {{ currentRow.receiveArea }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="街道:">
            {{ currentRow.receiveStreet }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="详细地址:">
            {{ currentRow.receiveAddress }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-divider content-position="left"><h2>出货详情</h2></el-divider>
    <dynamictable
      :key="tab"
      :data-source="currentRow.outboundOrderDetailList || []"
      :columns="getColumns()"
      :options="options"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    getOutboundOrderList,
    getDicValues,
    getDeliveryOrderType,
  } from '@/api/documentCenter';
  import { parseTime, debounce, initSearchParams } from '@/utils';

  export default {
    components: {
      dynamictable,
    },
    data() {
      return {
        tab: 1, // 1 列表  2，详情页
        searchParams: {
          orderStatus: '',
          deliveryType: '',
          deliveryOrderCode: '',
          ownerName: '',
          warehouseId: '',
          gmtCreate: '',
          gmtActualDelivery: '',
        },
        shippingWarehouse: [],
        purchaseOrderTypeList: [],
        list: [],
        currentRow: null,
        options: {
          loading: false,
          border: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },
    created() {
      getDeliveryOrderType({
        businessType: 2,
        // isCreate: false,
      }).then(res => {
        if (res) {
          this.purchaseOrderTypeList = res.concat([
            {
              businessOrderTypeCode: 'yjdfrk',
              businessOrderTypeName: '一件代发',
            },
            {
              businessOrderTypeCode: 'companyTraderk',
              businessOrderTypeName: '公司间交易',
            },
          ]);
        }
      });
      getDicValues({}).then(res => {
        if (res) {
          this.shippingWarehouse = res.shippingWarehouse;
        }
      });
      // this.getList(true);
    },
    methods: {
      getText(val, list, key = 'key', valKeys = 'values') {
        let text = '';
        list.map(item => {
          if (item[key] == val) {
            text = item[valKeys];
          }
        });
        return text;
      },
      handelJump(val, row) {
        this.tab = val;
        if (val === 1) {
          this.currentRow = null;
        } else {
          this.currentRow = row;
        }
      },
      getParams() {
        const gmtCreateDate = this.searchParams.gmtCreate;
        const gmtActualDeliveryDate = this.searchParams.gmtActualDelivery;

        this.searchParams.gmtCreateStart = gmtCreateDate
          ? parseTime(gmtCreateDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.gmtCreateEnd = gmtCreateDate
          ? parseTime(gmtCreateDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.gmtActualDeliveryStart = gmtActualDeliveryDate
          ? parseTime(gmtActualDeliveryDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.gmtActualDeliveryEnd = gmtActualDeliveryDate
          ? parseTime(gmtActualDeliveryDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        const { gmtCreate, gmtActualDelivery, ...params } = this.searchParams;
        const query = {
          ...params,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return query;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        try {
          const res = await getOutboundOrderList(params);
          this.options.loading = false;
          if (res) {
            this.list = res && res.list ? res.list : [];
            this.pagination.total = res ? res.total : 0;
          }
        } catch (error) {
          this.options.loading = false;
        }
      },
      onSearch: debounce(function () {
        const searchParams = initSearchParams(this.getParams());
        if (Object.keys(searchParams).length === 2) {
          return this.$message.warning('请最少选择一个查询条件');
        }
        this.getList(true);
      }, 1000),
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },

      getChildColumns() {
        return [
          {
            prop: 'deliveryOrderCode',
            label: '出库单号',
          },
          // {
          //   prop: 'goodsId',
          //   label: '商品ID',
          // },
          {
            prop: 'goodsBarcode',
            label: '商品条码',
          },
          {
            prop: 'goodsName',
            label: '商品名称',
          },
          {
            prop: 'goodsAliasName',
            label: '英文名称',
          },
          {
            prop: 'brandCode',
            label: '品牌CODE',
          },
          {
            prop: 'brandName',
            label: '品牌名称',
          },
          {
            prop: 'deliveryNum',
            label: '出库数量',
          },
          {
            prop: 'batchId',
            label: '批次属性ID',
          },
          {
            prop: 'batchCode',
            label: '批次库存CODE',
          },
          {
            prop: 'inventoryType',
            label: '库存类型',
            render: ({ inventoryType }) => (
              <span>
                {inventoryType == 1 ? '正品' : inventoryType == 2 ? '次品' : ''}
              </span>
            ),
          },
          {
            prop: 'erpProduceTime',
            label: '生产日期',
          },
          {
            prop: 'defectiveType',
            label: '次品详细信息',
          },
        ];
      },

      getColumns() {
        const columns = [
          {
            prop: 'ownerName',
            label: '货主名称',
          },
          {
            prop: 'deliveryOrderCode',
            label: '出库单号',
          },
          {
            prop: 'deliveryType',
            label: '出库类型',
            render: ({ deliveryType }) => (
              <span>
                {this.getText(
                  deliveryType,
                  this.purchaseOrderTypeList,
                  'businessOrderTypeCode',
                  'businessOrderTypeName',
                )}
              </span>
            ),
          },
          {
            prop: 'deliveryNum',
            label: '出库数量',
            scopedSlots: { customRender: 'deliveryNum' },
          },
          {
            prop: 'warehouseName',
            label: '仓库名称',
          },
          {
            prop: 'orderStatus',
            label: '单据状态',
            render: ({ orderStatus }) => (
              <span>
                {orderStatus == 2 || orderStatus == 5 || orderStatus == 20
                  ? '出库完成'
                  : ''}
              </span>
            ),
          },
          {
            prop: 'gmtCreate',
            label: '创建时间',
          },
          {
            prop: 'gmtActualDelivery',
            label: '实际发货时间',
          },
          {
            prop: 'inventoryType',
            label: '库存类型',
            render: ({ inventoryType }) => (
              <span>
                {inventoryType == 1 ? '正品' : inventoryType == 2 ? '次品' : ''}
              </span>
            ),
          },

          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            minWidth: '80px',
            maxWidth: '200px',
            scopedSlots: { customRender: 'operation' },
          },
        ];
        const columns1 = [
          {
            prop: 'deliveryOrderCode',
            label: '出库单号',
          },
          // {
          //   prop: 'goodsId',
          //   label: '商品ID',
          // },
          {
            prop: 'goodsBarcode',
            label: '商品条码',
          },
          {
            prop: 'goodsName',
            label: '商品名称',
          },
          {
            prop: 'goodsAliasName',
            label: '英文名称',
          },
          {
            prop: 'brandCode',
            label: '品牌CODE',
          },
          {
            prop: 'brandName',
            label: '品牌名称',
          },
          {
            prop: 'deliveryNum',
            label: '出库数量',
          },
          {
            prop: 'batchId',
            label: '批次属性ID',
          },
          {
            prop: 'batchCode',
            label: '批次库存CODE',
          },
          {
            prop: 'inventoryType',
            label: '库存类型',
            render: ({ inventoryType }) => (
              <span>
                {inventoryType == 1 ? '正品' : inventoryType == 2 ? '次品' : ''}
              </span>
            ),
          },
          {
            prop: 'erpProduceTime',
            label: '生产日期',
          },
          {
            prop: 'defectiveType',
            label: '次品详细信息',
          },
        ];
        return this.tab === 1 ? columns : columns1;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
