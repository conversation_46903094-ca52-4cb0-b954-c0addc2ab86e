<template>
  <div>
    <salesOrderList
      v-show="tab === 1"
      :tab="tab"
      :settlement-status="settlementStatus"
      :document-type="documentType"
      :purchase-order-status="purchaseOrderStatus"
      :toc-order-status="tocOrderStatus"
      :tob-order-status="tobOrderStatus"
      :subject-dict="subjectDict"
      :shipping-warehouse="shippingWarehouse"
      :shipping-type="shippingType"
      :user-level="userLevel"
      :trade-type="tradeType"
      @handleDetail="(tab, currentRow) => handelJump(tab, currentRow)"
    ></salesOrderList>
    <salesOrderDetails
      v-if="tab === 2"
      :current-row="currentRow"
      @goBack="handelJump(1)"
    ></salesOrderDetails>
    <salesAbnormalOrder
      v-if="tab === 3"
      :settlement-status="settlementStatus"
      :document-type="documentType"
      :purchase-order-status="purchaseOrderStatus"
      :toc-order-status="tocOrderStatus"
      :tob-order-status="tobOrderStatus"
      :subject-dict="subjectDict"
      :shipping-warehouse="shippingWarehouse"
      :shipping-type="shippingType"
      :user-level="userLevel"
      :trade-type="tradeType"
      :current-info="currentRow"
      :order-type="orderType"
      @goBack="handelJump(1)"
    ></salesAbnormalOrder>
    <performanceDetails
      v-if="tab === 4"
      :tab="tab"
      :current-row="currentRow"
      :brand-type="brandType"
      :settlement-status="settlementStatus"
      :payment-status="paymentStatus"
      :document-type="documentType"
      :sell-warehouse="sellWarehouse"
      :purchase-order-status="purchaseOrderStatus"
      :toc-order-status="tocOrderStatus"
      :tob-order-status="tobOrderStatus"
      :subject-dict="subjectDict"
      :sales-warehouse="salesWarehouse"
      :shipping-warehouse="shippingWarehouse"
      :shipping-type="shippingType"
      :user-level="userLevel"
      :trade-type="tradeType"
      :brand-list="brandList"
      @goBack="handelJump(1)"
    ></performanceDetails>
  </div>
</template>

<script>
  import salesOrderList from './components/salesOrderList.vue';
  import salesOrderDetails from './components/salesOrderDetails.vue';
  import salesAbnormalOrder from './components/salesAbnormalOrder.vue';
  import performanceDetails from './components/performanceDetails.vue';
  import {
    getDicValues,
    getListSelector,
    getQueryBrandListBySupplierId,
  } from '@/api/documentCenter';
  export default {
    components: {
      salesOrderList,
      salesOrderDetails,
      salesAbnormalOrder,
      performanceDetails,
    },
    data() {
      return {
        currentRow: null,
        tab: 1,

        settlementStatus: [],
        paymentStatus: [],
        documentType: [
          {
            key: 6,
            value: 'TOC SO单',
          },
        ],
        orderType: [],
        sellWarehouse: [],
        purchaseOrderStatus: [],
        subjectDict: [],
        supplyDict: [],

        brandList: [],

        shippingType: [], //发货方式数据
        shippingWarehouse: [], //发货仓库数据
        salesWarehouse: [], // 销售方式数据
        tradeType: [], // 贸易类型
        tocOrderStatus: [],
        tobOrderStatus: [],
        userLevel: [], // 用户等级
      };
    },
    created() {
      getQueryBrandListBySupplierId({}).then(res => {
        if (res) {
          this.brandList = res;
        }
      });
      getListSelector({}).then(res => {
        if (res) {
          this.subjectDict = res.subjectDict;
          this.supplyDict = res.supplyDict;
        }
      });
      getDicValues({}).then(res => {
        if (res) {
          this.settlementStatus = res.settlementStatus;
          this.paymentStatus = res.paymentStatus;
          this.documentType = res.documentType;
          this.orderType = res.orderType;
          this.sellWarehouse = res.sellWarehouse;
          this.purchaseOrderStatus = res.purchaseOrderStatus;
          this.shippingWarehouse = res.shippingWarehouse;
          this.salesWarehouse = res.salesWarehouse;
          this.shippingType = res.shippingType;
          this.tradeType = res.tradeType;
          this.tocOrderStatus = res.tocOrderStatus;
          this.tobOrderStatus = res.tobOrderStatus;
          this.brandType = res.brandType;
          this.userLevel = res.userLevel;
        }
      });
    },
    methods: {
      handelJump(tab, currentRow) {
        console.log(currentRow, 'currentRow11');
        if (currentRow) this.currentRow = currentRow;
        this.tab = tab;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
