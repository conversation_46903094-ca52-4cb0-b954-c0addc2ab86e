<template>
  <div class="receiveBill">
    <el-form inline>
      <el-form-item label="单据类型:">
        <el-select v-model="searchParams.type" clearable placeholder="请选择">
          <el-option
            v-if="routeName == 'receiveBill'"
            label="收款单"
            value="C"
          ></el-option>
          <el-option
            v-if="routeName == 'payBill'"
            label="付款单"
            value="P"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="routeName == 'receiveBill' ? '收款单号:' : '付款单号:'"
      >
        <el-input v-model="searchParams.no" clearable placeholder="请输入" />
      </el-form-item>
      <!-- <el-form-item label="账单编号:">
        <el-input
          v-model="searchParams.billNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item> -->
      <el-form-item label="请款单号:">
        <el-input
          v-model="searchParams.requisitionNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="创建时间:">
        <el-date-picker
          v-model="createDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="更新时间:">
        <el-date-picker
          v-model="updateDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="完成时间:">
        <el-date-picker
          v-model="completionDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="收款方名称:">
        <!-- <el-select
          v-model="searchParams.ccCompanyName"
          v-selectLoadMore="() => loadmore(1)"
          filterable
          :filter-method="val => searchCcCompanyName(val, 1)"
        >
          <el-option
            v-for="item in ccCompanyList"
            :key="item.id"
            :label="item.channelName"
            :value="item.channelName"
          ></el-option>
        </el-select> -->
        <el-select v-model="searchParams.ccCompanyName" clearable filterable>
          <el-option
            v-for="item in ccCompanyList"
            :key="item"
            :label="item"
            :value="item"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="付款方名称:">
        <!-- <el-select
          v-model="searchParams.pmCompanyName"
          v-selectLoadMore="() => loadmore(2)"
          filterable
          :filter-method="val => searchCcCompanyName(val, 2)"
        >
          <el-option
            v-for="item in pmCompanyList"
            :key="item.id"
            :label="item.channelName"
            :value="item.channelName"
          ></el-option>
        </el-select> -->
        <el-select v-model="searchParams.pmCompanyName" clearable filterable>
          <el-option
            v-for="item in pmCompanyList"
            :key="item"
            :label="item"
            :value="item"
          ></el-option>
        </el-select>
        <!-- <selectSearchLoadMore></selectSearchLoadMore> -->
      </el-form-item>

      <el-form-item
        :label="routeName == 'receiveBill' ? '收款状态:' : '付款状态:'"
      >
        <el-select v-model="searchParams.status" clearable placeholder="请选择">
          <el-option
            :label="routeName == 'receiveBill' ? '未收款' : '未付款'"
            value="U"
          ></el-option>
          <el-option
            :label="routeName == 'receiveBill' ? '部分收款' : '部分付款'"
            value="P"
          ></el-option>
          <el-option
            :label="routeName == 'receiveBill' ? '已收款' : '已付款'"
            value="S"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="对账状态:">
        <el-select
          v-model="searchParams.checkStatus"
          clearable
          placeholder="请选择"
        >
          <el-option label="未对账" value="U"></el-option>
          <el-option label="已对账" value="S"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item
        :label="routeName == 'receiveBill' ? '收款方式:' : '付款方式:'"
      >
        <el-select v-model="searchParams.method" clearable placeholder="请选择">
          <el-option label="账扣" value="D"></el-option>
          <el-option label="现金" value="C"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button
          v-if="routeName === 'payBill'"
          type="primary"
          @click="onExport"
        >
          导出
        </el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          :disabled="scope.row.status === 'U' || scope.row.method === 'D'"
          btn-text="查看凭证"
          type="text"
          size="small"
          permission-key=""
          @click="handleShowVoucher(scope.row)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="routeName === 'payBill'"
          slot="reference"
          :disabled="scope.row.status === 'S'"
          btn-text="付款确认"
          type="text"
          size="small"
          permission-key=""
          @click="handleOk(scope.row)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="routeName === 'receiveBill'"
          slot="reference"
          :disabled="!['U', 'P'].includes(scope.row.status)"
          btn-text="收款确认"
          type="text"
          size="small"
          permission-key=""
          @click="collectionConfirmation(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>

    <el-dialog
      title="查看凭证"
      :visible.sync="imgVisible"
      width="700px"
      @closed="imgList = []"
    >
      <div
        v-loading="imgLoading"
        style="display: flex; align-items: center; justify-content: center"
      >
        <a v-for="item in imgList" :key="item" :href="item" target="_blank">
          <el-image
            :src="item"
            style="
              width: 300px;
              height: 300px;
              margin: 10px;
              border: 1px solid #e5e5e5;
            "
          ></el-image>
        </a>
      </div>
      <el-pagination
        background
        layout="prev, pager, next"
        :current-page.sync="showVoucherSearch.pageNo"
        :page-size="showVoucherSearch.limit"
        :total="showVoucherSearch.total"
        @current-change="handleIndexChange"
      ></el-pagination>
    </el-dialog>

    <el-dialog
      title="付款确认"
      width="600px"
      :visible.sync="visible"
      @closed="onClose"
    >
      <el-form
        ref="formData"
        :model="form"
        label-position="right"
        label-width="120px"
      >
        <el-form-item
          prop="returnNo"
          :rules="[
            {
              required: true,
              message: '请输入回执单号',
              trigger: 'change',
            },
          ]"
          label="回执单号:"
        >
          <el-input
            v-model="form.returnNo"
            style="width: 300px"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item
          prop="amount"
          :rules="[
            {
              required: true,
              message: '请输入付款金额',
              trigger: 'change',
            },
          ]"
          label="本次付款金额:"
        >
          <el-input
            v-model="form.amount"
            style="width: 300px"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item
          prop="date"
          :rules="[
            {
              required: true,
              message: '请选择银行出款时间',
              trigger: 'change',
            },
          ]"
          label="银行出款时间:"
        >
          <el-date-picker
            v-model="form.date"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item required :error="errorTip" label="付款凭证:">
          <uploadImg
            ref="uploadImg"
            :max="1"
            :size="2"
            accept=".gif, .png, .jpg"
            @onRemove="onRemove"
            @changeImage="changeImage"
          />
          <div>上传付款凭证(GIF/PNG/JPG),单张大小不超过2M</div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
        <el-button @click="resetForm('formData')">重 置</el-button>
        <el-button type="primary" @click="handleSubmit">确 认</el-button>
      </div>
    </el-dialog>
    <uploadVoucherModal
      v-model="showUploadVoucherModal"
      :record="record"
      :subject-dict="subjectDict"
      :current-info-list="currentInfoList"
      success-tips="确认收款成功"
      @confirm="getList"
    ></uploadVoucherModal>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import selectSearchLoadMore from '@/components/selectSearchLoadMore';
  import uploadImg from '@/components/uploadImg';
  import {
    creditAndPaymentDocCompositeList,
    getFuzzySearchList,
    paymentConfirm,
    creditAndPaymentDocCompositeShowVoucher,
  } from '@/api/documentCenter';
  import { getSupplier, billReceived } from '@/api/billManagement';
  import { newExportExcel } from '@/api/blob';
  import { subtraction } from '@/utils/math';
  import uploadVoucherModal from '../../copingManagement/bill/components/uploadVoucherModal';
  import {
    parseTime,
    debounce,
    initSearchParams,
    setInitData,
    downloadFile,
  } from '@/utils';
  export default {
    components: {
      dynamictable,
      uploadImg,
      uploadVoucherModal,
      // selectSearchLoadMore,
    },
    data() {
      return {
        showUploadVoucherModal: false,
        record: null,
        subjectDict: [], // 签约主体
        currentInfoList: [], // 详情凭证List
        completionDate: '',
        updateDate: '',
        createDate: '',
        routeName: '',
        visible: false,
        imgVisible: false,
        imgLoading: false,
        form: {
          amount: '',
          processableNo: '',
          returnNo: '',
          date: '',
          voucher: [],
        },
        imgList: [],
        ccCompanyList: [],
        pmCompanyList: [],
        statusTextObj: {
          receiveBill: {
            U: '未收款',
            P: '部分收款',
            S: '已收款',
          },
          payBill: {
            U: '未付款',
            P: '部分付款',
            S: '已付款',
          },
        },
        selectParams: {
          limit: 200,
          pageNo: 1,
          pageType: '',
          queryType: '',
          text: '',
        },
        errorTip: '',
        searchParams: {
          returnNo: '',
          billNo: '',
          ccCompanyName: '',
          checkStatus: '',
          completeTimeEnd: '',
          completeTimeStart: '',
          createTimeEnd: '',
          createTimeStart: '',
          method: '',
          no: '',
          pmCompanyName: '',
          requisitionNo: '',
          status: '',
          type: '',
          updateTimeEnd: '',
          updateTimeStart: '',
        },
        showVoucherSearch: {
          limit: 1,
          pageNo: 1,
          total: 0,
          serialNo: '',
          type: '',
        },
        list: [],
        options: {
          loading: false,
          border: true,
          // showSummary: true,
        },
        pageSize: 1,
        currentRow: null,
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },
    watch: {
      $route: {
        handler(val, oldval) {
          const { name } = val;
          if (name !== oldval.name) {
            this.routeName = name;
            this.onReset();
            this.getList(true);
          }
        },
        // 深度观察监听
        deep: true,
      },
    },
    created() {
      this.routeName = this.$route.name;
      this.searchParams.type = this.routeName === 'receiveBill' ? 'C' : 'P';
      this.createDate = setInitData(90);
      this.getSupplier();
      const billNo = this.$route.query.billNo;
      if (billNo) this.searchParams.billNo = billNo;
      this.getList(true);
      this.creditAndPaymentDocCompositeList(1);
      this.creditAndPaymentDocCompositeList(2);
    },
    methods: {
      async collectionConfirmation(record) {
        this.$confirm('点击确认表示此款项已付，请选择！', '收款确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          await billReceived({ billNo: record.billNo });
          this.getList();
        });
        // const res = await creditAndPaymentDocCompositeShowVoucher({
        //   limit: 100,
        //   pageNo: 1,
        //   serialNo: record.processableNo,
        //   type: record.documentType,
        // });
        // this.showUploadVoucherModal = true;
        // this.record = {
        //   ...this.record,
        //   companyCode: record.ccCode,
        //   itemNo: record.processableNo,
        // };
        // this.currentInfoList = res ? res.list : [];
      },
      // 供应商和主体列表
      async getSupplier() {
        let res = await getSupplier();
        if (res) {
          this.subjectDict = res.subjectDict;
        }
      },
      trim(str) {
        let result;
        if (str) {
          result = str.replace(/\s/g, ''); // 去除字符串全部空格
        }
        return result ? result : '';
      },
      resetForm(formName) {
        this.onClose();
        if (this.currentRow)
          this.form.processableNo = this.currentRow.processableNo;
        // this.$refs.uploadImg.clearFiles();
        // this.errorTip = '';
        // this.$refs[formName].resetFields();
      },
      loadmore(type) {
        this.selectParams.pageNo++;
        this.creditAndPaymentDocCompositeList(type);
      },
      searchCcCompanyName(val, type) {
        this.selectParams.pageNo = 1;
        this.selectParams.text = val;
        this.creditAndPaymentDocCompositeList(type);
      },
      async creditAndPaymentDocCompositeList(type) {
        const params = {
          ...this.selectParams,
          pageType: this.routeName === 'receiveBill' ? 'C' : 'P',
          queryType: type === 1 ? 'C' : 'P',
        };
        const res = await getFuzzySearchList(params);
        if (res) {
          if (type === 1) {
            this.ccCompanyList = res && res.list ? res.list : [];
          } else {
            this.pmCompanyList = res && res.list ? res.list : [];
          }
        }
      },

      onExport: debounce(function () {
        const params = this.getParams();
        newExportExcel(
          params,
          `/api/finance-document/creditAndPaymentDocComposite/export`,
          'get',
        ).then(res => {
          downloadFile(res.data, '付款单列表');
        });
      }, 5000),
      onRemove(_, fileList) {
        if (fileList.length === 0) {
          this.errorTip = '请上传付款凭证';
        }
        this.form.voucher = fileList.map(item => item.id);
      },
      changeImage(_, fileList) {
        this.form.voucher = fileList.map(item => item.id);
        this.errorTip = '';
      },
      handleSubmit: debounce(function () {
        this.$refs.formData.validate(async valid => {
          if (!this.form.voucher.length) {
            this.errorTip = '请上传付款凭证';
            return;
          }
          if (!valid) return;

          this.$confirm('确定上传凭证？')
            .then(async _ => {
              await paymentConfirm(this.form);
              this.$message.success('付款确认成功');
              this.visible = false;
              this.getList();
            })
            .catch(_ => {});
        });
      }, 1000),
      onClose() {
        Object.assign(this.$data.form, this.$options.data().form);
        this.$nextTick(function () {
          this.$refs.uploadImg.clearFiles();
          this.errorTip = '';
          this.$refs.formData.clearValidate();
        });
      },
      // 查看凭证分页
      handleIndexChange(current) {
        this.showVoucherSearch.pageNo = current;
        this.creditAndPaymentDocCompositeShowVoucher();
      },
      // 查看凭证
      async handleShowVoucher({ processableNo, documentType }) {
        this.imgVisible = true;
        this.showVoucherSearch.serialNo = processableNo;
        this.showVoucherSearch.type = documentType;
        this.creditAndPaymentDocCompositeShowVoucher();
      },
      // 凭证列表
      async creditAndPaymentDocCompositeShowVoucher() {
        const { total, ...params } = this.showVoucherSearch;
        this.imgLoading = true;
        const res = await creditAndPaymentDocCompositeShowVoucher({
          ...params,
        });
        this.imgLoading = false;

        if (res) {
          this.showVoucherSearch.total = res.total;
          if (Array.isArray(res.list) && res.list.length)
            this.imgList = res.list[0]['images'];
        }
      },
      handleOk(row) {
        this.currentRow = row;
        this.form.processableNo = row.processableNo;
        this.form.amount = subtraction(row.amount, row.passAmount);
        this.visible = true;
      },
      getParams() {
        this.searchParams.createTimeStart = this.createDate
          ? parseTime(this.createDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.createTimeEnd = this.createDate
          ? parseTime(this.createDate[1], '{y}-{m}-{d} 23:59:59')
          : '';

        this.searchParams.updateTimeStart = this.updateDate
          ? parseTime(this.updateDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.updateTimeEnd = this.updateDate
          ? parseTime(this.updateDate[1], '{y}-{m}-{d} 23:59:59')
          : '';

        this.searchParams.completeTimeStart = this.completionDate
          ? parseTime(this.completionDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.completeTimeEnd = this.completionDate
          ? parseTime(this.completionDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        const params = {
          ...this.searchParams,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await creditAndPaymentDocCompositeList(params);
        this.options.loading = false;
        this.list = res && res.list ? res.list : [];
        this.pagination.total = res ? res.total : 0;
      },
      onSearch: debounce(function () {
        this.getList(true);
      }, 1000),
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.searchParams.type = this.routeName === 'receiveBill' ? 'C' : 'P';
        this.createDate = setInitData(90);
        this.updateDate = '';
        this.completionDate = '';
      },
      handleDetail(id, name, router) {
        this.$router.push({
          path: router,
          query: {
            [name]: id,
          },
        });
      },
      getColumns() {
        const routeName = this.routeName;
        const columns = [
          {
            prop: 'no',
            label: routeName === 'receiveBill' ? '收款单号' : '付款单号',
          },
          {
            prop: 'billNo',
            label: '账单编号',
            render: ({ billNo = [] }) => (
              <div>
                {Array.isArray(billNo) && billNo.length
                  ? billNo.map((item, index) => {
                      return (
                        <a
                          onClick={() => {
                            this.handleDetail(
                              item,
                              'billNo',
                              '/copingManagement/bill/billQuery',
                            );
                          }}
                        >
                          {`${item}${
                            index !== billNo.length - 1 ? ' | ' : ''
                          } `}
                        </a>
                      );
                    })
                  : ''}
              </div>
            ),
            // render: ({ billNo }) => (
            //   <a
            //     onClick={() =>
            //       this.handleDetail(
            //         billNo,
            //         'billNo',
            //         '/copingManagement/bill/billQuery',
            //       )
            //     }
            //   >
            //     {billNo}
            //   </a>
            // ),
          },
          {
            prop: 'requisitionNo',
            label: '请款单号',
            render: ({ requisitionNo }) => (
              <a
                onClick={() =>
                  this.handleDetail(
                    requisitionNo,
                    'requisitionNo',
                    '/copingManagement/bill/pleasePayQuery',
                  )
                }
              >
                {requisitionNo}
              </a>
            ),
          },
          // {
          //   prop: 'returnNo',
          //   label: '回执单号',
          //   hide: routeName === 'receiveBill',
          // },
          {
            prop: 'checkStatus',
            label: '对账状态',
            render: ({ checkStatus }) => (
              <span>{checkStatus === 'U' ? '未对账' : '已对账'}</span>
            ),
          },
          {
            prop: 'status',
            label: routeName === 'receiveBill' ? '收款状态' : '付款状态',
            render: ({ status = '' }) => (
              <span>{this.statusTextObj[routeName][status]}</span>
            ),
          },
          {
            prop: 'method',
            label: routeName === 'receiveBill' ? '收款方式' : '付款方式',
            render: ({ method }) => (
              <span>{method === 'C' ? '现金' : '账扣'}</span>
            ),
          },
          {
            prop: 'ccCompanyName',
            label: '收款方名称',
          },
          {
            prop: 'ccBankOpenName',
            label: '收款方开户行名称',
          },
          {
            prop: 'ccBankBranchName',
            label: '收款方支行名称',
          },
          {
            prop: 'ccBankNo',
            label: '收款方账户',
          },
          {
            prop: 'ccBankName',
            label: '收款账户名称',
          },
          {
            prop: 'pmCompanyName',
            label: '付款方名称',
          },
          {
            prop: 'pmBankOpenName',
            label: '付款方开户行名称',
          },
          {
            prop: 'pmBankBranchName',
            label: '付款方支行名称',
          },
          {
            prop: 'pmBankNo',
            label: '付款方账户',
          },
          {
            prop: 'currencyUnit',
            label: '币种',
          },
          {
            prop: 'amount',
            label: routeName === 'receiveBill' ? '应收金额' : '应付金额',
          },
          {
            prop: 'passAmount',
            label: routeName === 'receiveBill' ? '已收金额' : '已付金额',
          },
          {
            prop: 'uncollectedAmount',
            label: routeName === 'receiveBill' ? '未收金额' : '未付金额',
          },
          {
            prop: 'documentType',
            label: '单据类型',
            render: ({ documentType }) => (
              <span>{documentType === 'C' ? '收款单' : '付款单'}</span>
            ),
          },
          {
            prop: 'createTime',
            label: '创建时间',
          },
          {
            prop: 'updateTime',
            label: '更新时间',
          },
          {
            prop: 'completeTime',
            label: '完成时间',
          },
          // {
          //   prop: 'operation',
          //   label: '操作',
          //   fixed: 'right',
          //   width: '100',
          //   scopedSlots: { customRender: 'operation' },
          // },
        ];

        return columns;
      },
    },
  };
</script>
<style lang="scss" scoped>
  .receiveBill {
    /deep/.el-table a {
      cursor: pointer;
    }
  }
</style>
