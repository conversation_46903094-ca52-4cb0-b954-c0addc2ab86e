<template>
  <div class="order-abnormal-data-details">
    <div>
      <el-button
        style="font-size: 16px; margin-bottom: 10px"
        type="text"
        @click="handelJump(1)"
      >
        返回
      </el-button>
    </div>
    <el-form>
      <el-divider content-position="left"><h2>购销方信息</h2></el-divider>
      <el-row>
        <el-col :span="6">
          <el-form-item label="采购主体:">
            {{ currentRow.purchasePrincipal.companyName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="供应商名称:">
            {{ currentRow.supplierPrincipal.companyName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider content-position="left"><h2>订单信息</h2></el-divider>
      <el-row>
        <el-col :span="6">
          <el-form-item label="订单编号:">
            {{ currentRow.orderNo }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="订单名称:">
            {{ currentRow.orderTitle }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="订单类型:">
            {{ getText(currentRow.orderType, orderType) }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="品牌名称:">
            {{ currentRow.brand.brandName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="实际采购数量:">
            {{ currentRow.purchaseQuantity }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="结算币种:">
            {{ currentRow.settleCurrency }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="记账币种:">
            {{ currentRow.accountCurrency }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="采购金额(含税):">
            {{ currentRow.purchaseAmountTaxInclusive }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="采购金额(未税):">
            {{ currentRow.purchaseAmountTaxExclusive }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="税额:">{{ currentRow.tax }}</el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="订单状态:">
            {{
              getText(
                currentRow.docBaseDTO && currentRow.docBaseDTO.status,
                purchaseOrderStatus,
              )
            }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="渠道:">
            {{ currentRow.channelName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="入库销售仓库:">
            {{ currentRow.sellWarehouse.warehouseName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="下单时间:">
            {{ currentRow.orderTime }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="下单人:">
            {{ currentRow.purchaser }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider content-position="left"><h2>异常详情</h2></el-divider>
      <el-row>
        <el-form-item label="异常类型:">
          {{ currentRow.errorRes ? currentRow.errorRes.type : '' }}
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="异常内容:">
          {{ currentRow.errorRes ? currentRow.errorRes.content : '' }}
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="是否处理:">
          {{
            currentRow.errorRes ? (currentRow.errorRes.fixed ? '是' : '否') : ''
          }}
        </el-form-item>
      </el-row>
    </el-form>
    <el-divider content-position="left"><h2>处理日志</h2></el-divider>
    <el-row v-for="item in currentRow.events || []" :key="item" :gutter="12">
      <el-col :span="12">
        <el-card shadow="always">
          <div class="card-body">
            <div class="card-body-top">
              <h3>{{ item.content }}</h3>
              <span>{{ item.type }}</span>
            </div>
            <div>{{ item.creatTime }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  export default {
    props: {
      currentRow: {
        type: Object,
        default: () => {},
      },
      orderType: {
        type: Array,
        default: () => [],
      },
      purchaseOrderStatus: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      console.log(this.currentRow, 'currentRow1234567890');
      return {
        list: [1, 2],
      };
    },
    created() {},
    methods: {
      getText(val, list) {
        let text = '';
        list.map(item => {
          if (item.key == val) {
            text = item.value;
          }
        });
        return text;
      },
      handelJump() {
        this.$emit('goBack');
      },
    },
  };
</script>

<style lang="scss" scoped>
  .order-abnormal-data-details {
    .card-body .card-body-top {
      display: flex;
      align-items: center;
      > h3 {
        margin-right: 10px;
      }
    }
  }
</style>
