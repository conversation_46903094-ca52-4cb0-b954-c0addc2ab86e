<template>
  <div class="searchParams">
    <el-form ref="search" inline :model="searchParams">
      <el-form-item v-if="tab !== 4" label="单据类型:">
        <el-select
          v-model="searchParams.docType"
          filterable
          placeholder="请选择"
          @change="handleChangeDocType"
        >
          <template v-for="item in documentType">
            <el-option
              v-if="[6, 7, 9, 10].includes(item.key)"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            ></el-option>
          </template>
        </el-select>
      </el-form-item>
      <el-form-item label="订单状态:">
        <el-select
          v-model="searchParams.orderStatus"
          clearable
          placeholder="请选择"
        >
          <template
            v-for="item in [6, 7].includes(searchParams.docType)
              ? tocOrderStatus
              : tobOrderStatus"
          >
            <el-option
              v-if="
                [6, 9].includes(searchParams.docType)
                  ? item.key < 100
                  : searchParams.docType === 7
                  ? item.key >= 100 && item.key < 105
                  : item.key >= 100
              "
              :key="item.key"
              :label="item.value"
              :value="item.key"
            ></el-option>
          </template>
        </el-select>
      </el-form-item>
      <el-form-item label="下单时间:">
        <el-date-picker
          v-model="orderDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item
        v-if="[6, 7].includes(searchParams.docType)"
        label="支付完成时间:"
      >
        <el-date-picker
          v-model="paymentDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="发货时间:">
        <el-date-picker
          v-model="deliveryDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item
        v-if="[6, 7].includes(searchParams.docType)"
        label="签收时间:"
      >
        <el-date-picker
          v-model="signDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="完成时间:">
        <el-date-picker
          v-model="finishDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="取消时间:">
        <el-date-picker
          v-model="cancelDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="销售主体:">
        <el-select
          v-model="searchParams.salesEntityCode"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in subjectDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item
        v-if="[6, 7].includes(searchParams.docType)"
        label="销售渠道:"
      >
        <el-select
          v-model="searchParams.salesChannelCode"
          clearable
          placeholder="请选择"
        >
          <el-option label="VTN平台" value="VTN"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="[6, 7].includes(searchParams.docType)"
        label="销售店铺:"
      >
        <el-select
          v-model="searchParams.salesShopCode"
          clearable
          placeholder="请选择"
        >
          <el-option label="VTN商城" value="VTN"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="发货仓库:">
        <el-select
          v-model="searchParams.warehouseCode"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in shippingWarehouse"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item v-if="tab === 4" label="贸易类型:">
        <el-select
          v-model="searchParams.tradeType"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in tradeType"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item
        v-if="[6, 7].includes(searchParams.docType)"
        label="下单用户IDcode:"
      >
        <el-input
          v-model="searchParams.idCode"
          clearable
          placeholder="请输入"
        />
      </el-form-item>

      <el-form-item
        v-if="[6, 7].includes(searchParams.docType)"
        label="下单用户等级:"
      >
        <el-select
          v-model="searchParams.userLevel"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in userLevel"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="[6].includes(searchParams.docType) && tab !== 4"
        label="发货方式:"
      >
        <el-select
          v-model="searchParams.shippingType"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in shippingType"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="销售单号(售后单号):">
        <el-input
          v-model="searchParams.serialNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="原销售单号:">
        <el-input
          v-model="searchParams.originSalesNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item
        v-if="[6, 7].includes(searchParams.docType)"
        label="合并单号:"
      >
        <el-input v-model="searchParams.paySn" clearable placeholder="请输入" />
      </el-form-item>
      <el-form-item v-if="tab === 4" label="出库单号:">
        <el-input
          v-model="searchParams.pushOrderSn"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item v-if="tab === 4" label="物流单号:">
        <el-input
          v-model="searchParams.logisticsSn"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item
        v-if="[9, 10].includes(searchParams.docType)"
        label="关联采购单号:"
      >
        <el-input
          v-model="searchParams.relatedPurchaseDocNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>

      <el-form-item
        v-if="[9, 10].includes(searchParams.docType)"
        label="客户名称:"
      >
        <el-input
          v-model="searchParams.customer"
          clearable
          placeholder="请输入"
        />
      </el-form-item>

      <el-form-item
        v-if="[6, 7].includes(searchParams.docType) && tab !== 4"
        label="预收结算状态:"
      >
        <el-select
          v-model="searchParams.preRevenueSettleStatus"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in settlementStatus"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="收入结算状态:">
        <el-select
          v-model="searchParams.revenueSettleStatus"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in settlementStatus"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item v-if="tab === 4" label="品牌类型:">
        <el-select
          v-model="searchParams.brandType"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in brandType"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item v-if="tab === 4" label="品牌名称:">
        <el-select
          v-model="searchParams.goodsName"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in brandList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item v-if="tab === 4" label="商品条码:">
        <el-input
          v-model="searchParams.barcode"
          clearable
          placeholder="请输入"
        />
      </el-form-item>

      <el-form-item v-if="tab === 4" label="出库时间:">
        <el-date-picker
          v-model="outStockDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item v-if="tab === 4" label="包裹签收时间:">
        <el-date-picker
          v-model="parcelReceiptDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>

      <el-form-item
        v-if="[9, 10].includes(searchParams.docType)"
        label="是否关联交易:"
      >
        <el-select
          v-model="searchParams.relatedTrade"
          clearable
          placeholder="请选择"
        >
          <el-option label="是" :value="1"></el-option>
          <el-option label="否" :value="0"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button v-if="[4].includes(tab)" type="primary" @click="goBack">
          返回
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  export default {
    model: {
      prop: 'searchParams',
      event: 'change',
    },
    props: {
      // searchParams.docType 6 toc 正向,7toc逆向,9tob正向,10tob逆向
      searchParams: {
        type: Object,
        default: () => {},
      },
      tab: {
        type: Number,
        default: 1,
      },
      // 结算状态下拉数据
      settlementStatus: {
        type: Array,
        default: () => [],
      },
      // 发货方式数据
      shippingType: {
        type: Array,
        default: () => [],
      },
      // 发货仓库数据
      shippingWarehouse: {
        type: Array,
        default: () => [],
      },
      // 贸易类型
      tradeType: {
        type: Array,
        default: () => [],
      },
      // 单据类型
      documentType: {
        type: Array,
        default: () => [],
      },
      // 订单状态
      purchaseOrderStatus: {
        type: Array,
        default: () => [],
      },
      // 品牌列表
      brandList: {
        type: Array,
        default: () => [],
      },
      subjectDict: {
        type: Array,
        default: () => [],
      },
      tocOrderStatus: {
        type: Array,
        default: () => [],
      },
      tobOrderStatus: {
        type: Array,
        default: () => [],
      },
      brandType: {
        type: Array,
        default: () => [],
      },
      userLevel: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        outStockDate: '',
        parcelReceiptDate: '',
        orderDate: '',
        paymentDate: '',
        deliveryDate: '',
        signDate: '',
        finishDate: '',
        cancelDate: '',
      };
    },
    created() {
      console.log(this.tocOrderStatus, this.tobOrderStatus, 'tobOrderStatus');
    },
    methods: {
      handleChangeDocType() {
        this.$emit('handleChangeDocType');
      },
      onSearch() {
        const {
          orderDate,
          paymentDate,
          deliveryDate,
          signDate,
          finishDate,
          cancelDate,
          outStockDate,
          parcelReceiptDate,
        } = this;
        const date = {
          orderDate,
          paymentDate,
          deliveryDate,
          signDate,
          finishDate,
          cancelDate,
          outStockDate,
          parcelReceiptDate,
        };
        this.$emit('onSearch', date);
      },
      onReset() {
        Object.assign(this.$data, this.$options.data());
        this.$emit('onReset');
      },
      goBack() {
        this.$emit('goBack');
      },
    },
  };
</script>

<style lang="scss"></style>
