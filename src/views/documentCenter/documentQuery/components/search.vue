<template>
  <div class="searchParams">
    <el-form ref="search" inline :model="searchParams">
      <el-form-item label="下单时间:">
        <el-date-picker
          v-model="orderDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :picker-options="pickerOptions"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="订单状态:">
        <el-select
          v-model="searchParams.orderStatus"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in purchaseOrderStatus"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发货状态:">
        <el-select
          v-model="searchParams.deliveryStatus"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in deliveryStatus"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="收货状态:">
        <el-select
          v-model="searchParams.receiveStatus"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in receivedStatus"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="结算状态:">
        <el-select
          v-model="searchParams.settlementStatus"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in settlementStatus"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="付款状态:">
        <el-select
          v-model="searchParams.paymentStatus"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in paymentStatus"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="采购主体:">
        <el-select
          v-model="searchParams.purchasePrincipal"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in subjectDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="供应商名称:">
        <el-select
          v-model="searchParams.supplierPrincipal"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in supplyDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="采购品牌:">
        <el-select
          v-model="searchParams.brandId"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in brandList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="采购单号:">
        <el-input
          v-model="searchParams.purchaseNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="合同编号:">
        <el-input
          v-model="searchParams.contractNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="业务线:">
        <el-select
          v-model="searchParams.bizLine"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in bizLineId"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="tab === 2" label="商品条码:">
        <el-input
          v-model="searchParams.barcode"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item v-if="tab === 2" label="商品中文名称:">
        <el-input v-model="searchParams.title" clearable placeholder="请输入" />
      </el-form-item>
      <el-form-item label="关联采购单号:">
        <el-input
          v-model="searchParams.relatedPurchaseOrderNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="关联销售单号:">
        <el-input
          v-model="searchParams.relatedSellOrderNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="单据类型:">
        <el-select v-model="searchParams.docType" placeholder="请选择">
          <!-- <el-option
            v-for="item in documentType"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option> -->
          <el-option label="采购订单" :value="1"></el-option>
          <el-option label="采购订单(SO转单)" :value="4"></el-option>
          <!-- <el-option label="采购调整单" :value="2"></el-option> -->
          <el-option label="采购退供单(逆向采购单)" :value="11"></el-option>
          <el-option label="逆向采购单(SO转单)" :value="5"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="订单类型:">
        <el-select
          v-model="searchParams.orderType"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in orderType"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="销售仓库:">
        <el-select
          v-model="searchParams.sellWarehouseId"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in sellWarehouse"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="tab === 2" label="商品英文名称:">
        <el-input
          v-model="searchParams.englishTitle"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import { getDicValues, getListSelector } from '@/api/documentCenter';

  export default {
    model: {
      prop: 'searchParams',
      event: 'change',
    },
    props: {
      searchParams: {
        type: Object,
        default: () => {},
      },
      tab: {
        type: Number,
        default: 1,
      },
      bizLineId: {
        type: Array,
        default: () => [],
      },
      deliveryStatus: {
        type: Array,
        default: () => [],
      },
      receivedStatus: {
        type: Array,
        default: () => [],
      },
      settlementStatus: {
        type: Array,
        default: () => [],
      },
      paymentStatus: {
        type: Array,
        default: () => [],
      },
      documentType: {
        type: Array,
        default: () => [],
      },
      orderType: {
        type: Array,
        default: () => [],
      },
      sellWarehouse: {
        type: Array,
        default: () => [],
      },
      purchaseOrderStatus: {
        type: Array,
        default: () => [],
      },
      subjectDict: {
        type: Array,
        default: () => [],
      },
      supplyDict: {
        type: Array,
        default: () => [],
      },
      brandList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        orderDate: '',
        copyOrderDate: '',
        pickerOptions: {
          onPick: ({ maxDate, minDate }) => {
            this.copyOrderDate = minDate.getTime();
            if (maxDate) {
              this.copyOrderDate = '';
            }
          },
          disabledDate: time => {
            if (this.copyOrderDate !== '') {
              const one = 30 * 24 * 3600 * 1000;
              const minTime = this.copyOrderDate - one;
              const maxTime = this.copyOrderDate + one;
              return time.getTime() < minTime || time.getTime() > maxTime;
            }
          },
        },
      };
    },
    created() {},
    methods: {
      onSearch() {
        this.$emit('onSearch', this.orderDate);
      },
      onReset() {
        this.orderDate = '';
        this.$emit('onReset');
      },
    },
  };
</script>

<style lang="scss"></style>
