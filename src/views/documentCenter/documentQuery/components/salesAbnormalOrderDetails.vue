<template>
  <div class="order-abnormal-data-details">
    <div>
      <el-button
        style="font-size: 16px; margin-bottom: 10px"
        type="text"
        @click="handelJump(1)"
      >
        返回
      </el-button>
    </div>
    <el-form>
      <el-divider content-position="left"><h2>购销方信息</h2></el-divider>
      <basicSalesOrderDetails
        v-if="[9, 10].includes(currentRow.docType)"
        :records="currentRow"
      ></basicSalesOrderDetails>
      <template v-else>
        <el-row>
          <el-col :span="6">
            <el-form-item label="销售主体:">
              {{ currentRow.salesEntity }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="销售渠道:">
              {{ currentRow.salesChannel }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="销售店铺:">
              {{ currentRow.salesShop }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="发货仓库:">
              {{ currentRow.shippingWarehouse }}
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="6">
            <el-form-item label="下单用户IDcode:">
              {{ currentRow.idCode }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="下单用户等级:">
              {{ currentRow.userLevel }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="订单编号(售后单号):">
              {{ currentRow.serialNo }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="原订单编号:">
              {{ currentRow.originOrderNo }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="合并单号:">
              {{ currentRow.paySn }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="版本号:">
              {{ currentRow.correctVersion }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="售后类型:">
              {{ currentRow.refundType }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="订单类型:">
              {{ currentRow.orderType }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="订单来源:">
              {{ currentRow.orderFrom }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="收货地国家:">
              {{ currentRow.countryCode }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="销售数量:">
              {{ currentRow.quantity }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="币种:">
              {{ currentRow.currency }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="使用积分:">
              {{ currentRow.scoreCost }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="优惠前商品总金额:">
              {{ currentRow.goodsAmountTaxInclusive }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="优惠前运费金额:">
              {{ currentRow.shippingFeeAmount }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="优惠前商品税费:">
              {{ currentRow.taxFeeAmount }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="优惠前运费税费:">
              {{ currentRow.shippingFeeAmount }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="优惠后商品总金额:">
              {{ currentRow.discountedGoodsAmount }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="优惠后运费金额:">
              {{ currentRow.discountedShippingFeeAmount }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="优惠后商品税费:">
              {{ currentRow.discountedGoodsTaxAmount }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="优惠后运费税费:">
              {{ currentRow.discountedShippingFeeTaxAmount }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="优惠总金额:">
              {{ currentRow.discountedAmount }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="应付总金额:">
              {{ currentRow.orderAmount }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="实际支付金额:">
              {{ currentRow.paidAmount }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="订单状态:">
              {{ currentRow.orderStatus }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="下单时间:">
              {{ currentRow.orderTime }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="支付时间:">
              {{ currentRow.paidTime }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="发货时间:">
              {{ currentRow.shippingTime }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="签收时间:">
              {{ currentRow.signedTime }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="完成时间:">
              {{ currentRow.completedTime }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="取消时间:">
              {{ currentRow.canceledTime }}
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <el-divider content-position="left"><h2>异常详情</h2></el-divider>
      <el-row>
        <el-form-item label="异常类型:">
          {{ currentRow.errorRes ? currentRow.errorRes.type : '' }}
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="异常内容:">
          {{ currentRow.errorRes ? currentRow.errorRes.content : '' }}
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="是否处理:">
          {{
            currentRow.errorRes ? (currentRow.errorRes.fixed ? '是' : '否') : ''
          }}
        </el-form-item>
      </el-row>
    </el-form>
    <el-divider content-position="left"><h2>处理日志</h2></el-divider>
    <el-row v-for="item in currentRow.events || []" :key="item" :gutter="12">
      <el-col :span="12">
        <el-card shadow="always">
          <div class="card-body">
            <div class="card-body-top">
              <h3>{{ item.content }}</h3>
              <span>{{ item.type }}</span>
            </div>
            <div>{{ item.creatTime }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import basicSalesOrderDetails from './basicSalesOrderDetails.vue';
  export default {
    components: {
      basicSalesOrderDetails,
    },
    props: {
      currentRow: {
        type: Object,
        default: () => {},
      },
      orderType: {
        type: Array,
        default: () => [],
      },
      purchaseOrderStatus: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      console.log(this.currentRow, 'currentRow');
      return {};
    },
    created() {},
    methods: {
      getText(val, list) {
        let text = '';
        list.map(item => {
          if (item.key == val) {
            text = item.value;
          }
        });
        return text;
      },
      handelJump() {
        this.$emit('goBack');
      },
    },
  };
</script>

<style lang="scss" scoped>
  .order-abnormal-data-details {
    .card-body .card-body-top {
      display: flex;
      align-items: center;
      > h3 {
        margin-right: 10px;
      }
    }
  }
</style>
