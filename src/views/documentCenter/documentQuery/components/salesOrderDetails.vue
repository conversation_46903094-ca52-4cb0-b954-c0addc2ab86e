<template>
  <div>
    <div>
      <el-button
        style="font-size: 16px; margin-bottom: 10px"
        type="text"
        @click="handelJump(1)"
      >
        返回
      </el-button>
    </div>
    <el-form>
      <el-divider content-position="left"><h2>基本信息</h2></el-divider>
      <basicSalesOrderDetails
        v-if="[9, 10].includes(currentRow.docType)"
        :records="info"
      ></basicSalesOrderDetails>
      <template v-else>
        <el-row>
          <el-col :span="6">
            <el-form-item label="销售主体:">
              {{ info.salesEntity }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="销售渠道:">
              {{ info.salesChannel }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="销售店铺:">
              {{ info.salesShop }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="发货仓库:">
              {{ info.shippingWarehouse }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="下单用户IDcode:">
              {{ info.idCode }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="下单用户等级:">
              {{ info.userLevel }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="订单编号(售后单号):">
              {{ info.serialNo }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="原订单编号:">
              {{ info.originSalesNo }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="合并单号:">
              {{ info.paySn }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="版本号:">
              {{ info.correctVersion }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="售后类型:">{{ info.refundType }}</el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="订单类型:">
              {{ info.orderType }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="订单来源:">
              {{ info.orderFrom }}
            </el-form-item>
          </el-col>
          <template v-if="[6].includes(currentRow.docType)">
            <el-col :span="6">
              <el-form-item label="收货地国家:">
                {{ info.countryCode }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="发货方式:">
                {{ info.shippingType }}
              </el-form-item>
            </el-col>
          </template>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="销售数量:">
              {{ info.quantity }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="币种:">
              {{ info.currency }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="使用积分:">
              {{ info.scoreCost }}
            </el-form-item>
          </el-col>
        </el-row>
        <template v-if="[7].includes(currentRow.docType)">
          <el-row>
            <el-col :span="6">
              <el-form-item label="退商品总金额:">
                {{ info.discountedGoodsAmount }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="退运费金额:">
                {{ info.discountedShippingFeeAmount }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="退商品税费:">
                {{ info.discountedGoodsTaxAmount }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="退运费税费:">
                {{ info.discountedShippingFeeTaxAmount }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="退款总金额:">
                {{ info.orderAmount }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="订单状态:">
                {{ info.orderStatus }}
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        <template v-if="[6].includes(currentRow.docType)">
          <el-row>
            <el-col :span="6">
              <el-form-item label="优惠前商品总金额:">
                {{ info.goodsAmountTaxInclusive }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="优惠前运费金额:">
                {{ info.shippingFeeAmount }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="优惠前商品税费:">
                {{ info.taxFeeAmount }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="优惠前运费税费:">
                {{ info.shippingFeeTaxAmount }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="优惠后商品总金额:">
                {{ info.discountedGoodsAmount }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="优惠后运费金额:">
                {{ info.discountedShippingFeeAmount }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="优惠后商品税费:">
                {{ info.discountedGoodsTaxAmount }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="优惠后运费税费:">
                {{ info.discountedShippingFeeTaxAmount }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="优惠总金额:">
                {{ info.discountedAmount }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="应付总金额:">
                {{ info.orderAmount }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="实际支付金额:">
                {{ info.paidAmount }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="订单状态:">
                {{ info.orderStatus }}
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        <el-row>
          <el-col :span="6">
            <el-form-item label="下单时间:">
              {{ info.orderTime }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="支付时间:">
              {{ info.paidTime }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="发货时间:">
              {{ info.shippingTime }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="签收时间:">
              {{ info.signedTime }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="完成时间:">
              {{ info.completedTime }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              v-if="[6].includes(currentRow.docType)"
              label="取消时间:"
            >
              {{ info.canceledTime }}
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <template v-if="[6].includes(currentRow.docType)">
        <el-divider content-position="left"><h2>支付信息</h2></el-divider>
        <el-row>
          <el-col :span="6">
            <el-form-item label="支付渠道:">
              {{ info.payChannel }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="支付方式:">
              {{ info.payMethod }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="支付单号:">{{ info.paySn }}</el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="支付币种:">
              {{ info.currency }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="汇率:">
              {{ info.rate }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="支付金额:">
              {{ info.paidAmount }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="支付完成时间:">
              {{ info.paidTime }}
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <template v-if="[6, 9, 10].includes(currentRow.docType)">
        <el-divider content-position="left"><h2>财务状态</h2></el-divider>
        <el-row>
          <el-col v-if="[6, 7].includes(currentRow.docType)" :span="6">
            <el-form-item label="预收结算状态:">
              {{ info.preRevenueSettleStatusCopy }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="收入结算状态:">
              {{ info.revenueSettleStatusCopy }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="应结算金额(含税):">
              {{ info.revenueSettleAmount }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="已结算金额(含税):">
              {{ info.revenueSettledAmount }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="未结算金额(含税):">
              {{ info.revenueUnsettledAmount }}
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-form>
    <el-divider content-position="left">
      <h2>
        {{ [6, 9, 10].includes(currentRow.docType) ? '订单明细' : '退单明细' }}
      </h2>
    </el-divider>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :fetch="getInfo"
    >
      <template slot="discountedAmount" slot-scope="scope">
        <el-popover placement="top" trigger="hover">
          <dynamictable
            :data-source="scope.row.couponList || []"
            :columns="getAmountTableColumns(scope.row)"
            :options="options"
          />

          <el-button slot="reference" type="text">
            {{ scope.row.discountedAmount }}
          </el-button>
        </el-popover>
      </template>
    </dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    salesDocDetails,
    salesDocBusinessDetails,
  } from '@/api/documentCenter';
  import basicSalesOrderDetails from './basicSalesOrderDetails.vue';
  export default {
    components: {
      dynamictable,
      basicSalesOrderDetails,
    },
    props: {
      currentRow: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        list: [],
        info: {},
        options: {
          loading: false,
          border: true,
        },
      };
    },
    created() {
      this.getInfo();
    },

    methods: {
      getText(val, list) {
        let text = '';
        list.map(item => {
          if (item.key === val) {
            text = item.value;
          }
        });
        return text;
      },
      async getInfo() {
        this.options.loading = true;
        const isToC = [6, 7].includes(this.currentRow.docType);
        const currentRow = this.currentRow;
        const detailsApi = isToC ? salesDocDetails : salesDocBusinessDetails;
        try {
          const res = await detailsApi({
            id: currentRow.id,
          });
          this.options.loading = false;
          if (res) {
            this.info = res;
            this.list = isToC
              ? res.salesDocItemDetails
              : res.businessSalesDocItemDetails;
          }
        } catch (error) {
          this.options.loading = false;
        }
      },
      handelJump() {
        this.$emit('goBack');
      },
      getAmountTableColumns() {
        return [
          {
            prop: 'id',
            label: '明细行号',
          },
          {
            prop: 'typeCopy',
            label: '优惠类型',
          },
          {
            prop: 'couponId',
            label: '相关ID',
          },
          {
            prop: 'name',
            label: '名称',
          },
          {
            prop: 'discountedGoodsPrice',
            label: '优惠总金额',
          },
          {
            prop: 'goodsDiscountedAmount',
            label: '商品优惠价',
          },
          {
            prop: 'discountedGoodsAmount',
            label: '商品优惠总额',
          },
          {
            prop: 'shippingDiscountedPrice',
            label: '运费优惠价',
          },
          {
            prop: 'shippingDiscountedAmount',
            label: '运费优惠总额',
          },
          {
            prop: 'shippingTaxDiscountedPrice',
            label: '运费税优惠价',
          },
          {
            prop: 'shippingTaxDiscountedAmount',
            label: '运费税总额',
          },
          {
            prop: 'goodsDiscountedAmount',
            label: '商品税优惠价',
          },
          {
            prop: 'goodsTaxDiscountedAmount',
            label: '商品税优惠总额',
          },
        ];
      },
      getColumns() {
        const columns = [
          {
            prop: 'itemNo',
            label: '订单明细行号',
          },
          {
            prop: 'brandCode',
            label: '品牌CODE',
          },
          {
            prop: 'brandName',
            label: '品牌名称',
          },
          {
            prop: 'brandType',
            label: '品牌类型',
          },
          {
            prop: 'barcode',
            label: '商品条码',
          },
          {
            prop: 'goodsName',
            label: '商品中文名称',
          },
          {
            prop: 'sellQuantity',
            label: '销售数量',
          },
          {
            prop: 'finalTaxRate',
            label: '综合税税率(%)',
            render: ({ finalTaxRate }) => (
              <span>{finalTaxRate !== null ? finalTaxRate * 100 : ''}</span>
            ),
          },
          {
            prop: 'salesTax',
            label: '销项税率(%)',
            render: ({ salesTax }) => (
              <span>{salesTax !== null ? salesTax * 100 : ''}</span>
            ),
          },
          {
            prop: 'taxFree',
            label: '是否免税',
            render: ({ taxFree }) => <span>{taxFree ? '是' : '否'}</span>,
          },
          {
            prop: 'shippingFree',
            label: '是否包邮',
            render: ({ shippingFree }) => (
              <span>{shippingFree ? '是' : '否'}</span>
            ),
          },
          {
            prop: 'marketPrice',
            label: '市场价',
          },
          {
            prop: 'originalPrice',
            label: '商品原价',
          },
          {
            prop: 'priceInLevel',
            label: '商品等级价',
          },
          {
            prop: 'paidPrice',
            label: '实付商品单价',
          },
          {
            prop: 'paidShippingPrice',
            label: '实付运费价',
          },
          {
            prop: 'paidGoodsAmount',
            label: '实付商品金额',
          },
          {
            prop: 'paidGoodsTaxFeeAmount',
            label: '实付商品税费总额',
          },
          {
            prop: 'paidShippingFeeTaxAmount',
            label: '实付商品运费总额',
          },

          {
            prop: 'paidShippingFeeTaxAmount',
            label: '实付商品运费税总额',
          },
          {
            prop: 'discountedAmount',
            label: '优惠总金额',
            hide: this.currentRow.docType === 7,
            scopedSlots: { customRender: 'discountedAmount' },
          },
          {
            prop: 'scoreCost',
            label: '使用积分数',
            hide: this.currentRow.docType === 7,
          },
          {
            prop: 'totalAmountActuallyPaidGoodsSaleTax',
            label: '实付商品总金额',
          },
        ];
        const columns1 = [
          {
            prop: 'itemNo',
            label: '订单明细行号',
          },
          {
            prop: 'brandCode',
            label: '品牌CODE',
          },
          {
            prop: 'brandName',
            label: '品牌名称',
          },
          {
            prop: 'brandType',
            label: '品牌类型',
          },
          {
            prop: 'barcode',
            label: '商品条码',
          },
          {
            prop: 'goodsName',
            label: '商品中文名称',
          },
          {
            prop: 'sellQuantity',
            label: '销售数量',
          },
          {
            prop: 'salesTax',
            label: '销项税率(%)',
            render: ({ salesTax }) => (
              <span>{salesTax !== null ? salesTax * 100 : ''}</span>
            ),
          },

          {
            prop: 'priceTaxInclusive',
            label: '单价(含税)',
          },
          {
            prop: 'priceTaxExclusive',
            label: '单价(未税)',
          },
          {
            prop: 'amountTaxInclusive',
            label: '金额（含税）',
          },
          {
            prop: 'taxAmount',
            label: '税额',
          },
          {
            prop: 'amountTaxExclusive',
            label: '金额（未税）',
          },
        ];
        return [9, 10].includes(this.currentRow.docType) ? columns1 : columns;
      },
    },
  };
</script>

<style lang="scss" scoped></style>
