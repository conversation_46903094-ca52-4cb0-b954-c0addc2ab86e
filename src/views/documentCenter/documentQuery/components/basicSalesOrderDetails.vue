<template>
  <div>
    <el-row>
      <el-col :span="12">
        <el-form-item label="销售主体:">
          {{ records.salesEntity }}
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="客户名称:">
          {{ records.customer }}
        </el-form-item>
      </el-col>
    </el-row>
    <!-- <el-row>
      <el-col :span="6">
        <el-form-item label="销售渠道:">
          {{ records.salesChannel }}
        </el-form-item>
      </el-col>
    </el-row> -->
    <el-row>
      <el-col :span="6">
        <el-form-item label="订单编号:">
          {{ records.serialNo }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="原销售单号:">
          {{ records.originSalesNo }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="关联采购单号:">
          {{ records.relatedPurchaseOrderNo }}
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="6">
        <el-form-item label="商品数量:">
          {{ records.quantity }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="币种:">
          {{ records.currency }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="订单状态:">
          {{ records.orderStatus }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="发货仓库:">
          {{ records.shippingWarehouse }}
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="6">
        <el-form-item label="是否关联交易:">
          {{ records.relatedTrade ? '是' : '否' }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="合同编号:">
          {{ records.contractNo }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="合同版本号:">
          {{ records.contractVersion }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="单据类型:">
          {{ records.docType }}
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="6">
        <el-form-item label="订单金额(含税):">
          {{ records.amountTaxInclusive }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="税额:">
          {{ records.taxAmount }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="订单金额(未税):">
          {{ records.amountTaxExclusive }}
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="6">
        <el-form-item label="下单时间:">
          {{ records.orderTime }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="发货时间:">
          {{ records.shippingTime }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="完成时间:">
          {{ records.completedTime }}
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="取消时间:">
          {{ records.canceledTime }}
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>
<script>
  export default {
    props: {
      records: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {};
    },
    created() {},
    methods: {},
  };
</script>
<style lang="scss" scoped></style>
