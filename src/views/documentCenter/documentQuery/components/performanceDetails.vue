<template>
  <div>
    <salesOrderListSearch
      v-model="searchParams"
      :tab="tab"
      :settlement-status="settlementStatus"
      :document-type="documentType"
      :purchase-order-status="purchaseOrderStatus"
      :subject-dict="subjectDict"
      :sales-warehouse="salesWarehouse"
      :shipping-warehouse="shippingWarehouse"
      :shipping-type="shippingType"
      :trade-type="tradeType"
      :brand-list="brandList"
      :brand-type="brandType"
      :toc-order-status="tocOrderStatus"
      :tob-order-status="tobOrderStatus"
      :user-level="userLevel"
      @onSearch="onSearch"
      @onReset="onReset"
      @goBack="goBack"
    ></salesOrderListSearch>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import salesOrderListSearch from './salesOrderListSearch';
  import { salesDocFulfillmentDocs } from '@/api/documentCenter';
  import { parseTime, debounce, initSearchParams } from '@/utils';
  export default {
    components: {
      dynamictable,
      salesOrderListSearch,
    },
    props: {
      tab: {
        type: Number,
        default: 1,
      },
      currentRow: {
        type: Object,
        default: () => {},
      },
      settlementStatus: {
        type: Array,
        default: () => [],
      },
      paymentStatus: {
        type: Array,
        default: () => [],
      },
      documentType: {
        type: Array,
        default: () => [],
      },
      sellWarehouse: {
        type: Array,
        default: () => [],
      },
      purchaseOrderStatus: {
        type: Array,
        default: () => [],
      },
      subjectDict: {
        type: Array,
        default: () => [],
      },
      // 发货方式数据
      shippingType: {
        type: Array,
        default: () => [],
      },
      // 发货仓库数据
      shippingWarehouse: {
        type: Array,
        default: () => [],
      },
      // 销售方式数据
      salesWarehouse: {
        type: Array,
        default: () => [],
      },
      // 销售方式数据
      tradeType: {
        type: Array,
        default: () => [],
      },
      // 品牌名称
      brandList: {
        type: Array,
        default: () => [],
      },
      brandType: {
        type: Array,
        default: () => [],
      },

      tocOrderStatus: {
        type: Array,
        default: () => [],
      },
      tobOrderStatus: {
        type: Array,
        default: () => [],
      },
      userLevel: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        orderDate: '',
        paymentDate: '',
        deliveryDate: '',
        signDate: '',
        finishDate: '',
        cancelDate: '',
        outStockDate: '',
        parcelReceiptDate: '',
        searchParams: {
          canceledTimeEnd: '',
          canceledTimeStart: '',
          completedTimeEnd: '',
          completedTimeStart: '',
          docType: 6,
          idCode: '',
          orderStatus: '',
          orderTimeEnd: '',
          orderTimeStart: '',
          paySn: '',
          logisticsSn: '',
          brandName: '',
          barcode: '',
          pushOrderSn: '',
          goodsName: '',
          brandType: '',
          preRevenueSettleStatus: '',
          revenueSettleStatus: '',
          salesChannelCode: '',
          salesEntityCode: '',
          salesShopCode: '',
          serialNo: '',
          shippingTimeEnd: '',
          shippingTimeStart: '',
          shippingType: '',
          signedTimeEnd: '',
          signedTimeStart: '',
          userLevel: '',
          warehouseCode: '',
          tradeType: '',
          relatedPurchaseDocNo: '',
          purchaseCode: '',
        },
        list: [],
        options: {
          loading: false,
          border: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },
    created() {
      this.getList(true);
    },
    methods: {
      goBack() {
        this.$emit('goBack');
      },

      handleDetail(tab, currentRow) {
        const row = {
          ...currentRow,
          docType: this.searchParams.docType,
        };
        this.$emit('handleDetail', tab, row);
      },
      getParams() {
        if (this.currentRow) {
          this.searchParams.docType = this.currentRow.docType;
          this.searchParams.serialNo = this.currentRow.serialNo;
        }
        this.searchParams.orderTimeStart = this.orderDate
          ? parseTime(this.orderDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.orderTimeEnd = this.orderDate
          ? parseTime(this.orderDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.paidTimeStart = this.paymentDate
          ? parseTime(this.paymentDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.paidTimeEnd = this.paymentDate
          ? parseTime(this.paymentDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.shippingTimeStart = this.deliveryDate
          ? parseTime(this.deliveryDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.shippingTimeEnd = this.deliveryDate
          ? parseTime(this.deliveryDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.signedTimeStart = this.signDate
          ? parseTime(this.signDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.signedTimeEnd = this.signDate
          ? parseTime(this.signDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.completedTimeStart = this.finishDate
          ? parseTime(this.finishDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.completedTimeEnd = this.finishDate
          ? parseTime(this.finishDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.canceledTimeStart = this.cancelDate
          ? parseTime(this.cancelDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.canceledTimeEnd = this.cancelDate
          ? parseTime(this.cancelDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.canceledTimeStart = this.outStockDate
          ? parseTime(this.outStockDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.pushTimeEnd = this.outStockDate
          ? parseTime(this.outStockDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.packageSignedTimeStart = this.parcelReceiptDate
          ? parseTime(this.cancelDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.packageSignedTimeEnd = this.parcelReceiptDate
          ? parseTime(this.cancelDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        const params = {
          ...this.searchParams,
          page: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await salesDocFulfillmentDocs(initSearchParams(params));
        this.options.loading = false;
        this.list = res && res.records ? res.records : [];
        this.pagination.total = res ? res.total : 0;
      },
      onSearch: debounce(function (date) {
        Object.keys(date).forEach(item => {
          this[item] = date[item];
        });
        this.getList(true);
      }, 1000),
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },

      getColumns() {
        const columns = [
          {
            prop: 'pushOrderNo',
            label: '出库单号(推单号)',
            width: '100px',
          },
          {
            prop: 'logisticsSn',
            label: '物流单号',
          },
          {
            prop: 'salesOrderNo',
            label: '销售单号',
          },
          {
            prop: 'paySn',
            label: '合并单号',
          },
          {
            prop: 'originSalesOrderNo',
            label: '原销售单号',
          },
          {
            prop: 'docType',
            label: '单据类型',
          },
          {
            prop: 'salesEntity',
            label: '销售主体',
          },
          {
            prop: 'salesChannel',
            label: '销售渠道',
          },
          {
            prop: 'salesShop',
            label: '销售店铺',
          },
          {
            prop: 'idCode',
            label: '用户IDCode',
          },
          {
            prop: 'userLevel',
            label: '下单用户等级',
          },
          {
            prop: 'barcode',
            label: '商品条码',
          },
          {
            prop: 'goodsName',
            label: '商品名称',
          },
          {
            prop: 'quantity',
            label: 'P单出库数量',
          },
          {
            prop: 'currency',
            label: '币种',
          },
          {
            prop: 'paidAmount',
            label: '实付金额',
          },
          {
            prop: 'goodsAmountInLevel',
            label: '商品等级价总额',
          },
          {
            prop: 'goodsTaxAmount',
            label: '商品税费总额',
          },
          {
            prop: 'shippingFeeAmount',
            label: '运费总额',
          },
          {
            prop: 'shippingFeeTaxAmount',
            label: '运费税总额',
          },
          {
            prop: 'discountedAmount',
            label: '优惠总金额',
          },
          {
            prop: 'goodsPrice',
            label: '商品原价',
          },
          {
            prop: 'goodsPriceInLevel',
            label: '商品等级价',
          },
          {
            prop: 'paidGoodsPrice',
            label: '实付商品单价',
          },
          {
            prop: 'paidShippingFeePrice',
            label: '实付运费价',
          },
          {
            prop: 'shippingWarehouseName',
            label: '发货仓库',
          },
          {
            prop: 'shippingLogicWarehouseName',
            label: '发货实仓',
          },
          {
            prop: 'logisticsCompany',
            label: '物流公司',
          },
          {
            prop: 'orderType',
            label: '订单类型',
          },
          {
            prop: 'receiptCountry',
            label: '收货地国家',
          },
          {
            prop: 'orderStatus',
            label: '订单状态',
          },

          {
            prop: 'orderTime',
            label: '下单时间',
          },
          {
            prop: 'paidTime',
            label: '支付时间',
          },
          {
            prop: 'shippingTime',
            label: '发货时间',
          },
          {
            prop: 'signedTime',
            label: '签收时间',
          },

          {
            prop: 'canceledTime',
            label: '取消时间',
          },
          {
            prop: 'completedTime',
            label: '完成时间',
          },
          {
            prop: 'packageStatus',
            label: '包裹状态',
          },
          {
            prop: 'pushTime',
            label: '出库时间',
          },
          {
            prop: 'packageSignedTime',
            label: '包裹签收时间',
          },
          {
            prop: 'deliveryQuantity',
            label: '发货数量',
          },

          {
            prop: 'revenueSettleStatusCopy',
            label: '收入结算状态',
          },
          {
            prop: 'revenueSettleAmount',
            label: '收入应结算金额(含税)',
          },
          {
            prop: 'revenueSettledAmount',
            label: '收入已结算金额(含税)',
          },

          {
            prop: 'revenueUnsettledAmount',
            label: '收入未结算金额(含税)',
          },
        ];

        return columns;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
