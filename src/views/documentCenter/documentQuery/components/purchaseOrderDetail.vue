<template>
  <div class="purchaseOrderDetail">
    <div>
      <el-button
        style="font-size: 16px; margin-bottom: 10px"
        type="text"
        @click="handelJump(1)"
      >
        返回
      </el-button>
    </div>
    <search
      ref="search"
      v-model="searchParams"
      :tab="tab"
      :biz-line-id="bizLineId"
      :delivery-status="deliveryStatus"
      :received-status="receivedStatus"
      :settlement-status="settlementStatus"
      :payment-status="paymentStatus"
      :document-type="documentType"
      :order-type="orderType"
      :sell-warehouse="sellWarehouse"
      :purchase-order-status="purchaseOrderStatus"
      :subject-dict="subjectDict"
      :supply-dict="supplyDict"
      :brand-list="brandList"
      @onSearch="onSearch"
      @onReset="onReset"
    ></search>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="warehouseEntryList" slot-scope="scope">
        <el-popover placement="top" trigger="hover">
          <dynamictable
            :data-source="scope.row.warehouseEntryList || []"
            :columns="getSettledQuantityColumns(1, scope.row || {})"
            :options="options"
          />

          <el-button slot="reference" type="text">
            {{
              scope.row.purchaseOrderItem &&
              scope.row.purchaseOrderItem.receivedQuantity
            }}
          </el-button>
        </el-popover>
      </template>
      <template slot="settlementItemList" slot-scope="scope">
        <el-popover placement="top" trigger="hover">
          <dynamictable
            :data-source="scope.row.settlementItemList || []"
            :columns="getSettledQuantityColumns(2, scope.row || {})"
            :options="options"
          />
          <el-button slot="reference" type="text">
            {{
              scope.row.purchaseOrder &&
              scope.row.purchaseOrder.docBaseDTO.settledQuantity
            }}
          </el-button>
        </el-popover>
      </template>
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          btn-text="详情"
          type="text"
          size="small"
          permission-key=""
          @click="handleDetail(5, scope.row.purchaseOrder)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import search from './search';
  import { purchaseDocItems } from '@/api/documentCenter';
  import { parseTime, debounce } from '@/utils';
  export default {
    components: {
      dynamictable,
      search,
    },
    props: {
      tab: {
        type: Number,
        default: 1,
      },

      currentRow: {
        type: Object,
        default: null,
      },
      bizLineId: {
        type: Array,
        default: () => [],
      },
      deliveryStatus: {
        type: Array,
        default: () => [],
      },
      receivedStatus: {
        type: Array,
        default: () => [],
      },
      settlementStatus: {
        type: Array,
        default: () => [],
      },
      paymentStatus: {
        type: Array,
        default: () => [],
      },
      documentType: {
        type: Array,
        default: () => [],
      },
      orderType: {
        type: Array,
        default: () => [],
      },
      sellWarehouse: {
        type: Array,
        default: () => [],
      },
      purchaseOrderStatus: {
        type: Array,
        default: () => [],
      },
      brandList: {
        type: Array,
        default: () => [],
      },
      subjectDict: {
        type: Array,
        default: () => [],
      },
      supplyDict: {
        type: Array,
        default: () => [],
      },
    },

    data() {
      return {
        orderDate: '',
        searchParams: {
          barcode: '',
          bizLine: '',
          brandId: '',
          contractNo: '',
          deliveryStatus: '',
          englishTitle: '',
          docType: '',
          orderEndTime: '',
          orderStartTime: '',
          orderStatus: '',
          orderType: '',
          paymentStatus: '',
          purchaseNo: '',
          purchasePrincipal: '',
          receiveStatus: '',
          relatedPurchaseOrderNo: '',
          relatedSellOrderNo: '',
          sellWarehouseId: '',
          settlementStatus: '',
          supplierPrincipal: '',
          title: '',
        },
        list: [],
        options: {
          loading: false,
          border: true,
          // showSummary: true,
        },

        documentTypeTextArr: [
          '采购订单',
          '采购调整单',
          '采购退供单(逆向采购单)',
          '采购订单(SO转单)',
          '逆向采购单(SO转单)',
        ],
        pageSize: 1,
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },
    watch: {
      tab: {
        handler(val, oldval) {
          if (val === 1) {
            // 点击返回，初始化
            Object.assign(
              this.$data.searchParams,
              this.$options.data().searchParams,
            );
            Object.assign(
              this.$data.pagination,
              this.$options.data().pagination,
            );
            this.orderDate = '';
            this.$refs['search'].onReset();
          }
          if (val === 2) {
            const { currentRow } = this;
            if (currentRow && currentRow.docBaseDTO) {
              this.searchParams.purchaseNo =
                currentRow.docBaseDTO.purchaseOrderNo;
              this.searchParams.docType = currentRow.documentType;
            }

            this.getList(true);
          }
        },
        // 深度观察监听
        deep: true,
      },
    },
    // created() {
    //   const { currentRow } = this;

    //   if (currentRow && currentRow.docBaseDTO)
    //     this.searchParams.purchaseNo = currentRow.docBaseDTO.purchaseOrderNo;
    //   this.getList(true);
    // },
    methods: {
      getText(val, list) {
        let text = '';
        list.map(item => {
          if (item.key === val) {
            text = item.value;
          }
        });
        return text;
      },
      handleDetail(tab, currentRow) {
        this.$emit('handleDetail', tab, currentRow);
      },
      handelJump() {
        this.$emit('goBack');
      },
      getParams() {
        this.searchParams.orderStartTime = this.orderDate
          ? parseTime(this.orderDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.orderEndTime = this.orderDate
          ? parseTime(this.orderDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        const params = {
          ...this.searchParams,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await purchaseDocItems(params);
        this.options.loading = false;

        this.list = res && res.records ? res.records : [];
        this.pagination.total = res ? res.total : 0;
      },
      onSearch: debounce(function (orderDate) {
        this.orderDate = orderDate;
        this.getList(true);
      }, 1000),
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },
      getSettledQuantityColumns(type, val) {
        if (type === 1) {
          const { purchaseOrder = {} } = val;
          return [
            {
              prop: 'serialNo',
              label: [5, 11].includes(purchaseOrder.documentType)
                ? '出库单号'
                : '入库单号',
              render: (row = {}) => (
                <a onClick={() => this.handleDetail(3, val.purchaseOrder)}>
                  {row.serialNo}
                </a>
              ),
            },
            {
              prop: 'entryTime',
              label: [5, 11].includes(purchaseOrder.documentType)
                ? '出库时间'
                : '入库时间',
              render: ({ entryTime }) => <span>{entryTime}</span>,
            },
            {
              prop: 'goodsBarcode',
              label: '商品条码',
            },
            {
              prop: 'quantity',
              label: [5, 11].includes(purchaseOrder.documentType)
                ? '出库数量'
                : '入库数量',
            },
          ];
        }
        return [
          {
            prop: 'serialNo',
            label: '结算单号',
            render: (row = {}) => (
              <a
                style="cursor:pointer"
                onClick={() => this.handleDetail(3, val.purchaseOrder)}
              >
                {row.serialNo}
              </a>
            ),
          },
          {
            prop: 'settleTime',
            label: '结算时间',
            render: ({ settleTime }) => (
              <span>{settleTime ? parseTime(settleTime) : ''}</span>
            ),
          },
          {
            prop: 'goodsRes.barcode',
            label: '商品条码',
          },
          {
            prop: 'settledAmountInclusive',
            label: '结算金额',
          },
        ];
      },
      getColumns() {
        const { docType } = this.searchParams;
        const columns = [
          {
            prop: 'purchaseOrderItem.id',
            label: '采购明细行号',
          },
          {
            prop: 'purchaseOrderItem.purchaseOrderNo',
            label: '采购单号',
            render: ({ purchaseOrderItem, purchaseOrder }) => (
              <span>
                <a
                  style="cursor:pointer"
                  onClick={() => this.handleDetail(5, purchaseOrder)}
                >
                  {purchaseOrderItem.purchaseOrderNo}
                </a>
              </span>
            ),
          },
          {
            prop: 'purchaseOrder.originPurchaseOrderNo',
            label: '关联采购单号',
          },
          {
            prop: 'purchaseOrder.relatedSellOrderNo',
            label: '关联销售单号',
          },
          {
            prop: 'settlementNo',
            label: '结算单号',
            render: ({ purchaseOrderItem = {} }) => (
              <a
                onClick={() => {
                  this.$router.push({
                    path: '/copingManagement/coping/purchaseSettlementQuery',
                  });
                }}
              >
                {Array.isArray(purchaseOrderItem.settlementNos)
                  ? purchaseOrderItem.settlementNos.join(' | ')
                  : ''}
              </a>
            ),
          },
          {
            prop: 'goodsBarcode',
            label: '商品条码',
            render: ({ purchaseOrderItem = {} }) => (
              <span>{purchaseOrderItem.goodsBarcode}</span>
            ),
          },
          {
            prop: 'goodsTitle',
            label: '商品中文名称',
            render: ({ purchaseOrderItem = {} }) => (
              <span>{purchaseOrderItem.goodsTitle}</span>
            ),
          },
          {
            prop: 'goodsEnglishTitle',
            label: '商品英文名称',
            render: ({ purchaseOrderItem = {} }) => (
              <span>{purchaseOrderItem.goodsEnglishTitle}</span>
            ),
          },
          {
            prop: 'subjectName',
            label: '采购主体',
            render: ({ purchaseOrder = {} }) => (
              <span>
                {purchaseOrder.purchasePrincipal &&
                  purchaseOrder.purchasePrincipal.companyName}
              </span>
            ),
          },
          {
            prop: 'secondPartyName',
            label: '供应商名称',
            render: ({ purchaseOrder = {} }) => (
              <span>
                {purchaseOrder.supplierPrincipal &&
                  purchaseOrder.supplierPrincipal.companyName}
              </span>
            ),
          },
          {
            prop: 'brandName',
            label: '品牌名称',
            render: ({ purchaseOrder = {} }) => (
              <span>
                {purchaseOrder.brand && purchaseOrder.brand.brandName}
              </span>
            ),
          },
          {
            prop: 'purchaseOrder.contractNo',
            label: '合同编号',
          },
          {
            prop: 'sellWarehouse',
            label: '销售仓库',
            render: ({ purchaseOrder = {} }) => (
              <span>
                {purchaseOrder.sellWarehouse &&
                  purchaseOrder.sellWarehouse.warehouseName}
              </span>
            ),
          },
          {
            prop: 'purchaseOrder.documentType',
            label: '单据类型',
            render: ({ purchaseOrder = {} }) => (
              <span>
                {purchaseOrder.documentType === 11
                  ? '采购退供单(逆向采购单)'
                  : this.documentTypeTextArr[purchaseOrder.documentType - 1]}
              </span>
            ),
          },
          {
            prop: 'purchaseOrder.settleCurrency',
            label: '结算币种',
          },
          {
            prop: 'purchaseOrder.accountCurrency',
            label: '记账币种',
          },
          {
            prop: 'purchaseOrderItem.purchaseGoodQuantity',
            label: '采购数量(正品)',
          },
          {
            prop: 'purchaseOrderItem.freebieQuantity',
            label: '采购数量(赠品)',
          },
          {
            prop: 'purchaseOrderItem.purchasePriceTaxInclusive',
            label: '采购单价(含税)',
          },
          {
            prop: 'purchaseOrderItem.rate',
            label: '税率(%)',
            render: ({ purchaseOrderItem }) => (
              <span>
                {purchaseOrderItem ? purchaseOrderItem.rate * 100 : ''}
              </span>
            ),
          },
          {
            prop: 'purchaseOrderItem.taxAmount',
            label: '税额',
          },
          {
            prop: 'purchaseOrderItem.purchaseAmountTaxInclusive',
            label: '采购金额(含税)',
          },
          {
            prop: 'purchaseOrderItem.purchasePrice',
            label: '采购单价(未税)',
          },
          {
            prop: 'purchaseOrderItem.purchaseAmountTaxExclusive',
            label: '采购金额(未税)',
          },
          {
            prop: 'purchaseOrderItem.rebateTypeCopy',
            label: '返利类型',
          },
          {
            prop: 'actualPurchaseQuantity',
            label: '应退数量',
            hide: ![5, 11].includes(docType),
            // label: [5, 11].includes(docType) ? '应退数量' : '应收数量',
            render: ({ purchaseOrderItem = {} }) => (
              <span>
                {purchaseOrderItem && purchaseOrderItem.actualPurchaseQuantity}
              </span>
            ),
          },
          {
            prop: 'deliverStatus',
            label: '发货状态',
            render: ({ purchaseOrder = {} }) => (
              <span>
                {this.getText(
                  purchaseOrder.docBaseDTO.deliveryStatus,
                  this.deliveryStatus,
                )}
              </span>
            ),
          },
          {
            prop: 'warehouseEntryList',
            label: [5, 11].includes(docType) ? '实退数量' : '实收数量',
            scopedSlots: { customRender: 'warehouseEntryList' },
          },
          {
            prop: 'purchaseOrderItem.receivedStatus',
            label: '收货状态',
            render: ({ purchaseOrderItem = {} }) => (
              <span>
                {this.getText(
                  purchaseOrderItem.receivedStatus,
                  this.receivedStatus,
                )}
              </span>
            ),
          },
          {
            prop: 'purchaseOrderItem.settleQuantity',
            label: '应结算数量',
          },
          {
            prop: 'purchaseOrderItem.settleAmountTaxInclusive',
            label: '应结算金额(含税)',
          },
          {
            prop: 'settlementItemList',
            label: '结算数量',
            scopedSlots: { customRender: 'settlementItemList' },
          },
          {
            prop: 'settledAmountTaxInclusive',
            label: '结算金额(含税)',
            render: ({ purchaseOrder = {} }) => (
              <span>
                {purchaseOrder.docBaseDTO &&
                  purchaseOrder.docBaseDTO.settledAmountTaxInclusive}
              </span>
            ),
          },
          {
            prop: 'unsettledQuantity',
            label: '未结算数量',
            render: ({ purchaseOrder = {} }) => (
              <span>
                {purchaseOrder.docBaseDTO &&
                  purchaseOrder.docBaseDTO.unsettledQuantity}
              </span>
            ),
          },
          {
            prop: 'unsettledAmountTaxInclusive',
            label: '未结算金额(含税)',
            render: ({ purchaseOrder = {} }) => (
              <span>
                {purchaseOrder.docBaseDTO &&
                  purchaseOrder.docBaseDTO.unsettledAmountTaxInclusive}
              </span>
            ),
          },
          {
            prop: 'docBaseDTO.settlementStatus',
            label: '结算状态',
            render: ({ purchaseOrder = {} }) => (
              <span>
                {this.getText(
                  purchaseOrder.docBaseDTO.settlementStatus,
                  this.settlementStatus,
                )}
              </span>
            ),
          },
          {
            prop: 'docBaseDTO.paymentStatus',
            label: '付款状态',
            render: ({ purchaseOrder = {} }) => (
              <span>
                {this.getText(
                  purchaseOrder.docBaseDTO.paymentStatus,
                  this.paymentStatus,
                )}
              </span>
            ),
          },

          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '80',
            scopedSlots: { customRender: 'operation' },
          },
        ];

        return columns;
      },
    },
  };
</script>
<style lang="scss" scoped>
  .purchaseOrderDetail {
    /deep/.el-table a {
      cursor: pointer;
    }
  }
</style>
