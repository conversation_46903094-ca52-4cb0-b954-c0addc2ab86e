<template>
  <div>
    <div>
      <el-button
        style="font-size: 16px; margin-bottom: 10px"
        type="text"
        @click="handelJump(1)"
      >
        返回
      </el-button>
    </div>
    <el-divider content-position="left"><h2>基础信息</h2></el-divider>
    <el-form>
      <el-row>
        <el-form-item label="采购主体:">
          {{ currentRow.purchasePrincipal.companyName }}
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="供应商名称:">
          {{ currentRow.supplierPrincipal.companyName }}
        </el-form-item>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="订单编号:">
            {{ currentRow.orderNo }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="订单名称:">
            {{ currentRow.orderTitle }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="订单类型:">
            {{ getText(currentRow.orderType, orderType) }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="品牌名称:">
            {{ currentRow.brand.brandName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="实际采购数量:">
            {{ currentRow.purchaseQuantity }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="结算币种:">
            {{ currentRow.settleCurrency }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="记账币种:">
            {{ currentRow.accountCurrency }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="采购金额(含税):">
            {{ currentRow.purchaseAmountTaxInclusive }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="采购金额(未税):">
            {{ currentRow.purchaseAmountTaxExclusive }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="税额:">
            {{ currentRow.tax }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="订单状态:">
            {{
              getText(
                currentRow.docBaseDTO && currentRow.docBaseDTO.status,
                purchaseOrderStatus,
              )
            }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="渠道:">
            {{ currentRow.channelName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="入库销售仓库:">
            {{ currentRow.sellWarehouse.warehouseName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="下单时间:">
            {{ currentRow.orderTime }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="下单人:">
            {{ currentRow.purchaser }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-divider content-position="left">
      <h2>{{ tab === 3 ? '收发货详情' : '结算详情' }}</h2>
    </el-divider>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
      @header-click="handleHeaderClick"
    >
      <template slot="settlementNo" slot-scope="props">
        <dynamictable
          :data-source="props.row.settlementItemRes || []"
          :columns="getChildColumns()"
          :options="options"
        />
      </template>
    </dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    purchaseDocSettlementDetails,
    purchaseDocSettlementShipping,
  } from '@/api/documentCenter';
  import { parseTime } from '@/utils';
  export default {
    components: {
      dynamictable,
    },
    props: {
      tab: {
        type: Number,
        default: 3,
      },
      currentRow: {
        type: Object,
        default: () => {},
      },
      orderType: {
        type: Array,
        default: () => [],
      },
      purchaseOrderStatus: {
        type: Array,
        default: () => [],
      },
      receivedStatus: {
        type: Array,
        default: () => [],
      },
      deliveryStatus: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        purchaseOrder: {
          supplierPrincipal: {},
          purchasePrincipal: {},
          brand: {},
          status: {},
          sellWarehouse: {},
        },
        isHideTableColumn: true,
        list: [],
        options: {
          loading: false,
          border: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },
    created() {
      this.getList();
    },

    methods: {
      getChildColumns() {
        return [
          {
            prop: 'serialNo',
            label: '结算单号',
          },
          {
            prop: 'settleTime',
            label: '结算时间',
            render: ({ settleTime }) => (
              <span>{settleTime ? parseTime(settleTime) : ''}</span>
            ),
          },
          {
            prop: 'goodsRes.barcode',
            label: '商品条码',
          },
          {
            prop: 'settledQuantity',
            label: '数量',
          },
          {
            prop: 'settlePriceInclusive',
            label: '结算单价(含税)',
          },
          {
            prop: 'settlePriceExclusive',
            label: '结算单价(未税)',
          },
          {
            prop: 'settleAmountTaxInclusive',
            label: '结算金额(含税)',
          },
          {
            prop: 'taxAmount',
            label: '税额',
          },
          {
            prop: 'settledAmountExclusive',
            label: '结算金额(未税)',
          },
        ];
      },
      getText(val, list) {
        if (!list) return '';
        let text = '';

        list.map(item => {
          if (item.key == val) {
            text = item.value;
          }
        });
        return text;
      },
      handleHeaderClick(column, event) {
        const arr = [
          'warehouseEntryItemRes.quantity',
          'purchaseOrderItemRes.receivedQuantity',
        ];
        if (arr.includes(column.property)) {
          const { isHideTableColumn } = this;
          this.isHideTableColumn = !isHideTableColumn;
        }
      },
      async getList() {
        this.options.loading = true;
        const apiList =
          this.tab === 3
            ? purchaseDocSettlementShipping
            : purchaseDocSettlementDetails;
        const { pageSize, pageLimit } = this.pagination;
        const res = await apiList({
          id: this.currentRow.id,
          limit: pageLimit,
          page: pageSize,
        });
        if (res) {
          this.list = res && res.records ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        }
        this.options.loading = false;
      },
      handelJump() {
        Object.assign(this.$data.pagination, this.$options.data().pagination);
        this.isHideTableColumn = true;
        this.$emit('goBack');
      },
      getTableColumnsText(isHideTableColumn, docType) {
        if ([5, 11].includes(docType)) {
          return isHideTableColumn ? '实退数量 >' : '实退数量 <';
        }
        return isHideTableColumn ? '实收数量 >' : '实收数量 <';
      },
      getColumns() {
        const { documentType } = this.currentRow;
        const { isHideTableColumn, tab } = this;
        const columns = [
          {
            prop: 'purchaseOrderItem.id',
            label: '采购明细行号',
          },
          {
            prop: 'purchaseOrderItem.goodsBarcode',
            label: '商品条码',
          },
          {
            prop: 'purchaseOrderItem.goodsTitle',
            label: '商品中文名称',
          },
          {
            prop: 'purchaseOrderItem.goodsEnglishTitle',
            label: '商品英文名称',
          },
          {
            prop: 'purchaseOrderItem.purchaseQuantity',
            label: '采购数量',
          },
          {
            prop: 'purchaseOrderItem.rate',
            label: '税率(%)',
            render: ({ purchaseOrderItem }) => (
              <span>
                {purchaseOrderItem ? purchaseOrderItem.rate * 100 : ''}
              </span>
            ),
          },
          {
            prop: 'purchaseOrderItem.purchasePriceTaxInclusive',
            label: '采购单价(含税)',
          },
          {
            prop: 'purchaseOrderItem.purchaseAmountTaxInclusive',
            label: '采购金额(含税)',
          },
          {
            prop: 'purchaseOrderItem.purchasePrice',
            label: '采购单价(未税)',
          },
          {
            prop: 'purchaseOrderItem.purchaseAmountTaxExclusive',
            label: '采购金额(未税)',
          },
          {
            prop: 'purchaseOrderItem.taxAmount',
            label: '税额',
          },
          {
            prop: 'arrivalNoticeItemRes.arrivalOrderCode',
            label: '到货单号',
          },
          {
            prop: 'deliveryQuantity',
            label: '应退数量',
            // label: [5, 11].includes(documentType) ? '应退数量' : '应收数量',
            hide: ![5, 11].includes(documentType),
            render: ({ purchaseOrder = {} }) => (
              <span>
                {purchaseOrder.docBaseDTO &&
                  purchaseOrder.docBaseDTO.deliveryQuantity}
              </span>
            ),
          },
          {
            prop: 'arrivalNoticeItemRes.deliveryTime',
            label: '发货时间',
          },
          {
            prop: 'purchaseOrderItem.deliverStatus',
            label: '发货状态',
            render: ({ purchaseOrderItem = {} }) => (
              <span>
                {purchaseOrderItem.deliverStatus
                  ? this.getText(
                      purchaseOrderItem.deliverStatus,
                      this.deliveryStatus,
                    )
                  : ''}
              </span>
            ),
          },
          {
            prop: 'warehouseEntryItemRes.serialNo',
            label: '入库单号',
          },
          {
            prop: 'warehouseEntryItemRes.quantity',
            label: this.getTableColumnsText(isHideTableColumn, documentType),
            width: '100px',
          },
          {
            prop: 'purchaseOrderItem.goodQuantity',
            label: '良品数量',
            hide: isHideTableColumn,
          },
          {
            prop: 'purchaseOrderItem.defectiveQuantity',
            label: '次品数量',
            hide: isHideTableColumn,
          },
          // {
          //   prop: 'purchaseOrderItem.freebieQuantity',
          //   label: '赠品数量',
          //   hide: isHideTableColumn,
          // },
          {
            prop: 'warehouseEntryItemRes.entryTime',
            label: '收货时间',
          },
          {
            prop: 'purchaseOrderItem.receivedStatus',
            label: '收货状态',
            render: ({ purchaseOrderItem = {} }) => (
              <span>
                {purchaseOrderItem.receivedStatus
                  ? this.getText(
                      purchaseOrderItem.receivedStatus,
                      this.receivedStatus,
                    )
                  : ''}
              </span>
            ),
          },
        ];

        const columns1 = [
          {
            prop: 'purchaseOrderItemRes.id',
            label: '采购明细行号',
          },
          {
            prop: 'purchaseOrderItemRes.goodsBarcode',
            label: '商品条码',
          },
          {
            prop: 'purchaseOrderItemRes.goodsTitle',
            label: '商品中文名称',
          },
          {
            prop: 'purchaseOrderItemRes.goodsEnglishTitle',
            label: '商品英文名称',
          },
          {
            prop: 'purchaseOrderItemRes.purchaseQuantity',
            label: '采购数量',
          },
          {
            prop: 'purchaseOrderItemRes.rate',
            label: '税率(%)',
            render: ({ purchaseOrderItemRes }) => (
              <span>
                {purchaseOrderItemRes ? purchaseOrderItemRes.rate * 100 : ''}
              </span>
            ),
          },
          {
            prop: 'purchaseOrderItemRes.purchasePriceTaxInclusive',
            label: '采购单价(含税)',
          },
          {
            prop: 'purchaseOrderItemRes.purchaseAmountTaxInclusive',
            label: '采购金额(含税)',
          },
          {
            prop: 'purchaseOrderItemRes.purchasePrice',
            label: '采购单价(未税)',
          },
          {
            prop: 'purchaseOrderItemRes.purchaseAmountTaxExclusive',
            label: '采购金额(未税)',
          },
          {
            prop: 'purchaseOrderItemRes.taxAmount',
            label: '税额',
          },
          {
            prop: 'purchaseOrderItemRes.receivedQuantity',
            label: this.getTableColumnsText(isHideTableColumn, documentType),
            width: '100px',
          },
          {
            prop: 'purchaseOrderItemRes.goodQuantity',
            label: '良品数量',
            hide: isHideTableColumn,
          },
          {
            prop: 'purchaseOrderItemRes.defectiveQuantity',
            label: '次品数量',
            hide: isHideTableColumn,
          },
          // {
          //   prop: 'purchaseOrderItemRes.freebieQuantity',
          //   label: '赠品数量',
          //   hide: isHideTableColumn,
          // },
          {
            prop: 'purchaseOrderItemRes.settleQuantity',
            label: '应结算数量',
          },
          {
            prop: 'purchaseOrderItemRes.settleAmountTaxInclusive',
            label: '应结算金额(含税)',
          },
          {
            prop: 'unsettledQuantity',
            label: '未结算数量',
            render: ({ purchaseOrder = {} }) => (
              <span>
                {purchaseOrder.docBaseDTO &&
                  purchaseOrder.docBaseDTO.unsettledQuantity}
              </span>
            ),
          },
          {
            prop: 'unsettledAmountTaxInclusive',
            label: '未结算金额(含税)',
            render: ({ purchaseOrder = {} }) => (
              <span>
                {purchaseOrder.docBaseDTO &&
                  purchaseOrder.docBaseDTO.unsettledAmountTaxInclusive}
              </span>
            ),
          },
          {
            prop: 'purchaseOrderItemRes.settlementNo',
            label: '结算单号',
            type: 'expand',
            scopedSlots: { customRender: 'settlementNo' },
          },
          {
            prop: 'purchaseOrderItemRes.settledQuantity',
            label: '已结算数量',
            // render: ({ purchaseOrderItemRes = {} }) => (
            //   <span>
            //     {purchaseOrder.docBaseDTO &&
            //       purchaseOrder.docBaseDTO.settledQuantity}
            //   </span>
            // ),
          },
          {
            prop: 'purchaseOrderItemRes.settledAmountTaxInclusive',
            label: '已结算金额(含税)',
            // render: ({ purchaseOrder = {} }) => (
            //   <span>
            //     {purchaseOrder.docBaseDTO &&
            //       purchaseOrder.docBaseDTO.settledAmountTaxInclusive}
            //   </span>
            // ),
          },
        ];
        return tab === 3 ? columns : columns1;
      },
    },
  };
</script>

<style lang="scss" scoped></style>
