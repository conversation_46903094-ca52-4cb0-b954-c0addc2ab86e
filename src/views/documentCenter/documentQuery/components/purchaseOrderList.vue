<template>
  <div class="purchaseOrderList">
    <search
      v-model="searchParams"
      :tab="tab"
      :biz-line-id="bizLineId"
      :delivery-status="deliveryStatus"
      :received-status="receivedStatus"
      :settlement-status="settlementStatus"
      :payment-status="paymentStatus"
      :document-type="documentType"
      :order-type="orderType"
      :sell-warehouse="sellWarehouse"
      :purchase-order-status="purchaseOrderStatus"
      :subject-dict="subjectDict"
      :supply-dict="supplyDict"
      :brand-list="brandList"
      @onSearch="onSearch"
      @onReset="onReset"
    ></search>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
      :row-class-name="rowClassName"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          btn-text="详情"
          type="text"
          size="small"
          permission-key=""
          @click="handleDetail(5, scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import search from './search';
  import { purchaseDocList } from '@/api/documentCenter';
  import { parseTime, debounce } from '@/utils';
  export default {
    components: {
      dynamictable,
      search,
    },
    props: {
      tab: {
        type: Number,
        default: 1,
      },
      bizLineId: {
        type: Array,
        default: () => [],
      },
      deliveryStatus: {
        type: Array,
        default: () => [],
      },
      receivedStatus: {
        type: Array,
        default: () => [],
      },
      settlementStatus: {
        type: Array,
        default: () => [],
      },
      paymentStatus: {
        type: Array,
        default: () => [],
      },
      documentType: {
        type: Array,
        default: () => [],
      },
      orderType: {
        type: Array,
        default: () => [],
      },
      sellWarehouse: {
        type: Array,
        default: () => [],
      },
      purchaseOrderStatus: {
        type: Array,
        default: () => [],
      },
      subjectDict: {
        type: Array,
        default: () => [],
      },
      supplyDict: {
        type: Array,
        default: () => [],
      },
      settlementMethod: {
        type: Array,
        default: () => [],
      },
      brandList: {
        type: Array,
        default: () => [],
      },
      payModelType: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        orderDate: '',
        searchParams: {
          barcode: '',
          bizLine: '',
          brandId: '',
          contractNo: '',
          deliveryStatus: '',
          englishTitle: '',
          docType: 1,
          orderEndTime: '',
          orderStartTime: '',
          orderStatus: '',
          orderType: '',
          paymentStatus: '',
          purchaseNo: '',
          purchasePrincipal: '',
          receiveStatus: '',
          relatedPurchaseOrderNo: '',
          relatedSellOrderNo: '',
          sellWarehouseId: '',
          settlementStatus: '',
          supplierPrincipal: '',
          title: '',
        },
        list: [],
        documentTypeTextArr: [
          '采购订单',
          '采购调整单',
          '采购退供单(逆向采购单)',
          '采购订单(SO转单)',
          '逆向采购单(SO转单)',
        ],
        options: {
          loading: false,
          border: true,
          // showSummary: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },
    created() {
      // this.getList(true);
    },
    methods: {
      getText(val, list) {
        let text = '';
        list.map(item => {
          if (item.key == val) {
            text = item.value;
          }
        });
        return text;
      },
      rowClassName({ row }) {
        if (row.errorRes) {
          return 'warning-row';
        }
        return '';
      },
      handleDetail(tab, currentRow) {
        this.$emit('handleDetail', tab, currentRow);
      },
      getParams() {
        this.searchParams.orderStartTime = this.orderDate
          ? parseTime(this.orderDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.orderEndTime = this.orderDate
          ? parseTime(this.orderDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        const params = {
          ...this.searchParams,
          page: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();

        this.options.loading = true;
        try {
          const res = await purchaseDocList(params);
          this.options.loading = false;
          this.list = res && res.records ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        } catch (error) {
          this.options.loading = false;
        }
      },
      onSearch: debounce(function (orderDate) {
        this.orderDate = orderDate;
        this.getList(true);
      }, 1000),
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },
      getColumns() {
        const { docType } = this.searchParams;
        const columns = [
          {
            prop: 'docBaseDTO.purchaseOrderId',
            label: '采购单ID',
            width: '100px',
            render: row => (
              <span>
                {row.docBaseDTO?.purchaseOrderId}
                {row.errorRes ? (
                  <a onClick={() => this.handleDetail(6, row)}>查看异常</a>
                ) : (
                  ''
                )}
              </span>
            ),
          },
          {
            prop: 'docBaseDTO.purchaseOrderNo',
            label: '采购单号',
            render: row => (
              <a onClick={() => this.handleDetail(2, row)}>
                {row.docBaseDTO ? row.docBaseDTO.purchaseOrderNo : ''}
              </a>
            ),
          },
          {
            prop: 'originPurchaseOrderNo',
            label: '关联采购单号',
          },
          {
            prop: 'relatedSellOrderNo',
            label: '关联销售单号',
          },
          {
            prop: 'settlementNos',
            label: '结算单号',
            render: ({ settlementNos = [] }) => (
              <div>
                {Array.isArray(settlementNos) && settlementNos.length
                  ? settlementNos.map((item, index) => {
                      return (
                        <a
                          onClick={() => {
                            this.$router.push({
                              path: `/copingManagement/coping/purchaseSettlementQuery?settlementNo=${item}`,
                            });
                          }}
                        >
                          {`${item}${
                            index !== settlementNos.length - 1 ? ' | ' : ''
                          } `}
                        </a>
                      );
                    })
                  : ''}
              </div>
            ),
          },
          {
            prop: 'purchasePrincipal',
            label: '采购主体',
            render: ({ purchasePrincipal }) => (
              <span>
                {purchasePrincipal ? purchasePrincipal.companyName : ''}
              </span>
            ),
          },
          {
            prop: 'supplierPrincipal',
            label: '供应商名称',
            render: ({ supplierPrincipal }) => (
              <span>
                {supplierPrincipal ? supplierPrincipal.companyName : ''}
              </span>
            ),
          },
          {
            prop: 'brandName',
            label: '品牌名称',
            render: ({ brand }) => <span>{brand ? brand.brandName : ''}</span>,
          },
          {
            prop: 'contractNo',
            label: '合同编号',
          },
          {
            prop: 'sellWarehouse',
            label: '销售仓库',
            render: ({ sellWarehouse }) => (
              <span>{sellWarehouse ? sellWarehouse.warehouseName : ''}</span>
            ),
          },
          {
            prop: 'documentType',
            label: '单据类型',
            render: ({ documentType }) => (
              <span>
                {documentType === 11
                  ? '采购退供单(逆向采购单)'
                  : this.documentTypeTextArr[documentType - 1]}
              </span>
            ),
          },
          {
            prop: 'purchaseQuantity',
            label: '采购数量',
          },
          {
            prop: 'settleCurrency',
            label: '结算币种',
          },
          {
            prop: 'accountCurrency',
            label: '记账币种',
          },
          {
            prop: 'purchaseAmountTaxInclusive',
            label: '采购金额(含税)',
          },
          {
            prop: 'tax',
            label: '税额',
          },
          {
            prop: 'purchaseAmountTaxExclusive',
            label: '采购金额(未税)',
          },
          {
            prop: 'paymentMethod',
            label: '付款方式',
            render: ({ paymentMethod }) => (
              <span>{this.getText(paymentMethod, this.payModelType)}</span>
            ),
          },
          {
            prop: 'docBaseDTO.status',
            label: '订单状态',
            render: ({ docBaseDTO = {} }) => (
              <span>
                {this.getText(docBaseDTO.status, this.purchaseOrderStatus)}
              </span>
            ),
          },
          // {
          //   prop: 'docBaseDTO.deliveryQuantity',
          //   // label: [5, 11].includes(docType) ? '应退数量' : '应收数量',
          //   hide: ![5, 11].includes(docType),
          //   label: '应退数量',
          // },
          {
            prop: 'docBaseDTO.deliveryStatus',
            label: '发货状态',
            render: ({ docBaseDTO = {} }) => (
              <span>
                {this.getText(docBaseDTO.deliveryStatus, this.deliveryStatus)}
              </span>
            ),
          },
          {
            prop: 'docBaseDTO.receiveQuantity',
            label: [5, 11].includes(docType) ? '实退数量' : '实收数量',
            render: (row = {}) => (
              <a onClick={() => this.handleDetail(3, row)}>
                {row.docBaseDTO ? row.docBaseDTO.receiveQuantity : ''}
              </a>
            ),
          },
          {
            prop: 'docBaseDTO.receiveStatus',
            label: '收货状态',
            render: ({ docBaseDTO = {} }) => (
              <span>
                {this.getText(docBaseDTO.receiveStatus, this.receivedStatus)}
              </span>
            ),
          },
          {
            prop: 'docBaseDTO.settlementQuantity',
            label: '应结算数量',
          },
          {
            prop: 'docBaseDTO.settleAmountTaxInclusive',
            label: '应结算金额(含税)',
          },
          {
            prop: 'docBaseDTO.settledQuantity',
            label: '已结算数量',
            render: (row = {}) => (
              <a onClick={() => this.handleDetail(4, row)}>
                {row.docBaseDTO ? row.docBaseDTO.settledQuantity : ''}
              </a>
            ),
          },
          {
            prop: 'docBaseDTO.settledAmountTaxInclusive',
            label: '已结算金额(含税)',
          },
          {
            prop: 'docBaseDTO.unsettledQuantity',
            label: '未结算数量',
          },
          {
            prop: 'docBaseDTO.unsettledAmountTaxInclusive',
            label: '未结算金额(含税)',
          },
          {
            prop: 'orderTime',
            label: '下单时间',
            render: ({ orderTime }) => (
              <span>{parseTime ? parseTime(orderTime) : ''}</span>
            ),
          },
          {
            prop: 'docBaseDTO.settlementStatus',
            label: '结算状态',
            render: ({ docBaseDTO = {} }) => (
              <span>
                {this.getText(
                  docBaseDTO.settlementStatus,
                  this.settlementStatus,
                )}
              </span>
            ),
          },
          {
            prop: 'docBaseDTO.paymentStatus',
            label: '付款状态',
            render: ({ docBaseDTO = {} }) => (
              <span>
                {this.getText(docBaseDTO.paymentStatus, this.paymentStatus)}
              </span>
            ),
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '80',
            scopedSlots: { customRender: 'operation' },
          },
        ];

        return columns;
      },
    },
  };
</script>
<style lang="scss" scoped>
  .purchaseOrderList {
    /deep/.el-table .warning-row {
      background: #f56c6c !important;
      td {
        background: #f56c6c !important;
      }
    }
    /deep/.el-table a {
      cursor: pointer;
    }
  }
</style>
