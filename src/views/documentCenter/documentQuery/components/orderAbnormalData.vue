<template>
  <div v-if="tab === 1" class="orderAbnormalData">
    <el-form ref="search" inline :model="searchParams">
      <el-form-item label="下单时间:">
        <el-date-picker
          v-model="orderDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="订单状态:">
        <el-select
          v-model="searchParams.orderStatus"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in purchaseOrderStatus"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否异常:">
        <el-select
          v-model="searchParams.errorFlag"
          clearable
          placeholder="请选择"
        >
          <el-option label="是" value="1"></el-option>
          <el-option label="否" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="合同编号:">
        <el-input
          v-model="searchParams.contractNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="采购主体:">
        <el-select
          v-model="searchParams.purchasePrincipal"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in subjectDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="供应商名称:">
        <el-select
          v-model="searchParams.supplierPrincipal"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in supplyDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="采购品牌:">
        <el-select
          v-model="searchParams.brandId"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in brandList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="采购单号:">
        <el-input
          v-model="searchParams.purchaseNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button type="primary" @click="goBack">返回</el-button>
      </el-form-item>
    </el-form>
    <div style="text-align: right; margin-bottom: 10px">
      异常订单数量总计{{ errSum.count }}，金额总计{{ errSum.total }}
    </div>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          btn-text="详情"
          type="text"
          size="small"
          permission-key=""
          @click="handelJump(2, scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
  <orderAbnormalDataDetails
    v-else
    :current-row="currentRow"
    :purchase-order-status="purchaseOrderStatus"
    :order-type="orderType"
    @goBack="handelJump(1, null)"
  ></orderAbnormalDataDetails>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import { purchaseDocList, getPurchaseDocSum } from '@/api/documentCenter';
  import { parseTime, debounce, initSearchParams } from '@/utils';
  import orderAbnormalDataDetails from './orderAbnormalDataDetails';
  export default {
    components: {
      dynamictable,
      orderAbnormalDataDetails,
    },
    props: {
      currentInfo: {
        type: Object,
        default: null,
      },
      purchaseOrderStatus: {
        type: Array,
        default: () => [],
      },
      settlementMethod: {
        type: Array,
        default: () => [],
      },
      subjectDict: {
        type: Array,
        default: () => [],
      },
      supplyDict: {
        type: Array,
        default: () => [],
      },
      brandList: {
        type: Array,
        default: () => [],
      },
      orderType: {
        type: Array,
        default: () => [],
      },
      payModelType: {
        type: Array,
        default: () => [],
      },
    },

    data() {
      return {
        tab: 1,
        currentRow: {},
        searchParams: {
          errorFlag: '1',
          orderEndTime: '',
          orderStartTime: '',
          orderStatus: '',
          contractNo: '',
          purchasePrincipal: '',
          supplierPrincipal: '',
          brandId: '',
          purchaseNo: '',
        },
        list: [],
        orderDate: '',
        errSum: {},
        options: {
          loading: false,
          border: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },
    created() {
      if (this.currentInfo) {
        this.searchParams.purchaseNo = this.currentInfo.docBaseDTO?.purchaseOrderNo;
      }
      getPurchaseDocSum({ errorFlag: true }).then(res => {
        if (res) {
          this.errSum = res;
        }
      });
      this.getList(true);
    },
    methods: {
      getText(val, list) {
        let text = '';
        list.map(item => {
          if (item.key == val) {
            text = item.value;
          }
        });
        return text;
      },
      handelJump(tab, val) {
        this.tab = tab;
        this.currentRow = val;
      },
      onSearch: debounce(function () {
        this.getList(true);
      }, 1000),
      getParams() {
        this.searchParams.orderStartTime = this.orderDate
          ? parseTime(this.orderDate[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.orderEndTime = this.orderDate
          ? parseTime(this.orderDate[1], '{y}-{m}-{d}')
          : '';
        const params = {
          ...this.searchParams,
          errorFlag: this.searchParams.errorFlag === '1',
          page: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        try {
          this.options.loading = true;
          const res = await purchaseDocList(params);
          if (res) {
            this.options.loading = false;
            this.list = res && res.records ? res.records : [];
            this.pagination.total = res ? res.total : 0;
          }
        } catch (error) {
          this.options.loading = false;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },
      goBack() {
        this.$emit('goBack');
      },
      getColumns() {
        return [
          {
            prop: 'docBaseDTO.purchaseOrderNo',
            label: '采购单号',
          },
          {
            prop: 'purchasePrincipal.companyName',
            label: '公司名称',
          },
          {
            prop: 'supplierPrincipal.companyName',
            label: '供应商名称',
          },
          {
            prop: 'brand.brandName',
            label: '品牌名称',
          },
          {
            prop: 'contractNo',
            label: '合同编号',
          },
          {
            prop: 'purchaseQuantity',
            label: '采购数量',
          },
          {
            prop: 'purchaseAmountTaxInclusive',
            label: '采购金额(含税)',
          },
          {
            prop: 'settleCurrency',
            label: '结算币种',
          },
          {
            prop: 'paymentMethod',
            label: '付款方式',
            render: ({ paymentMethod }) => (
              <span>{this.getText(paymentMethod, this.payModelType)}</span>
            ),
          },
          {
            prop: 'rebateTypeCopy',
            label: '返利类型',
          },
          {
            prop: 'docBaseDTO.status',
            label: '订单状态',
            render: ({ docBaseDTO = {} }) => (
              <span>
                {this.getText(docBaseDTO.status, this.purchaseOrderStatus)}
              </span>
            ),
          },
          {
            prop: 'orderTime',
            label: '下单时间',
          },
          {
            prop: 'errorRes',
            label: '是否异常',
            render: ({ errorRes }) => <span>{errorRes ? '是' : '否'}</span>,
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '80',
            scopedSlots: { customRender: 'operation' },
          },
        ];
      },
    },
  };
</script>

<style lang="scss"></style>
