<template>
  <div>
    <div>
      <el-button
        style="font-size: 16px; margin-bottom: 10px"
        type="text"
        @click="handelJump(1)"
      >
        返回
      </el-button>
    </div>
    <el-form>
      <el-divider content-position="left"><h2>购销方信息</h2></el-divider>
      <el-row>
        <el-col :span="6">
          <el-form-item label="采购主体:">
            {{ purchaseOrder.purchasePrincipal.companyName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="供应商名称:">
            {{ purchaseOrder.supplierPrincipal.companyName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider content-position="left"><h2>订单信息</h2></el-divider>
      <el-row>
        <el-col :span="6">
          <el-form-item label="订单编号:">
            {{ purchaseOrder.orderNo }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="订单名称:">
            {{ purchaseOrder.orderTitle }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="订单类型:">
            {{ getText(purchaseOrder.orderType, orderType) }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="品牌名称:">
            {{ purchaseOrder.brand.brandName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="实际采购数量:">
            {{ purchaseOrder.purchaseQuantity }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="结算币种:">
            {{ purchaseOrder.settleCurrency }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="记账币种:">
            {{ purchaseOrder.accountCurrency }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="采购金额(含税):">
            {{ purchaseOrder.purchaseAmountTaxInclusive }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="采购金额(未税):">
            {{ purchaseOrder.purchaseAmountTaxExclusive }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="税额:">{{ purchaseOrder.tax }}</el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="订单状态:">
            {{
              getText(
                purchaseOrder.docBaseDTO && purchaseOrder.docBaseDTO.status,
                purchaseOrderStatus,
              )
            }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="渠道:">
            {{ purchaseOrder.channelName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="入库销售仓库:">
            {{ purchaseOrder.sellWarehouse.warehouseName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="下单时间:">
            {{ purchaseOrder.orderTime }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="下单人:">
            {{ purchaseOrder.purchaser }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider content-position="left"><h2>收发货信息</h2></el-divider>
      <el-row>
        <!-- <el-col :span="6">
          <el-form-item
            :label="
              [5, 11].includes(purchaseOrder.documentType)
                ? '应退数量:'
                : '应收数量:'
            "
          >
            {{ purchaseOrder.docBaseDTO.deliveryQuantity }}
          </el-form-item>
        </el-col> -->
        <el-col v-if="[5, 11].includes(purchaseOrder.documentType)" :span="6">
          <el-form-item label="应退数量">
            {{ purchaseOrder.docBaseDTO.deliveryQuantity }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="发货状态:">
            {{
              getText(purchaseOrder.docBaseDTO.deliveryStatus, deliveryStatus)
            }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item
            :label="
              [5, 11].includes(purchaseOrder.documentType)
                ? '实退数量:'
                : '实收数量:'
            "
          >
            {{ purchaseOrder.docBaseDTO.receiveQuantity }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="收货状态:">
            {{
              getText(purchaseOrder.docBaseDTO.receiveStatus, receivedStatus)
            }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-divider content-position="left"><h2>财务状态</h2></el-divider>
      <el-row>
        <el-form-item label="结算状态:">
          {{
            getText(purchaseOrder.docBaseDTO.settlementStatus, settlementStatus)
          }}
        </el-form-item>
        <el-row>
          <el-col :span="6">
            <el-form-item label="应结算数量:">
              {{ purchaseOrder.docBaseDTO.settlementQuantity }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="应结算金额(含税):">
              {{ purchaseOrder.docBaseDTO.settleAmountTaxInclusive }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="实际结算数量:">
              {{ purchaseOrder.docBaseDTO.settledQuantity }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="实际结算金额(含税):">
              {{ purchaseOrder.docBaseDTO.settledAmountTaxInclusive }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="未结算数量:">
              {{ purchaseOrder.docBaseDTO.unsettledQuantity }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="未结算金额(含税):">
              {{ purchaseOrder.docBaseDTO.unsettledAmountTaxInclusive }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="付款状态:">
          {{ getText(purchaseOrder.docBaseDTO.paymentStatus, paymentStatus) }}
        </el-form-item>
        <el-form-item label="支付金额:">
          {{ purchaseOrder.docBaseDTO.paidAmount }}
        </el-form-item>
      </el-row>
    </el-form>
    <el-divider content-position="left"><h2>采购明细</h2></el-divider>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import { getRefundOrder } from '@/api/refundManagement';
  import { purchaseDocDetails } from '@/api/documentCenter';
  export default {
    components: {
      dynamictable,
    },
    props: {
      currentRow: {
        type: Object,
        default: () => {},
      },
      orderType: {
        type: Array,
        default: () => [],
      },
      purchaseOrderStatus: {
        type: Array,
        default: () => [],
      },
      deliveryStatus: {
        type: Array,
        default: () => [],
      },
      receivedStatus: {
        type: Array,
        default: () => [],
      },
      settlementStatus: {
        type: Array,
        default: () => [],
      },
      paymentStatus: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        purchaseOrder: {
          supplierPrincipal: {},
          purchasePrincipal: {},
          brand: {},
          docBaseDTO: {},
          sellWarehouse: {},
        },
        list: [],
        options: {
          loading: false,
          border: true,
          // showSummary: true,
        },
      };
    },
    created() {
      this.getList();
    },

    methods: {
      getText(val, list) {
        let text = '';
        list.map(item => {
          if (item.key === val) {
            text = item.value;
          }
        });
        return text;
      },
      async getList() {
        this.options.loading = true;
        const res = await purchaseDocDetails({ id: this.currentRow.id });
        this.options.loading = false;
        if (res) {
          this.purchaseOrder = res.purchaseOrder;
          this.list = res.purchaseOrderItems;
        }
      },
      handelJump() {
        this.$emit('goBack');
      },
      getColumns() {
        console.log(this.purchaseOrder, 'lll');
        const columns = [
          {
            prop: 'id',
            label: '采购明细行号',
          },
          {
            prop: 'goodsBarcode',
            label: '商品条码',
          },
          {
            prop: 'goodsTitle',
            label: '商品中文名称',
          },
          {
            prop: 'goodsEnglishTitle',
            label: '商品英文名称',
          },
          {
            prop: 'purchaseGoodQuantity',
            label: '采购数量(正品)',
          },
          {
            prop: 'freebieQuantity',
            label: '采购数量(赠品)',
          },
          {
            prop: 'defectiveQuantity',
            label: '采购数量(次品)',
          },
          {
            prop: 'rate',
            label: '税率(%)',
            render: ({ rate }) => <span>{rate * 100}</span>,
          },
          {
            prop: 'purchasePriceTaxInclusive',
            label: '采购单价(含税)',
          },
          {
            prop: 'purchaseAmountTaxInclusive',
            label: '采购金额(含税)',
          },
          {
            prop: 'purchasePrice',
            label: '采购单价(未税)',
          },
          {
            prop: 'purchaseAmountTaxExclusive',
            label: '采购金额(未税)',
          },
          {
            prop: 'taxAmount',
            label: '税额',
          },
        ];
        return columns;
      },
    },
  };
</script>

<style lang="scss" scoped></style>
