<template>
  <div class="purchaseOrderList">
    <salesOrderListSearch
      v-model="searchParams"
      :tab="tab"
      :settlement-status="settlementStatus"
      :document-type="documentType"
      :purchase-order-status="purchaseOrderStatus"
      :subject-dict="subjectDict"
      :shipping-warehouse="shippingWarehouse"
      :shipping-type="shippingType"
      :trade-type="tradeType"
      :toc-order-status="tocOrderStatus"
      :user-level="userLevel"
      :tob-order-status="tobOrderStatus"
      @onSearch="onSearch"
      @onReset="onReset"
      @handleChangeDocType="handleChangeDocType"
    ></salesOrderListSearch>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
      :row-class-name="rowClassName"
    >
      <template slot="discountedAmount" slot-scope="scope">
        <el-popover placement="top" trigger="hover">
          <dynamictable
            :data-source="scope.row.couponList || []"
            :columns="getAmountTableColumns(1, scope.row)"
            :options="options"
          />
          <el-button slot="reference" type="text">
            {{ scope.row.discountedAmount }}
          </el-button>
        </el-popover>
      </template>
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          btn-text="详情"
          type="text"
          size="small"
          permission-key=""
          @click="handleDetail(2, scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import salesOrderListSearch from './salesOrderListSearch';
  import { salesDocList, salesDocBusinessList } from '@/api/documentCenter';
  import { parseTime, debounce, initSearchParams } from '@/utils';
  export default {
    components: {
      dynamictable,
      salesOrderListSearch,
    },
    props: {
      tab: {
        type: Number,
        default: 1,
      },
      settlementStatus: {
        type: Array,
        default: () => [],
      },
      documentType: {
        type: Array,
        default: () => [],
      },

      purchaseOrderStatus: {
        type: Array,
        default: () => [],
      },
      subjectDict: {
        type: Array,
        default: () => [],
      },
      // 发货方式数据
      shippingType: {
        type: Array,
        default: () => [],
      },
      // 发货仓库数据
      shippingWarehouse: {
        type: Array,
        default: () => [],
      },

      // 销售方式数据
      tradeType: {
        type: Array,
        default: () => [],
      },
      tocOrderStatus: {
        type: Array,
        default: () => [],
      },
      tobOrderStatus: {
        type: Array,
        default: () => [],
      },
      userLevel: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        orderDate: '',
        paymentDate: '',
        deliveryDate: '',
        signDate: '',
        finishDate: '',
        cancelDate: '',
        searchParams: {
          canceledTimeEnd: '',
          canceledTimeStart: '',
          completedTimeEnd: '',
          completedTimeStart: '',
          docType: 6,
          idCode: '',
          orderStatus: '',
          orderTimeEnd: '',
          orderTimeStart: '',
          paySn: '',
          preRevenueSettleStatus: '',
          revenueSettleStatus: '',
          salesChannelCode: '',
          salesEntityCode: '',
          salesShopCode: '',
          serialNo: '',
          shippingTimeEnd: '',
          shippingTimeStart: '',
          shippingType: '',
          signedTimeEnd: '',
          signedTimeStart: '',
          userLevel: '',
          warehouseCode: '',
          tradeType: '',
          relatedPurchaseDocNo: '',
          purchaseCode: '',
        },
        list: [],
        options: {
          loading: false,
          border: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },
    created() {
      // this.getList(true);
    },
    methods: {
      getText(val, list) {
        let text = '';
        list.map(item => {
          if (item.key == val) {
            text = item.value;
          }
        });
        return text;
      },
      rowClassName({ row }) {
        if (row.errorRes) {
          return 'warning-row';
        }
        return '';
      },
      handleDetail(tab, currentRow) {
        const row = {
          ...currentRow,
          docType: this.searchParams.docType,
        };
        this.$emit('handleDetail', tab, row);
      },
      getParams() {
        this.searchParams.orderTimeStart = this.orderDate
          ? parseTime(this.orderDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.orderTimeEnd = this.orderDate
          ? parseTime(this.orderDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.paidTimeStart = this.paymentDate
          ? parseTime(this.paymentDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.paidTimeEnd = this.paymentDate
          ? parseTime(this.paymentDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.shippingTimeStart = this.deliveryDate
          ? parseTime(this.deliveryDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.shippingTimeEnd = this.deliveryDate
          ? parseTime(this.deliveryDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.signedTimeStart = this.signDate
          ? parseTime(this.signDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.signedTimeEnd = this.signDate
          ? parseTime(this.signDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.completedTimeStart = this.finishDate
          ? parseTime(this.finishDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.completedTimeEnd = this.finishDate
          ? parseTime(this.finishDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.canceledTimeStart = this.cancelDate
          ? parseTime(this.cancelDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.canceledTimeEnd = this.cancelDate
          ? parseTime(this.cancelDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        const params = {
          ...this.searchParams,
          page: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const apiList = [6, 7].includes(params.docType)
          ? salesDocList
          : salesDocBusinessList;
        try {
          const res = await apiList(initSearchParams(params));
          this.options.loading = false;
          this.list = res && res.records ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        } catch (error) {
          this.list = [];
          this.pagination.total = null;
          this.options.loading = false;
        }
      },
      handleChangeDocType() {
        this.searchParams = {
          ...this.$options.data().searchParams,
          docType: this.searchParams.docType,
        };
        this.getList(true);
      },
      onSearch: debounce(function (date) {
        Object.keys(date).forEach(item => {
          this[item] = date[item];
        });
        this.getList(true);
      }, 1000),
      onReset() {
        this.searchParams = {
          ...this.$options.data().searchParams,
          docType: this.searchParams.docType,
        };
      },
      getAmountTableColumns(type, val) {
        if (type === 1) {
          return [
            {
              prop: 'typeCopy',
              label: '优惠类型',
            },
            {
              prop: 'couponId',
              label: '相关ID',
            },
            {
              prop: 'name',
              label: '名称',
            },
            {
              prop: 'discountedTotalAmount',
              label: '优惠合计',
            },
            {
              prop: 'goodsDiscountedAmount',
              label: '商品优惠金额',
            },
            {
              prop: 'shippingDiscountedAmount',
              label: '运费优惠金额',
            },
            {
              prop: 'shippingTaxDiscountedAmount',
              label: '运费税金额',
            },
            {
              prop: 'goodsTaxDiscountedAmount',
              label: '商品税优惠金额',
            },
          ];
        }
      },
      getColumns() {
        const columns = [
          {
            prop: 'serialNo',
            label: '销售单号(售后单号)',
            width: '100px',
            render: row => (
              <span>
                {row.serialNo}
                {row.errorRes ? (
                  <a onClick={() => this.handleDetail(3, row)}>查看异常</a>
                ) : (
                  ''
                )}
              </span>
            ),
          },
          {
            prop: 'correctVersion',
            label: '版本号',
          },
          {
            prop: 'paySn',
            label: '合并单号',
          },
          {
            prop: 'originSalesNo',
            label: '原销售单号',
          },
          {
            prop: 'salesEntity',
            label: '销售主体',
          },
          {
            prop: 'salesChannel',
            label: '销售渠道',
          },
          {
            prop: 'salesShop',
            label: '销售店铺',
          },
          {
            prop: 'idCode',
            label: '用户IDCode',
          },
          {
            prop: 'userLevel',
            label: '下单用户等级',
          },
          {
            prop: 'quantity',
            label: '商品数量',
          },
          {
            prop: 'currency',
            label: '币种',
          },
          {
            prop: 'discountedGoodsAmount',
            label: '退商品金额',
            hide: this.searchParams.docType === 6,
          },
          {
            prop: 'discountedShippingFeeAmount',
            label: '退运费金额',
            hide: this.searchParams.docType === 6,
          },
          {
            prop: 'discountedGoodsTaxAmount',
            label: '退税费金额',
            hide: this.searchParams.docType === 6,
          },
          {
            prop: 'discountedShippingFeeTaxAmount',
            label: '退运费税金额',
            hide: this.searchParams.docType === 6,
          },
          {
            prop: 'orderAmount',
            label: '退款总额',
            hide: this.searchParams.docType === 6,
          },
          {
            prop: 'goodsAmountTaxInclusive',
            label: '优惠前商品总金额(含税)',
            hide: this.searchParams.docType === 7,
          },
          {
            prop: 'shippingFeeAmount',
            label: '优惠前运费金额',
            hide: this.searchParams.docType === 7,
          },
          {
            prop: 'taxFeeAmount',
            label: '优惠前商品税费',
            hide: this.searchParams.docType === 7,
          },
          {
            prop: 'shippingFeeTaxAmount',
            label: '优惠前运费税费',
            hide: this.searchParams.docType === 7,
          },
          {
            prop: 'discountedAmount',
            label: '优惠总金额',
            hide: this.searchParams.docType === 7,
            scopedSlots: { customRender: 'discountedAmount' },
          },
          {
            prop: 'discountedGoodsAmount',
            label: '优惠后商品总金额(含税)',
            hide: this.searchParams.docType === 7,
          },
          {
            prop: 'discountedShippingFeeAmount',
            label: '优惠后运费金额',
            hide: this.searchParams.docType === 7,
          },
          {
            prop: 'discountedGoodsTaxAmount',
            label: '优惠后商品税费',
            hide: this.searchParams.docType === 7,
          },
          {
            prop: 'discountedShippingFeeTaxAmount',
            label: '优惠后运费税费',
            hide: this.searchParams.docType === 7,
          },
          {
            prop: 'orderAmount',
            label: '实际支付金额',
            hide: this.searchParams.docType === 7,
          },
          {
            prop: 'scoreCost',
            label: '消耗积分',
            hide: this.searchParams.docType === 7,
          },
          {
            prop: 'shippingWarehouse',
            label: '发货仓库',
            hide: this.searchParams.docType === 7,
          },
          {
            prop: 'orderFrom',
            label: '订单来源',
          },
          {
            prop: 'docType',
            label: '单据类型',
          },
          {
            prop: 'countryCode',
            label: '收货地国家',
            hide: this.searchParams.docType === 7,
          },
          {
            prop: 'shippingType',
            label: '发货方式',
            hide: this.searchParams.docType === 7,
          },
          {
            prop: 'orderStatus',
            label: '订单状态',
          },
          {
            prop: 'orderTime',
            label: '下单时间',
          },
          {
            prop: 'paidTime',
            label: '支付时间',
          },
          {
            prop: 'shippingTime',
            label: '发货时间',
          },
          {
            prop: 'signedTime',
            label: '签收时间',
          },

          {
            prop: 'canceledTime',
            label: '取消时间',
          },
          {
            prop: 'completedTime',
            label: '完成时间',
          },
          {
            prop: 'preRevenueSettleStatusCopy',
            label: '预收结算状态',
          },
          {
            prop:
              this.searchParams.docType === 6 ? 'deliveryQuantity' : 'quantity',
            label: this.searchParams.docType === 6 ? '发货数量' : '退货数量',
            render: (row = {}) => (
              <a onClick={() => this.handleDetail(4, row)}>
                {row.deliveryQuantity}
              </a>
            ),
          },
          {
            prop: 'signedQuantity',
            label: this.searchParams.docType === 6 ? '签收数量' : '收货数量',
            render: row => (
              <a onClick={() => this.handleDetail(4, row)}>
                {row.signedQuantity}
              </a>
            ),
          },
          {
            prop: 'revenueSettleStatusCopy',
            label: '收入结算状态',
          },
          {
            prop: 'revenueSettleAmount',
            label:
              this.searchParams.docType === 6
                ? '收入应结算金额(含税)'
                : '收入应扣减金额(含税)',
          },
          {
            prop: 'revenueSettledAmount',
            label:
              this.searchParams.docType === 6
                ? '收入已结算金额(含税)'
                : '收入已扣减金额(含税)',
          },

          {
            prop: 'revenueUnsettledAmount',
            label:
              this.searchParams.docType === 6
                ? '收入未结算金额(含税)'
                : '收入未扣减金额(含税)',
          },

          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '80',
            scopedSlots: { customRender: 'operation' },
          },
        ];

        const columns2 = [
          {
            prop: 'serialNo',
            label: '销售单号',
            width: '100px',
            render: row => (
              <span>
                {row.serialNo}
                {row.errorRes ? (
                  <a onClick={() => this.handleDetail(3, row)}>查看异常</a>
                ) : (
                  ''
                )}
              </span>
            ),
          },
          {
            prop: 'originSalesNo',
            label: '原销售单号',
          },
          {
            prop: 'relatedPurchaseOrderNo',
            label: '关联采购单号',
          },
          {
            prop: 'salesEntity',
            label: '销售主体',
          },
          {
            prop: 'customer',
            label: '客户名称',
          },
          {
            prop: 'contractNo',
            label: '合同编号',
          },
          // {
          //   prop: 'salesChannel',
          //   label: '销售渠道',
          // },
          {
            prop: 'relatedTrade',
            label: '是否关联交易',
            render: ({ relatedTrade }) => (
              <span>{relatedTrade ? '是' : '否'}</span>
            ),
          },
          {
            prop: 'quantity',
            label: '商品数量',
          },
          {
            prop: 'totalQuantity',
            label: this.searchParams.docType === 9 ? '出库数量' : '入库数量',
          },
          {
            prop: 'currency',
            label: '币种',
          },
          {
            prop: 'amountTaxInclusive',
            label: '订单金额(含税)',
          },
          {
            prop: 'taxAmount',
            label: '税额',
          },
          {
            prop: 'amountTaxExclusive',
            label: '订单金额(未税)',
          },
          {
            prop: 'shippingWarehouse',
            label: '(退)发货仓库',
          },
          {
            prop: 'docType',
            label: '单据类型',
          },
          {
            prop: 'orderStatus',
            label: '订单状态',
          },
          {
            prop: 'orderTime',
            label: '下单时间',
          },
          {
            prop: 'shippingTime',
            label: '发货时间',
          },
          {
            prop: 'canceledTime',
            label: '取消时间',
          },
          {
            prop: 'completedTime',
            label: '完成时间',
          },
          {
            prop: 'revenueSettleStatusCopy',
            label: '收入结算状态',
          },
          {
            prop: 'revenueSettledAmount',
            label: '收入应结算金额(含税)',
          },
          {
            prop: 'revenueSettledAmount',
            label: '收入已结算金额(含税)',
          },
          {
            prop: 'revenueUnsettledAmount',
            label: '收入未结算金额(含税)',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '80',
            scopedSlots: { customRender: 'operation' },
          },
        ];

        return [9, 10].includes(this.searchParams.docType) ? columns2 : columns;
      },
    },
  };
</script>
<style lang="scss" scoped>
  .purchaseOrderList {
    /deep/.el-table .warning-row {
      background: #f56c6c !important;
      td {
        background: #f56c6c !important;
      }
    }
    /deep/.el-table a {
      cursor: pointer;
    }
  }
</style>
