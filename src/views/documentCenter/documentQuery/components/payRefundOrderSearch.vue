<template>
  <div class="searchParams">
    <el-form ref="search" inline :model="searchParams">
      <el-form-item label="支付单号:">
        <el-input
          v-model="searchParams.poSerialNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="销售单号:">
        <el-input
          v-model="searchParams.settleSerialNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item v-if="tab === 2" label="支付流水号:">
        <el-input
          v-model="searchParams.settleSerialNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>

      <el-form-item label="销售下单时间:">
        <el-date-picker
          v-model="searchDate"
          type="daterange"
          :clearable="false"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="创建时间:">
        <el-date-picker
          v-model="searchDate"
          type="daterange"
          :clearable="false"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item v-if="tab === 2" label="订单状态:">
        <el-select
          v-model="searchParams.purchaseSubjectCode"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in subjectDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="tab === 1" label="支付单状态:">
        <el-select
          v-model="searchParams.purchaseSubjectCode"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in subjectDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="tab === 1" label="退款状态:">
        <el-select
          v-model="searchParams.supplierSubjectCodeList"
          clearable
          filterable
          multiple
          placeholder="请选择"
        >
          <el-option
            v-for="item in supplyDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import { parseTime, initSearchParams, setInitData } from '@/utils';
  export default {
    props: {
      tab: {
        type: Number,
        default: 1,
      },
      currentRow: {
        type: Object,
        default: null,
      },
      subjectDict: {
        type: Array,
        default: () => [],
      },
      supplyDict: {
        type: Array,
        default: () => [],
      },
      bizLineId: {
        type: Array,
        default: () => [],
      },
      currency: {
        type: Array,
        default: () => [],
      },
      payModelType: {
        type: Array,
        default: () => [],
      },
    },

    data() {
      return {
        searchDate: '',
        detailsParams: {},
        searchParams: {
          billStatus: '',
          bizLineId: '',
          contractNo: '',
          payStatus: '',
          payType: '',
          poSerialNo: '',
          purchaseSubjectCode: '',
          settleCurrency: '',
          settleSerialNo: '',
          settleTimeEnd: '',
          settleTimeStart: '',
          supplierSubjectCodeList: [],
        },
      };
    },
    watch: {
      currentRow: {
        handler(val, oldval) {
          if (val) {
            this.detailsParams.settleTimeEnd = val.accountPeriod;
            this.detailsParams.settleTimeStart = val.accountPeriod;
            this.detailsParams.purchaseSubjectCode = val.purchaseSubjectCode;
            this.detailsParams.supplierSubjectCode = val.supplierSubjectCode;
            this.detailsParams.bizLineId = val.bizLineId;
          } else this.detailsParams = {};
          this.onSearch();
        },
        // 深度观察监听
        deep: true,
      },
    },

    created() {
      this.searchDate = setInitData(30);
      this.onSearch();
    },
    methods: {
      getParams() {
        this.searchParams.settleTimeStart = this.searchDate
          ? parseTime(this.searchDate[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.settleTimeEnd = this.searchDate
          ? parseTime(this.searchDate[1], '{y}-{m}-{d}')
          : '';
        return initSearchParams({
          ...this.searchParams,
          ...this.detailsParams,
        });
      },
      onSearch() {
        this.$emit('onSearch', this.getParams());
      },
      onExport() {
        this.$emit('onExport');
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.searchDate = setInitData(30);
      },
    },
  };
</script>

<style lang="scss" scoped></style>
