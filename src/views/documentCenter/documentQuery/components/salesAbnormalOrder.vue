<template>
  <div v-if="tab === 1" class="orderAbnormalData">
    <el-form ref="search" inline :model="searchParams">
      <el-form-item label="订单状态:">
        <el-select
          v-model="searchParams.orderStatus"
          clearable
          placeholder="请选择"
        >
          <template
            v-for="item in [6, 7].includes(searchParams.docType)
              ? tocOrderStatus
              : tobOrderStatus"
          >
            <el-option
              v-if="
                [6, 9].includes(searchParams.docType)
                  ? item.key < 100
                  : searchParams.docType === 7
                  ? item.key >= 100 && item.key < 105
                  : item.key >= 100
              "
              :key="item.key"
              :label="item.value"
              :value="item.key"
            ></el-option>
          </template>
        </el-select>
      </el-form-item>
      <el-form-item label="下单时间:">
        <el-date-picker
          v-model="orderDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item
        v-if="[6, 7].includes(searchParams.docType)"
        label="支付完成时间:"
      >
        <el-date-picker
          v-model="paymentDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="发货时间:">
        <el-date-picker
          v-model="deliveryDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item
        v-if="[6, 7].includes(searchParams.docType)"
        label="签收时间:"
      >
        <el-date-picker
          v-model="signDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="完成时间:">
        <el-date-picker
          v-model="finishDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="取消时间:">
        <el-date-picker
          v-model="cancelDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="销售主体:">
        <el-select
          v-model="searchParams.salesEntityCode"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in subjectDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item
        v-if="[6, 7].includes(searchParams.docType)"
        label="销售渠道:"
      >
        <el-select
          v-model="searchParams.salesChannelCode"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option label="VTN平台" value="VTN"></el-option>
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item
        v-if="[6, 7].includes(searchParams.docType)"
        label="销售店铺:"
      >
        <el-select
          v-model="searchParams.salesShopCode"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option label="VTN商城" value="VTN"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="发货仓库:">
        <el-select
          v-model="searchParams.warehouseCode"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in shippingWarehouse"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="单据类型:">
        <el-select
          v-model="searchParams.docType"
          clearable
          filterable
          placeholder="请选择"
          @change="handleChangeDocType"
        >
          <template v-for="item in documentType">
            <el-option
              v-if="[6, 7, 9, 10].includes(item.key)"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            ></el-option>
          </template>
        </el-select>
      </el-form-item>

      <el-form-item
        v-if="[6, 7].includes(searchParams.docType)"
        label="下单用户IDcode:"
      >
        <el-input
          v-model="searchParams.idCode"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item
        v-if="[6, 7].includes(searchParams.docType)"
        label="下单用户等级:"
      >
        <el-select
          v-model="searchParams.userLevel"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in userLevel"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="[6, 7].includes(searchParams.docType)"
        label="发货方式:"
      >
        <el-select
          v-model="searchParams.shippingType"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in shippingType"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="销售单号（售后单号）:">
        <el-input
          v-model="searchParams.serialNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="原销售单号:">
        <el-input
          v-model="searchParams.originSalesNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item
        v-if="[9, 10].includes(searchParams.docType)"
        label="关联采购单号:"
      >
        <el-input
          v-model="searchParams.relatedPurchaseDocNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <!-- <el-form-item
        v-if="[9, 10].includes(searchParams.docType)"
        label="订单状态:"
      >
        <el-select
          v-model="searchParams.orderStatus"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in purchaseOrderStatus"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item
        v-if="[9, 10].includes(searchParams.docType)"
        label="客户名称:"
      >
        <el-input
          v-model="searchParams.customer"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item
        v-if="[9, 10].includes(searchParams.docType)"
        label="收入结算状态:"
      >
        <el-select
          v-model="searchParams.revenueSettleStatus"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in settlementStatus"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="[9, 10].includes(searchParams.docType)"
        label="是否关联交易:"
      >
        <el-select
          v-model="searchParams.relatedTrade"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option label="是" :value="1"></el-option>
          <el-option label="否" :value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="[6, 7].includes(searchParams.docType)"
        label="合并单号:"
      >
        <el-input v-model="searchParams.paySn" clearable placeholder="请输入" />
      </el-form-item>
      <el-form-item label="是否处理:">
        <el-select v-model="searchParams.dealt" clearable placeholder="请选择">
          <el-option label="是" value="1"></el-option>
          <el-option label="否" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button type="primary" @click="goBack">返回</el-button>
      </el-form-item>
    </el-form>
    <div style="text-align: right; margin-bottom: 10px">
      异常订单数量总计{{ errSum.count }}，金额总计{{ errSum.total }}
    </div>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          btn-text="详情"
          type="text"
          size="small"
          permission-key=""
          @click="handelJump(2, scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
  <salesAbnormalOrderDetails
    v-else
    :current-row="currentRow"
    :purchase-order-status="purchaseOrderStatus"
    :order-type="orderType"
    @goBack="handelJump(1, null)"
  ></salesAbnormalOrderDetails>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    salesDocList,
    salesDocBusinessList,
    customerSum,
    businessSum,
  } from '@/api/documentCenter';
  import { parseTime, debounce, initSearchParams } from '@/utils';
  import salesAbnormalOrderDetails from './salesAbnormalOrderDetails.vue';
  export default {
    components: {
      dynamictable,
      salesAbnormalOrderDetails,
    },
    props: {
      currentInfo: {
        type: Object,
        default: null,
      },
      // 收入结算状态下拉数据
      settlementStatus: {
        type: Array,
        default: () => [],
      },
      // 订单类型
      orderType: {
        type: Array,
        default: () => [],
      },
      // 发货方式数据
      shippingType: {
        type: Array,
        default: () => [],
      },
      // 发货仓库数据
      shippingWarehouse: {
        type: Array,
        default: () => [],
      },
      // 贸易类型
      tradeType: {
        type: Array,
        default: () => [],
      },
      // 单据类型
      documentType: {
        type: Array,
        default: () => [],
      },
      // 订单状态
      purchaseOrderStatus: {
        type: Array,
        default: () => [],
      },
      subjectDict: {
        type: Array,
        default: () => [],
      },
      tocOrderStatus: {
        type: Array,
        default: () => [],
      },
      tobOrderStatus: {
        type: Array,
        default: () => [],
      },
      userLevel: {
        type: Array,
        default: () => [],
      },
    },

    data() {
      return {
        tab: 1,
        currentRow: {}, // 当前跳转携带数据
        searchParams: {
          errorFlag: true,
          dealt: '', // 是否处理
          canceledTimeEnd: '',
          canceledTimeStart: '',
          completedTimeEnd: '',
          completedTimeStart: '',
          docType: 6, // 6 toC正向,7 toC逆向,9tob正向,10tob逆向
          idCode: '',
          orderStatus: '',
          orderTimeEnd: '',
          orderTimeStart: '',
          paySn: '',
          preRevenueSettleStatus: '',
          revenueSettleStatus: '',
          salesChannelCode: '',
          salesEntityCode: '',
          salesShopCode: '',
          serialNo: '',
          shippingTimeEnd: '',
          shippingTimeStart: '',
          shippingType: '',
          signedTimeEnd: '',
          signedTimeStart: '',
          userLevel: '',
          warehouseCode: '',
          tradeType: '',
          relatedPurchaseDocNo: '',
          purchaseCode: '',
        },
        list: [],
        orderDate: '', // 下单时间
        paymentDate: '', // 支付时间
        deliveryDate: '', // 发货时间
        signDate: '', // 签收时间
        finishDate: '', // 完成时间
        cancelDate: '', // 取消时间
        errSum: {}, // 异常统计
        options: {
          loading: false,
          border: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },
    created() {
      if (this.currentInfo) {
        this.searchParams.docType = this.currentInfo.docType;
      }
      // 异常统计
      this.getSumApi();
      this.getList(true);
    },
    methods: {
      handleChangeDocType() {
        this.searchParams = {
          ...this.$options.data().searchParams,
          docType: this.searchParams.docType,
        };
        this.getList(true);
      },
      handelJump(tab, val) {
        this.tab = tab;
        this.currentRow = {
          ...val,
          docType: this.searchParams.docType,
        };
      },
      async getSumApi() {
        const sumApi = [6, 7].includes(this.searchParams.docType)
          ? customerSum
          : businessSum;
        const params = this.getParams();
        sumApi({ ...params, errorFlag: true }).then(res => {
          if (res) {
            this.errSum = res;
          }
        });
      },

      onSearch: debounce(function () {
        this.getSumApi();
        this.getList(true);
      }, 1000),
      getParams() {
        this.searchParams.orderTimeStart = this.orderDate
          ? parseTime(this.orderDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.orderTimeEnd = this.orderDate
          ? parseTime(this.orderDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.paidTimeStart = this.paymentDate
          ? parseTime(this.paymentDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.paidTimeEnd = this.paymentDate
          ? parseTime(this.paymentDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.shippingTimeStart = this.deliveryDate
          ? parseTime(this.deliveryDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.shippingTimeEnd = this.deliveryDate
          ? parseTime(this.deliveryDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.signedTimeStart = this.signDate
          ? parseTime(this.signDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.signedTimeEnd = this.signDate
          ? parseTime(this.signDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.completedTimeStart = this.finishDate
          ? parseTime(this.finishDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.completedTimeEnd = this.finishDate
          ? parseTime(this.finishDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        this.searchParams.canceledTimeStart = this.cancelDate
          ? parseTime(this.cancelDate[0], '{y}-{m}-{d} 00:00:00')
          : '';
        this.searchParams.canceledTimeEnd = this.cancelDate
          ? parseTime(this.cancelDate[1], '{y}-{m}-{d} 23:59:59')
          : '';
        const params = {
          ...this.searchParams,
          dealt: this.searchParams.dealt === '1',
          page: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        try {
          this.options.loading = true;
          const apiList = [6, 7].includes(this.searchParams.docType)
            ? salesDocList
            : salesDocBusinessList;
          const res = await apiList(initSearchParams(params));
          if (res) {
            this.options.loading = false;
            this.list = res && res.records ? res.records : [];
            this.pagination.total = res ? res.total : 0;
          }
        } catch (error) {
          this.options.loading = false;
        }
      },
      onReset() {
        this.searchParams = {
          ...this.$options.data().searchParams,
          docType: this.searchParams.docType,
        };
      },
      goBack() {
        this.$emit('goBack');
      },
      getColumns() {
        const columns = [
          {
            prop: 'serialNo',
            label: '销售单号(售后单号)',
            width: '100px',
          },
          {
            prop: 'paySn',
            label: '合并单号',
          },
          {
            prop: 'originSalesNo',
            label: '原销售单号',
          },

          {
            prop: 'salesEntity',
            label: '销售主体',
          },
          {
            prop: 'dealt',
            label: '是否处理',
            render: ({ dealt }) => <span>{dealt ? '是' : '否'}</span>,
          },
          {
            prop: 'salesChannel',
            label: '销售渠道',
          },
          {
            prop: 'salesShop',
            label: '销售店铺',
          },
          {
            prop: 'idCode',
            label: '用户IDCode',
          },
          {
            prop: 'userLevel',
            label: '下单用户等级',
          },
          {
            prop: 'quantity',
            label: '商品数量',
          },
          {
            prop: 'currency',
            label: '币种',
          },

          {
            prop: 'discountedAmount',
            label: '优惠总金额',
          },

          {
            prop: 'discountedGoodsAmount',
            label: '优惠后商品总金额(含税)',
          },
          {
            prop: 'discountedShippingFeeAmount',
            label: '优惠后运费金额',
          },
          {
            prop: 'discountedGoodsTaxAmount',
            label: '优惠后商品税费',
          },
          {
            prop: 'discountedShippingFeeTaxAmount',
            label: '优惠后运费税费',
          },
          {
            prop: 'orderAmount',
            label: '实际支付金额',
          },
          {
            prop: 'scoreCost',
            label: '消耗积分',
          },
          {
            prop: 'shippingWarehouse',
            label: '发货仓库',
          },
          {
            prop: 'orderFrom',
            label: '订单来源',
          },
          {
            prop: 'orderType',
            label: '订单类型',
          },
          {
            prop: 'shippingType',
            label: '发货方式',
          },
          {
            prop: 'countryCode',
            label: '收货地国家',
          },

          {
            prop: 'orderStatus',
            label: '订单状态',
          },
          {
            prop: 'orderTime',
            label: '下单时间',
          },
          {
            prop: 'paidTime',
            label: '支付时间',
          },
          {
            prop: 'shippingTime',
            label: '发货时间',
          },
          {
            prop: 'signedTime',
            label: '签收时间',
          },

          {
            prop: 'canceledTime',
            label: '取消时间',
          },
          {
            prop: 'completedTime',
            label: '完成时间',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '80',
            scopedSlots: { customRender: 'operation' },
          },
        ];
        const columns1 = [
          {
            prop: 'serialNo',
            label: '销售单号',
          },
          {
            prop: 'originSalesNo',
            label: '原销售单号',
          },
          {
            prop: 'relatedPurchaseOrderNo',
            label: '关联采购单号',
          },
          {
            prop: 'salesEntity',
            label: '销售主体',
          },
          {
            prop: 'customer',
            label: '客户名称',
          },
          {
            prop: 'contractNo',
            label: '合同编号',
          },
          {
            prop: 'salesChannel',
            label: '销售渠道',
          },
          {
            prop: 'relatedTrade',
            label: '是否关联交易',
            render: ({ relatedTrade }) => (
              <span>{relatedTrade ? '是' : '否'}</span>
            ),
          },
          {
            prop: 'quantity',
            label: '商品数量',
          },
          {
            prop: 'currency',
            label: '币种',
          },
          {
            prop: 'amountTaxInclusive',
            label: '订单金额(含税)',
          },
          {
            prop: 'taxAmount',
            label: '税额',
          },
          {
            prop: 'amountTaxExclusive',
            label: '订单金额(未税)',
          },
          {
            prop: 'shippingWarehouse',
            label: '(退)发货仓库',
          },
          {
            prop: 'docType',
            label: '单据类型',
          },
          {
            prop: 'orderStatus',
            label: '订单状态',
          },
          {
            prop: 'orderTime',
            label: '下单时间',
          },
          {
            prop: 'shippingTime',
            label: '发货时间',
          },
          {
            prop: 'canceledTime',
            label: '取消时间',
          },
          {
            prop: 'completedTime',
            label: '完成时间',
          },
          {
            prop: 'revenueSettleStatusCopy',
            label: '收入结算状态',
          },
          {
            prop: 'revenueSettledAmount',
            label: '收入应结算金额(含税)',
          },
          {
            prop: 'revenueSettledAmount',
            label: '收入已结算金额(含税)',
          },
          {
            prop: 'revenueUnsettledAmount',
            label: '收入未结算金额(含税)',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '80',
            scopedSlots: { customRender: 'operation' },
          },
        ];
        return [6, 7].includes(this.searchParams.docType) ? columns : columns1;
      },
    },
  };
</script>

<style lang="scss"></style>
