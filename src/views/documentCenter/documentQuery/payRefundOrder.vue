<template>
  <div>
    <div v-if="tab === 2">
      <el-button
        style="font-size: 16px; margin-bottom: 10px"
        type="text"
        @click="handelJump(1, null)"
      >
        返回
      </el-button>
    </div>
    <el-form ref="search" inline :model="searchParams">
      <el-form-item
        prop="payId"
        :rules="[
          {
            validator: rules.validatePayId,
            trigger: 'change',
          },
        ]"
        label="支付单号:"
      >
        <el-input
          v-model="searchParams.payId"
          clearable
          placeholder="请输入支付单编号"
        />
      </el-form-item>
      <el-form-item label="销售单号(售后单号):">
        <el-input
          v-model="searchParams.refundOrderId"
          clearable
          placeholder="请输入原系统的销售单号(如W单号)"
        />
      </el-form-item>
      <el-form-item
        prop="refundId"
        :rules="[
          {
            validator: rules.validatePayId,
            trigger: 'change',
          },
        ]"
        label="退款单号:"
      >
        <el-input
          v-model="searchParams.refundId"
          clearable
          placeholder="请输入退款单号"
        />
      </el-form-item>
      <el-form-item label="原系统业务发生时间:">
        <el-date-picker
          v-model="transactionDate"
          type="datetimerange"
          :clearable="false"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="创建时间:">
        <el-date-picker
          v-model="refundCreatedDate"
          type="datetimerange"
          :clearable="false"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :picker-options="pickerOptions"
        ></el-date-picker>
      </el-form-item>
      <el-form-item
        prop="paymentId"
        :rules="[
          {
            validator: rules.validatePayId,
            trigger: 'change',
          },
        ]"
        label="支付流水号:"
      >
        <el-input
          v-model="searchParams.paymentId"
          clearable
          placeholder="请输入支付流水号"
        />
      </el-form-item>
      <el-form-item label="退款单状态:">
        <el-select
          v-model="searchParams.statusList"
          clearable
          multiple
          placeholder="请选择"
        >
          <el-option :value="1" label="退款中"></el-option>
          <el-option :value="2" label="退款成功"></el-option>
          <el-option :value="3" label="退款失败"></el-option>
          <el-option :value="4" label="已处理"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="{ ...options }"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="props">
        <ac-permission-button
          slot="reference"
          btn-text="查看退款流水"
          type="text"
          size="small"
          permission-key=""
          @click="handelJump(2, props.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import { isNumber } from '@/utils/validate';
  import { getRefundList, getRefundmentList } from '@/api/documentCenter';
  import { debounce, parseTime, initSearchParams } from '@/utils';
  export default {
    components: {
      dynamictable,
    },
    data() {
      const validatePayId = (rule, value, callback) => {
        const val = value ? value.trim() : '';
        if (!isNumber(val)) {
          callback(new Error('单号必须为数字'));
        } else {
          callback();
        }
      };
      return {
        tab: 1,
        list: [],
        transactionDate: '',
        refundCreatedDate: '',
        rules: {
          validatePayId,
        },
        searchParams: {
          payId: '',
          refundOrderId: '',
          refundId: '',
          transactionTimeStart: '',
          transactionTimeEnd: '',
          refundCreatedTimeStart: '',
          refundCreatedTimeEnd: '',
          paymentId: '',
          statusList: [],
        },
        search: {}, // 缓存搜索条件
        options: {
          loading: false,
          border: true,
        },
        pageSize: 1, // 缓存页数
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        copyDate: '',
        pickerOptions: {
          onPick: ({ maxDate, minDate }) => {
            this.copyDate = minDate.getTime();
            if (maxDate) {
              this.copyDate = '';
            }
          },
          disabledDate: time => {
            if (this.copyDate !== '') {
              const one = 30 * 24 * 3600 * 1000;
              const minTime = this.copyDate - one;
              const maxTime = this.copyDate + one;
              return time.getTime() < minTime || time.getTime() > maxTime;
            }
          },
        },
      };
    },
    created() {
      const payId = this.$route.query.payId;
      this.searchParams.payId = payId;
      if (payId) {
        this.getList();
      }
    },
    methods: {
      handelJump(val, row) {
        this.tab = val;
        this.list = [];
        if (val === 1) {
          this.getCacheData();
        } else {
          this.setCacheData(row);
        }
        this.getList();
      },
      getCacheData() {
        this.pagination.total = null;
        // 返回记住页数
        this.pagination.pageSize = this.pageSize;
        const {
          transactionTimeStart,
          transactionTimeEnd,
          refundCreatedTimeStart,
          refundCreatedTimeEnd,
          refundmentCreatedTimeStart,
          refundmentCreatedTimeEnd,
        } = this.search;
        this.transactionDate = [transactionTimeStart, transactionTimeEnd];
        this.refundCreatedDate = [
          this.tab === 1 ? refundCreatedTimeStart : refundmentCreatedTimeStart,
          this.tab === 1 ? refundCreatedTimeEnd : refundmentCreatedTimeEnd,
        ];
        this.searchParams = { ...this.search };
      },
      setCacheData(row) {
        this.pageSize = this.pagination.pageSize;
        this.search = { ...this.searchParams };
        this.searchParams = {
          refundId: row.refundId,
        };
        this.transactionDate = '';
        this.refundCreatedDate = '';
        Object.assign(this.$data.pagination, this.$options.data().pagination);
      },
      getParams() {
        this.searchParams.transactionTimeStart = this.transactionDate
          ? parseTime(this.transactionDate[0], '{y}-{m}-{d} {h}:{i}:{s}')
          : '';
        this.searchParams.transactionTimeEnd = this.transactionDate
          ? parseTime(this.transactionDate[1], '{y}-{m}-{d} {h}:{i}:{s}')
          : '';
        this.searchParams[
          this.tab === 1
            ? 'refundCreatedTimeStart'
            : 'refundmentCreatedTimeStart'
        ] = this.refundCreatedDate
          ? parseTime(this.refundCreatedDate[0], '{y}-{m}-{d} {h}:{i}:{s}')
          : '';
        this.searchParams[
          this.tab === 1 ? 'refundCreatedTimeEnd' : 'refundmentCreatedTimeEnd'
        ] = this.refundCreatedDate
          ? parseTime(this.refundCreatedDate[1], '{y}-{m}-{d} {h}:{i}:{s}')
          : '';
        const { statusList = [] } = this.searchParams;
        const params = {
          ...this.searchParams,
          statusList: statusList.length ? statusList.join(',') : '',
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const getApiList = this.tab === 1 ? getRefundList : getRefundmentList;
        try {
          const res = await getApiList(params);
          this.options.loading = false;
          if (res) {
            this.list = res && res.list ? res.list : [];
            this.pagination.total = res ? res.total : 0;
          }
        } catch (error) {
          this.options.loading = false;
        }
      },
      onSearch: debounce(function (e) {
        const searchParams = initSearchParams(this.getParams());
        if (Object.keys(searchParams).length === 2) {
          return this.$message.warning('请最少选择一个查询条件');
        }
        this.$refs.search.validate(async valid => {
          if (!valid) return;
          this.getList(true);
        });
      }, 1000),
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },

      getColumns() {
        const columns = [
          {
            prop: 'refundId',
            label: '退款单号',
          },
          {
            prop: 'paymentId',
            label: '支付流水号',
          },
          {
            prop: 'payId',
            label: '支付单号',
          },
          {
            prop: 'paySn',
            label: '租户订单号',
          },
          {
            prop: 'memberId',
            label: '会员ID',
          },
          {
            prop: 'companyName',
            label: '收单主体名称',
          },
          {
            prop: 'tenantId',
            label: '租户ID',
          },
          {
            prop: 'tenantName',
            label: '租户名称',
          },

          {
            prop: 'currencyUnit',
            label: '币种',
          },
          {
            prop: 'amount',
            label: '退款金额',
          },

          {
            prop: 'refundOrderId',
            label: '销售单号(售后单号)',
          },

          // {
          //   prop: 'amount',
          //   label: '销售单金额(CNY)',
          // },
          {
            prop: 'refundTypeName',
            label: '退款类型',
          },
          {
            prop: 'statusName',
            label: '退款单状态',
          },
          {
            prop: 'subStatusName',
            label: '子状态',
          },
          {
            prop: 'paymentChannelMerchantId',
            label: '支付渠道侧商户号',
          },
          {
            prop: 'channelOrderId',
            label: '支付渠道侧订单号',
          },
          {
            prop: 'transactionTime',
            label: '原系统业务发生时间',
          },
          {
            prop: 'refundCreatedTime',
            label: '创建时间',
          },
          {
            prop: 'refundUpdatedTime',
            label: '更新时间',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            minWidth: '80px',
            maxWidth: '140px',
            scopedSlots: { customRender: 'operation' },
          },
        ];
        const columns1 = [
          {
            prop: 'refundmentId',
            label: '退款流水号',
          },
          {
            prop: 'refundId',
            label: '退款单号',
          },
          {
            prop: 'paymentId',
            label: '支付流水号',
          },
          {
            prop: 'payId',
            label: '支付单号',
          },
          {
            prop: 'paySn',
            label: '租户订单号',
          },
          {
            prop: 'memberId',
            label: '会员ID',
          },
          {
            prop: 'companyName',
            label: '收单主体名称',
          },
          {
            prop: 'tenantId',
            label: '租户ID',
          },
          {
            prop: 'tenantName',
            label: '租户名称',
          },

          {
            prop: 'currencyUnit',
            label: '币种',
          },
          {
            prop: 'amount',
            label: '退款金额',
          },
          {
            prop: 'refundOrderId',
            label: '销售单号(售后单号)',
          },
          // {
          //   prop: 'amount',
          //   label: '销售单金额(CNY)',
          // },
          {
            prop: 'refundTypeName',
            label: '退款类型',
          },
          {
            prop: 'statusName',
            label: '退款单状态',
          },
          {
            prop: 'subStatusName',
            label: '子状态',
          },
          {
            prop: 'paymentChannelMerchantId',
            label: '支付渠道侧商户号',
          },
          {
            prop: 'channelOrderId',
            label: '支付渠道侧订单号',
          },
          {
            prop: 'transactionTime',
            label: '原系统业务发生时间',
          },
          {
            prop: 'refundmentCreateTime',
            label: '创建时间',
          },
          {
            prop: 'refundmentUpdateTime',
            label: '更新时间',
          },

          {
            prop: 'operatorName',
            label: '操作人名称',
          },
          {
            prop: 'operatorRemark',
            label: '操作人备注',
          },
        ];
        return this.tab === 1 ? columns : columns1;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
