<template>
  <div v-if="tab === 1">
    <el-form ref="formSearchParams" inline :model="searchParams">
      <el-form-item label="单据类型:">
        <el-select
          v-model="searchParams.chargeDocType"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in chargeDocType"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="计费单号:">
        <el-input
          v-model="searchParams.serialNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="原始单号:">
        <el-input
          v-model="searchParams.originSerialNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="关联合同号:">
        <el-input
          v-model="searchParams.contractNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="申请时间:">
        <el-date-picker
          v-model="createDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="更新时间:">
        <el-date-picker
          v-model="updateDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="完成时间:">
        <el-date-picker
          v-model="completionDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="收款方名称:">
        <el-select
          v-model="searchParams.recipientId"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in subjectDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="付款方名称:">
        <el-select
          v-model="searchParams.payerId"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="item in supplyDict"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="结算状态:">
        <el-select
          v-model="searchParams.settlementStatus"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in settlementStatus"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="支付状态:">
        <el-select
          v-model="searchParams.paymentStatus"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in paymentStatus"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="结算方式:">
        <el-select
          v-model="searchParams.paymentMethod"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in paymentMethod"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="paymentStatus" slot-scope="scope">
        <el-tag :type="tagType[scope.row.paymentStatus]">
          {{ ['未支付', '部分支付', '全部支付'][scope.row.paymentStatus] }}
        </el-tag>
      </template>
      <template slot="operation" slot-scope="{ row }">
        <ac-permission-button
          v-if="row.chargeDocType !== '002001'"
          slot="reference"
          btn-text="详情"
          type="text"
          size="small"
          permission-key="purchaseAaleAgreementMgt-edit"
          @click="handleDetails(2, row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
  <div v-else>
    <div class="billingSheet-details">
      <div class="h-title">
        <h1 class="title" style="display: inline-block">
          {{ billingSheetOrder.currency }} {{ billingSheetOrder.paidAmount }}
        </h1>
        <div style="margin-left: 10px; display: inline-block">
          <h3 style="display: block" type="success">
            {{ billingSheetOrder.chargeDocTypeCopy }}
          </h3>
          <el-tag type="success">
            {{
              ['未支付', '部分支付', '全部支付'][
                billingSheetOrder.paymentStatus
              ]
            }}
          </el-tag>
        </div>
        <el-button
          style="margin-left: 70%; display: inline-block"
          type="primary"
          @click="handleDetails(1)"
        >
          返回
        </el-button>
      </div>
      <el-divider></el-divider>
      <el-row>
        <el-col class="border-right" :span="3">
          <div class="billingSheet-item">
            <p>申请时间</p>
            <p>{{ parseTime(billingSheetOrder.applyTime) || '--' }}</p>
          </div>
        </el-col>
        <el-col class="border-right" :span="3">
          <div class="billingSheet-item">
            <p>更新时间</p>
            <p>
              {{ parseTime(billingSheetOrder.updateTime) || '--' }}
            </p>
          </div>
        </el-col>
        <el-col class="border-right" :span="3">
          <div class="billingSheet-item">
            <p>完成时间</p>
            <p>{{ parseTime(billingSheetOrder.completeTime) || '--' }}</p>
          </div>
        </el-col>
        <el-col class="border-right" :span="2">
          <div class="billingSheet-item">
            <p>关联合同号</p>
            <p style="word-wrap: break-word">
              {{ billingSheetOrder.relatedContractNo || '--' }}
            </p>
          </div>
        </el-col>
        <el-col class="border-right" :span="2">
          <div class="billingSheet-item">
            <p>计费单号</p>
            <p style="word-wrap: break-word">
              {{ billingSheetOrder.serialNo || '--' }}
            </p>
          </div>
        </el-col>
        <el-col class="border-right" :span="2">
          <div class="billingSheet-item">
            <p>申请金额</p>
            <p>{{ billingSheetOrder.payableAmount || '--' }}</p>
          </div>
        </el-col>
        <el-col class="border-right" :span="2">
          <div class="billingSheet-item">
            <p>未付金额</p>
            <p>{{ billingSheetOrder.unpaidAmount || '--' }}</p>
          </div>
        </el-col>
        <el-col class="border-right" :span="2">
          <div class="billingSheet-item">
            <p>结算方式</p>
            <p>
              {{
                getText(billingSheetOrder.paymentMethod, paymentMethod) || '--'
              }}
            </p>
          </div>
        </el-col>
        <el-col class="border-right" :span="2">
          <div class="billingSheet-item">
            <p>收款方名称</p>
            <p>{{ billingSheetOrder.recipientName || '--' }}</p>
          </div>
        </el-col>
        <el-col :span="2">
          <div class="billingSheet-item">
            <p>付款方名称</p>
            <p>{{ billingSheetOrder.payerName || '--' }}</p>
          </div>
        </el-col>
      </el-row>
    </div>
    <div style="margin-top: 20px">
      <h1>计费单明细</h1>
      <el-divider></el-divider>
    </div>
    <el-form ref="formSearch" inline :model="detailsSearchParams">
      <el-form-item
        required
        :error="detailsSearchParams.serialNo ? '' : '计费单号必填'"
        label="计费单号:"
      >
        <el-input
          v-model="detailsSearchParams.serialNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="原始单号:">
        <el-input
          v-model="detailsSearchParams.originSerialNo"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="品牌名称:">
        <el-input
          v-model="detailsSearchParams.brandName"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="商品条码:">
        <el-input
          v-model="detailsSearchParams.goodsBarcode"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="结算状态:">
        <el-select
          v-model="detailsSearchParams.settlementStatus"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in settlementStatus"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="支付状态:">
        <el-select
          v-model="detailsSearchParams.paymentStatus"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in paymentStatus"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    chargeDocList,
    getDicValues,
    getListSelector,
    chargeDocItemsList,
  } from '@/api/documentCenter';
  import { parseTime, debounce, initSearchParams, setInitData } from '@/utils';
  export default {
    components: {
      dynamictable,
    },
    data() {
      return {
        tab: 1,
        completionDate: '',
        updateDate: '',
        createDate: '',
        tagType: ['', 'success', 'danger', 'info'],
        billingSheetOrder: {},
        detailsSearchParams: {
          brandName: '',
          contractNo: '',
          goodsBarcode: '',
          originSerialNo: '',
          paymentStatus: '',
          serialNo: '',
          settlementStatus: '',
        },
        searchParams: {
          applyEndTime: '',
          applyStartTime: '',
          chargeDocType: '',
          completeEndTime: '',
          completeStartTime: [],
          contractNo: '',
          originSerialNo: '',
          payerId: '',
          paymentMethod: '',
          paymentStatus: '',
          recipientId: '',
          serialNo: '',
          settlementStatus: '',
          updateEndTime: '',
          updateStartTime: '',
        },
        list: [],
        paymentMethod: [],
        paymentStatus: [],
        settlementStatus: [],
        chargeDocType: [],
        subjectDict: [],
        supplyDict: [],
        options: {
          loading: false,
          border: true,
        },
        pageSize: 1,
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },
    created() {
      this.createDate = setInitData(90);
      getDicValues({}).then(res => {
        if (res) {
          this.paymentMethod = res.settlementMethod;
          this.paymentStatus = res.paymentStatus;
          this.settlementStatus = res.settlementStatus;
          this.chargeDocType = res.chargeDocType;
        }
      });
      getListSelector({}).then(res => {
        if (res) {
          this.subjectDict = res.subjectDict;
          this.supplyDict = res.supplyDict;
        }
      });
      this.getList(true);
    },
    methods: {
      parseTime,
      getText(val, list) {
        let text = '';
        list.map(item => {
          if (item.key === val) {
            text = item.value;
          }
        });
        return text;
      },
      handleDetails(val, row) {
        this.tab = val;
        if (val === 1) {
          // 返回记住页数
          this.pagination.total = null;
          this.pagination.pageSize = this.pageSize;
        } else {
          this.init(row);
        }
        this.getList();
      },
      init(row) {
        this.detailsSearchParams.serialNo = row.serialNo;
        this.pageSize = this.pagination.pageSize;
        Object.assign(this.$data.pagination, this.$options.data().pagination);
      },
      getParams() {
        this.searchParams.applyStartTime = this.createDate
          ? parseTime(this.createDate[0])
          : '';
        this.searchParams.applyEndTime = this.createDate
          ? parseTime(this.createDate[1])
          : '';

        this.searchParams.updateStartTime = this.updateDate
          ? parseTime(this.updateDate[0])
          : '';
        this.searchParams.updateEndTime = this.updateDate
          ? parseTime(this.updateDate[1])
          : '';

        this.searchParams.completeStartTime = this.completionDate
          ? parseTime(this.completionDate[0])
          : '';
        this.searchParams.completeEndTime = this.completionDate
          ? parseTime(this.completionDate[1])
          : '';

        const params = {
          page: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return this.tab === 1
          ? {
              ...this.searchParams,
              ...params,
            }
          : {
              ...this.detailsSearchParams,
              ...params,
            };
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        this.options.loading = true;
        const params = this.getParams();
        const getApiList = this.tab === 1 ? chargeDocList : chargeDocItemsList;
        const res = await getApiList(initSearchParams(params));
        this.options.loading = false;
        if (res) {
          if (
            this.tab === 2 &&
            Array.isArray(res.records) &&
            res.records.length
          )
            this.billingSheetOrder = {
              ...res.records[0].chargeDoc,
              chargeDocTypeCopy: res.records[0].chargeDocItem.chargeDocTypeCopy,
            };
          this.list = res && res.records ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        }
      },
      onSearch: debounce(function () {
        if (this.tab === 2 && !this.detailsSearchParams.serialNo) {
          return;
        }
        this.getList(true);
      }, 1000),
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.detailsSearchParams = {
          ...this.$options.data().detailsSearchParams,
          serialNo: this.billingSheetOrder.serialNo,
        };
        this.createDate = setInitData(90);
        this.completionDate = '';
        this.updateDate = '';
      },
      getColumns() {
        const columns = [
          {
            prop: 'serialNo',
            label: '计费单号',
          },
          {
            prop: 'paymentStatus',
            label: '支付状态',
            scopedSlots: { customRender: 'paymentStatus' },
          },
          {
            prop: 'relatedContractNo',
            label: '关联合同号',
          },
          {
            prop: 'recipientName',
            label: '收款方名称',
          },
          {
            prop: 'payerName',
            label: '付款方名称',
          },
          {
            prop: 'paymentMethod',
            label: '结算方式',
            render: ({ paymentMethod }) => (
              <span>{this.getText(paymentMethod, this.paymentMethod)}</span>
            ),
          },
          {
            prop: 'settlementStatus',
            label: '结算状态',
            render: ({ settlementStatus }) => (
              <span>
                {this.getText(settlementStatus, this.settlementStatus)}
              </span>
            ),
          },
          {
            prop: 'currency',
            label: '币种',
          },
          {
            prop: 'payableAmount',
            label: '应付金额',
          },
          {
            prop: 'paidAmount',
            label: '已付金额',
          },
          {
            prop: 'unpaidAmount',
            label: '未付金额',
          },
          {
            prop: 'chargeDocTypeCopy',
            label: '单据类型',
          },
          {
            prop: 'principal',
            label: '承责方',
          },
          {
            prop: 'applyTime',
            label: '申请时间',
            render: ({ applyTime }) => <span>{parseTime(applyTime)}</span>,
          },
          {
            prop: 'updateTime',
            label: '更新时间',
            render: ({ updateTime }) => <span>{parseTime(updateTime)}</span>,
          },
          {
            prop: 'completeTime',
            label: '完成时间',
            render: ({ completeTime }) => (
              <span>{parseTime(completeTime)}</span>
            ),
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '100',
            scopedSlots: { customRender: 'operation' },
          },
        ];
        const columns1 = [
          {
            prop: 'chargeDocItem.id',
            label: '计费单明细行号',
          },
          {
            prop: 'chargeDocItem.originSerialNo',
            label: '原始单号',
          },
          // {
          //   prop: 'chargeDocItem.originItemSerialNo',
          //   label: '原明细行号',
          // },
          {
            prop: 'chargeDocItem.brand.brandName',
            label: '品牌名称',
          },
          {
            prop: 'chargeDocItem.barcode',
            label: '商品条码',
          },
          {
            prop: 'chargeDocItem.goodsName',
            label: '商品名称',
          },
          {
            prop: 'chargeDocItem.priceTaxInclusive',
            label: '单价(含税)',
          },
          {
            prop: 'chargeDocItem.quantity',
            label: '数量',
          },
          {
            prop: 'chargeDocItem.calRate',
            label: '计算比例(%)',
          },
          {
            prop: 'chargeDocItem.payableAmount',
            label: '应付金额',
          },
          // {
          //   prop: 'chargeDocItem.paidAmount',
          //   label: '已付金额',
          // },
          // {
          //   prop: 'chargeDocItem.unpaidAmount',
          //   label: '未付金额',
          // },
          {
            prop: 'chargeDocItem.chargeDocTypeCopy',
            label: '计费类型',
          },
          {
            prop: 'chargeDocItem.settlementStatus',
            label: '结算状态',
            render: ({ chargeDocItem = {} }) => (
              <span>
                {this.getText(
                  chargeDocItem.settlementStatus,
                  this.settlementStatus,
                )}
              </span>
            ),
          },
          {
            prop: 'chargeDocItem.paymentStatus',
            label: '支付状态',
            render: ({ chargeDocItem = {} }) => (
              <span>
                {this.getText(chargeDocItem.paymentStatus, this.paymentStatus)}
              </span>
            ),
          },
        ];
        const columns2 = [
          {
            prop: 'chargeDoc.serialNo',
            label: '计费单号',
          },
          {
            prop: 'chargeDoc.paymentStatus',
            label: '支付状态',
            render: ({ paymentStatus }) => (
              <span>{this.getText(paymentStatus, this.paymentStatus)}</span>
            ),
          },
          {
            prop: 'chargeDoc.recipientName',
            label: '收款方名称',
          },
          {
            prop: 'chargeDoc.payerName',
            label: '付款方名称',
          },
          {
            prop: 'chargeDoc.paymentMethod',
            label: '结算方式',
            render: ({ paymentMethod }) => (
              <span>{this.getText(paymentMethod, this.paymentMethod)}</span>
            ),
          },
          {
            prop: 'chargeDoc.currency',
            label: '币种',
          },
          {
            prop: 'chargeDoc.payableAmount',
            label: '应付金额',
          },
          {
            prop: 'chargeDoc.paidAmount',
            label: '已付金额',
          },
          {
            prop: 'chargeDocItem.unpaidAmount',
            label: '未付金额',
          },
          {
            prop: 'chargeDoc.chargeDocTypeCopy',
            label: '单据类型',
          },
          {
            prop: 'chargeDoc.applyTime',
            label: '申请时间',
          },
          {
            prop: 'chargeDoc.updateTime',
            label: '更新时间',
          },
          {
            prop: 'chargeDoc.completeTime',
            label: '完成时间',
          },
        ];

        return this.tab === 1 ? columns : columns1;
      },
    },
  };
</script>
<style lang="scss" scoped>
  .billingSheet-details {
    .border-right {
      border-right: 1px solid #bebebe;
      height: 80px;
    }
    .billingSheet-item {
      text-align: center;
    }
  }
</style>
