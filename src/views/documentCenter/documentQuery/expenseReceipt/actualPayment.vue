<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-04-26 17:18:32
 * @LastEditTime: 2022-07-26 14:50:45
 * @LastEditors: xuxiang
 * @Reference: 
-->
<template>
  <div>
    <el-form inline>
      <el-form-item label="推单号:">
        <el-input
          v-model="searchParams.pushSn"
          placeholder="请输入推单号"
        ></el-input>
      </el-form-item>
      <el-form-item label="收款方名称:">
        <el-input
          v-model="searchParams.receiveCompany"
          placeholder="请输入收款方名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="付款方名称:">
        <el-input
          v-model="searchParams.payCompany"
          placeholder="请输入付款方名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="发货仓库:">
        <el-input
          v-model="searchParams.depotName"
          placeholder="请输入发货仓库"
        ></el-input>
      </el-form-item>
      <el-form-item label="支付时间:">
        <el-date-picker
          v-model="paymentDate"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="发货时间:">
        <el-date-picker
          v-model="deliveryDate"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';

  import { parseTime } from '@/utils';
  import { comprehensiveTaxList } from '@/api/documentCenter';

  export default {
    components: {
      dynamictable,
    },
    data() {
      let columns = [
        {
          prop: 'pushSn',
          label: '履约单号',
        },
        {
          prop: 'orderSn',
          label: '关联销售单号',
        },
        {
          prop: 'receiveCompany',
          label: '收款方名称',
        },
        {
          prop: 'payCompany',
          label: '付款方名称',
        },
        {
          prop: 'depotName',
          label: '发货仓库',
        },
        {
          prop: 'collectionTaxFee',
          label: '代收税费',
        },
        {
          prop: 'paidTaxFee',
          label: '实收税费',
        },

        {
          prop: 'returnTaxFee',
          label: '退回税费',
        },
        {
          prop: 'realPayTaxFee',
          label: '实付税费',
        },
        {
          prop: 'a',
          label: '费用类型',
          render: () => <span>{'跨境综合税'}</span>,
        },
        {
          prop: 'freeTaxTypeName',
          label: '是否包税',
        },
        {
          prop: 'orderStatusName',
          label: '订单状态',
        },
        {
          prop: 'paymentTime',
          label: '支付时间',
        },
        {
          prop: 'deliveryTime',
          label: '发货时间',
        },
      ];

      return {
        paymentDate: '',
        deliveryDate: '',
        searchParams: {
          deliveryTimeEnd: '',
          deliveryTimeStart: '',
          paymentTimeEnd: '',
          paymentTimeStart: '',
          depotName: '',
          payCompany: '',
          pushSn: '',
          receiveCompany: '',
        },
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
        columns,
      };
    },
    created() {
      // this.getList(true);
    },
    methods: {
      getParams() {
        const paymentDate = this.paymentDate;
        const deliveryDate = this.deliveryDate;
        this.searchParams.paymentTimeStart = paymentDate
          ? parseTime(paymentDate[0], '{y}-{m}-{d} {h}:{i}:{s}')
          : '';
        this.searchParams.paymentTimeEnd = paymentDate
          ? parseTime(paymentDate[1], '{y}-{m}-{d} {h}:{i}:{s}')
          : '';
        this.searchParams.deliveryTimeStart = deliveryDate
          ? parseTime(deliveryDate[0], '{y}-{m}-{d} {h}:{i}:{s}')
          : '';
        this.searchParams.deliveryTimeEnd = deliveryDate
          ? parseTime(deliveryDate[1], '{y}-{m}-{d} {h}:{i}:{s}')
          : '';
        const params = {
          ...this.searchParams,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        try {
          const res = await comprehensiveTaxList(params);
          this.options.loading = false;

          if (res) {
            this.list = res ? res.list : [];
            this.pagination.total = res ? res.total : 0;
          }
        } catch (error) {
          this.options.loading = false;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },
    },
  };
</script>
<style lang="scss"></style>
