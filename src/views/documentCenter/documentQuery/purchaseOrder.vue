<template>
  <div>
    <purchaseOrderList
      v-show="tab === 1"
      :tab="tab"
      :biz-line-id="bizLineId"
      :delivery-status="deliveryStatus"
      :received-status="receivedStatus"
      :settlement-status="settlementStatus"
      :payment-status="paymentStatus"
      :document-type="documentType"
      :order-type="orderType"
      :sell-warehouse="sellWarehouse"
      :purchase-order-status="purchaseOrderStatus"
      :subject-dict="subjectDict"
      :supply-dict="supplyDict"
      :pay-model-type="payModelType"
      :brand-list="brandList"
      :settlement-method="settlementMethod"
      @handleDetail="(tab, currentRow) => handelJump(tab, currentRow)"
    ></purchaseOrderList>
    <purchaseOrderDetail
      v-show="tab === 2"
      :tab="tab"
      :biz-line-id="bizLineId"
      :delivery-status="deliveryStatus"
      :received-status="receivedStatus"
      :settlement-status="settlementStatus"
      :payment-status="paymentStatus"
      :document-type="documentType"
      :order-type="orderType"
      :sell-warehouse="sellWarehouse"
      :purchase-order-status="purchaseOrderStatus"
      :subject-dict="subjectDict"
      :supply-dict="supplyDict"
      :current-row="currentRow"
      :brand-list="brandList"
      @goBack="val => handelJump(1)"
      @handleDetail="(val, currentRow) => handelJump(val, currentRow)"
    ></purchaseOrderDetail>
    <checkNumberShipments
      v-if="[3, 4].includes(tab)"
      :tab="tab"
      :current-row="currentRow"
      :order-type="orderType"
      :received-status="receivedStatus"
      :delivery-status="deliveryStatus"
      :purchase-order-status="purchaseOrderStatus"
      @goBack="handelJump(entryPage)"
    ></checkNumberShipments>
    <purchaseOrderDetails
      v-if="tab === 5"
      :current-row="currentRow"
      :order-type="orderType"
      :purchase-order-status="purchaseOrderStatus"
      :delivery-status="deliveryStatus"
      :received-status="receivedStatus"
      :settlement-status="settlementStatus"
      :payment-status="paymentStatus"
      @goBack="handelJump(entryPage)"
    ></purchaseOrderDetails>
    <orderAbnormalData
      v-if="tab === 6"
      :purchase-order-status="purchaseOrderStatus"
      :settlement-method="settlementMethod"
      :subject-dict="subjectDict"
      :supply-dict="supplyDict"
      :brand-list="brandList"
      :order-type="orderType"
      :pay-model-type="payModelType"
      :current-info="currentRow"
      @goBack="handelJump(1)"
    ></orderAbnormalData>
  </div>
</template>

<script>
  import purchaseOrderList from './components/purchaseOrderList';
  import purchaseOrderDetail from './components/purchaseOrderDetail';
  import checkNumberShipments from './components/checkNumberShipments';
  import purchaseOrderDetails from './components/purchaseOrderDetails';
  import orderAbnormalData from './components/orderAbnormalData';
  import {
    getDicValues,
    getListSelector,
    getQueryBrandListBySupplierId,
  } from '@/api/documentCenter';
  export default {
    components: {
      purchaseOrderList,
      purchaseOrderDetail,
      checkNumberShipments,
      purchaseOrderDetails,
      orderAbnormalData,
    },
    data() {
      return {
        entryPage: 1, // 1, 采购订单列表页， 2, 采购订单明细
        currentRow: null,
        tab: 1, // 1 采购订单列表页， 2 明细 3 发货收货页 4 结算数量页面   5 详情 6 异常数据页
        bizLineId: [],
        deliveryStatus: [],
        receivedStatus: [],
        settlementStatus: [],
        paymentStatus: [],
        documentType: [],
        orderType: [],
        sellWarehouse: [],
        purchaseOrderStatus: [],
        subjectDict: [],
        supplyDict: [],
        settlementMethod: [],
        brandList: [],
        payModelType: [],
      };
    },
    watch: {
      tab: {
        handler(val, oldval) {
          if ([1, 2].includes(val)) {
            this.entryPage = val;
          }
        },
        // 深度观察监听
        deep: true,
      },
    },
    created() {
      getQueryBrandListBySupplierId({}).then(res => {
        if (res) {
          this.brandList = res;
        }
      });
      getListSelector({}).then(res => {
        if (res) {
          this.subjectDict = res.subjectDict;
          this.supplyDict = res.supplyDict;
        }
      });
      getDicValues({}).then(res => {
        if (res) {
          this.bizLineId = res.bizLineId;
          this.deliveryStatus = res.deliveryStatus;
          this.receivedStatus = res.receivedStatus;
          this.settlementStatus = res.settlementStatus;
          this.payModelType = res.payModelType;
          this.paymentStatus = res.paymentStatus;
          this.documentType = res.documentType;
          this.orderType = res.orderType;
          this.sellWarehouse = res.sellWarehouse;
          this.purchaseOrderStatus = res.purchaseOrderStatus;
          this.settlementMethod = res.settlementMethod;
        }
      });
    },
    methods: {
      handelJump(tab, currentRow) {
        if (currentRow) this.currentRow = currentRow;
        this.tab = tab;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
