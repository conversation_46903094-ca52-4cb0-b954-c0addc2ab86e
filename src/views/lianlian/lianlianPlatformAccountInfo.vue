<template>
  <div class="platform-account-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <i class="el-icon-bank-card"></i>
        平台账户信息
      </h2>
      <el-button
        type="primary"
        icon="el-icon-refresh"
        :loading="loading"
        class="refresh-btn"
        @click="loadData"
      >
        刷新数据
      </el-button>
    </div>

    <!-- 信息卡片区域 -->
    <el-row v-loading="loading" :gutter="responsiveGutter" class="info-cards">
      <!-- 平台基本信息 -->
      <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
        <el-card class="info-card platform-info" shadow="hover">
          <div slot="header" class="card-header">
            <div class="header-icon">
              <i class="el-icon-office-building"></i>
            </div>
            <span>平台信息</span>
          </div>
          <div class="card-content">
            <div class="info-item">
              <span class="label">
                <i class="el-icon-user"></i>
                平台名称
              </span>
              <span class="value">{{ platformMerchantName || '暂无' }}</span>
            </div>
            <div class="info-item">
              <span class="label">
                <i class="el-icon-document"></i>
                平台编号
              </span>
              <span class="value">{{ platformMerchantNo || '暂无' }}</span>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 账户金额信息 -->
      <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
        <el-card class="info-card account-balance" shadow="hover">
          <div slot="header" class="card-header">
            <div class="header-icon">
              <i class="el-icon-money"></i>
            </div>
            <span>账户金额</span>
          </div>
          <div class="card-content">
            <div class="info-item">
              <span class="label">
                <i class="el-icon-wallet"></i>
                汇总账户金额
              </span>
              <span class="value amount animated-counter">
                ¥{{ formatAmount(animatedSummaryAmount) }}
              </span>
            </div>
            <div class="info-item">
              <span class="label">
                <i class="el-icon-coin"></i>
                子账户总额
              </span>
              <span class="value amount animated-counter">
                ¥{{ formatAmount(animatedSubAccountAmount) }}
              </span>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 提现信息 -->
      <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
        <el-card class="info-card withdraw-info" shadow="hover">
          <div slot="header" class="card-header">
            <div class="header-icon">
              <i class="el-icon-bank-card"></i>
            </div>
            <span>提现信息</span>
          </div>
          <div class="card-content">
            <div class="info-item">
              <span class="label">
                <i class="el-icon-bank-card"></i>
                提现银行
              </span>
              <span class="value">{{ withdrawBankName || '暂无' }}</span>
            </div>
            <div class="info-item">
              <span class="label">
                <i class="el-icon-document"></i>
                提现账号
              </span>
              <span class="value">
                {{ formatCardNo(withdrawCardNo) || '暂无' }}
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 子账户列表 -->
    <el-card class="table-card" shadow="hover">
      <div slot="header" class="table-header">
        <div class="header-left">
          <i class="el-icon-document"></i>
          <span>子账户列表</span>
          <el-tag type="info" class="count-tag">
            {{ list.length }} 个账户
          </el-tag>
        </div>
        <div class="header-right">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索子账户"
            prefix-icon="el-icon-search"
            clearable
            class="search-input"
            @input="handleSearch"
          />
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="filteredList"
        align="center"
        style="width: 100%"
        empty-text="暂无数据"
        stripe
        border
        highlight-current-row
        class="data-table enhanced-table"
        :header-cell-style="{
          background: '#f5f7fa',
          color: '#606266',
          fontWeight: '600',
        }"
        :cell-style="{ padding: '12px 0' }"
        @row-click="handleRowClick"
      >
        <el-table-column prop="accountNo" label="子账户ID" width="400">
          <template #default="{ row }">
            <span class="account-id-simple">{{ row.accountNo }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="accountName" label="子账户名" min-width="200">
          <template #default="{ row }">
            <div class="account-name enhanced">
              <i class="el-icon-user account-icon"></i>
              <span class="account-name-text">{{ row.accountName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="availableBal" label="子账户余额" width="200">
          <template #default="{ row }">
            <div class="balance-cell">
              <i class="el-icon-money balance-icon"></i>
              <span class="balance-amount enhanced">
                ¥{{ formatAmount(row.availableBal) }}
              </span>
            </div>
          </template>
        </el-table-column>
        <!--
        <el-table-column prop="sourceType" label="操作">
          <template v-slot="{ row, $index }">
            <ac-permission-button
              v-if="row.canWithdraw"
              btn-text="提现"
              permission-key="platformAccountInfo-reflect"
              @click="onRowClick(row, $index)"
            />
            <ac-permission-button
              btn-text="明细"
              permission-key="platformAccountInfo-reflect"
              @click="jump(row)"
            />
            <ac-permission-button
              v-if="row.canTransfer"
              btn-text="转账"
              permission-key="platformAccountInfo-reflect"
              @click="onRowTransfer(row, $index)"
            />
          </template>
        </el-table-column>
        -->
      </el-table>
    </el-card>

    <account-withdrawal-dialog
      v-model="showDialog"
      :record="currentRecord"
      @success="onWithdrawalSuccess"
    />
    <account-transfer-dialog
      v-model="showTransferDialog"
      :record="currentRecord"
      :member-sub-account-list="accountList"
      :transfer-type-list="transferTypeList"
      @success="onTransferSuccess"
    />
  </div>
</template>
<script>
  import AccountWithdrawalDialog from './components/AccountWithdrawalDialog.vue';
  import AccountTransferDialog from './components/AccountTransferDialog.vue';
  import {
    fundSelector,
    getMemberSubAccountOptions,
    getOpenSubAccountOptions,
    getPlatformSubAccountList,
  } from '@/api/lianlian';

  export default {
    name: 'LianlianPlatformAccountInfo',
    components: {
      AccountWithdrawalDialog,
      AccountTransferDialog,
    },
    data() {
      return {
        loading: false,
        list: [],
        platformSubAccountTotalBal: 0,
        platformMerchantNo: '',
        platformMerchantName: '',
        summaryAccountBal: 0,
        withdrawBankName: '',
        withdrawCardNo: '',
        showDialog: false,
        showTransferDialog: false,
        currentRecord: {},
        memberSubAccountList: [],
        transferTypeList: [], //转账类型
        typeOptions: [], // 二级账户类型下拉数据
        accountList: [], //员子账户列表
        idTypeList: [],
        statusList: [],
        searchKeyword: '', // 搜索关键词
        animatedSummaryAmount: 0, // 动画金额
        animatedSubAccountAmount: 0, // 动画子账户金额
      };
    },
    computed: {
      responsiveGutter() {
        // 根据屏幕宽度计算响应式间距
        const width = window.innerWidth;
        if (width < 768) {
          return 8; // 手机端
        } else if (width < 992) {
          return 12; // 平板端
        } else if (width < 1200) {
          return 16; // 小桌面
        } else {
          return 20; // 大桌面
        }
      },
      filteredList() {
        if (!this.searchKeyword) {
          return this.list;
        }
        return this.list.filter(
          item =>
            item.accountNo?.includes(this.searchKeyword) ||
            item.accountName?.includes(this.searchKeyword),
        );
      },
    },
    created() {
      this.loadData();
      this.loadOptions();
      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize);
    },
    beforeDestroy() {
      // 移除事件监听器
      window.removeEventListener('resize', this.handleResize);
    },
    methods: {
      handleResize() {
        // 触发计算属性重新计算
        this.$forceUpdate();
      },
      async loadOptions() {
        Promise.all([
          getMemberSubAccountOptions(),
          getOpenSubAccountOptions(),
          fundSelector(),
        ])
          .then(res => {
            if (res && res.length) {
              const {
                statusList,
                memberSubAccountTypeList,
                memberSubAccountList,
              } = res[0] && res[0].res;
              const { idTypeList } = res[1] && res[1].res;
              const { accountList, transferTypeList } = res[2] && res[2].res;
              this.memberSubAccountList = memberSubAccountList;
              this.typeOptions = memberSubAccountTypeList;
              this.accountList = accountList;
              this.transferTypeList = transferTypeList;
              this.statusList = statusList;
              this.idTypeList = idTypeList;
              console.log(idTypeList, 'idTypeList');
            }
          })
          .catch(err => {});
      },
      async loadData() {
        this.loading = true;
        this.todayQueries++;

        const res = await getPlatformSubAccountList();
        if (res) {
          const {
            platformMerchantNo,
            platformMerchantName,
            platformSubAccountTotalBal,
            summaryAccountBal,
            withdrawBankName,
            withdrawCardNo,
            list,
          } = res;
          this.list = list;
          this.platformSubAccountTotalBal = platformSubAccountTotalBal;
          this.summaryAccountBal = summaryAccountBal;
          this.withdrawBankName = withdrawBankName;
          this.withdrawCardNo = withdrawCardNo;
          this.platformMerchantNo = platformMerchantNo;
          this.platformMerchantName = platformMerchantName;

          // 触发金额动画
          this.animateAmount(summaryAccountBal, 0, 'animatedSummaryAmount');
          this.animateAmount(
            platformSubAccountTotalBal,
            0,
            'animatedSubAccountAmount',
          );

          console.log(withdrawCardNo, 'withdrawCardNo');
        }

        this.loading = false;
      },
      onRowClick(row, index) {
        this.currentRecord = {
          ...row,
          accountTypeDesc: '平台子账户',
          bankName: this.withdrawBankName,
          cardNo: this.withdrawCardNo,
        };
        this.showDialog = true;
      },
      onRowTransfer(row, index) {
        this.currentRecord = {
          ...row,
          accountTypeDesc: '营销子账户',
        };
        this.showTransferDialog = true;
      },
      onWithdrawalSuccess() {
        this.loadData();
      },
      onTransferSuccess() {
        this.loadData();
      },
      jump(row) {
        this.$router.push({
          path: 'fundSplitDetail',
          query: { accountNo: row.accountNo },
        });
      },
      // 格式化金额
      formatAmount(amount) {
        if (!amount && amount !== 0) return '0.00';
        return Number(amount).toLocaleString('zh-CN', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        });
      },
      // 格式化银行卡号
      formatCardNo(cardNo) {
        if (!cardNo) return '';
        if (cardNo.length <= 8) return cardNo;
        return (
          cardNo.substring(0, 4) +
          ' **** **** ' +
          cardNo.substring(cardNo.length - 4)
        );
      },
      // 搜索处理
      handleSearch() {
        // 搜索逻辑已在计算属性中处理
      },
      // 导出数据
      exportData() {
        this.$message.info('导出功能开发中...');
        this.todayExports++;
      },
      // 金额动画
      animateAmount(target, current, property) {
        const duration = 1000;
        const steps = 60;
        const increment = (target - current) / steps;
        const stepTime = duration / steps;

        let currentValue = current;
        const timer = setInterval(() => {
          currentValue += increment;
          if (currentValue >= target) {
            currentValue = target;
            clearInterval(timer);
          }
          this[property] = currentValue;
        }, stepTime);
      },

      // 处理行点击
      handleRowClick(row, column, event) {
        // 可以添加行点击逻辑
      },
    },
  };
</script>

<style lang="scss" scoped>
  .platform-account-container {
    padding: 12px 20px 20px;
    background: #f5f7fa;
    min-height: 100vh;

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding: 12px 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      .page-title {
        margin: 0;
        color: #303133;
        font-size: 20px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;

        i {
          color: #409eff;
          font-size: 24px;
        }
      }

      .refresh-btn {
        border-radius: 6px;
        font-weight: 500;
      }
    }

    .info-cards {
      margin-bottom: 12px;

      .info-card {
        height: 200px;
        border-radius: 8px;
        transition: all 0.3s ease;
        background: white;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .card-header {
          display: flex;
          align-items: center;
          gap: 12px;
          font-weight: 600;
          color: #303133;
          border-bottom: 1px solid rgba(235, 238, 245, 0.5);
          padding: 4px 0;
          position: relative;

          .header-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #409eff;
            color: white;
            font-size: 16px;
          }

          .action-count {
            margin-left: auto;
            background: #409eff;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
          }
        }

        .platform-info .card-header i {
          color: #409eff;
        }

        .account-balance .card-header i {
          color: #67c23a;
        }

        .withdraw-info .card-header i {
          color: #e6a23c;
        }

        .quick-actions .card-header i {
          color: #f56c6c;
        }

        .card-content {
          padding: 12px 0;

          .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px 0;
            border-radius: 8px;
            transition: all 0.3s ease;

            &:hover {
              background: rgba(64, 158, 255, 0.05);
              transform: translateX(4px);
            }

            &:last-child {
              margin-bottom: 0;
            }

            .label {
              color: #606266;
              font-size: 14px;
              font-weight: 500;
              display: flex;
              align-items: center;
              gap: 8px;

              i {
                color: #409eff;
                font-size: 16px;
              }
            }

            .value {
              color: #303133;
              font-size: 14px;
              font-weight: 600;
              text-align: right;
              word-break: break-all;
              transition: all 0.3s ease;

              &.amount {
                color: #67c23a;
                font-size: 16px;
                font-weight: 700;
              }

              &.animated-counter {
                background: linear-gradient(135deg, #67c23a, #85ce61);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                animation: pulse 2s infinite;
              }
            }
          }

          .action-stats {
            margin-top: 16px;
            display: flex;
            gap: 16px;

            .stat-item {
              flex: 1;
              text-align: center;
              padding: 12px;
              background: rgba(64, 158, 255, 0.05);
              border-radius: 8px;
              transition: all 0.3s ease;

              &:hover {
                background: rgba(64, 158, 255, 0.1);
                transform: translateY(-2px);
              }

              .stat-number {
                display: block;
                font-size: 20px;
                font-weight: 700;
                color: #409eff;
                margin-bottom: 4px;
              }

              .stat-label {
                font-size: 12px;
                color: #606266;
              }
            }
          }
        }

        .action-btn {
          width: 100%;
          margin-bottom: 8px;
          border-radius: 6px;
          font-weight: 500;
          transition: all 0.3s ease;

          &:last-child {
            margin-bottom: 0;
          }

          &.enhanced-btn {
            background: #409eff;
            border: none;
            color: white;
            font-weight: 600;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
            }

            &:active {
              transform: translateY(0);
            }
          }
        }
      }
    }

    .table-card {
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      background: white;

      .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0;

        .header-left {
          display: flex;
          align-items: center;
          gap: 12px;
          font-size: 16px;
          font-weight: 600;
          color: #303133;

          i {
            color: #409eff;
            font-size: 20px;
          }

          .count-tag {
            margin-left: 8px;
          }
        }

        .header-right {
          .search-input {
            width: 250px;
          }
        }
      }

      .data-table {
        &.enhanced-table {
          ::v-deep .el-table__body-wrapper {
            .el-table__row {
              transition: all 0.2s ease;
              cursor: pointer;

              &:hover {
                background: rgba(64, 158, 255, 0.05);
              }
            }
          }
        }

        .account-id-simple {
          font-family: 'Courier New', monospace;
          font-weight: 600;
          color: #409eff;
          background: rgba(64, 158, 255, 0.1);
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 13px;
        }

        .account-name {
          &.enhanced {
            display: flex;
            align-items: center;
            gap: 8px;

            .account-icon {
              color: #409eff;
              font-size: 16px;
            }

            .account-name-text {
              font-weight: 600;
              color: #303133;
            }
          }
        }

        .balance-cell {
          display: flex;
          align-items: center;
          gap: 6px;

          .balance-icon {
            color: #67c23a;
            font-size: 14px;
          }

          .balance-amount {
            &.enhanced {
              color: #67c23a;
              font-weight: 600;
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .platform-account-container {
      padding: 12px;

      .page-header {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;

        .page-title {
          font-size: 20px;
        }
      }

      .info-cards {
        .info-card {
          margin-bottom: 16px;
        }
      }

      .table-card {
        .table-header {
          flex-direction: column;
          gap: 12px;
          align-items: flex-start;

          .header-right {
            width: 100%;

            .search-input {
              width: 100%;
            }
          }
        }
      }
    }
  }

  // 动画关键帧
  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  // 应用动画
  .platform-account-container {
    animation: fadeInUp 0.6s ease-out;
  }

  .info-card {
    animation: slideInLeft 0.6s ease-out;

    &:nth-child(1) {
      animation-delay: 0.1s;
    }
    &:nth-child(2) {
      animation-delay: 0.2s;
    }
    &:nth-child(3) {
      animation-delay: 0.3s;
    }
    &:nth-child(4) {
      animation-delay: 0.4s;
    }
  }
</style>
