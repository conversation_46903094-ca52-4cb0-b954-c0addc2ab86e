<!--
 * @Author: 七七
 * @Date: 2022-04-07 18:20:51
 * @LastEditors: xuxiang
 * @LastEditTime: 2022-05-24 17:56:03
 * @FilePath: /access-fmis-web/src/views/pingan/secondaryAdjustmentDifference.vue
-->
// 二级调整差异
<template>
  <div>
    <el-form :model="search" inline>
      <el-form-item label="贸易主体">
        <el-input
          v-model="search.tradeBodyName"
          placeholder="请输入贸易主体名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="服务主体">
        <el-input
          v-model="search.servicePrincipalName"
          placeholder="请输入服务主体名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="费用类型">
        <el-select v-model="search.feeType" clearable>
          <el-option
            v-for="item in feeTypeList"
            :key="item.dictValue"
            :value="item.dictValue"
            :label="item.dictDesc"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="search.date"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          clearable
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="search.transferStatus" clearable>
          <el-option
            v-for="item in transferStatusList"
            :key="item.dictValue"
            :value="item.dictValue"
            :label="item.dictDesc"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          v-if="
            scope.row.adjustmentType === 1 || scope.row.adjustmentType === 2
          "
          slot="reference"
          btn-text="明细"
          type="text"
          size="small"
          permission-key=""
          @click="handleDetail(scope.row)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="
            scope.row.transferStatus === 0 || scope.row.transferStatus === 3
          "
          slot="reference"
          btn-text="支付"
          type="text"
          size="small"
          permission-key=""
          @click="showPayDialog(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <el-dialog
      title="支付"
      :visible.sync="payDialog"
      width="40%"
      :close-on-click-modal="false"
      @close="handleCancel"
    >
      <div class="mr-b-10">您接收的验证号码为：{{ phoneNumber }}</div>
      <el-form
        ref="confirmForm"
        :model="confirmData"
        :rules="rules"
        label-width="140px"
        :destroy-on-close="true"
      >
        <el-form-item label="短信指令号" prop="serialNo">
          <el-input v-model="confirmData.serialNo"></el-input>
        </el-form-item>
        <el-form-item label="短信验证码" prop="verificationCode">
          <el-input v-model="confirmData.verificationCode"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button :loading="loadingButton" @click="handlePay">
            确认
          </el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    codeVerify,
    secondaryAdjustmentList,
    secondaryAdjustmentPay,
    secondaryOption,
  } from '@/api/pingan';
  import dayjs from 'dayjs';

  export default {
    name: 'SecondaryAdjustmentDifference',
    components: { dynamictable },
    data() {
      return {
        search: {
          date: [],
        },
        loadingButton: false,
        searchCopy: {},
        list: [],
        transferStatusList: [],
        feeTypeList: [],
        phoneNumber: null,
        confirmData: {
          serialNo: '',
          verificationCode: '',
        },
        payId: null,
        options: {
          loading: false,
          border: true,

          // showSummary: true,
        },
        payDialog: false,
        examineText: '',
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        rules: {
          verificationCode: [
            { required: true, message: '请输入短信验证码', trigger: 'blur' },
          ],
          serialNo: [
            { required: true, message: '请输入短信指令号', trigger: 'blur' },
          ],
        },
        columns: [
          {
            prop: 'id',
            label: 'ID',
          },
          {
            prop: 'serialNo',
            label: '调整单号',
          },
          {
            prop: 'billDate',
            label: '账期（月）',
          },
          {
            prop: 'tradeBodyName',
            label: '贸易主体',
          },
          {
            prop: 'servicePrincipalName',
            label: '服务主体',
          },
          {
            prop: 'feeTypeDesc',
            label: '费用类型',
          },
          {
            prop: 'originFeeAmount',
            label: '已付金额',
          },
          {
            prop: 'feeAmount',
            label: '应付金额',
          },
          {
            prop: 'amountDifference',
            label: '差异金额',
            render: ({ adjustmentType, amountDifference }) => (
              <span>{adjustmentType === 1 ? '-' : amountDifference}</span>
            ),
          },
          {
            prop: 'createTime',
            label: '创建时间',
          },
          {
            prop: 'adjustmentTypeDesc',
            label: '调整类型',
          },
          {
            prop: 'transferStatusDesc',
            label: '状态',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '200',
            scopedSlots: { customRender: 'operation' },
          },
        ],
      };
    },
    mounted() {
      const endTime = dayjs();
      const startTime = endTime.subtract(1, 'month');
      this.search.date = [
        startTime.format('YYYY-MM-DD 00:00:00'),
        endTime.format('YYYY-MM-DD 23:59:59'),
      ];
      // this.search.date[0] = startTime.format('YYYY-MM-DD 00:00:00');
      // this.search.date[1] = startTime.format('YYYY-MM-DD 23:59:59');
      this.getOptionList();
      this.getList();
    },
    methods: {
      async getOptionList() {
        try {
          const { res } = await secondaryOption();
          if (res) {
            this.feeTypeList = res.feeTypeList ? res.feeTypeList : [];
            this.transferStatusList = res.transferStatusList
              ? res.transferStatusList
              : [];
          }
        } catch (e) {}
      },
      getParams() {
        const body = {
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        if (this.searchCopy.date?.length > 0) {
          const startTime = this.searchCopy.date[0];
          const endTime = this.searchCopy.date[1];
          body.beginDate =
            startTime.length === 10 ? startTime + ' 00:00:00' : startTime;
          body.endDate =
            endTime.length === 10 ? endTime + ' 23:59:59' : endTime;
        }
        if (this.searchCopy.tradeBodyName?.length > 0) {
          body.tradeBodyName = this.searchCopy.tradeBodyName;
        }
        if (this.searchCopy.servicePrincipalName?.length > 0) {
          body.servicePrincipalName = this.searchCopy.servicePrincipalName;
        }
        if (this.searchCopy.feeType?.length > 0) {
          body.feeType = Number(this.searchCopy.feeType);
        }
        if (this.searchCopy.transferStatus?.length > 0) {
          body.transferStatus = Number(this.searchCopy.transferStatus);
        }
        return body;
      },
      async getList() {
        this.options.loading = true;
        this.searchCopy = { ...this.search };
        const body = this.getParams();
        try {
          const { res, err } = await secondaryAdjustmentList(body);
          if (res && !err) {
            this.list = res ? res.records : [];
            this.pagination.total = res ? res.total : 0;
          }
        } catch (e) {}
        this.options.loading = false;
      },
      handleSearch() {
        this.pagination.pageSize = 1;
        this.getList();
      },
      handleDetail(row) {
        this.$router.push(
          `secondaryAdjustmentDifferenceDetails?id=${row.serialNo}&originSalesNo=${row.originSalesNo}&pushSn=${row.originSalesNo}&adjustmentType=${row.adjustmentType}&adjustmentId=${row.id}`,
        );
      },
      async showPayDialog(row) {
        // this.confirmData = {
        //   serialNo: '',
        //   verificationCode: '',
        // };
        await this.handleShowCode(row.id);
        this.payDialog = true;
      },
      async handleShowCode(id) {
        this.payId = id;
        try {
          const { res, err } = await codeVerify({ id: id });
          if (res && !err) {
            this.phoneNumber = res.msgPhone;
            this.confirmData.serialNo = res.serialNo;
          }
        } catch (e) {}
      },
      handlePay() {
        this.$refs['confirmForm'].validate(async valid => {
          if (!valid) {
            return false;
          }
          const body = {
            serialNo: this.confirmData.serialNo,
            id: this.payId,
            verificationCode: this.confirmData.verificationCode,
          };
          this.loadingButton = true;
          try {
            const { res, err } = await secondaryAdjustmentPay(body);
            if (!err) {
              this.$message.success('操作成功');
              this.payDialog = false;
              this.confirmData = {
                serialNo: '',
                verificationCode: '',
              };
              this.getList();
            }
          } catch (e) {}
          this.loadingButton = false;
        });
      },
      handleCancel() {
        this.payDialog = false;
        this.confirmData = {
          serialNo: '',
          verificationCode: '',
        };
        this.$refs['confirmForm'].clearValidate();
      },
      reset() {
        this.search = {
          date: [],
        };
        const endTime = dayjs();
        const startTime = endTime.subtract(1, 'month');
        this.search.date = [
          startTime.format('YYYY-MM-DD HH:mm:ss'),
          endTime.format('YYYY-MM-DD HH:mm:ss'),
        ];
      },
    },
  };
</script>
<style scoped></style>
