<template>
  <div>
    <el-form inline>
      <el-form-item label="交易类型:">
        <el-select
          v-model="searchParams.orderType"
          clearable
          placeholder="请选择交易类型"
        >
          <el-option
            v-for="item in orderType"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button type="primary" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>

    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    ></dynamictable>
    <secondaryDetails
      v-model="showSecondary"
      :secondary-item="secondaryItem"
      type="2"
    ></secondaryDetails>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import secondaryDetails from './components/secondaryDetails';

  import {
    firstSplitStatsDaySumDetail,
    secondaryItemQuery,
  } from '@/api/pingan';

  export default {
    components: {
      dynamictable,
      secondaryDetails,
    },

    data() {
      return {
        showSecondary: false,
        secondaryItem: [],
        searchParams: {
          orderType: '',
        },
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          index: true,
          indexName: 'ID',
        },
        orderType: [
          {
            key: 0,
            value: '正向',
          },
          {
            key: 1,
            value: '逆向',
          },
        ],
        tableData: [],
      };
    },
    created() {
      const query = this.$route.query;
      this.searchParams = {
        ...query,
        orderType: this.searchParams.orderType,
      };
      this.getList(true);
    },
    methods: {
      async showPopover(row) {
        let query = {
          id: row.id,
        };

        const { err, res } = await secondaryItemQuery(query);

        if (res && !err) {
          this.secondaryItem = this.getSecondaryItem(res);
        }
      },
      getSecondaryItem(arr = []) {
        let list = [];

        arr.forEach(item => {
          if (Array.isArray(item.secondarySubItemVOList)) {
            item.secondarySubItemVOList.forEach((item1, index) => {
              list.push({
                firstFeeAmount: item.feeAmount,
                firstFeeTypeName: item.feeAmount,
                isMerge: index == 0,
                rowspan: item.secondarySubItemVOList.length,
                ...item1,
              });
            });
          }
        });
        return list;
      },
      getParams() {
        const params = {
          ...this.searchParams,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const { res, err } = await firstSplitStatsDaySumDetail(params);
        if (res && !err) {
          this.list = res ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        }
        this.options.loading = false;
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },

      getColumns() {
        const columns = [
          {
            prop: 'salesNo',
            label: '单据编号',
          },
          {
            prop: 'settleSubjectName',
            label: '资金主体',
          },
          {
            prop: 'salesEntityName',
            label: '贸易主体',
          },
          {
            prop: 'orderTypeDesc',
            label: '交易类型',
          },
          {
            prop: 'shippingWarehouseName',
            label: '发货仓',
          },
          {
            prop: 'payChannel',
            label: '支付渠道',
          },
          {
            prop: 'currency',
            label: '币种',
          },
          {
            prop: 'amount',
            label: '实付金额',
            render: row => (
              <a
                onClick={() => {
                  this.secondaryItem = [];
                  this.showSecondary = true;
                  this.showPopover(row);
                }}
                style="cursor:pointer"
              >
                {row.amount}
              </a>
            ),
          },
          {
            prop: 'transferTime',
            label: '实际支付时间',
          },
        ];
        return columns;
      },
    },
  };
</script>
<style lang="scss"></style>
