<template lang="pug">
.vertical.top.vcenter.padding-bottom-10
  el-form(inline label-suffix=':')
    el-form-item(label='查询时间')
      el-date-picker(
        v-model='time'
        type='datetimerange'
        range-separator='至'
        start-placeholder='开始日期'
        end-placeholder='结束日期'
        value-format='yyyy-MM-dd HH:mm:ss'
      )
    el-form-item(label='子账户')
      el-select(
        v-model='accountNo'
        placeholder='请选择'
        style='width: 200px'
        :loading='optionLoading'
        clearable
        filterable
      )
        el-option(
          v-for='item in accountList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-button.margin-left-10(
      type='primary'
      icon='el-icon-search'
      @click='onSearchClick'
    ) 查询
    el-button.margin-left-10(type='primary' @click='onExport') 导出
  el-table.margin-top-20(
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
  )
    el-table-column(prop='withdrawNo' label='单号' width='190')
    el-table-column(prop='createTime' label='交易时间' width='170')
    el-table-column(prop='outAccountName' label='子账户名')
    el-table-column(prop='outAccountNo' label='账户ID' width='160')
    el-table-column(prop='withdrawBankName' label='提现银行' width='80')
    el-table-column(prop='withdrawCardNo' label='提现账号' width='130')
    el-table-column(prop='withdrawAmount' label='提现金额')
    el-table-column(prop='withdrawServiceCharge' label='手续费')
    el-table-column(prop='withdrawStatusDesc' label='提现状态' width='80')
    el-table-column(prop='channelCode' label='渠道返回码')
    el-table-column(prop='channelMsg' label='描述')
  el-pagination(
    :current-page='m_current'
    :page-sizes='[10, 15, 20, 30]'
    :page-size='m_pageSize'
    layout='total, sizes, prev, pager, next, jumper'
    :total='m_total'
    background
    @size-change='m_handleSizeChange'
    @current-change='m_handleCurrentChange'
  )
</template>
<script>
  import dayjs from 'dayjs';
  import { paginationMixin } from '@/mixins/tableMixin';
  import { debounce, downloadFile } from '@/utils';
  import { newExportExcel } from '@/api/blob';
  import { getWithdrawRecord, getWithdrawRecordOptions } from '@/api/lianlian';

  const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';

  export default {
    mixins: [paginationMixin],
    data() {
      return {
        loading: false,
        optionLoading: false,
        showDialog: true,

        accountNo: null,

        time: [],

        currentRecord: null,
        accountList: [],

        list: [],
      };
    },
    async created() {
      this.time = [
        dayjs().startOf('day').format(DATE_TIME_FORMAT),
        dayjs().endOf('day').format(DATE_TIME_FORMAT),
      ];
      await this.loadOptions();
      await this.m_loadData();
    },
    methods: {
      onExport: debounce(function () {
        const body = {};
        if (this.accountNo) {
          body.accountId = this.accountNo;
        }
        if (this.time && this.time.length > 0) {
          const [startTime, endTime] = this.time;
          body.beginTime = startTime;
          body.endTime = endTime;
        }

        newExportExcel(
          { ...body },
          '/api/split-service/withdrawOrder/export',
          'post',
        ).then(res => {
          if (res) {
            downloadFile(res.data, '提现记录列表');
          }
        });
      }, 800),
      async loadOptions() {
        this.optionLoading = true;
        this.accountList = await getWithdrawRecordOptions({
          channel: 'lianlian',
        });
        this.optionLoading = false;
      },
      async m_loadData() {
        this.loading = true;
        const body = {
          size: this.m_pageSize,
          current: this.m_current,
          channel: 'lianlian',
        };
        if (this.accountNo) {
          body.accountNo = this.accountNo;
        }
        if (this.time && this.time.length > 0) {
          const [startTime, endTime] = this.time;
          body.startTime = startTime;
          body.endTime = endTime;
        }
        const res = await getWithdrawRecord(body);
        const { total, records } = res;
        this.m_total = total;
        this.list = records;
        this.loading = false;
      },
      onSearchClick() {
        this.m_current = 1;
        this.m_loadData();
      },
      onRowClick(row, index) {},
    },
  };
</script>
