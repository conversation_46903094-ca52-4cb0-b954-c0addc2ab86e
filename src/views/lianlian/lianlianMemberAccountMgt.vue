<template>
  <div>
    <el-form inline>
      <el-form-item label="账户主体:">
        <!-- <el-input
          v-model="searchParams.systemCode"
          placeholder="请输入账户主体"
        /> -->
        <el-select
          v-model="searchParams.custId"
          clearable
          filterable
          placeholder="请选择账户主体"
        >
          <el-option
            v-for="item in memberSubAccountList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <!-- <el-button type="primary" @click="handleBatchReview">
          批量审批
        </el-button> -->
      </el-form-item>
    </el-form>

    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
      @selection-change="selectionChange"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          v-if="scope.row.status === 'NEED_AUDIT'"
          slot="reference"
          type="text"
          size="small"
          btn-text="通过"
          permission-key=""
          @click="handleOperate(scope.row, 1)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="scope.row.status === 'NEED_AUDIT'"
          slot="reference"
          type="text"
          size="small"
          btn-text="拒绝"
          permission-key=""
          @click="handleOperate(scope.row, 2)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="scope.row.status === 'OPENING'"
          slot="reference"
          type="text"
          size="small"
          btn-text="开户检查"
          permission-key=""
          @click="accountOpeningCheck(scope.row)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="scope.row.status === 'NORMAL'"
          slot="reference"
          type="text"
          size="small"
          btn-text="提现"
          permission-key=""
          @click="withdraw(scope.row, scope.$index)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="scope.row.status === 'NORMAL'"
          slot="reference"
          type="text"
          size="small"
          btn-text="转账"
          permission-key=""
          @click="onTransferClick(scope.row, scope.$index)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="['NORMAL', 'NEED_BINDING'].includes(scope.row.status)"
          slot="reference"
          type="text"
          size="small"
          btn-text="刷新余额"
          permission-key=""
          @click="onRefreshRowClick(scope.row, scope.$index)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="['NORMAL', 'NEED_BINDING'].includes(scope.row.status)"
          slot="reference"
          type="text"
          size="small"
          btn-text="银行卡"
          permission-key=""
          @click="go(scope.row, 1)"
        ></ac-permission-button>
      </template>
    </dynamictable>

    <MemberAccountWithdrawalDialog
      v-model="showDialog"
      :record="currentRecord"
      :cards-list="cardsList"
      @success="refreshPage"
    ></MemberAccountWithdrawalDialog>
    <AccountCreateDialog
      v-model="showCreateDialog"
      :id-type-list="idTypeList"
      @success="refreshPage"
    ></AccountCreateDialog>
    <MemberTransferNew
      v-model="showTransferDialog"
      :record="currentRecord"
      :member-sub-account-list="accountList"
      :transfer-type-list="transferTypeList"
      @success="refreshPage"
    ></MemberTransferNew>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import { debounce } from '@/utils';
  import MemberAccountWithdrawalDialog from './components/MemberAccountWithdrawalDialog';
  import AccountCreateDialog from './components/AccountCreateDialog';
  import MemberTransferNew from './components/MemberTransferNew';
  import {
    bindingCardsList,
    fundSelector,
    getMemberSubAccountList,
    getMemberSubAccountOptions,
    getOpenSubAccountOptions,
    refreshSubAccountBal,
    subAccountAudit,
    subAccountOpeningCheck,
  } from '@/api/lianlian';

  export default {
    components: {
      dynamictable,
      MemberAccountWithdrawalDialog,
      AccountCreateDialog,
      MemberTransferNew,
    },

    data() {
      let columns = [
        {
          prop: 'id',
          label: 'ID',
        },
        {
          prop: 'accountName',
          label: '账户主体',
        },
        {
          prop: 'accountNo',
          label: '账户ID',
        },
        // {
        //   prop: 'accountBalance',
        //   label: '资金余额',
        // },
        {
          prop: 'accountActualBalance',
          label: '可用余额',
        },
        {
          prop: 'accountCanCashBal',
          label: '可提现余额',
        },
        {
          prop: 'preSettleActualBalance',
          label: '待结算余额',
        },
        {
          prop: 'statusDesc',
          label: '开户状态',
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          minWidth: '150',
          maxWidth: '300',
          scopedSlots: { customRender: 'operation' },
        },
      ];

      return {
        currentId: '',
        showTransferDialog: false, // 转账弹窗
        showCreateDialog: false, // 开户弹窗
        showDialog: false, // 提现弹窗
        currentRecord: null,
        typeOptions: [], // 二级账户类型下拉数据
        accountList: [], //员子账户列表
        idTypeList: [],
        memberSubAccountList: [],
        transferTypeList: [], //转账类型
        cardsList: [], // 收款账户列表
        selectionIds: [],
        statusList: [],
        supplier: '',
        searchParams: {
          accountType: '',
          status: 'NORMAL',
          channel: 'pingan',
          custId: '',
        },
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          mutiSelect: false,
        },
        columns,
      };
    },
    async created() {
      await this.loadOptions();
      this.getList(true);
    },
    methods: {
      handleOperate(row, type) {
        if (type === 2) {
          this.$prompt('拒绝理由', '二次确认', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputType: 'textarea',
            inputValidator: value => {
              if (!value) {
                return '审批意见不能为空！';
              }
            },
          })
            .then(({ value }) => {
              this.saveOperateApi({
                id: row.id,
                type: 2,
                reason: value,
              });
            })
            .catch(() => {});
          return;
        }

        this.$confirm('确认同意该操作?', '二次确认', {
          confirmButtonText: '通过',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.saveOperateApi({
              id: row.id,
              type: 1,
            });
          })
          .catch(() => {});
      },
      async saveOperateApi(params, cb) {
        const { res, err } = await subAccountAudit(params);
        if (!err) {
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          this.getList();
          cb && cb();
        }
      },
      // 开户检查
      async accountOpeningCheck(row) {
        const { res, err } = await subAccountOpeningCheck(row.id);
        if (!err) {
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          this.getList();
        }
      },

      // 提现弹窗
      async withdraw(row, index) {
        await this.refreshBalance(row.accountNo, index);
        await this.bindingCardsList(row.custId);
        this.showDialog = true;
        // this.currentRecord = row;
      },
      async onTransferClick(row, index) {
        await this.refreshBalance(row.accountNo, index);
        this.currentRecord = this.list[index];
        console.log(this.currentRecord, 'this.currentRecord');
        this.showTransferDialog = true;
      },
      async refreshBalance(id, index) {
        this.options.loading = true;
        const res = await refreshSubAccountBal(id);
        const { accountActualBalance, accountBalance, accountCanCashBal } = res;
        const list = [...this.list];
        const row = list[index];
        const item = {
          ...row,
          accountActualBalance,
          accountBalance,
          canCashBal: accountCanCashBal,
          canTransferBal: accountActualBalance,
        };
        list[index] = item;
        this.currentRecord = item;
        this.list = list;
        this.options.loading = false;
      },
      onRefreshRowClick(row, index) {
        this.refreshBalance(row.accountNo, index);
      },
      async bindingCardsList(custId) {
        console.log('Loading cardsList for custId:', custId);
        this.cardsList = await bindingCardsList(custId);
        console.log('cardsList loaded:', this.cardsList);
      },

      async loadOptions() {
        try {
          const [res1, res2, res3] = await Promise.all([
            getMemberSubAccountOptions({ channel: 'lianlian' }),
            getOpenSubAccountOptions({ channel: 'lianlian' }),
            fundSelector({ channel: 'lianlian' }),
          ]);

          // 统一解构，避免重复判断
          const {
            statusList = [],
            memberSubAccountTypeList = [],
            memberSubAccountList = [],
          } = res1 || {};
          const { idTypeList = [] } = res2 || {};
          const { accountList = [], transferTypeList = [] } = res3 || {};

          // 赋值
          this.memberSubAccountList = memberSubAccountList;
          this.typeOptions = memberSubAccountTypeList;
          this.statusList = statusList;
          this.idTypeList = idTypeList;
          this.accountList = accountList;
          this.transferTypeList = transferTypeList;

          // 可选：调试输出
          // console.log({
          //   memberSubAccountList,
          //   typeOptions: memberSubAccountTypeList,
          //   accountList,
          //   transferTypeList,
          //   statusList,
          //   idTypeList,
          // });
        } catch (error) {
          console.error('loadOptions 加载失败:', error);
          this.$message && this.$message.error('下拉选项加载失败，请稍后重试');
        }
      },
      handleBatchReview: debounce(function () {
        const { selectionIds } = this;
        if (selectionIds.length === 0) {
          this.$message.error('请勾选未审批记录');
          return;
        }
      }, 1000),

      selectionChange(ids = []) {
        this.selectionIds = ids.map(item => item.id);
      },
      refreshPage() {
        this.getList();
      },

      go(row, type) {
        if (type === 1) {
          this.$router.push({
            path: 'memberAccountBankCard',
            query: { custId: row.custId },
          });
          return;
        }
        this.$router.push({
          path: 'fundSplitDetail',
          query: { accountNo: row.accountNo },
        });
      },
      getParams() {
        const params = {
          ...this.searchParams,
          channel: 'lianlian',
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;

        const res = await getMemberSubAccountList(params);
        console.log(res, 'res');
        this.list = res ? res.list : [];
        this.pagination.total = res ? res.total : 0;
        this.options.loading = false;
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },
    },
  };
</script>
<style lang="scss"></style>
