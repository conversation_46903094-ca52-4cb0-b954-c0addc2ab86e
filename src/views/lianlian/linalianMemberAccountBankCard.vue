<template>
  <div>
    <el-row>
      <el-col :span="23">
        <el-tabs v-model="activeName" type="card" @tab-click="handleTabClick">
          <el-tab-pane label="银行卡" name="1">
            <el-form v-show="false" w inline>
              <el-form-item label="状态:">
                <el-select
                  v-model="searchParams.applyStatus"
                  clearable
                  placeholder="请选择状态"
                >
                  <el-option label="待复核" value="APPLY"></el-option>
                  <el-option label="复核通过" value="PASS"></el-option>
                  <el-option label="复核拒绝" value="REJECT"></el-option>
                  <el-option label="待绑卡" value="NEED_BIND"></el-option>
                  <el-option label="待验证" value="NEED_VALID"></el-option>
                  <el-option label="绑定成功" value="BIND_SUCCESS"></el-option>
                  <el-option
                    label="解绑待复核"
                    value="UN_BIND_APPLY"
                  ></el-option>
                  <el-option
                    label="解绑拒绝"
                    value="UN_BIND_REJECT"
                  ></el-option>
                  <el-option
                    label="解绑成功"
                    value="UN_BIND_SUCCESS"
                  ></el-option>
                  <el-option label="解绑失败" value="UN_BIND_FAIL"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="getList(true)">
                  查询
                </el-button>
                <el-button type="primary" @click="onReset">重置</el-button>
              </el-form-item>
            </el-form>

            <el-button
              v-show="false"
              type="primary"
              style="margin-bottom: 10px"
              @click="handleAdd"
            >
              新增
            </el-button>
          </el-tab-pane>
          <!-- <el-tab-pane label="操作日志" name="2"></el-tab-pane> -->
        </el-tabs>
      </el-col>
      <el-col :span="1">
        <el-button type="text" @click="$router.go(-1)">返回</el-button>
      </el-col>
    </el-row>

    <dynamictable
      :data-source="list"
      :columns="activeName === '1' ? columns : columns1"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          v-if="['UN_BIND_APPLY', 'APPLY'].includes(scope.row.applyStatus)"
          slot="reference"
          type="text"
          size="small"
          btn-text="通过"
          permission-key=""
          @click="handleOperate(scope.row, 1)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="['UN_BIND_APPLY', 'APPLY'].includes(scope.row.applyStatus)"
          slot="reference"
          type="text"
          size="small"
          btn-text="拒绝"
          permission-key=""
          @click="handleOperate(scope.row, 2)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="scope.row.applyStatus === 'NEED_BIND'"
          slot="reference"
          type="text"
          size="small"
          btn-text="绑卡"
          permission-key=""
          @click="handleOperate(scope.row, 3)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="scope.row.applyStatus === 'NEED_VALID'"
          slot="reference"
          type="text"
          size="small"
          btn-text="验证卡"
          permission-key=""
          @click="verificationCard(scope.row)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="['BIND_SUCCESS'].includes(scope.row.applyStatus)"
          slot="reference"
          type="text"
          size="small"
          btn-text="解绑"
          permission-key=""
          @click="handleUnbind(scope.row, scope.row.applyStatus)"
        ></ac-permission-button>
        <!-- <ac-permission-button
          v-if="scope.row.applyStatus === 'UN_BIND_FAIL'"
          slot="reference"
          type="text"
          size="small"
          btn-text="重试"
          permission-key=""
          @click="handleRetry(scope.row)"
        ></ac-permission-button> -->
      </template>
    </dynamictable>

    <AddBankCardDialog
      v-model="showDialog"
      :bank-list="bankList"
      @onGet="getList(true)"
    ></AddBankCardDialog>
    <confirmDialog
      v-model="showConfirmDialog"
      :show-title="false"
      title="确认需要解绑这张卡?"
      @onClose="onConfirmDialogClose"
    ></confirmDialog>
    <el-dialog
      title="验证卡"
      :visible.sync="dialogVisible"
      width="520px"
      @closed="onClose"
    >
      <el-form ref="form" :model="form" :rules="rulesForm" label-width="160px">
        <!-- <el-form-item label="您接收验证码的号码为: ">
          <div>{{ form.msgPhone }}</div>
        </el-form-item> -->
        <el-form-item label="验证码: " prop="serialNo">
          <el-input
            v-model="form.serialNo"
            style="width: 300px"
            placeholder="请输入验证码"
          ></el-input>
        </el-form-item>
        <el-form-item label="验证金额: " prop="amount">
          <el-input-number
            v-model="form.amount"
            :precision="2"
            :controls="false"
            style="width: 300px"
            placeholder="请输入验证金额"
          ></el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="tiedCard">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import { debounce } from '@/utils';
  import { isPhone } from '@/utils/validate';

  import AddBankCardDialog from './components/AddBankCardDialog';

  import {
    bindingCardAudit,
    bindingCardBinding,
    bindingCardValidAmount,
    getBindingCardsList,
    getSubAccountBankList,
    unBindingApply,
    unBindingAudit,
    unBindingRetry,
  } from '@/api/lianlian';
  import confirmDialog from '../xiamen/components/confirmDialog';

  export default {
    components: {
      dynamictable,
      AddBankCardDialog,
      confirmDialog,
    },

    data() {
      const validatePhone = (rule, value, callback) => {
        if (!isPhone(value) && value) {
          callback(new Error('手机号格式错误'));
        } else {
          callback();
        }
      };

      let columns = [
        {
          prop: 'id',
          label: '编号',
        },
        {
          prop: 'accountName',
          label: '开户名称',
        },
        {
          prop: 'bankName',
          label: '开户银行',
        },
        {
          prop: 'cardNo',
          label: '开户卡号',
        },
        {
          prop: 'branchName',
          label: '开户支行',
        },
        {
          prop: 'remark',
          label: '备注',
        },
        {
          prop: 'applyStatusDesc',
          label: '状态',
        },
        // {
        //   prop: 'operation',
        //   label: '操作',
        //   fixed: 'right',
        //   minWidth: '120',
        //   maxWidth: '200',
        //   scopedSlots: { customRender: 'operation' },
        // },
      ];
      let columns1 = [
        {
          prop: 'id',
          label: '操作ID',
        },
        {
          prop: 'systemNo',
          label: '操作人姓名',
        },
        {
          prop: 'systemName',
          label: '操作动作',
        },
        {
          prop: 'systemCode',
          label: '操作时间',
        },
      ];

      return {
        turnOn: false,
        dialogVisible: false,
        currentRow: null,
        showConfirmDialog: false,
        showDialog: false,
        activeName: '1',
        list: [],
        bankList: [],
        searchParams: {
          applyStatus: '',
        },
        form: {
          amount: '',
          id: '',
          serialNo: '',
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
        rulesForm: {
          serialNo: [
            {
              required: true,
              message: '请输入验证码',
              trigger: 'blur',
            },
          ],
          phone: [
            {
              required: true,
              message: '请输入手机号',
              trigger: 'blur',
            },
            {
              validator: validatePhone,
              trigger: 'blur',
            },
          ],
          amount: [
            { required: true, message: '请输入验证金额', trigger: 'blur' },
          ],
        },
        columns1,
        columns,
      };
    },
    created() {
      this.getSubAccountBankList();
      this.getList(true);
    },
    methods: {
      handleAdd() {
        this.showDialog = true;
      },
      async onConfirmDialogClose(type, value) {
        const { currentRow } = this;
        if (type === 'pass') {
          this.unBindingAudit(
            {
              id: currentRow.id,
              auditType: 1,
              remark: value.remark,
            },
            () => {
              this.showConfirmDialog = false;
            },
          );
        }
        if (type === 'refuse') {
          if (!value.remark) {
            this.$message({
              type: 'error',
              message: '拒绝原因不能为空!',
            });
            return;
          }
          this.unBindingAudit(
            {
              id: currentRow.id,
              auditType: 2,
              remark: value.remark,
            },
            () => {
              this.showConfirmDialog = false;
            },
          );
        }
      },
      handleUnbind: debounce(async function (row, type) {
        //BIND_SUCCESS 绑卡成功  UN_BIND_APPLY待解绑
        if (type === 'BIND_SUCCESS') {
          const { res, err } = await unBindingApply({ id: row.id });
          if (!err) {
            this.$message({
              type: 'success',
              message: '操作成功!',
            });
            this.getList();
          }
          return;
        }
        // this.showConfirmDialog = true;
        // this.currentRow = row;
      }, 1000),
      async unBindingAudit(params, cb) {
        const { res, err } = await unBindingAudit(params);
        if (!err) {
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          cb && cb();
          this.getList();
        }
      },
      // 解绑重试
      handleRetry: debounce(async function (row) {
        const { res, err } = await unBindingRetry({ id: row.id });
        if (!err) {
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          this.getList();
        }
      }, 1000),
      onClose() {
        Object.assign(this.$data.form, this.$options.data().form);
        this.$nextTick(function () {
          this.$refs.form.clearValidate();
        });
      },
      // 验证银行卡
      verificationCard(row) {
        this.currentRow = row;
        this.dialogVisible = true;
      },
      tiedCard: debounce(function () {
        this.$refs.form.validate(async valid => {
          if (!valid) return;
          bindingCardValidAmount({
            ...this.form,
            id: this.currentRow.id,
          }).then(async ({ res, err }) => {
            if (!err) {
              this.getList();
              this.$message({
                type: 'success',
                message: '验证卡成功!',
              });
              this.dialogVisible = false;
            }
          });
        });
      }, 1000),
      handleOperate: debounce(async function (row, type) {
        if (type === 2) {
          this.$prompt('拒绝理由', '二次确认', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputType: 'textarea',
            inputValidator: value => {
              if (!value) {
                return '拒绝原因不能为空！';
              }
            },
          })
            .then(({ value }) => {
              if (row.applyStatus === 'UN_BIND_APPLY') {
                this.unBindingAudit({
                  id: row.id,
                  auditType: 2,
                  remark: value,
                });
              } else {
                this.saveOperateApi({
                  id: row.id,
                  auditType: 2,
                  remark: value,
                });
              }
            })
            .catch(() => {});
          return;
        }

        if (type === 3) {
          const res = await bindingCardBinding({
            id: row.id,
          });
          if (!res.err) {
            this.getList();
            this.$message({
              type: 'success',
              message: '绑卡成功!, 请去验证卡',
            });
            this.verificationCard(row);
          }
          return;
        }
        this.$confirm('确认同意该操作?', '二次确认', {
          confirmButtonText: '通过',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            if (row.applyStatus === 'UN_BIND_APPLY') {
              this.unBindingAudit({
                id: row.id,
                auditType: 1,
              });
            } else {
              this.saveOperateApi({ id: row.id, auditType: 1 });
            }
          })
          .catch(() => {});
      }, 1000),
      async saveOperateApi(params) {
        console.log(params, 'paramsparamsparams');
        const { res, err } = await bindingCardAudit(params);
        if (!err) {
          this.getList();
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
        }
      },

      handleTabClick() {},
      getParams() {
        const params = {
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getSubAccountBankList() {
        const res = await getSubAccountBankList();

        this.bankList = res ? res.res : [];
      },
      async getList() {
        const { query } = this.$route;
        this.options.loading = true;
        const res = await getBindingCardsList({
          ...this.searchParams,
          custId: query.custId,
          channel: 'lianlian',
        });
        console.log(res, 'res');
        this.list = res ? res : [];
        this.options.loading = false;
      },

      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },
    },
  };
</script>
<style lang="scss"></style>
