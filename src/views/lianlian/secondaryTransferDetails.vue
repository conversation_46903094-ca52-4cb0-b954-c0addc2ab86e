<template>
  <div>
    <el-form inline>
      <el-form-item label="订单编号:">
        <el-input v-model="searchParams.salesNo" placeholder="请输入订单编号" />
      </el-form-item>
      <el-form-item label="交易类型:">
        <el-select
          v-model="searchParams.orderType"
          clearable
          placeholder="请选择交易类型"
        >
          <el-option
            v-for="item in orderType"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button type="primary" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>

    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import { secondSplitDetail } from '@/api/pingan';

  export default {
    components: {
      dynamictable,
    },

    data() {
      return {
        searchParams: {
          orderType: null,
          salesNo: null,
        },
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
        orderType: [
          {
            key: 0,
            value: '正向',
          },
          {
            key: 1,
            value: '逆向',
          },
        ],
      };
    },
    created() {
      const query = this.$route.query;
      this.searchParams = {
        ...query,
        orderType: this.searchParams.orderType,
        salesNo: this.searchParams.salesNo,
      };
      this.getList(true);
    },
    methods: {
      getParams() {
        const params = {
          ...this.searchParams,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const { res, err } = await secondSplitDetail(params);
        if (res && !err) {
          this.list = res ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        }
        this.options.loading = false;
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },

      getColumns() {
        const columns = [
          {
            prop: 'id',
            label: 'ID',
          },
          {
            prop: 'salesNo',
            label: '订单编号',
          },
          {
            prop: 'outAccountName',
            label: '贸易主体',
          },
          {
            prop: 'orderTypeDesc',
            label: '交易类型',
          },
          {
            prop: 'currency',
            label: '币种',
          },
          {
            prop: 'orderAmount',
            label: '实付金额',
          },
          {
            prop: 'payTime',
            label: '实际支付时间',
          },
          {
            prop: 'feeTypeDesc',
            label: '费用类型',
          },
          {
            prop: 'feeRate',
            label: '费用比例',
          },
          {
            prop: 'originFeeAmount',
            label: '应结金额',
          },
          {
            prop: 'feeAmount',
            label: '实结金额',
          },
          {
            prop: 'inAccountName',
            label: '结算主体',
          },
        ];
        return columns;
      },
    },
  };
</script>
<style lang="scss"></style>
