<template>
  <div>
    <el-button type="text" @click="$router.go(-1)">返回</el-button>
    <dynamictable
      :data-source="logList"
      :columns="logColumns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    />
  </div>
</template>

<script>
  import { fundOperLog } from '@/api/pingan';
  import dynamictable from '@/components/dynamic-table';
  import { parseTime } from '@/utils';

  export default {
    components: {
      dynamictable,
    },
    data() {
      return {
        logList: [],
        options: {
          loading: false,
          border: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        logColumns: [
          {
            prop: 'id',
            label: '操作人ID',
          },
          {
            prop: 'operator',
            label: '操作人姓名',
          },
          {
            prop: 'operDesc',
            label: '操作动作',
          },
          {
            prop: 'remark',
            label: '备注',
          },
          {
            prop: 'createTime',
            label: '操作时间',
            render: ({ createTime }) => (
              <span>{createTime ? parseTime(createTime) : ''}</span>
            ),
          },
        ],
      };
    },
    created() {
      this.getList();
    },

    methods: {
      async getList() {
        const id = this.$route.query.id;
        console.log(id, 'id');
        this.options.loading = true;
        const { res, err } = await fundOperLog(id);
        this.options.loading = false;
        if (res && !err) {
          this.logList = res ? res : [];
        }
      },
    },
  };
</script>

<style lang="scss" scoped></style>
