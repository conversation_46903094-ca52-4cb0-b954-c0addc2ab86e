<template lang="pug">
el-dialog(
  title='会员子账户开户'
  :visible.sync='showDialog'
  width='40%'
  :close-on-click-modal='false'
)
  el-form(
    ref='form'
    v-loading='loading'
    :model='form'
    :rules='rules'
    label-position='right'
    label-width='150px'
    label-suffix=':'
  )
    el-form-item(label='子账户名' prop='accountName')
      el-input(v-model='form.accountName')
    //- el-form-item(label='企业编码' prop='custId')
    //-   el-input(v-model='form.custId')
    el-form-item(label='账户类型')
      el-checkbox(label='普通子账户' checked disabled)
      el-checkbox(label='商家子账户' checked disabled)

    el-form-item(label='会员证件类型' prop='idType')
      el-select(v-model='form.idType' placeholder='请选择' style='width: 100%')
        el-option(
          v-for='item in idTypeList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-form-item(label='会员证件号码' prop='idNo')
      el-input(v-model='form.idNo')
    el-form-item(label='法人姓名' prop='legalName')
      el-input(v-model='form.legalName')
    el-form-item(label='法人证件号' prop='legalIdNo')
      el-input(v-model='form.legalIdNo')
    el-form-item(label='手机号码' prop='mobile')
      el-input(v-model='form.mobile')
    <div class='agreement'>
      el-col(:offset='1') 阅读并同意以下协议：
      el-col(:offset='1') 
        a(
          href='https://my.orangebank.com.cn/orgLogin/hd/act/jianzb/jzbxy.html'
          target='_blank'
          style='color: #1890ff'
        ) 平安银行电子商务“见证宝”商户服务协议
      el-col(:offset='1')
        a(
          href='https://auth.orangebank.com.cn/#/m/cDealOne'
          target='_blank'
          style='color: #1890ff'
        ) 平安数字用户服务协议
    </div>
  span.dialog-footer(slot='footer')
    el-button(@click='showDialog = false') 取消
    el-button(
      type='primary'
      :loading='saveLoading'
      @click='handleConfirmClick'
    ) 提交
</template>
<script>
  import { isIdCard, isPhone } from '@/utils/validate';

  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      record: {
        type: Object,
        default: undefined,
      },
      idTypeList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      const validatePhone = (rule, value, callback) => {
        if (!isPhone(value) && value) {
          callback(new Error('手机号格式错误'));
        } else {
          callback();
        }
      };
      const validateIsIdCard = (rule, value, callback) => {
        if (!isIdCard(value) && value) {
          callback();
        } else {
          callback();
        }
      };
      const isName = (rule, value, callback) => {
        if (value.length > 12 && value) {
          callback(new Error('请填写正确的法人姓名'));
        } else {
          callback();
        }
      };
      return {
        form: {
          idType: '73',
        },
        offset: 1,
        loading: false,
        saveLoading: false,
        rules: {
          accountName: {
            required: true,
            message: '请输入子账户名',
            trigger: 'change',
          },
          // custId: {
          //   required: true,
          //   message: '请输入企业编码',
          //   trigger: 'change',
          // },
          idType: {
            required: true,
            message: '请选择会员证件类型',
            trigger: 'change',
          },
          idNo: {
            required: true,
            message: '请输入会员证件号码',
            trigger: 'change',
          },
          // legalName: {
          //   required: true,
          //   message: '请输入法人姓名',
          //   trigger: 'change',
          // },
          // legalIdNo: {
          //   required: true,
          //   message: '请输入法人证件号',
          //   trigger: 'change',
          // },
          legalName: [
            {
              required: true,
              message: '请输入法人姓名',
              trigger: 'change',
            },
          ],
          legalIdNo: [
            {
              required: true,
              message: '请输入法人证件号',
              trigger: 'change',
            },
          ],
          mobile: [
            {
              required: true,
              message: '请输入手机号',
              trigger: 'change',
            },
          ],
          // mobile: {
          //   required: true,
          //   message: '请输入手机号码',
          //   trigger: 'change',
          // },
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          if (!value) {
            // 重置
            this.form = {};
            this.$refs.form.resetFields();
          }
          this.$emit('change', value);
        },
      },
    },
    methods: {
      handleConfirmClick() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.submit();
          }
        });
      },
      async submit() {
        this.saveLoading = true;
        const body = {
          ...this.form,
        };
        const { err, res } = await this.$apis.pingan.openSubAccount(body);
        if (!err) {
          const { success, message } = res;
          if (res) {
            this.$message.success(res);
            this.showDialog = false;
            this.$emit('success');
          } else {
            this.$message.error(message);
          }
        }

        this.saveLoading = false;
      },
    },
  };
</script>
<style lang="scss" scoped>
  .add-dialog {
    ::v-deep {
      .el-input-number .el-input__inner {
        text-align: left;
      }
    }
  }
  .agreement a {
    display: block;
    color: #606266;
    margin-top: 6px;
    margin-left: 14px;
  }
</style>
