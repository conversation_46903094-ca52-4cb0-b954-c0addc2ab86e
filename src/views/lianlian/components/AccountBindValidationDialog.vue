<template lang="pug">
el-dialog.add-dialog(
  title='会员子账户绑定提现账户回填验证'
  :visible.sync='showDialog'
  width='40%'
  :close-on-click-modal='false'
)
  el-form(
    ref='form'
    v-loading='loading'
    :model='form'
    :rules='rules'
    label-position='right'
    label-width='150px'
    label-suffix=':'
  )
    el-form-item(label='子账户名')
      div {{ record.accountName }}
    el-form-item(label='账户类型')
      div {{ record.accountTypeDesc }}
    el-form-item(label='提现账号')
      div {{ record.cardNo }}
    el-form-item(label='提现开户行')
      div {{ record.bankName }}
    el-form-item(label='超级网银号')
      div {{ record.superBankCode }}
    el-form-item(label='验证金额' prop='amount')
      el-input-number(
        v-model='form.amount'
        placeholder='请输入验证金额'
        :min='0'
        :precision='2'
        :controls='false'
        style='width: 100%'
      )
    el-form-item(label='短信验证码' prop='serialNo')
      el-input(v-model='form.serialNo' placeholder='请输入短信验证码')

  span.dialog-footer(slot='footer')
    el-button(@click='showDialog = false') 取消
    el-button(
      type='primary'
      :loading='saveLoading'
      @click='handleConfirmClick'
    ) 提交
</template>
<script>
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      record: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        form: {},
        loading: false,
        saveLoading: false,
        rules: {
          amount: {
            required: true,
            message: '请输入验证金额，保留2位小数',
            trigger: 'blur',
          },
          serialNo: {
            required: true,
            message: '请输入短信验证码',
            trigger: 'blur',
          },
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          if (!value) {
            // 重置
            this.form = {};
            this.$refs.form.resetFields();
          }
          this.$emit('change', value);
        },
      },
    },
    methods: {
      handleConfirmClick() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.submit();
          }
        });
      },
      async submit() {
        this.saveLoading = true;
        const body = {
          id: this.record.id,
          ...this.form,
        };
        const { err, res } = await this.$apis.pingan.subAccountBindValid(body);
        if (!err) {
          const { success, message } = res;
          if (success) {
            this.$message.success('操作成功');
            this.showDialog = false;
            this.$emit('success');
          } else {
            this.$message.error(message);
          }
        }

        this.saveLoading = false;
      },
      onIsPinganCardChange(value) {
        if (value == '1') {
          delete this.form.bankName;
        }
      },
    },
  };
</script>
<style lang="scss" scoped>
  .add-dialog {
    ::v-deep {
      .el-input-number .el-input__inner {
        text-align: left;
      }
    }
  }
</style>
