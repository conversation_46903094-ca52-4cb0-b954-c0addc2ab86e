<!--
 * @Author: 七七
 * @Date: 2022-04-18 00:18:40
 * @LastEditors: xuxiang
 * @LastEditTime: 2022-04-24 10:40:15
 * @FilePath: /access-fmis-web/src/views/xiamen/components/dailyBalanceDetails.vue
-->
<template>
  <div>
    <el-form inline>
      <el-form-item label="余额时间:">
        <el-date-picker
          v-model="balanceData"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          :clearable="false"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button @click="onReset">重置</el-button>
        <el-button @click="onExport">导出</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import { debounce, downloadFile, parseTime, setInitData } from '@/utils';
  import { listHisBalance } from '@/api/pingan';
  import { exportExcel } from '@/api/blob';

  export default {
    components: { dynamictable },
    data() {
      return {
        balanceData: '',
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          index: true,
          indexName: '编号',
        },
        columns: [
          {
            prop: 'amount',
            label: '日终可用余额',
          },
          {
            prop: 'avlAmount',
            label: '日终可提现余额',
          },
          {
            prop: 'freezeAmount',
            label: '日终冻结余额',
          },
          {
            prop: 'newWaitAmount',
            label: '当日待转可提现发生额',
          },
          {
            prop: 'waitAmount',
            label: '日终待转可提现余额',
          },
          {
            prop: 'accDate',
            label: '日切时间',
          },
        ],
      };
    },
    created() {
      this.balanceData = setInitData(30);
      this.getList(true);
    },
    methods: {
      getParams() {
        const { accountNo } = this.$route.query;
        const { balanceData } = this;
        const body = {
          startTime: balanceData
            ? parseTime(balanceData[0], '{y}-{m}-{d}')
            : '',
          endTime: balanceData ? parseTime(balanceData[1], '{y}-{m}-{d}') : '',
          accountNo,
          pageNo: this.pagination.pageSize,
          pageSize: this.pagination.pageLimit,
        };
        return body;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        this.options.loading = true;
        const body = this.getParams();
        const { res, err } = await listHisBalance(body);
        this.options.loading = false;
        if (res && !err) {
          this.list = res ? res.records : [];
          this.pagination.total = res.total ? res.total : 0;
        }
      },
      onExport: debounce(function () {
        const params = this.getParams();
        exportExcel(
          params,
          '/api/split-service/acc/hisBalance/exportHisBalance',
          'post',
        ).then(res => {
          downloadFile(res.data, '日切余额明细');
        });
      }, 3000),
      onReset() {
        this.balanceData = setInitData(30);
      },
    },
  };
</script>
<style scoped></style>
