<template lang="pug">
el-dialog(
  :title='"会员子账户转账" + (record ? "修改" : "新增")'
  :visible.sync='showDialog'
  width='40%'
  :close-on-click-modal='false'
)
  el-form(
    ref='form'
    v-loading='loading'
    :model='form'
    :rules='rules'
    label-position='right'
    label-width='150px'
    label-suffix=':'
  )
    el-form-item(label='付款子账户名' prop='payerId')
      el-select(v-model='form.payerId' placeholder='请选择' style='width: 100%')
        el-option(
          v-for='item in payerAccountName'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    <!-- el-form-item(label='付款账户类型' prop='payeeId') -->
      <!-- el-select(v-model="form.payeeId" placeholder="请选择" style="width: 100%") -->
        <!-- el-option( -->
          <!-- v-for="item in payerAccountName" -->
          <!-- :key="item.dictValue" -->
          <!-- :label="item.dictDesc" -->
          <!-- :value="item.dictValue" -->
        <!-- ) -->
    el-form-item(label='收款子账户名' prop='payeeId')
      el-select(v-model='form.payeeId' placeholder='请选择' style='width: 100%')
        el-option(
          v-for='item in payeeAccountName'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    <!-- el-form-item(label='收款账户类型' prop='payeeId') -->
      <!-- el-select(v-model="form.payeeId" placeholder="请选择" style="width: 100%") -->
        <!-- el-option( -->
          <!-- v-for="item in payerAccountName" -->
          <!-- :key="item.dictValue" -->
          <!-- :label="item.dictDesc" -->
          <!-- :value="item.dictValue" -->
        <!-- ) -->
    el-form-item(v-if='form.payerId' label='付款子账户')
      div {{ payerAccountMap[form.payerId] }}
    el-form-item(v-if='form.payeeId' label='收款子账户')
      div {{ payeeAccountMap[form.payeeId] }}
    el-form-item(label='操作备注' prop='remark')
      el-input(v-model='form.remark' type='textarea' placeholder='请输入备注')
  span.dialog-footer(slot='footer')
    el-button(@click='showDialog = false') 取消
    el-button(
      type='primary'
      :disabled='confirmDisable'
      :loading='saveLoading'
      @click='handleConfirmClick'
    ) 提交
</template>
<script>
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      record: {
        type: Object,
        default: undefined,
      },
      payerAccountName: {
        type: Array,
        default: () => [],
      },
      payeeAccountName: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        form: {
          transferAmount: undefined,
          remark: '',
        },
        loading: false,
        saveLoading: false,
        rules: {
          payerId: [
            {
              required: true,
              message: '请选择付款子账户',
              trigger: 'change',
            },
          ],
          payeeId: [
            {
              required: true,
              message: '请选择收款子账户',
              trigger: 'change',
            },
          ],
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          if (!value) {
            // 重置
            this.form = {};
            this.$refs.form.resetFields();
          }
          this.$emit('change', value);
        },
      },
      confirmDisable() {
        const { payerId, payeeId } = this.form;
        if (payeeId && payerId) {
          return false;
        }
        return true;
      },
      payeeAccountMap() {
        const map = {};
        if (this.payeeAccountName && this.payeeAccountName.length > 0) {
          this.payeeAccountName.forEach(({ dictValue, dictDesc }) => {
            map[dictValue] = dictDesc;
          });
        }
        return map;
      },
      payerAccountMap() {
        const map = {};
        if (this.payerAccountName && this.payerAccountName.length > 0) {
          this.payerAccountName.forEach(({ dictValue, dictDesc }) => {
            map[dictValue] = dictDesc;
          });
        }
        return map;
      },
    },
    watch: {
      show(value) {
        if (value) {
          if (this.record) {
            const { payerId, payeeId, remark } = this.record;
            this.form = {
              payerId: String(payerId),
              payeeId: String(payeeId),
              remark,
            };
          }
        }
      },
    },
    methods: {
      handleConfirmClick() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.submit();
          }
        });
      },
      async submit() {
        this.saveLoading = true;
        const { payeeId, payerId, remark } = this.form;
        const body = {
          payeeId,
          payerId,
          remark,
        };
        let funcName = 'addMemberSubAccountTransfer';
        if (this.record) {
          body.id = this.record.id;
          funcName = 'updateMemberSubAccountTransfer';
        }
        const { err, res } = await this.$apis.pingan[funcName](body);
        if (!err) {
          this.$message.success('操作成功');
          this.showDialog = false;
          this.$emit('success');
        }

        this.saveLoading = false;
      },
    },
  };
</script>
<style lang="scss" scoped>
  .add-dialog {
    ::v-deep {
      .el-input-number .el-input__inner {
        text-align: left;
      }
    }
  }
</style>
