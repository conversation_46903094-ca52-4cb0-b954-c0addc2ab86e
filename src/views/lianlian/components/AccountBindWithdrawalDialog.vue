<template lang="pug">
el-dialog(
  title='会员子账户绑定提现账户'
  :visible.sync='showDialog'
  width='40%'
  :close-on-click-modal='false'
)
  el-form(
    ref='form'
    v-loading='loading'
    :model='form'
    :rules='rules'
    label-position='right'
    label-width='150px'
    label-suffix=':'
  )
    el-form-item(label='子账户名')
      div {{ record.accountName }}
    el-form-item(label='账户类型')
      div {{ record.accountTypeDesc }}
    //- el-form-item(label='开户行是否为本行' prop='isPinganCard')
    //-   el-radio-group(
    //-     v-model='form.isPinganCard'
    //-     @change='onIsPinganCardChange'
    //-   )
    //-     el-radio(label='1') 是
    //-     el-radio(label='2') 否
    //- el-form-item(v-if='form.isPinganCard != 1' label='提现账号开户行' prop='bankName')
    //-   el-input(v-model='form.bankName' placeholder='请输入开户行名称')
    el-form-item(label='开户银行' prop='superBankCode')
      el-select(
        v-model='form.superBankCode'
        filterable
        style='width: 100%'
        placeholder='请选择开户银行'
      ) 
        el-option(
          v-for='item in bankList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
      //- el-input(v-model='form.superBankCode' placeholder='请输入超级网银号')
    el-form-item(label='提现账号' prop='cardNo')
      el-input(v-model='form.cardNo')
    el-form-item(label='手机号码')
      div {{ record.mobile }}
  span.dialog-footer(slot='footer')
    el-button(@click='showDialog = false') 取消
    el-button(
      type='primary'
      :loading='saveLoading'
      @click='handleConfirmClick'
    ) 提交
</template>
<script>
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      record: {
        type: Object,
        default: () => {},
      },
      bankList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        form: {},
        loading: false,
        saveLoading: false,
        rules: {
          isPinganCard: {
            required: true,
            message: '请选择',
            trigger: 'change',
          },
          bankName: {
            required: true,
            message: '请输入提现账号开户行',
            trigger: 'change',
          },
          superBankCode: {
            required: true,
            message: '请输入超级网银号',
            trigger: 'change',
          },
          cardNo: {
            required: true,
            message: '请输入提现账号',
            trigger: 'change',
          },
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          if (!value) {
            // 重置
            this.form = {};
            this.$refs.form.resetFields();
          }
          this.$emit('change', value);
        },
      },
    },
    methods: {
      handleConfirmClick() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.submit();
          }
        });
      },
      async submit() {
        this.saveLoading = true;
        const body = {
          id: this.record.id,
          ...this.form,
        };
        const { err, res } = await this.$apis.pingan.subAccountBindCard(body);
        if (!err) {
          const { success, message } = res;
          if (success) {
            this.$message.success('操作成功');
            this.showDialog = false;
            this.$emit('success');
          } else {
            this.$message.error(message);
          }
        }

        this.saveLoading = false;
      },
      onIsPinganCardChange(value) {
        if (value == '1') {
          delete this.form.bankName;
        }
      },
    },
  };
</script>
<style lang="scss" scoped>
  .add-dialog {
    ::v-deep {
      .el-input-number .el-input__inner {
        text-align: left;
      }
    }
  }
</style>
