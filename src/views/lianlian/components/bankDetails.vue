<!--
 * @Author: 七七
 * @Date: 2022-04-18 00:18:40
 * @LastEditors: xuxiang
 * @LastEditTime: 2022-04-27 17:03:49
 * @FilePath: /access-fmis-web/src/views/xiamen/components/dailyBalanceDetails.vue
-->
<template>
  <div>
    <el-form inline>
      <el-form-item label="交易流水:">
        <el-input
          v-model="searchParams.channelSeqNo"
          placeholder="请输入交易流水号"
        ></el-input>
      </el-form-item>
      <el-form-item label="交易类型:">
        <el-select
          v-model="searchParams.bookingFlag"
          clearable
          placeholder="请选择交易类型"
        >
          <el-option
            v-for="item in bookingFlag"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="账单类型:">
        <el-select
          v-model="searchParams.bookingType"
          clearable
          placeholder="请选择账单类型"
        >
          <el-option
            v-for="item in bookingType"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="交易时间:">
        <el-date-picker
          v-model="transactionData"
          type="daterange"
          :clearable="false"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :picker-options="pickerOptions"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button @click="onReset">重置</el-button>
        <el-button @click="onExport">导出</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import { debounce, downloadFile, parseTime, setInitData } from '@/utils';
  import { bankDetailQuery } from '@/api/pingan';
  import { exportExcel } from '@/api/blob';

  export default {
    components: { dynamictable },
    props: {
      bookingType: {
        type: Array,
        default: () => [],
      },
      bookingFlag: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        transactionData: '',
        list: [],
        searchParams: {
          accountNo: '',
          beginDate: '',
          endDate: '',
          bookingFlag: '',
          bookingType: '',
          channelSeqNo: '',
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          index: true,
          indexName: '编号',
        },
        choiceDate0: '',
        pickerOptions: {
          onPick: ({ maxDate, minDate }) => {
            console.log(minDate, 'minDate');
            this.choiceDate0 = minDate.getTime();
            if (maxDate) {
              this.choiceDate0 = '';
            }
          },
          disabledDate: time => {
            if (this.choiceDate0 !== '') {
              const one = 2 * 24 * 3600 * 1000;
              const minTime = this.choiceDate0 - one;
              const maxTime = this.choiceDate0 + one;
              return time.getTime() < minTime || time.getTime() > maxTime;
            }
          },
        },
        columns: [
          {
            prop: 'channelSeqNo',
            label: '交易流水',
          },

          {
            prop: 'transAmount',
            label: '交易金额',
          },
          {
            prop: 'bookingTypeDesc',
            label: '账单类型',
          },
          {
            prop: 'bookingFlagDesc',
            label: '交易类型',
          },

          {
            prop: 'transTime',
            label: '交易时间',
          },
        ],
      };
    },
    created() {
      this.transactionData = setInitData(2);
      this.getList(true);
    },
    methods: {
      getParams() {
        const { accountNo } = this.$route.query;
        const { transactionData } = this;
        const body = {
          ...this.searchParams,
          accountNo,
          beginDate: transactionData
            ? parseTime(transactionData[0], '{y}-{m}-{d}')
            : '',
          endDate: transactionData
            ? parseTime(transactionData[1], '{y}-{m}-{d}')
            : '',
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return body;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        this.options.loading = true;
        const body = this.getParams();

        const { res, err } = await bankDetailQuery(body);
        this.options.loading = false;
        if (res && !err) {
          this.list = res ? res.list : [];
          this.pagination.total = res.total ? res.total : 0;
        }
      },
      onExport: debounce(function () {
        const params = this.getParams();
        exportExcel(
          params,
          '/api/split-service/subAccountDetail/bankDetailExport',
          'post',
        ).then(res => {
          downloadFile(res.data, '银行流水明细');
        });
      }, 3000),
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.transactionData = setInitData(2);
      },
    },
  };
</script>
<style scoped></style>
