<template lang="pug">
el-dialog(
  title='贸易主体开户'
  :visible.sync='showDialog'
  width='40%'
  :close-on-click-modal='false'
)
  el-form(
    ref='form'
    v-loading='loading'
    :model='form'
    :rules='rules'
    label-position='right'
    label-width='150px'
    label-suffix=':'
  )
    el-form-item(label='贸易主体名称' prop='accountName')
      el-input(v-model='form.accountName' placeholder='请输入贸易主体名称')
    el-form-item(label='企业编码' prop='custId')
      el-input(v-model='form.custId' placeholder='请输入企业编码')
    el-form-item(label='账户类型')
      el-checkbox(label='普通子账户' checked disabled)
  span.dialog-footer(slot='footer')
    el-button(@click='showDialog = false') 取消
    el-button(
      type='primary'
      :loading='saveLoading'
      @click='handleConfirmClick'
    ) 提交
</template>
<script>
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        form: {},
        loading: false,
        saveLoading: false,
        rules: {
          accountName: {
            required: true,
            message: '请输入贸易主体名称',
            trigger: 'change',
          },
          custId: {
            required: true,
            message: '请输入企业编码',
            trigger: 'change',
          },
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          if (!value) {
            // 重置
            this.form = {};
            this.$refs.form.resetFields();
          }
          this.$emit('change', value);
        },
      },
    },
    methods: {
      handleConfirmClick() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.submit();
          }
        });
      },
      async submit() {
        this.saveLoading = true;
        const body = {
          ...this.form,
          channel: 'pingan',
        };
        const { err, res } = await this.$apis.pingan.createMainPartAccount(
          body,
        );
        if (!err) {
          const { success, message } = res;
          if (success) {
            this.$message.success('操作成功');
            this.showDialog = false;
            this.$emit('success');
          } else {
            this.$message.error(message);
          }
        }

        this.saveLoading = false;
      },
    },
  };
</script>
<style lang="scss" scoped>
  .add-dialog {
    ::v-deep {
      .el-input-number .el-input__inner {
        text-align: left;
      }
    }
  }
</style>
