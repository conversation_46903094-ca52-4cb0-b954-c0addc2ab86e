<template lang="pug">
el-dialog.add-dialog(
  title='账户提现'
  :visible.sync='showDialog'
  width='40%'
  :close-on-click-modal='false'
)
  el-form(
    ref='form'
    v-loading='loading'
    :model='form'
    :rules='rules'
    label-position='right'
    label-width='150px'
    label-suffix=':'
  )
    el-form-item(label='出款账户类型' prop='accountType')
      span {{ accountTypeDesc }}
    el-form-item(label='出款账户' prop='accountNo')
      span {{ accountDesc }}
    el-form-item(label='账户余额' prop='canCashBal')
      span {{ canCashBal }}元

    el-form-item(label='转出金额' prop='amount')
      .horizontal.left.vcenter
        el-input-number(
          v-model='form.amount'
          :min='0'
          :max='canCashBal'
          :precision='2'
          :controls='false'
          style='width: 100%'
          placeholder='请输入转出金额'
        )
        .margin-left-10 元
      ac-tag-value(
        v-if='form.amount != null'
        tag='账户余额'
        :value='balance'
        width='auto'
      )
    el-form-item(label='提现手续费' prop='serviceChargePercent' v-if='false')
      .horizontal.left.vcenter
        el-input-number(
          v-model='form.serviceChargePercent'
          :min='0'
          :max='100'
          :precision='2'
          :controls='false'
          style='width: 100%'
          placeholder='请输入提现手续费'
        )
        .margin-left-10 %
    el-form-item(label='收款账户' prop='cardNo')
      el-select(v-model='form.cardNo' placeholder='请选择' style='width: 100%')
        el-option(
          v-for='item in cardsList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-form-item(label='操作备注' prop='summary')
      el-input(v-model='form.summary' type='textarea' placeholder='请输入备注')

  span.dialog-footer(slot='footer')
    el-button(@click='showDialog = false') 取消
    el-button(
      type='primary'
      :loading='saveLoading'
      @click='handleConfirmClick'
    ) 提现
</template>
<script>
  import { subtraction } from '@/utils/math.js';
  import { debounce } from '@/utils';
  import { withdrawApply } from '@/api/lianlian';

  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      record: {
        type: Object,
        default: null,
        // required: true,
      },
      cardsList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        loading: false,
        saveLoading: false,
        form: {},
        rules: {
          // accountType: [{ required: true }],
          // accountNo: [{ required: true }],
          amount: [
            { required: true, message: '请输入转账金额', trigger: 'blur' },
          ],
          serviceChargePercent: [
            { required: true, message: '请输入转账金额', trigger: 'blur' },
          ],
          cardNo: [
            { required: true, message: '请选择收款账户', trigger: 'change' },
          ],
        },

        accountTypeDesc: '',
        canCashBal: 0,
        accountDesc: '',
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },

      balance() {
        if (this.form.amount != null) {
          return subtraction(this.canCashBal, this.form.amount);
        }
        return '';
      },
    },
    watch: {
      show(value) {
        if (value) {
          this.initData();
        } else {
          // 重置
          this.form = {};
          this.$refs.form.resetFields();
        }
      },
      cardsList: {
        handler(cardsList) {
          console.log('cardsList changed:', cardsList);
          if (Array.isArray(cardsList) && cardsList.length) {
            this.form.cardNo = cardsList[0]['dictValue'];
            console.log('Set default cardNo:', this.form.cardNo);
          } else {
            console.log('cardsList is empty or invalid');
          }
        },
        deep: true,
        immediate: true,
      },
    },
    mounted() {
      console.log('3333');
    },
    beforeUpdate() {
      console.log('444');
    },
    methods: {
      initData() {
        console.log('initData called, record:', this.record);
        if (this.record) {
          const {
            accountType,
            accountTypeDesc,
            accountName,
            accountNo,
            serviceChargePercent,
            canCashBal,
            bankName,
            cardNo,
            id,
          } = this.record;

          this.accountTypeDesc = accountTypeDesc;
          this.accountDesc = accountNo + ' ' + accountName;
          this.form.accountId = id;
          this.canCashBal = canCashBal;
          this.form.serviceChargePercent = serviceChargePercent;
          this.form.payerAccountNo = accountNo;

          console.log('Form initialized:', this.form);
        }
      },
      handleConfirmClick: debounce(function () {
        console.log('this.form', this.form);
        console.log('this.cardsList', this.cardsList);
        console.log('form.cardNo', this.form.cardNo);

        // 检查cardsList是否已加载
        if (!this.cardsList || this.cardsList.length === 0) {
          this.$message.error('收款账户列表正在加载中，请稍后重试');
          return;
        }

        // 检查cardNo是否有值
        if (!this.form.cardNo) {
          this.$message.error('请选择收款账户');
          return;
        }

        this.$refs.form.validate(async valid => {
          if (!valid) return;
          this.submit();
        });
      }, 800),
      async submit() {
        this.saveLoading = true;
        const body = {
          ...this.form,
        };

        const res = await withdrawApply(body);
        if (res) {
          this.$message.success('操作成功');
          this.$emit('success');
          this.showDialog = false;
        }

        this.saveLoading = false;
      },
    },
  };
</script>
<style lang="scss" scoped>
  .add-dialog {
    ::v-deep {
      .el-input-number .el-input__inner {
        text-align: left;
      }
    }
  }
</style>
