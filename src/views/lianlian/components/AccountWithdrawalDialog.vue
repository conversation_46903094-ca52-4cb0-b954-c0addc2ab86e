<template lang="pug">
el-dialog.add-dialog(
  title='账户提现'
  :visible.sync='showDialog'
  width='40%'
  :close-on-click-modal='false'
)
  el-form(
    ref='form'
    v-loading='loading'
    :model='form'
    :rules='rules'
    label-position='right'
    label-width='150px'
    label-suffix=':'
  )
    el-form-item(label='出款账户类型' prop='accountType')
      //- el-input(v-model="form.accountTypeDesc" disabled)
      span {{ accountTypeDesc }}
    el-form-item(label='出款账户' prop='accountNo')
      //- el-input(v-model="form.accountNo" disabled)
      span {{ accountDesc }}
    el-form-item(label='账户余额' prop='canCashBal')
      span {{ canCashBal }}元
      //- el-input(v-model="form.canCashBal" disabled)
      //-   span(slot="suffix") 元
    el-form-item(label='转出金额' prop='amount')
      .horizontal.left.vcenter
        el-input-number(
          v-model='form.amount'
          :min='0'
          :max='canCashBal'
          :precision='2'
          :controls='false'
          style='width: 100%'
          placeholder='请输入转出金额'
        )
        .margin-left-10 元
      ac-tag-value(
        v-if='form.amount != null'
        tag='账户余额'
        :value='balance'
        width='auto'
      )
    el-form-item(label='提现手续费' prop='serviceChargePercent')
      .horizontal.left.vcenter
        el-input-number(
          v-model='form.serviceChargePercent'
          :min='0'
          :max='100'
          :precision='2'
          :controls='false'
          style='width: 100%'
          placeholder='请输入提现手续费'
        )
        .margin-left-10 %
    el-form-item(label='收款账户')
      span {{ targetBankInfo }}
    el-form-item(label='操作备注' prop='remark')
      el-input(v-model='form.remark' type='textarea' placeholder='请输入备注')

  span.dialog-footer(slot='footer')
    el-button(@click='showDialog = false') 取消
    el-button(
      type='primary'
      :disabled='confirmDisable'
      :loading='saveLoading'
      @click='handleConfirmClick'
    ) 提现
</template>
<script>
  import { subtraction } from '@/utils/math.js';

  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      record: {
        type: Object,
        required: true,
      },
    },
    data() {
      return {
        loading: false,
        saveLoading: false,
        form: {},
        rules: {
          // accountType: [{ required: true }],
          // accountNo: [{ required: true }],
          amount: [
            { required: true, message: '请输入转账金额', trigger: 'blur' },
          ],
          serviceChargePercent: [
            { required: true, message: '请输入转账金额', trigger: 'blur' },
          ],
        },

        targetBankInfo: '',
        accountTypeDesc: '',
        canCashBal: 0,
        accountDesc: '',
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
      confirmDisable() {
        return !this.form.amount;
      },
      balance() {
        if (this.form.amount != null) {
          return subtraction(this.canCashBal, this.form.amount);
        }
        return '';
      },
    },
    watch: {
      show(value) {
        if (value) {
          this.initData();
        } else {
          // 重置
          this.form = {};
          this.$refs.form.resetFields();
        }
      },
    },
    methods: {
      initData() {
        if (this.record) {
          const {
            accountType,
            accountTypeDesc,
            accountName,
            accountNo,
            serviceChargePercent,
            canCashBal,
            bankName,
            cardNo,
            id,
          } = this.record;
          this.targetBankInfo =
            bankName && cardNo ? bankName + ' ' + cardNo : '';
          this.accountTypeDesc = accountTypeDesc;
          this.accountDesc = accountNo + ' ' + accountName;
          this.form.accountId = id;
          this.canCashBal = canCashBal;
          this.form.serviceChargePercent = serviceChargePercent;
        }
      },
      handleConfirmClick() {
        this.submit();
      },
      async submit() {
        this.saveLoading = true;
        const body = {
          ...this.form,
        };

        const { err, res } = await this.$apis.pingan.doWithdraw(body);
        if (!err) {
          this.$message.success('操作成功');
          this.$emit('success');
          this.showDialog = false;
        }

        this.saveLoading = false;
      },
    },
  };
</script>
<style lang="scss" scoped>
  .add-dialog {
    ::v-deep {
      .el-input-number .el-input__inner {
        text-align: left;
      }
    }
  }
</style>
