<template>
  <div>
    <el-dialog
      width="600px"
      title="新建"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="saveForm"
        :rules="rulesForm"
        label-width="160px"
      >
        <el-form-item label="开户银行: " prop="superBankCode">
          <el-select
            v-model="saveForm.superBankCode"
            style="width: 80%"
            filterable
            placeholder="请选择开户银行"
          >
            <el-option
              v-for="item in bankList"
              :key="item.superBankCode"
              :label="item.bankName"
              :value="item.superBankCode"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开户卡号: " prop="cardNo">
          <el-input
            v-model="saveForm.cardNo"
            placeholder="请输入开户卡号"
            style="width: 80%"
          ></el-input>
        </el-form-item>
        <el-form-item label="开户支行: " prop="branchName">
          <el-input
            v-model="saveForm.branchName"
            placeholder="请输入开户支行"
            style="width: 80%"
          ></el-input>
        </el-form-item>
        <el-form-item label="手机号: " prop="mobile">
          <el-input
            v-model="saveForm.mobile"
            style="width: 80%"
            placeholder="请输入手机号"
          ></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="onOK">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { bindingCardApply } from '@/api/pingan';
  import { debounce } from '@/utils';
  import { isPhone } from '@/utils/validate';

  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      currentRow: {
        type: Object,
        default: null,
      },
      bankList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      const validatePhone = (rule, value, callback) => {
        if (!isPhone(value) && value) {
          callback(new Error('手机号格式错误'));
        } else {
          callback();
        }
      };
      return {
        rulesForm: {
          superBankCode: [
            { required: true, message: '请选择开户银行', trigger: 'blur' },
          ],
          cardNo: [
            {
              required: true,
              message: '请输入开户卡号',
              trigger: 'blur',
            },
          ],
          mobile: [
            { required: true, message: '请输入手机号', trigger: 'blur' },
            {
              validator: validatePhone,
              trigger: 'blur',
            },
          ],
        },
        saveForm: {
          superBankCode: '',
          cardNo: '',
          branchName: '',
          mobile: '',
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(val) {
        if (val) {
        } else {
          Object.assign(this.$data.saveForm, this.$options.data().saveForm);
          this.$nextTick(function () {
            this.$refs.formData.clearValidate();
          });
        }
      },
    },
    created() {},
    methods: {
      onOK: debounce(function () {
        this.$refs.formData.validate(async valid => {
          if (!valid) return;
          const custId = this.$route.query.custId;
          bindingCardApply({
            ...this.saveForm,
            custId,
          }).then(({ res, err }) => {
            if (!err) {
              this.$message.success('添加成功');
              this.showDialog = false;
              this.$emit('onGet');
            }
          });
        });
      }, 800),
    },
  };
</script>

<style></style>
