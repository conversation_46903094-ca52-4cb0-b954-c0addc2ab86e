<template lang="pug">
el-dialog.add-dialog(
  title='会员子账户转账'
  :visible.sync='showDialog'
  width='40%'
  :close-on-click-modal='false'
)
  el-form(
    v-if='record'
    ref='form'
    :model='form'
    :rules='rules'
    label-position='right'
    label-width='150px'
    label-suffix=':'
  )
    el-form-item(label='付款子账户')
      div {{ record.payerAccountName }} {{ record.payerAccountTypeDesc }}
      div 账户余额：
        span {{ record.payerBalance }}
    el-form-item(label='收款子账户')
      div {{ record.payeeAccountName }} {{ record.payeeAccountTypeDesc }}
    el-form-item(label='金额' prop='amount')
      el-input-number(
        v-model='form.amount'
        :min='0'
        :max='record.payerBalance'
        :precision='2'
        :controls='false'
        style='width: 100%'
        placeholder='请输入转出金额'
      )
    el-form-item(label='操作备注' prop='remark')
      el-input(v-model='form.remark' type='textarea' placeholder='请输入备注')

  span.dialog-footer(slot='footer')
    el-button(@click='showDialog = false') 取消
    el-button(
      type='primary'
      :loading='saveLoading'
      @click='handleConfirmClick'
    ) 提交
  el-dialog(
    v-if='showVerifyDialog'
    title='短信验证'
    :visible.sync='showVerifyDialog'
    width='30%'
    :close-on-click-modal='false'
    append-to-body
  )
    el-form(
      ref='verifyForm'
      :model='form'
      :rules='rules'
      label-position='right'
      label-width='180px'
      label-suffix=':'
    )
      el-form-item(label='您接收验证码的号码为')
        div {{ entity.msgPhone }}
      el-form-item(label='短信验证码' prop='verificationCode')
        el-input(v-model='form.verificationCode' placeholder='请输入验证码')
    span.dialog-footer(slot='footer')
      el-button(@click='showVerifyDialog = false') 取消
      el-button(
        type='primary'
        :loading='verifySaveLoading'
        @click='handleVerifyConfirmClick'
      ) 提交
</template>
<script>
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      record: {
        type: Object,
        default: undefined,
      },
    },
    data() {
      //const validatePass = (rule, value, callback) => {
      //  console.log('rule', rule, value)
      //}
      return {
        form: {},
        verifyForm: {},
        entity: {},
        saveLoading: false,
        verifySaveLoading: false,
        showVerifyDialog: false,
        rules: {
          amount: [
            {
              //validator: validatePass,
              required: true,
              message: '请输入金额',
              trigger: 'blur',
            },
          ],
          verificationCode: {
            required: true,
            message: '请输入短信验证码',
            trigger: 'change',
          },
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          if (value) {
            this.initData();
          } else {
            // 重置
            this.form = {};
            this.$refs.form.resetFields();
          }
          this.$emit('change', value);
        },
      },
    },
    methods: {
      handleConfirmClick() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.submit();
          }
        });
      },
      handleVerifyConfirmClick() {
        this.$refs.verifyForm.validate(valid => {
          if (valid) {
            this.submit(true);
          }
        });
      },
      async submit(needAuthCode = false) {
        if (needAuthCode) {
          this.verifySaveLoading = true;
        } else {
          this.saveLoading = true;
        }
        const { payerId, payeeId } = this.record;
        const body = {
          ...this.form,
          outAccId: payerId,
          inAccId: payeeId,
          needAuthCode,
        };
        if (needAuthCode) {
          body.serialNo = this.entity.serialNo;
        }

        const { err, res } = await this.$apis.pingan.createSubAccountTransfer(
          body,
        );
        if (!err) {
          this.showVerifyDialog = false;
          const { success, entity, message } = res;
          if (success) {
            const { needVerify, msgPhone, serialNo } = entity || {};
            if (needVerify) {
              //需要短信验证
              this.entity = entity;
              this.showVerifyDialog = true;
            } else {
              this.$emit('success');
              this.$message.success('操作成功');
              this.showDialog = false;
            }
          } else {
            // 显示提示
            this.$message.error(message);
          }
        }

        this.saveLoading = false;
        this.verifySaveLoading = false;
      },
    },
  };
</script>
<style lang="scss" scoped>
  .add-dialog {
    ::v-deep {
      .el-input-number .el-input__inner {
        text-align: left;
      }
    }
  }
</style>
