<template lang="pug">
.vertical.top.vcenter.padding-bottom-10
  el-form(inline label-suffix=':')
    el-form-item(label='交易时间')
      el-date-picker(
        v-model='time'
        type='datetimerange'
        range-separator='至'
        start-placeholder='开始日期'
        end-placeholder='结束日期'
        value-format='yyyy-MM-dd HH:mm:ss'
        clearable
      )
    el-form-item(label='付款子账户')
      el-select(
        v-model='outAccount'
        placeholder='请选择'
        style='width: 200px'
        :loading='optionLoading'
        clearable
        filterable
      )
        el-option(
          v-for='item in outList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-form-item(label='收款子账户')
      el-select(
        v-model='inAccount'
        placeholder='请选择'
        style='width: 200px'
        :loading='optionLoading'
        clearable
        filterable
      )
        el-option(
          v-for='item in inAccountList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-form-item(label='交易状态')
      el-select(
        v-model='status'
        placeholder='请选择'
        style='width: 200px'
        :loading='optionLoading'
        clearable
      )
        el-option(
          v-for='item in statusList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-button.margin-left-10(
      type='primary'
      icon='el-icon-search'
      @click='onSearchClick'
    ) 查询
    el-button.margin-left-10(type='primary' @click='onExport') 导出
  el-table.margin-top-20(
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
  )
    el-table-column(prop='transferNo' label='交易单号' width='190')
    el-table-column(prop='createTime' label='交易时间' width='170')
    el-table-column(prop='outAccount' label='付款子账户')
    el-table-column(prop='inAccount' label='收款子账户')
    el-table-column(prop='transferTypeDesc' label='转账类型')
    el-table-column(prop='amount' label='金额' width='120')
    el-table-column(prop='status' label='交易状态' width='100')
    el-table-column(prop='remark' label='备注')
    el-table-column(prop='channelCode' label='渠道返回码')
    el-table-column(prop='channelMsg' label='描述')
    //- 交易状态I-初始P-处理中S-成功F-失败
  el-pagination(
    :current-page='m_current'
    :page-sizes='[10, 15, 20, 30]'
    :page-size='m_pageSize'
    layout='total, sizes, prev, pager, next, jumper'
    :total='m_total'
    background
    @size-change='m_handleSizeChange'
    @current-change='m_handleCurrentChange'
  )
</template>
<script>
  import dayjs from 'dayjs';
  import { paginationMixin } from '@/mixins/tableMixin';
  import { debounce, downloadFile } from '@/utils';
  import { newExportExcel } from '@/api/blob';
  import {
    getTransferOrderList,
    getTransferOrderOptions,
  } from '@/api/lianlian';

  const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';
  export default {
    mixins: [paginationMixin],
    data() {
      return {
        loading: false,
        optionLoading: false,
        showDialog: true,

        time: [],
        status: null,
        outAccount: null,
        inAccount: null,

        currentRecord: null,

        statusList: [],
        inAccountList: [],
        outList: [],

        list: [],
      };
    },
    async created() {
      this.time = [
        dayjs().startOf('day').format(DATE_TIME_FORMAT),
        dayjs().endOf('day').format(DATE_TIME_FORMAT),
      ];
      await this.loadOptions();
      this.m_loadData();
    },
    methods: {
      onExport: debounce(function () {
        const body = {};
        if (this.time && this.time.length === 2) {
          const [start, end] = this.time;
          body.startTime = start;
          body.endTime = end;
        }
        if (this.status) {
          body.status = this.status;
        }
        if (this.inAccount) {
          body.inAccount = this.inAccount;
        }
        if (this.outAccount) {
          body.outAccount = this.outAccount;
        }

        newExportExcel(
          { ...body },
          '/api/split-service/transferOrder/export',
          'post',
        ).then(res => {
          if (res) {
            downloadFile(res.data, '转账记录列表');
          }
        });
      }, 800),
      async loadOptions() {
        this.optionLoading = true;
        const res = await getTransferOrderOptions({ channel: 'lianlian' });
        if (res) {
          const { inAccount, outList, statusList } = res;
          this.inAccountList = inAccount;
          this.outList = outList;
          this.statusList = statusList;
        } else {
          this.$message.error('下拉框数据获取失败');
        }

        this.optionLoading = false;
      },
      async m_loadData() {
        this.loading = false;
        const body = {
          current: this.m_current,
          size: this.m_pageSize,
          channel: 'lianlian',
        };
        if (this.time && this.time.length === 2) {
          const [start, end] = this.time;
          body.startTime = start;
          body.endTime = end;
        }
        if (this.status) {
          body.status = this.status;
        }
        if (this.inAccount) {
          body.inAccount = this.inAccount;
        }
        if (this.outAccount) {
          body.outAccount = this.outAccount;
        }
        const res = await getTransferOrderList(body);
        if (res) {
          const { total, records } = res;
          this.m_total = total;
          this.list = records;
        }
      },
      onSearchClick() {
        this.m_current = 1;
        this.m_loadData();
      },
      onResplitClick() {},
      onRowClick(row, index) {},
      handleSelectionChange(list) {
        this.selectList = list;
      },
    },
  };
</script>
