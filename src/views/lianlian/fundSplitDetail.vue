<!--
 * @Author: 七七
 * @Date: 2022-04-18 00:07:22
 * @LastEditors: xuxiang
 * @LastEditTime: 2022-04-29 17:04:23
 * @FilePath: /access-fmis-web/src/views/xiamen/fundDetail.vue
-->
<template>
  <div>
    <el-button
      style="font-size: 16px; margin-bottom: 10px"
      type="text"
      @click="$router.go(-1)"
    >
      返回
    </el-button>
    <el-tabs v-model="detailType" type="card" @tab-click="handleClick">
      <el-tab-pane label="日切余额明细" name="1">
        <dailyBalanceDetails></dailyBalanceDetails>
      </el-tab-pane>
      <el-tab-pane label="银行流水明细" name="2">
        <bankDetails :booking-type="bookingType" :booking-flag="bookingFlag" />
      </el-tab-pane>
      <el-tab-pane label="平台交易明细" name="3">
        <platformTransactionDetails
          :plat-trans-type="platTransType"
          :plat-biz-type="platBizType"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
  import bankDetails from './components/bankDetails.vue';
  import dailyBalanceDetails from './components/dailyBalanceDetails.vue';
  import platformTransactionDetails from './components/platformTransactionDetails.vue';
  import { subAccountDetailSelector } from '@/api/pingan';

  export default {
    components: {
      bankDetails,
      dailyBalanceDetails,
      platformTransactionDetails,
    },
    data() {
      return {
        detailType: '1', //明细类型1:日切余额明细 2:银行流水明细 3:平台交易明细
        bookingType: [],
        bookingFlag: [],
        platTransType: [],
        platBizType: [],
      };
    },
    created() {
      subAccountDetailSelector().then(({ err, res }) => {
        if (res && !err) {
          this.bookingType = res.bookingType;
          this.bookingFlag = res.bookingFlag;
          this.platTransType = res.platTransType;
          this.platBizType = res.platBizType;
        }
      });
    },
    mounted() {},
    methods: {
      handleClick() {},
    },
  };
</script>
<style scoped></style>
