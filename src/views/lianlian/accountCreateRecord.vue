<template lang="pug">
.vertical.top.vcenter.padding-bottom-10
  el-form(ref='form' :rules='rules' :model='form' label-suffix=':' inline)
    el-form-item(label='子账户名' prop='custId')
      el-select(
        v-model='form.custId'
        placeholder='请选择'
        style='width: 200px'
        :loading='optionLoading'
        filterable
      )
        el-option(
          v-for='item in memberSubAccountList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-form-item(label='账户类型' prop='subAccountType')
      el-select(
        v-model='form.subAccountType'
        placeholder='请选择'
        style='width: 200px'
        :loading='optionLoading'
      )
        el-option(
          v-for='item in memberSubAccountTypeList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-button.margin-left-10(
      type='primary'
      icon='el-icon-search'
      @click='onSearchClick'
    ) 查询
    ac-permission-button.margin-left-10(
      type='primary'
      btn-text='新增'
      permission-key='accountCreateRecord-add'
      icon='el-icon-plus'
      @click='onAddClick'
    )
  el-table.margin-top-20(
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
  )
    el-table-column(prop='openTime' label='开户时间')
    el-table-column(prop='accountNo' label='子账户ID')
    el-table-column(prop='accountName' label='子账户名')
    el-table-column(prop='accountTypeDesc' label='账户类型')
    el-table-column(prop='bankName' label='提现银行')
    el-table-column(prop='cardNo' label='提现银行账号')
    el-table-column(label='状态')
      template(v-slot='{ $index, row: { status } }')
        div {{ statusMap[status] }}
    el-table-column(label='操作')
      template(v-slot='{ $index, row }')
        ac-permission-button(
          v-if='row.status == "NEED_BINDING"'
          btn-text='绑定提现账号'
          permission-key='accountCreateRecord-bind-account'
          @click='onBindClick(row, $index)'
        )
        ac-permission-button(
          v-if='row.status == "NEED_VALID"'
          btn-text='回填验证金额'
          permission-key='accountCreateRecord-verification-amount'
          @click='onValidateClick(row, $index)'
        )
        ac-permission-button(
          title='解绑后无法撤销,需重新绑定，确认解绑么？'
          nodeType='popconfirm'
          v-if='row.status == "NORMAL"'
          btn-text='解绑提现卡'
          slotBtn='reference'
          permission-key='accountCreateRecord-unbinding-card'
          confirm-button-text='确定解绑'
          confirm-button-type='danger'
          @click='onUnBindClick(row, $index)'
        )
  el-pagination(
    :current-page='m_current'
    :page-sizes='[10, 15, 20, 30]'
    :page-size='m_pageSize'
    layout='total, sizes, prev, pager, next, jumper'
    :total='m_total'
    background
    @size-change='m_handleSizeChange'
    @current-change='m_handleCurrentChange'
  )
  account-create-dialog(
    v-model='showCreateDialog'
    :id-type-list='idTypeList'
    @success='refreshPage'
  )
  account-bind-withdrawal-dialog(
    v-if='currentRecord'
    v-model='showBindDialog'
    :record='currentRecord'
    :bank-list='bankList'
    @success='m_loadData'
  )
  account-bind-validation-dialog(
    v-if='currentRecord'
    v-model='showValidateDialog'
    :record='currentRecord'
    @success='m_loadData'
  )
</template>
<script>
  import { paginationMixin } from '@/mixins/tableMixin';
  import AccountCreateDialog from './components/AccountCreateDialog';
  import AccountBindWithdrawalDialog from './components/AccountBindWithdrawalDialog';
  import AccountBindValidationDialog from './components/AccountBindValidationDialog';

  const STATUS_MAP = {
    INIT: '初始',
    OPENING: '开户中',
    NEED_BINDING: '待绑卡',
    NEED_VALID: '待验证',
    NORMAL: '正常',
    CANCEL: '注销',
    FAIL: '开户失败',
  };

  export default {
    components: {
      AccountCreateDialog,
      AccountBindWithdrawalDialog,
      AccountBindValidationDialog,
    },
    mixins: [paginationMixin],
    data() {
      return {
        loading: false,
        optionLoading: false,
        showCreateDialog: false,
        showBindDialog: false,
        showValidateDialog: false,

        form: {
          subAccountType: null,
          custId: null,
        },

        currentRecord: null,

        memberSubAccountTypeList: [],
        memberSubAccountList: [],
        idTypeList: [],

        bankList: [],

        list: [],

        statusMap: STATUS_MAP,

        rules: {},
      };
    },
    async created() {
      await this.loadOptions();
      // this.$apis.pingan.getMemberSubAccountOptions().then(res => {});
      this.m_loadData();
    },
    methods: {
      refreshPage() {
        this.loadOptions();
        this.m_loadData();
      },
      async loadOptions() {
        const { err, res } = await this.$apis.pingan.getOpenSubAccountOptions();
        if (!err) {
          const {
            memberSubAccountList,
            memberSubAccountTypeList,
            idTypeList,
            bankList,
          } = res;
          this.memberSubAccountList = memberSubAccountList;
          this.memberSubAccountTypeList = memberSubAccountTypeList;
          this.idTypeList = idTypeList;
          this.bankList = bankList;
        }
      },
      async m_loadData() {
        this.loading = true;
        const body = {
          channel: 'pingan',
          current: this.m_current,
          size: this.m_pageSize,
          ...this.form,
        };

        const { err, res } = await this.$apis.pingan.getOpenSubAccountList(
          body,
        );
        if (!err && res) {
          const { total, list } = res;
          this.m_total = total;
          this.list = list;
        }
        this.loading = false;
      },
      onSearchClick() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.m_current = 1;
            this.m_loadData();
          }
        });
      },
      onAddClick() {
        this.showCreateDialog = true;
      },
      onBindClick(row, index) {
        this.currentRecord = row;
        this.showBindDialog = true;
      },
      onValidateClick(row, index) {
        this.currentRecord = row;
        this.showValidateDialog = true;
      },
      onUnBindClick(row, index) {
        this.unBindCardRecord(row.id);
      },
      async unBindCardRecord(id) {
        const { res, err } = await this.$apis.pingan.unbindingCard(id);
        if (!err) {
          this.$message.success('操作成功');
          this.m_current = 1;
          this.m_loadData();
        }
      },
    },
  };
</script>
