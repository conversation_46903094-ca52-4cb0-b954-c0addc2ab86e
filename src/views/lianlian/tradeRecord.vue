<template lang="pug">
.vertical.top.vcenter.padding-bottom-10
  el-form(inline label-suffix=':')
    el-form-item(label='交易时间' required :error='errMsg')
      el-date-picker(
        v-model='time'
        type='datetimerange'
        range-separator='至'
        start-placeholder='开始日期'
        end-placeholder='结束日期'
        value-format='yyyy-MM-dd HH:mm:ss'
        clearable
      )
    el-form-item(label='发货仓')
      el-select(
        v-model='depoCode'
        placeholder='请选择'
        style='width: 200px'
        :loading='optionLoading'
        clearable
      )
        el-option(
          v-for='item in depoList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-form-item(label='收款子账户')
      el-select(
        v-model='accountNo'
        placeholder='请选择'
        style='width: 200px'
        :loading='optionLoading'
        clearable
        filterable
      )
        el-option(
          v-for='item in subAccountList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-form-item(label='交易状态')
      el-select(
        v-model='status'
        placeholder='请选择'
        style='width: 200px'
        :loading='optionLoading'
        clearable
      )
        el-option(
          v-for='item in statusList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-button.margin-left-10(
      type='primary'
      icon='el-icon-search'
      @click='onSearchClick'
    ) 查询
    ac-permission-button.margin-left-10(
      type='primary'
      btn-text='重新分账'
      permission-key='tradeRecord-repartition'
      @click='onResplitClick'
      :disabled='selectList.length === 0'
    )
  el-table.margin-top-20(
    ref='table'
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
    @selection-change='handleSelectionChange'
  )
    el-table-column(
      type='selection'
      width='55'
      :selectable='calcRowSelectable'
    )
    el-table-column(prop='orderNo' label='交易单号' width='190')
    el-table-column(prop='createTime' label='交易时间' width='170')
    el-table-column(prop='depoName' label='发货仓')
    el-table-column(prop='custLevelMsg' label='经销商等级')
    el-table-column(prop='accountNo' label='收款子账户' width='170')
    el-table-column(prop='amount' label='金额')
    el-table-column(prop='statusMsg' label='交易状态')
    el-table-column(prop='channelCode' label='渠道返回码')
    el-table-column(prop='channelMsg' label='描述')
      <!-- template(v-slot="{ row: { status } }") -->
        <!-- div {{statusMap[status]}} -->

    //- 交易状态I-初始P-处理中S-成功F-失败
  el-pagination(
    :current-page='m_current'
    :page-sizes='[10, 20, 30, 50]'
    :page-size='m_pageSize'
    layout='total, sizes, prev, pager, next, jumper'
    :total='m_total'
    background
    @size-change='m_handleSizeChange'
    @current-change='m_handleCurrentChange'
  )
</template>
<script>
  import dayjs from 'dayjs';
  import { paginationMixin } from '@/mixins/tableMixin';

  const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';

  export default {
    mixins: [paginationMixin],
    data() {
      return {
        loading: false,
        optionLoading: false,
        showDialog: true,

        time: [],
        depoCode: null,
        accountNo: null,
        status: null,

        errMsg: '',

        currentRecord: null,

        depoList: [],
        subAccountList: [],
        statusList: [],

        selectList: [],

        list: [],
      };
    },
    computed: {
      statusMap() {
        const map = {};
        if (this.statusList && this.statusList.length > 0) {
          this.statusList.forEach(({ dictValue, dictDesc }) => {
            map[dictValue] = dictDesc;
          });
        }
        return map;
      },
    },
    async created() {
      this.time = [
        dayjs().startOf('day').format(DATE_TIME_FORMAT),
        dayjs().endOf('day').format(DATE_TIME_FORMAT),
      ];
      await this.loadOptions();
      this.m_loadData();
    },
    methods: {
      async loadOptions() {
        this.optionLoading = true;
        const {
          err,
          res,
        } = await this.$apis.pingan.getTradeSubjectRecordOptions();
        if (!err) {
          const { depoList, inAccount, statusList } = res;
          this.depoList = depoList;
          this.subAccountList = inAccount;
          this.statusList = statusList;
        }
        this.optionLoading = false;
      },
      async m_loadData() {
        this.loading = true;
        this.clearSelect();
        const body = {
          current: this.m_current,
          size: this.m_pageSize,
        };
        if (this.time && this.time.length === 2) {
          const [start, end] = this.time;
          body.startTime = start;
          body.endTime = end;
        }
        if (this.status) {
          body.status = this.status;
        }
        if (this.accountNo) {
          body.accountNo = this.accountNo;
        }
        if (this.depoCode) {
          body.depoCode = this.depoCode;
        }

        const { err, res } = await this.$apis.pingan.getTradeSubjectRecords(
          body,
        );
        if (!err) {
          const { total, records } = res;
          this.m_total = total;
          this.list = records || [];
        }
        this.loading = false;
      },
      onSearchClick() {
        if (this.time == null || this.time.length === 0) {
          this.errMsg = '请选择交易时间';
          return;
        } else {
          this.errMsg = '';
        }

        this.current = 1;
        this.m_loadData();
      },
      clearSelect() {
        this.selectList = [];
        this.$refs.table && this.$refs.table.clearSelection();
      },
      onResplitClick() {
        if (this.selectList.length > 0) {
          const list = this.selectList.map(({ orderNo }) => orderNo);
          this.resplit(list);
        }
      },
      async resplit(list) {
        this.resplitLoading = true;
        const body = {
          orderNoList: list,
        };
        const { err, res } = await this.$apis.pingan.restartTradeSubjectRecords(
          body,
        );
        if (!err) {
          this.$message.success('操作成功');
          this.clearSelect();
          this.current = 1;
          this.m_loadData();
        }
        this.resplitLoading = false;
      },
      onRowClick(row, index) {},
      handleSelectionChange(list) {
        this.selectList = list;
      },
      calcRowSelectable(row, index) {
        return row.statusKey == 'F';
      },
    },
  };
</script>
