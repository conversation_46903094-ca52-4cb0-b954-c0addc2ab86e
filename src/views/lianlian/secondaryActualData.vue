<!--
 * @Author: 七七
 * @Date: 2022-04-07 18:20:51
 * @LastEditors: 七七
 * @LastEditTime: 2022-04-25 16:00:32
 * @FilePath: /access-fmis-web/src/views/pingan/secondaryActualData.vue
-->
// 二级实际数据
<template>
  <div>
    <el-form :model="search" inline>
      <el-form-item label="贸易主体">
        <el-input
          v-model="search.tradeBodyName"
          placeholder="请输入贸易主体名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="服务主体">
        <el-input
          v-model="search.servicePrincipalName"
          placeholder="请输入服务主体名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="费用类型">
        <el-select v-model="search.feeType" clearable>
          <el-option
            v-for="item in feeTypeList"
            :key="item.dictValue"
            :value="item.dictValue"
            :label="item.dictDesc"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="search.date"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          clearable
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="search.auditStatus" clearable>
          <el-option
            v-for="item in auditStatusList"
            :key="item.dictValue"
            :value="item.dictValue"
            :label="item.dictDesc"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="reset">重置</el-button>
        <el-button @click="downloadTemplate">导入模板下载</el-button>
        <uoloadFile
          style="margin-left: 10px; display: initial"
          accept=".xlsx, .xls"
          btn-text="导入"
          :upload-data="null"
          :disabled="uploading"
          upload-url="/api/split-service/serviceFeeUpload/fileTemplateUpload"
          @onSuccess="onSuccess"
          @beforeUpload="beforeUpload"
        ></uoloadFile>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          v-if="scope.row.auditStatus === 0"
          slot="reference"
          btn-text="通过"
          type="text"
          size="small"
          @click="handleExamine(true, scope.row)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="scope.row.auditStatus === 0"
          slot="reference"
          btn-text="拒绝"
          type="text"
          size="small"
          permission-key=""
          @click="handleExamine(false, scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <el-dialog
      title="二次确认"
      :visible.sync="confirmDialog"
      width="40%"
      :destroy-on-close="true"
    >
      <el-form ref="confirmForm" :model="confirmData" :rules="rules">
        <div>{{ '确认该笔数据审核' + examineText }}？</div>
        <el-form-item v-if="examineText === '通过'" label="备注" prop="remark">
          <el-input
            v-model="confirmData.remark"
            type="textarea"
            :rows="4"
          ></el-input>
        </el-form-item>
        <el-form-item
          v-if="examineText === '拒绝'"
          label="拒绝理由"
          prop="reason"
        >
          <el-input
            v-model="confirmData.reason"
            type="textarea"
            :rows="4"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleAudit">确认</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    secondaryActualDataMerge,
    secondaryActualList,
    secondaryDataDownloadTemplate,
    secondaryOption,
  } from '@/api/pingan';
  import dayjs from 'dayjs';
  import uoloadFile from '@/components/uoloadFile';

  export default {
    components: { dynamictable, uoloadFile },
    data() {
      return {
        search: {
          date: [],
        },
        searchCopy: {},
        list: [],
        auditStatusList: [],
        feeTypeList: [],
        uploading: false,
        options: {
          loading: false,
          border: true,

          // showSummary: true,
        },
        confirmDialog: false,
        confirmData: {
          remark: '',
          reason: '',
        },
        examineText: '',
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        rules: {
          reason: [
            { required: true, message: '请填写拒绝理由', trigger: 'blur' },
          ],
        },
        columns: [
          {
            prop: 'id',
            label: 'ID',
          },
          {
            prop: 'billDate',
            label: '账期（月）',
          },
          {
            prop: 'tradeBodyName',
            label: '贸易主体',
          },
          {
            prop: 'servicePrincipalName',
            label: '服务主体',
          },
          {
            prop: 'feeTypeDesc',
            label: '费用类型',
          },
          {
            prop: 'feeAmount',
            label: '应付金额',
          },
          {
            prop: 'createTime',
            label: '创建时间',
          },
          {
            prop: 'auditRemark',
            label: '备注',
          },
          {
            prop: 'auditStatus',
            label: '状态',
            render: ({ auditStatus }) => (
              <span>
                {auditStatus === 0
                  ? '待审核'
                  : auditStatus === 1
                  ? '审核拒绝'
                  : '审核成功'}
              </span>
            ),
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '200',
            scopedSlots: { customRender: 'operation' },
          },
        ],
      };
    },
    mounted() {
      const endTime = dayjs();
      const startTime = endTime.subtract(1, 'month');
      this.search.date = [
        startTime.format('YYYY-MM-DD 00:00:00'),
        endTime.format('YYYY-MM-DD 23:59:59'),
      ];
      this.getOptionList();
      this.getList();
    },
    methods: {
      async getOptionList() {
        try {
          const { res } = await secondaryOption();
          if (res) {
            this.feeTypeList = res.feeTypeList ? res.feeTypeList : [];
            this.auditStatusList = res.auditStatusList
              ? res.auditStatusList
              : [];
          }
        } catch (e) {}
      },
      getParams() {
        const body = {
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        if (this.searchCopy.date?.length > 0) {
          const startTime = this.searchCopy.date[0];
          const endTime = this.searchCopy.date[1];
          body.beginDate =
            startTime.length === 10 ? startTime + ' 00:00:00' : startTime;
          body.endDate =
            endTime.length === 10 ? endTime + ' 23:59:59' : endTime;
        }
        if (this.searchCopy.tradeBodyName?.length > 0) {
          body.tradeBodyName = this.searchCopy.tradeBodyName;
        }
        if (this.searchCopy.servicePrincipalName?.length > 0) {
          body.servicePrincipalName = this.searchCopy.servicePrincipalName;
        }
        if (this.searchCopy.feeType?.length > 0) {
          body.feeType = Number(this.searchCopy.feeType);
        }
        if (this.searchCopy.auditStatus?.length > 0) {
          body.auditStatus = Number(this.searchCopy.auditStatus);
        }
        return body;
      },
      async getList() {
        this.options.loading = true;
        this.searchCopy = { ...this.search };
        const body = this.getParams();
        try {
          const { res, err } = await secondaryActualList(body);
          if (res && !err) {
            this.list = res ? res.records : [];
            this.pagination.total = res ? res.total : 0;
          }
        } catch (e) {}
        this.options.loading = false;
      },
      handleSearch() {
        this.pagination.pageSize = 1;
        this.getList();
      },
      reset() {
        this.search = {
          date: [],
        };
        const endTime = dayjs();
        const startTime = endTime.subtract(1, 'month');
        this.search.date = [
          startTime.format('YYYY-MM-DD HH:mm:ss'),
          endTime.format('YYYY-MM-DD HH:mm:ss'),
        ];
      },
      handleExamine(val, row) {
        this.auditId = row.id;
        if (val) {
          this.examineText = '通过';
        } else {
          this.examineText = '拒绝';
        }
        this.confirmDialog = true;
      },

      async handleAudit() {
        this.$refs['confirmForm'].validate(async valid => {
          if (!valid) {
            return false;
          }
          const body = {
            auditStatus: this.examineText === '通过' ? 2 : 1,
            id: this.auditId,
            auditRemark:
              this.examineText === '通过'
                ? this.confirmData.remark
                : this.confirmData.reason,
          };
          try {
            const { res, err } = await secondaryActualDataMerge(body);
            console.log('getParams', this.confirmData);
            if (!err) {
              this.confirmDialog = false;
              this.confirmData = {
                remark: '',
                reason: '',
              };
              this.$message.success('操作成功');
              this.getList();
            }
          } catch (e) {
            console.log(e);
          }
        });
      },
      handleCancel() {
        this.confirmDialog = false;
        this.confirmData = {
          remark: '',
          reason: '',
        };
        this.$refs['confirmForm'].clearValidate();
      },
      async downloadTemplate() {
        try {
          const { res } = await secondaryDataDownloadTemplate();
          if (res) {
            window.open(res, '_blank');
          }
        } catch (e) {}
      },
      async onSuccess(e) {
        if (!e.success) {
          this.$message.error(e.msg || '导入失败');
          this.uploading = false;
          return;
        }
        this.uploading = false;
        this.$message.success('导入成功');
        this.getList();
      },
      beforeUpload(e) {
        this.uploading = true;
      },
    },
  };
</script>
<style scoped></style>
