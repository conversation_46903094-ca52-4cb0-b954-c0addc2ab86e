<template lang="pug">
.vertical.top.vcenter.padding-bottom-10
  el-form(inline label-suffix=':')
    el-form-item(label='发货仓')
      el-select(
        v-model='depoCode'
        placeholder='请选择'
        style='width: 200px'
        :loading='optionLoading'
        clearable
      )
        el-option(
          v-for='item in depoList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-form-item(label='经销商等级')
      el-select(
        v-model='custLevel'
        placeholder='请选择'
        style='width: 200px'
        :loading='optionLoading'
        clearable
      )
        el-option(
          v-for='item in custLevelList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-form-item(label='收款子账户')
      el-select(
        v-model='accountId'
        placeholder='请选择'
        style='width: 200px'
        :loading='optionLoading'
        clearable
        filterable
      )
        el-option(
          v-for='item in subAccountList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-button.margin-left-10(
      type='primary'
      icon='el-icon-search'
      @click='onSearchClick'
    ) 查询
  el-table.margin-top-20(
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
  )
    el-table-column(prop='depoName' label='发货仓' width='100')
    el-table-column(prop='accountName' label='收款子账户')
    el-table-column(prop='custLevelDesc' label='经销商等级' width='100')
    el-table-column(prop='percent' label='订单百分比' width='100')
    el-table-column(prop='remark' label='备注')
    el-table-column(label='操作' width='100')
      template(v-slot='{ $index, row }')
        ac-permission-button(
          type='text'
          btn-text='修改'
          permission-key='tradeConfig-edit'
          @click='onRowClick(row, $index)'
        )
        ac-permission-button.margin-left-10(
          title='删除后无法撤销，确认删除么？'
          nodeType='popconfirm'
          btn-text='删除'
          type='text'
          slotBtn='reference'
          permission-key='tradeConfig-delete'
          confirm-button-text='删除'
          confirm-button-type='danger'
          @click='onRowDeleteClick(row, $index)'
        )
          //- ac-permission-button(
          //-   slot='reference'
          //-   type="text"
          //-   btn-text="删除"
          //-   permission-key="tradeConfig-delete" 
          //- ) 
  el-pagination(
    :current-page='m_current'
    :page-sizes='[10, 15, 20, 30]'
    :page-size='m_pageSize'
    layout='total, sizes, prev, pager, next, jumper'
    :total='m_total'
    background
    @size-change='m_handleSizeChange'
    @current-change='m_handleCurrentChange'
  )
  trade-config-dialog(
    v-model='showDialog'
    :record='currentRecord'
    :depo-list='depoList'
    :cust-level-list='custLevelList'
    :sub-account-list='subAccountList'
    @success='onConfigSuccess'
  )
</template>
<script>
  import { paginationMixin } from '@/mixins/tableMixin';
  import TradeConfigDialog from './components/TradeConfigDialog';

  export default {
    components: {
      TradeConfigDialog,
    },
    mixins: [paginationMixin],
    data() {
      return {
        loading: false,
        optionLoading: false,
        list: [],
        showDialog: false,

        depoList: [],
        custLevelList: [],
        subAccountList: [],

        depoCode: null,
        custLevel: null,
        accountId: null,

        currentRecord: {},
      };
    },
    async created() {
      await this.loadOptions();
      await this.m_loadData();
    },
    methods: {
      async loadOptions() {
        this.optionLoading = true;
        const { err, res } = await this.$apis.pingan.getTradeSubjectOptions();
        if (!err) {
          const { custLevelList, depoList, subAccountList } = res;
          this.subAccountList = subAccountList;
          this.custLevelList = custLevelList;
          this.depoList = depoList;
        }
        this.optionLoading = false;
      },
      async m_loadData() {
        this.loading = true;
        const body = {
          size: this.m_pageSize,
          current: this.m_current,
        };
        if (this.accountId) {
          body.accountId = this.accountId;
        }
        if (this.custLevel) {
          body.custLevel = this.custLevel;
        }
        if (this.depoCode) {
          body.depoCode = this.depoCode;
        }

        const { res, err } = await this.$apis.pingan.getTradeSubjectConfigList(
          body,
        );
        if (!err) {
          const { list, total } = res;
          this.m_total = total;
          this.list = list;
        }
        this.loading = false;
      },
      onSearchClick() {
        this.m_current = 1;
        this.m_loadData();
      },
      onRowClick(row, index) {
        this.currentRecord = row;
        this.showDialog = true;
      },
      onConfigSuccess() {
        this.m_current = 1;
        this.m_loadData();
      },
      onRowDeleteClick(row, index) {
        this.deleteRecord(row.id);
      },
      async deleteRecord(id) {
        this.loading = true;
        const { res, err } = await this.$apis.pingan.deleteTradeSubjectConfig(
          id,
        );
        if (!err) {
          this.$message.success('操作成功');
          this.m_current = 1;
          this.m_loadData();
        }
        this.loading = false;
      },
    },
  };
</script>
