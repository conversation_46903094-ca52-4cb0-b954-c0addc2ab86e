<template>
  <div>
    <el-form inline>
      <el-form-item label="交易日期:">
        <el-date-picker
          v-model="tradeDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="付款子账户:">
        <el-select
          v-model="searchParams.payerAccountNo"
          clearable
          placeholder="请选择资金类型"
        >
          <el-option
            v-for="item in payerAccountList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态:">
        <el-select
          v-model="searchParams.status"
          clearable
          placeholder="请选择审核状态"
        >
          <el-option
            v-for="item in fundApplyStatusList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="操作日志"
          permission-key=""
          @click="auditOperationLog(scope.row)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="['AUDIT', 'FINAL_AUDIT'].includes(scope.row.status)"
          slot="reference"
          type="text"
          size="small"
          btn-text="通过"
          permission-key=""
          @click="handleOperate(scope.row, 1)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="['AUDIT', 'FINAL_AUDIT'].includes(scope.row.status)"
          slot="reference"
          type="text"
          size="small"
          btn-text="拒绝"
          permission-key=""
          @click="handleOperate(scope.row, 2)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import { parseTime } from '@/utils';
  import {
    auditFinal,
    auditOper,
    fundSelector,
    fundCrossPayoutPage,
  } from '@/api/lianlian';

  export default {
    name: 'LianlianCrossOverseaPayReviewMgt',
    components: {
      dynamictable,
    },

    data() {
      let columns = [
        {
          prop: 'id',
          label: 'ID',
        },
        {
          prop: 'payerCustName',
          label: '付款子账户',
          render: ({ payerCustName, payerAccountType, payerAccountNo }) => (
            <span>
              {payerCustName}-{payerAccountType}-{payerAccountNo}
            </span>
          ),
        },
        {
          prop: 'applyType',
          label: '操作类型',
        },
        {
          prop: 'amount',
          label: '交易金额',
        },
        {
          prop: 'feeAmount',
          label: '手续费',
        },
        {
          prop: 'cardNo',
          label: '账号',
        },
        {
          prop: 'createTime',
          label: '交易时间',
        },
        {
          prop: 'statusDesc',
          label: '状态',
        },
        {
          prop: 'summary',
          label: '操作备注',
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '100',
          scopedSlots: { customRender: 'operation' },
        },
      ];

      return {
        tradeDate: '',
        searchParams: {
          // payerAccountNo: '****************',
          // status: 'AUDIT',
          // startTime: '2021-01-01',
          // endTime: '2022-03-01',
          payerAccountNo: '',
          status: '',
          startTime: '',
          endTime: '',
        },
        fundApplyStatusList: [], // 状态列表
        payerAccountList: [], // 付款子账户列表
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          mutiSelect: false,
        },
        columns,
      };
    },
    async created() {
      await this.loadOptions();
      this.getList(true);
    },
    methods: {
      auditOperationLog(row) {
        this.$router.push({
          path: 'auditOperationLog',
          query: {
            id: row.id,
          },
        });
      },
      async loadOptions() {
        const res = await fundSelector({
          channel: 'lianlian',
        });
        this.fundApplyStatusList = res.fundApplyStatusList;
        this.payerAccountList = res.accountList;
      },

      async saveOperateApi(params, row) {
        const apis = row.status === 'AUDIT' ? auditOper : auditFinal;
        const res = await apis(params);
        if (res) {
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          this.getList();
        }
      },

      handleOperate(row, type) {
        if (type === 2) {
          this.$prompt('拒绝理由', '二次确认', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputType: 'textarea',
            inputValidator: value => {
              if (!value) {
                return '拒绝原因不能为空！';
              }
            },
          })
            .then(({ value }) => {
              this.saveOperateApi(
                {
                  id: row.id,
                  auditType: 2,
                  remark: value,
                },
                row,
              );
            })
            .catch(() => {});
          return;
        }
        this.$confirm('确认同意该操作?', '二次确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.saveOperateApi(
              {
                id: row.id,
                auditType: 1,
              },
              row,
            );
          })
          .catch(() => {});
      },
      getParams() {
        const tradeDate = this.tradeDate;
        this.searchParams.startTime = tradeDate
          ? parseTime(tradeDate[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.endTime = tradeDate
          ? parseTime(tradeDate[1], '{y}-{m}-{d}')
          : '';
        const params = {
          ...this.searchParams,
          channel: 'lianlian',
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await fundCrossPayoutPage(params);
        this.options.loading = false;
        this.list = res ? res.records : [];
        this.pagination.total = res ? res.total : 0;
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.tradeDate = '';
      },
    },
  };
</script>
<style lang="scss"></style>
