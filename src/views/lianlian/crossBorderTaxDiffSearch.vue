<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-18 16:56:46
 * @LastEditTime: 2022-06-23 16:57:11
 * @LastEditors: xuxiang
 * @Reference: 
-->
<template>
  <div style="height: 100%">
    <el-row
      :style="{ width: '100%' }"
      :gutter="0"
      :type="''"
      :align="''"
      :justify="'start'"
    >
      <el-col :span="24" :offset="0" :push="0" :pull="0">
        <ac-data-page
          ref="myAcDataPageRef_16528628487788565"
          :style="{ width: '100%' }"
          :search="acDataPageSearch_16528628487788565"
          :table="acDataPageTable_16528628487788565"
          :add="acDataPageAdd_16528628487788565"
          :edit="acDataPageEdit_16528628487788565"
          :before-open-add="beforeOpenAdd_16528628487788565"
          :before-open-edit="beforeOpenEdit_16528628487788565"
          @getTableData="getTableData_16528628487788565"
          @searchCustomClick="searchCustomClick_16528628487788565"
          @searchFormChange="searchFormChange_16528628487788565"
          @tableCustomClick="tableCustomClick_16528628487788565"
          @addCommit="addCommit_16528628487788565"
          @delTableData="delTableData_16528628487788565"
          @editCommit="editCommit_16528628487788565"
        ></ac-data-page>
      </el-col>
    </el-row>
  </div>
</template>
<script>
  import { crossBorderConsolidatedTaxPage } from '@/api/pingan';
  import { downloadFile, parseTime, setInitData } from '@/utils';
  import { newExportExcel } from '@/api/blob';

  export default {
    name: 'CrossBorderTaxDiffSearch',
    data() {
      return {
        // 数据页面新增表单配置
        acDataPageAdd_16528628487788565: { options: [] },
        // 数据页面编辑表单配置
        acDataPageEdit_16528628487788565: { options: [] },
        // 数据页面筛选栏表单配置
        acDataPageSearch_16528628487788565: {
          button: {
            config: { hidden: ['添加'], resetNotSearch: true },
            options: [{ key: 'export', label: '导出', type: '' }],
          },
          form: {
            options: [
              {
                key: 'billDate',
                type: 'daterange',
                label: '账期',
                clearable: false,
                width: 120,
                format: 'yyyy-MM-dd',
                valueFormat: 'yyyy-MM-dd',
                defaultValue: ['2022-05-05', '2022-05-06'],
              },
              {
                type: 'input',
                key: 'salesEntityName',
                label: '贸易主体',
                clearable: true,
                placeholder: '请输入贸易主体',
                color: '',
                options: [{ label: '', value: '' }],
                tokenKey: '',
              },
              {
                type: 'input',
                key: 'settleSubjectName',
                label: '服务主体',
                clearable: true,
                placeholder: '请输入服务主体',
                color: '',
                options: [{ label: '', value: '' }],
                tokenKey: '',
              },
              {
                type: 'select',
                key: 'resultStatus',
                label: '状态',
                clearable: true,
                placeholder: '请选择状态',
                color: '',
                options: [
                  { label: '未匹配', value: '0' },
                  { label: '部分匹配', value: '1' },
                  { label: '全部匹配', value: '2' },
                ],
                tokenKey: '',
              },
            ],
          },
        },
        // 数据页面表格列配置
        acDataPageTable_16528628487788565: {
          config: { hidden: ['编辑', '删除'] },
          options: [
            { prop: 'number', label: '序号' },
            { prop: 'billDate', label: '日期' },
            {
              prop: 'tradeBodyName',
              label: '贸易主体',
              type: '',
              clickKey: '',
            },
            {
              prop: 'servicePrincipalName',
              label: '服务主体',
              type: '',
              clickKey: '',
            },
            {
              prop: 'shippingWarehouseName',
              label: '发货仓',
              type: '',
              clickKey: '',
            },
            { prop: 'payFeeAmount', label: '应缴税费', type: '', clickKey: '' },
            {
              prop: 'realFeeAmount',
              label: '实付税费',
              type: '',
              clickKey: '',
            },
            { prop: 'diffAmount', label: '差异金额', type: '', clickKey: '' },
            { prop: 'resultStatusDesc', label: '状态', type: '', clickKey: '' },
            {
              type: 'operation',
              prop: 'operation',
              label: '操作',
              options: [
                {
                  key: 'details',
                  label: '查看',
                },
              ],
            },
          ],
        },
      };
    },
    computed: {},
    watch: {},
    created() {
      this.acDataPageSearch_16528628487788565.form.options[0].defaultValue = setInitData(
        30,
      );
      this.getTableData_16528628487788565();
    },
    mounted() {},
    methods: {
      // 数据页面-新增表单确认事件
      addCommit_16528628487788565(saveData, done) {
        // 内置新增弹窗确认事件
      },
      // 数据页面-获取表格数据
      async getTableData_16528628487788565(requestData) {
        // 点击搜索按钮获取数据事件
        this.$nextTick(async function () {
          const reqData =
            requestData ||
            this.$refs.myAcDataPageRef_16528628487788565?.GetRequestData();
          const body = this.getParams(reqData || {});

          const { err, res } = await crossBorderConsolidatedTaxPage(body);
          this.$refs.myAcDataPageRef_16528628487788565?.OverLoading();
          const list = (res?.records || []).map((item, index) => ({
            ...item,
            number: body.size * (body.current - 1) + (index + 1),
          }));
          console.log(list, 'list');
          if (res && !err) {
            this.$refs.myAcDataPageRef_16528628487788565.SetTableData(
              list,
              res.total,
            );
          }
        });
      },
      // 数据页面-筛选栏自定义按钮点击事件
      searchCustomClick_16528628487788565(key) {
        // 自定义筛选栏按钮点击事件
        if (key === 'export') {
          const reqData = this.$refs.myAcDataPageRef_16528628487788565.GetRequestData();
          const { size, current, ...params } = this.getParams(reqData || {});
          newExportExcel(
            { ...params },
            '/api/split-service/crossBorderConsolidatedTax/daySumExport',
            'post',
          ).then(res => {
            if (res) {
              downloadFile(res.data, '跨境综合税差异列表');
            }
          });
        }
      },
      // 数据页面-筛选栏表单值改变触发
      searchFormChange_16528628487788565(obj) {
        // 筛选栏表单项值改变事件
        const { key, value } = obj;
        console.log(key);
        console.log(value);
      },
      // 数据页面-表格自定义点击事件
      tableCustomClick_16528628487788565(key, row) {
        // 表格自定义点击事件
        console.log(key);
        if (key === 'details') {
          this.$router.push({
            path: 'crossBorderTaxDiffDetails',
            query: {
              id: row.id,
            },
          });
        }
      },
      // 数据页面-打开新增表单拦截
      beforeOpenAdd_16528628487788565(done) {
        // 新建数据确认拦截
        done();
      },
      // 数据页面-打开编辑表单拦截
      beforeOpenEdit_16528628487788565(done, row) {
        // 编辑数据确认拦截
        console.log(row);
        done(row);
      },
      // 数据页面-表格删除单条数据触发
      delTableData_16528628487788565(row, index, done) {
        // 表格行删除事件
        console.log(row);
      },
      // 数据页面-编辑表单确认事件
      editCommit_16528628487788565(saveData, done) {
        // 内置编辑弹窗确认事件
      },
      getParams(body) {
        const {
          billDate = [],
          currentPage = 1,
          pageSize = 1,
          ...params
        } = body;
        console.log(billDate, 'billDate');

        const query = {
          ...params,
          beginDate: parseTime(billDate[0], '{y}-{m}-{d}'),
          endDate: parseTime(billDate[1], '{y}-{m}-{d}'),
          current: currentPage,
          size: pageSize,
        };
        return query;
      },
    },
  };
</script>
<style scoped></style>
