<!--
 * @Description: 
 * @Author: xuxiang
 * @Date: 2022-05-19 11:11:09
 * @LastEditTime: 2022-05-24 18:44:26
 * @LastEditors: xuxiang
 * @Reference: 
-->
<template>
  <div style="height: 100%">
    <el-row
      :style="{ width: '100%' }"
      :gutter="0"
      :type="''"
      :align="''"
      :justify="'start'"
    >
      <el-col :span="24" :offset="0" :push="0" :pull="0">
        <ac-data-page
          ref="myAcDataPageRef_16529284015035035"
          :style="{ width: '100%' }"
          :search="acDataPageSearch_16529284015035035"
          :table="acDataPageTable_16529284015035035"
          :add="acDataPageAdd_16529284015035035"
          :edit="acDataPageEdit_16529284015035035"
          :before-open-add="beforeOpenAdd_16529284015035035"
          :before-open-edit="beforeOpenEdit_16529284015035035"
          @getTableData="getTableData_16529284015035035"
          @searchCustomClick="searchCustomClick_16529284015035035"
          @searchFormChange="searchFormChange_16529284015035035"
          @tableCustomClick="tableCustomClick_16529284015035035"
          @addCommit="addCommit_16529284015035035"
          @delTableData="delTableData_16529284015035035"
          @editCommit="editCommit_16529284015035035"
        ></ac-data-page>
      </el-col>
    </el-row>
  </div>
</template>
<script>
  import { crossBorderConsolidatedTaxDetailPage } from '@/api/pingan';

  export default {
    data() {
      return {
        // 数据页面新增表单配置
        acDataPageAdd_16529284015035035: { options: [] },
        // 数据页面编辑表单配置
        acDataPageEdit_16529284015035035: { options: [] },
        // 数据页面筛选栏表单配置
        acDataPageSearch_16529284015035035: {
          button: {
            config: { hidden: ['添加'], resetNotSearch: true },
            options: [],
          },
          form: {
            options: [
              {
                key: 'originSalesNo',
                type: 'input',
                label: '销售订单',
                clearable: true,
                placeholder: '请输入销售订单',
              },
              {
                type: 'input',
                key: 'pushSn',
                label: '履约单号',
                clearable: true,
                placeholder: '请输入履约单号',
                color: '',
                options: [{ label: '', value: '' }],
                tokenKey: '',
              },
              {
                type: 'select',
                key: 'resultStatus',
                label: '状态',
                clearable: true,
                placeholder: '请选择',
                color: '',
                options: [
                  { label: '未匹配', value: '0' },
                  { label: '已匹配', value: '1' },
                ],
                tokenKey: '',
              },
            ],
          },
        },
        // 数据页面表格列配置
        acDataPageTable_16529284015035035: {
          config: { hidden: ['编辑', '删除'] },
          options: [
            { prop: 'id', label: '序号' },
            { prop: 'billDate', label: '账期' },
            {
              prop: 'originSalesNo',
              label: '销售订单号',
              type: '',
              clickKey: '',
            },
            { prop: 'pushSn', label: '履约单号', type: '', clickKey: '' },
            {
              prop: 'tradeBodyName',
              label: '贸易主体',
              type: '',
              clickKey: '',
            },
            {
              prop: 'shippingWarehouseName',
              label: '发货仓库',
              type: '',
              clickKey: '',
            },
            {
              prop: 'orderTypeDesc',
              label: '交易类型',
              type: '',
              clickKey: '',
            },
            { prop: 'feeAmount', label: '实收税费', type: '', clickKey: '' },
            { prop: 'taxBillNo', label: '缴税书编号', type: '', clickKey: '' },
            {
              prop: 'realPayTaxFee',
              label: '实付税费',
              type: '',
              clickKey: '',
            },
            { prop: 'diffAmount', label: '差异金额', type: '', clickKey: '' },
            {
              prop: 'paySuccessTime',
              label: '实际实付时间',
              type: '',
              clickKey: '',
            },
            { prop: 'resultStatusDesc', label: '状态', type: '', clickKey: '' },
          ],
        },
      };
    },
    computed: {},
    watch: {},
    created() {},
    mounted() {
      this.getTableData_16529284015035035();
    },
    methods: {
      // 数据页面-新增表单确认事件
      addCommit_16529284015035035(saveData, done) {
        // 内置新增弹窗确认事件
      },
      // 数据页面-获取表格数据
      async getTableData_16529284015035035(requestData) {
        // 点击搜索按钮获取数据事件
        const reqData =
          requestData ||
          this.$refs.myAcDataPageRef_16529284015035035.GetRequestData();
        const body = this.getParams(reqData || {});

        const { err, res } = await crossBorderConsolidatedTaxDetailPage(body);
        this.$refs.myAcDataPageRef_16529284015035035.OverLoading();
        if (res && !err) {
          this.$refs.myAcDataPageRef_16529284015035035.SetTableData(
            res.records,
            res.total,
          );
        }
      },
      // 数据页面-筛选栏自定义按钮点击事件
      searchCustomClick_16529284015035035(key) {
        // 自定义筛选栏按钮点击事件
        console.log(key);
      },
      // 数据页面-筛选栏表单值改变触发
      searchFormChange_16529284015035035(obj) {
        // 筛选栏表单项值改变事件
        const { key, value } = obj;
        console.log(key);
        console.log(value);
      },
      // 数据页面-表格自定义点击事件
      tableCustomClick_16529284015035035(key, row) {
        // 表格自定义点击事件
        console.log(key);
        console.log(row);
      },
      // 数据页面-打开新增表单拦截
      beforeOpenAdd_16529284015035035(done) {
        // 新建数据确认拦截
        done();
      },
      // 数据页面-打开编辑表单拦截
      beforeOpenEdit_16529284015035035(done, row) {
        // 编辑数据确认拦截
        console.log(row);
        done(row);
      },
      // 数据页面-表格删除单条数据触发
      delTableData_16529284015035035(row, index, done) {
        // 表格行删除事件
        console.log(row);
      },
      // 数据页面-编辑表单确认事件
      editCommit_16529284015035035(saveData, done) {
        // 内置编辑弹窗确认事件
      },
      getParams(body) {
        const { id } = this.$route.query;

        const { currentPage = 1, pageSize = 1, ...params } = body;

        const query = {
          ...params,
          id,
          current: currentPage,
          size: pageSize,
        };
        return query;
      },
    },
  };
</script>
<style scoped></style>
