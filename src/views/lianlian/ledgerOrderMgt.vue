<template>
  <div>
    <el-form inline :model="searchParams">
      <el-form-item label="日期:">
        <el-date-picker
          v-model="billDate"
          :type="activeName === '1' ? 'daterange' : 'monthrange'"
          range-separator="至"
          :value-format="activeName === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
          :format="activeName === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="资金主体:">
        <el-input
          v-model="searchParams.settleSubjectName"
          placeholder="请输入资金主体"
        />
      </el-form-item>
      <el-form-item label="贸易主体:">
        <el-input
          v-model="searchParams.salesEntityName"
          placeholder="请输入贸易主体"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-popover placement="bottom" trigger="click">
          <p>请选择导出时间</p>
          <el-date-picker
            v-model="exportDate"
            :clearable="false"
            :type="activeName === '1' ? 'daterange' : 'monthrange'"
            range-separator="至"
            :value-format="activeName === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
            :format="activeName === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :picker-options="pickerOptions"
          ></el-date-picker>
          <div style="text-align: right; margin: 10px 0 0">
            <el-button type="primary" size="mini" @click="onExport">
              确定
            </el-button>
          </div>
          <el-button slot="reference" style="margin-left: 10px" type="primary">
            导出
          </el-button>
        </el-popover>
        <!-- <el-button type="primary" @click="onExport">导出</el-button> -->
        <!-- <el-button type="primary" @click="onReset">下载导入模板</el-button>
        <el-button type="primary" @click="showDialog = true">
          导入调整单
        </el-button> -->
      </el-form-item>
    </el-form>
    <el-tabs v-model="activeName" type="card" @tab-click="handleTabClick">
      <el-tab-pane label="日汇总" name="1"></el-tab-pane>
      <el-tab-pane label="月汇总" name="2"></el-tab-pane>
    </el-tabs>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="明细"
          permission-key=""
          @click="go(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <ImportAdjustmentDialog v-model="showDialog"></ImportAdjustmentDialog>
    <secondaryDetails
      v-model="showSecondary"
      :secondary-item="secondaryItem"
    ></secondaryDetails>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import ImportAdjustmentDialog from './components/ImportAdjustmentDialog';
  import secondaryDetails from './components/secondaryDetails.vue';
  import {
    firstSplitStatsDaySum,
    firstSplitStatsMonthSum,
    secondaryItemStatistics,
  } from '@/api/pingan';
  import { newExportExcel } from '@/api/blob';
  import { debounce, downloadFile, parseTime, setInitData } from '@/utils';

  export default {
    name: 'LedgerOrderMgt',
    components: {
      dynamictable,
      ImportAdjustmentDialog,
      secondaryDetails,
    },
    data() {
      return {
        billDate: '',
        activeName: '1',
        showDialog: false,
        searchParams: {
          settleSubjectName: '',
          salesEntityName: '',
          beginDate: '',
          endDate: '',
        },
        list: [],
        secondaryItem: [],
        showSecondary: false,
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          index: true,
          indexName: 'ID',
        },
        exportDate: '', // 导出时间
        choiceDate0: '',
        pickerOptions: {
          onPick: ({ maxDate, minDate }) => {
            this.choiceDate0 = minDate.getTime();
            if (maxDate) {
              this.choiceDate0 = '';
            }
          },
          disabledDate: time => {
            if (this.choiceDate0 !== '') {
              const one = 30 * 24 * 3600 * 1000;
              const minTime = this.choiceDate0 - one;
              const maxTime = this.choiceDate0 + one;
              return time.getTime() < minTime || time.getTime() > maxTime;
            }
          },
        },
      };
    },
    created() {
      this.billDate = setInitData(30);
      this.exportDate = setInitData(30);
      this.getList();
    },
    methods: {
      async showPopover(row) {
        const { activeName } = this;
        let query = {
          tradeSubjectAccountNo: row.salesEntityCode,
        };
        if (activeName === '1') {
          query.day = row.billDate;
        } else {
          query.moon = row.billDate;
        }

        const { err, res } = await secondaryItemStatistics(query);

        if (res && !err) {
          this.secondaryItem = [res];
        }
      },
      go(row) {
        const { activeName } = this;
        let query = {
          salesEntityCode: row.salesEntityCode,
          settleSubjectCode: row.settleSubjectCode,
        };
        if (activeName === '1') {
          query.day = row.billDate;
        } else {
          query.month = row.billDate;
        }
        this.$router.push({
          path: 'ledgerOrderDetails',
          query,
        });
      },
      handleTabClick() {
        this.getList(true);
      },
      getParams(type) {
        const activeName = this.activeName;
        const billDate = type === 'export' ? this.exportDate : this.billDate;
        this.searchParams.beginDate = billDate
          ? parseTime(
              billDate[0],
              activeName === '1' ? '{y}-{m}-{d}' : '{y}-{m}',
            )
          : '';
        this.searchParams.endDate = billDate
          ? parseTime(
              billDate[1],
              activeName === '1' ? '{y}-{m}-{d}' : '{y}-{m}',
            )
          : '';
        const params = {
          ...this.searchParams,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const { activeName } = this;
        const params = this.getParams();

        this.options.loading = true;
        const api =
          activeName === '1' ? firstSplitStatsDaySum : firstSplitStatsMonthSum;
        const { err, res } = await api(params);
        if (res && !err) {
          this.list = res ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        }
        this.options.loading = false;
      },
      onExport: debounce(function () {
        const params = this.getParams('export');
        const api =
          this.activeName === '1'
            ? '/api/split-service/firstSplitStats/daySumExport'
            : '/api/split-service/firstSplitStats/monthSumExport';
        newExportExcel({ ...params }, api, 'post').then(res => {
          if (res) {
            downloadFile(res.data, '一级分账列表');
          }
        });
      }, 5000),
      onReset() {
        const data = setInitData(30);
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.exportDate = data;
        this.billDate = data;
      },

      getColumns() {
        const columns = [
          {
            prop: 'billDate',
            label: '日期',
          },
          {
            prop: 'settleSubjectName',
            label: '资金主体',
          },
          {
            prop: 'salesEntityName',
            label: '贸易主体',
          },

          {
            prop: 'amount',
            label: '订单总金额',
            // render: row => (
            //   <a
            //     style="cursor:pointer"
            //     onClick={() => {
            //       this.secondaryItem = [];
            //       this.showSecondary = true;
            //       this.showPopover(row);
            //     }}
            //   >
            //     {row.amount}
            //   </a>
            // ),
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '200',
            scopedSlots: { customRender: 'operation' },
          },
        ];
        return columns;
      },
    },
  };
</script>
<style lang="scss"></style>
