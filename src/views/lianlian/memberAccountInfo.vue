<template lang="pug">
.vertical.top.vcenter.padding-bottom-10
  el-form(inline :model='searchForm' :rules='rules' label-suffix=':')
    el-form-item(label='子账户名' prop='custId')
      el-select(
        v-model='searchForm.custId'
        placeholder='请选择'
        style='width: 200px'
        :loading='optionLoading'
        clearable
        filterable
      )
        el-option(
          v-for='item in accountList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-form-item(label='二级账户类型' prop='accountType')
      el-select(
        v-model='searchForm.accountType'
        placeholder='请选择账户类型'
        style='width: 200px'
        :loading='optionLoading'
      )
        el-option(
          v-for='item in typeOptions'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-form-item(label='银行账号' prop='cardNo')
      el-input(v-model='searchForm.cardNo' placeholder='请输入银行账号' clearable)
    el-button.margin-left-10(
      type='primary'
      icon='el-icon-search'
      @click='onSearchClick'
      :disabled='!searchEnable'
    ) 查询
  ac-tag-value(
    tag='CNY汇总账户余额'
    :value='summaryAccountBal'
    width='150px'
    empty-text='无数据'
  )
  ac-tag-value.margin-top-10(
    tag='会员子账户总额'
    :value='memberSubAccountTotalBal'
    width='150px'
    empty-text='无数据'
  )
  el-table.margin-top-20(
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
  )
    el-table-column(prop='openTime' label='开户时间' width='170')
    el-table-column(prop='accountNo' label='子账户ID' width='160')
    el-table-column(prop='accountName' label='子账户名')
    el-table-column(prop='accountTypeDesc' label='二级账户类型' width='110')
    el-table-column(prop='bankName' label='提现银行')
    el-table-column(prop='cardNo' label='提现银行卡号' width='140')
    el-table-column(prop='availableBal' label='子账户余额')
    el-table-column(prop='canCashBal' label='实际可用余额')
    el-table-column(prop='sourceType' label='操作' width='80')
      template(v-slot='{ $index, row }')
        ac-permission-button(
          btn-text='提现'
          permission-key='memberAccountInfo-reflect'
          @click='onRowClick(row, $index)'
        )
  el-pagination(
    :current-page='m_current'
    :page-sizes='[10, 15, 20, 30]'
    :page-size='m_pageSize'
    layout='total, sizes, prev, pager, next, jumper'
    :total='m_total'
    background
    @size-change='m_handleSizeChange'
    @current-change='m_handleCurrentChange'
  )
  account-withdrawal-dialog(
    v-model='showDialog'
    :record='currentRecord'
    @success='handleWithdrawalSuccess'
  )
</template>
<script>
  import { paginationMixin } from '@/mixins/tableMixin';
  import AccountWithdrawalDialog from './components/AccountWithdrawalDialog';

  export default {
    components: {
      AccountWithdrawalDialog,
    },
    mixins: [paginationMixin],
    data() {
      return {
        loading: false,
        optionLoading: false,
        showDialog: false,

        currentRecord: {},

        accountList: [],
        searchForm: {
          custId: null,
          accountType: null,
          cardNo: '',
        },
        typeOptions: [],
        list: [],
        rules: {
          accountType: [
            {
              required: true,
              message: '请选择账户类型',
              trigger: 'change',
            },
          ],
        },
        summaryAccountBal: null,
        memberSubAccountTotalBal: null,
      };
    },
    computed: {
      searchEnable() {
        return this.searchForm.accountType != null;
      },
    },
    created() {
      this.loadOptions();
    },
    methods: {
      async loadOptions() {
        this.optionLoading = true;
        const {
          err,
          res,
        } = await this.$apis.pingan.getMemberSubAccountOptions();
        if (!err) {
          const { memberSubAccountList, memberSubAccountTypeList } = res;
          this.typeOptions = memberSubAccountTypeList;
          this.accountList = memberSubAccountList;
        }
        this.optionLoading = false;
      },
      async m_loadData() {
        this.loading = true;
        const body = {
          size: this.m_pageSize,
          current: this.m_current,
          channel: 'pingan',
        };
        const { accountType, cardNo, custId } = this.searchForm;
        if (accountType != null) {
          body.accountType = accountType;
        }
        if (custId) {
          body.custId = custId;
        }
        if (cardNo) {
          body.cardNo = cardNo;
        }

        const { err, res } = await this.$apis.pingan.getMemberSubAccountList(
          body,
        );
        if (!err) {
          const {
            memberSubAccountTotalBal,
            summaryAccountBal,
            total,
            list,
          } = res;
          this.m_total = total;
          this.list = list;
          this.memberSubAccountTotalBal = memberSubAccountTotalBal;
          this.summaryAccountBal = summaryAccountBal;
        }
        this.loading = false;
      },
      onSearchClick() {
        this.m_loadData();
      },
      onRowClick(row, index) {
        this.currentRecord = row;
        this.showDialog = true;
      },
      m_handleSizeChange(size) {
        this.m_pageSize = size;
        if (tis.searchEnable) {
          this.m_loadData();
        }
      },
      handleWithdrawalSuccess() {
        this.m_loadData();
      },
    },
  };
</script>
