<!--
 * @Author: 七七
 * @Date: 2022-04-12 15:28:49
 * @LastEditors: xuxiang
 * @LastEditTime: 2022-05-24 17:51:21
 * @FilePath: /access-fmis-web/src/views/pingan/secondaryAdjustmentDifferenceDetails.vue
-->
<template>
  <div>
    <el-form :model="search" inline>
      <el-form-item v-if="adjustmentType === '1'" label="订单编号">
        <el-input v-model="search.salesNo" placeholder="请输入"></el-input>
      </el-form-item>
      <template v-if="adjustmentType === '2'">
        <el-form-item label="销售订单:">
          <el-input
            v-model="search.originSalesNo"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="履约单号:">
          <el-input v-model="search.pushSn" placeholder="请输入"></el-input>
        </el-form-item>
      </template>

      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    crossBorderConsolidatedTaxDetailsPage,
    secondaryAdjustmentDetail,
  } from '@/api/pingan';

  export default {
    components: { dynamictable },
    data() {
      return {
        serialNo: null,
        search: {},
        searchCopy: {},
        list: [],
        adjustmentType: '1',
        options: {
          loading: false,
          border: true,
          index: true,
          indexName: 'ID',
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
      };
    },
    created() {
      this.adjustmentType = this.$route.query.adjustmentType;
    },
    mounted() {
      if (this.$route.query && this.$route.query.id != null) {
        this.serialNo = this.$route.query.id;
        this.getList();
      }
    },
    methods: {
      handleSearch() {
        this.pagination.pageSize = 1;
        this.getList();
      },
      getParams() {
        const {
          pushSn = '',
          originSalesNo = '',
          adjustmentId = '',
          adjustmentType,
        } = this.$route.query;
        const body = {
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        if (adjustmentType === '2') {
          // body.originSalesNo = originSalesNo;
          // body.pushSn = pushSn;
          body.id = adjustmentId;
        } else {
          body.serialNo = this.serialNo;
        }
        if (this.searchCopy.salesNo?.length > 0) {
          body.salesNo = this.searchCopy.salesNo;
        }
        if (this.searchCopy.pushSn?.length > 0) {
          body.pushSn = this.searchCopy.pushSn;
        }
        if (this.searchCopy.originSalesNo?.length > 0) {
          body.originSalesNo = this.searchCopy.originSalesNo;
        }
        return body;
      },
      async getList() {
        this.options.loading = true;
        this.searchCopy = { ...this.search };
        const body = this.getParams();
        try {
          const api =
            this.adjustmentType === '2'
              ? crossBorderConsolidatedTaxDetailsPage
              : secondaryAdjustmentDetail;
          const { res, err } = await api(body);
          if (res && !err) {
            this.list = res ? res.records : [];
            this.pagination.total = res ? res.total : 0;
          }
        } catch (e) {}
        this.options.loading = false;
      },
      reset() {
        this.search = {};
        // this.handleSearch();
      },
      getColumns() {
        const adjustmentType = this.$route.query.adjustmentType;
        const columns1 = [
          {
            prop: 'salesNo',
            label: '订单编号',
          },
          {
            prop: 'outAccountName',
            label: '贸易主体',
          },
          {
            prop: 'orderType',
            label: '交易类型',
          },
          {
            prop: 'channelIdDesc',
            label: '支付渠道',
          },
          {
            prop: 'currency',
            label: '币种',
          },
          {
            prop: 'orderAmount',
            label: '实付金额',
          },
          {
            prop: 'payTime',
            label: '实际支付时间',
          },
          {
            prop: 'shippingType',
            label: '业务类型',
          },
          {
            prop: 'shippingWarehouseName',
            label: '发货仓库',
          },
          {
            prop: 'inAccountName',
            label: '结算主体',
          },
        ];

        const columns2 = [
          { prop: 'billDate', label: '账期' },
          {
            prop: 'originSalesNo',
            label: '销售订单号',
          },
          { prop: 'pushSn', label: '履约单号' },
          {
            prop: 'tradeBodyName',
            label: '贸易主体',
          },
          {
            prop: 'shippingWarehouseName',
            label: '发货仓库',
          },
          {
            prop: 'orderTypeDesc',
            label: '交易类型',
          },
          { prop: 'feeAmount', label: '实收税费' },
          { prop: 'taxBillNo', label: '缴税书编号' },
          {
            prop: 'realPayTaxFee',
            label: '实付税费',
          },
          { prop: 'diffAmount', label: '差异金额' },
          {
            prop: 'paySuccessTime',
            label: '实际实付时间',
          },
          { prop: 'resultStatusDesc', label: '状态' },
        ];
        return adjustmentType === '1' ? columns1 : columns2;
      },
    },
  };
</script>
<style scoped></style>
