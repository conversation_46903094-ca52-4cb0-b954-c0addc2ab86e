<template>
  <div>
    <el-form inline>
      <el-form-item label="挂账订单号:">
        <el-input
          v-model="searchParams.hangApplyNo"
          placeholder="请输入挂账订单号"
        />
      </el-form-item>
      <el-form-item label="状态:">
        <el-select v-model="searchParams.applyStatus" placeholder="请选择">
          <el-option
            v-for="item in applyStatusList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间:">
        <el-date-picker
          v-model="searchDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="挂账时间:">
        <el-date-picker
          v-model="searchDate1"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button type="primary" @click="showDialog = true">创建</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <el-button
          slot="reference"
          type="text"
          size="small"
          :disabled="
            ssoId == scope.row.creatorSsoId ? true : scope.row.applyStatus !== 1
          "
          @click="openModal(0, scope.row)"
        >
          审核并确认
        </el-button>
        <el-button
          slot="reference"
          type="text"
          size="small"
          :disabled="
            ssoId == scope.row.creatorSsoId ? true : scope.row.applyStatus !== 1
          "
          @click="openModal(1, scope.row)"
        >
          拒绝
        </el-button>
        <el-button
          slot="reference"
          type="text"
          size="small"
          :disabled="
            ssoId != scope.row.creatorSsoId ? true : scope.row.applyStatus !== 1
          "
          @click="openModal(2, scope.row)"
        >
          取消
        </el-button>
      </template>
    </dynamictable>
    <el-dialog
      width="400px"
      :title="title"
      :visible.sync="visible"
      @closed="onClose"
    >
      <el-form ref="formData" :model="saveParams">
        <el-form-item v-if="type === 0" label="挂账订单号：">
          {{ currentRow ? currentRow.hangApplyNo : '' }}
        </el-form-item>
        <el-form-item v-if="type === 0" label="资金总账号：">
          {{ currentRow ? currentRow.summaryAccountNo : '' }}
        </el-form-item>
        <el-form-item v-if="type === 0" label="见证子账号：">
          {{ currentRow ? currentRow.subAccountName : '' }}
        </el-form-item>
        <el-form-item v-if="type === 0" label="挂证金额：">
          {{ currentRow ? currentRow.hangAmount : '' }}
        </el-form-item>
        <el-form-item v-if="type === 0" label="再次确认挂证金额：">
          <el-input
            v-model="saveParams.confirmHangAmount"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item
          v-if="type !== 0"
          prop="handleReason"
          :rules="[{ required: true, message: '请输入', trigger: 'change' }]"
          :label="type === 1 ? '拒绝原因：' : '取消原因：'"
        >
          <el-input
            v-model="saveParams.handleReason"
            type="textarea"
            :rows="4"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" style="text-align: center" class="dialog-footer">
        <el-button type="primary" @click="visible = false">取 消</el-button>
        <el-button type="primary" @click="onOK">确 认</el-button>
      </div>
    </el-dialog>
    <AddRegisterTheAccount
      v-model="showDialog"
      :sub-account-list="subAccountList"
      @onGet="getList(true)"
    ></AddRegisterTheAccount>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    hangOrderSelector,
    hangOrderList,
    hangOrderConfirm,
    hangOrderCancel,
    hangOrderRefuse,
  } from '@/api/pingan';
  import AddRegisterTheAccount from './components/AddRegisterTheAccount';
  import { parseTime } from '@/utils';

  export default {
    components: {
      dynamictable,
      AddRegisterTheAccount,
    },

    data() {
      let columns = [
        {
          prop: 'hangApplyNo',
          label: '挂账订单号',
        },
        {
          prop: 'summaryAccountNo',
          label: '资金汇总账号',
        },
        {
          prop: 'subAccountName',
          label: '见证子账号',
        },
        {
          prop: 'hangAmount',
          label: '挂账金额',
        },
        {
          prop: 'tradingCharge',
          label: '交易费用',
        },
        {
          prop: 'applyStatusDesc',
          label: '状态',
        },
        {
          prop: 'handleReason',
          label: '状态描述',
        },
        {
          prop: 'remark',
          label: '备注',
        },
        {
          prop: 'createTime',
          label: '创建时间',
        },
        {
          prop: 'hangTime',
          label: '挂账时间',
        },
        {
          prop: 'creatorName',
          label: '创建人',
        },
        {
          prop: 'reviewerName',
          label: '审核 | 拒绝人',
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '200',
          scopedSlots: { customRender: 'operation' },
        },
      ];

      return {
        showDialog: false,
        visible: false,
        searchDate: '',
        searchDate1: '',
        title: '',
        type: 0,
        currentRow: null,
        searchParams: {
          hangApplyNo: '',
          applyStatus: '',
          startCreateTime: '',
          endCreateTime: '',
          startHangTime: '',
          endHangTime: '',
        },
        saveParams: {
          hangApplyNo: '',
          confirmHangAmount: '',
          handleReason: '',
        },
        list: [],
        applyStatusList: [],
        subAccountList: [],
        pagination: {
          pageSize: 1,
          pageLimit: 20,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
        columns,
      };
    },
    computed: {
      ssoId: function () {
        console.log(this.$store.state, '000000');
        return this.$store.state.user.userInfo.id;
      },
    },
    created() {
      hangOrderSelector().then(r => {
        if (r) {
          const { res } = r;
          this.applyStatusList = res.applyStatusList;
          this.subAccountList = res.subAccountList;
        }
      });
      this.getList(true);
    },

    methods: {
      getParams() {
        this.searchParams.startCreateTime = this.searchDate
          ? parseTime(this.searchDate[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.endCreateTime = this.searchDate
          ? parseTime(this.searchDate[1], '{y}-{m}-{d}')
          : '';
        this.searchParams.startHangTime = this.searchDate1
          ? parseTime(this.searchDate1[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.endHangTime = this.searchDate1
          ? parseTime(this.searchDate1[1], '{y}-{m}-{d}')
          : '';
        const params = {
          ...this.searchParams,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const { res } = await hangOrderList(params);
        this.options.loading = false;

        this.list = res ? res.list : [];
        this.pagination.total = res ? res.total : 0;
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.getList(true);
      },
      onClose() {
        Object.assign(this.$data.saveParams, this.$options.data().saveParams);
        this.$nextTick(function () {
          this.$refs.formData.clearValidate();
        });
      },

      openModal(type, row) {
        this.currentRow = row;
        this.saveParams.hangApplyNo = row.hangApplyNo;
        this.type = type;
        this.visible = true;
        this.title = ['审核并确认', '拒绝', '取消'][type];
      },
      onOK() {
        this.$refs.formData.validate(valid => {
          if (!valid) return;
          const apiList = [hangOrderConfirm, hangOrderRefuse, hangOrderCancel];
          const {
            handleReason,
            confirmHangAmount,
            hangApplyNo,
          } = this.saveParams;
          apiList[this.type](
            this.type === 0
              ? {
                  hangApplyNo,
                  confirmHangAmount,
                }
              : {
                  hangApplyNo,
                  handleReason,
                },
          ).then(res => {
            if (!res.err) {
              this.$message.success('操作成功');
            }

            this.visible = false;
            this.getList();
          });
        });
      },
    },
  };
</script>
<style lang="scss"></style>
