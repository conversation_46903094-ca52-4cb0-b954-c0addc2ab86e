<template>
  <div>
    <el-form inline>
      <el-form-item label="交易日期:">
        <el-date-picker
          v-model="tradeDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="付款子账户:">
        <el-select
          v-model="searchParams.payerAccountNo"
          clearable
          placeholder="请选择资金类型"
        >
          <el-option
            v-for="item in accountList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="收款子账户:">
        <el-select
          v-model="searchParams.payeeAccountNo"
          clearable
          placeholder="请选择资金类型"
        >
          <el-option
            v-for="item in accountList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态:">
        <el-select
          v-model="searchParams.status"
          clearable
          placeholder="请选择审核状态"
        >
          <el-option
            v-for="item in fundApplyStatusList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <ac-permission-button
          btn-text="查询"
          permission-key="transferAuditMgt-search"
          @click="getList(true)"
        ></ac-permission-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <ac-permission-button
          btn-text="批量审核"
          permission-key="transferAuditMgt-batch-audit"
          @click="batchAudit('batchAudit')"
        ></ac-permission-button>
        <ac-permission-button
          btn-text="批量终审"
          permission-key="transferAuditMgt-batch-final-review"
          @click="batchFinalAudit('batchFinalAudit')"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
      @selection-change="handleSelectionChange"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="操作日志"
          permission-key=""
          @click="auditOperationLog(scope.row)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="['AUDIT', 'FINAL_AUDIT'].includes(scope.row.status)"
          slot="reference"
          type="text"
          size="small"
          btn-text="通过"
          permission-key=""
          @click="handleOperate(scope.row, 1)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="['AUDIT', 'FINAL_AUDIT'].includes(scope.row.status)"
          slot="reference"
          type="text"
          size="small"
          btn-text="拒绝"
          permission-key=""
          @click="handleOperate(scope.row, 2)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="
            ['MANUAL'].includes(scope.row.status) ||
            (4 === scope.row.accountRelationship &&
              ['PROCESSING'].includes(scope.row.status))
          "
          slot="reference"
          type="text"
          size="small"
          btn-text="确认转账"
          permission-key=""
          @click="handleOperate(scope.row, 3)"
        ></ac-permission-button>
      </template>
    </dynamictable>

    <el-dialog
      title="确认转账"
      :visible.sync="dialogVisible"
      width="520px"
      @closed="onClose"
    >
      <el-form ref="form" :model="form" :rules="rulesForm" label-width="160px">
        <el-form-item label="您接收验证码的号码为: ">
          <div>{{ form.msgPhone }}</div>
        </el-form-item>
        <el-form-item label="短信指令号: " prop="serialNo">
          <el-input v-model="form.serialNo" style="width: 300px"></el-input>
        </el-form-item>
        <el-form-item label="短信验证码: " prop="verificationCode">
          <el-input
            v-model="form.verificationCode"
            style="width: 300px"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onOK">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import { parseTime, debounce } from '@/utils';
  import {
    fundSelector,
    getSubAccountTransferOptions,
    fundTransferPage,
    auditFinal,
    auditOper,
    mobileCode,
    auditTransferConfirm,
    batchFinalAuditSubmit,
    batchAuditSubmit,
  } from '@/api/pingan';

  export default {
    components: {
      dynamictable,
    },
    data() {
      let columns = [
        {
          prop: 'id',
          label: 'ID',
        },
        {
          prop: 'payerCustName',
          label: '付款子账户',
          render: ({ payerCustName, payerAccountType, payerAccountNo }) => (
            <span>
              {payerCustName}-{payerAccountType}-{payerAccountNo}
            </span>
          ),
        },
        {
          prop: 'applyType',
          label: '操作类型',
        },
        {
          prop: 'bizType',
          label: '业务类型',
          render: () => <span>普通转账</span>,
        },
        {
          prop: 'transferTypeDesc',
          label: '转账类型',
        },
        {
          prop: 'amount',
          label: '交易金额',
        },
        {
          prop: 'feeAmount',
          label: '手续费',
        },
        // {
        //   prop: 'bankName',
        //   label: '提现银行',
        // },
        // {
        //   prop: 'cardNo',
        //   label: '提现账号',
        // },
        {
          prop: 'payeeCustName',
          label: '收款子账户',
          render: ({ payeeCustName, payeeAccountType, payeeAccountNo }) => (
            <span>
              {payeeCustName}-{payeeAccountType}-{payeeAccountNo}
            </span>
          ),
        },
        {
          prop: 'createTime',
          label: '交易时间',
        },
        {
          prop: 'statusDesc',
          label: '状态',
        },
        {
          prop: 'summary',
          label: '操作备注',
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '100',
          scopedSlots: { customRender: 'operation' },
        },
      ];

      return {
        selectArr: [],
        dialogVisible: false, // 确认转账弹框
        tradeDate: '',
        form: {
          verificationCode: '',
          msgPhone: '',
          serialNo: '',
        },
        currentRow: null,
        searchParams: {
          payeeAccountNo: '',
          payerAccountNo: '',
          status: '',
          startTime: '',
          endTime: '',
        },
        fundApplyStatusList: [], // 状态列表
        payerAccountList: [], // 付款子账户列表
        payeeAccountList: [], // 收款字账户列表
        accountList: [], // 账户列表
        list: [],
        rulesForm: {
          verificationCode: [
            {
              required: true,
              message: '请输入短信验证码',
              trigger: 'blur',
            },
          ],
          serialNo: [
            {
              required: true,
              message: '请输入短信短信指令号',
              trigger: 'blur',
            },
          ],
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          mutiSelect: true,
        },
        columns,
      };
    },
    async created() {
      await this.loadOptions();
      this.getList(true);
    },
    methods: {
      batchAudit(auditType) {
        if (this.selectArr.length === 0) {
          return this.$message.error('请选择审核记录');
        }
        let saveParamsArr = [];
        for (let i in this.selectArr) {
          if (this.selectArr[i].status != 'AUDIT') {
            return this.$message.error(
              '批量审核，请全部选择【待审核】状态记录',
            );
          }
          saveParamsArr.push(this.selectArr[i].id);
        }
        this.$confirm('确认批量审核？', '批量审核', {
          cancelButtonText: '拒绝',
          confirmButtonText: '确定',
          type: 'warning',
        })
          .then(() => {
            this.$confirm('确认同意该操作？', '二次确认', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            })
              .then(() => {
                const params = {
                  idList: saveParamsArr,
                  auditType: 1,
                };
                this.batchSaveOperateApi(auditType, params);
              })
              .catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消',
                });
              });
          })
          .catch(() => {
            this.$prompt('拒绝理由', '二次确认', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              inputType: 'textarea',
              inputValidator: value => {
                if (!value) {
                  return '拒绝原因不能为空！';
                }
              },
            })
              .then(({ value }) => {
                const params = {
                  idList: saveParamsArr,
                  auditType: 2,
                  remark: value,
                };
                this.batchSaveOperateApi(auditType, params);
              })
              .catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消',
                });
              });
          });
        return;
      },
      batchFinalAudit(auditType) {
        if (this.selectArr.length === 0) {
          return this.$message.error('请选择终审记录');
        }
        let saveParamsArr = [];
        for (let i in this.selectArr) {
          if (this.selectArr[i].status != 'FINAL_AUDIT') {
            return this.$message.error(
              '批量终审，请全部选择【待终审】状态记录',
            );
          }
          saveParamsArr.push(this.selectArr[i].id);
        }
        this.$confirm('确认批量终审？', '批量终审', {
          cancelButtonText: '拒绝',
          confirmButtonText: '确定',
          type: 'warning',
        })
          .then(() => {
            this.$confirm('确认同意该操作？', '二次确认', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            })
              .then(() => {
                const params = {
                  idList: saveParamsArr,
                  auditType: 1,
                };
                this.batchSaveOperateApi(auditType, params);
              })
              .catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消',
                });
              });
          })
          .catch(() => {
            this.$prompt('拒绝理由', '二次确认', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              inputType: 'textarea',
              inputValidator: value => {
                if (!value) {
                  return '拒绝原因不能为空！';
                }
              },
            })
              .then(({ value }) => {
                const params = {
                  idList: saveParamsArr,
                  auditType: 2,
                  remark: value,
                };
                this.batchSaveOperateApi(auditType, params);
              })
              .catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消',
                });
              });
          });
        return;
      },
      handleSelectionChange(val) {
        this.selectArr = val;
      },
      auditOperationLog(row) {
        this.$router.push({
          path: 'auditOperationLog',
          query: {
            id: row.id,
          },
        });
      },
      // 确认转账
      onOK: debounce(function () {
        this.$refs.form.validate(async valid => {
          if (!valid) return;
          const { serialNo, verificationCode } = this.form;
          auditTransferConfirm({
            verificationCode,
            serialNo,
            id: this.currentRow.id,
          }).then(async ({ res, err }) => {
            if (!err) {
              this.$message({
                type: 'success',
                message: '确认转账成功!',
              });
              this.getList();
              this.dialogVisible = false;
            }
          });
        });
      }, 1000),

      // 加载下拉数据
      async loadOptions() {
        const { res, err } = await fundSelector();
        if (!err && res) {
          this.fundApplyStatusList = res.fundApplyStatusList;
          this.accountList = res.accountList;
        }
        // Promise.all([fundSelector(), getSubAccountTransferOptions()])
        //   .then(result => {
        //     if (result && result.length) {
        //       this.fundApplyStatusList = result[0]?.res?.fundApplyStatusList;
        //       this.payerAccountList = result[1]?.res?.payerAccountName;
        //       this.payeeAccountList = result[1]?.res?.payeeAccountName;
        //     }
        //   })
        //   .catch(e => console.log(e));
      },

      async saveOperateApi(params, row) {
        const apis = row.status === 'AUDIT' ? auditOper : auditFinal;
        const { res, err } = await apis(params);
        if (!err) {
          this.getList();
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
        }
      },
      // 确认转账发送验证码
      async sendMobileCode(row) {
        const { res, err } = await mobileCode({ id: row.id });
        if (!err) {
          this.dialogVisible = true;
          this.currentRow = row;
          if (res) {
            const { entity } = res;
            this.form = entity;
          }
        }
      },

      handleOperate(row, type) {
        if (type === 2) {
          this.$prompt('拒绝理由', '二次确认', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputType: 'textarea',
            inputValidator: value => {
              if (!value) {
                return '拒绝原因不能为空！';
              }
            },
          })
            .then(({ value }) => {
              this.saveOperateApi(
                {
                  id: row.id,
                  auditType: 2,
                  remark: value,
                },
                row,
              );
            })
            .catch(() => {});
          return;
        }
        // 确认转账
        if (type === 3) {
          this.sendMobileCode(row);
          return;
        }
        this.$confirm('确认同意该操作?', '二次确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.saveOperateApi(
              {
                id: row.id,
                auditType: 1,
              },
              row,
            );
          })
          .catch(() => {});
      },
      getParams() {
        const tradeDate = this.tradeDate;
        this.searchParams.startTime = tradeDate
          ? parseTime(tradeDate[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.endTime = tradeDate
          ? parseTime(tradeDate[1], '{y}-{m}-{d}')
          : '';
        const params = {
          ...this.searchParams,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const { res, err } = await fundTransferPage(params);
        this.options.loading = false;
        if (res && !err) {
          this.list = res ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.tradeDate = '';
      },
      onClose() {
        Object.assign(this.$data.form, this.$options.data().form);
        this.$nextTick(function () {
          this.$refs.form.clearValidate();
        });
      },
      async batchSaveOperateApi(batchType, params) {
        const apis =
          batchType === 'batchAudit' ? batchAuditSubmit : batchFinalAuditSubmit;
        const { res, err } = await apis(params);
        if (!err) {
          this.getList();
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
        }
      },
    },
  };
</script>
<style lang="scss"></style>
