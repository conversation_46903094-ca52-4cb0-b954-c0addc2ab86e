<!--
 * @Author: 七七
 * @Date: 2022-05-11 15:18:19
 * @LastEditors: 七七
 * @LastEditTime: 2022-05-25 16:33:23
 * @FilePath: /access-fmis-web/src/views/pingan/crossBorderDetail.vue
-->
<template>
  <div>
    <el-form :model="search" inline>
      <el-form-item label="订单编号">
        <el-input
          v-model="search.orderSn"
          placeholder="请输入订单编号"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="交易方向">
        <el-select v-model="search.transType" clearable>
          <el-option
            v-for="item in typeList"
            :key="item.key"
            :label="item.val"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>
<script>
  import {
    fundDeclarationDetailList,
    fundDeclarationSelector,
  } from '@/api/pingan';
  import dynamictable from '@/components/dynamic-table';
  export default {
    components: { dynamictable },
    data() {
      let columns = [
        {
          prop: 'orderSn',
          label: '订单编号',
        },
        {
          prop: 'amount',
          label: '交易金额',
        },
        {
          prop: 'costType',
          label: '费用类型',
        },
        {
          prop: 'costSettleAmount',
          label: '费用结算总金额',
        },
        {
          prop: 'currency',
          label: '币种',
        },
        {
          prop: 'transType',
          label: '交易方向',
        },
        {
          prop: 'channelId',
          label: '支付渠道',
        },
        {
          prop: 'transactionTime',
          label: '实际支付时间',
        },
      ];
      return {
        search: {},
        searchCopy: {},
        detailInfo: {
          billDate: '',
          capitalSubjectName: '',
          tradeSubjectName: '',
        },
        typeList: [],
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          index: true,
          indexName: 'ID',
        },
        columns,
      };
    },
    mounted() {
      if (this.$route.query.billDate?.length > 0) {
        this.detailInfo.billDate = this.$route.query.billDate;
      }
      if (this.$route.query.capitalSubjectName?.length > 0) {
        this.detailInfo.capitalSubjectName = this.$route.query.capitalSubjectName;
      }
      if (this.$route.query.tradeSubjectName?.length > 0) {
        this.detailInfo.tradeSubjectName = this.$route.query.tradeSubjectName;
      }
      this.getOptions();
      this.getList();
    },
    methods: {
      async getList() {
        this.options.loading = true;
        this.searchCopy = { ...this.search };
        const body = this.getParams();
        try {
          const { res, err } = await fundDeclarationDetailList(body);
          if (res && !err) {
            this.list = res ? res.records : [];
            this.pagination.total = res ? res.total : 0;
          }
        } catch (e) {}
        this.options.loading = false;
      },
      getParams() {
        const body = {
          pageNumber: this.pagination.pageSize,
          pageSize: this.pagination.pageLimit,
          billDate: this.detailInfo.billDate,
          capitalSubjectName: this.detailInfo.capitalSubjectName,
          tradeSubjectName: this.detailInfo.tradeSubjectName,
        };
        if (this.searchCopy.orderSn?.length > 0) {
          body.orderSn = this.searchCopy.orderSn;
        }
        if (
          this.searchCopy.transType === 0 ||
          this.searchCopy.transType === 1
        ) {
          body.transType = this.searchCopy.transType;
        }
        return body;
      },
      async getOptions() {
        try {
          const { res, err } = await fundDeclarationSelector();
          if (res && !err) {
            this.typeList = res.transTypes ? res.transTypes : [];
          }
        } catch (e) {}
      },
      handleSearch() {
        this.pagination.pageSize = 1;
        this.getList();
      },
      reset() {
        this.search = {};
      },
    },
  };
</script>
<style scoped></style>
