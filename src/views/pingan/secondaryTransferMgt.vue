<template>
  <div>
    <el-form inline :model="searchParams">
      <el-form-item label="日期:">
        <el-date-picker
          v-model="billDate"
          :type="activeName === '1' ? 'daterange' : 'monthrange'"
          range-separator="至"
          :value-format="activeName === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
          :format="activeName === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="贸易主体:">
        <el-input
          v-model="searchParams.outAccountName"
          placeholder="请输入贸易主体"
        />
      </el-form-item>
      <el-form-item label="服务主体:">
        <el-input
          v-model="searchParams.inAccountName"
          placeholder="请输入服务主体"
        />
      </el-form-item>
      <el-form-item label="款项类型:">
        <el-select
          v-model="searchParams.feeType"
          clearable
          placeholder="请选择款项类型"
        >
          <el-option
            v-for="item in feeTypeList"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-popover placement="bottom" trigger="click">
          <p>请选择导出时间</p>
          <el-date-picker
            v-model="exportDate"
            :clearable="false"
            :type="activeName === '1' ? 'daterange' : 'monthrange'"
            range-separator="至"
            :value-format="activeName === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
            :format="activeName === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :picker-options="pickerOptions"
          ></el-date-picker>
          <div style="text-align: right; margin: 10px 0 0">
            <el-button type="primary" size="mini" @click="onExport">
              确定
            </el-button>
          </div>
          <el-button slot="reference" style="margin-left: 10px" type="primary">
            导出
          </el-button>
        </el-popover>
        <!-- <el-button type="primary" @click="onExport">导出</el-button> -->
      </el-form-item>
    </el-form>
    <el-tabs v-model="activeName" type="card" @tab-click="handleTabClick">
      <el-tab-pane label="日汇总" name="1"></el-tab-pane>
      <el-tab-pane label="月汇总" name="2"></el-tab-pane>
    </el-tabs>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="明细"
          permission-key=""
          @click="go(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import { newExportExcel } from '@/api/blob';
  import { downloadFile, debounce, setInitData, parseTime } from '@/utils';

  import {
    secondSplitStatsSelector,
    secondSplitStatsDaySum,
    secondSplitStatsMonthSum,
  } from '@/api/pingan';

  export default {
    name: 'SecondaryTransferMgt',
    components: {
      dynamictable,
    },
    data() {
      return {
        billDate: '',
        activeName: '1',
        feeTypeList: [],
        salesEntityList: [],
        settleSubjectLisy: [],
        searchParams: {
          feeType: '',
          inAccountName: '',
          outAccountName: '',
          beginDate: '',
          endDate: '',
        },
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          index: true,
          indexName: 'ID',
        },
        exportDate: '', // 导出时间
        choiceDate0: '',
        pickerOptions: {
          onPick: ({ maxDate, minDate }) => {
            this.choiceDate0 = minDate.getTime();
            if (maxDate) {
              this.choiceDate0 = '';
            }
          },
          disabledDate: time => {
            if (this.choiceDate0 !== '') {
              const one = 30 * 24 * 3600 * 1000;
              const minTime = this.choiceDate0 - one;
              const maxTime = this.choiceDate0 + one;
              return time.getTime() < minTime || time.getTime() > maxTime;
            }
          },
        },
      };
    },

    created() {
      secondSplitStatsSelector({}).then(({ res, err }) => {
        if (res && !err) {
          this.feeTypeList = res.feeTypeList;
          this.salesEntityList = res.salesEntityList;
          this.settleSubjectLisy = res.settleSubjectLisy;
        }
      });
      this.billDate = setInitData(30);
      this.exportDate = setInitData(30);
      this.getList();
    },
    methods: {
      getText(val, list) {
        let text = '';
        list.map(item => {
          if (item.dictValue == val) {
            text = item.dictDesc;
          }
        });
        return text;
      },
      onExport: debounce(function () {
        const params = this.getParams('export');
        const api =
          this.activeName === '1'
            ? '/api/split-service/secondarySplitStats/daySumExport'
            : '/api/split-service/secondarySplitStats/monthSumExport';
        newExportExcel({ ...params }, api, 'post').then(res => {
          if (res) {
            downloadFile(res.data, '二级转账列表');
          }
        });
      }, 800),
      go(row) {
        const { activeName } = this;
        let query = {
          inAccountNo: row.inAccountNo,
          outAccountNo: row.outAccountNo,
          feeType: row.feeType,
        };
        if (activeName === '1') {
          query.day = row.billDate;
        } else {
          query.month = row.billDate;
        }
        this.$router.push({
          path: 'secondaryTransferDetails',
          query,
        });
      },
      handleTabClick() {
        this.getList(true);
      },
      getParams(type) {
        const activeName = this.activeName;
        const billDate = type === 'export' ? this.exportDate : this.billDate;
        this.searchParams.beginDate = billDate
          ? parseTime(
              billDate[0],
              activeName === '1' ? '{y}-{m}-{d}' : '{y}-{m}',
            )
          : '';
        this.searchParams.endDate = billDate
          ? parseTime(
              billDate[1],
              activeName === '1' ? '{y}-{m}-{d}' : '{y}-{m}',
            )
          : '';
        const params = {
          ...this.searchParams,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const { activeName } = this;
        const params = this.getParams();
        this.options.loading = true;
        const api =
          activeName === '1'
            ? secondSplitStatsDaySum
            : secondSplitStatsMonthSum;
        const { err, res } = await api(params);
        if (res && !err) {
          this.list = res ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        }
        this.options.loading = false;
      },
      onReset() {
        const data = setInitData(30);
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.exportDate = data;
        this.billDate = data;
      },

      getColumns() {
        const columns = [
          {
            prop: 'billDate',
            label: '日期',
          },
          {
            prop: 'outAccountName',
            label: '贸易主体',
          },
          {
            prop: 'inAccountName',
            label: '服务主体',
          },
          {
            prop: 'feeType',
            label: '款项类型',
            render: ({ feeType }) => (
              <span>{this.getText(feeType, this.feeTypeList)}</span>
            ),
          },
          {
            prop: 'feeAmount',
            label: '结算金额',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '200',
            scopedSlots: { customRender: 'operation' },
          },
        ];
        return columns;
      },
    },
  };
</script>
<style lang="scss"></style>
