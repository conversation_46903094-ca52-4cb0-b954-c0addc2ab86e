<template lang="pug">
.vertical.top.vcenter.padding-bottom-10
  el-form(inline label-suffix=':')
    el-form-item(label='付款子账户')
      el-select(
        v-model='payerId'
        placeholder='请选择'
        style='width: 200px'
        :loading='optionLoading'
        filterable
        clearable
      )
        el-option(
          v-for='item in payerAccountName'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-form-item(label='收款子账户')
      el-select(
        v-model='payeeId'
        placeholder='请选择'
        style='width: 200px'
        :loading='optionLoading'
        filterable
        clearable
      )
        el-option(
          v-for='item in payeeAccountName'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-button.margin-left-10(
      type='primary'
      icon='el-icon-search'
      @click='onSearchClick'
    ) 查询
    ac-permission-button.margin-left-10(
      type='primary'
      btn-text='新增'
      permission-key='memberTransfer-add'
      icon='el-icon-plus'
      @click='onAddClick'
    )
  el-table.margin-top-20(
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
  )
    el-table-column(prop='payerAccountName' label='付款子账户')
      <!-- template(v-slot="{ $index, row:{payerAccountTypeDesc, payerAccountName} }") -->
        <!-- div {{payerAccountName}}-{{payerAccountTypeDesc}} -->
    el-table-column(prop='payerBalance' label='付款子账户总余额/可用余额')
      template(v-slot='{ $index, row: { payerActualBalance, payerBalance } }')
        div {{ payerBalance }} / {{ payerActualBalance }}
    el-table-column(prop='payeeAccountName' label='收款子账户')
      <!-- template(v-slot="{ $index, row:{payeeAccountTypeDesc, payeeAccountName} }") -->
        <!-- div {{payeeAccountName}}-{{payeeAccountTypeDesc}} -->
    el-table-column(prop='payeeBalance' label='收款子账户总余额/可用余额')
      template(v-slot='{ $index, row: { payeeBalance, payeeActualBalance } }')
        div {{ payeeBalance }} / {{ payeeActualBalance }}
    el-table-column(prop='remark' label='备注')
    el-table-column(label='操作')
      template(v-slot='{ $index, row }')
        ac-permission-button(
          btn-text='刷新余额'
          permission-key='memberTransfer-refresh-balance'
          @click='onRefreshRowClick(row, $index)'
        )
        ac-permission-button.margin-left-10(
          @click='onModifyRowClick(row, $index)'
          btn-text='修改'
          permission-key='memberTransfer-edit'
        )
        ac-permission-button.margin-left-10(
          btn-text='转账'
          permission-key='memberTransfer-transfer'
          @click='onTransferClick(row, $index)'
        )
  el-pagination(
    :current-page='m_current'
    :page-sizes='[10, 15, 20, 30]'
    :page-size='m_pageSize'
    layout='total, sizes, prev, pager, next, jumper'
    :total='m_total'
    background
    @size-change='m_handleSizeChange'
    @current-change='m_handleCurrentChange'
  )
  member-transfer-dialog(
    v-model='showTransferDialog'
    :record='currentRecord'
    @success='m_loadData'
  )
  member-transfer-info-dialog(
    v-model='showDialog'
    :record='currentRecord'
    :payer-account-name='payerAccountName'
    :payee-account-name='payeeAccountName'
    @success='m_loadData'
  )
</template>
<script>
  import { paginationMixin } from '@/mixins/tableMixin';
  import MemberTransferDialog from './components/MemberTransferDialog';
  import MemberTransferInfoDialog from './components/MemberTransferInfoDialog';

  export default {
    components: {
      MemberTransferDialog,
      MemberTransferInfoDialog,
    },
    mixins: [paginationMixin],
    data() {
      return {
        loading: false,
        optionLoading: false,
        showDialog: false,
        showTransferDialog: false,

        payeeId: null,
        payerId: null,

        currentRecord: null,

        payeeAccountName: [], // 收款子账户字典
        payerAccountName: [], // 付款子账户字典

        list: [],
      };
    },
    async created() {
      await this.loadOptions();
      this.m_loadData();
    },
    methods: {
      async loadOptions() {
        const {
          err,
          res,
        } = await this.$apis.pingan.getSubAccountTransferOptions();
        if (!err) {
          const { payeeAccountName, payerAccountName } = res;
          this.payerAccountName = payerAccountName;
          this.payeeAccountName = payeeAccountName;
        }
      },
      async m_loadData() {
        this.loading = true;
        const body = {
          current: this.m_current,
          size: this.m_pageSize,
        };
        if (this.payerId) {
          body.payerId = this.payerId;
        }
        if (this.payeeId) {
          body.payeeId = this.payeeId;
        }
        const { err, res } = await this.$apis.pingan.getSubAccountTransferList(
          body,
        );
        if (!err && res) {
          const { total, records } = res;
          this.m_total = total;
          console.log(records, 'records');
          this.list = records;
        }
        this.loading = false;
      },
      onSearchClick() {
        this.m_current = 1;
        this.m_loadData();
      },
      onAddClick() {
        this.currentRecord = null;
        this.showDialog = true;
      },
      async refreshBalance(id, index) {
        this.loading = true;
        const { err, res } = await this.$apis.pingan.refreshSubAccountBalance(
          id,
        );
        if (!err) {
          const {
            payeeAccountBalance: payeeBalance,
            payerAccountBalance: payerBalance,
            payerAccountActualBalance: payerActualBalance,
            payeeAccountActualBalance: payeeActualBalance,
          } = res;
          const list = [...this.list];
          const row = list[index];
          list[index] = {
            ...row,
            payeeBalance,
            payerBalance,
            payerActualBalance,
            payeeActualBalance,
          };
          this.list = list;
        }
        this.loading = false;
      },
      onRefreshRowClick(row, index) {
        this.refreshBalance(row.id, index);
      },
      onModifyRowClick(row, index) {
        this.currentRecord = row;
        this.showDialog = true;
      },
      async onTransferClick(row, index) {
        await this.refreshBalance(row.id, index);
        this.currentRecord = this.list[index];
        this.showTransferDialog = true;
      },
    },
  };
</script>
