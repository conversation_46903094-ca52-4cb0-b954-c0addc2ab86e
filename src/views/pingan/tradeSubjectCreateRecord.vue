<template lang="pug">
.vertical.top.vcenter.padding-bottom-10
  el-form(ref='form' :rules='rules' :model='form' label-suffix=':' inline)
    el-form-item(label='贸易主体名称' prop='custId')
      el-select(
        v-model='form.custId'
        placeholder='请选择'
        style='width: 200px'
        :loading='optionLoading'
        filterable
        clearable
      )
        el-option(
          v-for='item in mainPartAccountList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-button.margin-left-10(
      type='primary'
      icon='el-icon-search'
      @click='onSearchClick'
    ) 查询
    ac-permission-button.margin-left-10(
      type='primary'
      btn-text='开户'
      permission-key='tradeSubjectCreateRecord-add'
      icon='el-icon-plus'
      @click='onAddClick'
    )
  el-table.margin-top-20(
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
  )
    el-table-column(prop='openTime' label='开户时间')
    el-table-column(prop='accountName' label='贸易主体名称')
    el-table-column(prop='custId' label='企业编码')
    el-table-column(prop='accountTypeDesc' label='账户类型')

  trade-record-create-dialog(v-model='showDialog' @success='refreshPage')
</template>
<script>
  import TradeRecordCreateDialog from './components/TradeRecordCreateDialog.vue';

  export default {
    components: {
      TradeRecordCreateDialog,
    },
    data() {
      return {
        loading: false,
        optionLoading: false,
        showDialog: false,

        form: {
          custId: null,
        },

        mainPartAccountList: [],

        list: [],

        rules: {},
      };
    },
    async created() {
      await this.loadOptions();
      this.m_loadData();
    },
    methods: {
      refreshPage() {
        this.loadOptions();
        this.m_loadData();
      },
      async loadOptions() {
        const {
          err,
          res,
        } = await this.$apis.pingan.getOpenMainPartAccountOptions();
        if (!err) {
          const { mainPartAccountList } = res;
          this.mainPartAccountList = mainPartAccountList;
        }
      },
      async m_loadData() {
        this.loading = true;
        const body = {
          channel: 'pingan',
        };
        if (this.form.custId) {
          body.custId = this.form.custId;
        }

        const { err, res } = await this.$apis.pingan.getOpenMainPartAccountList(
          body,
        );
        if (!err && res) {
          this.list = res;
        }
        this.loading = false;
      },
      onSearchClick() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.m_loadData();
          }
        });
      },
      onAddClick() {
        this.showDialog = true;
      },
    },
  };
</script>
