<!--
 * @Author: 七七
 * @Date: 2022-05-11 15:18:19
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-10-21 10:27:39
 * @FilePath: /access-fmis-web/src/views/pingan/crossBorderManage.vue
-->
<template>
  <div>
    <el-form :model="search" inline>
      <el-form-item label="日期">
        <el-date-picker
          v-model="search.date"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          clearable
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="资金名称">
        <el-input
          v-model="search.capitalSubjectName"
          placeholder="请输入资金名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="贸易主体">
        <el-input
          v-model="search.tradeSubjectName"
          placeholder="请输入贸易主体"
          clearable
        ></el-input>
      </el-form-item>

      <el-form-item label="状态">
        <el-select v-model="search.status" clearable>
          <el-option
            v-for="item in statusList"
            :key="item.key"
            :label="item.val"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="reset">重置</el-button>
        <el-button @click="handleBatchSubmit">批量提交申报</el-button>
      </el-form-item>
    </el-form>
    <div class="mr-b-10">
      已选择条数：{{ selectNum }}条 结算汇总金额：{{ selectFund }}元
    </div>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
      :check-selectable="handleCheckSelectable"
      @selection-change="selectionChange"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          v-if="scope.row.statusCode === 0 || scope.row.statusCode === 4"
          slot="reference"
          type="text"
          size="small"
          btn-text="提交申报"
          @click="handleCheck(scope.row)"
        ></ac-permission-button>
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="明细"
          @click="handleDetail(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <el-dialog
      title="二次确认"
      :visible.sync="showCheckDialog"
      width="40%"
      center
    >
      <span>您已选择</span>
      <span>条数：{{ num }}条</span>
      <div>结算汇总金额：{{ fund }}元</div>
      <div>是否确认进行申报？</div>
      <span slot="footer">
        <el-button @click="handleDeclaration">确认</el-button>
        <el-button type="primary" @click="showCheckDialog = false">
          取消
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
  import {
    fundDeclarationManageList,
    fundDeclarationSelector,
    fundDeclarationSubmit,
  } from '@/api/pingan';
  import dynamictable from '@/components/dynamic-table';
  import dayjs from 'dayjs';
  export default {
    components: { dynamictable },
    data() {
      let columns = [
        {
          prop: 'id',
          label: 'ID',
        },
        {
          prop: 'billDate',
          label: '日期',
        },
        {
          prop: 'capitalSubjectName',
          label: '资金主体',
        },
        {
          prop: 'tradeSubjectName',
          label: '贸易主体',
        },
        {
          prop: 'costType',
          label: '费用类型',
        },
        {
          prop: 'summaryAmount',
          label: '费用结算总金额',
        },
        {
          prop: 'declareAmount',
          label: '已申报金额',
        },
        {
          prop: 'unDeclareAmount',
          label: '待申报金额',
        },
        {
          prop: 'status',
          label: '状态',
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          minWidth: '100',
          maxWidth: '300',
          scopedSlots: { customRender: 'operation' },
        },
      ];
      return {
        search: {
          date: [],
        },
        num: null,
        fund: null,
        selectNum: 0,
        selectFund: 0,
        currentId: null,
        selectionIds: [],
        showCheckDialog: false,
        statusList: [],
        typeList: [],
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          mutiSelect: true,
        },
        columns,
      };
    },
    mounted() {
      if (this.$route.query.serialNo?.length > 0) {
        this.search.serialNo = this.$route.query.serialNo;
      }
      const endTime = dayjs();
      const startTime = endTime.subtract(1, 'month');
      this.search.date = [
        startTime.format('YYYY-MM-DD 00:00:00'),
        endTime.format('YYYY-MM-DD 23:59:59'),
      ];
      this.getOptions();
      this.getList();
    },
    methods: {
      async getList() {
        this.options.loading = true;
        this.searchCopy = { ...this.search };
        const body = this.getParams();
        try {
          const { res, err } = await fundDeclarationManageList(body);
          if (res && !err) {
            this.list = res ? res.records : [];
            this.pagination.total = res ? res.total : 0;
          }
        } catch (e) {
          console.log(e);
        }
        this.options.loading = false;
      },
      getParams() {
        const body = {
          pageNumber: this.pagination.pageSize,
          pageSize: this.pagination.pageLimit,
        };
        if (this.searchCopy.date?.length > 0) {
          const startTime = this.searchCopy.date[0];
          const endTime = this.searchCopy.date[1];
          body.starTime =
            startTime.length === 10 ? startTime + ' 00:00:00' : startTime;
          body.endTime =
            endTime.length === 10 ? endTime + ' 23:59:59' : endTime;
        }
        if (this.searchCopy.capitalSubjectName?.length > 0) {
          body.capitalSubjectName = this.searchCopy.capitalSubjectName;
        }
        if (this.searchCopy.tradeSubjectName?.length > 0) {
          body.tradeSubjectName = this.searchCopy.tradeSubjectName;
        }
        if (this.searchCopy.status >= 0) {
          body.status = this.searchCopy.status;
        }

        if (this.searchCopy.serialNo?.length > 0) {
          body.serialNo = this.searchCopy.serialNo;
        }
        return body;
      },
      async getOptions() {
        try {
          const { res, err } = await fundDeclarationSelector();
          if (res && !err) {
            this.statusList = res.declareStatus ? res.declareStatus : [];
            this.typeList = res.transTypes ? res.transTypes : [];
          }
        } catch (e) {}
      },
      handleSearch() {
        this.pagination.pageSize = 1;
        this.getList();
      },
      reset() {
        this.search = {
          date: [],
        };
        const endTime = dayjs();
        const startTime = endTime.subtract(1, 'month');
        this.search.date = [
          startTime.format('YYYY-MM-DD HH:mm:ss'),
          endTime.format('YYYY-MM-DD HH:mm:ss'),
        ];
      },
      handleBatchSubmit() {
        if (this.selectionIds?.length > 0) {
          this.showCheckDialog = true;
          this.num = this.selectNum;
          this.fund = this.selectFund;
        } else {
          this.$message.error('请选择数据再进行操作');
        }
      },
      handleCheck(row) {
        //将选中数置为1，选中金额置为当前数据
        this.showCheckDialog = true;
        this.num = 1;
        this.fund = row.summaryAmount;
        this.selectionIds = [row.id];
      },
      handleDetail(row) {
        this.$router.push({
          name: 'crossBorderDetail',
          query: {
            billDate: row.billDate,
            capitalSubjectName: row.capitalSubjectName,
            tradeSubjectName: row.tradeSubjectName,
          },
        });
      },
      async handleDeclaration() {
        const ids = this.selectionIds;
        try {
          const { res, err } = await fundDeclarationSubmit({ ids: ids });
          if (!err) {
            this.$message.success('提交成功！');
            this.showCheckDialog = false;
            this.getList();
          }
        } catch (e) {
          console.log(e);
        }
      },
      // 禁止选择不符合的数据
      handleCheckSelectable(row) {
        return row.statusCode === 0 || row.statusCode === 4;
      },
      sumCount(arr = []) {
        let s = 0;
        for (let i = 0; i < arr.length - 1; i++) {
          s += arr[i];
        }
        return s;
      },
      // 勾选记录
      selectionChange(ids = []) {
        this.selectionIds = ids.map(item => item.id);
        this.selectNum = this.selectionIds.length;
        const fund = ids.map(item => item.summaryAmount * 100);

        let sum = 0;
        fund.forEach(val => {
          sum += val;
        });
        this.selectFund = sum / 100;
      },
    },
  };
</script>
<style scoped></style>
