<template lang="pug">
el-dialog.add-dialog(
  title='贸易主体修改'
  :visible.sync='showDialog'
  width='40%'
  :close-on-click-modal='false'
)
  el-form(
    ref='form'
    v-loading='loading'
    :model='form'
    :rules='rules'
    label-position='right'
    label-width='150px'
    label-suffix=':'
  )
    el-form-item(label='发货仓' prop='depoCode')
      el-select(v-model='form.depoCode' placeholder='请选择') 
        el-option(
          v-for='item in depoList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-form-item(label='经销商等级' prop='custLevelList')
      el-checkbox-group(v-model='form.custLevelList')
        el-checkbox(
          v-for='item in custLevelList'
          :key='item.dictValue'
          :label='item.dictValue'
        ) {{ item.dictDesc }}
    el-form-item(label='收款子账号' prop='accountId')
      el-select(v-model='form.accountId' placeholder='请选择')
        el-option(
          v-for='item in subAccountList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-form-item(label='操作备注' prop='remark')
      el-input(v-model='form.remark' type='textarea' placeholder='请输入备注')

  span.dialog-footer(slot='footer')
    el-button(@click='showDialog = false') 取消
    el-button(
      type='primary'
      :disabled='confirmDisable'
      :loading='saveLoading'
      @click='handleConfirmClick'
    ) 提交
</template>
<script>
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      record: {
        type: Object,
        required: true,
      },
      depoList: {
        type: Array,
        default: () => [],
      },
      custLevelList: {
        type: Array,
        default: () => [],
      },
      subAccountList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        loading: false,
        saveLoading: false,
        form: {
          custLevelList: [],
        },
        rules: {
          // accountType: [{ required: true }],
          // accountNo: [{ required: true }],
          transferAmount: [
            { required: true, message: '请输入转账金额', trigger: 'change' },
          ],
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          if (!value) {
            this.form = {
              custLevelList: [],
            };
          }
          this.$emit('change', value);
        },
      },
      confirmDisable() {
        const { accountId, custLevelList, depoCode } = this.form;
        return (
          accountId == null ||
          custLevelList == null ||
          custLevelList.length == 0 ||
          depoCode == null
        );
      },
    },
    watch: {
      show(value) {
        if (value) {
          this.initData();
        }
      },
    },
    methods: {
      async initData() {
        if (this.record) {
          this.loading = true;
          const { depoCode, accountId } = this.record;
          const {
            err,
            res,
          } = await this.$apis.pingan.getTradeSubjectConfigDetail(
            accountId,
            depoCode,
          );
          if (!err) {
            const { custLevelList, remark } = res;
            this.form = {
              custLevelList,
              remark,
              depoCode,
              accountId,
            };
          }
          this.loading = false;
        }
      },
      handleConfirmClick() {
        this.submit();
      },
      async submit() {
        this.saveLoading = true;
        const body = {
          ...this.form,
        };
        const { err, res } = await this.$apis.pingan.updateTradeSubjectConfig(
          body,
        );
        if (!err) {
          this.$message.success('操作成功');
          this.$emit('success');
          this.showDialog = false;
        }
        this.saveLoading = false;
      },
    },
  };
</script>
<style lang="scss" scoped>
  .add-dialog {
    ::v-deep {
      .el-input-number .el-input__inner {
        text-align: left;
      }
    }
  }
</style>
