<!--
 * @Author: 七七
 * @Date: 2022-04-18 00:18:40
 * @LastEditors: xuxiang
 * @LastEditTime: 2022-05-05 14:07:31
 * @FilePath: /access-fmis-web/src/views/xiamen/components/dailyBalanceDetails.vue
-->
<template>
  <div>
    <el-form inline>
      <el-form-item label="交易流水号:">
        <el-input
          v-model="searchParams.transNo"
          placeholder="请输入交易流水号"
        ></el-input>
      </el-form-item>

      <el-form-item label="交易类型:">
        <el-select
          v-model="searchParams.transType"
          clearable
          placeholder="请选择交易类型"
        >
          <el-option
            v-for="item in platTransType"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="业务类型:">
        <el-select
          v-model="searchParams.bizType"
          clearable
          placeholder="请选择交易类型"
        >
          <el-option
            v-for="item in platBizType"
            :key="item.dictValue"
            :label="item.dictDesc"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间:">
        <el-date-picker
          v-model="transactionData"
          type="daterange"
          :clearable="false"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import { parseTime, setInitData } from '@/utils';
  import { platDetailQuery } from '@/api/pingan';

  export default {
    components: { dynamictable },
    props: {
      platTransType: {
        type: Array,
        default: () => [],
      },
      platBizType: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        transactionData: '',
        list: [],
        searchParams: {
          transNo: '',
          beginTime: '',
          endTime: '',
          accountNo: '',
          transType: '',
          bizType: '',
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          index: true,
          indexName: '编号',
        },
        columns: [
          {
            prop: 'transNo',
            label: '交易流水',
          },
          {
            prop: 'accountName',
            label: '账户名称',
          },
          {
            prop: 'accountTypeDesc',
            label: '账户类型',
          },
          {
            prop: 'accountNo',
            label: '账户号',
          },
          {
            prop: 'transTypeDesc',
            label: '交易类型',
          },
          {
            prop: 'bizTypeDesc',
            label: '业务类型',
          },
          {
            prop: 'targetAccountName',
            label: '对方账户名称',
          },
          {
            prop: 'targetAccountTypeDesc',
            label: '对方账户类型',
          },
          {
            prop: 'targetAccountNo',
            label: '对方账户号',
          },
          {
            prop: 'transAmount',
            label: '金额',
          },
          {
            prop: 'transTime',
            label: '操作时间',
          },
          {
            prop: 'transStatusDesc',
            label: '状态',
          },
        ],
      };
    },
    created() {
      this.transactionData = setInitData(30);
      this.getList(true);
    },
    methods: {
      getParams() {
        const { accountNo } = this.$route.query;
        const { transactionData } = this;
        const body = {
          ...this.searchParams,
          accountNo,
          beginTime: transactionData
            ? parseTime(transactionData[0], '{y}-{m}-{d} 00:00:00')
            : '',
          endTime: transactionData
            ? parseTime(transactionData[1], '{y}-{m}-{d} 23:59:59')
            : '',
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return body;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        this.options.loading = true;
        const body = this.getParams();

        const { res, err } = await platDetailQuery(body);
        this.options.loading = false;
        if (res && !err) {
          this.list = res ? res.list : [];
          this.pagination.total = res.total ? res.total : 0;
        }
      },

      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.transactionData = setInitData(30);
      },
    },
  };
</script>
<style scoped></style>
