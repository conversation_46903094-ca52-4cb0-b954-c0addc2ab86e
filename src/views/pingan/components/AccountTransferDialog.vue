<template lang="pug">
el-dialog.add-dialog(
  title='子账户转账'
  :visible.sync='showDialog'
  width='40%'
  :close-on-click-modal='false'
)
  el-form(
    v-if='record'
    ref='form'
    :model='form'
    :rules='rules'
    label-position='right'
    label-width='150px'
    label-suffix=':'
  )
    el-form-item(label='营销子账户')
      div {{ record.accountNo }}
    el-form-item(label='账户余额：')
      div {{ record.availableBal }}
    el-form-item(label='转账类型' prop='transferType')
      el-select(
        v-model='form.transferType'
        placeholder='请选择'
        filterable
        style='width: 100%'
      )
        el-option(
          v-for='item in transferTypeList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-form-item(label='收款子账户' prop='payeeAccountNo')
      el-select(
        v-model='form.payeeAccountNo'
        placeholder='请选择'
        filterable
        style='width: 100%'
      )
        el-option(
          v-for='item in memberSubAccountList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )
    el-form-item(label='转账金额' prop='amount')
      el-input-number(
        v-model='form.amount'
        :min='0'
        :max='record.canCashBal'
        :precision='2'
        :controls='false'
        style='width: 100%'
        placeholder='请输入转账金额'
      )
    el-form-item(label='操作备注' prop='summary')
      el-input(v-model='form.summary' type='textarea' placeholder='请输入操作备注')

  span.dialog-footer(slot='footer')
    el-button(@click='showDialog = false') 取消
    el-button(
      type='primary'
      :loading='saveLoading'
      @click='handleConfirmClick'
    ) 提交
</template>
<script>
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      record: {
        type: Object,
        default: undefined,
      },
      memberSubAccountList: {
        type: Array,
        default: () => [],
      },
      transferTypeList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        form: {},
        verifyForm: {},
        entity: {},
        saveLoading: false,

        rules: {
          amount: [
            {
              required: true,
              message: '请输入金额',
              trigger: 'blur',
            },
          ],
          payeeAccountNo: [
            {
              required: true,
              message: '请选择收款子账户',
              trigger: 'blur',
            },
          ],
          verificationCode: {
            required: true,
            message: '请输入短信验证码',
            trigger: 'change',
          },
          transferType: [
            {
              required: true,
              message: '请选择转账类型',
              trigger: 'blur',
            },
          ],
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          if (value) {
          } else {
            // 重置
            this.form = {};
            this.$refs.form.resetFields();
          }
          this.$emit('change', value);
        },
      },
    },
    methods: {
      handleConfirmClick() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.submit();
          }
        });
      },
      async submit() {
        this.saveLoading = true;
        const { accountNo } = this.record;
        const body = {
          ...this.form,
          payerAccountNo: accountNo,
        };

        const { err, res } = await this.$apis.pingan.transferApply(body);
        if (!err) {
          this.$message.success('操作成功');
          this.$emit('success');
          this.showDialog = false;
        }

        this.saveLoading = false;
      },
    },
  };
</script>
<style lang="scss" scoped>
  .add-dialog {
    ::v-deep {
      .el-input-number .el-input__inner {
        text-align: left;
      }
    }
  }
</style>
