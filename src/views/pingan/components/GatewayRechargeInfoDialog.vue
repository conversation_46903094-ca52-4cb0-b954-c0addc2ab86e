<template lang="pug">
el-dialog.add-dialog(
  :title='"网关充值"'
  :visible.sync='showDialog'
  width='40%'
  :close-on-click-modal='false'
)
  el-form(
    ref='form'
    v-loading='loading'
    :model='form'
    :rules='rules'
    label-position='right'
    label-width='150px'
    label-suffix=':'
  )
    el-form-item(label='商户号' prop='merchantNo')
      el-select(
        v-model='form.merchantNo'
        placeholder='请选择'
        style='width: 100%'
      )
        el-option(
          v-for='item in rechargeMerchants'
          :key='item.merchantNo'
          :label='item.merchantName'
          :value='item.merchantNo'
        )
    el-form-item(label='付款银行' prop='bankCode')
      el-select(
        v-model='form.bankCode'
        placeholder='请选择'
        style='width: 100%'
        filterable
      )
        el-option(
          v-for='item in rechargeSupportBanks'
          :key='item.bankCode'
          :label='item.bankName'
          :value='item.bankCode'
        )
    el-form-item(label='收款账号' prop='cardNo')
      el-select(
        v-model='form.cardNo'
        placeholder='请选择'
        style='width: 100%'
        filterable
      )
        el-option(
          v-for='item in subAccountLists'
          :key='item.accountNo'
          :label='item.accountName'
          :value='item.accountNo'
        )
    el-form-item(label='金额' prop='transAmount')
      el-input-number(
        v-model='form.transAmount'
        :min='0.01'
        :precision='2'
        :controls='false'
        style='width: 100%'
        placeholder='请输入转出金额'
      )

    el-form-item(label='操作备注' prop='remark')
      el-input(v-model='form.remark' type='text' placeholder='请输入备注')

  span.dialog-footer(slot='footer')
    el-button(@click='showDialog = false') 取消
    el-button(
      type='primary'
      :disabled='confirmDisable'
      :loading='saveLoading'
      @click='handleConfirmClick'
    ) 提交
</template>

<script>
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      record: {
        type: Object,
        default: undefined,
      },
      rechargeSupportBanks: {
        type: Array,
        default: () => [],
      },
      subAccountLists: {
        type: Array,
        default: () => [],
      },

      rechargeMerchants: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        form: {
          transAmount: undefined,
          remark: '',
        },

        loading: false,
        saveLoading: false,

        rules: {
          merchantNo: [
            {
              required: true,
              message: '请选择商户号',
              trigger: 'change',
            },
          ],
          bankCode: [
            {
              required: true,
              message: '请选择付款银行',
              trigger: 'change',
            },
          ],
          cardNo: [
            {
              required: true,
              message: '请选择收款账户',
              trigger: 'change',
            },
          ],
          transAmount: [
            {
              //validator: validatePass,
              required: true,
              message: '请输入金额',
              trigger: 'blur',
            },
          ],
          remark: [
            {
              required: true,
              message: '请输入备注',
              trigger: 'blur',
            },
          ],
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          if (!value) {
            // 重置
            this.form = {};
            this.$refs.form.resetFields();
          }
          this.$emit('change', value);
        },
      },

      confirmDisable() {
        const { merchantNo, bankCode, cardNo, transAmount, remark } = this.form;
        if (merchantNo && bankCode && cardNo && transAmount && remark) {
          return false;
        }
        return true;
      },
    },

    methods: {
      handleConfirmClick() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.submit1();
          }
        });
      },
      async submit1() {
        this.saveLoading = true;
        const {
          merchantNo,
          bankCode,
          cardNo,
          transAmount,
          remark,
          tradeSeqNo,
        } = this.form;
        const body = {
          merchantNo,
          bankCode,
          cardNo,
          transAmount,
          remark,
          tradeSeqNo,
        };
        const { err, res } = await this.$apis.pingan.doGatewayRecharge(body);
        if (!err) {
          const { htmlStr } = res;

          let div = document.createElement('div');
          div.id = 'paydiv';
          div.innerHTML = htmlStr;
          document.body.appendChild(div);
          document.forms.payForm.submit();
          document.body.removeChild(document.getElementById('paydiv'));
          this.$message.success('操作成功');
          this.showDialog = false;
          this.$emit('success');
        }
        this.saveLoading = false;
      },
    },
  };
</script>
<style lang="scss" scoped>
  .add-dialog {
    ::v-deep {
      .el-input-number .el-input__inner {
        text-align: left;
      }
    }
  }
</style>
