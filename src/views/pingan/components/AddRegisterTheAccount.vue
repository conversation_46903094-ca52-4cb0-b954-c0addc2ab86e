<template>
  <div>
    <el-dialog width="600px" title="创建挂账单" :visible.sync="showDialog">
      <el-form ref="formData" :model="saveParams" label-width="140px">
        <el-form-item label="资金汇总账号">
          {{ saveParams.summaryAccountNo }}
        </el-form-item>

        <el-form-item
          label="见证子账号"
          prop="subAccountId"
          :rules="[
            {
              required: true,
              message: '请选择',
              trigger: 'change',
            },
          ]"
        >
          <el-select
            v-model="saveParams.subAccountId"
            filterable
            placeholder="请选择"
          >
            <el-option
              v-for="item in subAccountList"
              :key="item.dictValue"
              :label="item.dictDesc"
              :value="item.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          label="挂账金额"
          prop="hangAmount"
          :rules="[
            {
              required: true,
              message: '请输入',
              trigger: 'change',
            },
          ]"
        >
          <el-input
            v-model="saveParams.hangAmount"
            style="width: 340px"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item label="交易费用:">
          {{ saveParams.tradingCharge }}
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remark"
          :rules="[
            {
              required: true,
              message: '请输入',
              trigger: 'change',
            },
          ]"
        >
          <el-input
            v-model="saveParams.remark"
            style="width: 340px"
            placeholder="请输入"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="onOK">创 建</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getEssentialInfo, hangOrderCreate } from '@/api/pingan';
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      subAccountList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        id: null,
        saveParams: {
          summaryAccountNo: '',
          subAccountId: '',
          hangAmount: '',
          tradingCharge: '',
          remark: '',
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(val) {
        if (val) {
          getEssentialInfo().then(r => {
            if (r) {
              const { res } = r;
              this.saveParams.summaryAccountNo = res.summaryAccountNo;
              this.saveParams.tradingCharge = res.tradingCharge;
            }
          });
        } else {
          Object.assign(this.$data.saveParams, this.$options.data().saveParams);
          this.id = null;
          this.$nextTick(function () {
            this.$refs.formData.clearValidate();
          });
        }
      },
    },
    methods: {
      onOK() {
        this.$refs.formData.validate(valid => {
          if (!valid) return;
          console.log(this.saveParams, 'this.saveParams');

          hangOrderCreate(this.saveParams).then(res => {
            this.showDialog = false;
            this.$message.success('创建成功');
            this.$emit('onGet');
          });
        });
      },
    },
  };
</script>

<style></style>
