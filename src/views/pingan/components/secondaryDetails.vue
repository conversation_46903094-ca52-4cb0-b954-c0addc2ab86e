<template>
  <el-dialog
    :visible.sync="showDialog"
    :close-on-click-modal="false"
    width="700px"
  >
    <!-- <dynamictable
      :data-source="secondaryItem || []"
      :columns="getTotalAmountColumns()"
      :options="options"
      style="width: 100%"
      :array-span-method="arraySpanMethod"
    /> -->
    <dynamictable
      :data-source="secondaryItem || []"
      :columns="getTotalAmountColumns()"
      :options="options"
      style="width: 100%"
    />
  </el-dialog>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  export default {
    components: {
      dynamictable,
    },
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      secondaryItem: {
        type: Array,
        default: () => [],
      },
      type: {
        type: String,
        default: '1',
      },
    },
    data() {
      return {
        options: {
          loading: false,
          border: true,
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {
        if (!value) {
        }
      },
    },
    created() {},
    methods: {
      arraySpanMethod({ row, column, rowIndex, columnIndex }) {
        if (this.type === '1') return;
        if (columnIndex === 0 || columnIndex === 1) {
          if (row.isMerge) {
            return {
              rowspan: row.rowspan,
              colspan: 1,
            };
          } else {
            return {
              rowspan: 0,
              colspan: 0,
            };
          }
        }
      },

      getTotalAmountColumns() {
        if (this.type === '1') {
          return [
            {
              prop: 'goodAmount',
              label: '货款总额',
            },
            {
              prop: 'threeAmount',
              label: '三费总额',
            },
            {
              prop: 'serviceAmount',
              label: '服务费总额',
            },
          ];
        }

        return [
          // {
          //   prop: 'firstFeeTypeName',
          //   label: '款项类型',
          // },
          // {
          //   prop: 'firstFeeAmount',
          //   label: '费用类型金额',
          // },
          {
            prop: 'feeTypeName',
            label: '费用类型',
          },
          {
            prop: 'feeRate',
            label: '费用比例',
            render: ({ feeRate }) => <span>{0 > feeRate ? '-' : feeRate}</span>,
          },
          {
            prop: 'feeAmount',
            label: '费用金额',
          },
          {
            prop: 'inAccountName',
            label: '结算主体',
          },
        ];
      },
    },
  };
</script>
<style lang="scss"></style>
