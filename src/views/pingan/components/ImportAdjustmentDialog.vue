<template>
  <el-dialog
    title="导入调整单"
    :visible.sync="showDialog"
    :close-on-click-modal="false"
    width="600px"
  >
    <el-form
      ref="formData"
      :model="saveForm"
      label-width="160px"
      label-position="right"
    >
      <el-upload
        ref="upload"
        drag
        :data="saveForm"
        :action="action"
        :headers="headers"
        :on-success="onSuccess"
        accept=".xlsx, .xls"
        :auto-upload="false"
        style="text-align: center"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          <p>将文件拖到此处，或点击上传</p>
          <p>支持扩展名：.xlsx, .xls</p>
        </div>
      </el-upload>
      <el-divider></el-divider>
      <div>
        <h3>注意事项：</h3>
        <p>
          •
          上传后系统会进行解析处理，处理结果将会以邮件通知，处理完成后方体现在列表中。
        </p>
        <p>• 处理时间随上传文档数据量而上升，请耐心等待。</p>
        <p>
          • 系统会对导入内容进行校验，如有错误将通过邮件形式推送错误信息清单。
        </p>
        <p>• 导入进度在本页内可直观查看，读条状态下请勿关闭页面／刷新页面。</p>
        <p>
          • 导入完成后，请点击‘确定’提交任务至系统，如关闭／取消将视为放弃操作。
        </p>
      </div>

      <el-divider></el-divider>
      <div>
        <el-button type="text" icon="el-icon-paperclip" @click="onDownload">
          下载导入模板
        </el-button>
      </div>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="showDialog = false">取 消</el-button>
      <el-button type="primary" @click="handleOK">上 传</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { debounce } from '@/utils';
  import { getCookie } from '@/utils/auth';
  import injectHost from '@/utils/injectHost';
  import { replaceLocalDomain } from '@/utils/index.js';
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        saveForm: {
          chargeType: '001002',
        },
        headers: {
          token: getCookie(),
          appCode: process.env.VUE_APP_LOGIN_APP_CODE,
        }, // 导入头部信息
        action: '', // 导入接口
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {
        if (!value) {
          this.$nextTick(function () {
            this.$refs.upload.clearFiles();
          });
        }
      },
    },
    created() {
      // headers加参数
      let Base64 = require('js-base64').Base64;
      this.headers['userName'] = Base64.encode(this.$store.state.user.username);
      this.headers['userId'] = this.$store.state.user.userInfo.id;
      // 上传地址
      this.action =
        window.location.protocol +
        '//' +
        replaceLocalDomain(injectHost().apiHost) +
        '/api/finance-receivable/chargeItemDetail/importDetailData';
    },
    methods: {
      onSuccess(e, files) {
        if (!e.success) {
          this.$message.error(e.msg || '导入失败');

          return;
        }
        if (e.data && !e.data.importSuccess) {
          this.$message.error('excel文件错误');
          const uid = files.uid;

          const idx = this.$refs.upload.uploadFiles.findIndex(
            item => item.uid === uid,
          );
          this.$refs.upload.uploadFiles.splice(idx, 1); // 去除文件列表失败文件
          this.$emit('onErrDownload', e.data.errorFileId);
          return;
        }
        this.$message.success('导入成功');
        this.showDialog = false;
        this.$emit('onGet');
      },
      onDownload() {
        this.$emit('onDownload', this.saveForm);
      },
      handleOK: debounce(function () {
        this.$refs.upload.submit();
      }, 1000),
    },
  };
</script>
<style lang="scss"></style>
