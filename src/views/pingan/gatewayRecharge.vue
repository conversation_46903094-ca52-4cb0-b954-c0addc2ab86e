<template lang="pug">
.vertical.top.vcenter.padding-bottom-10
  el-form(inline label-suffix=':')
    el-form-item(label='付款银行')
      el-select(
        v-model='payerBankCode'
        placeholder='请选择'
        style='width: 200px'
        :loading='optionLoading'
        filterable
        clearable
      )
        el-option(
          v-for='item in rechargeSupportBanks'
          :key='item.bankCode'
          :label='item.bankName'
          :value='item.bankCode'
        )
    el-form-item(label='收款子账户')
      el-select(
        v-model='payeeAccountNo'
        placeholder='请选择'
        style='width: 200px'
        :loading='optionLoading'
        filterable
        clearable
      )
        el-option(
          v-for='item in subAccountLists'
          :key='item.accountNo'
          :label='item.accountName'
          :value='item.accountNo'
        )
    el-form-item(label='状态')
      el-select(
        v-model='status'
        placeholder='请选择'
        style='width: 200px'
        :loading='optionLoading'
        filterable
        clearable
      )
        el-option(
          v-for='item in statusList'
          :key='item.dictValue'
          :label='item.dictDesc'
          :value='item.dictValue'
        )

    el-form-item(label='下单时间')
      el-date-picker(
        v-model='time'
        type='datetimerange'
        range-separator='至'
        start-placeholder='开始日期'
        end-placeholder='结束日期'
        value-format='yyyy-MM-dd HH:mm:ss'
      )
    el-button.margin-left-10(
      type='primary'
      icon='el-icon-search'
      @click='onSearchClick'
    ) 查询
    el-button.margin-left-10(
      type='primary'
      icon='el-icon-money'
      @click='onAddClick'
    ) 充值
  el-table.margin-top-20(
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
  )
    el-table-column(prop='tradeSeqNo' label='充值订单号')
    el-table-column(prop='payerBankCode' label='付款银行')
    el-table-column(prop='payeeAccountName' label='收款户名')
    el-table-column(prop='payeeAccountNo' label='收款户号')
    el-table-column(prop='tradeAmt' label='金额')
    el-table-column(prop='status' label='状态')
    el-table-column(prop='remark' label='备注')
    el-table-column(prop='createTime' label='下单时间')

  el-pagination(
    :current-page='m_current'
    :page-sizes='[10, 15, 20, 30]'
    :page-size='m_pageSize'
    layout='total, sizes, prev, pager, next, jumper'
    :total='m_total'
    background
    @size-change='m_handleSizeChange'
    @current-change='m_handleCurrentChange'
  )

  gateway-recharge-info-dialog(
    v-model='showDialog'
    :recharge-support-banks='rechargeSupportBanks'
    :sub-account-lists='subAccountLists'
    :recharge-merchants='rechargeMerchants'
    @success='m_loadData'
  )
</template>
<script>
  import dayjs from 'dayjs';
  import { paginationMixin } from '@/mixins/tableMixin';
  import GatewayRechargeInfoDialog from './components/GatewayRechargeInfoDialog';
  const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';

  export default {
    components: {
      GatewayRechargeInfoDialog,
    },
    mixins: [paginationMixin],
    data() {
      return {
        loading: false,
        optionLoading: false,
        showDialog: false,
        payerBankCode: null,
        payeeAccountNo: null,
        rechargeSupportBanks: [],
        subAccountLists: [],
        statusList: [],
        rechargeMerchants: [],
        currentRecord: null,
        status: null,
        list: [],
        time: [],
      };
    },
    async created() {
      this.time = [
        dayjs().startOf('day').format(DATE_TIME_FORMAT),
        dayjs().endOf('day').format(DATE_TIME_FORMAT),
      ];
      await this.loadOptions();
      this.m_loadData();
    },
    methods: {
      async loadOptions() {
        const {
          err,
          res,
        } = await this.$apis.pingan.getGatewayRechargeOptions();
        if (!err) {
          const {
            subAccountLists,
            rechargeSupportBanks,
            statusList,
            rechargeMerchants,
          } = res;
          this.subAccountLists = subAccountLists;
          this.rechargeSupportBanks = rechargeSupportBanks;
          this.statusList = statusList;
          this.rechargeMerchants = rechargeMerchants;
        }
      },
      async m_loadData() {
        this.loading = true;
        const body = {
          current: this.m_current,
          pageSize: this.m_pageSize,
        };

        if (this.payerBankCode) {
          body.payerBankCode = this.payerBankCode;
        }
        if (this.payeeAccountNo) {
          body.payeeAccountNo = this.payeeAccountNo;
        }

        if (this.status) {
          body.status = this.status;
        }

        if (this.time && this.time.length > 0) {
          const [startTime, endTime] = this.time;
          body.beginCreateTime = startTime;
          body.endCreateTime = endTime;
        }

        const { err, res } = await this.$apis.pingan.getGatewayRechargeList(
          body,
        );
        if (!err && res) {
          const { total, records } = res;
          this.m_total = total;
          this.list = records;
        }
        this.loading = false;
      },
      onSearchClick() {
        this.m_current = 1;
        this.m_loadData();
      },
      onAddClick() {
        this.currentRecord = null;
        this.showDialog = true;
      },
    },
  };
</script>
