<template>
  <div>
    <el-form inline>
      <el-form-item label="贸易主体:">
        <el-select
          v-model="searchParams.accountNo"
          placeholder="请选择贸易主体"
          filterable
          style="width: 300px"
        >
          <el-option
            v-for="item in subjectAccount"
            :key="item.accountNo"
            :label="item.accountName"
            :value="item.accountNo"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button type="primary" @click="handleImport">模版导出</el-button>
        <!-- <el-upload
          :action="action"
          :headers="headers"
          :data="uploadData"
          style="margin-left: 10px; display: initial"
          :on-success="onSuccess"
          :show-file-list="false"
          accept=".xlsx, .xls, .xltx"
        >
          <el-button type="primary">导入</el-button>
        </el-upload> -->
        <uoloadFile
          style="margin-left: 10px; display: initial"
          accept=".xlsx, .xls, .xltx"
          @onSuccess="onSuccess"
        ></uoloadFile>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import uoloadFile from '@/components/uoloadFile';
  import {
    secondaryConfigQuery,
    secondaryTradeSubjectQuery,
    secondaryUploadConfig,
    secondaryTemplateImport,
  } from '@/api/pingan';
  import { debounce } from '@/utils';

  export default {
    components: {
      dynamictable,
      uoloadFile,
    },

    data() {
      return {
        searchParams: {
          accountNo: '',
        },
        list: [],
        subjectAccount: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
      };
    },
    created() {
      secondaryTradeSubjectQuery().then(({ res, err }) => {
        if (res && !err) {
          this.subjectAccount = res;
        }
      });
      this.getList(true);
    },
    methods: {
      handleImport: debounce(async function () {
        const { res, err } = await secondaryTemplateImport();

        if (res && !err) {
          this.$message.success('导出成功');

          window.location.href = res;
        }
      }, 1000),

      async onSuccess(e) {
        if (!e.success) {
          this.$message.error(e.msg || '导入失败');

          return;
        }
        if (e.data) {
          const { res, err } = await secondaryUploadConfig({
            fileId: e.data.id,
          });
          if (!err) {
            this.$message.success('导入成功');
            this.getList(true);
          }
        }
      },
      getParams() {
        const params = {
          ...this.searchParams,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const { res, err } = await secondaryConfigQuery(params);
        if (res && !err) {
          this.list = res ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        }
        this.options.loading = false;
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },

      getColumns() {
        const columns = [
          {
            prop: 'id',
            label: 'ID',
          },
          {
            prop: 'outAccountName',
            label: '贸易主体',
          },
          {
            prop: 'feeName',
            label: '费用类型',
          },
          {
            prop: 'feeRate',
            label: '订单比例',
          },
          {
            prop: 'inAccountName',
            label: '费用结算主体',
          },
          {
            prop: 'updateTime',
            label: '最后更新时间',
          },
          {
            prop: 'creator',
            label: '申请人',
          },
          {
            prop: 'createTime',
            label: '生效期间',
          },
        ];
        return columns;
      },
    },
  };
</script>
<style lang="scss"></style>
