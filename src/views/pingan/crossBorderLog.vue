<!--
 * @Author: 七七
 * @Date: 2022-05-11 15:18:19
 * @LastEditors: 七七
 * @LastEditTime: 2022-05-24 10:45:40
 * @FilePath: /access-fmis-web/src/views/pingan/crossBorderLog.vue
-->
<template>
  <div>
    <el-form :model="search" inline>
      <el-form-item label="提交申报日期">
        <el-date-picker
          v-model="search.date"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          clearable
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="资金名称">
        <el-input
          v-model="search.capitalSubjectName"
          placeholder="请输入资金名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="贸易主体">
        <el-input
          v-model="search.tradeSubjectName"
          placeholder="请输入贸易主体"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="明细"
          @click="handleDetail(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
</template>
<script>
  import { fundDeclarationLogList } from '@/api/pingan';
  import dynamictable from '@/components/dynamic-table';
  import dayjs from 'dayjs';
  export default {
    components: { dynamictable },
    data() {
      let columns = [
        {
          prop: 'capitalSubjectName',
          label: '资金主体',
        },
        {
          prop: 'tradeSubjectName',
          label: '贸易主体',
        },
        {
          prop: 'declareAmount',
          label: '申报金额',
        },
        {
          prop: 'declareTime',
          label: '提交申报时间',
        },
        {
          prop: 'status',
          label: '状态',
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          minWidth: '100',
          maxWidth: '300',
          scopedSlots: { customRender: 'operation' },
        },
      ];
      return {
        search: {
          date: [],
        },
        searchCopy: {},
        selectNum: null,
        selectFund: null,
        selectionIds: [],
        showCheckDialog: false,
        statusList: [],
        typeList: [],
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          index: true,
          indexName: 'ID',
          mutiSelect: true,
        },
        columns,
      };
    },
    mounted() {
      const endTime = dayjs();
      const startTime = endTime.subtract(1, 'month');
      this.search.date = [
        startTime.format('YYYY-MM-DD 00:00:00'),
        endTime.format('YYYY-MM-DD 23:59:59'),
      ];
      this.getList();
    },
    methods: {
      async getList() {
        this.options.loading = true;
        this.searchCopy = { ...this.search };
        const body = this.getParams();
        try {
          const { res, err } = await fundDeclarationLogList(body);
          if (res && !err) {
            this.list = res ? res.records : [];
            this.pagination.total = res ? res.total : 0;
          }
        } catch (e) {}
        this.options.loading = false;
      },
      getParams() {
        const body = {
          pageNumber: this.pagination.pageSize,
          pageSize: this.pagination.pageLimit,
        };
        if (this.searchCopy.date?.length > 0) {
          const startTime = this.searchCopy.date[0];
          const endTime = this.searchCopy.date[1];
          body.starTime =
            startTime.length === 10 ? startTime + ' 00:00:00' : startTime;
          body.endTime =
            endTime.length === 10 ? endTime + ' 23:59:59' : endTime;
        }
        if (this.searchCopy.capitalSubjectName?.length > 0) {
          body.capitalSubjectName = this.searchCopy.capitalSubjectName;
        }
        if (this.searchCopy.tradeSubjectName?.length > 0) {
          body.tradeSubjectName = this.searchCopy.tradeSubjectName;
        }
        return body;
      },
      handleSearch() {
        this.pagination.pageSize = 1;
        this.getList();
      },
      handleDetail(row) {
        this.$router.push({
          name: 'crossBorderManage',
          query: {
            serialNo: row.serialNo,
          },
        });
      },
      reset() {
        this.search = {
          date: [],
        };
        const endTime = dayjs();
        const startTime = endTime.subtract(1, 'month');
        this.search.date = [
          startTime.format('YYYY-MM-DD HH:mm:ss'),
          endTime.format('YYYY-MM-DD HH:mm:ss'),
        ];
      },
    },
  };
</script>
<style scoped></style>
