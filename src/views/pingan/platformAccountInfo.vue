<template lang="pug">
.vertical.top.left(v-loading='loading')
  .horizontal.left.bottom
    .vertical.left.vcenter
      ac-tag-value(tag='CNY汇总账户金额' :value='summaryAccountBal' width='150px')
      ac-tag-value.margin-top-10(
        tag='平台子账户总额'
        :value='platformSubAccountTotalBal'
        width='150px'
      )
    .vertical.left.vcenter.margin-right-20(style='min-width: 300px')
      ac-tag-value(tag='提现银行' :value='withdrawBankName' width='150px')
      ac-tag-value.margin-top-10(
        tag='提现账号'
        :value='withdrawCardNo'
        width='150px'
      )
    el-button(type='primary' @click='loadData') 查询
  el-table.margin-top-20(
    v-loading='loading'
    :data='list'
    align='center'
    style='width: 100%'
    empty-text='暂无数据'
  )
    el-table-column(prop='accountNo' label='子账户ID')
    el-table-column(prop='accountName' label='子账户名')
    el-table-column(prop='availableBal' label='子账户余额')
    el-table-column(prop='sourceType' label='操作')
      template(v-slot='{ $index, row }')
        ac-permission-button(
          v-if='row.canWithdraw'
          btn-text='提现'
          permission-key='platformAccountInfo-reflect'
          @click='onRowClick(row, $index)'
        )
        ac-permission-button(
          btn-text='明细'
          permission-key='platformAccountInfo-reflect'
          @click='jump(row)'
        )
        ac-permission-button(
          v-if='row.canTransfer'
          btn-text='转账'
          permission-key='platformAccountInfo-reflect'
          @click='onRowTransfer(row, $index)'
        )
  account-withdrawal-dialog(
    v-model='showDialog'
    :record='currentRecord'
    @success='onWithdrawalSuccess'
  )
  account-transfer-dialog(
    v-model='showTransferDialog'
    :record='currentRecord'
    :member-sub-account-list='accountList'
    :transfer-type-list='transferTypeList'
    @success='onTransferSuccess'
  )
</template>
<script>
  import AccountWithdrawalDialog from './components/AccountWithdrawalDialog.vue';
  import AccountTransferDialog from './components/AccountTransferDialog.vue';
  import {
    getMemberSubAccountOptions,
    getOpenSubAccountOptions,
    bindingCardsList,
    subAccountAudit,
    subAccountOpeningCheck,
    fundSelector,
  } from '@/api/pingan';
  export default {
    name: 'PlatformAccountInfo',
    components: {
      AccountWithdrawalDialog,
      AccountTransferDialog,
    },
    data() {
      return {
        loading: false,
        list: [],
        platformSubAccountTotalBal: 0,
        summaryAccountBal: 0,
        withdrawBankName: '',
        withdrawCardNo: '',
        showDialog: false,
        showTransferDialog: false,
        currentRecord: {},
        memberSubAccountList: [],
        transferTypeList: [], //转账类型
        typeOptions: [], // 二级账户类型下拉数据
        accountList: [], //员子账户列表
        idTypeList: [],
        statusList: [],
      };
    },
    created() {
      this.loadData();
      this.loadOptions();
    },
    methods: {
      async loadOptions() {
        Promise.all([
          getMemberSubAccountOptions(),
          getOpenSubAccountOptions(),
          fundSelector(),
        ])
          .then(res => {
            if (res && res.length) {
              const {
                statusList,
                memberSubAccountTypeList,
                memberSubAccountList,
              } = res[0] && res[0].res;
              const { idTypeList } = res[1] && res[1].res;
              const { accountList, transferTypeList } = res[2] && res[2].res;
              this.memberSubAccountList = memberSubAccountList;
              this.typeOptions = memberSubAccountTypeList;
              this.accountList = accountList;
              this.transferTypeList = transferTypeList;
              this.statusList = statusList;
              this.idTypeList = idTypeList;
              console.log(idTypeList, 'idTypeList');
            }
          })
          .catch(err => {});
      },
      async loadData() {
        this.loading = true;
        const {
          err,
          res,
        } = await this.$apis.pingan.getPlatformSubAccountList();
        if (!err) {
          const {
            platformSubAccountTotalBal,
            summaryAccountBal,
            withdrawBankName,
            withdrawCardNo,
            list,
          } = res;
          this.list = list;
          this.platformSubAccountTotalBal = platformSubAccountTotalBal;
          this.summaryAccountBal = summaryAccountBal;
          this.withdrawBankName = withdrawBankName;
          this.withdrawCardNo = withdrawCardNo;
          console.log(withdrawCardNo, 'withdrawCardNo');
        }

        this.loading = false;
      },
      onRowClick(row, index) {
        this.currentRecord = {
          ...row,
          accountTypeDesc: '平台子账户',
          bankName: this.withdrawBankName,
          cardNo: this.withdrawCardNo,
        };
        this.showDialog = true;
      },
      onRowTransfer(row, index) {
        this.currentRecord = {
          ...row,
          accountTypeDesc: '营销子账户',
        };
        this.showTransferDialog = true;
      },
      onWithdrawalSuccess() {
        this.loadData();
      },
      onTransferSuccess() {
        this.loadData();
      },
      jump(row) {
        this.$router.push({
          path: 'fundSplitDetail',
          query: { accountNo: row.accountNo },
        });
      },
    },
  };
</script>
