<!--
 * @Description: 
 * @Author: 项萍～
 * @Date: 2021-08-16 10:05:27
 * @LastEditTime: 2021-08-24 15:50:23
 * @LastEditors: 项萍～
 * @Reference: 
-->
<template>
  <div class="journal">
    <el-form ref="searchForm" :model="searchForm" label-width="120px" inline>
      <el-form-item label="主体名称：" prop="subsidiaryCode">
        <el-select
          v-model="searchForm.subsidiaryCode"
          placeholder="请选择"
          clearable
          filterable
        >
          <el-option
            v-for="(item, index) in subsidiaryList"
            :key="index"
            :label="item.label"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="凭证编号：" prop="externalId">
        <el-input v-model="searchForm.externalId"></el-input>
      </el-form-item>
      <el-form-item label="OA流程编号：" prop="businessId">
        <el-input v-model="searchForm.businessId"></el-input>
      </el-form-item>
      <el-form-item label="OA流程类型" prop="businessLine">
        <el-select
          v-model="searchForm.businessLine"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="(item, index) in businessList"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="凭证状态：" prop="status">
        <el-select
          v-model="searchForm.status"
          placeholder="请选择状态"
          clearable
        >
          <el-option
            v-for="(item, index) in statusList"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="凭证创建日期：" prop="depotId">
        <el-date-picker
          v-model="searchDate"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        ></el-date-picker>
      </el-form-item>

      <el-form-item style="margin-left: 20px">
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
        <el-popover placement="bottom" trigger="click">
          <p style="text-align: center">选择凭证创建时间</p>
          <div>
            凭证创建日期:
            <el-date-picker
              v-model="exportDate"
              :clearable="true"
              type="daterange"
              range-separator="至"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
            ></el-date-picker>
          </div>

          <div style="text-align: right; margin: 10px 0 0">
            <el-button type="primary" size="mini" @click="onExport">
              确定
            </el-button>
          </div>
          <el-button slot="reference" style="margin-left: 10px" type="primary">
            导出
          </el-button>
        </el-popover>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <el-button type="text" size="small" @click="toDetail(scope.row)">
          查看明细
        </el-button>
      </template>
    </dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    getJournalList,
    getSubsidiaryList,
    invoiceDownload,
  } from '@/api/journal';
  import { exportExcel } from '@/api/blob';
  import { parseTime, downloadFile } from '@/utils';
  export default {
    components: {
      dynamictable,
    },

    data() {
      let columns = [
        {
          prop: 'externalId',
          label: '凭证编号',
          width: '150px',
        },
        {
          prop: 'businessLine',
          label: 'OA流程类型',
          render: ({ businessLine }) => (
            <span>
              {businessLine == 'travel'
                ? '差旅'
                : businessLine == 'welfare'
                ? '福利'
                : businessLine == 'other'
                ? '其他'
                : ''}
            </span>
          ),
        },
        {
          prop: 'status',
          label: '凭证状态',
          render: ({ status }) => (
            <span>
              {status == 'U'
                ? '新建'
                : status == 'S'
                ? '成功'
                : status == 'F'
                ? '失败'
                : ''}
            </span>
          ),
        },
        {
          prop: 'currency',
          label: '凭证币种',
        },
        {
          prop: 'subsidiaryName',
          label: '主体名称',
        },
        {
          prop: 'createTime',
          label: '凭证创建日期',
          width: 180,
        },
        {
          prop: 'businessId',
          label: '关联OA流程编号',
          width: '150px',
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '120',
          scopedSlots: { customRender: 'operation' },
        },
      ];
      return {
        exportDate: '',
        searchDate: null,
        searchForm: {
          businessId: '', //oa流程编号
          businessLine: '', //业务线
          endCreateTime: '', //截止创建时间
          startCreateTime: '', //开始创建时间
          externalId: '', //凭证号
          subsidiaryCode: '', // 主体公
          status: '', //记账状态
        },
        subsidiaryList: [],
        businessList: [
          { label: '差旅', value: 'travel' },
          { label: '福利', value: 'welfare' },
          { label: '其他', value: 'other' },
        ],
        statusList: [
          { label: '新建', value: 'U' },
          { label: '成功', value: 'S' },
          { label: '失败', value: 'F' },
        ],
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
        columns,
      };
    },

    created() {
      this.getList(true);
      this.getSubsidiaryList();
    },
    methods: {
      onExport() {
        const { exportDate } = this;
        if (!exportDate) {
          this.$message({
            message: '请选择导出时间',
            type: 'warning',
          });
          return;
        }
        const params = {
          from: exportDate ? exportDate[0] : '',
          to: exportDate ? exportDate[1] : '',
        };
        exportExcel(params, '/api/magpie-bridge/invoice/download', 'get').then(
          res => {
            downloadFile(res.data, '凭证列表');
          },
        );
      },
      // 获取公司主体名称
      async getSubsidiaryList() {
        this.subsidiaryList = [];
        let params = {
          limit: 20,
          pageNo: 1,
          name: '',
        };
        const res = await getSubsidiaryList(params);
        res.records.forEach(ele => {
          let obj = {
            label: ele.subsidiaryName,
            code: ele.subsidiaryCode,
          };
          this.subsidiaryList.push(obj);
        });
      },
      getParams() {
        this.searchForm.startCreateTime = this.searchDate
          ? this.searchDate[0]
          : '';
        this.searchForm.endCreateTime = this.searchDate
          ? this.searchDate[1]
          : '';
        const params = {
          ...this.searchForm,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        console.log(params);
        this.options.loading = true;
        const res = await getJournalList(params);
        this.options.loading = false;
        this.list = res ? res.records : [];
        this.pagination.total = res ? res.total : 0;
      },
      // 重置
      resetForm() {
        this.searchDate = null;
        this.$refs.searchForm.resetFields();
        this.getList(true);
      },
      toDetail(row) {
        this.$router.push({
          path: './journalDetail',
          query: {
            id: row.id,
            externalId: row.externalId,
            businessLine: row.businessLine,
            status: row.status,
          },
        });
      },
    },
  };
</script>
<style></style>
