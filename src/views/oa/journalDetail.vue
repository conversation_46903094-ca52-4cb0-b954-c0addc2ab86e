<!--
 * @Description: 
 * @Author: 项萍～
 * @Date: 2021-08-16 15:25:47
 * @LastEditTime: 2021-08-17 14:45:39
 * @LastEditors: 项萍～
 * @Reference: 
-->
<template>
  <div class="journalDetail">
    <el-row :gutter="20">
      <el-col :span="6">凭证编号：{{ $route.query.externalId }}</el-col>
      <el-col :span="6">
        OA流程类型：{{
          $route.query.businessLine == 'travel'
            ? '差旅'
            : $route.query.businessLine == 'welfare'
            ? '福利'
            : $route.query.businessLine == 'other'
            ? '其他'
            : ''
        }}
      </el-col>
      <el-col :span="6">
        凭证状态：{{
          $route.query.status == 'U'
            ? '新建'
            : $route.query.status == 'S'
            ? '成功'
            : $route.query.status == 'F'
            ? '失败'
            : ''
        }}
      </el-col>
    </el-row>
    <el-table :data="tableData" style="margin-top: 20px" border>
      <el-table-column
        v-for="(th, index) in tableHeader"
        :key="index"
        :prop="th.prop"
        :label="th.label"
        :width="th.width || ''"
        align="center"
      ></el-table-column>
    </el-table>
  </div>
</template>

<script>
  import { getDetail } from '@/api/journal';
  export default {
    data() {
      return {
        tableHeader: [
          {
            prop: 'accountName',
            label: '会计科目名称',
            width: '120px',
          },
          {
            prop: 'accountInternalId',
            label: '会计科目内部标识',
            width: '150px',
          },
          {
            prop: 'debit',
            label: '借记（原币）',
            width: '120px',
          },
          {
            prop: 'credit',
            label: '贷记（原币）',
            width: '120px',
          },
          {
            prop: 'memo',
            label: '凭证摘要',
            width: '120px',
          },
          {
            prop: 'brandName',
            label: '品牌名称',
            width: '120px',
          },
          {
            prop: 'brandInternalId',
            label: '品牌内部标识',
            width: '120px',
          },
          {
            prop: 'cseCode',
            label: '客户/供应商编号',
            width: '130px',
          },
          {
            prop: 'className',
            label: '渠道名称',
          },
          {
            prop: 'departmentCode',
            label: '成本中心编码',
            width: '120px',
          },
          {
            prop: 'projectCode',
            label: '项目号编号',
            width: '120px',
          },
          {
            prop: 'tranTypeName',
            label: '交易类型',
            width: '120px',
          },
        ],
        tableData: [],
      };
    },
    created() {
      this.getDetail();
    },
    methods: {
      async getDetail() {
        const res = await getDetail(this.$route.query.id);
        this.tableData = res;
      },
    },
  };
</script>

<style></style>
