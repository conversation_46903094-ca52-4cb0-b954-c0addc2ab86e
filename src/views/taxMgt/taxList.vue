<template>
  <div class="taxList">
    <el-form
      ref="searchForm"
      :model="searchForm"
      inline
      class="search-form"
      label-position="top"
    >
      <el-row type="flex" class="search-row" :gutter="20">
        <el-col :span="6">
          <el-form-item label="公司：">
            <el-select
              v-model="searchForm.companyName"
              placeholder="请选择类型"
              style="width: 100%"
              filterable
              clearable
            >
              <el-option
                v-for="item in companyNames"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="查询类型：">
            <el-select
              v-model="searchForm.dateType"
              placeholder="请选择类型"
              style="width: 100%"
              clearable
            >
              <el-option label="归属期" value="归属期"></el-option>
              <el-option label="入库期" value="入库期"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item v-if="searchForm.dateType === '归属期'" label="归属期">
            <el-date-picker
              v-model="searchForm.fiscalPeriod"
              type="monthrange"
              align="right"
              format="yyyy-MM"
              unlink-panels
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptions"
            ></el-date-picker>
          </el-form-item>
          <el-form-item v-else label="入库期">
            <el-date-picker
              v-model="searchForm.storagePeriod"
              type="monthrange"
              align="right"
              unlink-panels
              format="yyyy-MM"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptions"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="属地：">
            <el-select
              v-model="searchForm.location"
              placeholder="请选择类型"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="item in locations"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="操作">
            <div style="width: 100%">
              <el-button type="primary" @click="getList(true)">查询</el-button>
              <el-button @click="resetForm()">重置</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row
      class="search-form"
      style="margin-bottom: 10px"
      type="flex"
      :gutter="20"
    >
      <el-col :span="6">
        <span style="display: flex; gap: 5px">
          <AIUploadButton @upload-success="getList(true)" />
          <el-button @click="exportFile(true)">导出</el-button>
        </span>
      </el-col>
    </el-row>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="companyName" slot-scope="scope">
        <el-tag>{{ scope.row.companyName }}</el-tag>
      </template>
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="编辑"
          @click="editorAction(scope.row)"
        ></ac-permission-button>
        <ac-permission-button
          slot="reference"
          size="small"
          type="text"
          confirm-button-type="danger"
          slot-btn="reference"
          node-type="popconfirm"
          title="确定删除吗?"
          btn-text="删除"
          @click="removeRow(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>

    <el-dialog
      title="编辑"
      :visible.sync="dialogVisible"
      width="70vw"
      :destroy-on-close="true"
    >
      <el-table :data="[currentRow]">
        <el-table-column label="公司" width="180">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{ scope.row.companyName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="归属期" width="180">
          <template slot-scope="scope">
            <span style="margin-left: 10px">
              {{ scope.row.financialPeriod }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="入库期" width="180">
          <template slot-scope="scope">
            <span style="margin-left: 10px">{{ scope.row.storagePeriod }}</span>
          </template>
        </el-table-column>
        <el-table-column label="币种" width="180">
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.currency"
              placeholder="请选择类型"
              style="width: 100%"
              filterable
              clearable
            >
              <el-option
                v-for="item in exchangeRateList"
                :key="item.id"
                :label="`${item.currencyCode}(${item.currencyChineseName})`"
                :value="item.currencyCode"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="收入(CNY)" width="180">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.incomeCny"
              controls-position="right"
            >
              {{ scope.row.incomeCny }}
            </el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="利润(CNY)" width="180">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.profitCny"
              controls-position="right"
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="增值税" width="180">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.valueAddedTax"
              controls-position="right"
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="附加税" width="180">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.additionalTax"
              controls-position="right"
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="企业所得税" width="180">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.corporateIncomeTax"
              controls-position="right"
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="印花税" width="180">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.stampDuty"
              controls-position="right"
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="员工福利税" width="180">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.employeeBenefitTax"
              controls-position="right"
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="工资税Payroll Tax" width="180">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.payrollTax"
              controls-position="right"
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="消费税" width="180">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.consumptionTax"
              controls-position="right"
            ></el-input-number>
          </template>
        </el-table-column>
        <!--        <el-table-column label="进口增值税" width="180">-->
        <!--          <template slot-scope="scope">-->
        <!--            <el-input-number-->
        <!--              v-model="scope.row.importVat"-->
        <!--              controls-position="right"-->
        <!--            ></el-input-number>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <!--        <el-table-column label="进口关税" width="180">-->
        <!--          <template slot-scope="scope">-->
        <!--            <el-input-number-->
        <!--              v-model="scope.row.importTariff"-->
        <!--              controls-position="right"-->
        <!--            ></el-input-number>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <!--        <el-table-column label="进口消费税" width="180">-->
        <!--          <template slot-scope="scope">-->
        <!--            <el-input-number-->
        <!--              v-model="scope.row.importConsumptionTax"-->
        <!--              controls-position="right"-->
        <!--            ></el-input-number>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column label="个人所得税" width="180">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.personalIncomeTax"
              controls-position="right"
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="其他税费" width="180">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.otherTaxes"
              controls-position="right"
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="车辆购置税" width="180">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.vehiclePurchaseTax"
              controls-position="right"
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="残疾人就业保障金" width="180">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.disabledEmploymentSecurityFund"
              controls-position="right"
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="文化事业建设费" width="180">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.culturalConstructionFee"
              controls-position="right"
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="税务部门罚款" width="180">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.taxAuthorityFine"
              controls-position="right"
            ></el-input-number>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitAction">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    deleteTaxRowData,
    exportTax,
    getListData,
    listOptions,
    updateTaxRowData,
  } from '@/api/taxList';

  import { getNpPaymentRequisitionDic } from '@/api/settle';
  import AIUploadButton from './components/AiUpload.vue';
  import { getCurrencyList } from '@/api/exchange';
  import { mapGetters } from 'vuex';

  export default {
    name: 'TaxList',
    components: {
      dynamictable,
      AIUploadButton,
    },
    data() {
      let columns = [
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '100',
          scopedSlots: { customRender: 'operation' },
        },
      ];

      return {
        exchangeRateList: [],
        dialogVisible: false,
        searchForm: {
          dateType: '归属期',
          companyName: null,
          fiscalPeriod: null,
          storagePeriod: null,
          location: null,
        },
        actionType: 'look',
        currentRow: [],
        companyNames: [],
        locations: [],
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
        columns,
        pickerOptions: {
          shortcuts: [
            {
              text: '最近一个月',
              onClick(picker) {
                const start = new Date();
                start.setMonth(start.getMonth() - 1);
                start.setDate(1);
                start.setHours(0, 0, 0, 0);

                const end = new Date();
                end.setMonth(end.getMonth() + 1, 0); // 当前月最后一天
                end.setHours(23, 59, 59, 999);
                end.setDate(1);

                picker.$emit('pick', [start, end]);
              },
            },
            {
              text: '最近三个月',
              onClick(picker) {
                const start = new Date();
                start.setMonth(start.getMonth() - 2); // 推 2 个自然月前
                start.setDate(1);
                start.setHours(0, 0, 0, 0);

                const end = new Date();
                end.setMonth(end.getMonth() + 1, 0); // 当前月最后一天
                end.setHours(23, 59, 59, 999);
                end.setDate(1);

                picker.$emit('pick', [start, end]);
              },
            },
            {
              text: '最近一年',
              onClick(picker) {
                const start = new Date();
                start.setMonth(start.getMonth() - 11); // 11 个月前
                start.setDate(1);
                start.setHours(0, 0, 0, 0);

                const end = new Date();
                end.setMonth(end.getMonth() + 1, 0); // 当前月最后一天
                end.setHours(23, 59, 59, 999);
                end.setDate(1);

                picker.$emit('pick', [start, end]);
              },
            },
            {
              text: '最近两年',
              onClick(picker) {
                const start = new Date();
                start.setMonth(start.getMonth() - 23); // 23 个月前
                start.setDate(1);
                start.setHours(0, 0, 0, 0);

                const end = new Date();
                end.setMonth(end.getMonth() + 1, 0); // 当前月最后一天
                end.setHours(23, 59, 59, 999);
                end.setDate(1);

                picker.$emit('pick', [start, end]);
              },
            },
          ],
        },
      };
    },
    computed: {
      ...mapGetters({
        avatar: 'user/avatar',
        username: 'user/username',
      }),
    },
    created() {
      this.getList(true);
      this.screen();
    },
    mounted() {
      listOptions().then(res => {
        this.companyNames = res.companyNames;
        this.locations = res.locations;
      });

      getCurrencyList().then(res => {
        this.exchangeRateList = res;
      });
    },
    methods: {
      // 获取公司主体名称
      async screen() {
        getNpPaymentRequisitionDic().then(res => {
          if (res) {
            this.paymentStatusList = res.paymentStatus;
            this.businessLineList = res.businessLine;
          }
        });
      },
      getParams() {
        return {
          ...this.searchForm,
          size: this.pagination.pageLimit,
          current: this.pagination.pageSize,
        };
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await getListData(params);
        this.options.loading = false;
        this.columns = res.columns || [];
        this.columns.push({
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '100',
          scopedSlots: { customRender: 'operation' },
        });
        this.list = res ? res.records : [];
        this.pagination.total = res ? res.total : 0;
      },
      async removeRow(row) {
        await deleteTaxRowData({
          fiscalPeriod: row.financialPeriod,
          storagePeriod: row.storagePeriod,
          companyName: row.companyName,
        });
        await this.getList(true);
      },
      async exportFile(isSearch) {
        this.getList(isSearch);
        const params = this.getParams();
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始
        const day = String(now.getDate()).padStart(2, '0');
        const fileName = `${year}-${month}-${day}-${this.username}-taxReport.xlsx`;
        exportTax(params, encodeURIComponent(fileName));
      },
      editorAction(row) {
        this.actionType = 'edit';
        this.currentRow = row;
        this.dialogVisible = true;
      },
      async submitAction() {
        try {
          await updateTaxRowData(this.currentRow);
          await this.getList(true);
          this.dialogVisible = false;
        } catch (e) {
          console.log(e);
        }
      },
      // 重置
      resetForm() {
        Object.assign(this.$data.searchForm, this.$options.data().searchForm);
        this.getList(true);
      },
    },
  };
</script>
<style lang="scss" scoped>
  .taxList {
    /deep/ .el-form-item {
      width: 100% !important;
    }
  }
</style>
