<template>
  <div>
    <el-dialog
      width="600px"
      title="新增类目"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="saveForm"
        :rules="rulesForm"
        label-width="100px"
      >
        <el-form-item label="国家" prop="countryId">
          <el-select
            v-model="saveForm.countryId"
            placeholder="请选择国家"
            style="width: 80%"
          >
            <el-option
              v-for="item in countryList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
              :disabled="item.name != '中国'"
              @click.native="selectCountry(item)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="税务编码" prop="taxNo">
          <el-input
            v-model="saveForm.taxNo"
            placeholder="请输入税务编码"
            style="width: 80%"
          ></el-input>
        </el-form-item>
        <el-form-item label="税务类目" prop="categoryId1">
          <el-select
            :value="saveForm.categoryId1"
            filterable
            allow-create
            default-first-option
            placeholder="请输入或选择一级税务规则类目"
            style="width: 80%"
            @change="val => handleSelectChange(val, 1)"
          >
            <el-option
              v-for="item in categoryList1"
              :key="item.id"
              :label="item.categoryName"
              :value="item.id"
              @click.native="selectCategory(item, 1)"
            ></el-option>
          </el-select>
          <el-select
            :value="saveForm.categoryId2"
            :disabled="!saveForm.categoryId1"
            filterable
            allow-create
            default-first-option
            placeholder="请输入或选择二级税务规则类目"
            style="width: 80%; margin-top: 10px"
            @change="val => handleSelectChange(val, 2)"
          >
            <el-option
              v-for="item in categoryList2"
              :key="item.id"
              :label="item.categoryName"
              :value="item.id"
              @click.native="selectCategory(item, 2)"
            ></el-option>
          </el-select>
          <el-select
            :value="saveForm.categoryId3"
            :disabled="!saveForm.categoryId2"
            filterable
            allow-create
            default-first-option
            placeholder="请输入或选择三级税务规则类目"
            style="width: 80%; margin-top: 10px"
            @change="val => handleSelectChange(val, 3)"
          >
            <el-option
              v-for="item in categoryList3"
              :key="item.id"
              :label="item.categoryName"
              :value="item.id"
              @click.native="selectCategory(item, 3)"
            ></el-option>
          </el-select>
          <el-select
            :value="saveForm.categoryId4"
            :disabled="!saveForm.categoryId3"
            filterable
            allow-create
            default-first-option
            placeholder="请输入或选择四级税务规则类目"
            style="width: 80%; margin-top: 10px"
            @change="val => handleSelectChange(val, 4)"
          >
            <el-option
              v-for="item in categoryList4"
              :key="item.id"
              :label="item.categoryName"
              :value="item.id"
              @click.native="selectCategory(item, 4)"
            ></el-option>
          </el-select>
          <el-select
            :value="saveForm.categoryId5"
            :disabled="!saveForm.categoryId4"
            filterable
            allow-create
            default-first-option
            placeholder="请输入或选择五级税务规则类目"
            style="width: 80%; margin-top: 10px"
            @change="val => handleSelectChange(val, 5)"
          >
            <el-option
              v-for="item in categoryList5"
              :key="item.id"
              :label="item.categoryName"
              :value="item.id"
              @click.native="selectCategory(item, 5)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="onOK()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { debounce } from '@/utils';
  import { getTaxCategoryQuery, categoryAdd } from '@/api/taxMgt';

  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      countryList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        rulesForm: {
          countryId: [
            {
              required: true,
              message: '请选择国家',
              trigger: 'blur',
            },
          ],
          taxNo: [
            {
              required: true,
              message: '请输入税务编码',
              trigger: 'blur',
            },
          ],
          categoryId1: [
            {
              required: true,
              message: '至少选择一个税务类目',
              trigger: 'blur',
            },
          ],
        },
        saveForm: {
          countryId: '',
          countryName: '',
          taxNo: '',
          categoryId: '',
          categoryInnerCode: '',
          categoryLevel: '',
          categoryName: '',
          categoryId1: '',
          categoryId2: '',
          categoryId3: '',
          categoryId4: '',
          categoryId5: '',
        },
        level: 1, //选择类目的层级
        categoryItem1: null,
        categoryItem2: null,
        categoryItem3: null,
        categoryItem4: null,
        categoryItem5: null,
        categoryList1: [], // 1级类目列表
        categoryList2: [], // 2级类目列表
        categoryList3: [], // 3级类目列表
        categoryList4: [], // 4级类目列表
        categoryList5: [], // 5级类目列表
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(val) {
        if (val) {
          this.getTaxCategoryQuery(undefined, 1);
        } else {
          Object.assign(this.$data.saveForm, this.$options.data().saveForm);
          this.$nextTick(function () {
            this.$refs.formData.clearValidate();
          });
        }
      },
    },
    created() {
      // this.getTaxCategoryQuery(undefined, 1);
    },
    methods: {
      // 类目选择
      selectCategory(item, type) {
        if (item) {
          const { childrenList, level, topId, id, parentId, ...params } = item;
          this[`categoryItem${type}`] = {
            ...params,
            categoryId: id,
            categoryLevel: level,
          };
          this.getTaxCategoryQuery(item.categoryCode, type + 1);
        }
      },
      getTaxCategoryQuery(categoryCode, type) {
        getTaxCategoryQuery({ categoryCode }).then(({ res, err }) => {
          if (res && !err) {
            if (type === 1) {
              const { id, level, categoryInnerCode, categoryName } = res;
              this.saveForm = {
                ...this.saveForm,
                categoryId: id,
                categoryInnerCode,
                categoryLevel: level,
                categoryName,
              };
            }
            this[`categoryList${type}`] = res.childrenList;
          }
        });
      },
      // 国家选择
      selectCountry(val) {
        if (!val || val.name != '中国') return;
        this.saveForm.countryId = val.id;
        this.saveForm.countryName = val.name;
      },
      handleSelectChange(val, type) {
        const {
          categoryId1,
          categoryId2,
          categoryId3,
          categoryId4,
        } = this.saveForm;
        this.level = type;
        if (type === 1 && categoryId1 != val) {
          this.saveForm.categoryId1 = val;
          this.saveForm.categoryId2 = '';
          this.saveForm.categoryId3 = '';
          this.saveForm.categoryId4 = '';
          this.saveForm.categoryId5 = '';
          this.categoryList2 = [];
        }

        if (type === 2 && categoryId2 != val) {
          this.saveForm.categoryId2 = val;
          this.saveForm.categoryId3 = '';
          this.saveForm.categoryId4 = '';
          this.saveForm.categoryId5 = '';
          this.categoryList3 = [];
        }
        if (type === 3 && categoryId3 != val) {
          this.saveForm.categoryId3 = val;
          this.saveForm.categoryId4 = '';
          this.saveForm.categoryId5 = '';
          this.categoryList4 = [];
        }
        if (type === 4 && categoryId4 != val) {
          this.saveForm.categoryId4 = val;
          this.saveForm.categoryId5 = '';
          this.categoryList5 = [];
        }
        if (type === 5) {
          this.saveForm.categoryId5 = val;
        }
      },
      getBodyParams() {
        let obj = {};
        const {
          categoryId1,
          categoryId2,
          categoryId3,
          categoryId4,
          categoryId5,
          taxNo,
          ...params
        } = this.saveForm;
        const {
          categoryItem1,
          categoryItem2,
          categoryItem3,
          categoryItem4,
          categoryItem5,
          level,
        } = this;
        const countryItem = {
          countryId: this.saveForm.countryId,
          countryName: this.saveForm.countryName,
        };

        for (let i = 0; i < 5; i++) {
          let j = i + 1;
          if (i == 0 && categoryId1) {
            obj.children =
              typeof categoryId1 === 'number'
                ? {
                    ...categoryItem1,
                    ...countryItem,
                    taxNo: level == j ? taxNo : null,
                  }
                : {
                    ...countryItem,
                    categoryName: categoryId1,
                    categoryLevel: 1,
                    taxNo: level == j ? taxNo : null,
                  };
          }
          if (i == 1 && categoryId2) {
            obj.children.children =
              typeof categoryId2 === 'number'
                ? {
                    ...categoryItem2,
                    ...countryItem,
                    taxNo: level == j ? taxNo : null,
                  }
                : {
                    ...countryItem,
                    categoryName: categoryId2,
                    categoryLevel: 2,
                    taxNo: level == j ? taxNo : null,
                  };
          }
          if (i == 2 && categoryId3) {
            obj.children.children.children =
              typeof categoryId3 === 'number'
                ? {
                    ...categoryItem3,
                    ...countryItem,
                    taxNo: level == j ? taxNo : null,
                  }
                : {
                    ...countryItem,
                    categoryName: categoryId3,
                    categoryLevel: 3,
                    taxNo: level == j ? taxNo : null,
                  };
          }
          if (i == 3 && categoryId4) {
            obj.children.children.children.children =
              typeof categoryId4 === 'number'
                ? {
                    ...categoryItem4,
                    ...countryItem,
                    taxNo: level == j ? taxNo : null,
                  }
                : {
                    ...countryItem,
                    categoryName: categoryId4,
                    categoryLevel: 4,
                    taxNo: level == j ? taxNo : null,
                  };
          }
          if (i == 4 && categoryId5) {
            obj.children.children.children.children.children =
              typeof categoryId5 === 'number'
                ? {
                    ...categoryItem5,
                    ...countryItem,
                    taxNo: level == j ? taxNo : null,
                  }
                : {
                    ...countryItem,
                    categoryName: categoryId5,
                    categoryLevel: 5,
                    taxNo: level == j ? taxNo : null,
                  };
          }
        }

        return {
          ...params,
          ...obj,
        };
      },
      onOK: debounce(function () {
        this.$refs.formData.validate(async valid => {
          if (!valid) return;
          categoryAdd(this.getBodyParams()).then(data => {
            const { err } = data;
            if (!err) {
              this.$message({
                message: '新增类目成功',
                type: 'success',
              });
              this.showDialog = false;
              this.$emit('onGet');
            }
          });
        });
      }, 800),
    },
  };
</script>

<style></style>
