<template>
  <div class="ai-upload-container">
    <div class="upload-wrapper">
      <el-upload
        ref="upload"
        :action="uploadUrl"
        :http-request="httpRequest"
        :before-upload="beforeUpload"
        :show-file-list="false"
        :auto-upload="true"
      >
        <el-button size="small" type="primary">点击上传</el-button>
      </el-upload>

      <!-- 文件信息显示 -->
      <div v-if="fileInfo && false" class="file-info">
        <div class="file-details">
          <i class="el-icon-document"></i>
          <span class="file-name">{{ fileInfo.name }}</span>
          <span class="file-size">{{ formatFileSize(fileInfo.size) }}</span>
        </div>
      </div>
    </div>

    <!-- 上传进度弹窗 -->
    <el-dialog
      title="文件上传"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="uploadComplete"
      width="400px"
      custom-class="ai-upload-dialog"
    >
      <div class="dialog-content">
        <!-- 上传中状态 -->
        <div v-if="isUploading" class="upload-progress-container">
          <div class="ai-progress-animation">
            <div class="ai-spinner-large">
              <div class="spinner-ring-large"></div>
              <div class="spinner-core-large"></div>
            </div>
          </div>

          <div class="progress-info">
            <div class="progress-title">正在上传文件</div>
            <div class="progress-filename">
              {{ fileInfo ? fileInfo.name : '' }}
            </div>

            <el-progress
              :percentage="uploadProgress"
              :stroke-width="8"
              :color="progressColor"
              class="ai-progress-bar"
            ></el-progress>

            <div class="progress-stats">
              <span>{{ uploadProgress }}% 完成</span>
              <span v-if="uploadSpeed">{{ uploadSpeed }}</span>
            </div>
          </div>
        </div>

        <!-- 上传完成状态 -->
        <div v-if="uploadComplete" class="upload-complete-container">
          <div
            class="complete-icon"
            :class="{ success: uploadSuccess, error: !uploadSuccess }"
          >
            <i v-if="uploadSuccess" class="el-icon-check"></i>
            <i v-else class="el-icon-close"></i>
          </div>

          <div class="complete-title">
            {{ uploadSuccess ? '上传成功' : '上传失败' }}
          </div>

          <div v-if="responseData" class="response-container">
            <div class="response-title">
              处理编号：{{ responseData.processCode || '' }}
            </div>
            <div class="response-content">
              <pre>{{ responseData.message || '...' }}</pre>
            </div>
          </div>
        </div>
      </div>

      <span v-if="uploadComplete" slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">关闭</el-button>
        <el-button v-if="!uploadSuccess" type="primary" @click="handleRetry">
          重试
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { service } from '@/utils/request';

  export default {
    name: 'AIUploadButton',
    data() {
      return {
        uploadUrl: '/finance-bill/tax/management/upload/excel', // 替换为您的上传接口
        // uploadUrl: 'https://jsonplaceholder.typicode.com/posts',
        isUploading: false,
        uploadProgress: 0,
        uploadSuccess: false,
        fileInfo: null,
        dialogVisible: false,
        uploadComplete: false,
        responseData: null,
        uploadSpeed: '',
        progressColor: {
          type: 'linear',
          color: [
            { offset: '0%', color: '#4facfe' },
            { offset: '100%', color: '#00f2fe' },
          ],
        },
        uploadStartTime: 0,
      };
    },
    computed: {},
    methods: {
      async httpRequest(request) {
        const { action, file, onError, onSuccess, onProgress } = request;
        const formData = new FormData();
        formData.append('file', file);

        try {
          const { res } = await service.instance.http.post(action, formData, {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
            onUploadProgress: progressEvent => {
              this.uploadProgress = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total,
              );
              // 上传速度计算
              const currentTime = Date.now();
              const elapsedTime = (currentTime - this.uploadStartTime) / 1000;
              if (elapsedTime > 0) {
                const speed = progressEvent.loaded / elapsedTime;
                this.uploadSpeed = this.formatSpeed(speed);
              }
              // onProgress({
              //   percent: Math.round(
              //     (progressEvent.loaded * 100) / progressEvent.total,
              //   ),
              // });
            },
          });
          if (res) {
            console.log(JSON.stringify(res));
            this.handleSuccess(res);
          } else {
            console.log(JSON.stringify(res));
            this.handleError({
              message:
                '上传的文件格式有误，系统无法识别。建议检查模板或联系管理员协助处理。',
              processCode: '',
            });
          }
        } catch (error) {
          this.handleError(error);
        }
      },
      beforeUpload(file) {
        // 文件类型和大小验证
        const isValidType = this.checkFileType(file);
        const isValidSize = file.size / 1024 / 1024 < 10; // 10MB限制

        if (!isValidType) {
          this.$message.error('文件类型不支持！');
          return false;
        }

        if (!isValidSize) {
          this.$message.error('文件大小不能超过 10MB！');
          return false;
        }

        this.fileInfo = {
          name: file.name,
          size: file.size,
        };

        this.isUploading = true;
        this.uploadSuccess = false;
        this.uploadProgress = 0;
        this.uploadComplete = false;
        this.responseData = null;
        this.dialogVisible = true;
        this.uploadStartTime = Date.now();

        return true;
      },
      handleProgress(event) {
        console.log('handleProgress', event);
        this.uploadProgress = Math.round(event.percent);
        // 计算上传速度
        const currentTime = Date.now();
        const elapsedTime = (currentTime - this.uploadStartTime) / 1000; // 转换为秒
        if (elapsedTime > 0) {
          const loadedBytes = event.loaded || 0;
          const bytesPerSecond = loadedBytes / elapsedTime;
          this.uploadSpeed = this.formatSpeed(bytesPerSecond);
        }
      },

      handleSuccess(response, file) {
        console.log('handleSuccess', response);
        this.isUploading = false;
        this.uploadSuccess = true;
        this.uploadComplete = true;
        this.responseData = response;
        // 不再自动关闭弹窗，让用户查看返回信息
        // 触发成功事件
        this.$emit('upload-success', { response, file });
      },

      handleError(error, file) {
        console.log('handleError', error);
        this.isUploading = false;
        this.uploadSuccess = false;
        this.uploadComplete = true;
        this.responseData = {
          processCode: error.code || '500',
          message: error.message || '上传失败',
        };
        // 触发错误事件
        this.$emit('upload-error', error);
      },

      checkFileType(file) {
        const allowedTypes = [
          'application/vnd.ms-excel', // .xls（旧版 Excel 97-2003）
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx（新版 Excel）
        ];
        return allowedTypes.includes(file.type);
      },

      formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
      },
      closeDialog() {
        this.dialogVisible = false;
        // 重置状态
        if (this.uploadSuccess) {
          this.fileInfo = null;
        }
      },

      handleRetry() {
        if (this.fileInfo) {
          this.closeDialog();
          // 延迟一下再触发上传，确保弹窗已关闭
          setTimeout(() => {
            this.$refs.upload.submit();
          }, 300);
        }
      },

      formatSpeed(bytesPerSecond) {
        if (bytesPerSecond < 1024) {
          return bytesPerSecond.toFixed(2) + ' B/s';
        } else if (bytesPerSecond < 1024 * 1024) {
          return (bytesPerSecond / 1024).toFixed(2) + ' KB/s';
        } else {
          return (bytesPerSecond / (1024 * 1024)).toFixed(2) + ' MB/s';
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  //.ai-upload-container {
  //  display: flex;
  //  flex-direction: column;
  //  align-items: center;
  //  padding: 20px;
  //  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  //}
  //
  //.upload-wrapper {
  //  position: relative;
  //  width: 100%;
  //  max-width: 400px;
  //}

  .ai-upload {
    width: 100%;
  }

  .upload-button {
    position: relative;
    width: 100%;
    min-height: 120px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
  }

  .upload-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
  }

  .upload-button.uploading {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    animation: pulse 2s infinite;
  }

  .upload-button.success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
  }

  .button-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    z-index: 2;
    position: relative;
  }

  .icon-container {
    position: relative;
    width: 48px;
    height: 48px;
  }

  .upload-icon svg,
  .success-icon svg {
    width: 48px;
    height: 48px;
    stroke-width: 1.5;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }

  .ai-spinner {
    position: relative;
    width: 48px;
    height: 48px;
  }

  .spinner-ring {
    position: absolute;
    width: 48px;
    height: 48px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .spinner-core {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulse-core 1.5s ease-in-out infinite;
  }

  .progress-bar {
    width: 60px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background: white;
    border-radius: 2px;
    transition: width 0.3s ease;
  }

  .text-container {
    text-align: center;
  }

  .main-text {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 4px;
  }

  .sub-text,
  .progress-text {
    font-size: 14px;
    opacity: 0.9;
  }

  .ai-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
  }

  .corner-lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  .corner-line {
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.5);
  }

  .corner-line.top-left {
    top: 12px;
    left: 12px;
    border-right: none;
    border-bottom: none;
  }

  .corner-line.top-right {
    top: 12px;
    right: 12px;
    border-left: none;
    border-bottom: none;
  }

  .corner-line.bottom-left {
    bottom: 12px;
    left: 12px;
    border-right: none;
    border-top: none;
  }

  .corner-line.bottom-right {
    bottom: 12px;
    right: 12px;
    border-left: none;
    border-top: none;
  }

  .glow-effect {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(
      45deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
    border-radius: 18px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .upload-button:hover .glow-effect {
    opacity: 1;
  }

  .file-info {
    margin-top: 16px;
    padding: 12px 16px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(102, 126, 234, 0.2);
  }

  .file-details {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #667eea;
    font-size: 14px;
  }

  .file-name {
    font-weight: 500;
    flex: 1;
  }

  .file-size {
    color: #999;
    font-size: 12px;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
  }

  @keyframes pulse-core {
    0%,
    100% {
      transform: translate(-50%, -50%) scale(1);
    }
    50% {
      transform: translate(-50%, -50%) scale(1.2);
    }
  }

  /* 响应式设计 */
  @media (max-width: 480px) {
    .upload-button {
      min-height: 100px;
    }

    .main-text {
      font-size: 16px;
    }

    .sub-text,
    .progress-text {
      font-size: 12px;
    }

    .icon-container {
      width: 40px;
      height: 40px;
    }

    .upload-icon svg,
    .success-icon svg {
      width: 40px;
      height: 40px;
    }

    .ai-spinner {
      width: 40px;
      height: 40px;
    }

    .spinner-ring {
      width: 40px;
      height: 40px;
    }
  }

  /* 弹窗样式 */
  .ai-upload-dialog {
    border-radius: 16px;
    overflow: hidden;
  }

  .ai-upload-dialog .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    border-radius: 16px;
  }

  .ai-upload-dialog .el-dialog__title {
    color: white;
    font-weight: 600;
  }

  .dialog-content {
    padding: 20px 0;
  }

  .upload-progress-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .ai-progress-animation {
    margin-bottom: 10px;
  }

  .ai-spinner-large {
    position: relative;
    width: 60px;
    height: 60px;
  }

  .spinner-ring-large {
    position: absolute;
    width: 60px;
    height: 60px;
    border: 4px solid rgba(102, 126, 234, 0.2);
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1.2s linear infinite;
  }

  .spinner-core-large {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    background: #667eea;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulse-core 1.5s ease-in-out infinite;
  }

  .progress-info {
    width: 100%;
    padding: 0 20px;
  }

  .progress-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
  }

  .progress-filename {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .ai-progress-bar {
    margin-bottom: 10px;
  }

  .progress-stats {
    display: flex;
    justify-content: space-between;
    font-size: 13px;
    color: #666;
  }

  .upload-complete-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 20px;
    border-radius: 15px;
  }

  .complete-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 30px;
    color: white;
  }

  .complete-icon.success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
  }

  .complete-icon.error {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
  }

  .complete-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
  }

  .response-container {
    width: 100%;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
  }

  .response-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #555;
  }

  .response-content {
    max-height: 150px;
    overflow-y: auto;
    background: #f1f1f1;
    border-radius: 4px;
    padding: 8px;
  }

  .response-content pre {
    margin: 0;
    font-family: monospace;
    font-size: 12px;
    white-space: pre-wrap;
    word-break: break-all;
    color: #333;
  }

  .dialog-footer {
    text-align: right;
  }
</style>
