<template>
  <div>
    <el-dialog
      width="600px"
      :title="currentRow ? '编辑税则' : '新增税则'"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="saveForm"
        :rules="rulesForm"
        label-width="100px"
      >
        <el-form-item label="国家" prop="countryId">
          <el-select
            v-model="saveForm.countryId"
            :disabled="!!currentRow"
            placeholder="请选择国家"
            style="width: 80%"
          >
            <el-option
              v-for="item in countryList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
              :disabled="item.name != '中国'"
              @click.native="selectCountry(item)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="税务类目" prop="categoryId1">
          <el-select
            :value="saveForm.categoryId1"
            placeholder="请选择税务类目"
            style="width: 80%"
            :disabled="!!currentRow"
          >
            <el-option
              v-for="item in categoryList1"
              :key="item.id"
              :label="item.categoryName"
              :value="item.id"
              @click.native="() => handleSelect(item, 1)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="categoryId2">
          <el-select
            :value="saveForm.categoryId2"
            placeholder="请选择税务类目"
            style="width: 80%"
            :disabled="!!currentRow"
          >
            <el-option
              v-for="item in categoryList2"
              :key="item.id"
              :label="item.categoryName"
              :value="item.id"
              @click.native="() => handleSelect(item, 2)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="categoryId3">
          <el-select
            :value="saveForm.categoryId3"
            placeholder="请选择税务类目"
            style="width: 80%"
            :disabled="!!currentRow"
          >
            <el-option
              v-for="item in categoryList3"
              :key="item.id"
              :label="item.categoryName"
              :value="item.id"
              @click.native="() => handleSelect(item, 3)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="categoryId4">
          <el-select
            :value="saveForm.categoryId4"
            placeholder="请选择税务类目"
            style="width: 80%"
            :disabled="!!currentRow"
          >
            <el-option
              v-for="item in categoryList4"
              :key="item.id"
              :label="item.categoryName"
              :value="item.id"
              @click.native="() => handleSelect(item, 4)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="categoryId5">
          <el-select
            :value="saveForm.categoryId5"
            placeholder="请选择税务类目"
            style="width: 80%"
            :disabled="!!currentRow"
          >
            <el-option
              v-for="item in categoryList5"
              :key="item.id"
              :label="item.categoryName"
              :value="item.id"
              @click.native="() => handleSelect(item, 5)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="税务编码" prop="taxNo">
          <el-input
            v-model="saveForm.taxNo"
            disabled
            placeholder="请输入税务编码"
            style="width: 80%"
          ></el-input>
        </el-form-item>
        <el-form-item label="纳税人类型" prop="taxpayerType">
          <el-select
            v-model="saveForm.taxpayerType"
            placeholder="请选择纳税人类型"
            style="width: 80%"
            :disabled="!!currentRow"
          >
            <el-option label="一般纳税人" :value="10"></el-option>
            <el-option label="小规模纳税人" :value="20"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="税率" prop="taxRate">
          <el-checkbox-group v-model="saveForm.taxRate">
            <!-- <el-checkbox label="0">0%</el-checkbox>
            <el-checkbox label="9">9%</el-checkbox>
            <el-checkbox label="13">13%</el-checkbox>
            <el-checkbox label="7">7%</el-checkbox>
            <el-checkbox label="10">10%</el-checkbox>
            <el-checkbox label="15">15%</el-checkbox> -->
            <el-checkbox
              v-for="item in rateList"
              :key="item"
              :label="String(item)"
            >
              {{ item }}%
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="生效期间" prop="beginDate">
          <el-date-picker
            v-model="saveForm.beginDate"
            type="datetimerange"
            range-separator="至"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          ></el-date-picker>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="onOK()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { debounce } from '@/utils';
  import { getTaxCategoryQuery, taxRuleAdd, taxRuleUpdate } from '@/api/taxMgt';

  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      countryList: {
        type: Array,
        default: () => [],
      },
      rateList: {
        type: Array,
        default: () => [],
      },
      currentRow: {
        type: Object,
        default: null,
      },
    },
    data() {
      const validateCategory = (rule, value, callback, type) => {
        if (!value && this[`categoryList${type}`].length) {
          callback(new Error('请选择'));
        } else {
          callback();
        }
      };
      return {
        rulesForm: {
          countryId: [
            {
              required: true,
              message: '请选择国家',
              trigger: 'blur',
            },
          ],
          taxNo: [
            {
              required: true,
              message: '请输入税务编码',
              trigger: 'blur',
            },
          ],
          taxpayerType: [
            {
              required: true,
              message: '请选择纳税人类型',
              trigger: 'blur',
            },
          ],
          taxRate: [
            {
              required: true,
              message: '请选择税率',
              trigger: 'blur',
            },
          ],
          beginDate: [
            {
              required: true,
              message: '请选择生效期间',
              trigger: 'blur',
            },
          ],
          categoryId1: [
            {
              trigger: 'blur',
              validator: (rule, value, callback) =>
                validateCategory(rule, value, callback, 1),
            },
          ],
          categoryId2: [
            {
              trigger: 'blur',
              validator: (rule, value, callback) =>
                validateCategory(rule, value, callback, 2),
            },
          ],
          categoryId3: [
            {
              trigger: 'blur',
              validator: (rule, value, callback) =>
                validateCategory(rule, value, callback, 3),
            },
          ],
          categoryId4: [
            {
              trigger: 'blur',
              validator: (rule, value, callback) =>
                validateCategory(rule, value, callback, 4),
            },
          ],
          categoryId5: [
            {
              trigger: 'blur',
              validator: (rule, value, callback) =>
                validateCategory(rule, value, callback, 5),
            },
          ],
        },
        saveForm: {
          beginDate: '', // 生效时间
          taxRate: [], // taxRate
          countryId: '',
          countryName: '',
          taxNo: '',
          categoryCode: '',
          taxpayerType: '',
          taxType: 0,
          categoryId1: '',
          categoryId2: '',
          categoryId3: '',
          categoryId4: '',
          categoryId5: '',
        },
        level: 1, // 类目层级
        levelObj: {}, // 最后一层数据
        categoryList1: [], // 1级类目列表
        categoryList2: [], // 2级类目列表
        categoryList3: [], // 3级类目列表
        categoryList4: [], // 4级类目列表
        categoryList5: [], // 5级类目列表
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(val) {
        if (val) {
          if (this.currentRow) {
            const {
              beginTime,
              endTime,
              countryId,
              taxpayerType,
              countryName,
              taxNo,
              categoryCode,
              taxRate,
              firstLevelCategoryName,
              secondLevelCategoryName,
              thirdLevelCategoryName,
              fourLevelCategoryName,
              fiveLevelCategoryName,
            } = this.currentRow;
            this.saveForm = {
              beginDate: beginTime && endTime ? [beginTime, endTime] : [],
              taxRate: taxRate ? taxRate.split(',') : [],
              countryId: String(countryId),
              taxNo,
              categoryCode,
              countryName,
              taxType: 0,
              taxpayerType,
              categoryId1: firstLevelCategoryName,
              categoryId2: secondLevelCategoryName,
              categoryId3: thirdLevelCategoryName,
              categoryId4: fourLevelCategoryName,
              categoryId5: fiveLevelCategoryName,
            };
          } else {
            this.getTaxCategoryQuery(undefined, 1);
          }
        } else {
          this.initData();
          this.$nextTick(function () {
            this.$refs.formData.clearValidate();
          });
        }
      },
    },
    created() {},
    methods: {
      initData() {
        this.level = 1;
        this.levelObj = {};
        this.categoryList1 = [];
        this.categoryList2 = [];
        this.categoryList3 = [];
        this.categoryList4 = [];
        this.categoryList5 = [];
        Object.assign(this.$data.saveForm, this.$options.data().saveForm);
      },
      getTaxCategoryQuery(categoryCode, type) {
        getTaxCategoryQuery({ categoryCode }).then(({ res, err }) => {
          if (res && !err) {
            // if (type === 1) {
            //   const { categoryCode } = res;
            //   this.saveForm = {
            //     ...this.saveForm,
            //     categoryCode,
            //   };
            // }
            this[`categoryList${type}`] = res.childrenList;
          }
        });
      },
      // 国家选择
      selectCountry(val) {
        if (!val || val.name != '中国') return;
        this.saveForm.countryId = val.id;
        this.saveForm.countryName = val.name;
      },
      // 类目选择
      handleSelect(val, type) {
        if (val) {
          this.getTaxCategoryQuery(val.categoryCode, type + 1);
          this.level = val.level;
          this.levelObj = val;
        }
        if (val.taxNo) this.saveForm.taxNo = val.taxNo;
        const {
          categoryId1,
          categoryId2,
          categoryId3,
          categoryId4,
        } = this.saveForm;
        if (type === 1 && categoryId1 != val.id) {
          this.saveForm.categoryId1 = val.id;
          this.saveForm.categoryId2 = '';
          this.saveForm.categoryId3 = '';
          this.saveForm.categoryId4 = '';
          this.saveForm.categoryId5 = '';
        }

        if (type === 2 && categoryId2 != val.id) {
          this.saveForm.categoryId2 = val.id;
          this.saveForm.categoryId3 = '';
          this.saveForm.categoryId4 = '';
          this.saveForm.categoryId5 = '';
        }
        if (type === 3 && categoryId3 != val.id) {
          this.saveForm.categoryId3 = val.id;
          this.saveForm.categoryId4 = '';
          this.saveForm.categoryId5 = '';
        }
        if (type === 4 && categoryId4 != val.id) {
          this.saveForm.categoryId4 = val.id;
          this.saveForm.categoryId5 = '';
        }
        if (type === 5) {
          this.saveForm.categoryId5 = val.id;
        }
      },
      getBodyParams() {
        const {
          categoryId1,
          categoryId2,
          categoryId3,
          categoryId4,
          categoryId5,
          beginDate,
          taxRate,
          categoryCode,
          ...params
        } = this.saveForm;

        return {
          ...params,
          taxRate: taxRate.map(item => Number(item)),
          categoryCode: categoryCode || this.levelObj.categoryCode,
          beginTime: beginDate[0],
          endTime: beginDate[1],
        };
      },
      onOK: debounce(function () {
        this.$refs.formData.validate(async valid => {
          if (!valid) return;
          const saveApi = this.currentRow ? taxRuleUpdate : taxRuleAdd;
          const {
            countryId,
            countryName,
            taxNo,
            ...params
          } = this.getBodyParams();
          saveApi(this.currentRow ? { ...params } : this.getBodyParams()).then(
            data => {
              const { err } = data;
              if (!err) {
                this.$message({
                  message: this.currentRow ? '编辑成功' : '添加成功',
                  type: 'success',
                });
                this.showDialog = false;
                this.$emit('onGet');
              }
            },
          );
        });
      }, 800),
    },
  };
</script>

<style></style>
