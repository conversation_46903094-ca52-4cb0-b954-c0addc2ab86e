<template>
  <div>
    <el-form inline>
      <!-- <el-form-item label="类目ID:">
        <el-input
          v-model="searchParams.payerSupplierName"
          placeholder="请输入类目ID"
        />
      </el-form-item>
      <el-form-item label="类目名称:">
        <el-input
          v-model="searchParams.payeeSupplierName"
          placeholder="请输入收款主体"
        />
      </el-form-item> -->
      <el-form-item label="税务编码:">
        <el-input v-model="searchParams.taxNo" placeholder="请输入税务编码" />
      </el-form-item>
      <el-form-item label="国家:">
        <el-select
          v-model="searchParams.countryId"
          clearable
          placeholder="请选择国家"
        >
          <el-option
            v-for="item in countryList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="纳税人类型:">
        <el-select
          v-model="searchParams.taxpayerType"
          clearable
          placeholder="请选择纳税人类型"
        >
          <el-option label="一般纳税人" :value="10"></el-option>
          <el-option label="小规模纳税人" :value="20"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <!-- <el-button type="primary" @click="handleAdd">新增类目</el-button>
        <el-button type="primary" @click="handleAddTax()">新增税则</el-button> -->
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="getColumns()"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="编辑"
          permission-key=""
          @click="handleAddTax(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <CategoryDialog
      v-model="showDialog"
      :country-list="countryList"
      @onGet="getList"
    />
    <TaxDialog
      v-model="showTaxDialog"
      :country-list="countryList"
      :rate-list="rateList"
      :current-row="currentRow"
      @onGet="getList"
    />
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import CategoryDialog from './components/CategoryDialog';
  import TaxDialog from './components/TaxDialog';
  import {
    getTaxRulePage,
    getTaxCountryQuery,
    getCountryRate,
  } from '@/api/taxMgt';

  export default {
    components: {
      dynamictable,
      CategoryDialog,
      TaxDialog,
    },

    data() {
      return {
        searchParams: {
          taxNo: '',
          countryId: '',
          taxpayerType: '',
        },
        list: [],
        countryList: [], // 国家列表
        rateList: [], // 税率列表
        showDialog: false,
        showTaxDialog: false,
        currentRow: null,
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
      };
    },
    created() {
      getTaxCountryQuery().then(({ res, err }) => {
        if (res && !err) {
          this.countryList = Object.keys(res).map(item => {
            return {
              id: item,
              name: res[item],
            };
          });
        }
      });
      getCountryRate({ countryId: 86 }).then(({ res, err }) => {
        if (res && !err) {
          this.rateList = res.rateList;
        }
      });
      this.getList(true);
    },
    methods: {
      handleAdd() {
        this.showDialog = true;
      },
      handleAddTax(row) {
        this.currentRow = row;
        this.showTaxDialog = true;
      },
      getParams() {
        const params = {
          ...this.searchParams,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const { res, err } = await getTaxRulePage(params);
        if (res && !err) {
          this.options.loading = false;
          this.list = res.records;
          this.pagination.total = res.total;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        // this.getList(true);
      },
      getColumns() {
        return [
          {
            prop: 'categoryCode',
            label: '编码',
          },
          {
            prop: 'countryName',
            label: '国家',
          },
          {
            prop: 'taxNo',
            label: '税务编码',
          },
          {
            prop: 'firstLevelCategoryId',
            label: '一级类目ID',
          },
          {
            prop: 'firstLevelCategoryName',
            label: '一级类目名称',
          },
          {
            prop: 'secondLevelCategoryId',
            label: '二级类目ID',
          },
          {
            prop: 'secondLevelCategoryName',
            label: '二级类目名称',
          },
          {
            prop: 'thirdLevelCategoryId',
            label: '三级类目ID',
          },
          {
            prop: 'thirdLevelCategoryName',
            label: '三级类目名称',
          },
          {
            prop: 'fourLevelCategoryId',
            label: '四级类目ID',
          },
          {
            prop: 'fourLevelCategoryName',
            label: '四级类目名称',
          },
          {
            prop: 'fiveLevelCategoryId',
            label: '五级类目ID',
          },
          {
            prop: 'fiveLevelCategoryName',
            label: '五级类目名称',
          },
          {
            prop: 'taxpayerTypeName',
            label: '纳税人类型',
          },
          {
            prop: 'taxRate',
            label: '税率',
          },
          {
            prop: 'beginTime',
            label: '生效时间',
            width: '200',
            render: ({ beginTime, endTime }) => (
              <div>
                {beginTime} ～ {endTime}
              </div>
            ),
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            minWidth: '100',
            maxWidth: '200',
            scopedSlots: { customRender: 'operation' },
          },
        ];
      },
    },
  };
</script>
<style lang="scss"></style>
