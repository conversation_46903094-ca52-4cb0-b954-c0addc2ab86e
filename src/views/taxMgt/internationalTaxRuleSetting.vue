<template>
  <div>
    <div slot="search">
      <el-form :model="formData" inline @keydown.native.enter.prevent="getData">
        <el-form-item>
          <el-select
            v-model="formData.countryId"
            placeholder="选择国家"
            clearable
            filterable
            @change="getData()"
          >
            <el-option
              v-for="item in allCountries"
              :key="`search_country` + item.id"
              :value="item.id"
              :label="item.name"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="getData">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </el-form-item>
        <el-form-item style="float: right">
          <el-button type="primary" @click="openAdd">新增</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table
      :data="basicPageObj.table"
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" />
      <el-table-column prop="countryName" label="国家"></el-table-column>
      <el-table-column prop="entities" label="公司组合">
        <template #default="{ row }">
          <span
            v-for="et in row.entities"
            :key="`show_entities_` + row.id + `_` + et.id"
          >
            {{ et.entityName }}<br/>
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="beginTime" label="生效时间">
        <template #default="{ row }">
          {{ row.beginTime }}-{{ row.endTime }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态">
        <template #default="{ row }">
          <el-tag v-if="row.status === 0" type="info">停用</el-tag>
          <el-tag v-else type="success">启用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button type="text" @click="openEdit(row)">编辑</el-button>
          <el-button type="text" @click="updateStatus(row)">
            {{ row.status === 0 ? '启用' : '关闭' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="formData.pageNo"
      :page-sizes="[10, 15, 20, 50, 100]"
      :page-size="formData.limit"
      layout="total, sizes, prev, pager, next, jumper"
      :total="basicPageObj.total"
      background
      @size-change="sizeChange"
      @current-change="pageChange"
    />

    <el-dialog
      v-if="addOrEditDialog.visible"
      :title="addOrEditDialog.title"
      :visible.sync="addOrEditDialog.visible"
      width="70%"
    >
      <div>
        <el-form
          ref="addOrEditDialogForm"
          :model="addOrEditDialog.formData"
          label-width="140px"
        >
          <el-form-item label="国家">
            <el-select
              v-model="addOrEditDialog.formData.countryId"
              placeholder="选择国家"
              filterable
              :disabled="addOrEditDialog.formData.id !== undefined"
              @change="countryChange()"
            >
              <el-option
                v-for="item in allCountries"
                :key="`search_country` + item.id"
                :value="item.id"
                :label="item.name"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="公司主体">
            <div
              v-for="(item, index) in addOrEditDialog.formData.entities"
              :key="`add_rule_entity_` + index"
              style="display: flex; margin-bottom: 10px"
            >
              <el-select
                v-model="item.entityCode"
                placeholder="选择公司主体"
                filterable
              >
                <el-option
                  v-for="et in addOrEditDialog.allEntities"
                  :key="`add_entity_item_`+ index + `_` + et.entityCode"
                  :value="et.entityCode"
                  :label="et.name"
                ></el-option>
              </el-select>
              <el-button
                icon="el-icon-plus"
                circle
                style="margin-left: 10px"
                @click="addRuleEntity"
              ></el-button>
              <el-button
                v-if="addOrEditDialog.formData.entities.length > 1"
                type="danger"
                icon="el-icon-delete"
                circle
                style="margin-left: 10px"
                @click="removeRuleEntity(index)"
              ></el-button>
            </div>
          </el-form-item>
          <el-form-item label="生效时间">
            <el-date-picker
              v-model="addOrEditDialog.formData.timeRange"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              range-separator="-"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="状态">
            <el-radio v-model="addOrEditDialog.formData.status" :label="1">启用</el-radio>
            <el-radio v-model="addOrEditDialog.formData.status" :label="0">关闭</el-radio>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="doCommit">确认</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
  import {
    get_getCountries,
    get_getEntityByCountryId,
    get_getInternationalTaxRulePage,
    post_addInternationalTaxRule,
    post_updateInternationalTaxRule,
  } from '@/api/internationalTaxMgt';

  export default {
    data() {
      return {
        loading: false,
        allCountries: [],
        basicPageObj: {
          table: [],
          total: 0,
        },
        formData: {
          pageNo: 1,
          limit: 10,
          countryId: undefined,
        },
        addOrEditDialog: {
          visible: false,
          loading: false,
          title: '新增国际税务规则',
          allEntities: [],
          formData: {
            id: undefined,
            countryId: undefined,
            status: 1,
            beginTime: undefined,
            endTime: undefined,
            entities: [{
              entityCode: undefined,
              entityName: undefined,
            }],
            timeRange: ['', ''],
          },
        },
      };
    },
    mounted() {
      this.getOptions();
      this.getData();
    },
    methods: {
      async getOptions() {
        const { res, err } = await get_getCountries({});
        console.log('res: ', res);
        if (!err) {
          this.allCountries = res;
        }
      },
      async getData() {
        this.loading = true;
        const { res, err } = await get_getInternationalTaxRulePage({
          ...this.formData,
        });
        if (!err) {
          let data = res;
          this.basicPageObj.table = data.records;
          this.basicPageObj.total = data.total;
        }
        this.loading = false;
      },
      reset() {
        this.formData = this.$options.data().formData;
        this.getData();
      },
      pageChange(page) {
        this.formData.pageNo = page;
        this.getData();
      },
      sizeChange(size) {
        this.formData.limit = size;
        this.getData();
      },
      addRuleEntity() {
        this.addOrEditDialog.formData.entities.push({
          entityCode: undefined,
          entityName: undefined,
        });
      },
      removeRuleEntity(index) {
        this.addOrEditDialog.formData.entities.splice(index, 1);
      },
      async countryChange() {
        const { res, err } = await get_getEntityByCountryId({
          countryId: this.addOrEditDialog.formData.countryId,
        });
        if (!err) {
          this.addOrEditDialog.allEntities = res;
        }
      },
      openAdd() {
        this.addOrEditDialog.title = '新增国际税务规则';
        this.addOrEditDialog.formData = this.$options.data().addOrEditDialog.formData;
        this.addOrEditDialog.visible = true;
      },
      openEdit(row) {
        this.addOrEditDialog.title = '编辑国际税务规则';
        this.addOrEditDialog.formData = {
          ...row,
          timeRange: [row.beginTime, row.endTime]
        };
        this.countryChange();
        this.addOrEditDialog.visible = true;
      },
      async updateStatus(row) {
        let form = { ...row };
        form.status = row.status === 0 ? 1 : 0;
        const {res, err} = await post_updateInternationalTaxRule(form);
        if (!err) {
          this.$message.success("更新成功");
          this.addOrEditDialog.visible = false;
          this.getData();
        }
      },
      async doCommit() {
        this.addOrEditDialog.formData.beginTime = this.addOrEditDialog.formData.timeRange[0];
        this.addOrEditDialog.formData.endTime = this.addOrEditDialog.formData.timeRange[1];
        this.addOrEditDialog.formData.countryName = this.findCountryName(this.addOrEditDialog.formData.countryId);
        for (let i = 0; i < this.addOrEditDialog.formData.entities.length; i++) {
          let et = this.addOrEditDialog.formData.entities[i];
          et.entityName = this.findEntityName(et.entityCode);
        }
        if (this.addOrEditDialog.formData.id) {
          const {res, err} = await post_updateInternationalTaxRule({
            ...this.addOrEditDialog.formData
          });
          if (!err) {
            this.$message.success("更新成功");
            this.addOrEditDialog.visible = false;
            this.getData();
          }
        } else {
          const {res, err} = await post_addInternationalTaxRule({
            ...this.addOrEditDialog.formData
          });
          if (!err) {
            this.$message.success("新增成功");
            this.addOrEditDialog.visible = false;
            this.getData();
          }
        }
      },
      findCountryName(countryId) {
        for (let i = 0; i < this.allCountries.length; i++) {
          let et = this.allCountries[i];
          if (et.id === countryId) {
            return et.name;
          }
        }
      },
      findEntityName(entityCode) {
        for (let i = 0; i < this.addOrEditDialog.allEntities.length; i++) {
          let et = this.addOrEditDialog.allEntities[i];
          if (et.entityCode === entityCode) {
            return et.name;
          }
        }
      }
    },
  };
</script>

<style scoped></style>
