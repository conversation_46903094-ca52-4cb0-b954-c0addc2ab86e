<template>
  <div>
    <div v-show="tab === 1">
      <el-tabs v-model="tabType" type="card" @tab-click="handleClick">
        <el-tab-pane label="日账单" name="1" />
        <el-tab-pane label="月账单" name="2" />
      </el-tabs>
      <el-form inline>
        <el-form-item label="公司名称:">
          <el-input
            v-model="searchParams.accountEntityName"
            placeholder="请输入公司名称"
          />
        </el-form-item>
        <el-form-item label="品牌名称:">
          <el-input
            v-model="searchParams.brandName"
            placeholder="请输入品牌名称"
          />
        </el-form-item>
        <el-form-item label="仓库名称:">
          <el-select v-model="searchParams.depotId" placeholder="请选择">
            <el-option
              v-for="item in depot"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="费用类型:">
          <el-select v-model="searchParams.biz" placeholder="请选择">
            <el-option
              v-for="item in accountType"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="经销商等级:">
          <el-select
            v-model="searchParams.brandProviderLevel"
            placeholder="请选择"
          >
            <el-option
              v-for="item in provider"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择日期:">
          <el-date-picker
            v-model="searchDate"
            :clearable="false"
            :type="tabType === '1' ? 'daterange' : 'monthrange'"
            range-separator="至"
            :value-format="tabType === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
            :format="tabType === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList(true)">查询</el-button>
          <el-button type="primary" @click="onReset">重置</el-button>
          <el-popover placement="bottom" trigger="click">
            <p>请选择导出时间</p>
            <el-date-picker
              v-model="exportDate"
              :clearable="false"
              :type="tabType === '1' ? 'daterange' : 'monthrange'"
              range-separator="至"
              :value-format="tabType === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
              :format="tabType === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :picker-options="pickerOptions"
            ></el-date-picker>
            <div style="text-align: right; margin: 10px 0 0">
              <el-button type="primary" size="mini" @click="onExport">
                确定
              </el-button>
            </div>
            <el-button
              slot="reference"
              style="margin-left: 10px"
              type="primary"
            >
              <!-- 导出(最多支持5万条) -->
              导出
            </el-button>
          </el-popover>
        </el-form-item>
      </el-form>
      <dynamictable
        :data-source="list"
        :columns="columns"
        :options="options"
        :pagination="tab === 1 ? pagination : null"
        :fetch="getList"
      >
        <template slot="operation" slot-scope="scope">
          <el-button
            slot="reference"
            type="text"
            size="small"
            @click="handelJump(2, scope.row)"
          >
            明细
          </el-button>
        </template>
      </dynamictable>
    </div>

    <div v-show="tab === 2">
      <div>
        <el-button
          style="font-size: 16px; margin-bottom: 10px"
          type="text"
          @click="handelJump(1)"
        >
          返回
        </el-button>
      </div>

      <dynamictable
        :data-source="list"
        :columns="columns1"
        :options="options"
        :fetch="getDetailsList"
        :pagination="pagination"
      ></dynamictable>
    </div>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import { parseTime, downloadFile, setInitData } from '@/utils';
  import { exportExcel } from '@/api/blob';
  import {
    getGoodsList,
    getGoodsDetail,
    conditionList,
  } from '@/api/accountManagement';

  export default {
    components: {
      dynamictable,
    },
    data() {
      return {
        depot: [],
        provider: [],
        accountType: [],
        tab: 1, // 页面切换tab
        loading: false,
        list: [],
        tabType: '1', // 账单切换tab
        searchDate: '',
        exportDate: '', // 导出时间

        choiceDate0: '',
        pickerOptions: {
          onPick: ({ maxDate, minDate }) => {
            console.log(minDate, 'minDate');
            this.choiceDate0 = minDate.getTime();
            if (maxDate) {
              this.choiceDate0 = '';
            }
          },
          disabledDate: time => {
            if (this.choiceDate0 !== '') {
              const one = 30 * 24 * 3600 * 1000;
              const minTime = this.choiceDate0 - one;
              const maxTime = this.choiceDate0 + one;
              return time.getTime() < minTime || time.getTime() > maxTime;
            }
          },
        },

        searchParams: {
          accountEntityName: '',
          brandName: '',
          depotId: '',
          biz: '',
          type: '',
          brandProviderLevel: '',
          startTimeStr: '',
          endTimeStr: '',
        },
        detailsParams: {
          monthDate: '',
          type: '',
          accountEntityCode: '',
          depotId: '',
          brandId: '',
          goodsId: '',
          brandProviderLevel: '',
          barCode: '',
          bizType: '',
        },
        pageNo: 1,
        // 分页
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
        columns1: [
          {
            prop: 'orderId',
            label: '单据编号',
            render: row => (
              <span>
                {row.orderId === 0 ? row.accountEntryId : row.orderId}
              </span>
            ),
          },
          {
            prop: 'idCode',
            label: '经销商编码',
          },
          // {
          //   prop: 'accountEntityName',
          //   label: '经销商名称',
          // },
          {
            prop: 'amount',
            label: '金额',
            render: row => (
              <span>{row.orderId === 0 ? row.serviceAmount : row.amount}</span>
            ),
          },
        ],
        columns: [
          {
            prop: 'id',
            label: 'ID',
          },
          {
            prop: 'monthDate',
            label: '日期',
          },
          {
            prop: 'accountEntityCode',
            label: '公司编码',
          },
          {
            prop: 'accountEntityName',
            label: '公司名称',
          },
          {
            prop: 'depotName',
            label: '仓库',
          },
          {
            prop: 'brandProviderLevelDes',
            label: '经销商等级',
          },
          {
            prop: 'barCode',
            label: '商品条码',
          },
          {
            prop: 'goodsName',
            label: '商品名称',
          },
          {
            prop: 'brandName',
            label: '品牌名称',
          },
          {
            prop: 'bizName',
            label: '费用类型',
          },
          {
            prop: 'amount',
            label: '发生金额',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            scopedSlots: { customRender: 'operation' },
          },
        ],
      };
    },
    created() {
      this.searchDate = setInitData(30);
      this.exportDate = setInitData(30);
      conditionList({}).then(res => {
        if (res) {
          this.depot = res.depot;
          this.provider = res.provider;
          this.accountType = res.accountType;
        }
      });
      this.getList(true);
    },
    methods: {
      handelJump(val, row) {
        this.tab = val;
        if (val === 1) {
          // 返回记住页数
          this.pagination.pageSize = this.pageNo;

          this.getList();
        } else {
          this.init();
          Object.keys(this.detailsParams).forEach(item => {
            this.detailsParams[item] = row[item];
          });
          this.getDetailsList();
        }
      },

      init() {
        this.pageNo = this.pagination.pageSize;
        Object.assign(this.$data.pagination, this.$options.data().pagination);
      },
      getParams(flag) {
        const time = flag ? this.exportDate : this.searchDate;
        this.searchParams.startTimeStr = time
          ? parseTime(time[0], this.tabType === '1' ? '{y}-{m}-{d}' : '{y}-{m}')
          : '';
        this.searchParams.endTimeStr = time
          ? parseTime(time[1], this.tabType === '1' ? '{y}-{m}-{d}' : '{y}-{m}')
          : '';
        const params = {
          ...this.searchParams,
          type: this.tabType === '1' ? 'day' : 'month',
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await getGoodsList(params);
        if (res) {
          this.list = res.records;
          this.pagination.total = res.total;
        }
        this.options.loading = false;
      },
      async getDetailsList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        this.options.loading = true;
        const params = {
          ...this.detailsParams,
          type: this.tabType === '1' ? 'day' : 'month',
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        const res = await getGoodsDetail(params);
        if (res) {
          this.list = res.records;
          this.pagination.total = res.total;
        }
        this.options.loading = false;
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );

        if (this.tab === 1) {
          this.setInit(this.tabType);
          this.getList(true);
        } else {
          this.getDetailsList();
        }
      },
      onExport() {
        const params = this.getParams(true);
        exportExcel(
          params,
          '/api/pay-financial-report/goods/exportData',
          'get',
        ).then(res => {
          if (res) {
            downloadFile(res.data, '服务费明细汇总列表');
            return;
          }
          // this.$message.error('暂无数据')
        });
      },

      setInit(val) {
        const data = setInitData(30);
        if (val == '1') {
          this.exportDate = data;
          this.searchDate = data;
        } else {
          this.exportDate = [data[0], data[1]];
          this.searchDate = [data[0], data[1]];
        }
      },

      handleClick(val) {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.setInit(val.name);
        this.tabType = val.name;
        this.getList(true);
      },
    },
  };
</script>
<style lang="scss" scoped></style>
