<template>
  <div>
    <el-table
      v-loading="loading"
      :data="data"
      style="width: 100%; margin-bottom: 20px"
      border
    >
      <el-table-column prop="id" label="单据编号">
        <template slot-scope="scope">
          <span @click="handleDetails(scope.row)">
            {{
              scope.row.orderId === 0
                ? scope.row.accountEntryId
                : `${scope.row.orderId} >`
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="idCode" label="经销商编码"></el-table-column>
      <!-- <el-table-column
        prop="accountEntityName"
        label="经销商名称"
      ></el-table-column> -->
      <el-table-column
        prop="brandProviderLevelDes"
        label="等级"
      ></el-table-column>
      <el-table-column prop="bizTypeName" label="交易类型"></el-table-column>
      <el-table-column prop="bizName" label="费用类型"></el-table-column>
      <el-table-column prop="businessAt" label="记账时间"></el-table-column>
      <el-table-column prop="amount" label="金额">
        <template slot-scope="scope">
          <span @click="handleDetails(scope.row)">
            {{
              scope.row.orderId === 0
                ? scope.row.serviceAmount
                : scope.row.amount
            }}
          </span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      ref="listPage"
      background
      :current-page.sync="pagination.pageNo"
      :page-size.sync="pagination.limit"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      @current-change="pagination_currentChange"
      @size-change="pagination_sizeChange"
    ></el-pagination>
    <el-dialog width="800px" title="商品列表明细" :visible.sync="visible">
      <el-table :data="detailsData">
        <el-table-column prop="depotName" label="仓库"></el-table-column>
        <!-- <el-table-column
          prop="brandProviderLevelDes"
          label="经销商等级"
        ></el-table-column> -->
        <el-table-column prop="barCode" label="商品条码"></el-table-column>
        <el-table-column prop="goodsName" label="商品名称"></el-table-column>
        <el-table-column prop="brandName" label="品牌名称"></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
  import { goodesDetail } from '@/api/accountManagement';
  export default {
    props: {
      data: {
        type: Array,
        default: () => [],
      },
      pagination: {
        type: Object,
        default: null,
      },
      loading: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        visible: false,
        detailsData: [],
      };
    },

    methods: {
      handleDetails(row) {
        if (row.orderId === 0) {
          return;
        }
        goodesDetail({ orderId: row.orderId }).then(res => {
          if (res) {
            this.detailsData = res;
          }
        });
        this.visible = true;
      },
      pagination_currentChange(val) {
        this.$emit('handlePaginationChange', val, this.pagination.limit);
      },
      pagination_sizeChange(val) {
        this.$emit('handlePaginationChange', this.pagination.pageNo, val);
      },
    },
  };
</script>
<style lang="scss" scoped></style>
