<template>
  <div>
    <el-table v-loading="loading" :data="data" style="width: 100%">
      <el-table-column prop="id" label="ID" type="index"></el-table-column>
      <el-table-column prop="monthDate" label="日期"></el-table-column>
      <el-table-column
        prop="accountEntityCode"
        label="公司编码"
      ></el-table-column>
      <el-table-column
        prop="accountEntityName"
        label="公司名称"
      ></el-table-column>
      <el-table-column prop="startBalance" label="期初余额"></el-table-column>

      <template v-for="item in tableHead">
        <div :key="item.value">
          <el-table-column
            v-if="item.value === '1'"
            prop="endBalance"
            label="期末余额"
          ></el-table-column>
          <!-- 一级表头 -->
          <el-table-column
            v-if="item && item.name"
            :label="item.name"
            align="center"
          >
            <el-table-column prop="platformIncomeOriginalCurrency" label="入账">
              <template slot-scope="{ row }">
                {{ getRowVal(row.detailList, item.value, 'entry') }}
              </template>
            </el-table-column>
            <el-table-column prop="platformIncomeCny" label="扣除">
              <template slot-scope="{ row }">
                {{ getRowVal(row.detailList, item.value, 'deduction') }}
              </template>
            </el-table-column>
            <el-table-column prop="platformExpendOriginalCurrency" label="净额">
              <template slot-scope="{ row }">
                {{ getRowVal(row.detailList, item.value, 'net') }}
              </template>
            </el-table-column>
          </el-table-column>
        </div>
      </template>

      <el-table-column fixed="right" label="操作" width="100">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleClick(scope.row)">
            明细
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      ref="listPage"
      background
      :current-page.sync="pagination.pageNo"
      :page-size.sync="pagination.limit"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      @current-change="pagination_currentChange"
      @size-change="pagination_sizeChange"
    ></el-pagination>
  </div>
</template>

<script>
  export default {
    props: {
      data: {
        type: Array,
        default: () => [],
      },
      tableHead: {
        type: Array,
        default: () => [],
      },
      pagination: {
        type: Object,
        default: null,
      },
      loading: {
        type: Boolean,
        default: false,
      },
      tabType: {
        type: String,
        default: '1',
      },
    },
    data() {
      return {};
    },
    methods: {
      getRowVal(list, inx, key) {
        let val = 0;
        if (!Array.isArray(list)) return val;
        list.forEach(item => {
          if (inx === item.type) {
            val = item[key];
          }
        });
        return val;
      },
      handleClick(val) {
        this.$emit('handelDetails', val);
      },
      pagination_currentChange(val) {
        this.$emit('handlePaginationChange', val, this.pagination.limit);
      },
      pagination_sizeChange(val) {
        this.$emit('handlePaginationChange', this.pagination.pageNo, val);
      },
    },
  };
</script>
<style lang="scss" scoped>
  a {
    color: #606266;
    cursor: default !important;
  }
</style>
