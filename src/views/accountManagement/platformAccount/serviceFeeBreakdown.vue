<template>
  <div>
    <div v-if="tab === 1">
      <el-tabs v-model="tabType" type="card" @tab-click="handleClick">
        <el-tab-pane label="日账单" name="1" />
        <el-tab-pane label="月账单" name="2" />
      </el-tabs>
      <el-form inline>
        <el-form-item label="公司名称:">
          <el-input
            v-model="searchParams.accountEntityName"
            placeholder="请输入公司名称"
          />
        </el-form-item>
        <el-form-item label="账单日期:">
          <el-date-picker
            v-model="searchDate"
            :clearable="false"
            :type="tabType === '1' ? 'daterange' : 'monthrange'"
            range-separator="至"
            :value-format="tabType === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
            :format="tabType === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          ></el-date-picker>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="getList(true)">查询</el-button>
          <el-button type="primary" @click="onReset">重置</el-button>
          <el-popover placement="bottom" trigger="click">
            <p>请选择导出时间</p>
            <el-date-picker
              v-model="exportDate"
              :clearable="false"
              :type="tabType === '1' ? 'daterange' : 'monthrange'"
              range-separator="至"
              :value-format="tabType === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
              :format="tabType === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :picker-options="pickerOptions"
            ></el-date-picker>
            <div style="text-align: right; margin: 10px 0 0">
              <el-button type="primary" size="mini" @click="onExport">
                确定
              </el-button>
            </div>
            <el-button
              slot="reference"
              style="margin-left: 10px"
              type="primary"
            >
              导出
            </el-button>
          </el-popover>
          <!-- <el-button type="primary" @click="onExport">导出</el-button> -->
        </el-form-item>
      </el-form>
      <dataTable
        :table-head="tableHead"
        :loading="loading"
        :pagination="pagination"
        :data="list"
        :tab-type="tabType"
        @handlePaginationChange="handlePaginationChange"
        @handelDetails="val => handelJump(2, val)"
      ></dataTable>
    </div>

    <div v-if="tab === 2">
      <div>
        <el-button
          style="font-size: 16px; margin-bottom: 10px"
          type="text"
          @click="handelJump(1)"
        >
          返回
        </el-button>
      </div>

      <el-form inline>
        <el-form-item label="费用类型:">
          <el-select v-model="detailsParams.biz" placeholder="请选择费用类型">
            <el-option
              v-for="item in accountType"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getDetailsList(true)">
            查询
          </el-button>
          <el-button type="primary" @click="onReset">重置</el-button>
          <!-- <el-button type="primary" @click="onExport">导出</el-button> -->
        </el-form-item>
      </el-form>
      <informationTable
        :loading="loading"
        :pagination="pagination"
        :data="list"
        :on-get="getList"
        @handlePaginationChange="handlePaginationChange"
      ></informationTable>
    </div>
  </div>
</template>

<script>
  import dataTable from './components/dataTable';
  import informationTable from './components/informationTable';
  import { parseTime, downloadFile, setInitData } from '@/utils';
  import { exportExcel } from '@/api/blob';
  import {
    getAccountsummaryList,
    accountsummaryDetail,
    accountsummaryCondition,
    conditionList,
  } from '@/api/accountManagement';

  export default {
    components: {
      dataTable,
      informationTable,
    },
    data() {
      return {
        tab: 1, // 页面切换tab
        loading: false,
        list: [],
        accountType: [],
        tableHead: [],
        tabType: '1', // 账单切换tab
        searchDate: '', // 页面筛选时间
        exportDate: '', // 导出时间
        choiceDate0: '',
        pickerOptions: {
          onPick: ({ maxDate, minDate }) => {
            this.choiceDate0 = minDate.getTime();
            if (maxDate) {
              this.choiceDate0 = '';
            }
          },
          disabledDate: time => {
            if (this.choiceDate0 !== '') {
              const one = 30 * 24 * 3600 * 1000;
              const minTime = this.choiceDate0 - one;
              const maxTime = this.choiceDate0 + one;
              return time.getTime() < minTime || time.getTime() > maxTime;
            }
          },
        },

        searchParams: {
          accountEntityName: '',
          type: '',
          startTimeStr: '',
          endTimeStr: '',
        },
        detailsParams: {
          accountEntityCode: '',
          type: '',
          biz: '',
          monthDate: '',
        },
        pageNo: 1,
        // 分页
        pagination: {
          pageNo: 1,
          limit: 10,
          total: null,
        },
      };
    },
    created() {
      this.searchDate = setInitData(30);
      this.exportDate = setInitData(30);
      conditionList({}).then(res => {
        if (res) {
          let arr = [];
          const accountTypeList = res.accountType || [];
          // accountTypeList.map(item => {
          //   if (!['9', '10', '11'].includes(item.value)) {
          //     arr.push(item);
          //   }
          // });
          // arr.splice(2, 0, accountTypeList[accountTypeList.length - 3]);
          // arr.splice(4, 0, accountTypeList[accountTypeList.length - 2]);
          // arr.splice(8, 0, accountTypeList[accountTypeList.length - 1]);
          this.tableHead = accountTypeList;
        }
      });
      accountsummaryCondition({}).then(res => {
        if (res && Array.isArray(res)) {
          this.accountType = res;
        }
      });
      this.getList(true);
    },
    methods: {
      handelJump(val, row) {
        this.tab = val;
        if (val === 1) {
          // 返回记住页数
          this.pagination.pageNo = this.pageNo;

          this.getList();
        } else {
          this.init();
          Object.keys(this.detailsParams).forEach(item => {
            this.detailsParams[item] = row[item] || '';
          });
          this.getDetailsList();
        }
      },

      init() {
        this.pageNo = this.pagination.pageNo;
        Object.assign(this.$data.pagination, this.$options.data().pagination);
      },
      getParams(flag) {
        const time = flag ? this.exportDate : this.searchDate;
        this.searchParams.startTimeStr = time
          ? parseTime(time[0], this.tabType === '1' ? '{y}-{m}-{d}' : '{y}-{m}')
          : '';
        this.searchParams.endTimeStr = time
          ? parseTime(time[1], this.tabType === '1' ? '{y}-{m}-{d}' : '{y}-{m}')
          : '';
        const params = {
          ...this.searchParams,
          type: this.tabType === '1' ? 'day' : 'month',
          pageNo: this.pagination.pageNo,
          limit: this.pagination.limit,
        };
        return params;
      },
      async getList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageNo = 1;
        }
        const params = this.getParams();
        this.loading = true;
        try {
          const res = await getAccountsummaryList(params);
          if (res) {
            this.list = res.records;
            this.pagination.total = res.total;
          }
          this.loading = false;
        } catch {
          this.list = [];
          this.pagination.total = 0;
          this.loading = false;
        }
      },
      async getDetailsList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageNo = 1;
        }
        const { pageNo, limit } = this.pagination;
        const params = {
          ...this.detailsParams,
          pageNo,
          limit,
        };
        this.loading = true;
        try {
          const res = await accountsummaryDetail(params);
          if (res) {
            this.list = res.records;
            this.pagination.total = res.total;
          }
          this.loading = false;
        } catch {
          this.list = [];
          this.pagination.total = 0;
          this.loading = false;
        }
      },
      handlePaginationChange(pageNo, limit) {
        this.pagination.pageNo = pageNo;
        this.pagination.limit = limit;
        if (this.tab === 1) {
          this.getList();
        } else {
          this.getDetailsList();
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.detailsParams.biz = '';
        if (this.tab === 1) {
          this.setInit(this.tabType);
          this.getList(true);
        } else {
          this.getDetailsList(true);
        }
      },
      onExport() {
        const params =
          this.tab === 1
            ? this.getParams(true)
            : {
                ...this.detailsParams,
                pageNo: this.pagination.pageNo,
                limit: this.pagination.limit,
              };
        const exportApi =
          this.tab === 1
            ? 'accountsummary/exportData'
            : 'accountsummary/detailListExcel';
        exportExcel(
          params,
          `/api/pay-financial-report/${exportApi}`,
          'get',
        ).then(res => {
          if (res) {
            downloadFile(
              res.data,
              this.tab === 1 ? '服务费账户列表' : '服务费明细列表',
            );
          } else {
            // this.$message.error('暂无数据')
          }
        });
      },

      setInit(val) {
        const data = setInitData(30);
        if (val == '1') {
          this.exportDate = data;
          this.searchDate = data;
        } else {
          this.exportDate = [data[0], data[1]];
          this.searchDate = [data[0], data[1]];
        }
      },

      handleClick(val) {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.setInit(val.name);
        this.tabType = val.name;
        this.getList(true);
      },
    },
  };
</script>
<style lang="scss" scoped></style>
