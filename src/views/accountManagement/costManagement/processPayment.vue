<template>
  <div>
    <el-row>
      <el-col :span="4">
        <div
          :style="{
            'background-color': selected.selectedTask
              ? '#b92121'
              : 'rgba(185,33,33,0.3)',
            color: 'white',
            margin: '2%',
            padding: '5%',
            'font-weight': 'bold',
            cursor: 'pointer',
          }"
          @click="getTaskList()"
        >
          <div class="big-number">{{ statistics.taskCountByError }}</div>
          <div class="description">执行中</div>
        </div>
      </el-col>

      <el-col :span="4">
        <div
          :style="{
            'background-color': selected.selectedAllList
              ? '#0f89cb'
              : 'rgba(15,137,203,0.3)',
            color: 'white',
            margin: '2%',
            padding: '5%',
            'font-weight': 'bold',
            cursor: 'pointer',
          }"
          @click="getAllList()"
        >
          <div class="big-number">{{ statistics.countAllRequisition }}</div>
          <div class="description">所有</div>
        </div>
      </el-col>
      <el-col :span="4">
        <div
          :style="{
            'background-color': selected.selectedPaidList
              ? 'rgba(89, 141, 66, 0.98)'
              : 'rgba(89,141,66,0.3)',

            color: 'white',
            margin: '2%',
            padding: '5%',
            'font-weight': 'bold',
            cursor: 'pointer',
          }"
          @click="getPaidList()"
        >
          <div class="big-number">{{ statistics.countPaidRequisition }}</div>
          <div class="description">已付款</div>
        </div>
      </el-col>
      <el-col :span="4">
        <div
          :style="{
            'background-color': selected.selectedPendingPaymentList
              ? '#c5a726'
              : 'rgba(197,167,38,0.3)',
            color: 'white',
            margin: '2%',
            padding: '5%',
            'font-weight': 'bold',
            cursor: 'pointer',
          }"
          @click="getPendingPaymentList()"
        >
          <div class="big-number">
            {{ statistics.countPendingPaymentRequisition }}
          </div>
          <div class="description">待付款</div>
        </div>
      </el-col>
      <el-col :span="4">
        <div
          :style="{
            'background-color': selected.selectedUnrecordedBillVoucher
              ? '#e55507'
              : 'rgba(229,85,7,0.3)',
            color: 'white',
            margin: '2%',
            padding: '5%',
            'font-weight': 'bold',
            cursor: 'pointer',
          }"
          @click="getUnrecordedBillVoucher()"
        >
          <div class="big-number">
            {{ statistics.countUnrecordedBillVoucher }}
          </div>
          <div class="description">待记账单凭证（国内）</div>
        </div>
      </el-col>
      <el-col :span="4">
        <div
          :style="{
            'background-color': selected.selectedUnrecordedPaymentVoucher
              ? '#6522c5'
              : 'rgba(101,34,197,0.3)',
            color: 'white',
            margin: '2%',
            padding: '5%',
            'font-weight': 'bold',
            cursor: 'pointer',
          }"
          @click="getUnrecordedPaymentVoucher()"
        >
          <div class="big-number">
            {{ statistics.countUnrecordedPaymentVoucher }}
          </div>
          <div class="description">待记付款凭证</div>
        </div>
      </el-col>
    </el-row>

    <div v-if="payment_show" style="margin-top: 2%">
      <el-divider content-position="left">
        <i
          v-if="filter_show"
          style="cursor: pointer; color: blue"
          class="el-icon-remove-outline"
          @click="closeFilter()"
        >
          筛选
        </i>
        <i
          v-if="!filter_show"
          style="cursor: pointer; color: red"
          class="el-icon-circle-plus-outline"
          @click="openFilter()"
        >
          筛选
        </i>
      </el-divider>
    </div>

    <div v-if="!payment_show" style="margin-top: 2%">
      <el-divider></el-divider>
    </div>

    <div v-if="payment_show">
      <div v-if="filter_show">
        <el-form :inline="true" label-width="auto" size="mini">
          <el-form-item label="流程类型：">
            <el-select
              v-model="searchParams.businessLineCode"
              placeholder="请选择类型"
              clearable
            >
              <el-option
                v-for="(item, index) in businessLineList"
                :key="index"
                :label="item.value"
                :value="item.key"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="付款方式:">
            <el-select
              v-model="searchParams.paymentMethod"
              clearable
              filterable
              placeholder="请选择付款方式"
            >
              <el-option
                v-for="item in paymentMethod"
                :key="item.key"
                :label="item.value"
                :value="item.key"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="请款币种:">
            <el-select
              v-model="searchParams.requisitionCurrency"
              placeholder="请输入请款币种"
            >
              <el-option
                v-for="item in requestCurrency"
                :key="item.key"
                :label="item.value"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="请款单号:">
            <el-input
              v-model="searchParams.requestSerialNo"
              placeholder="请款单号"
            />
          </el-form-item>

          <el-form-item label="OA流程号:">
            <el-input
              v-model="searchParams.oaPaymentRequisitionNo"
              placeholder="请输入OA支付流程号"
            />
          </el-form-item>
          <el-form-item label="付款状态:">
            <el-select
              v-model="searchParams.paymentStatus"
              clearable
              placeholder="请选择付款款状态"
            >
              <el-option
                v-for="item in paymentStatus"
                :key="item.key"
                :label="item.value"
                :value="item.key"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="账单记账状态:">
            <el-select
              v-model="searchParams.billRecordVoucherStatus"
              clearable
              filterable
              placeholder="请选择记账状态"
            >
              <el-option
                v-for="item in billRecordVoucher"
                :key="item.key"
                :label="item.value"
                :value="item.key"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="付款记账状态:">
            <el-select
              v-model="searchParams.recordVoucherStatus"
              clearable
              filterable
              placeholder="请选择记账状态"
            >
              <el-option
                v-for="item in recordVoucher"
                :key="item.key"
                :label="item.value"
                :value="item.key"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="NS账户:">
            <el-select
              v-model="searchParams.tenantTag"
              clearable
              filterable
              placeholder="请选择NS账户"
            >
              <el-option
                v-for="item in tenantTagList"
                :key="item.key"
                :label="item.value"
                :value="item.key"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="付款主体:">
            <el-input
              v-model="searchParams.principalName"
              placeholder="付款主体"
            />
          </el-form-item>

          <el-form-item label="供应商名称:">
            <el-input
              v-model="searchParams.supplierName"
              placeholder="供应商名称"
            />
          </el-form-item>
          <el-form-item label="请款时间:">
            <el-date-picker
              v-model="applyDate"
              type="daterange"
              :clearable="true"
              range-separator="至"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
            ></el-date-picker>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="getList(true)">查询</el-button>
            <el-button type="primary" @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
        <el-divider></el-divider>
      </div>
      <dynamictable
        :data-source="list"
        :columns="columns"
        :options="options"
        :pagination="pagination"
        :fetch="getList"
      >
        <template slot="operationDetail" slot-scope="scope">
          <div
            style="color: blue; cursor: pointer"
            @click="viewDetails(scope.row)"
          >
            查看
          </div>
        </template>
        <template slot="paymentStatusKey" slot-scope="scope">
          <el-tag
            :type="getPaymentStatusKeyType(scope.row.paymentStatusKey)"
            effect="dark"
          >
            {{ scope.row.paymentStatusKey }}
          </el-tag>
        </template>
        <template slot="recordVoucherDescription" slot-scope="scope">
          <el-tag
            :type="getRecordVoucherType(scope.row.recordVoucherDescription)"
            effect="plain"
          >
            {{ scope.row.recordVoucherDescription }}
          </el-tag>
        </template>
        <template slot="billVoucherStatus" slot-scope="scope">
          <el-tag
            :type="getRecordVoucherType(scope.row.billVoucherStatus)"
            effect="plain"
          >
            {{ scope.row.billVoucherStatus }}
          </el-tag>
        </template>

        <template slot="oaPaymentRequisitionNo" slot-scope="scope">
          <div style="color: blue; cursor: pointer" @click="jump2OA(scope.row)">
            {{ scope.row.oaPaymentRequisitionNo }}
          </div>
        </template>
      </dynamictable>
    </div>

    <div v-if="!payment_show" class="taskList">
      <dynamictable
        :data-source="task_list"
        :columns="task_columns"
        :options="options"
        :pagination="task_pagination"
        :fetch="getTaskList"
      >
        <template slot="operationDetail" slot-scope="scope">
          <div
            style="color: blue; cursor: pointer"
            @click="viewDetails(scope.row)"
          >
            查看
          </div>
        </template>
        <template slot="oaRequestNo" slot-scope="scope">
          <div style="color: blue; cursor: pointer" @click="jump2OA(scope.row)">
            {{ scope.row.oaRequestNo }}
          </div>
        </template>
        <template slot="operation_task" slot-scope="props">
          <dynamictable
            :data-source="props.row.transactionDOList || []"
            :columns="subtask_columns"
            :options="options"
          />
        </template>

        <template slot="memo" slot-scope="scope">
          <el-popover v-if="scope.row.memo" trigger="hover" placement="top">
            <p>{{ scope.row.memo }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag size="medium" type="danger">{{ scope.row.memo }}</el-tag>
            </div>
          </el-popover>
        </template>

        <template slot="message" slot-scope="scope">
          <el-popover placement="right" width="800" trigger="click">
            <div>
              <el-scrollbar style="height: 400px; width: 780px">
                {{ scope.row.message }}
              </el-scrollbar>
            </div>
            <el-button slot="reference">查看</el-button>
          </el-popover>
        </template>
      </dynamictable>
    </div>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table/index.vue';
  import { parseTime } from '@/utils';

  import {
    getNpPaymentRequisitionDic,
    getTaskList,
    getStatistics,
    getExpenseProcessPage,
  } from '@/api/settle';
  import { red } from 'chalk';

  export default {
    name: 'ProcessPayment',
    components: {
      dynamictable,
    },
    data() {
      let columns = [
        {
          prop: 'operation',
          label: '详情',
          fixed: 'left',
          scopedSlots: { customRender: 'operationDetail' },
        },
        {
          prop: 'oaPaymentRequisitionNo',
          label: 'OA流程号',
          scopedSlots: { customRender: 'oaPaymentRequisitionNo' },
        },
        {
          prop: 'serialNo',
          label: '请款单号',
        },
        {
          prop: 'paymentSerialNo',
          label: '付款单号',
        },
        {
          prop: 'businessLineName',
          label: '流程类型',
        },
        {
          prop: 'tenantTag',
          label: 'NS标签',
        },
        {
          prop: 'buTag',
          label: 'BU标签',
        },
        {
          prop: 'principalName',
          label: '付款主体',
        },
        {
          prop: 'supplierName',
          label: '供应商',
        },
        {
          prop: 'paymentStatusKey',
          label: '付款状态',
          scopedSlots: { customRender: 'paymentStatusKey' },
        },
        {
          prop: 'paymentMethodKey',
          label: '付款方式',
        },
        {
          prop: 'totalAmount',
          label: '请款金额',
        },
        {
          prop: 'requisitionCurrency',
          label: '请款币种',
        },
        {
          prop: 'billVoucherStatus',
          label: '费用记账',
          scopedSlots: { customRender: 'billVoucherStatus' },
        },
        {
          prop: 'recordVoucherDescription',
          label: '付款记账',
          scopedSlots: { customRender: 'recordVoucherDescription' },
        },
        {
          prop: 'requisitionDate',
          label: '请款时间',
        },
        {
          prop: 'paidDate',
          label: '付款时间',
        },
        {
          prop: 'dueDate',
          label: '截至时间',
        },
      ];

      let task_columns = [
        {
          prop: 'operation',
          label: '详情',
          fixed: 'left',
          scopedSlots: { customRender: 'operationDetail' },
        },
        {
          prop: 'oaRequestNo',
          label: '流程编号',
          scopedSlots: { customRender: 'oaRequestNo' },
        },
        {
          prop: 'tenantTag',
          label: 'NS标签',
        },
        {
          prop: 'buTag',
          label: 'BU标签',
        },
        {
          prop: 'oaRequestId',
          label: '流程ID',
        },
        {
          prop: 'businessLineName',
          label: '流程类型',
        },
        {
          prop: 'typeName',
          label: '任务类型',
        },
        {
          prop: 'statusDesc',
          label: '状态',
        },
        {
          prop: 'serialId',
          label: '任务ID',
        },
        {
          prop: 'messageId',
          label: '消息ID',
        },
        {
          prop: 'coolDownExpireTime',
          label: '下次执行时间',
        },
        {
          prop: 'createTime',
          label: '创建时间',
        },
        {
          prop: 'memo',
          label: '异常描述',
          scopedSlots: { customRender: 'memo' },

          // showOverflowTooltip: true,
        },
        {
          prop: 'message',
          label: '消息内容',
          scopedSlots: { customRender: 'message' },
        },
        {
          prop: 'operation',
          label: '',
          type: 'expand',
          scopedSlots: { customRender: 'operation_task' },
        },
      ];

      let subtask_columns = [
        {
          prop: 'typeName',
          label: '子任务类型',
        },
        {
          prop: 'statusDesc',
          label: '子状态',
        },
        {
          prop: 'serialId',
          label: '子任务ID',
        },
        {
          prop: 'successTime',
          label: '成功时间',
        },
        {
          prop: 'createTime',
          label: '创建时间',
        },
        {
          prop: 'blockers',
          label: '依赖子任务列表',
        },
      ];

      return {
        isTurn: false,
        selected: {
          selectedTask: false,
          selectedAllList: true,
          selectedPaidList: false,
          selectedPendingPaymentList: false,
          selectedUnrecordedBillVoucher: false,
          selectedUnrecordedPaymentVoucher: false,
        },
        applyDate: '',
        showDialog: false,
        currentRow: null,
        searchParams: {
          requisitionDate: {
            upper: '',
            lower: '',
          },
          principalCode: '',
          principalName: '',
          //付款方式：
          paymentMethod: '',
          //请款单号
          requestSerialNo: '',
          //OA支付流程号：
          oaPaymentRequisitionNo: '',
          //付款状态
          paymentStatus: '',
          //请款币种
          requisitionCurrency: '',
          //记账状态
          recordVoucherStatus: '',
          //记账状态(账单)
          billRecordVoucherStatus: '',
          //供应商名称
          supplierName: '',
          //流程类型
          businessLineCode: '',
          //租户
          tenantTag: '',
        },
        list: [],
        task_list: [],
        statistics: {},
        payment_show: true,
        filter_show: false,

        paymentMethod: [],
        paymentStatus: [],
        recordVoucher: [],
        tenantTagList: [
          { key: 'VTN', value: 'VTN' },
          { key: 'WLZ', value: 'WLZ' },
        ],
        billRecordVoucher: [
          { key: 0, value: '未记账' },
          { key: 1, value: '已记账' },
        ],
        principalCode: [],
        requestCurrency: [],
        tallyStatus: [],
        businessLineList: [],

        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },

        task_pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
        task_options: {
          loading: false,
          border: true,
        },
        columns,
        task_columns,
        subtask_columns,
        total: {
          amountTotal: '',
        },
        jsonData: {},
      };
    },
    created() {
      // this.defaultDate();
      const { oaPaymentRequisitionNo = '' } = this.$route.query;
      if (oaPaymentRequisitionNo) {
        this.searchParams.oaPaymentRequisitionNo = oaPaymentRequisitionNo;
        this.applyDate = null;
      }
      getNpPaymentRequisitionDic().then(res => {
        if (res) {
          this.paymentMethod = res.paymentMethod;
          this.paymentStatus = res.paymentStatus;
          this.recordVoucher = res.recordVoucherStatus;
          this.principalCode = res.principalCode;
          this.requestCurrency = res.currency;
          this.businessLineList = res.businessLine;
        }
      });
      this.getList(true);
      this.getStatistics();
    },
    methods: {
      red,
      viewDetails(row) {
        this.$router.push({
          path: '/accountManagement/costManagement/processPaymentDetail',
          query: {
            oaRequestId: row.oaRequestId,
            requisitionId: row.requisitionId,
          },
        });
      },
      jump2OA(row) {
        window.open(
          `https://oa.accesscorporate.com.cn/spa/workflow/static4form/index.html?_rdm=*************#/main/workflow/req?requestid=${row.oaRequestId}&ismonitor=1&_key=y2jxyt`,
        );
      },
      getParams() {
        const { applyDate } = this;

        const params = {
          ...this.searchParams,
          requisitionDate: applyDate
            ? {
                lower: parseTime(applyDate[0], '{y}-{m}-{d} 00:00:00'),
                upper: parseTime(applyDate[1], '{y}-{m}-{d} 23:59:59'),
              }
            : null,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        return params;
      },

      getPendingPaymentList() {
        this.clearSelect();
        this.selected.selectedPendingPaymentList = true;
        this.onReset();
        this.applyDate = null;
        this.searchParams.paymentStatus = 2;
        this.getList(true);
      },
      getPaidList() {
        this.clearSelect();
        this.selected.selectedPaidList = true;
        this.onReset();
        this.applyDate = null;
        this.searchParams.paymentStatus = 4;
        this.getList(true);
      },
      getUnrecordedPaymentVoucher() {
        this.clearSelect();
        this.selected.selectedUnrecordedPaymentVoucher = true;
        this.onReset();
        this.applyDate = null;
        this.searchParams.recordVoucherStatus = 0;
        this.searchParams.paymentStatus = 4;
        this.getList(true);
      },
      getUnrecordedBillVoucher() {
        this.clearSelect();
        this.selected.selectedUnrecordedBillVoucher = true;
        this.onReset();
        this.applyDate = null;
        this.searchParams.billRecordVoucherStatus = 0;
        this.getList(true);
      },
      getAllList() {
        this.clearSelect();
        this.selected.selectedAllList = true;
        this.onReset();
        this.applyDate = null;
        this.getList(true);
      },
      async getList(isSearch) {
        this.payment_show = true;

        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await getExpenseProcessPage(params);
        this.options.loading = false;
        if (res) {
          this.list = res ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        }
        // const amountRe = await getRequisitionAmountTotal(params);
        // this.total.amountTotal = amountRe ? amountRe.amountTotal : 0;
      },
      async getStatistics() {
        this.options.loading = true;
        const res = await getStatistics();
        this.options.loading = false;
        if (res) {
          this.statistics = res;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.defaultDate();
      },
      defaultDate() {
        const end = new Date();
        const start = new Date();
        end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 31);
        this.applyDate = [
          parseTime(start, '{y}-{m}-{d} 00:00:00'),
          parseTime(end, '{y}-{m}-{d} 23:59:59'),
        ];
      },

      clearSelect() {
        this.getStatistics();
        for (const key in this.selected) {
          if (this.selected.hasOwnProperty(key)) {
            this.selected[key] = false;
          }
        }
      },
      async getTaskList() {
        this.clearSelect();
        this.selected.selectedTask = true;
        this.payment_show = false;
        // 从服务器获取任务列表数据

        this.task_options.loading = true;
        const res = await getTaskList({
          current: this.task_pagination.pageSize,
          size: this.task_pagination.pageLimit,
          status: 1,
        });
        this.task_options.loading = false;
        if (res) {
          this.task_list = res ? res.records : [];
          this.task_pagination.total = res ? res.total : 0;
        }
      },
      rowClassName({ row }) {
        // if (row === 1 && (row.memo === null || row.memo === '')) {
        if (row.memo) {
          console.log('warning - row');
          return 'warning-row';
        }
        return '';
      },
      open(row) {
        this.jsonData = row.message;
        this.showDialog = true;
      },
      onClose() {
        this.jsonData = null;
        this.showDialog = false;
      },

      openFilter() {
        this.filter_show = true;
      },

      closeFilter() {
        this.filter_show = false;
      },
      getPaymentStatusKeyType(paymentStatusKey) {
        if (paymentStatusKey === '待付款') {
          return 'warning';
        } else if (
          paymentStatusKey === '已付款' ||
          paymentStatusKey === '已抵扣'
        ) {
          return 'success';
        } else if (paymentStatusKey === '驳回/打款失败') {
          return 'danger';
        } else if (paymentStatusKey === '已请款') {
          return '';
        } else {
          return 'info';
        }
      },
      getRecordVoucherType(recordVoucherDescription) {
        if (recordVoucherDescription === '未记账') {
          return 'warning';
        } else if (recordVoucherDescription === '已记账') {
          return 'success';
        } else {
          return 'info';
        }
      },
    },
  };
</script>
<style lang="scss">
  .red-box {
    background-color: red;
    color: white;
    padding: 20px;
    margin: 10px;
    text-align: center;
  }

  .big-number {
    font-size: 24px;
    font-weight: bold;
  }

  .description {
    font-size: 14px;
    margin-top: 10px;
    font-weight: bold;
  }

  .taskList {
    .el-table .warning-row {
      background: rgba(232, 93, 18, 0.1) !important;

      td {
        background: rgba(232, 93, 18, 0.1) !important;
      }
    }

    .el-table a {
      cursor: pointer;
    }
  }
</style>
