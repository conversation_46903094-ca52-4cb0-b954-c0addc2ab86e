<!--
 * @Description: 
 * @Author: 项萍～
 * @Date: 2021-08-16 10:05:27
 * @LastEditTime: 2023-06-05 17:59:11
 * @LastEditors: dddd
 * @Reference: 
-->
<template>
  <div class="journal">
    <el-form ref="searchForm" :model="searchForm" label-width="120px" inline>
      <el-form-item label="业务单号：">
        <el-input
          v-model="searchForm.businessOrderNo"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="付款主体：">
        <el-input
          v-model="searchForm.principalName"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="付款状态：">
        <el-select
          v-model="searchForm.paymentStatusList"
          placeholder="请选择状态"
          clearable
          multiple
        >
          <el-option
            v-for="(item, index) in paymentStatusList"
            :key="index"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="流程类型：">
        <el-select
          v-model="searchForm.businessLineCode"
          placeholder="请选择类型"
          clearable
        >
          <el-option
            v-for="(item, index) in businessLineList"
            :key="index"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="收款主体：">
        <el-input
          v-model="searchForm.supplierName"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="账期日期：">
        <el-date-picker
          v-model="searchForm.billDate"
          placeholder="请选择"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="申请人：">
        <el-input
          v-model="searchForm.applicant"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="应付单号(NS账单凭证号)："
        label-width="200px"
        prop="externalId"
      >
        <el-input v-model="searchForm.billNo" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item
        label="付款单号(NS付款单凭证号)："
        label-width="200px"
        prop="externalId"
      >
        <el-input
          v-model="searchForm.requisitionNo"
          placeholder="请输入"
        ></el-input>
      </el-form-item>

      <el-form-item style="margin-left: 20px">
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <!-- v-if=" (scope.row.status == 0 && scope.row.approvalStatus != 1 &&
        scope.row.approvalStatus != 3) || (scope.row.status != 0 &&
        scope.row.approvalStatus == 1) " -->
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="查看"
          @click="moneyLink(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <el-dialog
      title="节点时间"
      width="40%"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
      @closed="onClose"
    >
      <el-table class="table" :data="dataList" border>
        <el-table-column
          label="节点"
          prop="decs"
          align="center"
          min-width="140"
        ></el-table-column>
        <el-table-column
          label="状态"
          prop="statusDesc"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          label="成功时间"
          prop="successTime"
          align="center"
          min-width="180"
        ></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import { getCostDocumentData, getLinkTime } from '@/api/costManagement';
  import { getNpPaymentRequisitionDic } from '@/api/settle';
  export default {
    name: 'CostDocument',
    components: {
      dynamictable,
    },

    data() {
      let columns = [
        {
          prop: 'expenseId',
          label: '费用单号',
        },
        {
          prop: 'businessDomain',
          label: '业务域',
        },
        {
          prop: 'businessOrderNo',
          label: '业务单号',
          width: '180px',
        },
        {
          prop: 'businessLineName',
          label: '流程类型',
        },

        {
          prop: 'amount',
          label: '金额',
        },
        {
          prop: 'currency',
          label: '币种',
        },
        {
          prop: 'payeeName',
          label: '收款人名称',
        },
        {
          prop: 'projectNo',
          label: '项目号',
        },
        {
          prop: 'billDate',
          label: '账单日期',
          width: '120px',
        },
        {
          prop: 'expenseAuxiliaryItem',
          label: '费用辅助项',
        },
        {
          prop: 'expenseItem',
          label: '费用项',
        },
        {
          prop: 'brand',
          label: '品牌',
        },
        {
          prop: 'channelBigClass',
          label: '渠道大类',
        },
        {
          prop: 'channelMinClass',
          label: '渠道小类',
        },
        {
          prop: 'paymentSubject',
          label: '付款主体',
          width: '200px',
        },
        {
          prop: 'actualCurrency',
          label: '实付币种',
        },
        {
          prop: 'paymentBank',
          label: '付款银行',
        },
        {
          prop: 'paymentAccountNo',
          label: '付款账号',
          width: '150px',
        },
        {
          prop: 'applicant',
          label: '申请人',
          width: '150px',
        },
        {
          prop: 'billExternalId',
          label: '应付单号',
        },
        {
          prop: 'requisitionNo',
          label: '付款单号',
          width: '180px',
        },
        {
          prop: 'warehouseAddress',
          label: '仓库地址',
        },
        {
          prop: 'rentalAddress',
          label: '房租地址',
        },
        {
          prop: 'paymentStatus',
          label: '付款状态',
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '100',
          scopedSlots: { customRender: 'operation' },
        },
      ];
      return {
        searchForm: {
          businessOrderNo: null,
          principalName: null,
          paymentStatusList: [],
          supplierName: null,
          billDate: [],
          billDateStart: null,
          billDateEnd: null,
          applicant: null,
          billNo: null,
          requisitionNo: null,
          businessLineCode: null,
        },
        subsidiaryList: [],
        dataList: [
          {
            decs: '',
            statusDesc: '',
            successTime: '',
          },
        ],
        paymentStatusList: [],
        businessLineList: [],
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
        columns,
        options1: false,
        showDialog: false,
      };
    },

    created() {
      this.getList(true);
      this.screen();
    },
    methods: {
      // 获取公司主体名称
      async screen() {
        getNpPaymentRequisitionDic().then(res => {
          if (res) {
            this.paymentStatusList = res.paymentStatus;
            this.businessLineList = res.businessLine;
          }
        });
      },
      getParams() {
        const params = {
          ...this.searchForm,
          size: this.pagination.pageLimit,
          current: this.pagination.pageSize,
        };
        if (this.searchForm.billDate && this.searchForm.billDate.length > 0) {
          params.billDateStart = this.searchForm.billDate[0];
          params.billDateEnd = this.searchForm.billDate[1];
        }
        delete params.billDate;
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await getCostDocumentData(params);
        this.options.loading = false;
        this.list = res ? res.records : [];
        this.pagination.total = res ? res.total : 0;
      },
      async moneyLink(row) {
        // this.options.loading= true;
        console.log('row.businessOrderNo', row);
        const res = await getLinkTime(row.businessOrderNo);
        if (res) {
          this.dataList = res || [];
          this.showDialog = true;
        }
        // this.options.loading= false;
      },
      // 重置
      resetForm() {
        Object.assign(this.$data.searchForm, this.$options.data().searchForm);
        this.getList(true);
      },
      onClose() {
        this.showDialog = false;
      },
    },
  };
</script>
<style></style>
