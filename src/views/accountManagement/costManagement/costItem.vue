<!--
 * @Description: 
 * @Author: 丁永冲
 * @Date: 2021-07-06 09:56:30
 * @LastEditTime: 2023-05-25 17:22:50
 * @LastEditors: dddd
 * @Reference: 
-->
<template>
  <div>
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      label-width="100px"
      class="demo-ruleForm"
      inline
    >
      <el-form-item label="业务域">
        <el-select
          v-model="ruleForm.businessCode"
          placeholder="请选择业务域"
          clearable
        >
          <el-option
            v-for="(item, index) in businessList"
            :key="index"
            :label="item.label"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item style="margin-left: 20px">
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
        <ac-permission-button
          type="primary"
          btn-text="新增"
          permission-key="message_add"
          plain
          @click="addHandle()"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <!-- v-if=" (scope.row.status == 0 && scope.row.approvalStatus != 1 &&
        scope.row.approvalStatus != 3) || (scope.row.status != 0 &&
        scope.row.approvalStatus == 1) " -->
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="编辑"
          permission-key="message_edit"
          @click="addHandle(scope.row)"
        ></ac-permission-button>
        <!-- v-if="scope.row.approvalStatus == 1 && scope.row.status != 0" -->
        <ac-permission-button
          :key="scope.row.id"
          slot-btn="reference"
          node-type="popconfirm"
          :title="scope.row.status == 0 ? '确定启用吗？' : '确定禁用吗？'"
          type="text"
          size="small"
          :btn-text="scope.row.status == 1 ? '禁用' : '启用'"
          :permission-key="
            scope.row.status == 1 ? 'message_close' : 'message_open'
          "
          @click="handelOperate(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <!-- 新增销售主体/编辑销售主体 -->
    <el-dialog
      width="40%"
      :title="formData.expenseId ? '编辑' : '新增'"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
      @closed="onClose"
    >
      <el-form
        ref="formData"
        :model="formData"
        label-width="80px"
        :rules="rulesFormData"
      >
        <el-form-item label="业务域" prop="businessCode">
          <el-select
            v-model="formData.businessCode"
            placeholder="请选择业务域"
            clearable
            :disabled="!!formData.expenseId"
            @change="changeBusinessCode"
          >
            <el-option
              v-for="(item, index) in businessList"
              :key="index"
              :label="item.label"
              :value="item.code"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="费用项目" prop="expenseDesc">
          <el-input
            v-model="formData.expenseDesc"
            placeholder="请输入费用项"
            style="width: 195px"
          ></el-input>
        </el-form-item>
        <el-form-item label="上级费用">
          <el-cascader
            v-model="formData.costId"
            :options="cascaderOptions"
            :props="{ checkStrictly: true }"
            clearable
          ></el-cascader>
        </el-form-item>
        <el-form-item>
          <el-button @click="showDialog = false">取消</el-button>
          <el-button type="primary" @click="saveForm()">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    getListData,
    getBusinessList,
    getTree,
    saveAdd,
    saveUpdate,
    updateStatus,
  } from '@/api/costManagement';

  export default {
    name: 'CostItem',
    components: {
      dynamictable,
    },

    data() {
      let columns = [
        {
          prop: 'expenseId',
          label: '编号',
        },
        {
          prop: 'businessDesc',
          label: '业务域',
        },
        {
          prop: 'expenseDesc',
          label: '费用项',
        },
        {
          prop: 'expenseLevel',
          label: '费用层级',
        },
        {
          prop: 'expenseParentDesc',
          label: '上级费用',
        },
        {
          prop: 'statusDesc',
          label: '状态',
        },

        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '160',
          scopedSlots: { customRender: 'operation' },
        },
      ];
      return {
        cascaderOptions: [],
        ruleForm: {
          businessCode: '', //业务域
        },

        businessList: [],
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },

        options: {
          loading: false,
          border: true,
          mutiSelect: false,
        },
        columns,
        // 弹窗
        showDialog: false,
        formData: { businessCode: null, expenseDesc: null, costId: [] },
        rulesFormData: {
          businessCode: [
            { required: true, message: '请选择业务域', trigger: 'change' },
          ],
          expenseDesc: [
            { required: true, message: '请输入费用项', trigger: 'change' },
          ],
        },
      };
    },

    created() {
      this.getList(true);
      this.screen();
    },
    methods: {
      async screen() {
        const res = await getBusinessList();
        this.businessList = res || [];
      },
      changeBusinessCode(code) {
        this.getTree(code);
      },
      async getTree(code) {
        const res = await getTree({ businessCode: code });
        this.cascaderOptions = res || [];
      },
      onClose() {
        this.formData = Object.assign({}, this.$options.data().formData);
        this.$nextTick(() => {
          this.$refs.formData.clearValidate();
        });
      },
      // 重置
      resetForm() {
        this.ruleForm = Object.assign({}, this.$options.data().ruleForm);
        this.$nextTick(() => {
          this.$refs.ruleForm.clearValidate();
        });

        this.getList(true);
      },
      // 搜索
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }

        this.options.loading = true;
        let params = {
          ...this.ruleForm,
          size: this.pagination.pageLimit,
          current: this.pagination.pageSize,
        };
        try {
          const res = await getListData(params);

          this.options.loading = false;
          this.list = res ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        } catch (error) {
          this.options.loading = false;
        }
      },

      // 新增/编辑
      async addHandle(row) {
        this.showDialog = true;
        if (row && row.expenseId) {
          this.getTree(row.businessCode);
          this.formData = {
            ...row,
          };
        } else {
          this.formData = Object.assign({}, this.$options.data().formData);

          this.$nextTick(() => {
            this.$refs.formData.clearValidate();
          });
        }
      },

      // 启用，禁用
      async handelOperate(row) {
        let params = {
          id: row.expenseId,
          status: row.status,
        };
        const res = await updateStatus(params);
        this.$message.success('操作成功');
        this.getList();
      },
      // 弹窗保存
      async saveForm() {
        this.$refs.formData.validate(async valid => {
          if (valid) {
            if (this.formData.expenseId) {
              const res = await saveUpdate({ ...this.formData });
              this.getList();
              this.$message.success('编辑成功');
              this.showDialog = false;
            } else {
              const res = await saveAdd({ ...this.formData });
              this.getList(true);
              this.$message.success('新增成功');
              this.showDialog = false;
            }
          }
        });
      },
    },
  };
</script>
<style>
  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
