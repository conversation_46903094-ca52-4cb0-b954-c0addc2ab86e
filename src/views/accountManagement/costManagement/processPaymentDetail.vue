<template>
  <div>
    <el-form inline>
      <el-row>
        <el-col :span="6">
          <el-form-item label="流程类型:">
            {{ requisitionInfo.businessLineName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="付款主体:">
            {{ requisitionInfo.principalName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="供应商:">
            {{ requisitionInfo.supplierName }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="申请人:">
            {{ requisitionInfo.submitter }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="OA单号:" @click="jump2OA()">
            <div style="color: blue; cursor: pointer" @click="jump2OA()">
              {{ requisitionInfo.oaPaymentRequisitionNo }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="OA流程ID:">
            {{ requisitionInfo.oaRequestId }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="请款单号:">
            {{ requisitionInfo.serialNo }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="付款单号:">
            {{ requisitionInfo.paymentSerialNo }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="请款时间:">
            {{ requisitionInfo.scheduleTime }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="请款金额:">
            {{ requisitionInfo.amount }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="请款币种:">
            {{ requisitionInfo.currency }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="请款类型:">
            {{ requisitionInfo.payBizTypeCode }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="实付时间:">
            {{ requisitionInfo.paidTime }}
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="实付金额:">
            {{ requisitionInfo.paidAmount }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="实付币种:">
            {{ requisitionInfo.paidCurrency }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="实付账户:">
            {{ requisitionInfo.paidAccount }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="实付汇率:">
            {{ requisitionInfo.paidExchangeRate }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="请款状态:">
            <el-tag
              :type="getPaymentStatusKeyType(requisitionInfo.statusDesc)"
              effect="plain"
            >
              {{ requisitionInfo.statusDesc }}
            </el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="凭证状态:">
            <el-tag
              :type="
                getRecordVoucherDescriptionType(
                  requisitionInfo.recordVoucherStatusDesc,
                )
              "
              effect="plain"
            >
              {{ requisitionInfo.recordVoucherStatusDesc }}
            </el-tag>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-divider content-position="left">
      <i class="el-icon-cpu" @click="addDev"></i>
    </el-divider>
    <el-tabs v-model="activeName" type="card" @tab-click="handleTab">
      <el-tab-pane label="费用明细" name="1" />
      <el-tab-pane label="账单凭证" name="2" />
      <el-tab-pane label="OA流程" name="3" />
      <el-tab-pane label="任务流程" name="4" />
      <!--      <el-tab-pane label="系统日志" name="5" />-->
    </el-tabs>

    <div v-if="activeName === '1'">
      <dynamictable
        :data-source="expenseInfoList"
        :columns="expenseColumns"
        :options="expense_options"
        :fetch="getExpenseList"
      ></dynamictable>
    </div>
    <div v-if="activeName === '2'">
      <dynamictable
        :data-source="voucherList"
        :columns="voucherColumns"
        :options="voucher_options"
        :fetch="getVoucherList"
      >
        <template slot="voucherStatusOption" slot-scope="scope">
          <el-tag
            :type="getRecordVoucherDescriptionType(scope.row.voucherStatus)"
            effect="plain"
          >
            {{ scope.row.voucherStatus }}
          </el-tag>
        </template>
      </dynamictable>
    </div>
    <div v-if="activeName === '3'">
      <dynamictable
        :data-source="oAApprovalProcessInfoList"
        :columns="oAApprovalColumns"
        :options="oa_options"
        :fetch="getOAApprovalProcessInfo"
      ></dynamictable>
    </div>
    <div v-if="activeName === '4'" class="taskList">
      <dynamictable
        :data-source="task_list"
        :columns="task_columns"
        :options="task_options"
        :fetch="getTaskList"
      >
        <template slot="oaRequestNo" slot-scope="scope">
          {{ scope.row.oaRequestNo }}
          <i
            v-if="dev > 5"
            class="el-icon-delete"
            style="cursor: pointer"
            @click="removeRequestLogAction(scope.row.oaRequestNo)"
          ></i>
        </template>

        <template slot="serialId" slot-scope="scope">
          {{ scope.row.serialId }}
          <i
            v-if="dev > 5 && scope.row.status !== 2"
            class="el-icon-video-play"
            style="cursor: pointer"
            @click="executeJobAction(scope.row.serialId)"
          ></i>
          <i
            v-if="dev > 5"
            style="cursor: pointer"
            class="el-icon-video-pause"
            @click="stopJobAction(scope.row.serialId)"
          ></i>
        </template>
        <template slot="memo" slot-scope="scope">
          <el-popover v-if="scope.row.memo" trigger="hover" placement="top">
            <p>{{ scope.row.memo }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag size="medium" type="danger">{{ scope.row.memo }}</el-tag>
            </div>
          </el-popover>
        </template>

        <template slot="operation_task" slot-scope="props">
          <dynamictable
            :data-source="props.row.transactionDOList || []"
            :columns="subtask_columns"
            :options="task_options"
          >
            <template slot="serialId" slot-scope="scope">
              {{ scope.row.serialId }}
              <i
                v-if="dev > 5 && scope.row.typeName === '创建日记账'"
                class="el-icon-refresh"
                style="cursor: pointer"
                @click="restartTransactionAction(scope.row.serialId)"
              ></i>
              <i
                v-if="
                  dev > 5 &&
                  ['创建日记账', '创建日记账确认'].includes(scope.row.typeName)
                "
                style="cursor: pointer"
                class="el-icon-check"
                @click="successTransactionAction(scope.row.serialId)"
              ></i>
            </template>
          </dynamictable>
        </template>

        <template slot="message" slot-scope="scope">
          <el-popover placement="top" width="800" trigger="click">
            <div>
              <el-scrollbar style="height: 400px; width: 780px">
                {{ scope.row.message }}
              </el-scrollbar>
            </div>
            <el-button slot="reference">查看</el-button>
          </el-popover>
          <el-popover placement="top" width="800" trigger="click">
            <div>
              <el-scrollbar style="height: 400px; width: 780px">
                {{ requestLog }}
              </el-scrollbar>
            </div>
            <el-button
              slot="reference"
              @click="queryNsBillLogAction(scope.row.oaRequestNo)"
            >
              查看请求
            </el-button>
          </el-popover>
        </template>

        <!--        <template slot="message" slot-scope="scope">-->
        <!--          <ac-permission-button-->
        <!--            slot="reference"-->
        <!--            type="text"-->
        <!--            size="small"-->
        <!--            btn-text="查看"-->
        <!--            @click="openSubTask(scope.row)"-->
        <!--          ></ac-permission-button>-->

        <!--          <el-dialog-->
        <!--            title="消息正文"-->
        <!--            width="50%"-->
        <!--            append-to-body-->
        <!--            :visible.sync="showDialog"-->
        <!--            @closed="closeSubTask"-->
        <!--            @before-close="closeSubTask"-->
        <!--          >-->
        <!--            <div>-->
        <!--              <pre>{{ jsonData }}</pre>-->
        <!--            </div>-->
        <!--          </el-dialog>-->
        <!--        </template>-->
      </dynamictable>
    </div>
  </div>
</template>
<script>
  import {
    getByOaRequestId,
    getExpenseList,
    getNpPaymentRequisitionPage,
    getOAApprovalProcessInfo,
    getTaskList,
    getVoucherList,
  } from '@/api/settle';
  import {
    deleteRequestLog,
    executeJob,
    stopJob,
    queryNsBillLog,
    restartTransaction,
    successTransaction,
  } from '@/api/financeBill';
  import dynamictable from '@/components/dynamic-table/index.vue';
  import Clipboard from 'clipboard';
  // import JsonViewer from 'vue-json-viewer';

  window.onload = function () {
    // 调用 init 方法
    this.init();
  };
  export default {
    name: 'ProcessPaymentDetail',
    components: { dynamictable },

    data() {
      let expenseColumns = [
        {
          prop: 'expenseId',
          label: '费用单号',
        },
        {
          prop: 'expeseAmount',
          label: '金额',
        },
        {
          prop: 'projectNo',
          label: '项目号',
        },
        {
          prop: 'invoiceCode',
          label: '发票号',
        },
        {
          prop: 'invoiceType',
          label: '发票类型',
        },
        {
          prop: 'invoiceId',
          label: '发票ID',
        },
        {
          prop: 'expenseItem',
          label: '费用项',
        },
        {
          prop: 'brand',
          label: '品牌',
        },
        {
          prop: 'classNamePath',
          label: '渠道',
        },
        {
          prop: 'warehouseAddress',
          label: '仓库地址',
        },
        {
          prop: 'rentalAddress',
          label: '房租地址',
        },
      ];
      let oAApprovalColumns = [
        {
          prop: 'time',
          label: '时间',
        },
        {
          prop: 'nodeName',
          label: '节点',
        },
        {
          prop: 'sqr',
          label: '提交人',
        },

        {
          prop: 'remark',
          label: '备注',
        },
      ];
      let voucherColumns = [
        {
          prop: 'voucherType',
          label: '凭证类型',
        },
        {
          prop: 'voucherStatus',
          label: '凭证状态',
          scopedSlots: { customRender: 'voucherStatusOption' },
        },
        {
          prop: 'recordVoucher',
          label: '是否记录凭证',
        },
        {
          prop: 'voucherId',
          label: 'NS内部ID',
        },
        {
          prop: 'voucherExternalId',
          label: 'NS外部ID',
        },
        {
          prop: 'creditVoucherId',
          label: 'NS冲销内部ID',
        },
      ];
      let task_columns = [
        {
          prop: 'oaRequestNo',
          label: '流程编号',
          scopedSlots: { customRender: 'oaRequestNo' },
        },
        {
          prop: 'typeName',
          label: '任务类型',
        },
        {
          prop: 'statusDesc',
          label: '状态',
        },
        {
          prop: 'serialId',
          label: '任务ID',
          scopedSlots: { customRender: 'serialId' },
        },
        {
          prop: 'messageId',
          label: '消息ID',
        },
        {
          prop: 'coolDownExpireTime',
          label: '下次执行时间',
        },
        {
          prop: 'createTime',
          label: '创建时间',
        },
        {
          prop: 'memo',
          label: '异常描述',
          // showOverflowTooltip: true,
          scopedSlots: { customRender: 'memo' },
        },
        {
          prop: 'message',
          label: '消息内容',
          scopedSlots: { customRender: 'message' },
        },
        {
          prop: 'operation',
          label: '',
          type: 'expand',
          scopedSlots: { customRender: 'operation_task' },
        },
      ];

      let subtask_columns = [
        {
          prop: 'typeName',
          label: '子任务类型',
        },
        {
          prop: 'statusDesc',
          label: '子状态',
        },
        {
          prop: 'serialId',
          label: '子任务ID',
          scopedSlots: { customRender: 'serialId' },
        },
        {
          prop: 'successTime',
          label: '成功时间',
        },
        {
          prop: 'createTime',
          label: '创建时间',
        },
        {
          prop: 'blockers',
          label: '依赖子任务列表',
        },
      ];

      return {
        tab: '0',
        list: [],
        task_list: [],
        dev: 0,
        requestLog: '',
        expenseInfoList: [],
        oAApprovalProcessInfoList: [],
        voucherList: [],
        showDialog: false,

        activeName: '1',
        oaPaymentRequisitionNo: '',
        oaRequestId: '',
        requisitionId: '',
        billIdList: [],
        requisitionInfo: {},
        btnLoading: false,
        options: {
          loading: false,
          border: true,
        },
        expense_options: {
          loading: false,
          border: true,
        },
        voucher_options: {
          loading: false,
          border: true,
        },
        oa_options: {
          loading: false,
          border: true,
        },
        task_options: {
          loading: false,
          border: true,
        },
        jsonData: {},
        expenseColumns,
        oAApprovalColumns,
        voucherColumns,
        task_columns,
        subtask_columns,
      };
    },

    created() {
      this.requisitionInfo = this.$route.query;
      this.oaRequestId = this.requisitionInfo.oaRequestId;
      this.requisitionId = this.requisitionInfo.requisitionId;
      this.getRequisitionInfo();
    },

    methods: {
      addDev() {
        this.dev += 1;
      },
      handleClick() {
        if (this.tab === '5') {
          getNpPaymentRequisitionPage({}).then(r => {
            const { err, res } = r;
            if (!err && res) {
              this.list = res[0];
            }
          });
        }
      },
      handleTab() {
        if (this.activeName === '1') {
          this.getExpenseList();
        } else if (this.activeName === '2') {
          this.getVoucherList();
        } else if (this.activeName === '3') {
          this.getOAApprovalProcessInfo();
        } else if (this.activeName === '4') {
          this.getTaskList();
        } else {
          // this.getLog();
        }
      },
      rowClassName({ row }) {
        if (row.memo) {
          return 'warning-row';
        }
        return '';
      },
      getRequisitionInfo() {
        const { requisitionId, oaRequestId } = this.$route.query;

        getByOaRequestId({
          oaRequestId: oaRequestId,
          requisitionId: requisitionId,
        }).then(res => {
          if (res) {
            this.requisitionInfo = res;
            this.requisitionInfo.oaPaymentRequisitionNo =
              res.oaRequisitionSerialNo;
            this.requisitionInfo.oaRequestId = res.oaId;
            this.billIdList = res.billIdList;
            this.requisitionId = res.id;
            this.getExpenseList(res.billIdList);
          }
        });
      },
      queryNsBillLogAction(oaCode) {
        if (oaCode) {
          queryNsBillLog(oaCode).then(res => {
            if (res) {
              this.requestLog = res;
              if (navigator.clipboard) {
                navigator.clipboard
                  .writeText(this.requestLog)
                  .then(() => {
                    this.$message({ type: 'success', message: '复制成功' }); // 假设您使用了 Element UI 或类似的库来显示消息
                  })
                  .catch(err => {
                    console.error('无法复制文本：', err);
                    this.$message({
                      type: 'error',
                      message: '复制失败，请检查您的浏览器权限设置。',
                    }); // 同样，这里假设了消息显示方法
                  });
              } else {
                console.error('浏览器不支持 navigator.clipboard API');
                this.$message({
                  type: 'error',
                  message: '您的浏览器不支持复制功能，请尝试使用其他方法。',
                });
                // 这里可以添加回退方案，比如使用 document.execCommand('copy')（尽管不推荐）
              }
            }
          });
        }
      },
      getOAApprovalProcessInfo() {
        this.oa_options.loading = true;
        getOAApprovalProcessInfo({
          requestId: this.oaRequestId,
        }).then(res => {
          if (res) {
            this.oAApprovalProcessInfoList = res;
          }
        });
        this.oa_options.loading = false;
      },

      getExpenseList() {
        this.expense_options.loading = true;
        getExpenseList(this.billIdList).then(res => {
          if (res) {
            this.expenseInfoList = res;
          }
        });
        this.expense_options.loading = false;
      },
      getVoucherList() {
        this.voucher_options.loading = true;
        getVoucherList({ requisitionId: this.requisitionId }).then(res => {
          if (res) {
            this.voucherList = res;
          }
        });
        this.voucher_options.loading = false;
      },
      removeRequestLogAction(oaCode) {
        if (oaCode) {
          deleteRequestLog(oaCode).then(res => {
            if (res) {
              this.$message.success('请求日志已删除');
            }
          });
        }
      },
      restartTransactionAction(id) {
        restartTransaction(id).then(res => {
          this.$message.success('执行成功');
        });
      },
      executeJobAction(id) {
        executeJob(id).then(res => {
          this.$message.success('执行成功');
        });
      },
      stopJobAction(id) {
        stopJob(id).then(res => {
          this.$message.success('执行成功');
        });
      },
      successTransactionAction(id) {
        successTransaction(id).then(res => {
          this.$message.success('执行成功');
        });
      },
      async getTaskList() {
        this.task_options.loading = true;
        const res = await getTaskList({
          current: 1,
          size: 10,
          bizNoList: [this.oaRequestId, this.requisitionInfo.serialNo],
        });
        this.task_options.loading = false;
        if (res) {
          this.task_list = res ? res.records : [];
        }
      },
      jump2OA() {
        window.open(
          `https://oa.accesscorporate.com.cn/spa/workflow/static4form/index.html?_rdm=1699264242898#/main/workflow/req?requestid=${this.requisitionInfo.oaRequestId}&ismonitor=1&_key=y2jxyt`,
        );
      },
      getPaymentStatusKeyType(paymentStatusKey) {
        if (paymentStatusKey === '待付款') {
          return 'warning';
        } else if (
          paymentStatusKey === '已付款' ||
          paymentStatusKey === '已抵扣'
        ) {
          return 'success';
        } else if (paymentStatusKey === '驳回/打款失败') {
          return 'danger';
        } else if (paymentStatusKey === '已请款') {
          return '';
        } else {
          return 'info';
        }
      },
      getRecordVoucherDescriptionType(recordVoucherDescription) {
        if (recordVoucherDescription === '未记账') {
          return 'warning';
        } else if (recordVoucherDescription === '已记账') {
          return 'success';
        } else {
          return 'info';
        }
      },
      openSubTask(row) {
        this.jsonData = JSON.stringify(row.message, null, 4);
        console.log(this.jsonData);
        this.showDialog = true;
      },
      closeSubTask() {
        this.jsonData = null;
        this.showDialog = false;
      },
    },
  };
</script>
<style>
  .taskList {
    .el-table .warning-row {
      background: rgba(232, 93, 18, 0.1) !important;

      td {
        background: rgba(232, 93, 18, 0.1) !important;
      }
    }

    .el-table a {
      cursor: pointer;
    }
  }
</style>
