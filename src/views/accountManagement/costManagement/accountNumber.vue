<template>
  <div class="journal">
    <el-form ref="searchForm" :model="searchForm" inline class="search-form">
      <el-row type="flex" class="search-row">
        <el-col :span="6">
          <el-form-item label="流程类型：">
            <el-select
              v-model="searchForm.businessLine"
              placeholder="请选择类型"
              clearable
            >
              <el-option
                v-for="(item, index) in businessLineList"
                :key="index"
                :label="item.value"
                :value="item.key"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="小类ID：">
            <el-input
              v-model="searchForm.oaFeeId"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="科目号：">
            <el-input
              v-model="searchForm.accountNumber"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="科目名称：">
            <el-input
              v-model="searchForm.subjectName"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="是否存量：">
            <el-select v-model="searchForm.oldFlag" placeholder="请输入">
              <el-option label="老" :value="1" />
              <el-option label="新" :value="0" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="" class="search-buttons">
            <el-button type="primary" @click="getList(true)">查询</el-button>
            <el-button @click="newAccountAction()">新增</el-button>
            <el-button @click="resetForm()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="credit" slot-scope="scope">
        <el-tag v-if="scope.row.credit">credit</el-tag>
        <el-tag v-else type="info">debit</el-tag>
      </template>
      <template slot="oldFlag" slot-scope="scope">
        <el-tag v-if="scope.row.oldFlag === 1" type="danger">老</el-tag>
        <el-tag v-else type="info">新</el-tag>
      </template>
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="编辑"
          @click="editorAction(scope.row)"
        ></ac-permission-button>
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="删除"
          @click="removeAction(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <el-dialog
      title="密码确认"
      :visible.sync="passwordDialog"
      width="30%"
      :close-on-click-modal="false"
      @closed="closePasswordDialog"
    >
      <el-form
        ref="passwordForm"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="80px"
      >
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="passwordForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closePasswordDialog">取 消</el-button>
        <el-button type="primary" @click="confirmPassword">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="编辑"
      :visible.sync="editDialog"
      width="50%"
      :close-on-click-modal="false"
      @closed="closeEditDialog"
    >
      <el-form
        ref="editForm"
        :model="editForm"
        :rules="rules"
        label-width="120px"
        class="edit-form"
      >
        <el-form-item label="科目" prop="subjectName">
          <el-input
            v-model="editForm.subjectName"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="科目编码" prop="subjectNumber">
          <el-input
            v-model="editForm.subjectNumber"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="流程类型" prop="businessLine">
          <el-select
            v-model="editForm.businessLine"
            placeholder="请选择类型"
            clearable
          >
            <el-option
              v-for="(item, index) in businessLineList"
              :key="index"
              :label="item.value"
              :value="item.key"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="OA费用" prop="oaFeeId">
          <el-select
            v-model="editForm.oaFeeId"
            filterable
            remote
            reserve-keyword
            placeholder="请输入关键词"
            :remote-method="searchOaFeeList"
            :loading="feeLoading"
            @change="computeOaFeeId"
          >
            <el-option
              v-for="item in feeOptions"
              :key="item.id"
              :label="item.smallName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="条件表达式" prop="expression">
          <el-input
            v-model="editForm.expression"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="条件字段" prop="amountField">
          <el-input
            v-model="editForm.amountField"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
        <el-form-item label="借贷标记" prop="credit">
          <el-select v-model="editForm.credit" placeholder="请选择">
            <el-option label="credit" :value="true" />
            <el-option label="debit" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="配置描述" prop="desc">
          <el-input
            v-model="editForm.desc"
            type="textarea"
            :rows="3"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeEditDialog">取 消</el-button>
        <el-button type="primary" @click="submitEdit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    getListData,
    saveAccountNumber,
    removeId,
    listFeeByName,
  } from '@/api/accountNumber';

  import { getNpPaymentRequisitionDic } from '@/api/settle';

  export default {
    name: 'AccountNumber',
    components: {
      dynamictable,
    },

    data() {
      let columns = [
        {
          prop: 'subjectName',
          label: '科目',
          width: '180px',
        },
        {
          prop: 'subjectNumber',
          label: '科目编码',
        },
        {
          prop: 'businessLine',
          label: '流程类型',
        },
        {
          prop: 'expression',
          label: '条件表达式',
        },
        {
          prop: 'amountField',
          label: '条件字段',
        },
        {
          prop: 'credit',
          label: '借贷标记',
          scopedSlots: { customRender: 'credit' },
        },
        {
          prop: 'desc',
          label: '配置描述',
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: '120px',
        },
        {
          prop: 'oldFlag',
          label: 'oldFlag',
          scopedSlots: { customRender: 'oldFlag' },
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '100',
          scopedSlots: { customRender: 'operation' },
        },
      ];
      return {
        feeLoading: false,
        feeOptions: [],
        searchForm: {
          accountNumber: null,
          businessLine: null,
          subjectName: null,
          oaFeeId: null,
          oldFlag: null,
        },
        subsidiaryList: [],
        dataList: [
          {
            decs: '',
            statusDesc: '',
            successTime: '',
          },
        ],
        paymentStatusList: [],
        businessLineList: [],
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
        columns,
        options1: false,
        showDialog: false,
        editDialog: false,
        editForm: {
          businessLine: '',
          subjectName: '',
          subjectNumber: '',
          expression: '',
          amountField: '',
          credit: true,
          desc: '新增配置',
          oldFlag: null,
          oaFeeId: null,
        },
        rules: {
          businessLine: [
            { required: true, message: '请选择业务域', trigger: 'change' },
          ],
          subjectName: [
            { required: true, message: '请输入科目', trigger: 'blur' },
          ],
          subjectNumber: [
            { required: true, message: '请输入科目编码', trigger: 'blur' },
          ],
          expression: [
            { required: true, message: '请输入条件表达式', trigger: 'blur' },
          ],
          amountField: [
            { required: true, message: '请输入条件字段', trigger: 'blur' },
          ],
          credit: [
            { required: true, message: '请选择借贷标记', trigger: 'change' },
          ],
          desc: [
            { required: true, message: '请输入配置描述', trigger: 'blur' },
          ],
          oldFlag: [
            { required: true, message: '请选择是否存量', trigger: 'change' },
          ],
        },
        passwordDialog: false,
        passwordForm: {
          password: '',
        },
        passwordRules: {
          password: [
            { required: true, message: '请输入密码', trigger: 'blur' },
            { min: 6, message: '密码长度不能小于6位', trigger: 'blur' },
          ],
        },
        currentRow: null,
        actionType: '',
        deleteId: null,
      };
    },

    created() {
      this.getList(true);
      this.screen();
    },
    methods: {
      // 获取公司主体名称
      async screen() {
        getNpPaymentRequisitionDic().then(res => {
          if (res) {
            this.paymentStatusList = res.paymentStatus;
            this.businessLineList = res.businessLine;
          }
        });
      },
      getParams() {
        const params = {
          ...this.searchForm,
          size: this.pagination.pageLimit,
          current: this.pagination.pageSize,
        };
        if (this.searchForm.billDate && this.searchForm.billDate.length > 0) {
          params.billDateStart = this.searchForm.billDate[0];
          params.billDateEnd = this.searchForm.billDate[1];
        }
        delete params.billDate;
        return params;
      },
      async searchOaFeeList(feeName) {
        this.feeLoading = true;
        const res = await listFeeByName(feeName);
        this.feeOptions = res;
        this.feeLoading = false;
      },
      computeOaFeeId(row) {
        if (this.editForm.oaFeeId) {
          this.editForm.expression = `expenses.M${this.editForm.oaFeeId} > 0`;
          this.editForm.amountField = `expenses.M${this.editForm.oaFeeId}`;
        }
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const res = await getListData(params);
        this.options.loading = false;
        this.list = res ? res.records : [];
        this.pagination.total = res ? res.total : 0;
      },
      editorAction(row) {
        this.actionType = 'edit';
        this.currentRow = row;
        this.passwordDialog = true;
      },
      newAccountAction() {
        this.editorAction({});
      },
      closePasswordDialog() {
        this.passwordDialog = false;
        this.passwordForm.password = '';
        this.$refs.passwordForm?.resetFields();
        if (this.actionType === 'delete') {
          this.deleteId = null;
        }
        this.actionType = '';
      },
      confirmPassword() {
        let actionType = this.actionType;
        this.$refs.passwordForm.validate(valid => {
          if (valid) {
            const correctPassword = '123456';
            if (this.passwordForm.password === correctPassword) {
              console.log('actionType', actionType);
              if (actionType === 'edit') {
                this.editDialog = true;
                this.editForm = {
                  ...this.currentRow,
                };
              } else if (actionType === 'delete') {
                this.executeDelete();
              }
              this.closePasswordDialog();
            } else {
              this.$message.error('密码错误，请重新输入');
              this.passwordForm.password = '';
            }
          }
        });
      },
      closeEditDialog() {
        this.editDialog = false;
        this.passwordDialog = false;
        this.currentRow = null;
        this.$refs.editForm?.resetFields();
      },
      submitEdit() {
        this.$refs.editForm.validate(async valid => {
          if (valid) {
            try {
              await saveAccountNumber(this.editForm);
              this.$message.success('操作成功');
              this.closeEditDialog();
              this.getList(true);
            } catch (error) {
              this.$message.error('更新失败');
            }
          }
        });
      },
      removeAction({ id }) {
        this.$confirm('确定删除该条数据吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.actionType = 'delete';
            this.deleteId = id;
            this.passwordDialog = true;
          })
          .catch(() => {
            this.$message.info('已取消删除');
          });
      },
      async executeDelete() {
        try {
          await removeId(this.deleteId);
          this.$message.success('删除成功');
          this.getList(true);
        } catch (error) {
          this.$message.error('删除失败');
        } finally {
          this.deleteId = null;
          this.actionType = '';
        }
      },
      // 重置
      resetForm() {
        Object.assign(this.$data.searchForm, this.$options.data().searchForm);
        this.getList(true);
      },
      onClose() {
        this.showDialog = false;
      },
    },
  };
</script>
<style lang="scss" scoped>
  .search-form {
    padding: 16px 16px 0;

    .search-row {
      flex-wrap: wrap;
      margin: 0 -5px;

      .el-col {
        margin-bottom: 16px;
        padding: 0 5px;
      }
    }

    :deep(.el-form-item) {
      margin-bottom: 0;
      width: 100%;

      .el-form-item__label {
        padding-right: 8px;
        line-height: 32px;
      }

      .el-form-item__content {
        width: calc(100% - 100px);
        line-height: 32px;

        .el-select {
          width: 200px;

          .el-input {
            width: 100%;
          }
        }

        .el-input {
          width: 200px;
        }
      }
    }

    .search-buttons {
      .el-form-item__content {
        margin-left: 0 !important;
        width: 100%;
      }

      .el-button {
        margin-right: 8px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .edit-form {
    padding: 20px;

    :deep(.el-form-item) {
      margin-bottom: 20px;

      .el-select,
      .el-input {
        width: 300px;
      }

      .el-input.el-input--textarea {
        width: 500px;
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }

  :deep(.el-form-item) {
    .el-input {
      width: 220px;
    }
  }
</style>
