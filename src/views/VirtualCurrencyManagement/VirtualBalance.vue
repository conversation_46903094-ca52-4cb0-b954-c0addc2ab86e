<!--
 * @Description: 虚拟币管理-账户余额管理
 * @Author: xuxiang
 * @Date: 2022-05-27 16:30:38
 * @LastEditTime: 2024-06-12 13:58:48
 * @LastEditors: Please set LastEditors
 * @Reference: 
-->
<template>
  <div>
    <el-form :inline="true" :model="params">
      <el-form-item label="用户ID:">
        <el-input
          v-model="params.idCode"
          placeholder="请输入代理ID"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <ac-permission-button
          icon="el-icon-search"
          btn-text="查询"
          permission-key=""
          @click="search(true)"
        ></ac-permission-button>
        <el-button icon="el-icon-refresh" @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column label="序号" type="index" width="50"></el-table-column>
      <el-table-column
        align="center"
        prop="accountCode"
        label="账户ID"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="idCode"
        label="用户ID"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="subjectName"
        label="账户主体"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="currency"
        label="本位币币种"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="balance"
        label="账户余额"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="statusDesc"
        label="状态"
        min-width="100"
      ></el-table-column>

      <el-table-column
        min-width="100"
        max-width="300"
        fixed="right"
        label="操作"
      >
        <template slot-scope="{ row }">
          <el-button type="text" @click="gotoPage(row)">账户流水</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="params.current"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="params.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>
<script>
  import { getVirtualCoinList } from '@/api/southEast/index.js';

  export default {
    data() {
      return {
        params: {
          idCode: '',
          current: 1,
          size: 10,
          platformCode: 'WLZ',
        },
        tableData: [],
        loading: false, // loading表格加载层
        total: 0,
      };
    },
    methods: {
      async fetch() {
        let params = { ...this.params };
        if (!params.idCode) {
          this.$message.error('用户ID不能为空');
          return;
        }
        this.loading = true;
        const { res, err } = await getVirtualCoinList(params);

        if (!err) {
          this.tableData = res.records;
          this.total = res.total;
        }
        this.loading = false;
      },
      search() {
        this.fetch();
      },
      reset() {
        Object.assign(this.params, this.$options.data.call(this).params);
      },
      gotoPage(row) {
        this.$router.push({
          path:
            '/userFinancialManagementWLZ/VirtualCurrencyManagement/VirtualAccountFlow',
          query: {
            idCode: row.idCode,
            accountCode: row.accountCode,
          },
        });
      },
      handleSizeChange(val) {
        this.$set(this.params, 'size', val);
      },
      handleCurrentChange(val) {
        this.$set(this.params, 'current', val);
      },
    },
  };
</script>
<style lang="scss"></style>
