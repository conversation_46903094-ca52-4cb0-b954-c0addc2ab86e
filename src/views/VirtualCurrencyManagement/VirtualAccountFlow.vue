<!--
 * @Description: 虚拟币管理-账户流水管理
 * @Author: xuxiang
 * @Date: 2022-05-27 16:30:38
 * @LastEditTime: 2024-06-18 10:02:18
 * @LastEditors: Please set LastEditors
 * @Reference: 
-->
<template>
  <div>
    <el-form :inline="true" :model="params" class="acg-filter-form">
      <el-form-item label="账户ID:">
        <el-input
          v-model="params.accountCode"
          placeholder="请输入账户ID"
          clearable
        />
      </el-form-item>
      <el-form-item label="用户ID:">
        <el-input
          v-model="params.idCode"
          placeholder="请输入用户ID"
          clearable
        />
      </el-form-item>
      <el-form-item label="交易业务单号:">
        <el-input
          v-model="params.orderNo"
          placeholder="请输入交易业务单号"
          clearable
        />
      </el-form-item>
      <el-form-item label="销售主体:">
        <el-select
          v-model="params.subjectCode"
          filterable
          clearable
          placeholder="请输入销售主体"
        >
          <el-option
            v-for="(item, index) in subjectList"
            :key="index"
            :value="item.code"
            :label="item.desc"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="记账时间:">
        <el-date-picker
          v-model="params.detailTime"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"
          clearable
          filterable
        ></el-date-picker>
      </el-form-item>
      <el-form-item label=" ">
        <ac-permission-button
          icon="el-icon-search"
          btn-text="查询"
          permission-key=""
          @click="search(true)"
        ></ac-permission-button>
        <el-button icon="el-icon-refresh" @click="reset">重置</el-button>
        <el-button type="warning" icon="el-icon-download" @click="onExport">
          导出
        </el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column label="序号" type="index" width="50"></el-table-column>
      <el-table-column
        align="center"
        prop="accountCode"
        label="账户ID"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="idCode"
        label="用户ID"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="orderNo"
        label="交易业务单号"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="saleSubjectName"
        label="销售主体"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="bizTypeDesc"
        label="交易类型"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="currency"
        label="币种"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="balanceBefore"
        label="期初余额"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="amount"
        label="变动金额"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="balanceAfter"
        label="变动后余额"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="businessTime"
        label="交易发生时间"
        min-width="120"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="createTime"
        label="记账时间"
        min-width="100"
      ></el-table-column>
    </el-table>
    <el-pagination
      :current-page="params.current"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="params.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>
<script>
  import {
    getVirtualCoinFlows,
    exportFlows,
    getComboBoxData,
  } from '@/api/southEast/index.js';

  export default {
    data() {
      return {
        params: {
          platformCode: 'WLZ',
          detailTime: [],
          orderNo: '',
          subjectCode: '',
          size: 10,
          current: 1,
        },
        platformList: [], // 发放状态下拉框
        tableData: [],
        loading: false, // loading表格加载层
        subjectList: [],
        total: 0,
      };
    },
    mounted() {
      this.getEnums();
      if (this.$route.query.idCode) {
        this.$set(this.params, 'idCode', this.$route.query.idCode);
        this.$set(this.params, 'accountCode', this.$route.query.accountCode);
        this.fetch();
      }
    },
    methods: {
      async getEnums() {
        const { res, err } = await getComboBoxData();
        if (!err) {
          this.subjectList = res.subjectList || [];
        }
      },
      async fetch() {
        let params = { ...this.params };
        if (!params.accountCode && !params.idCode) {
          this.$message.error('用户ID和账户ID不能同时为空');
          return;
        }
        if (params.detailTime?.length) {
          params.startTime = params.detailTime[0];
          params.endTime = params.detailTime[1];
        }
        delete params.detailTime;
        this.loading = true;
        const { res, err } = await getVirtualCoinFlows(params);
        if (!err) {
          this.tableData = res.records;
          this.total = res.total;
        }
        this.loading = false;
      },
      search() {
        this.fetch();
      },
      reset() {
        Object.assign(this.params, this.$options.data.call(this).params);
      },
      async onExport() {
        let params = { ...this.params };
        if (params.detailTime.length) {
          params.startTime = params.detailTime[0];
          params.endTime = params.detailTime[1];
        }
        delete params.detailTime;
        const { res, err } = await exportFlows(params);
        this.$message.success('数据导入成功，请在任务中心查看!');
      },
      handleSizeChange(val) {
        this.$set(this.params, 'size', val);
      },
      handleCurrentChange(val) {
        this.$set(this.params, 'current', val);
      },
    },
  };
</script>
<style lang="scss"></style>
