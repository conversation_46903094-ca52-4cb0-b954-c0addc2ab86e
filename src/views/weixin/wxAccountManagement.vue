<template>
  <div>
    <el-form ref="formSearch" inline :model="searchParams">
      <el-form-item label="账户主体名称:">
        <el-input
          v-model="searchParams.subjectName"
          placeholder="请输入账户主体名称"
        />
      </el-form-item>
      <el-form-item label="渠道类型:">
        <el-select
          v-model="searchParams.channelType"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in channelTypeMap"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="余额"
          permission-key=""
          @click="jump(scope.row, 1)"
        ></ac-permission-button>
        <!-- <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="明细"
          permission-key=""
          @click="jump(scope.row, 2)"
        ></ac-permission-button> -->
      </template>
    </dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';

  import { getListAccountDetail } from '@/api/weixin';

  export default {
    // eslint-disable-next-line vue/component-definition-name-casing
    name: 'wxAccountManagement',
    components: {
      dynamictable,
    },

    data() {
      let columns = [
        {
          prop: 'channelName',
          label: '渠道名称',
        },
        {
          prop: 'subjectName',
          label: '账户主体',
        },
        {
          prop: 'accountNo',
          label: '账户ID',
        },
        {
          prop: 'applymentCode',
          label: '申请编码',
        },
        {
          prop: 'tradeSubjectName',
          label: '销售主体',
        },
        {
          prop: 'accountLevelDesc',
          label: '二级账户类型',
        },
        {
          prop: 'accountTypeDesc',
          label: '账户类型',
        },
        {
          prop: 'realTimeAvailableAmount',
          label: '实时可用余额',
        },
        {
          prop: 'realTimePendingAmount',
          label: '实时不可用余额',
        },
        {
          prop: 'realTimeBalanceTime',
          label: '实时余额时间',
        },
        {
          prop: 'dayClosingAvailableAmount',
          label: '最新日终可用余额',
        },
        {
          prop: 'dayClosingPendingAmount',
          label: '最新日终不可用余额',
        },
        {
          prop: 'dayClosingBalanceTime',
          label: '日终余额时间',
        },
        {
          prop: 'status',
          label: '状态',
        },

        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          minWidth: '100',
          maxWidth: '300',
          scopedSlots: { customRender: 'operation' },
        },
      ];

      return {
        searchParams: {
          subjectName: '',
          channelType: '',
        },
        list: [],
        options: {
          loading: false,
          border: true,
          index: true,
          indexName: '编号',
          mutiSelect: false,
        },
        columns,
        channelTypeMap: [
          {
            key: 'wxpayServer',
            value: '微信服务商',
          },
          {
            key: 'alipayZft',
            value: '支付宝直付通',
          },
        ],
      };
    },

    created() {
      this.getList(true);
    },

    methods: {
      handleSearch() {
        this.getList(true);
      },

      jump(row, type) {
        if (type === 1) {
          this.$router.push({
            path: 'wxBalanceManagement',
            query: { applymentCode: row.applymentCode },
          });
          return;
        }
        // this.$router.push({
        //   path: 'wxFundDetail',
        //   query: {
        //     thirdAccountId: row.supplierPlatCustNo,
        //   },
        // });
      },
      getParams() {
        const params = {
          ...this.searchParams,
        };
        return params;
      },
      async getList(isSearch) {
        const params = this.getParams();
        this.options.loading = true;
        const { res, err } = await getListAccountDetail(params);
        this.options.loading = false;
        if (res && !err) {
          this.list = res;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.getList(true);
      },
    },
  };
</script>
<style lang="scss"></style>
