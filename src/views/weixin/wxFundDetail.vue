<template>
  <div>
    <el-form inline>
      <el-form-item label="对方账户名称:">
        <el-input
          v-model="searchParams.supplierName"
          placeholder="请输入对方账户名称"
        />
      </el-form-item>
      <el-form-item label="操作日期:">
        <el-date-picker
          v-model="searchParams.signingDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="状态:">
        <el-select
          v-model="searchParams.acctTypes"
          clearable
          multiple
          placeholder="请选择(支持多选)"
        >
          <el-option
            v-for="item in typeList"
            :key="item.code"
            :label="item.label"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="loadData()">查询</el-button>
        <el-button @click="resetData">重置</el-button>
      </el-form-item>
    </el-form>

    <dynamictable
      :data-source="items"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="loadData"
    ></dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import { queryXibAccountInfo } from '@/api/xiamen';

  export default {
    name: 'BalanceManager',
    components: {
      dynamictable,
    },
    data() {
      const columns = [
        {
          prop: 'acctTypeDesc',
          label: '交易流水',
        },
        {
          prop: 'acctAmount',
          label: '账户名称',
        },
        {
          prop: 'cashBal',
          label: '账户号',
        },
        {
          prop: 'frzCash',
          label: '账户类型',
        },
        {
          prop: 'floatBal',
          label: '业务类型',
        },
        {
          prop: 'floatBal',
          label: '交易类型',
        },
        {
          prop: 'floatBal',
          label: '对方账户名称',
        },
        {
          prop: 'floatBal',
          label: '对方账户类型',
        },
        {
          prop: 'floatBal',
          label: '状态',
        },
        {
          prop: 'floatBal',
          label: '操作日期',
        },
      ];
      return {
        typeList: [
          { code: '01', label: '01-普通资金分户' },
          { code: '02', label: '02-交易手续费分户' },
          { code: '03', label: '03-提现手续费分户' },
          { code: '04', label: '04-营销费用分户' },
          { code: '05', label: '05-鉴权分户' },
          { code: '11', label: '11-待清算资金分户' },
          { code: '88', label: '88-经销商账户' },
          { code: '89', label: '89-供应商账户' },
        ],
        searchParams: {
          supplierName: null,
          signingDate: [],
          acctTypes: [],
        },
        items: [],

        columns,
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          index: true,
          indexName: '编号',
        },
      };
    },
    methods: {
      async loadData() {
        // 加载数据
        if (this.options.loading) {
          return;
        }
        this.items = [];

        if (this.acctTypes.length === 0) {
          this.$message.error('请至少选择一个子账号');
          return false;
        }
        this.options.loading = true;
        const acctTypeStr = this.acctTypes ? this.acctTypes.join(',') : '';
        const { res, err } = await queryXibAccountInfo({
          acctTypes: acctTypeStr,
        });

        this.options.loading = false;
        if (res && !err) {
          this.items = res.accountInfoList;

          this.pagination.total = res.total;
        }
      },
      resetData() {
        // 重置查询条件
        this.acctTypes = [];
        this.items = [];
      },
    },
  };
</script>
<style lang="scss" scoped></style>
