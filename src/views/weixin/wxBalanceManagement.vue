<template>
  <div>
    <el-form inline>
      <el-form-item label="日终日期:">
        <el-date-picker
          v-model="signingDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="渠道类型:">
        <el-select v-model="channelType" clearable placeholder="请选择">
          <el-option
            v-for="item in channelTypeMap"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="loadData()">查询</el-button>
        <el-button @click="resetData">重置</el-button>
        <!-- <el-button type="primary" @click="resetData">导出</el-button> -->
        <el-button type="warning" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>

    <dynamictable
      :data-source="items"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="loadData"
    ></dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import { queryPageAccountBalance } from '@/api/weixin';
  import { parseTime } from '@/utils';

  export default {
    // eslint-disable-next-line vue/component-definition-name-casing
    name: 'wxBalanceManagement',
    components: {
      dynamictable,
    },
    data() {
      const columns = [
        {
          prop: 'channelName',
          label: '渠道名称',
        },
        {
          prop: 'subjectName',
          label: '账户主体',
        },
        {
          prop: 'tradeSubjectName',
          label: '销售主体',
        },
        {
          prop: 'accountNo',
          label: '账户ID',
        },
        {
          prop: 'dayClosingAvailableAmount',
          label: '日终可用余额',
        },
        {
          prop: 'dayClosingPendingAmount',
          label: '日终不可用余额',
        },
        {
          prop: 'dayClosingBalanceTime',
          label: '日终日期',
        },
      ];
      return {
        signingDate: [],
        channelType: '',
        items: [],

        columns,
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          index: true,
          indexName: '编号',
        },
        channelTypeMap: [
          {
            key: 'wxpayServer',
            value: '微信服务商',
          },
          {
            key: 'alipayZft',
            value: '支付宝直付通',
          },
        ],
      };
    },
    created() {
      this.loadData();
    },
    methods: {
      async loadData() {
        console.log(this.$route.query);
        let applymentCode = '';
        if (this.$route.query.applymentCode) {
          applymentCode = this.$route.query.applymentCode;
        }
        // 加载数据
        if (this.options.loading) {
          return;
        }
        let params = {
          applymentCode: applymentCode,
          channelType: this.channelType,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        const signingDate = this.signingDate;
        params.dayClosingBalanceTimeStart = signingDate
          ? parseTime(signingDate[0], '{y}-{m}-{d}')
          : '';
        params.dayClosingBalanceTimeEnd = signingDate
          ? parseTime(signingDate[1], '{y}-{m}-{d}')
          : '';

        this.items = [];
        this.options.loading = true;
        const { res, err } = await queryPageAccountBalance(params);

        this.options.loading = false;
        if (res && !err) {
          this.items = res.records;

          this.pagination.total = res.total;
        }
      },
      resetData() {
        // 重置查询条件
        this.signingDate = [];
        this.channelType = '';
        this.loadData();
      },
    },
  };
</script>
<style lang="scss" scoped></style>
