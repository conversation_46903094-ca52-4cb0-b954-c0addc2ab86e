<!--
 * @Description: 
 * @Author: dyc
 * @Date: 2023-05-16 10:05:27
 * @LastEditTime: 2023-05-24 14:06:24
 * @LastEditors: dddd
 * @Reference: 
-->
<template>
  <div class="journal">
    <el-button type="primary" style="margin: 20px 0" @click="showDialog = true">
      新增
    </el-button>
    <el-tabs v-model="activeName" type="card" @tab-click="tabClick">
      <el-tab-pane label="BU" name="BU"></el-tab-pane>
      <el-tab-pane label="专项" name="专项"></el-tab-pane>
    </el-tabs>

    <div id="container"></div>
    <el-empty
      v-if="!treeData || !treeData.pcName"
      description="正在加载中..."
    ></el-empty>
    <el-dialog
      width="40%"
      :title="'新增'"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
      @closed="onClose"
    >
      <el-form
        ref="formData"
        :model="formData"
        label-width="120px"
        :rules="rulesFormData"
      >
        <el-form-item label="类型" prop="pcType">
          <el-select
            v-model="formData.pcType"
            placeholder="请选择类型"
            clearable
            @change="changePcType"
          >
            <el-option
              v-for="(item, index) in pcTypeList"
              :key="index"
              :label="item.desc"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="名称" prop="pcName">
          <el-input
            v-model="formData.pcName"
            placeholder="请输入名称"
            style="width: 195px"
          ></el-input>
        </el-form-item>
        <el-form-item label="负责人">
          <el-input
            v-model="formData.pcLeader"
            placeholder="请输入负责人"
            style="width: 195px"
          ></el-input>
        </el-form-item>
        <el-form-item label="上级利润中心" prop="pcParentCodes">
          <el-cascader
            v-model="formData.pcParentCodes"
            :options="treeDataList"
            :props="{
              checkStrictly: true,
              value: 'pcCode',
              label: 'pcName',
              children: 'childrenList',
            }"
            clearable
          ></el-cascader>
        </el-form-item>
        <el-form-item>
          <el-button @click="showDialog = false">取消</el-button>
          <el-button type="primary" @click="saveForm()">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
  // import { getCostDocumentData } from '@/api/costManagement';
  import {
    defineTreeQuery,
    defineAdd,
    commonContentQuery,
  } from '@/api/profitCenter';
  import G6 from '@antv/g6';
  export default {
    components: {},

    data() {
      return {
        activeName: 'BU',
        pcType: 1, //1BU 2专项
        showDialog: false,

        formData: {
          pcType: null,
          pcName: null,
          pcParentCodes: [],
          pcLeader: null,
        },
        pcTypeList: [], //类型
        rulesFormData: {
          pcType: [
            { required: true, message: '请选择类型', trigger: 'change' },
          ],
          pcName: [
            { required: true, message: '请输入名称', trigger: 'change' },
          ],
          pcParentCodes: [
            {
              required: true,
              message: '请选择上级利润中心',
              trigger: 'change',
            },
          ],
        },
        treeData: {},
        treeDataList: [],
      };
    },
    mounted() {
      // this.initG6();
    },
    created() {
      this.fetch();
      this.screen();
    },
    methods: {
      async screen() {
        const res = await commonContentQuery();

        this.pcTypeList = res['pcType'] || [];
      },
      async changePcType(val) {
        const res = await defineTreeQuery({ pcType: val });

        this.treeDataList = [res] || [];
      },
      tabClick(tab) {
        if (this.activeName == 'BU') {
          this.pcType = 1;
        } else if (this.activeName == '专项') {
          this.pcType = 2;
        }
        this.destroyG6(); // 销毁之前的 G6 图片
        this.fetch();
      },
      // 销毁G6
      destroyG6() {
        const container = document.getElementById('container');
        if (container) {
          this.treeData = {};
          container.innerHTML = ''; // 清空容器
        }
      },
      async fetch() {
        const res = await defineTreeQuery({ pcType: this.pcType });
        if (res) {
          this.treeData = res || [];
          this.initG6();
        }
      },
      onClose() {
        Object.assign(this.$data.formData, this.$options.data().formData);
        this.showDialog = false;
      },
      async saveForm() {
        console.log(this.formData);
        this.$refs.formData.validate(async valid => {
          if (valid) {
            const res = await defineAdd({ ...this.formData });
            this.$message.success('新增成功');
            this.showDialog = false;
            this.destroyG6();
            this.fetch();
          }
        });
      },
      initG6() {
        G6.registerNode(
          'tree-node',
          {
            drawShape: function drawShape(cfg, group) {
              const rect = group.addShape('rect', {
                attrs: {
                  fill: '#fff',
                  stroke: '#666',
                  x: 100,
                  y: 100,
                  width: 100,
                  height: 100,
                },
                // must be assigned in G6 3.3 and later versions. it can be any string you want, but should be unique in a custom item type
                name: 'rect-shape',
              });
              const content = cfg.description.replace(/(.{19})/g, '$1\n');
              const text = group.addShape('text', {
                attrs: {
                  text: content,
                  x: 0,
                  y: 0,
                  textAlign: 'left',
                  textBaseline: 'middle',
                  fill: '#666',
                },
                // must be assigned in G6 3.3 and later versions. it can be any string you want, but should be unique in a custom item type
                name: 'text-shape',
              });
              const bbox = text.getBBox();
              const hasChildren = cfg.children && cfg.children.length > 0;
              rect.attr({
                x: -bbox.width / 2 - 4,
                y: -bbox.height / 2 - 6,
                // 框框的宽高
                width: bbox.width + (hasChildren ? 26 : 12),
                height: bbox.height + 12,
              });
              text.attr({
                x: -bbox.width / 2,
                y: 0,
              });

              if (hasChildren) {
                // 收缩按钮的宽高
                group.addShape('marker', {
                  attrs: {
                    x: bbox.width / 2 + 12,
                    y: 0,
                    r: 6,
                    symbol: cfg.collapsed
                      ? G6.Marker.expand
                      : G6.Marker.collapse,
                    stroke: '#666',
                    lineWidth: 1,
                  },
                  // must be assigned in G6 3.3 and later versions. it can be any string you want, but should be unique in a custom item type
                  name: 'collapse-icon',
                });
              }
              return rect;
            },
            update: (cfg, item) => {
              const group = item.getContainer();
              const icon = group.find(e => e.get('name') === 'collapse-icon');
              icon.attr(
                'symbol',
                cfg.collapsed ? G6.Marker.expand : G6.Marker.collapse,
              );
            },
          },
          'single-node',
        );

        const container = document.getElementById('container');
        const width = container.scrollWidth;
        const height = container.scrollHeight || 500;
        const graph = new G6.TreeGraph({
          container: 'container',
          width,
          height,
          modes: {
            default: [
              {
                type: 'collapse-expand',
                onChange: function onChange(item, collapsed) {
                  const data = item.get('model');
                  graph.updateItem(item, {
                    collapsed,
                  });
                  data.collapsed = collapsed;
                  return true;
                },
              },
              'drag-canvas',
              'zoom-canvas',
            ],
          },
          defaultNode: {
            type: 'tree-node',
            anchorPoints: [
              [0, 0.5],
              [1, 0.5],
            ],
          },
          defaultEdge: {
            type: 'cubic-horizontal',
            style: {
              stroke: '#A3B1BF',
            },
          },
          layout: {
            type: 'compactBox',
            direction: 'LR',
            getId: function getId(d) {
              return d.id;
            },
            getHeight: function getHeight() {
              return 100;
            },
            // 线的长度
            getWidth: function getWidth() {
              return 94;
            },
            // 表的缩放比例
            getVGap: function getVGap() {
              return 20;
            },
            // 表的高度，越大越长
            getHGap: function getHGap() {
              return 80;
            },
          },
        });

        G6.Util.traverseTree(this.treeData, function (item) {
          item.id = item.pcCode;
          item.children = item.childrenList;
        });
        graph.data(this.treeData);
        graph.render();
        graph.fitView();

        if (typeof window !== 'undefined')
          window.onresize = () => {
            if (!graph || graph.get('destroyed')) return;
            if (!container || !container.scrollWidth || !container.scrollHeight)
              return;
            graph.changeSize(container.scrollWidth, container.scrollHeight);
          };
      },
    },
  };
</script>
<style></style>
