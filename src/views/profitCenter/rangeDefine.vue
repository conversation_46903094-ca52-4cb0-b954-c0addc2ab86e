<!--
 * @Description: 
 * @Author: 丁永冲
 * @Date: 2021-07-06 09:56:30
 * @LastEditTime: 2023-05-29 13:53:04
 * @LastEditors: dddd
 * @Reference: 
-->
<template>
  <div>
    <el-form ref="searchForm" :model="searchForm" label-width="120px" inline>
      <el-form-item label="规则ID：">
        <el-input v-model="searchForm.ruleCode" placeholder="请输入"></el-input>
      </el-form-item>

      <el-form-item label="规则名称：">
        <el-input v-model="searchForm.ruleName" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="利润中心ID：">
        <el-input v-model="searchForm.pcCode" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="利润中心：">
        <el-input v-model="searchForm.pcName" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="利润中心类型：">
        <el-select
          v-model="searchForm.pcType"
          placeholder="请选择类型"
          clearable
        >
          <el-option
            v-for="(item, index) in pcTypeList"
            :key="index"
            :label="item.desc"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item style="margin-left: 20px">
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button @click="resetForm()">重置</el-button>
        <ac-permission-button
          type="primary"
          btn-text="新增"
          permission-key="message_add"
          plain
          @click="addHandle()"
        ></ac-permission-button>
      </el-form-item>
    </el-form>

    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <!-- v-if=" (scope.row.status == 0 && scope.row.approvalStatus != 1 &&
        scope.row.approvalStatus != 3) || (scope.row.status != 0 &&
        scope.row.approvalStatus == 1) " -->
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="编辑"
          permission-key="message_edit"
          @click="addHandle(scope.row)"
        ></ac-permission-button>
        <!-- v-if="scope.row.approvalStatus == 1 && scope.row.status != 0" -->
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="版本"
          @click="handelOperate(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <!-- 新增销售主体/编辑销售主体 -->
    <el-dialog
      width="50%"
      :title="formData.ruleCode ? '编辑' : '新增'"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
      @closed="onClose"
    >
      <el-form
        ref="formData"
        :model="formData"
        :inline="true"
        label-width="120px"
        :rules="rulesFormData"
      >
        <el-row>
          <el-col>
            <el-form-item
              label="利润中心类型："
              label-width="140px"
              prop="pcType"
            >
              <el-select
                v-model="formData.pcType"
                placeholder="请选择类型"
                clearable
                :disabled="!!formData.ruleCode"
                @change="changePcType"
              >
                <el-option
                  v-for="(item, index) in pcTypeList"
                  :key="index"
                  :label="item.desc"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="规则名称" prop="ruleName">
              <el-input
                v-model="formData.ruleName"
                :disabled="!!formData.ruleCode"
                placeholder="请输入规则名称"
                style="width: 195px"
              ></el-input>
            </el-form-item>
            <el-form-item label="利润中心" prop="pcCodes">
              <el-cascader
                ref="pcCodes"
                v-model="formData.pcCodes"
                :disabled="!!formData.ruleCode"
                :options="treeData"
                :props="{
                  checkStrictly: true,
                  value: 'pcCode',
                  label: 'pcName',
                  children: 'childrenList',
                }"
                clearable
                @change="changeRuleCode"
              ></el-cascader>
            </el-form-item>
            <el-form-item
              v-if="isLeafNode"
              label="下一级规则"
              label-width="120px"
            >
              <el-select
                v-model="formData.nextLevelKey"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="(item, index2) in fieldList"
                  :key="index2"
                  :label="item.desc"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col>
            <p>规则设置</p>
            <el-button type="primary" style="margin: 20px 0" @click="addData">
              新增
            </el-button>
          </el-col>

          <el-col v-for="(data, index) in formData.ruleDataList" :key="index">
            <!-- TODO 待确定 -->

            <el-form-item
              label="字段"
              label-width="60px"
              :prop="'ruleDataList.' + index + '.field'"
              :rules="rulesFormData.field"
            >
              <el-select
                v-model="data.field"
                placeholder="请选择字段"
                clearable
                filterable
                @change="changeField($event, index)"
              >
                <el-option
                  v-for="(item, index2) in fieldList"
                  :key="index2"
                  :label="item.desc"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label=""
              :prop="'ruleDataList.' + index + '.conditionType'"
              :rules="rulesFormData.conditionType"
            >
              <el-select
                v-model="data.conditionType"
                placeholder="是否包含"
                clearable
                style="width: 100px"
              >
                <el-option label="包含" :value="1"></el-option>
                <el-option
                  v-if="
                    !['channelType', 'vipFlag', 'kolFlag'].includes(data.field)
                  "
                  label="不包含"
                  :value="2"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="值"
              label-width="40px"
              :prop="'ruleDataList.' + index + '.values'"
              :rules="rulesFormData.values"
            >
              <el-select
                v-model="data.values"
                placeholder="值"
                clearable
                multiple
                filterable
                style="width: 160px"
              >
                <el-option
                  v-for="(item, index4) in data.valueList"
                  :key="index4"
                  :label="item.desc"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-button type="danger" @click="deleteRulesData(index)">
              删除
            </el-button>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer">
        <el-button @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="saveForm">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="历史版本" :visible="dialogVisible" @close="closeDialog">
      <el-table :data="tableData" border>
        <el-table-column prop="ruleVersion" label="版本号"></el-table-column>
        <el-table-column prop="ruleDesc" label="规则定义"></el-table-column>
        <el-table-column
          prop="effectiveTime"
          label="生效时间"
        ></el-table-column>
        <el-table-column prop="modifyName" label="操作人"></el-table-column>
      </el-table>
      <div slot="footer">
        <el-button @click="closeDialog">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  const _ = require('lodash');
  import dynamictable from '@/components/dynamic-table';

  import {
    mappingPageQuery,
    mappingQueryDetail,
    commonContentQuery,
    commonRuleContentQuery,
    defineTreeQuery,
    saveUpdate,
    mappingQueryHistory,
  } from '@/api/profitCenter';
  export default {
    components: {
      dynamictable,
    },

    data() {
      let columns = [
        {
          prop: 'ruleCode',
          label: '规则ID',
        },
        {
          prop: 'ruleName',
          label: '规则名称',
        },
        {
          prop: 'ruleVersion',
          label: '规则版本',
        },
        {
          prop: 'pcCode',
          label: '利润中心ID',
        },
        {
          prop: 'pcName',
          label: '利润中心名称',
        },
        {
          prop: 'pcTypeDesc',
          label: '利润中心类型',
        },
        {
          prop: 'ruleDesc',
          label: '规则定义',
        },

        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '160',
          scopedSlots: { customRender: 'operation' },
        },
      ];
      return {
        isLeafNode: false,
        searchForm: {
          ruleCode: null,
          ruleName: null,
          pcName: null,
          pcType: null,
          pcCode: null,
        },
        fieldList: [], //字段
        vallueList: [], //值
        conditionTypeList: [], //是否包含
        treeData: [], //利润中心
        pcTypeList: [], //利润中心类型

        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },

        options: {
          loading: false,
          border: true,
          mutiSelect: false,
        },
        columns,
        // 弹窗
        showDialog: false,
        formData: {
          ruleName: null,
          pcCodes: [],
          pcType: null,
          nextLevelKey: null,
          ruleDataList: [],
        },
        rulesFormData: {
          pcType: [
            {
              required: true,
              message: '请选择利润中心类型',
              trigger: 'change',
            },
          ],
          ruleName: [
            { required: true, message: '请输入规则名称', trigger: 'change' },
          ],
          pcCodes: [
            { required: true, message: '请选择利润中心', trigger: 'change' },
          ],
          field: [{ required: true, message: '请输入', trigger: 'change' }],
          conditionType: [
            { required: true, message: '请选择', trigger: 'change' },
          ],
          values: [{ required: true, message: '请输入值', trigger: 'change' }],
        },
        dialogVisible: false, // 控制对话框的显示状态
        tableData: [],
      };
    },

    created() {
      this.getList(true);
      this.screen();
    },
    methods: {
      async changePcType(val) {
        const res = await defineTreeQuery({ pcType: val });

        this.treeData = [res] || [];
      },
      async changeField(val, index, type) {
        // 单条修改字段以后，重置value的值，并且渲染最新的valueList
        const res = await commonRuleContentQuery(val);
        if (res) {
          if (type != 'edit') {
            this.formData.ruleDataList[index].values = [];
            this.formData.ruleDataList[index].conditionType = null;
          }

          this.$set(this.formData.ruleDataList[index], 'valueList', res[val]);
        }
      },
      changeRuleCode(val) {
        // 改变利润中心的值
        // 规则。新增的时候，利润中心切换，会导致radio切换，切换到非子节点，隐藏radio，切换另一个子节点，清空radio
        // 编辑的时候，不可修改规则名称，利润中心

        this.formData.nextLevelKey = null;
        if (!this.$refs.pcCodes.getCheckedNodes(false)[0].hasChildren) {
          // 无子节点
          this.isLeafNode = true;
        } else {
          // 有子节点
          this.isLeafNode = false;
        }
      },
      addData() {
        this.formData.ruleDataList.push({
          values: [],
          field: null,
          conditionType: null,
          valueList: [],
        });
      },
      deleteRulesData(index) {
        this.formData.ruleDataList.splice(index, 1);
      },
      async screen() {
        const res = await commonContentQuery();

        this.pcTypeList = res['pcType'] || [];
        this.fieldList = res['ruleFields'] || [];
      },

      onClose() {
        this.formData = Object.assign({}, this.$options.data().formData);
      },
      // 重置
      resetForm() {
        Object.assign(this.$data.searchForm, this.$options.data().searchForm);

        this.getList(true);
      },
      // 搜索
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        this.options.loading = true;
        let params = {
          ...this.searchForm,
          limit: this.pagination.pageLimit,
          pageNo: this.pagination.pageSize,
        };

        try {
          const res = await mappingPageQuery(params);

          this.options.loading = false;
          this.list = res ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        } catch (error) {
          this.options.loading = false;
        }
      },

      // 新增/编辑
      async addHandle(row) {
        this.showDialog = true;

        if (row && row.ruleCode) {
          const res = await mappingQueryDetail({ ruleCode: row.ruleCode });

          if (res) {
            // 获取利润中心类型下的利润中心数据
            this.changePcType(res.pcType);
            this.$nextTick(() => {
              this.formData = {
                ...res,
              };

              // 获取选中字段的值
              res.ruleDataList.forEach((item, index) => {
                this.changeField(item.field, index, 'edit');
              });
              // 获取是否有叶子节点
              if (res.leafNode) {
                // 无叶子节点，展示
                this.isLeafNode = true;
              } else {
                this.isLeafNode = false;
              }
              // // 根据nextLevelKey的key值找到 value的下表绑定
              // let nextLevelKeyIndex = res.ruleDataList.findIndex(item => {
              //   return item.field == res.nextLevelKey;
              // });
              // this.formData.nextLevelKey = nextLevelKeyIndex;
            });
          }
        } else {
          this.formData = Object.assign({}, this.$options.data().formData);
        }
      },

      // 查看历史版本
      async handelOperate(row) {
        let params = {
          ruleCode: row.ruleCode,
        };
        const res = await mappingQueryHistory(params);
        console.log(res);
        this.dialogVisible = true;
        this.tableData = res;
      },
      // 弹窗保存
      async saveForm() {
        this.$refs.formData.validate(async valid => {
          if (valid) {
            let params = _.cloneDeep(this.formData);

            params.ruleDataList.forEach(item => {
              delete item.valueList;
            });

            if (this.formData.ruleCode) {
              const res = await saveUpdate({ ...params, opType: 'update' });
              this.getList();

              this.$message.success('编辑成功');
              this.showDialog = false;
              this.formData = Object.assign({}, this.$options.data().formData);
            } else {
              const res = await saveUpdate({ ...params, opType: 'add' });
              this.getList(true);
              this.$message.success('新增成功');
              this.showDialog = false;
              this.formData = Object.assign({}, this.$options.data().formData);
            }
          }
        });
      },
      closeDialog() {
        this.dialogVisible = false; // 关闭对话框
        this.tableData = [];
      },
    },
  };
</script>
<style>
  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
