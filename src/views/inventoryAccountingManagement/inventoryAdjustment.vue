<!-- 财务-存货核算管理-异常单 -->
<template>
  <div>
    <el-form :inline="true" :model="params">
      <el-form-item label="账期：">
        <el-date-picker
          v-model="searchDate"
          type="monthrange"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          value-format="yyyy-MM"
          format="yyyy-MM"
          :editable="false"
          style="width: 350px"
          :picker-options="pickerOptions"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="货主：">
        <ac-lazy-load
          ref="abc1"
          v-model="params.ownerCode"
          :request-body="requestBody"
          :wait-time="waitTime"
          :search-key="searchKey"
          :self-searh-key="selfSearhKey"
          :options="options"
          :page="page"
          clearable
          width="200px"
        />
      </el-form-item>

      <el-form-item label="仓库：">
        <el-select
          v-model="params.warehouseCodeList"
          clearable
          filterable
          multiple
          style="width: 200px"
          placeholder="请选择"
        >
          <el-option
            v-for="(cw, index) in warehouseList"
            :key="index"
            :label="cw.name"
            :value="cw.code"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="条形码：">
        <el-input
          v-model="params.baseGoodsBarcode"
          placeholder="请输入"
          style="width: 200px"
        ></el-input>
      </el-form-item>
      <el-form-item label="货品名称：">
        <el-input
          v-model.trim="params.goodsName"
          placeholder="请输入"
          style="width: 200px"
        ></el-input>
      </el-form-item>
      <el-form-item class="mr-l-30">
        <el-button type="primary" @click="search">查询</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row style="margin-bottom: 20px">
      <el-button
        type="primary"
        style="margin-left: 20px; height: 32px"
        @click="downloadTemp"
      >
        导入模板下载
      </el-button>
      <uoloadFile
        style="margin-left: 10px; display: initial"
        btn-text="导入"
        accept=".xlsx, .xls"
        @onSuccess="onSuccess"
      ></uoloadFile>
      <el-button
        style="margin-left: 20px; height: 32px"
        type="primary"
        @click="down_excel"
      >
        导出数据
      </el-button>
    </el-row>
    <el-row>
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
      >
        <el-table-column
          align="center"
          prop="costMonth"
          label="账期"
          min-width="150"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="newTypeStr"
          label="调整单类型"
          min-width="150"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="ownerName"
          label="货主"
          min-width="150"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="warehouseName"
          label="仓库"
          min-width="150"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="warehouseCode"
          label="仓库编码"
          min-width="150"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="baseGoodsBarcode"
          label="货品条码"
          min-width="150"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="goodsCode"
          label="货品编码"
          min-width="150"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="goodsName"
          label="货品名称"
          min-width="150"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="quantity"
          label="计费件数"
          min-width="150"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="currencyCode"
          label="原币币种"
          min-width="150"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="amount"
          label="金额"
          min-width="150"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="remark"
          label="备注"
          min-width="150"
        ></el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="params.current"
        :page-size="params.size"
        :page-sizes="[10, 20, 30, 50, 100, 200]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </el-row>
  </div>
</template>

<script>
  import uoloadFile from '@/components/uoloadFile';
  import { exportExcel, newExportExcel } from '@/api/blob';
  import { setInitData, downloadFile } from '@/utils';

  import {
    queryCostListOfPage,
    queryFinanceAdjustEnums,
    importCostData,
  } from '@/api/inventoryAccountingManagement';
  export default {
    name: 'ExceptionList',
    components: { uoloadFile },
    data() {
      return {
        params: {
          costMonthStart: null,
          costMonthEnd: null,
          baseGoodsBarcode: null,
          goodsName: null,
          ownerCode: null,
          warehouseCodeList: [],
          current: 1,
          size: 10,
        },
        searchDate: '', // 页面筛选时间
        brands: [],
        currencyList: [],
        warehouseList: [], //仓库
        loading: false, // loading表格加载层
        total: 0,
        tableData: [],
        dialogEditVisible: false,
        saveEditPriceLoading: false,
        waitTime: 300,
        searchKey: 'name',
        selfSearhKey: 'entityCode',
        options: { label: 'name', value: 'entityCode' },
        requestBody: {
          url: '/api/recon-core/financeAdjust/queryPage',
          method: 'post',
        },
        page: { pageCurrent: 'current', pageSize: 'size' },
        pickerMinDate: null,
        pickerMaxDate: null,
        pickerOptions: {
          //点击时间回调
          onPick: ({ maxDate, minDate }) => {
            if (minDate) {
              let currentYear = minDate.getFullYear();
              this.pickerMinDate = new Date(minDate).setFullYear(
                currentYear - 1,
              );
              this.pickerMaxDate = new Date(minDate).setFullYear(
                currentYear + 1,
              );
            }
          },
          //禁用时间 打开选择器就调用
          disabledDate: time => {
            if (this.pickerMinDate != null) {
              //点击月份后只允许选前后1年的时间
              //return true是禁用
              return (
                time.getTime() < this.pickerMinDate ||
                this.pickerMaxDate < time.getTime()
              );
            }
          },
        },
      };
    },
    watch: {
      searchDate: {
        handler: function (value) {
          if (!value) {
            this.pickerMinDate = null;
            this.pickerMaxDate = null;
          }
        },
        immediate: true,
      },
    },
    created() {
      this.fetch();
      this.queryFinanceAdjustEnums();
    },

    methods: {
      async queryFinanceAdjustEnums() {
        const res = await queryFinanceAdjustEnums({});
        if (res) {
          this.warehouseList = res?.adjustWarehouseList || [];
        }
      },
      async fetch() {
        let params = JSON.parse(JSON.stringify({ ...this.params }));
        params.costMonthStart = this.searchDate ? this.searchDate[0] : '';
        params.costMonthEnd = this.searchDate ? this.searchDate[1] : '';
        this.loading = true;
        const res = await queryCostListOfPage(params);
        if (res) {
          this.tableData = res.records;
          this.total = res.total;
        }
        this.loading = false;
      },

      search() {
        this.params.current = 1;
        this.fetch();
      },
      handleSizeChange(val) {
        this.params.current = 1;
        this.params.size = val;
        this.fetch();
      },
      handleCurrentChange(val) {
        this.params.current = val;
        this.fetch();
      },
      // 获取导入模板
      async downloadTemp() {
        window.location.href =
          'https://zkt-pro-bucket.obs.cn-east-3.myhuaweicloud.com/recon/%E5%AD%98%E8%B4%A7%E8%B0%83%E6%95%B4%E6%A8%A1%E6%9D%BF.xlsx';
      },
      reset() {
        Object.assign(this.params, this.$options.data().params);
        this.$refs.abc1.val = null;
        this.searchDate = '';
        this.fetch();
      },
      async onSuccess(e) {
        if (!e.success) {
          this.$message.error(e.msg || '导入失败');
          return;
        }
        if (e.data) {
          importCostData({
            fileId: e.data.id,
          }).then(res => {
            this.$message.success('导入成功');
            this.params.current = 1;
            this.params.size = 10;
            this.fetch();
          });
        }
      },
      // 导出
      down_excel() {
        let params = JSON.parse(JSON.stringify({ ...this.params }));
        params.costMonthStart = this.searchDate ? this.searchDate[0] : '';
        params.costMonthEnd = this.searchDate ? this.searchDate[1] : '';
        if (!this.searchDate) {
          this.$message.error('请选择账期');
          return;
        }
        newExportExcel(
          params,
          '/api/recon-core/financeAdjust/exportCostData',
          'post',
        ).then(res => {
          downloadFile(res.data, '存货调整导出结果');
        });
      },
    },
  };
</script>
<style scoped>
  .table-empty {
    min-height: 60px;
    text-align: center;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #dcdfe6;
    border-radius: 2px;
    color: rgba(0, 0, 0, 0.45);
  }
</style>
