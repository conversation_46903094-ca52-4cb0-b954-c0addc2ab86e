<!-- 财务-存货核算管理-异常单 -->
<template>
  <div>
    <el-form :inline="true" :model="params">
      <el-form-item label="账期：">
        <el-date-picker
            v-model="searchDate"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            value-format="yyyy-MM"
            format="yyyy-MM"
            style="width: 350px"
            :picker-options="pickerOptions"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="货主： ">
        <ac-lazy-load
            ref="abc1"
            v-model="params.ownerCode"
            :request-body="requestBody"
            :wait-time="waitTime"
            :search-key="searchKey"
            :self-searh-key="selfSearhKey"
            :options="options"
            :page="page"
            clearable
            width="217px"
        />
      </el-form-item>

      <el-form-item label="仓库：">
        <el-select
            v-model="params.warehouseCodeList"
            clearable
            filterable
            multiple
            placeholder="请选择"
        >
          <el-option
              v-for="(item, index) in warehouseList"
              :key="index"
              :label="item.name"
              :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="条形码：">
        <el-input v-model="params.barCode" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="货品名称：">
        <el-input
            v-model.trim="params.goodsName"
            placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item label="原业务单据类型：">
        <el-input
            v-model.trim="params.oldTypeName"
            placeholder="请输入"
        ></el-input>
      </el-form-item>

      <el-form-item label="业务场景：">
        <el-select
            v-model="params.sceneFlag"
            clearable
            filterable
            placeholder="请选择"
        >
          <el-option
              v-for="(item, index) in sceneFlagList"
              :key="index"
              :label="item.name"
              :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="新定义业务单据类型：">
        <el-select
            v-model="params.newTypeList"
            clearable
            filterable
            multiple
            placeholder="请选择"
        >
          <el-option
              v-for="(item, index) in adjustTypeList"
              :key="index"
              :label="item.name"
              :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="业务单据号：">
        <el-input v-model.trim="params.bizCode" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="状态：">
        <el-select
            v-model="params.status"
            clearable
            filterable
            placeholder="请选择"
        >
          <el-option
              v-for="(item, index) in statusList"
              :key="index"
              :label="item.name"
              :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="mr-l-30">
        <el-button type="primary" @click="search">查询</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row style="margin-bottom: 20px">
<!--      <el-button
          type="primary"
          style="margin-left: 20px; height: 32px"
          @click="downloadTemp"
      >
        导入模板下载
      </el-button>-->
<!--      <uoloadFile
          style="margin-left: 10px; display: initial"
          btn-text="导入"
          accept=".xlsx, .xls"
          @onSuccess="onSuccess"
      ></uoloadFile>-->
      <el-button
          style="margin-left: 20px; height: 32px"
          type="primary"
          @click="down_excel"
      >
        导出数据
      </el-button>
    </el-row>
    <el-row>
      <el-table
          v-loading="loading"
          :data="tableData"
          border
          style="width: 100%"
      >
        <el-table-column
            align="center"
            prop="costMonth"
            label="账期"
            min-width="150"
        ></el-table-column>
        <el-table-column
            align="center"
            prop="relationCode"
            label="关联单据"
            min-width="150"
        ></el-table-column>

        <el-table-column
            align="center"
            prop="oldTypeDesc"
            label="原业务单据类型"
            min-width="150"
        ></el-table-column>
        <el-table-column
            align="center"
            prop="newTypeDesc"
            label="新定义业务单据类型"
            min-width="150"
        ></el-table-column>
        <el-table-column
            align="center"
            prop="ownerName"
            label="货主"
            min-width="150"
        ></el-table-column>
        <el-table-column
            align="center"
            prop="warehouseName"
            label="仓库"
            min-width="150"
        ></el-table-column>
        <el-table-column
            align="center"
            prop="warehouseCode"
            label="仓库编码"
            min-width="150"
        ></el-table-column>
        <el-table-column
            align="center"
            prop="baseGoodsBarcode"
            label="条形码"
            min-width="150"
        ></el-table-column>
        <el-table-column
            align="center"
            prop="bizCode"
            label="业务单据号"
            min-width="150"
        ></el-table-column>
        <el-table-column
            align="center"
            prop="goodsName"
            label="货品名称"
            min-width="150"
        ></el-table-column>
        <el-table-column
            align="center"
            prop="quantity"
            label="计费件数"
            min-width="150"
        ></el-table-column>
        <el-table-column
            align="center"
            prop="currencyCode"
            label="原币币种"
            min-width="150"
        ></el-table-column>
        <el-table-column
            align="center"
            prop="unTaxPrice"
            label="未税单价"
            min-width="150"
        ></el-table-column>
        <el-table-column
            align="center"
            prop="amount"
            label="金额"
            min-width="150"
        ></el-table-column>
        <el-table-column
            align="center"
            prop="customName"
            label="客商"
            min-width="150"
        ></el-table-column>
        <el-table-column
            align="center"
            prop="relationWarehouseName"
            label="对方仓库"
            min-width="150"
        ></el-table-column>
        <el-table-column
            align="center"
            prop="remark"
            label="备注"
            min-width="150"
        ></el-table-column>
        <el-table-column
            align="center"
            prop="statusName"
            label="状态"
            min-width="150"
        ></el-table-column>
        <el-table-column
            align="center"
            prop="sceneFlagName"
            label="业务场景"
            min-width="150"
        ></el-table-column>
        <el-table-column min-width="120" align="center" label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="onHandleEdit(scope.row)">
              修改
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          background
          :current-page="params.current"
          :page-size="params.size"
          :page-sizes="[10, 20, 30, 50, 100, 200]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
      ></el-pagination>
    </el-row>

    <el-dialog
        :title="'修改'"
        :visible.sync="dialogEditVisible"
        :close-on-click-modal="false"
        width="80%"
        @close="cancelEditPrice"
    >
      <el-form
          ref="dialogFormRef"
          :model="dialogForm"
          label-width="auto"
          :rules="editRules"
      >
        <el-form-item label="新定义业务单据类型：" prop="newType">
          <el-select
              v-model="dialogForm.newType"
              clearable
              filterable
              placeholder="请选择"
              style="width: 200px"
              @change="newTypeChange"
          >
            <el-option
                v-for="(item, index) in adjustTypeList"
                :key="index"
                :label="item.name"
                :value="item.code"
            ></el-option>
          </el-select>
        </el-form-item>

        <!-- 采购退货 & 账期为2099-01 场景 -->
        <template
            v-if="
            (dialogForm.newType === 42 || dialogForm.newType === 43)
          "
        >
          <el-form-item label="货品条码：">
            <el-input
                v-model="dialogForm.baseGoodsBarcode"
                disabled
                style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item label="货品名称：">
            <el-input
                v-model="dialogForm.goodsName"
                disabled
                style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item label="退货数量：">
            <el-input-number
                v-model="dialogForm.quantity"
                disabled
                style="width: 200px"
            ></el-input-number>
          </el-form-item>

          <el-form-item label="关联PO单：">
            <div
                v-for="(item, index) in dialogForm.relationPoList"
                :key="`bind_relationPoList_` + index"
                style="display: flex; margin-bottom: 10px"
            >
              <el-input
                  v-model="item.poCode"
                  placeholder="输入PO单号"
                  style="max-width: 170px"
              ></el-input>
              <el-input-number
                  v-model="item.quantity"
                  placeholder="数量"
                  style="margin-left: 10px"
              ></el-input-number>
              <el-button
                  icon="el-icon-plus"
                  circle
                  style="margin-left: 10px"
                  @click="addBindPo"
              ></el-button>
              <el-button
                  v-if="dialogForm.relationPoList.length > 1"
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  style="margin-left: 10px"
                  @click="removeBindPo(index)"
              ></el-button>
            </div>
          </el-form-item>

          <el-form-item label="无关联PO：">
            <div
                v-for="(item, index) in dialogForm.noRelationList"
                :key="`bind_noRelationList_` + index"
                style="display: flex; margin-bottom: 10px"
            >
              <el-select
                  v-model="item.customCode"
                  clearable
                  filterable
                  placeholder="供应商"
                  style="width: 120px;"
              >
                <el-option
                    v-for="(eitem, ci) in allEntities"
                    :key="`edit_currencyCode_` + ci"
                    :label="eitem.name"
                    :value="eitem.entityCode"
                ></el-option>
              </el-select>
              <el-select
                  v-model="item.currencyCode"
                  clearable
                  filterable
                  placeholder="币种"
                  style="width: 120px; margin-left: 10px;"
              >
                <el-option
                    v-for="(citem, ci) in currencyList"
                    :key="`edit_currencyCode_` + ci"
                    :label="citem.currencyCode"
                    :value="citem.currencyCode"
                ></el-option>
              </el-select>
              <el-input-number
                  v-model="item.unTaxPrice"
                  :precision="4"
                  :step="0.1"
                  placeholder="未税单价"
                  style="margin-left: 10px;"
              ></el-input-number>
              <el-input-number
                  v-model="item.quantity"
                  placeholder="数量"
                  style="margin-left: 10px"
              ></el-input-number>
              <el-button
                  icon="el-icon-plus"
                  circle
                  style="margin-left: 10px"
                  @click="addNoPo"
              ></el-button>
              <el-button
                  v-if="dialogForm.noRelationList.length > 1"
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  style="margin-left: 10px"
                  @click="removeNoPo(index)"
              ></el-button>
            </div>
          </el-form-item>
        </template>
        <!-- 切货主场景 -->
        <template v-else-if="dialogForm.newType === 19">
          <el-form-item label="原币币种：" prop="currencyCode">
            <el-select
                v-model="dialogForm.currencyCode"
                clearable
                filterable
                placeholder="请选择"
                style="width: 200px"
            >
              <el-option
                  v-for="(item, index) in currencyList"
                  :key="`newType_19_` + index"
                  :label="item.currencyCode"
                  :value="item.currencyCode"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="未税单价：" prop="unTaxPrice">
            <el-input-number
                v-model="dialogForm.unTaxPrice"
                :precision="4"
                :step="0.1"
                label="输入单价"
                style="width: 200px"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="备注：">
            <el-input
                v-model.trim="dialogForm.remark"
                type="textarea"
                placeholder="请输入"
                style="width: 200px"
                maxlength="500"
                :autosize="{ minRows: 2, maxRows: 3 }"
                show-word-limit
            ></el-input>
          </el-form-item>
        </template>
        <template v-else>
          <el-form-item
              label="原币币种："
              :prop="
              [1, 2, 3, 4].includes(dialogForm.newType)
                ? 'currencyCode'
                : 'empty'
            "
          >
            <el-select
                v-model="dialogForm.currencyCode"
                clearable
                filterable
                placeholder="请选择"
                style="width: 200px"
            >
              <el-option
                  v-for="(item, index) in currencyList"
                  :key="index"
                  :label="item.currencyCode"
                  :value="item.currencyCode"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
              label="供应商："
              :prop="[1, 2].includes(dialogForm.newType) ? 'customCode' : 'empty'"
          >
            <ac-lazy-load
                v-if="abc2"
                ref="abc2"
                v-model="dialogForm.customCode"
                :request-body="requestBody"
                :wait-time="waitTime"
                :search-key="searchKey"
                :self-searh-key="selfSearhKey"
                :options="options"
                :page="page"
                clearable
                width="200px"
            />
          </el-form-item>
          <el-form-item
              label="未税单价："
              :prop="[1, 2].includes(dialogForm.newType) ? 'unTaxPrice' : 'empty'"
          >
            <el-input-number
                v-model="dialogForm.unTaxPrice"
                :precision="4"
                :step="0.1"
                label="输入单价"
                style="width: 200px"
            ></el-input-number>
          </el-form-item>
          <el-form-item
              label="客户："
              :prop="[3, 4].includes(dialogForm.newType) ? 'customName' : 'empty'"
          >
            <el-input
                v-model="dialogForm.customName"
                placeholder="请输入"
                style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item label="对方仓库：">
            <el-select
                v-model="dialogForm.relationWarehouseCode"
                clearable
                filterable
                placeholder="请选择"
                style="width: 200px"
            >
              <el-option
                  v-for="(item, index) in warehouseList"
                  :key="index"
                  :label="item.name"
                  :value="item.code"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
              label="关联单据："
              :prop="
              [5, 6, 7, 8].includes(dialogForm.newType)
                ? 'relationCode'
                : 'empty'
            "
          >
            <el-input
                v-model="dialogForm.relationCode"
                type="textarea"
                placeholder="请输入"
                style="width: 200px"
                maxlength="500"
                :autosize="{ minRows: 2, maxRows: 3 }"
                show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item
              label="订单状态："
              :prop="
              [4].includes(dialogForm.newType) ? 'orderStatusName' : 'empty'
            "
          >
            <el-input
                v-model="dialogForm.orderStatusName"
                placeholder="请输入"
                style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item label="备注：">
            <el-input
                v-model.trim="dialogForm.remark"
                type="textarea"
                placeholder="请输入"
                style="width: 200px"
                maxlength="500"
                :autosize="{ minRows: 2, maxRows: 3 }"
                show-word-limit
            ></el-input>
          </el-form-item>
        </template>
      </el-form>
      <div class="btn" style="text-align: center; margin-top: 20px">
        <el-button @click="cancelEditPrice">取消</el-button>
        <el-button
            :loading="saveEditPriceLoading"
            type="primary"
            @click="saveEditPrice"
        >
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  queryOrderListOfPage,
  queryFinanceAdjustEnums,
  modifyOrderByAdjustCostCode,
  importOrderData,
  getCurrencyList, getAllEntities, get_getAllEntities
} from "@/api/inventoryAccountingManagement";
import uoloadFile from '@/components/uoloadFile';
import {exportExcel, newExportExcel} from '@/api/blob';
import {setInitData, downloadFile} from '@/utils';

export default {
  name: 'ExceptionList',
  components: {uoloadFile},
  data() {
    return {
      params: {
        barCode: '',
        costMonthStart: '',
        costMonthEnd: '',
        // goodsCode: '',
        goodsName: '',
        newTypeList: [],
        oldTypeName: '',
        ownerCode: '',
        sceneFlag: '',
        warehouseCodeList: [],
        status: null,
        bizCode: '',
        current: 1,
        size: 10,
        // 少了品牌
      },
      waitTime: 300,
      searchKey: 'name',
      selfSearhKey: 'entityCode',
      options: {label: 'name', value: 'entityCode'},
      requestBody: {
        url: '/api/recon-core/financeAdjust/queryPage',
        method: 'post',
      },
      page: {pageCurrent: 'current', pageSize: 'size'},
      searchDate: '', // 页面筛选时间
      loading: false, // loading表格加载层
      total: 0,
      tableData: [],
      tableDataId: null,
      dialogForm: {
        id: undefined,
        oldType: undefined,
        adjustOrderCode: null,
        newType: null,
        currencyCode: null,
        customCode: null,
        orderStatusName: null,
        unTaxPrice: null,
        customName: null,
        relationCode: null,
        remark: null,
        relationWarehouseCode: null,
        costMonth: '',
        relationPoList: [{poCode: undefined, quantity: undefined}],
        noRelationList: [{
          currencyCode: undefined,
          customCode: undefined,
          customName: undefined,
          unTaxPrice: undefined
        }],
      },
      dialogEditVisible: false,
      saveEditPriceLoading: false,
      editRules: {
        newType: [
          {required: true, trigger: 'change', message: '请选择操作方式'},
        ],
        currencyCode: [
          {required: true, trigger: 'change', message: '请选择原币币种'},
        ],
        customCode: [
          {required: true, trigger: 'change', message: '请选择供应商'},
        ],
        unTaxPrice: [
          {required: true, trigger: 'blur', message: '请输入未税单价'},
        ],
        customName: [
          {required: true, trigger: 'blur', message: '请选择供应商'},
        ],
        relationCode: [
          {required: true, trigger: 'blur', message: '请输入关联单据'},
        ],
        orderStatusName: [
          {required: true, trigger: 'blur', message: '请输入订单状态'},
        ],
        // relationWarehouseCode: [
        //   { required: true, trigger: 'change', message: '请选择对方仓库' },
        // ],
      },
      adjustTypeList: [],
      statusList: [],
      sceneFlagList: [],
      currencyList: [],
      allEntities: [],
      warehouseList: [], //仓库
      abc2: true,
      pickerMinDate: null,
      pickerMaxDate: null,
      pickerOptions: {
        //点击时间回调
        onPick: ({maxDate, minDate}) => {
          if (minDate) {
            let currentYear = minDate.getFullYear();
            this.pickerMinDate = new Date(minDate).setFullYear(
                currentYear - 1,
            );
            this.pickerMaxDate = new Date(minDate).setFullYear(
                currentYear + 1,
            );
          }
        },
        //禁用时间 打开选择器就调用
        disabledDate: time => {
          if (this.pickerMinDate != null) {
            //点击月份后只允许选前后1年的时间
            //return true是禁用
            return (
                time.getTime() < this.pickerMinDate ||
                this.pickerMaxDate < time.getTime()
            );
          }
        },
      },
    };
  },
  watch: {
    searchDate: {
      handler: function (value) {
        if (!value) {
          this.pickerMinDate = null;
          this.pickerMaxDate = null;
        }
      },
      immediate: true,
    },
  },
  created() {
    this.queryFinanceAdjustEnums();
    this.getCurrency();
    this.getAllEntities();
    this.fetch();
  },
  methods: {
    newTypeChange() {
      this.$nextTick(function () {
        this.$refs.dialogFormRef.clearValidate();
      });
    },
    // 获取品牌下拉框
    async queryFinanceAdjustEnums() {
      const res = await queryFinanceAdjustEnums({});
      if (res) {
        this.adjustTypeList = res?.adjustTypeList || [];
        this.warehouseList = res?.adjustWarehouseList || [];
        this.statusList = res?.adjustUnusualStatusList || [];
        this.sceneFlagList = res?.sceneFlagList || [];
      }
    },
    async getCurrency() {
      const res = await getCurrencyList();
      if (res) {
        this.currencyList = res;
      }
    },
    async getAllEntities() {
      const res = await get_getAllEntities();
      if (res) {
        this.allEntities = res;
      }
    },

    async fetch() {
      let params = JSON.parse(JSON.stringify({...this.params}));
      params.costMonthStart = this.searchDate ? this.searchDate[0] : '';
      params.costMonthEnd = this.searchDate ? this.searchDate[1] : '';
      this.loading = true;
      const res = await queryOrderListOfPage(params);
      if (res) {
        this.tableData = res.records;
        this.total = res.total;
      }
      this.loading = false;
    },

    search() {
      this.params.current = 1;
      this.fetch();
    },
    handleSizeChange(val) {
      this.params.current = 1;
      this.params.size = val;
      this.fetch();
    },
    handleCurrentChange(val) {
      this.params.current = val;
      this.fetch();
    },
    // 点击修改
    onHandleEdit(row) {
      this.dialogForm = {...row, relationPoList: [], noRelationList: []};
      if (row.oldType === 35) {
        this.dialogForm.relationPoList = [
          {poCode: undefined, quantity: undefined},
        ];
        this.dialogForm.noRelationList = [{
          currencyCode: undefined,
          customCode: undefined,
          customName: undefined,
          unTaxPrice: undefined
        }];
      }
      this.dialogEditVisible = true;
      this.$nextTick(() => {
        this.abc2 = true;
      });
    },
    addBindPo() {
      this.dialogForm.relationPoList.push({
        poCode: undefined,
        quantity: undefined,
      });
    },
    removeBindPo(index) {
      this.dialogForm.relationPoList.splice(index, 1);
    },
    addNoPo() {
      this.dialogForm.noRelationList.push({currencyCode: undefined, customCode: undefined, unTaxPrice: undefined});
    },
    removeNoPo(index) {
      this.dialogForm.noRelationList.splice(index, 1);
    },
    // 保存价格编辑
    async saveEditPrice() {
      this.$refs.dialogFormRef.validate(async valid => {
        if (valid) {
          let params = {...this.dialogForm};
          this.filterEditParams(params);
          this.saveEditPriceLoading = true;
          modifyOrderByAdjustCostCode(params)
              .then(res => {
                if (!res.err) {
                  this.$message({
                    message: '调整成功',
                    type: 'success',
                    center: true,
                  });
                  this.dialogEditVisible = false;
                  this.fetch();
                }
              })
              .finally(() => {
                this.saveEditPriceLoading = false;
              });
        } else {
          return false;
        }
      });
    },
    // 关闭价格编辑弹窗
    cancelEditPrice() {
      this.dialogEditVisible = false;
      Object.assign(this.$data.dialogForm, this.$options.data().dialogForm);
      this.$refs.abc2.val = null;
      this.abc2 = false;

      this.$nextTick(function () {
        this.$refs.dialogFormRef.clearValidate();
      });
    },
    // 获取导入模板
    async downloadTemp() {
      window.location.href =
          'https://zkt-pro-bucket.obs.cn-east-3.myhuaweicloud.com/recon/%E5%BC%82%E5%B8%B8%E5%8D%95%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx';
    },
    reset() {
      Object.assign(this.params, this.$options.data().params);
      this.searchDate = '';
      this.$refs.abc1.val = null;
      this.fetch();
    },
    // 导出
    updateFormSHow(data) {
      // const param = JSON.stringify(this.params);
      // const nargin = {
      //   appName: 'business-center-admin',
      //   fileName: data,
      //   fileType: 'inventoryCostPriceExportHandler',
      // };
      // var url = '/data-center/excel/createDownloadTask';
      // this.$httpHelperYapiSso.postAxios(url, { param, ...nargin }, res => {
      //   if (res.code === 0) {
      //     this.$message({
      //       message: '下载任务已建立,请去数据下载页面下载',
      //       type: 'success',
      //       center: true,
      //     });
      //   }
      // });
    },
    async onSuccess(e) {
      if (!e.success) {
        this.$message.error(e.msg || '导入失败');
        return;
      }
      if (e.data) {
        importOrderData({
          fileId: e.data.id,
        }).then(res => {
          this.$message.success('导入成功');
          this.params.current = 1;
          this.params.size = 10;
          this.fetch();
        });
      }
    },
    // 导出
    down_excel() {
      let params = JSON.parse(JSON.stringify({...this.params}));
      params.costMonthStart = this.searchDate ? this.searchDate[0] : '';
      params.costMonthEnd = this.searchDate ? this.searchDate[1] : '';
      if (!this.searchDate) {
        this.$message.error('请选择账期');
        return;
      }
      if (this.params.sceneFlag == '' || this.params.sceneFlag == null) {
        this.$message.error('请选择业务场景');
        return;
      }
      newExportExcel(
          params,
          '/api/recon-core/financeAdjust/exportOrderData',
          'post',
      ).then(res => {
        console.log('res', res.data);
        downloadFile(res.data, '异常单导出结果');
      });
    },
    filterEditParams(params) {
      let finalRelationList = [], finalNoRelationList = [];
      let relationPoList = params.relationPoList;
      let noRelationList = params.noRelationList;
      params.relationPoList = finalRelationList;
      params.noRelationList = finalNoRelationList;
      for (let i = 0; i < relationPoList.length; i++) {
        if (relationPoList[i].poCode) {
          finalRelationList.push(relationPoList[i]);
        }
      }
      for (let i = 0; i < noRelationList.length; i++) {
        if (noRelationList[i].customCode) {
          noRelationList[i].customName = this.findEntityName(noRelationList[i].customCode);
          finalNoRelationList.push(noRelationList[i]);
        }
      }
    },
    findEntityName(entityCode) {
      for (let i = 0; i < this.allEntities.length; i++) {
        if (this.allEntities[i].entityCode === entityCode) {
          return this.allEntities[i].name;
        }
      }
      return null;
    }
  },
};
</script>
<style scoped>
.table-empty {
  min-height: 60px;
  text-align: center;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 2px;
  color: rgba(0, 0, 0, 0.45);
}
</style>
