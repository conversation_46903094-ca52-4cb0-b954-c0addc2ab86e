<template>
  <div>
    <el-form ref="formSearch" inline :model="searchParams">
      <el-form-item label="签约时间:">
        <el-date-picker
          v-model="signingDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item
        prop="supplierPlatCustNo"
        :rules="[
          {
            validator: rules.validateMemberNo,
            trigger: 'change',
          },
        ]"
        label="会员编号:"
      >
        <el-input
          v-model="searchParams.supplierPlatCustNo"
          placeholder="请输入会员编号"
        />
      </el-form-item>
      <el-form-item label="会员名称:">
        <el-input
          v-model="searchParams.supplierName"
          placeholder="请输入会员名称"
        />
      </el-form-item>
      <el-form-item label="平台名称:">
        <el-input
          v-model="searchParams.platform"
          placeholder="请输入平台名称"
        />
      </el-form-item>
      <el-form-item label="状态:">
        <el-select
          v-model="searchParams.status"
          clearable
          placeholder="请选择状态"
        >
          <el-option
            v-for="item in hasSignedList"
            :key="item.code"
            :label="item.label"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <ac-permission-button
          btn-text="批量审批"
          permission-key=""
          @click="handleBatchApproval"
        ></ac-permission-button>
        <ac-permission-button
          type="primary"
          btn-text="新增签约"
          icon="el-icon-plus"
          permission-key=""
          @click="
            showDialog = true;
            currentRow = null;
          "
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
      :check-selectable="handleCheckSelectable"
      @selection-change="selectionChange"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          v-if="scope.row.status === 1"
          slot="reference"
          type="text"
          size="small"
          btn-text="通过"
          permission-key=""
          @click="handleOperate(scope.row, 1)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="scope.row.status === 1"
          slot="reference"
          type="text"
          size="small"
          btn-text="拒绝"
          permission-key=""
          @click="handleOperate(scope.row, 2)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="[2, 4].includes(scope.row.status)"
          slot="reference"
          type="text"
          size="small"
          :btn-text="scope.row.status === 4 ? '提现' : '签约'"
          permission-key=""
          @click="openModal({ ...scope.row })"
        ></ac-permission-button>
        <ac-permission-button
          v-if="scope.row.status === 4"
          slot="reference"
          type="text"
          size="small"
          btn-text="银行卡"
          permission-key=""
          @click="jump(scope.row, 1)"
        ></ac-permission-button>
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="明细"
          permission-key=""
          @click="jump(scope.row, 2)"
        ></ac-permission-button>
        <!-- <a v-if="[0, 3, 5].includes(scope.row.status)">-</a> -->
      </template>
    </dynamictable>
    <addMemberModal
      v-model="showDialog"
      :current-row="currentRow"
      @onGet="getList(true)"
    ></addMemberModal>
    <withdrawalModal
      v-model="showWithdrawal"
      :supplier="currentRow"
      type="xiamen"
      @onGet="getList()"
    ></withdrawalModal>
    <confirmDialog
      v-model="showConfirmDialog"
      :title="title"
      @onClose="onClose"
    ></confirmDialog>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import addMemberModal from './components/addMemberModal';
  import withdrawalModal from './components/withdrawalModal';
  import confirmDialog from './components/confirmDialog';
  import {
    supplierQueryList,
    supplierReview,
    supplierSubmit,
  } from '@/api/xiamen';
  import { parseTime, debounce } from '@/utils';
  import { validateMember } from '@/utils/validate';

  export default {
    name: 'MemberInformationMgt',
    components: {
      dynamictable,
      addMemberModal,
      withdrawalModal,
      confirmDialog,
    },

    data() {
      const validateMemberNo = (rule, value, callback) => {
        if (!validateMember(value) && value) {
          callback(new Error('只能输入中文、数字、英文查询'));
        } else {
          callback();
        }
      };
      let columns = [
        {
          prop: 'supplierPlatCustNo',
          label: '会员编号',
        },
        {
          prop: 'supplierName',
          label: '会员名称',
        },
        {
          prop: 'platform',
          label: '平台',
        },
        {
          prop: 'accountCashBal',
          label: '账户余额',
        },
        {
          prop: 'accountFloatBal',
          label: '在途资金',
        },
        {
          prop: 'signTime',
          label: '签约时间',
        },
        {
          prop: 'statusDesc',
          label: '状态',
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          minWidth: '100',
          maxWidth: '300',
          scopedSlots: { customRender: 'operation' },
        },
      ];

      return {
        showConfirmDialog: false,
        title: '批量审批',
        showDialog: false, // 签约弹窗
        showWithdrawal: false, // 提现弹窗
        currentRow: null, // 当前数据列
        signingDate: '', // 签约时间
        selectionIds: [], // 批量选择的数据
        hasSignedList: [
          { code: 0, label: '默认' },
          { code: 1, label: '待复核' },
          { code: 2, label: '待签约' },
          { code: 3, label: '复核拒绝' },
          { code: 4, label: '签约成功' },
          { code: 5, label: '签约失败' },
        ],
        searchParams: {
          supplierPlatCustNo: '',
          supplierName: '',
          platform: '',
          status: '',
        },
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          index: true,
          indexName: '编号',
          mutiSelect: true,
        },
        columns,
        rules: {
          validateMemberNo,
        },
      };
    },

    created() {
      this.getList(true);
    },

    methods: {
      handleOperate(row, type) {
        if (type === 2) {
          this.$prompt('拒绝理由', '二次确认', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputType: 'textarea',
            inputValidator: value => {
              if (!value) {
                return '拒绝原因不能为空！';
              }
            },
          })
            .then(({ value }) => {
              this.saveOperateApi({
                ids: [row.id],
                pass: false,
                remark: value,
                tradeType: row.tradeType,
              });
            })
            .catch(() => {});
          return;
        }

        this.$confirm('确认同意该操作?', '二次确认', {
          confirmButtonText: '通过',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.saveOperateApi({
              ids: [row.id],
              pass: true,
              tradeType: row.tradeType,
            });
          })
          .catch(() => {});
      },
      async saveOperateApi(params, cb) {
        const { res, err } = await supplierReview(params);
        if (!err) {
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          this.getList();
          cb && cb();
        }
      },
      // 禁止选择不符合的数据
      handleCheckSelectable(row) {
        return row.status === 1;
      },

      // 勾选记录
      selectionChange(ids = []) {
        this.selectionIds = ids.map(item => item.id);
      },
      // 批量确认
      onClose(type, { remark }) {
        if (type !== 'pass' && !remark) {
          return this.$message.error('拒绝原因不能为空！');
        }
        const { selectionIds } = this;
        this.saveOperateApi(
          {
            ids: selectionIds,
            pass: type === 'pass',
            remark,
          },
          () => {
            this.showConfirmDialog = false;
          },
        );
      },
      // 批量审批
      handleBatchApproval: debounce(function () {
        const { selectionIds } = this;
        if (selectionIds.length === 0) {
          this.$message.error('请勾选未审批记录');
          return;
        }
        this.showConfirmDialog = true;
      }, 1000),
      handleSearch() {
        this.$refs.formSearch.validate(async valid => {
          if (!valid) return;
          this.getList(true);
        });
      },
      openModal(row) {
        if (!row) return;
        this.currentRow = row;
        // status 4 提现 2 签约
        if (row.status === 4) {
          this.showWithdrawal = true;
        } else {
          this.supplierSubmit(row.id);
          // this.showDialog = true;
        }
      },
      // 直接签约
      async supplierSubmit(id) {
        const { res, err } = await supplierSubmit({ id });
        if (res && !err) {
          window.open(res);
        }
      },
      jump(row, type) {
        if (type === 1) {
          this.$router.push({
            path: 'memberInforBankCard',
            query: { platCustNo: row.supplierPlatCustNo, id: row.id },
          });
          return;
        }
        this.$router.push({
          path: 'fundDetail',
          query: {
            thirdAccountId: row.supplierPlatCustNo,
          },
        });
      },
      getParams() {
        const signingDate = this.signingDate;
        this.searchParams.signStartTime = signingDate
          ? parseTime(signingDate[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.signEndTime = signingDate
          ? parseTime(signingDate[1], '{y}-{m}-{d}')
          : '';
        const params = {
          ...this.searchParams,
          pageNum: this.pagination.pageSize,
          pageSize: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const { res, err } = await supplierQueryList(params);
        this.options.loading = false;
        if (res && !err) {
          this.list = res.list;
          this.pagination.total = res.totalNum;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.signingDate = '';
        this.getList(true);
      },
    },
  };
</script>
<style lang="scss"></style>
