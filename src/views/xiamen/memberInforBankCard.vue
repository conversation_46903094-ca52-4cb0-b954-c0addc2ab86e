<template>
  <div class="memberInforBankCard">
    <el-row>
      <el-col :span="22">
        <el-tabs v-model="tabType" type="card" @tab-click="handleTabClick">
          <el-tab-pane label="银行卡明细" name="1"></el-tab-pane>
          <!-- <el-tab-pane label="操作日志" name="2"></el-tab-pane> -->
        </el-tabs>
      </el-col>
      <el-col :span="2">
        <el-button type="primary" @click="jump()">返回</el-button>
      </el-col>
    </el-row>

    <div v-if="tabType === '2'">
      <dynamictable
        key="2"
        :data-source="logList"
        :columns="logColumns"
        :options="options"
        :pagination="pagination"
        :fetch="getList"
      />
    </div>
    <div v-else>
      <el-row style="margin-bottom: 20px">
        <el-col :span="22">
          <el-form inline>
            <el-form-item label="状态:">
              <el-select
                v-model="searchParams.status"
                clearable
                placeholder="请选择状态"
              >
                <el-option label="待复核" :value="1"></el-option>
                <el-option label="待绑卡" :value="2"></el-option>
                <el-option label="复核拒绝" :value="3"></el-option>
                <el-option label="绑卡成功" :value="4"></el-option>
                <el-option label="绑卡失败" :value="5"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getList(true)">查询</el-button>
              <el-button type="primary" @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
          <el-button type="primary" @click="bankDialog = true">新增</el-button>
        </el-col>
      </el-row>
      <dynamictable
        key="1"
        :data-source="list"
        :columns="columns"
        :options="options"
        :fetch="getList"
      >
        <template slot="operation" slot-scope="scope">
          <ac-permission-button
            v-if="scope.row.status === 1 && scope.row.settleFlag != 'Y'"
            slot="reference"
            type="text"
            size="small"
            btn-text="通过"
            permission-key=""
            @click="handleOperate(scope.row, 1)"
          ></ac-permission-button>
          <ac-permission-button
            v-if="scope.row.status === 1 && scope.row.settleFlag != 'Y'"
            slot="reference"
            type="text"
            size="small"
            btn-text="拒绝"
            permission-key=""
            @click="handleOperate(scope.row, 2)"
          ></ac-permission-button>
          <ac-permission-button
            v-if="scope.row.status === 2 && scope.row.settleFlag != 'Y'"
            slot="reference"
            type="text"
            size="small"
            btn-text="绑卡"
            permission-key=""
            @click="handleOperate(scope.row, 3)"
          ></ac-permission-button>
          <ac-permission-button
            v-if="scope.row.settleFlag != 'Y' && scope.row.status === 4"
            slot="reference"
            type="text"
            size="small"
            btn-text="设为结算卡"
            permission-key=""
            @click="handleSetCard(scope.row)"
          ></ac-permission-button>
          <a
            v-if="
              [3, 5].includes(scope.row.status) ||
              (scope.row.status === 4 && scope.row.settleFlag === 'Y')
            "
          >
            -
          </a>
        </template>
      </dynamictable>
      <el-dialog
        title="新建"
        :visible.sync="bankDialog"
        :close-on-click-modal="false"
        @closed="onClose"
      >
        <el-form
          ref="bankForm"
          :model="bankForm"
          :rules="rulesForm"
          label-width="140px"
        >
          <el-form-item label="开户银行: " prop="bankNo">
            <el-select
              v-model="bankForm.bankNo"
              style="width: 80%"
              placeholder="请选择开户银行"
            >
              <el-option
                v-for="item in bankList"
                :key="item.bankNo"
                :label="item.bankName"
                :value="item.bankNo"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="开户卡号: " prop="accNo">
            <el-input
              v-model="bankForm.accNo"
              placeholder="请输入开户卡号"
              style="width: 80%"
            ></el-input>
          </el-form-item>
          <el-form-item label="开户支行: " prop="bankName">
            <el-input
              v-model="bankForm.bankName"
              placeholder="请输入开户支行"
              style="width: 80%"
            ></el-input>
          </el-form-item>
          <el-form-item label="手机号: " prop="accMobile">
            <el-input
              v-model="bankForm.accMobile"
              style="width: 80%"
              placeholder="请输入手机号"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="bankDialog = false">取 消</el-button>
          <el-button type="primary" @click="saveBank">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
  import {
    queryBankCardList,
    supportBankList,
    supplierBankCardSave,
    supplierReplaceBankCard,
    supplierBankCardApply,
    supplierBankCardReview,
    supplierBankCardSubmit,
    querySupplierCards,
    bankCardLogSearch,
  } from '@/api/xiamen';
  import dynamictable from '@/components/dynamic-table';
  import { debounce, setInitData } from '@/utils';
  import injectHost from '@/utils/injectHost';
  import { replaceLocalDomain } from '@/utils/index.js';
  import { isPhone } from '@/utils/validate';
  const BASE_URL = injectHost().apiHost;

  export default {
    components: {
      dynamictable,
    },
    data() {
      const validatePhone = (rule, value, callback) => {
        if (!isPhone(value) && value) {
          callback(new Error('手机号格式错误'));
        } else {
          callback();
        }
      };
      let columns = [
        {
          prop: 'supplierName',
          label: '开户名称',
        },
        {
          prop: 'bankNm',
          label: '开户银行',
        },
        {
          prop: 'cardNo',
          label: '开户卡号',
        },
        // {
        //   prop: 'bankNm',
        //   label: '开户支行',
        // },
        {
          prop: 'settleFlag',
          label: '是否为结算卡',
          render: ({ settleFlag }) => (
            <span>{settleFlag === 'Y' ? '是' : '否'}</span>
          ),
        },
        {
          prop: 'remark',
          label: '备注',
        },
        {
          prop: 'statusDesc',
          label: '开卡状态',
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '200',
          scopedSlots: { customRender: 'operation' },
        },
      ];
      return {
        logColumns: [
          {
            prop: 'operatorId',
            label: '操作ID',
          },
          {
            prop: 'operatorName',
            label: '操作人姓名',
          },
          {
            prop: 'optAction',
            label: '操作动作',
          },
          {
            prop: 'optTime',
            label: '操作时间',
          },
        ],
        tabType: '1',
        searchParams: {
          status: '',
        }, // 搜索参数
        columns,
        list: [],
        logList: [],
        bankForm: {}, // 新增银行卡数据
        bankDialog: false, // 银行卡弹窗
        bankList: [],
        options: {
          loading: false,
          border: true,
          index: true,
          indexName: '编号',
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        rulesForm: {
          bankNo: [
            { required: true, message: '请选择开户银行', trigger: 'blur' },
          ],
          accNo: [
            {
              required: true,
              message: '请输入开户卡号',
              trigger: 'blur',
            },
          ],
          accMobile: [
            { required: true, message: '请输入手机号', trigger: 'blur' },
            {
              validator: validatePhone,
              trigger: 'blur',
            },
          ],
        },
      };
    },
    created() {
      this.getList();
      supportBankList().then(({ res, err }) => {
        if (res && !err) {
          this.bankList = res;
        }
      });
    },

    methods: {
      handleOperate: debounce(async function (row, type) {
        if (type === 2) {
          this.$prompt('拒绝理由', '二次确认', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputType: 'textarea',
            inputValidator: value => {
              if (!value) {
                return '拒绝原因不能为空！';
              }
            },
          })
            .then(({ value }) => {
              this.saveOperateApi({
                id: row.id,
                pass: false,
                remark: value,
              });
            })
            .catch(() => {});
          return;
        }
        if (type === 3) {
          const { res, err } = await supplierBankCardSubmit({
            id: row.id,
          });
          if (!err) {
            this.$message({
              type: 'success',
              message: '绑卡成功!',
            });
            if (res.retUrl) {
              window.open(res.retUrl);
            }
            this.getList();
          }
          return;
        }

        this.$confirm('确认同意该操作?', '二次确认', {
          confirmButtonText: '通过',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.saveOperateApi({ id: row.id, pass: true });
          })
          .catch(() => {});
      }, 1000),
      // async handleOperate(row, type) {
      //   if (type === 2) {
      //     this.$prompt('拒绝理由', '二次确认', {
      //       confirmButtonText: '确定',
      //       cancelButtonText: '取消',
      //       inputType: 'textarea',
      //       inputValidator: value => {
      //         if (!value) {
      //           return '拒绝原因不能为空！';
      //         }
      //       },
      //     })
      //       .then(({ value }) => {
      //         this.saveOperateApi({
      //           id: row.id,
      //           pass: false,
      //           remark: value,
      //         });
      //       })
      //       .catch(() => {});
      //     return;
      //   }
      //   if (type === 3) {
      //     const { res, err } = await supplierBankCardSubmit({
      //       id: row.id,
      //     });
      //     if (!err) {
      //       this.$message({
      //         type: 'success',
      //         message: '绑卡成功!',
      //       });
      //       this.getList();
      //     }
      //     return;
      //   }

      //   this.$confirm('确认同意该操作?', '二次确认', {
      //     confirmButtonText: '通过',
      //     cancelButtonText: '取消',
      //     type: 'warning',
      //   })
      //     .then(() => {
      //       this.saveOperateApi({ id: row.id, pass: true });
      //     })
      //     .catch(() => {});
      // },
      async saveOperateApi(params) {
        const { res, err } = await supplierBankCardReview(params);
        if (!err) {
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          this.getList();
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
      },
      handleTabClick({ name }) {
        if (name === '1') {
          this.getList();
        } else {
          this.bankCardLogSearch();
        }
      },
      async bankCardLogSearch() {
        const { pageSize, pageLimit } = this.pagination;
        const date = setInitData(30);
        const body = {
          fromDate: date[0],
          endDate: date[1],
          pageNo: pageSize,
          pageSize: pageLimit,
          userId: this.$store.state.user.userInfo.id,
        };

        const { res, err } = await bankCardLogSearch(body);
        if (res && !err) {
          this.logList = res ? res.records : [];
          this.pagination.total = res ? res.total : [];
        }
      },
      async getList() {
        const { id } = this.$route.query;

        this.options.loading = true;
        const { res, err } = await querySupplierCards({
          ...this.searchParams,
          supplierId: id,
        });
        this.options.loading = false;
        if (res && !err) {
          this.list = res ? res : [];
        }
      },
      jump() {
        this.$router.go(-1);
      },
      saveBank: debounce(function () {
        this.$refs.bankForm.validate(async valid => {
          if (!valid) return;
          const { platCustNo, id } = this.$route.query;
          const { bankForm } = this;
          supplierBankCardApply({
            ...bankForm,
            platCusNo: platCustNo,
            supplierId: id,
          }).then(({ res, err }) => {
            if (!err) {
              this.bankDialog = false;
              this.$alert('银行卡信息保存成功', '提示', {
                confirmButtonText: '确定',
                showClose: false,
                callback: action => {
                  if (res.retUrl) {
                    window.open(res.retUrl);
                  }
                  this.getList();
                },
              });
            }
          });
        });
      }, 1000),
      handleSetCard: debounce(function (row) {
        const { path, query } = this.$route;
        const url = `${window.location.protocol}//${replaceLocalDomain(
          BASE_URL,
        )}/#${path}?platCustNo=${query.platCustNo}&id=${query.id}`;
        supplierReplaceBankCard({
          platCusNo: row.platCustNo,
          cardNoSec: row.cardNoSec,
          id: row.id,
          returnUrl: url,
        }).then(({ res, err }) => {
          if (!err) {
            this.$alert('设置成功', '提示', {
              confirmButtonText: '确定',
              showClose: false,
              callback: action => {
                if (res.retUrl) {
                  window.open(res.retUrl);
                }
                this.getList();
              },
            });
          }
        });
      }, 1000),
      onClose() {
        this.$nextTick(function () {
          this.$refs.bankForm.clearValidate();
          this.bankForm = {};
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .memberInforBankCard {
    /deep/.el-input__inner {
      text-align: left;
      &::placeholder {
        text-align: left;
      }

      &::-webkit-input-placeholder {
        /* WebKit browsers 适配谷歌 */
        text-align: left;
      }

      &:-moz-placeholder {
        /* Mozilla Firefox 4 to 18 适配火狐 */
        text-align: left;
      }

      &::-moz-placeholder {
        /* Mozilla Firefox 19+ 适配火狐 */
        text-align: left;
      }

      &:-ms-input-placeholder {
        /* Internet Explorer 10+  适配ie*/
        text-align: left;
      }
    }
  }
</style>
