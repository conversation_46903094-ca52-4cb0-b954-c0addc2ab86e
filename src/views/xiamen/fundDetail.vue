<!--
 * @Author: 七七
 * @Date: 2022-04-18 00:07:22
 * @LastEditors: xuxiang
 * @LastEditTime: 2022-05-05 11:01:31
 * @FilePath: /access-fmis-web/src/views/xiamen/fundDetail.vue
-->
<template>
  <div>
    <el-button
      style="font-size: 16px; margin-bottom: 10px"
      type="text"
      @click="$router.go(-1)"
    >
      返回
    </el-button>
    <el-tabs v-model="detailType" type="card" @tab-click="handleClick">
      <el-tab-pane label="日切余额明细" name="1">
        <dailyBalanceDetails></dailyBalanceDetails>
      </el-tab-pane>
      <el-tab-pane label="银行流水明细" name="2"><bankDetails /></el-tab-pane>
      <el-tab-pane label="平台交易明细" name="3">
        <platformTransactionDetails
          :biz-type="bizType"
        ></platformTransactionDetails>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
  import bankDetails from './components/bankDetails';
  import dailyBalanceDetails from './components/dailyBalanceDetails';
  import platformTransactionDetails from './components/platformTransactionDetails.vue';
  import { getAccountSelectors } from '@/api/xiamen';
  export default {
    components: {
      bankDetails,
      dailyBalanceDetails,
      platformTransactionDetails,
    },
    data() {
      return {
        bizType: [],
        detailType: '1', //明细类型1:日切余额明细 2:银行流水明细 3:平台交易明细
      };
    },
    created() {
      getAccountSelectors().then(({ err, res }) => {
        if (res && !err) {
          this.bizType = res;
          // res.forEach(element => {
          //   if (element.bizType === 'tradeTypes') {
          //     console.log(element.selectors, 'element');
          //     this.bizType = element.selectors;
          //   }
          // });
        }
      });
    },
    mounted() {},
    methods: {
      handleClick() {},
    },
  };
</script>
<style scoped></style>
