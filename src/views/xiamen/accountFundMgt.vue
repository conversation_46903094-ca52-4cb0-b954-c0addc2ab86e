<template>
  <div>
    <el-form inline>
      <el-form-item label="申请时间:">
        <el-date-picker
          v-model="applicationDate"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="转出账户类型:">
        <el-select
          v-model="searchParams.outAccountType"
          clearable
          placeholder="请选择转出账户类型"
        >
          <el-option label="平台子账户" :value="1"></el-option>
          <el-option label="会员子账户" :value="2"></el-option>
          <el-option label="银行账户" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="转入账户类型:">
        <el-select
          v-model="searchParams.inAccountType"
          clearable
          placeholder="请选择转入账户类型"
        >
          <el-option label="平台子账户" :value="1"></el-option>
          <el-option label="会员子账户" :value="2"></el-option>
          <el-option label="银行账户" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="交易类型:">
        <el-select
          v-model="searchParams.tradeType"
          clearable
          placeholder="请选择交易类型"
        >
          <el-option label="转账" :value="1"></el-option>
          <el-option label="提现" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="转账来源:">
        <el-select
          v-model="searchParams.applyFrom"
          clearable
          placeholder="请选择转账来源"
        >
          <el-option label="一般转账" :value="1"></el-option>
          <el-option label="厦门资金链路转账" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态:">
        <el-select
          v-model="searchParams.reviewStatus"
          clearable
          placeholder="请选择状态"
        >
          <el-option label="待复核" :value="101"></el-option>
          <el-option label="复核拒绝" :value="102"></el-option>
          <el-option label="待终审" :value="103"></el-option>
          <el-option label="终审拒绝" :value="104"></el-option>
          <el-option label="待操作" :value="105"></el-option>
          <el-option label="操作成功" :value="106"></el-option>
          <el-option label="操作失败" :value="108"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <!-- <el-button
          type="primary"
          :loading="batchLoading"
          @click="handleBatchTransfer"
        >
          {{ batchLoading ? '批量操作中' : '批量操作' }}
        </el-button> -->
        <el-button type="primary" @click="handleBatchTrial(101)">
          批量复核
        </el-button>
        <el-button type="primary" @click="handleBatchTrial(103)">
          批量终审
        </el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      v-loading="btnLoading"
      element-loading-text="审核中"
      element-loading-spinner="el-icon-loading"
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
      @selection-change="selectionChange"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          v-if="[101, 103].includes(scope.row.reviewStatus)"
          slot="reference"
          type="text"
          size="small"
          btn-text="通过"
          permission-key=""
          @click="handleOperate(scope.row, 1)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="[101, 103].includes(scope.row.reviewStatus)"
          slot="reference"
          type="text"
          size="small"
          btn-text="拒绝"
          permission-key=""
          @click="handleOperate(scope.row, 2)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="
            scope.row.reviewStatus === 105 ||
            (scope.row.applyFrom === 2 &&
              [1, 5].includes(scope.row.reviewStatus))
          "
          slot="reference"
          type="text"
          size="small"
          :btn-text="scope.row.tradeTypeDesc"
          permission-key=""
          @click="handleTransfer(scope.row)"
        ></ac-permission-button>
        <a
          v-if="
            [102, 104, 106, 108].includes(scope.row.reviewStatus) ||
            (scope.row.applyFrom === 1 &&
              ![101, 103, 105].includes(scope.row.reviewStatus)) ||
            (scope.row.applyFrom === 2 &&
              ![1, 5].includes(scope.row.reviewStatus))
          "
        >
          -
        </a>
      </template>
    </dynamictable>
    <confirmDialog
      v-model="showDialog"
      :title="title"
      @onClose="onClose"
    ></confirmDialog>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';

  import { debounce, parseTime } from '@/utils';
  import {
    accCapitalChangeReviewList,
    accCapitalChangeReviewPass,
    accCapitalChangeRevieBwatchPass,
    accCapitalChangeReviewReview,
    accCapitalChangeReviewReview2,
  } from '@/api/xiamen';
  import confirmDialog from './components/confirmDialog';

  export default {
    components: {
      dynamictable,
      confirmDialog,
    },

    data() {
      let columns = [
        {
          prop: 'id',
          label: 'ID',
        },
        {
          prop: 'applyTime',
          label: '申请时间',
        },
        {
          prop: 'outAccountTypeDesc',
          label: '转出账户类型',
        },
        {
          prop: 'outAccountName',
          label: '转出账户名',
        },
        {
          prop: 'outAccount',
          label: '转出账户号',
        },
        {
          prop: 'inAccountTypeDesc',
          label: '转入账户类型',
        },
        {
          prop: 'inAccountName',
          label: '转入账户名',
        },
        {
          prop: 'inAccount',
          label: '转入账户号',
        },

        {
          prop: 'tradeTypeDesc',
          label: '交易类型',
        },
        {
          prop: 'amount',
          label: '金额',
        },
        {
          prop: 'remark',
          label: '备注',
        },
        {
          prop: 'reviewStatusDesc',
          label: '状态',
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '100',
          scopedSlots: { customRender: 'operation' },
        },
      ];

      return {
        reviewStatus: '', // 批量审核状态
        applicationDate: '',
        selectionRows: [],
        btnLoading: false,
        batchLoading: false,
        showDialog: false,
        title: '批量审核',
        searchParams: {
          outAccountType: '',
          inAccountType: '',
          tradeType: '',
          applyFrom: 1,
          reviewStatus: '',
        },
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          mutiSelect: true,
        },
        columns,
      };
    },
    created() {
      this.applicationDate = this.initDate();
      this.getList(true);
    },
    methods: {
      handleOperate(row, type) {
        if (type === 2) {
          this.$prompt('拒绝理由', '二次确认', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputType: 'textarea',
            inputValidator: value => {
              if (!value) {
                return '拒绝原因不能为空！';
              }
            },
          })
            .then(({ value }) => {
              this.saveOperateApi(
                {
                  ids: [row.id],
                  pass: false,
                  remark: value,
                  tradeType: row.tradeType,
                },
                row,
              );
            })
            .catch(() => {});
          return;
        }
        this.$confirm('确认同意该操作?', '二次确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.saveOperateApi(
              {
                ids: [row.id],
                pass: true,
                tradeType: row.tradeType,
              },
              row,
            );
          })
          .catch(() => {});
      },
      async saveOperateApi(params, row, cb) {
        const apis =
          row.reviewStatus === 101
            ? accCapitalChangeReviewReview
            : accCapitalChangeReviewReview2;
        const { res, err } = await apis(params);
        if (!err) {
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          cb && cb();
          this.getList();
        }
      },
      initDate() {
        let now = new Date(); //当前日期
        let nowMonth = now.getMonth(); //当前月
        let nowYear = now.getFullYear(); //当前年
        const applyStartTime = parseTime(
          new Date(nowYear, nowMonth, 1),
          '{y}-{m}-{d}',
        );
        const applyEndTime = parseTime(now, '{y}-{m}-{d}');
        return [applyStartTime, applyEndTime];
      },
      handleCheckSelectable(row) {
        return (
          row.reviewStatus === 1 ||
          (row.applyFrom === 2 && row.reviewStatus === 5)
        );
      },
      selectionChange(ids = []) {
        this.selectionRows = ids;
      },
      checkSelectionRows(selectionRows) {
        let flag = false;
        selectionRows.forEach(item => {
          if (![105, 108].includes(item.reviewStatus)) {
            flag = true;
          } else {
            if (item.reviewStatus === 108 && item.applyFrom === 1) {
              flag = true;
            }
          }
        });
        return flag;
      },
      checkSelectionRowsTrial(reviewStatus, selectionRows) {
        let flag = false;
        if (reviewStatus === 101) {
          selectionRows.map(item => {
            if (item.reviewStatus !== 101) {
              flag = true;
            }
          });
        } else {
          selectionRows.map(item => {
            if (item.reviewStatus !== 103) {
              flag = true;
            }
          });
        }
        return flag;
      },
      onClose(type, { remark }) {
        const { selectionRows, reviewStatus } = this;
        const selectionIds = selectionRows.map(item => item.id);
        if (type !== 'pass' && !remark) {
          return this.$message.error('拒绝原因不能为空！');
        }

        this.saveOperateApi(
          {
            ids: selectionIds,
            pass: type === 'pass',
            tradeType: selectionRows[0]?.tradeType,
            remark,
          },
          { reviewStatus },
          () => {
            this.showDialog = false;
          },
        );
      },
      // 批量审批
      handleBatchTrial: debounce(function (reviewStatus) {
        const { selectionRows } = this;
        if (selectionRows.length === 0) {
          this.$message.error(
            `请选择待${reviewStatus === 101 ? '复核' : '终审'}的转账记录`,
          );
          return;
        }
        // if (this.checkSelectionRowsTrial(reviewStatus, selectionRows)) {
        //   this.$message.error(
        //     `请勾选符合${reviewStatus === 101 ? '复核' : '终审'}数据`,
        //   );
        //   return;
        // }
        this.showDialog = true;
        this.reviewStatus = reviewStatus;
      }, 1000),

      // 批量审核
      handleBatchTransfer: debounce(function () {
        const { selectionRows } = this;
        if (selectionRows.length === 0) {
          this.$message.error('请勾选未审核记录');
          return;
        }
        // if (this.checkSelectionRows(selectionRows)) {
        //   this.$message.error('请勾选符合审核数据');
        //   return;
        // }

        const selectionIds = selectionRows.map(item => item.id);
        this.batchLoading = true;
        accCapitalChangeRevieBwatchPass({
          reviewIdList: selectionIds,
          tradeType: selectionRows[0]?.tradeType,
        })
          .then(({ res, err }) => {
            this.batchLoading = false;
            if (!err) {
              this.$message.success('批量操作成功');
              this.getList();
            }
          })
          .catch(err => {
            this.batchLoading = false;
          });
      }, 1000),
      handleTransfer: debounce(function (row) {
        this.btnLoading = true;
        accCapitalChangeReviewPass({ id: row.id, tradeType: row.tradeType })
          .then(({ res, err }) => {
            this.btnLoading = false;
            if (!err) {
              this.$message.success(`${row.tradeTypeDesc}成功`);
              this.getList();
            }
          })
          .catch(err => {
            this.btnLoading = false;
          });
      }, 1000),
      getParams() {
        const applicationDate = this.applicationDate;
        this.searchParams.applyStartTime = applicationDate
          ? parseTime(applicationDate[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.applyEndTime = applicationDate
          ? parseTime(applicationDate[1], '{y}-{m}-{d}')
          : '';
        const params = {
          ...this.searchParams,
          pageNum: this.pagination.pageSize,
          pageSize: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const { res, err } = await accCapitalChangeReviewList(params);
        this.options.loading = false;
        if (res && !err) {
          this.list = res ? res.list : [];
          this.pagination.total = res ? res.totalNum : 0;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.applicationDate = this.initDate();
        this.getList(true);
      },
    },
  };
</script>
<style lang="scss"></style>
