<template>
  <div>
    <el-form inline>
      <el-form-item label="子账户类型:">
        <el-select
          v-model="acctTypes"
          clearable
          multiple
          placeholder="请选择(支持多选)"
        >
          <el-option
            v-for="item in typeList"
            :key="item.code"
            :label="item.label"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="loadData()">查询</el-button>
        <el-button type="primary" @click="resetData">重置</el-button>
      </el-form-item>
    </el-form>
    <div style="margin-bottom: 10px">
      <ac-permission-button
        btn-text="转账"
        permission-key=""
        @click="onTransferClick()"
      ></ac-permission-button>
      <span style="color: #d9001b; float: right">
        CNY汇总账户余额：{{ totalAmount }} 元
      </span>
    </div>
    <dynamictable
      :data-source="items"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="loadData"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          v-if="scope.row.acctType !== '88'"
          slot="reference"
          type="text"
          size="small"
          btn-text="明细"
          permission-key=""
          @click="onDetailClick(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <transfer-dialog v-model="showTransfreDialog" @change="onTransferEvent" />
  </div>
</template>
<script>
  import TransferDialog from './components/transferDialog';
  import dynamictable from '@/components/dynamic-table';
  import { queryXibAccountInfo } from '@/api/xiamen';

  export default {
    name: 'BalanceManager',
    components: {
      TransferDialog,
      dynamictable,
    },
    data() {
      const columns = [
        {
          prop: 'acctTypeDesc',
          label: '子账户类型',
        },
        {
          prop: 'acctAmount',
          label: '余额',
        },
        {
          prop: 'cashBal',
          label: '现金余额',
        },
        {
          prop: 'frzCash',
          label: '冻结现金',
        },
        {
          prop: 'floatBal',
          label: '在途资金',
        },
        {
          prop: 'frzFloat',
          label: '冻结在途资金',
        },

        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          minWidth: '100',
          maxWidth: '300',
          scopedSlots: { customRender: 'operation' },
        },
      ];
      return {
        showTransfreDialog: false, // 显示转账dialog
        acctTypes: [],
        typeList: [
          { code: '01', label: '01-普通资金分户' },
          { code: '02', label: '02-交易手续费分户' },
          { code: '03', label: '03-提现手续费分户' },
          { code: '04', label: '04-营销费用分户' },
          { code: '05', label: '05-鉴权分户' },
          { code: '11', label: '11-待清算资金分户' },
          { code: '88', label: '88-经销商账户' },
          { code: '89', label: '89-供应商账户' },
        ],
        items: [],
        totalAmount: 0,
        columns,
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
      };
    },
    methods: {
      async loadData() {
        // 加载数据
        if (this.options.loading) {
          return;
        }
        this.items = [];

        if (this.acctTypes.length === 0) {
          this.$message.error('请至少选择一个子账号');
          return false;
        }
        this.options.loading = true;
        const acctTypeStr = this.acctTypes ? this.acctTypes.join(',') : '';
        const { res, err } = await queryXibAccountInfo({
          acctTypes: acctTypeStr,
        });

        this.options.loading = false;
        if (res && !err) {
          this.items = res.accountInfoList;
          this.totalAmount = res.totalAmount;
          this.pagination.total = res.total;
        }
      },
      resetData() {
        // 重置查询条件
        this.acctTypes = [];
        this.items = [];
      },
      onDetailClick(item) {
        //  跳转到详情
        // if (item.acctType === '88') {
        //   this.detailUrl = '/depository/query/paymentAccountQuery';
        // } else if (item.acctType === '89') {
        //   this.detailUrl = '/xiamen/providerInfo';
        // } else {
        //   this.detailUrl =
        //     '/recon/xib/xibAccTransRecord?accType=' + item.acctType;
        // }

        // this.detailUrl = '/xiamen/xibAccTransRecord?accType=' + item.acctType;
        // if (item.acctType === '89') {
        //   this.detailUrl = '/xiamen/memberInformationMgt';
        // }

        // this.$router.push(this.detailUrl);

        if (item.acctType === '89') {
          this.$router.push('memberInformationMgt');
          return;
        }
        this.$router.push({
          path: 'fundDetail',
          query: {
            thirdAccountId: item.supplierPlatCustNo,
            accType: item.acctType,
          },
        });
      },
      onTransferClick() {
        this.showTransfreDialog = true;
      },
      onTransferEvent() {
        // 转账成功，重新加载数据
        if (this.acctTypes.length !== 0) {
          this.loadData();
        }
      },
    },
  };
</script>
<style lang="scss" scoped></style>
