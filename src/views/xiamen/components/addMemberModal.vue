<template>
  <div>
    <el-dialog
      width="660px"
      title="供应商签约"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="saveForm"
        :rules="rulesForm"
        label-width="140px"
      >
        <el-form-item label="平台">
          <div>
            {{ saveForm.platform }}
          </div>
        </el-form-item>
        <el-form-item label="供应商名称" prop="supplierName">
          <el-input
            v-model="saveForm.supplierName"
            placeholder="公司全称，如杭州**文化**有限公司"
            style="width: 400px"
          ></el-input>
        </el-form-item>
        <el-form-item label="组织机构代码" prop="orgLicense">
          <el-input
            v-model="saveForm.orgLicense"
            placeholder="如91****01MA2A****70"
            style="width: 400px"
          ></el-input>
        </el-form-item>
        <el-form-item label="企业对公账号" prop="withdrawBankNo">
          <el-input
            v-model="saveForm.withdrawBankNo"
            placeholder="如364******420"
            style="width: 400px"
          ></el-input>
        </el-form-item>
        <el-form-item label="开户行名称" prop="withdrawBankName">
          <el-input
            v-model="saveForm.withdrawBankName"
            placeholder="如中国银行股份有限公司杭州市钱塘新区支行"
            style="width: 400px"
          ></el-input>
        </el-form-item>
        <el-form-item label="开户行编码" prop="bankCode">
          <el-input
            v-model="saveForm.bankCode"
            placeholder="请查询厦门国际银行银行编码文档，如中国银行：100004"
            style="width: 400px"
          ></el-input>
        </el-form-item>
        <el-form-item label="法人姓名" prop="legalName">
          <el-input
            v-model="saveForm.legalName"
            placeholder="如张三"
            style="width: 400px"
          ></el-input>
        </el-form-item>
        <el-form-item label="法人性别" prop="legalGender">
          <el-select
            v-model="saveForm.legalGender"
            placeholder="请选择"
            style="width: 400px"
          >
            <el-option
              v-for="item in sexArray"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="法人身份证号" prop="legalIdno">
          <el-input
            v-model="saveForm.legalIdno"
            placeholder="如350624********1010"
            style="width: 400px"
          ></el-input>
        </el-form-item>
        <el-form-item label="联系人手机号" prop="contactMobile">
          <el-input
            v-model="saveForm.contactMobile"
            placeholder="如137****6377"
            style="width: 400px"
          ></el-input>
        </el-form-item>
        <el-form-item label="联系人邮箱" prop="contactEmail">
          <el-input
            v-model="saveForm.contactEmail"
            placeholder="如*******************************"
            style="width: 400px"
          ></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showDialog = false">取 消</el-button>
        <el-button type="primary" @click="onOK">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { supplierSave, supplierApply } from '@/api/xiamen';
  import { debounce } from '@/utils';
  import { isPhone, isIdCard, isEmail } from '@/utils/validate';
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      currentRow: {
        type: Object,
        default: null,
      },
    },
    data() {
      const validatePhone = (rule, value, callback) => {
        if (!isPhone(value) && value) {
          callback(new Error('手机号格式错误'));
        } else {
          callback();
        }
      };
      const validateIdCard = (rule, value, callback) => {
        if (!isIdCard(value) && value) {
          callback(new Error('身份证号格式错误'));
        } else {
          callback();
        }
      };
      const validateEmail = (rule, value, callback) => {
        if (!isEmail(value) && value) {
          callback(new Error('邮箱格式错误'));
        } else {
          callback();
        }
      };
      return {
        rulesForm: {
          supplierName: [
            { required: true, message: '供应商名称不能为空', trigger: 'blur' },
          ],
          withdrawBankNo: [
            {
              required: true,
              message: '企业对公账号不能为空',
              trigger: 'blur',
            },
          ],
          withdrawBankName: [
            { required: true, message: '开户行不能为空', trigger: 'blur' },
          ],
          bankCode: [
            { required: true, message: '开户行编码不能为空', trigger: 'blur' },
          ],
          orgLicense: [
            {
              required: true,
              message: '组织机构代码不能为空',
              trigger: 'blur',
            },
          ],
          legalName: [
            { required: true, message: '法人姓名不能为空', trigger: 'blur' },
          ],
          legalGender: [
            { required: true, message: '法人性别不能为空', trigger: 'blur' },
          ],
          legalIdno: [
            {
              required: true,
              message: '法人身份证号不能为空',
              trigger: 'blur',
            },
            {
              validator: validateIdCard,
              trigger: 'blur',
            },
          ],
          contactMobile: [
            {
              required: true,
              message: '联系人手机号码不能为空',
              trigger: 'blur',
            },
            {
              validator: validatePhone,
              trigger: 'blur',
            },
          ],
          contactEmail: [
            { required: true, message: '联系人邮箱不能为空', trigger: 'blur' },
            {
              validator: validateEmail,
              trigger: 'blur',
            },
          ],
        },
        sexArray: [
          { value: 'M', label: '男' },
          { value: 'F', label: '女' },
        ],
        saveForm: {
          platform: 'VTN',
          id: null,
          supplierName: null,
          bankCode: '',
          withdrawBankNo: null,
          withdrawBankName: null,
          orgLicense: null,
          legalName: null,
          legalGender: null,
          legalIdno: null,
          contactMobile: null,
          contactEmail: null,
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(val) {
        if (val) {
          if (this.currentRow) {
            let form = {};
            Object.keys(this.saveForm).map(item => {
              form[item] = this.currentRow[item];
            });
            form.id = this.currentRow.id;
            this.saveForm = form;
          }
        } else {
          Object.assign(this.$data.saveForm, this.$options.data().saveForm);
          this.$nextTick(function () {
            this.$refs.formData.clearValidate();
          });
        }
      },
    },
    created() {},
    methods: {
      onOK: debounce(function () {
        this.$refs.formData.validate(async valid => {
          if (!valid) return;

          supplierApply({
            ...this.saveForm,
          }).then(({ res, err }) => {
            if (!err) {
              this.showDialog = false;
              this.$message({
                message: '供货商信息保存成功',
                type: 'success',
              });

              this.$emit('onGet');
              // this.$confirm('供货商信息保存成功, 继续去签约?', '提示', {
              //   confirmButtonText: '确定',
              //   cancelButtonText: '取消',
              //   type: 'warning',
              // })
              //   .then(() => {
              //     if (res) {
              //       window.open(res);
              //     }
              //   })
              //   .catch(() => {
              //     this.$message({
              //       type: 'info',
              //       message: '已取消供货商签约，可在列表中继续操作',
              //     });
              //     this.$emit('onGet');
              //   });
            }
          });
        });
      }, 1000),
    },
  };
</script>

<style></style>
