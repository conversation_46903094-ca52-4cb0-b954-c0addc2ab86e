<template>
  <el-dialog title="账户提现" :visible.sync="showDialog" width="40%">
    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      :rules="rules"
      label-position="right"
      label-width="150px"
      label-suffix=":"
    >
      <el-form-item label="出款账户类型" prop="outAccountType">
        <el-select
          v-model="form.outAccountType"
          placeholder="请选择"
          style="width: 80%"
          @change="outAccountTypeChange"
        >
          <el-option
            v-for="item in outAccountTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="出款账户" prop="outAccount">
        <el-select
          v-model="form.outAccount"
          placeholder="请选择"
          style="width: 80%"
          @change="outAccountChange"
        >
          <el-option
            v-for="item in outAccountList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="账户余额">
        <el-input v-model="form.cashBal" style="width: 80%" disabled />
      </el-form-item>
      <el-form-item label="提现金额" prop="amount">
        <div>
          <el-input-number
            v-model="form.amount"
            placeholder="请输入提现金额"
            style="width: 80%"
            :min="0"
            :precision="2"
            :controls="false"
          />
          <span style="margin-left: 10px">元</span>
        </div>
      </el-form-item>
      <el-form-item
        v-if="type === 'pinan'"
        label="提现手续费"
        prop="handlingFee"
      >
        <div>
          <el-input-number
            v-model="form.handlingFee"
            placeholder="请输入提现手续费"
            style="width: 80%"
            :min="0"
            :precision="2"
            :controls="false"
          />
          <span style="margin-left: 10px">元</span>
        </div>
      </el-form-item>
      <el-form-item label="收款账户" prop="inAccount">
        <el-select
          v-model="form.inAccount"
          style="width: 80%"
          placeholder="请选择"
        >
          <el-option
            v-for="item in inAccountList"
            :key="item.value"
            :label="item.bankNo"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          style="width: 80%"
          :rows="3"
          :maxlength="255"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="showDialog = false">取 消</el-button>
      <el-button
        type="primary"
        :disabled="confirmDisable"
        :loading="saveLoading"
        @click="handleConfirmClick"
      >
        保 存
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
  import { debounce } from '@/utils';
  import {
    accountOperateInfo,
    submitWithdraw,
    supplierQueryListForSelect,
  } from '@/api/xiamen';
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      type: {
        type: String,
        default: 'xiamen',
      },
      supplier: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      const validateAmount = (rule, value, callback) => {
        if (0 >= value || value > this.form.cashBal) {
          callback(new Error('提现金额需大于0并且小于账户余额'));
        } else {
          callback();
        }
      };

      return {
        loading: false,
        saveLoading: false,
        form: {
          outAccountType: null,
          outAccount: null,
          inAccount: null,
          remark: null,
          amount: null,
          cashBal: null, // 账户余额
        },

        rules: {
          outAccountType: [
            {
              required: true,
              message: '请选择出款账户类型',
              trigger: 'change',
            },
          ],
          outAccount: [
            { required: true, message: '请选择出款账户', trigger: 'change' },
          ],
          inAccount: [
            { required: true, message: '请选择入款账户', trigger: 'change' },
          ],
          amount: [
            { required: true, message: '请输入提现金额', trigger: 'blur' },
            { validator: validateAmount, trigger: 'blur' },
          ],
          handlingFee: [
            { required: true, message: '请输入提现手续费', trigger: 'blur' },
          ],
        },
        outAccountTypeList: [
          { value: 1, label: '平台子账户' },
          { value: 2, label: '会员子账户' },
        ],
        outAccountList: [],
        inAccountList: [],
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
      confirmDisable() {
        return false;
      },
    },
    watch: {
      show(value) {
        if (value) {
          let params = {};
          // 对公账户，平台01直接提现
          if (this.supplier.bePublicAccount) {
            this.form.outAccountType = 1;
            this.outAccountList = [{ value: '01', label: '01-普通资金分户' }];
            this.form.outAccount = '01';
            params = {
              accountType: this.form.outAccount,
            };
          } else {
            this.form.outAccountType = 2;
            this.outAccountList = [
              {
                value: this.supplier.id,
                label: this.supplier.supplierName,
              },
            ];
            this.form.outAccount = this.supplier.id;
            params = {
              supplierId: this.supplier.id,
              accountType: '12',
            };
          }
          this.inAccountList = [
            {
              value: this.supplier.id,
              label: this.supplier.withdrawBankName,
              bankNo: this.supplier.withdrawBankNo,
            },
          ];
          this.form.inAccount = this.supplier.id;
          this.setCashBal(params);
        } else {
          Object.assign(this.$data.form, this.$options.data().form);
          this.$nextTick(function () {
            this.$refs.form.clearValidate();
          });
        }
      },
    },
    mounted() {
      this.loadData();
    },
    methods: {
      loadData() {
        // 获取账户余额
        this.loading = true;
        this.form.cashBal = 0;
        this.loading = false;
      },
      saveData() {
        this.saveLoading = true;
        let params = {};
        // 表示提现出款账户类型为平台子账户
        if (this.form.outAccountType === 1) {
          params = {
            outAccount: this.form.outAccount,
            amount: this.form.amount,
            remark: this.form.remark,
            inSupplierIdForQueryBankNo: this.form.inAccount,
          };
        } else {
          params = {
            outSupplierIdForQueryPlatCustNo: this.form.outAccount,
            amount: this.form.amount,
            remark: this.form.remark,
            inSupplierIdForQueryBankNo: this.form.inAccount,
          };
        }

        submitWithdraw(params).then(({ res, err }) => {
          if (!err) {
            this.showDialog = false;
            this.$message({
              message: '提现审核提交成功',
              type: 'success',
            });
            this.$emit('onGet');
          }
          this.saveLoading = false;
        });
      },
      setCashBal(params) {
        //设置余额
        this.loading = true;
        accountOperateInfo(params).then(({ res, err }) => {
          if (res && !err) {
            this.form.cashBal = res.cashBal;
          }
          this.loading = false;
        });
      },
      setInAccount(params) {
        supplierQueryListForSelect(params).then(({ res, err }) => {
          if (res && !err) {
            if (res.length > 0) {
              this.inAccountList = [];
              this.form.inAccount = null;
              const list = res || [];
              this.inAccountList = list.map(item => {
                return {
                  value: item.id,
                  label: item.withdrawBankName,
                  bankNo: item.withdrawBankNo,
                };
              });

              this.form.inAccount = this.inAccountList[0].value;
            }
          }
        });
      },
      handleConfirmClick: debounce(function () {
        this.$refs.form.validate(async valid => {
          if (!valid) return;
          this.saveData();
        });
      }, 800),

      outAccountTypeChange() {
        this.form.outAccount = null;
        this.form.inAccount = null;
        this.outAccountList = [];
        this.inAccountList = [];
        // 表示出款账户类型为平台子账户
        if (this.form.outAccountType === 1) {
          this.outAccountList = [{ value: '01', label: '01-普通资金分户' }];
          this.form.outAccount = '01';
          let params = {
            accountType: this.form.outAccount,
          };
          this.setCashBal(params);
          params = {
            bePublicAccount: true,
            hasSigned: true,
          };
          this.setInAccount(params);
          return;
        }
        if (this.form.outAccountType === 2) {
          let params = {
            bePublicAccount: false,
            hasSigned: true,
          };
          supplierQueryListForSelect(params).then(({ res, err }) => {
            if (res && !err) {
              if (res.length > 0) {
                this.outAccountList = [];
                this.form.inAccount = null;
                const list = res || [];
                this.outAccountList = list.map(item => {
                  return {
                    value: item.id,
                    label: item.supplierName,
                  };
                });
              }
            }
          });
        }
      },
      outAccountChange() {
        this.form.inAccount = null;
        this.inAccountList = [];
        // 表示出款账户类型为平台子账户
        if (this.form.outAccountType === 1) {
          let params = {
            accountType: this.form.outAccount,
          };
          this.setCashBal(params);
          params = {
            bePublicAccount: true,
            hasSigned: true,
          };
          this.setInAccount(params);
        }
        if (this.form.outAccountType === 2) {
          let params = {
            supplierId: this.form.outAccount,
            accountType: '12',
          };
          this.setCashBal(params);
          params = {
            id: this.form.outAccount,
            bePublicAccount: false,
            hasSigned: true,
          };
          this.setInAccount(params);
        }
      },
    },
  };
</script>
<style lang="less" scoped></style>
