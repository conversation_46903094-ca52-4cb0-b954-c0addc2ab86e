<!--
 * @Author: 七七
 * @Date: 2022-04-18 00:18:40
 * @LastEditors: xuxiang
 * @LastEditTime: 2022-04-21 09:47:17
 * @FilePath: /access-fmis-web/src/views/xiamen/components/dailyBalanceDetails.vue
-->
<template>
  <div>
    <el-form inline>
      <el-form-item label="余额时间:">
        <el-date-picker
          v-model="balanceData"
          type="daterange"
          :clearable="false"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button @click="onReset">重置</el-button>
        <el-button @click="onExport">导出</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import { parseTime, setInitData, downloadFile, debounce } from '@/utils';
  import { listDailyBalanceDetail } from '@/api/xiamen';
  import { exportExcel } from '@/api/blob';
  export default {
    components: { dynamictable },
    data() {
      return {
        balanceData: '',
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          index: true,
          indexName: '编号',
        },
        columns: [
          {
            prop: 'cashBal',
            label: '现金可用余额',
          },
          {
            prop: 'cashFrozenBal',
            label: '现金冻结金额',
          },
          {
            prop: 'transitBal',
            label: '在途可用余额',
          },
          {
            prop: 'transitFrozenBal',
            label: '在途冻结余额',
          },
          {
            prop: 'dataVersion',
            label: '日切时间',
          },
        ],
      };
    },
    created() {
      this.balanceData = setInitData(30);
      this.getList(true);
    },
    methods: {
      getParams() {
        const { thirdAccountId, accType } = this.$route.query;
        const { balanceData } = this;
        const body = {
          startTime: balanceData
            ? parseTime(balanceData[0], '{y}-{m}-{d}')
            : '',
          endTime: balanceData ? parseTime(balanceData[1], '{y}-{m}-{d}') : '',
          thirdAccountId,
          accountType: accType || 12,
          pageNo: this.pagination.pageSize,
          pageSize: this.pagination.pageLimit,
        };
        return body;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        this.options.loading = true;
        const body = this.getParams();
        const { res, err } = await listDailyBalanceDetail(body);
        this.options.loading = false;
        if (res && !err) {
          this.list = res ? res.records : [];
          this.pagination.total = res.total ? res.total : 0;
        }
      },
      onExport: debounce(function () {
        const params = this.getParams();
        exportExcel(
          params,
          '/api/account-core/fund/account/exportDailyBalance',
          'get',
        ).then(res => {
          downloadFile(res.data, '日切余额明细');
        });
      }, 3000),
      onReset() {
        this.balanceData = setInitData(30);
      },
    },
  };
</script>
<style scoped></style>
