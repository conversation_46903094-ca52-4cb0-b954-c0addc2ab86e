<!--
 * @Author: 七七
 * @Date: 2022-04-18 00:18:40
 * @LastEditors: dddd
 * @LastEditTime: 2022-11-18 16:28:41
 * @FilePath: /fmis/src/views/xiamen/components/bankDetails.vue
-->
<template>
  <div>
    <el-form inline>
      <el-form-item label="交易流水号:">
        <el-input
          v-model="searchParams.transId"
          placeholder="请输入交易流水号"
        ></el-input>
      </el-form-item>
      <el-form-item label="资金方向:">
        <el-select
          v-model="searchParams.transType"
          clearable
          placeholder="请选择资金方向"
        >
          <el-option label="支出" :value="1"></el-option>
          <el-option label="收入" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="交易类型:">
        <el-select
          v-model="searchParams.businessType"
          clearable
          placeholder="请选择交易类型"
        >
          <el-option label="预付款充值(线上)" :value="10"></el-option>
          <el-option label="预付款充值(线下)" :value="11"></el-option>
          <el-option label="交易收入" :value="12"></el-option>
          <el-option label="清算出款" :value="13"></el-option>
          <el-option label="预付款退款" :value="14"></el-option>
          <el-option label="订单分账" :value="15"></el-option>
          <el-option label="原路退款" :value="16"></el-option>
          <el-option label="其他" :value="99"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="交易时间:">
        <el-date-picker
          v-model="transactionData"
          type="daterange"
          :clearable="false"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :picker-options="pickerOptions"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button @click="onReset">重置</el-button>
        <el-button @click="onExport">导出</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import { parseTime, setInitData, downloadFile, debounce } from '@/utils';
  import { accTransList } from '@/api/xiamen';
  import { exportExcel } from '@/api/blob';
  export default {
    components: { dynamictable },
    data() {
      return {
        transactionData: '',
        list: [],
        searchParams: {
          transId: '',
          startDate: '',
          endDate: '',
          platCustNo: '',
          accType: '',
          businessType: '',
          transType: '',
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          index: true,
          indexName: '编号',
        },
        choiceDate0: '',
        pickerOptions: {
          onPick: ({ maxDate, minDate }) => {
            this.choiceDate0 = minDate.getTime();
            if (maxDate) {
              this.choiceDate0 = '';
            }
          },
          disabledDate: time => {
            if (this.choiceDate0 !== '') {
              const one = 2 * 24 * 3600 * 1000;
              const minTime = this.choiceDate0 - one;
              const maxTime = this.choiceDate0 + one;
              return time.getTime() < minTime || time.getTime() > maxTime;
            }
          },
        },
        columns: [
          {
            prop: 'transId',
            label: '交易流水',
          },
          {
            prop: 'transType',
            label: '资金方向',
            render: ({ transType }) => (
              <span>
                {transType == 1 ? '支出' : transType == 2 ? '收入' : ''}
              </span>
            ),
          },
          {
            prop: 'transAmt',
            label: '交易金额',
          },
          {
            prop: 'bal',
            label: '账户余额',
          },
          {
            prop: 'businessName',
            label: '交易类型',
          },
          {
            prop: 'peerAcct',
            label: '对方账号（卡号/客编）',
          },
          {
            prop: 'peerAcctNm',
            label: '对方账户名',
          },
          // {
          //   prop: 'signData',
          //   label: '签名数据',
          // },
          {
            prop: 'transTime',
            label: '交易时间',
          },
          // {
          //   prop: 'createTime',
          //   label: '创建时间',
          // },
        ],
      };
    },
    created() {
      this.transactionData = setInitData(2);
      this.getList(true);
    },
    methods: {
      getParams() {
        const { thirdAccountId, accType } = this.$route.query;
        const { transactionData } = this;
        const body = {
          ...this.searchParams,
          accType: accType || 12,
          platCustNo: thirdAccountId,
          startDate: transactionData
            ? parseTime(transactionData[0], '{y}-{m}-{d}')
            : '',
          endDate: transactionData
            ? parseTime(transactionData[1], '{y}-{m}-{d}')
            : '',
          pageNo: this.pagination.pageSize,
          pageSize: this.pagination.pageLimit,
        };
        return body;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        this.options.loading = true;
        const body = this.getParams();

        const { res, err } = await accTransList(body);
        this.options.loading = false;
        if (res && !err) {
          this.list = res ? res.records : [];
          this.pagination.total = res.total ? res.total : 0;
        }
      },

      onExport: debounce(function () {
        const params = this.getParams();
        exportExcel(
          params,
          '/api/account-core/fund/account/acc/trans/export',
          'post',
        ).then(res => {
          downloadFile(res.data, '银行流水明细');
        });
      }, 3000),
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.transactionData = setInitData(2);
      },
    },
  };
</script>
<style scoped></style>
