<template>
  <div>
    <el-dialog
      width="500px"
      :title="showTitle ? title : ''"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="saveForm"
        :rules="rulesForm"
        label-width="60px"
      >
        <el-form-item prop="remark">
          <p>确认{{ title }}？</p>
          <el-input
            v-model="saveForm.remark"
            placeholder="请输入备注"
            type="textarea"
            style="width: 80%"
          ></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="onOK('refuse')">拒 绝</el-button>
        <el-button type="primary" @click="onOK('pass')">通 过</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { debounce } from '@/utils';

  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      showTitle: {
        type: Boolean,
        default: true,
      },
      title: {
        type: String,
        default: '',
      },
      currentRow: {
        type: Object,
        default: null,
      },
    },
    data() {
      return {
        rulesForm: {
          accNo: [
            {
              required: true,
              message: '请输入备注',
              trigger: 'blur',
            },
          ],
        },
        saveForm: {
          remark: '',
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(val) {
        if (val) {
        } else {
          Object.assign(this.$data.saveForm, this.$options.data().saveForm);
          this.$nextTick(function () {
            this.$refs.formData.clearValidate();
          });
        }
      },
    },
    created() {},
    methods: {
      onOK: debounce(function (type) {
        this.$emit('onClose', type, this.saveForm);
      }, 1000),
    },
  };
</script>

<style></style>
