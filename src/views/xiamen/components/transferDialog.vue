<template>
  <el-dialog
    title="子账户转账"
    :visible.sync="showDialog"
    width="40%"
    class="wd-dialog"
  >
    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      :rules="rules"
      label-position="right"
      label-width="150px"
      label-suffix=":"
      class="wd-form"
    >
      <el-form-item label="出款账户类型" prop="outAccountType">
        <el-select
          v-model="form.outAccountType"
          placeholder="请选择"
          class="wd-width-100"
          @change="outAccountTypeChange"
        >
          <el-option
            v-for="item in outAccountTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="出款账户" prop="outAccount">
        <el-select
          v-model="form.outAccount"
          placeholder="请选择"
          class="wd-width-100"
          @change="outAccountChange"
        >
          <el-option
            v-for="item in outAccountList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="账户余额">
        <el-input v-model="form.cashBal" disabled />
      </el-form-item>
      <el-form-item label="收款账户类型" prop="inAccountType">
        <el-select
          v-model="form.inAccountType"
          placeholder="请选择"
          class="wd-width-100"
          @change="inAccountTypeChange"
        >
          <el-option
            v-for="item in inAccountTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="收款款账户" prop="inAccount">
        <el-select
          v-model="form.inAccount"
          placeholder="请选择"
          class="wd-width-100"
        >
          <el-option
            v-for="item in inAccountList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="转出金额" prop="amount">
        <div class="wd-outer">
          <el-input-number
            v-model="form.amount"
            placeholder="请输入转出金额"
            style="width: 100%"
            :min="0"
            :precision="2"
            :controls="false"
          />
          <div>元</div>
        </div>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          :maxlength="255"
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="showDialog = false">取消</el-button>
      <el-button
        type="primary"
        :disabled="confirmDisable"
        :loading="saveLoading"
        @click="handleConfirmClick"
      >
        确定
      </el-button>
    </span>
  </el-dialog>
</template>
<script>
  import {
    accountOperateInfo,
    submitTransfer,
    supplierQueryListForSelect,
  } from '@/api/xiamen';
  import { debounce } from '@/utils';

  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        loading: false,
        saveLoading: false,
        form: {
          outAccountType: null,
          outAccount: null,
          amount: null,
          remark: null,
          inAccountType: null,
          inAccount: null,
          cashBal: '', // 账户余额
        },
        rules: {
          outAccountType: [
            {
              required: true,
              message: '请选择出款账户类型',
              trigger: 'change',
            },
          ],
          outAccount: [
            { required: true, message: '请选择出款账户', trigger: 'change' },
          ],
          amount: [
            { required: true, message: '请输入转出金额', trigger: 'blur' },
          ],
          inAccountType: [
            {
              required: true,
              message: '请选择收款账户类型',
              trigger: 'change',
            },
          ],
          inAccount: [
            { required: true, message: '请输入收款账户', trigger: 'blur' },
          ],
        },
        outAccountTypeList: [{ value: 1, label: '平台子账户' }],
        inAccountTypeList: [
          { value: 1, label: '平台子账户' },
          { value: 2, label: '会员子账户' },
        ],
        outAccountList: [],
        inAccountList: [],
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
      confirmDisable() {
        return false;
      },
    },
    watch: {
      show(value) {
        if (value) {
        } else {
          Object.assign(this.$data.form, this.$options.data().form);
          this.$nextTick(function () {
            this.$refs.form.clearValidate();
          });
        }
      },
    },
    mounted() {
      this.loadData();
    },
    methods: {
      loadData() {
        // 获取账户余额
        this.loading = true;

        this.form.cashBal = 0;
        this.loading = false;
      },
      saveData() {
        if (this.form.amount < 0.01) {
          this.$message.warning('转账金额小于0.01');
          return;
        }
        if (this.form.amount > this.form.cashBal) {
          this.$message.warning('转账金额大于余额');
          return;
        }
        this.saveLoading = true;
        // 平台互转
        let params = {};
        if (this.form.outAccountType === 1 && this.form.inAccountType === 1) {
          params = {
            outAccount: this.form.outAccount,
            inAccount: this.form.inAccount,
            amount: this.form.amount,
            transferFlowType: 0, //转账方式，0平台账户互转，1平台转会员
            remark: this.form.remark,
          };
        }
        if (this.form.outAccountType === 1 && this.form.inAccountType === 2) {
          params = {
            outAccount: this.form.outAccount,
            inSupplierIdForQueryPlatCustNo: this.form.inAccount,
            amount: this.form.amount,
            transferFlowType: 1, //转账方式，0平台账户互转，1平台转会员
            remark: this.form.remark,
          };
        }
        submitTransfer(params).then(({ res, err }) => {
          if (!err) {
            this.showDialog = false;
            this.$message({
              message: '转账审核提交成功',
              type: 'success',
            });
            // this.$emit('onGet');
          }
          this.saveLoading = false;
        });
      },
      setCashBal(params) {
        this.loading = true;
        accountOperateInfo(params).then(({ res, err }) => {
          if (res && !err) {
            this.form.cashBal = res.cashBal;
          }
          this.loading = false;
        });
      },
      outAccountTypeChange() {
        this.form.outAccount = null;
        this.outAccountList = [];
        this.form.inAccountType = null;
        this.inAccountTypeList = [];
        this.form.inAccount = null;
        this.inAccountList = [];
        // 表示出款账户类型为平台子账户
        if (this.form.outAccountType === 1) {
          this.outAccountList = [
            { value: '01', label: '01-普通资金分户' },
            { value: '04', label: '04-营销费用分户' },
          ];
          return;
        }
      },
      outAccountChange() {
        this.form.inAccountType = null;
        this.inAccountTypeList = [];
        this.form.inAccount = null;
        this.inAccountList = [];
        let param = {
          accountType: this.form.outAccount,
        };
        this.setCashBal(param);
        // 表示出款账户类型为平台子账户
        if (this.form.outAccountType === 1 && this.form.outAccount === '01') {
          this.inAccountTypeList = [{ value: 1, label: '平台子账户' }];
          this.form.inAccountType = 1;
          this.inAccountList = [{ value: '04', label: '04-营销费用分户' }];
          this.form.inAccount = '04';
          return;
        }
        if (this.form.outAccountType === 1 && this.form.outAccount === '04') {
          this.inAccountTypeList = [
            { value: 1, label: '平台子账户' },
            { value: 2, label: '会员子账户' },
          ];
          return;
        }
      },
      inAccountTypeChange() {
        this.form.inAccount = null;
        this.inAccountList = [];
        if (this.form.outAccountType === 1 && this.form.outAccount === '01') {
          if (this.form.inAccountType === 1) {
            this.inAccountList = [{ value: '04', label: '04-营销费用分户' }];
            this.form.inAccount = '04';
            return;
          }
        }
        if (this.form.outAccountType === 1 && this.form.outAccount === '04') {
          if (this.form.inAccountType === 1) {
            this.inAccountList = [{ value: '01', label: '01-普通资金分户' }];
            this.form.inAccount = '01';
            return;
          }
          if (this.form.inAccountType === 2) {
            let params = {
              bePublicAccount: false,
              hasSigned: true,
            };
            supplierQueryListForSelect(params).then(({ res, err }) => {
              if (res && !err) {
                if (res.length > 0) {
                  this.inAccountList = [];
                  this.form.inAccount = null;
                  for (let n = 0; n < res.length; n++) {
                    this.inAccountList.push({
                      value: res[n].id,
                      label: res[n].supplierName,
                    });
                  }
                }
              }
            });
          }
        }
      },
      handleConfirmClick: debounce(function () {
        this.$refs.form.validate(async valid => {
          if (!valid) return;
          this.saveData();
        });
      }, 800),
    },
  };
</script>

<style lang="scss" scoped>
  .wd-dialog /deep/ .el-input-number .el-input__inner {
    text-align: left;
  }
  .wd-form {
    padding-right: 80px;
  }
  .wd-width-100 {
    width: 100%;
  }
  .wd-outer {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
  }
</style>
