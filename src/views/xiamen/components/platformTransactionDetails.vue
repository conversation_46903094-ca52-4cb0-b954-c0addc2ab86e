<!--
 * @Author: 七七
 * @Date: 2022-04-18 00:18:40
 * @LastEditors: xuxiang
 * @LastEditTime: 2022-04-29 18:17:16
 * @FilePath: /access-fmis-web/src/views/xiamen/components/dailyBalanceDetails.vue
-->
<template>
  <div>
    <el-form inline>
      <el-form-item label="交易流水号:">
        <el-input
          v-model="searchParams.serialNo"
          placeholder="请输入交易流水号"
        ></el-input>
      </el-form-item>

      <el-form-item label="交易类型:">
        <el-select
          v-model="searchParams.tradeType"
          clearable
          placeholder="请选择交易类型"
        >
          <el-option label="转出" :value="1"></el-option>
          <el-option label="转入" :value="2"></el-option>
          <el-option label="提现" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="业务类型:">
        <el-select
          v-model="searchParams.bizType"
          clearable
          placeholder="请选择交易类型"
        >
          <el-option
            v-for="item in bizType"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间:">
        <el-date-picker
          v-model="transactionData"
          type="daterange"
          :clearable="false"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';
  import { parseTime, setInitData } from '@/utils';
  import { listPlatformFundFlow } from '@/api/xiamen';

  export default {
    components: { dynamictable },
    props: {
      bizType: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        transactionData: '',
        list: [],
        searchParams: {
          serialNo: '',
          startTime: '',
          endTime: '',
          platCustNo: '',
          accountNo: '',
          tradeType: '',
          bizType: '',
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          index: true,
          indexName: '编号',
        },
        columns: [
          {
            prop: 'serialNo',
            label: '交易流水',
          },
          {
            prop: 'createTime',
            label: '申请时间',
          },
          {
            prop: 'entityType',
            label: '账户类型',
          },
          {
            prop: 'accountName',
            label: '账户名',
          },
          {
            prop: 'accountNo',
            label: '账户号',
          },
          {
            prop: 'tradeType',
            label: '交易类型',
          },
          {
            prop: 'bizType',
            label: '业务类型',
          },
          {
            prop: 'toSupplierName',
            label: '对方主体名称',
          },
          {
            prop: 'toEntityType',
            label: '对方账户类型',
          },
          {
            prop: 'toAccountName',
            label: '对方账户名',
          },

          {
            prop: 'toAccountNo',
            label: '对方账户号',
          },
          {
            prop: 'amount',
            label: '金额',
          },
          {
            prop: 'remark',
            label: '备注',
          },
          // {
          //   prop: 'transStatusDesc',
          //   label: '状态',
          // },
        ],
      };
    },
    created() {
      this.transactionData = setInitData(30);
      this.getList(true);
    },
    methods: {
      getParams() {
        const { thirdAccountId, accType } = this.$route.query;
        const { transactionData } = this;
        const body = {
          ...this.searchParams,
          accountNo: accType || 12,
          platCustNo: thirdAccountId,
          startTime: transactionData
            ? parseTime(transactionData[0], '{y}-{m}-{d}')
            : '',
          endTime: transactionData
            ? parseTime(transactionData[1], '{y}-{m}-{d}')
            : '',
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return body;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        this.options.loading = true;
        const body = this.getParams();

        const { res, err } = await listPlatformFundFlow(body);
        this.options.loading = false;
        if (res && !err) {
          this.list = res ? res.records : [];
          this.pagination.total = res.total ? res.total : 0;
        }
      },

      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.transactionData = setInitData(30);
      },
    },
  };
</script>
<style scoped></style>
