<template>
  <div>
    <dynamictable
      :data-source="logList"
      :columns="logColumns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    />
  </div>
</template>

<script>
  import { bankCardLogSearch } from '@/api/xiamen';
  import { setInitData } from '@/utils';
  import dynamictable from '@/components/dynamic-table';
  export default {
    components: {
      dynamictable,
    },
    data() {
      return {
        logList: [],
        options: {
          loading: false,
          border: true,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        logColumns: [
          {
            prop: 'operatorId',
            label: '操作人ID',
          },
          {
            prop: 'operatorName',
            label: '操作人姓名',
          },
          {
            prop: 'optAction',
            label: '操作动作',
          },
          {
            prop: 'optTime',
            label: '操作时间',
          },
        ],
      };
    },
    created() {
      this.getList();
    },

    methods: {
      async getList() {
        const { pageSize, pageLimit } = this.pagination;
        const date = setInitData(30);
        const body = {
          fromDate: date[0],
          endDate: date[1],
          pageNo: pageSize,
          pageSize: pageLimit,
          // userId: this.$store.state.user.userInfo.id,
        };
        this.options.loading = true;
        const { res, err } = await bankCardLogSearch(body);
        this.options.loading = false;
        if (res && !err) {
          this.logList = res ? res.records : [];
          this.pagination.total = res ? res.total : [];
        }
      },
    },
  };
</script>

<style lang="scss" scoped></style>
