<template>
  <div>
    <el-form inline>
      <el-form-item label="申请时间:">
        <el-date-picker
          v-model="applicationDate"
          type="daterange"
          :clearable="false"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="交易流水号:">
        <el-input
          v-model="searchParams.transId"
          placeholder="请输入交易流水号"
        ></el-input>
      </el-form-item>
      <el-form-item label="分户类型:">
        <el-select
          v-model="searchParams.accType"
          clearable
          placeholder="请选择转出账户类型"
        >
          <el-option
            v-for="item in listAccType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="客户编号:">
        <el-input
          v-model="searchParams.platCustNo"
          placeholder="请输入客户编号"
        ></el-input>
      </el-form-item>

      <el-form-item label="交易类型:">
        <el-select
          v-model="searchParams.businessType"
          clearable
          placeholder="请选择交易类型"
        >
          <el-option
            v-for="item in listBusinessType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button type="primary" @click="onExport">导出</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';

  import { debounce, parseTime, downloadFile } from '@/utils';
  import { transList, transExport } from '@/api/xiamen';

  export default {
    components: {
      dynamictable,
    },
    data() {
      let columns = [
        {
          prop: 'id',
          label: '序号',
        },
        {
          prop: 'transId',
          label: '交易流水号',
        },
        {
          prop: 'transType',
          label: '资金方向',
        },
        {
          prop: 'transType',
          label: '交易金额',
          render: props => (
            <span>
              {props.transType === '1' ? '-' + props.transAmt : props.transAmt}
            </span>
          ),
        },
        {
          prop: 'bal',
          label: '余额',
        },
        {
          prop: 'transTime',
          label: '交易日期',
        },
        {
          prop: 'remark',
          label: '备注',
        },
        {
          prop: 'platCustNo',
          label: '客户编号',
        },

        {
          prop: 'peerAcct',
          label: '对手账号(卡号|客编)',
        },
        {
          prop: 'peerAcctNm',
          label: '对手账号名',
        },
        {
          prop: 'businessType',
          label: '交易类型',
        },
        {
          prop: 'businessName',
          label: '交易名',
        },
        {
          prop: 'signData',
          label: '签名数据',
        },
        {
          prop: 'accType',
          label: '账户类型',
        },
        {
          prop: 'createTime',
          label: '创建时间',
        },
      ];

      return {
        applicationDate: '',
        searchParams: {
          startDate: '',
          endDate: '',
          transId: '',
          accType: '',
          platCustNo: '',
          businessType: '',
        },
        list: [],
        listAccType: [
          { value: '01', label: '[01]普通资金分户' },
          { value: '02', label: '[02]交易手续费分户' },
          { value: '03', label: '[03]提现手续费分户' },
          { value: '04', label: '[04]营销费用分户' },
          { value: '05', label: '[05]转账验证子账户' },
          { value: '11', label: '[11]待清算资金分户' },
          { value: '12', label: '[12]预付货款分户' },
        ],

        listBusinessType: [
          { value: '10', label: '[10]预付款充值(线上)' },
          { value: '11', label: '[11]预付款充值(线下)' },
          { value: '12', label: '[12]交易收入' },
          { value: '13', label: '[13]清算出款' },
          { value: '14', label: '[14]预付款退款' },
          { value: '15', label: '[15]订单分账' },
          { value: '16', label: '[16]原路退款' },
          { value: '99', label: '[99]其他' },
        ],

        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
        columns,
      };
    },
    created() {
      this.applicationDate = this.initDate();
      const accType = this.$route.query.accType;
      if (accType && !['88', '89'].includes(accType))
        this.searchParams.accType = accType;

      this.getList(true);
    },
    methods: {
      initDate() {
        let now = new Date(); //当前日期
        let nowMonth = now.getMonth(); //当前月
        let nowYear = now.getFullYear(); //当前年
        const applyStartTime = parseTime(
          new Date(nowYear, nowMonth, 1),
          '{y}-{m}-{d}',
        );
        const applyEndTime = parseTime(now, '{y}-{m}-{d}');
        return [applyStartTime, applyEndTime];
      },

      onExport: debounce(function () {
        const params = this.getParams();
        transExport(params).then(res => {
          if (!res.err) {
            this.$message.success(
              '导出任务已经创建，稍后会发送到您的飞书上，请注意查收',
            );
          }
        });
      }, 1000),

      getParams() {
        const applicationDate = this.applicationDate;
        this.searchParams.startDate = applicationDate
          ? parseTime(applicationDate[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.endDate = applicationDate
          ? parseTime(applicationDate[1], '{y}-{m}-{d}')
          : '';
        const params = {
          ...this.searchParams,
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const { res, err } = await transList(params);
        this.options.loading = false;
        if (res && !err) {
          this.list = res ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.applicationDate = this.initDate();
        this.getList(true);
      },
    },
  };
</script>
<style lang="scss"></style>
