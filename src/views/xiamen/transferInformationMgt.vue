<template>
  <div>
    <el-form inline>
      <el-form-item label="贸易主体:">
        <el-input
          v-model="searchParams.payerSupplierName"
          placeholder="请输入贸易主体"
        />
      </el-form-item>
      <el-form-item label="收款主体:">
        <el-input
          v-model="searchParams.payeeSupplierName"
          placeholder="请输入收款主体"
        />
      </el-form-item>
      <el-form-item label="状态:">
        <el-select
          v-model="searchParams.payStatus"
          clearable
          placeholder="请选择状态"
        >
          <!-- <el-option label="已创建" :value="0"></el-option> -->
          <el-option label="待支付" :value="1"></el-option>
          <el-option label="支付成功" :value="2"></el-option>
          <el-option label="支付失败" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          v-if="[0, 1].includes(scope.row.payStatus)"
          slot="reference"
          type="text"
          size="small"
          btn-text="支付"
          permission-key=""
          @click="handlePay(scope.row)"
        ></ac-permission-button>
        <a v-else>-</a>
      </template>
    </dynamictable>
  </div>
</template>
<script>
  import dynamictable from '@/components/dynamic-table';

  import { debounce } from '@/utils';
  import { specialOrderList, specialOrderPay } from '@/api/xiamen';

  export default {
    components: {
      dynamictable,
    },

    data() {
      let columns = [
        {
          prop: 'orderId',
          label: 'ID',
        },
        {
          prop: 'summaryStartDate',
          label: '日期',
        },
        {
          prop: 'payerSupplierName',
          label: '贸易主体',
        },
        {
          prop: 'payerSupplierPlatCustNo',
          label: '打款账号',
        },
        {
          prop: 'payeeSupplierName',
          label: '收款主体',
        },
        {
          prop: 'payeeSupplierPlatCustNo',
          label: '收款账号',
        },
        {
          prop: 'amount',
          label: '交易总金额',
        },
        {
          prop: 'statusText',
          label: '状态',
          // render: ({ payStatus }) => (
          //   <span>
          //     {['已创建', '支付中', '支付成功', '支付失败'][payStatus]}
          //   </span>
          // ),
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: '200',
          scopedSlots: { customRender: 'operation' },
        },
      ];

      return {
        searchParams: {
          payeeSupplierName: '',
          payerSupplierName: '',
          payStatus: '',
        },
        list: [],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
        columns,
      };
    },
    created() {
      this.getList(true);
    },
    methods: {
      handlePay: debounce(function ({ id }) {
        specialOrderPay({
          id,
          operatorName: this.$store.state.user.username,
        }).then(({ res, err }) => {
          if (res && res.success) {
            this.$message.success('支付成功');
            if (res.data) {
              window.open(res.data);
            }
            this.getList();
          } else {
            this.$message.error(res.msg);
          }
        });
      }, 1000),
      getParams() {
        const params = {
          ...this.searchParams,
          pageNum: this.pagination.pageSize,
          pageSize: this.pagination.pageLimit,
        };
        return params;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        const { res, err } = await specialOrderList(params);
        if (res && !err) {
          this.options.loading = false;
          this.list = res.records;
          this.pagination.total = res.total;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.getList(true);
      },
    },
  };
</script>
<style lang="scss"></style>
