<template>
  <div>
    <el-form inline>
      <el-form-item label="分类:">
        <el-select v-model="form.parentId" clearable>
          <el-option
            v-for="item in categoryList"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态:">
        <el-select v-model="form.status" clearable>
          <el-option
            v-for="item in statusList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onGetList(true)">查询</el-button>
        <el-button type="success" @click="onAddContent(1)">新增分类</el-button>
        <el-button type="success" @click="onAddContent(2)">新增内容</el-button>
        <el-button type="danger" @click="onChangeAllStatus(0)">
          全部禁用
        </el-button>
        <el-button type="warning" @click="onChangeAllStatus(1)">
          全部启用
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      border
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column type="index" label="序号" width="50" />
      <el-table-column label="分类" prop="parentTitle" />
      <el-table-column label="内容" prop="title" />
      <el-table-column label="状态">
        <template slot-scope="{ row }">
          <span :style="`color: ${row.status ? '#000' : '#f00'}`">
            {{ row.status ? '启用中' : '禁用中' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="{ row }">
          <el-button
            :type="row.status ? 'danger' : 'primary'"
            @click="onChangeStatus(row)"
          >
            {{ row.status ? '禁用' : '启用' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="form.current"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="form.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
    <el-dialog :title="dialogTitle" :visible.sync="dialogShow" width="400px">
      <el-form :model="form" label-width="120">
        <el-form-item v-if="dialogShow === 2" label="分类名称：">
          <el-select v-model="dialogForm.title" clearable>
            <el-option
              v-for="item in categoryList"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="dialogShow === 2 ? '分类内容：' : '分类名称：'">
          <el-input
            v-for="(item, index) in dialogForm.content"
            :key="index"
            v-model="item.name"
            :maxlength="50"
            :style="`width: 215px; margin-top: 5px; margin-left:${
              index === 0 ? '0' : '82px'
            }`"
          >
            <el-button
              slot="append"
              :icon="index === 0 ? 'el-icon-circle-plus' : 'el-icon-delete'"
              @click="onAddInputItem(index)"
            ></el-button>
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogShow = false">取 消</el-button>
        <el-button
          :loading="btnLoading"
          :disabled="btnLoading"
          type="primary"
          @click="onConfirmAdd"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import {
    getCategoryList,
    addCategoryValue,
    addCategory,
    editCategory,
    getFirstCategoryList,
  } from '@/api/award';

  import { mapGetters } from 'vuex';
  export default {
    data() {
      return {
        form: {
          status: '', // 状态
          parentId: '', // 分类
          current: 1,
          size: 10,
        },
        tableData: [],
        selectedItems: [],
        total: 0,
        dialogShow: false,
        dialogTitle: '',
        dialogFormModal: {
          title: '',
          content: [{ name: '' }],
        },
        dialogForm: {},
        statusList: [
          {
            label: '启用',
            value: 1,
          },
          {
            label: '禁用',
            value: 0,
          },
        ],
        categoryList: [],
        btnLoading: false,
      };
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
      }),
    },
    created() {
      this.onGetList();
      this.onGetFirstCategoryList();
    },
    methods: {
      onGetList(boo = false) {
        if (boo) this.form.current = 1;
        getCategoryList(this.form).then(res => {
          this.tableData = res.records;
          this.total = res.total;
        });
      },
      onGetFirstCategoryList() {
        getFirstCategoryList({ status: 1, parentId: 0 }).then(res => {
          this.categoryList = res;
        });
      },

      handleCurrentChange(e) {
        this.form.current = e;
        this.onGetList();
      },
      handleSizeChange(e) {
        this.form.size = e;
        this.onGetList();
      },
      handleSelectionChange(e) {
        this.selectedItems = e;
      },
      onChangeAllStatus(type) {
        const permissionKey =
          type === 1 ? 'basicInfo-batch-enable' : 'basicInfo-batch-disable';
        this.$btnPermission(permissionKey).then(res => {
          if (this.selectedItems.length === 0) {
            this.$message.error('请选择至少一项操作');
            return;
          }
          const text = type ? '全部启用' : '全部禁用';
          this.$confirm(`该操作将会${text}, 是否继续?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(() => {
            const categoryList = this.selectedItems.map(i => {
              return {
                id: i.id,
              };
            });
            editCategory({
              categoryList,
              operate: this.username,
              status: type ? 1 : 0,
            }).then(() => {
              this.$message.success(`${text}成功`);
              this.onGetList();
            });
          });
        });
      },
      onChangeStatus(obj) {
        const permissionKey = obj.status
          ? 'basicInfo-disable'
          : 'basicInfo-enable';
        this.$btnPermission(permissionKey).then(res => {
          const text = obj.status ? '禁用' : '启用';
          this.$confirm(`该操作将会${text}, 是否继续?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(() => {
            const categoryList = [{ id: obj.id }];
            editCategory({
              categoryList,
              operate: this.username,
              status: obj.status ? 0 : 1,
            }).then(() => {
              this.$message.success(`${text}成功`);
              this.onGetList();
            });
          });
        });
      },
      onAddContent(type) {
        const permissionKey =
          type === 1 ? 'basicInfo-add-classification' : 'basicInfo-add-content';
        this.$btnPermission(permissionKey).then(res => {
          this.dialogTitle = type === 1 ? '新增分类' : '新增内容';
          this.dialogShow = true;
          this.dialogForm = JSON.parse(JSON.stringify(this.dialogFormModal));
        });
      },
      onConfirmAdd() {
        if (this.dialogShow === 1) {
          // 新增分类
          if (this.dialogForm.content.some(i => !i.name)) {
            this.$message.error('分类名称不能为空');
            return;
          }
          const categoryList = this.dialogForm.content.map(i => {
            return {
              title: i.name,
            };
          });
          this.btnLoading = true;
          addCategory({
            categoryList,
            operate: this.username,
          })
            .then(() => {
              this.$message.success('操作成功');
              this.dialogShow = false;
              this.dialogForm = JSON.parse(
                JSON.stringify(this.dialogFormModal),
              );
              this.onGetList();
              this.onGetFirstCategoryList();
            })
            .finally(() => {
              this.btnLoading = false;
            });
        } else {
          // 新增内容
          if (!this.dialogForm.title) {
            // 分类名称不能为空
            this.$message.error('类名称不能为空');
            return;
          }
          if (this.dialogForm.content.some(i => !i.name)) {
            this.$message.error('分类内容不能为空');
            return;
          }
          const categoryList = this.dialogForm.content.map(i => {
            return {
              title: i.name,
              parentId: this.dialogForm.title,
            };
          });
          this.btnLoading = true;
          addCategoryValue({
            categoryList,
            operate: this.username,
          })
            .then(() => {
              this.$message.success('操作成功');
              this.dialogShow = false;
              this.dialogForm = JSON.parse(
                JSON.stringify(this.dialogFormModal),
              );
              this.onGetList();
            })
            .finally(() => {
              this.btnLoading = false;
            });
        }
      },

      onAddInputItem(index) {
        if (index === 0) {
          if (this.dialogForm.content.length > 5) {
            this.$message.error('最多只能添加6条哦~');
            return;
            return;
          }
          this.dialogForm.content.push({ name: '' });
        } else {
          // 删除
          this.dialogForm.content.splice(index, 1);
        }
      },
    },
  };
</script>
