<template>
  <div>
    <el-form inline label-width="100px" class="apply-record">
      <el-form-item label="经办人:">
        <el-select v-model="form.agentId" clearable>
          <el-option
            v-for="item in agentList"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="用户ID:">
        <el-input v-model="form.idCode" placeholder="用户ID" :maxlength="20" />
      </el-form-item>
      <el-form-item label="申请单号:">
        <el-input
          v-model="form.applyNo"
          placeholder="申请单号"
          :maxlength="255"
        />
      </el-form-item>
      <el-form-item label="处理类型:">
        <el-select v-model="form.dealId" clearable>
          <el-option
            v-for="item in dealList"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态:">
        <el-select v-model="form.status" clearable>
          <el-option
            v-for="item in APPLY_ORDER_STATUS"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间:">
        <el-date-picker
          v-model="form.createTime"
          type="daterange"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="提交时间:">
        <el-date-picker
          v-model="form.pushTime"
          type="daterange"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="完成时间:">
        <el-date-picker
          v-model="form.finishTime"
          type="daterange"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="取消时间:">
        <el-date-picker
          v-model="form.cancelTime"
          type="daterange"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
      </el-form-item>
      <el-form-item style="width: 100%">
        <el-button type="primary" @click="onGetList(true)">查询</el-button>
        <el-button type="success" @click="onAdd()">新建</el-button>
        <!-- <el-button type="warning" @click="onImport">导入</el-button> -->
        <el-upload
          style="display: inline-block; margin: 0 10px"
          :action="action"
          :data="uploadData"
          :headers="headers"
          accept="[xlsx]"
          :on-success="onImportSuccess"
        >
          <el-button size="small" type="warning">导入</el-button>
        </el-upload>
        <el-button type="warning" @click="onExport">导出</el-button>
        <el-button type="primary" @click="onDownload">下载模板</el-button>
        <el-button type="danger" @click="onDelete(null, 1)">整单提交</el-button>
        <el-button type="danger" @click="onDelete()">整单删除</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      border
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="55"
        :selectable="onSelectable"
      ></el-table-column>
      <el-table-column prop="applyNo" label="申请单号" min-width="160" />
      <el-table-column prop="idCode" label="用户ID" />
      <el-table-column prop="registerMobile" label="注册号码" min-width="100" />
      <el-table-column prop="name" label="代理名称" />
      <el-table-column prop="level" label="等级" min-width="90" />
      <el-table-column prop="dealName" label="处理类型">
        <template slot-scope="{ row }">
          {{ formatType(row.dealId) }}
        </template>
      </el-table-column>
      <el-table-column prop="money" label="扣除金额" />
      <el-table-column prop="deductDate" label="扣款区间" />
      <el-table-column prop="createdTime" label="创建时间" min-width="150" />
      <el-table-column prop="submitTime" label="提交时间" min-width="150" />
      <el-table-column prop="doneTime" label="完成时间" min-width="150" />
      <el-table-column prop="revokeTime" label="取消时间" min-width="150" />
      <el-table-column prop="agentId" label="经办人">
        <template slot-scope="{ row }">
          {{ formatAgent(row.agentId) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="{ row }">
          {{ formatStatus(row.status) }}
        </template>
      </el-table-column>
      <el-table-column width="220" label="操作" fixed="right">
        <template slot-scope="{ row }">
          <!-- 草稿，已驳回，重新编辑的才能编辑 -->
          <el-button
            type="warning"
            :disabled="row.status !== 0 && row.status !== 3 && row.status !== 5"
            @click="onAdd(row)"
          >
            编辑
          </el-button>
          <!-- 草稿，已驳回，重新编辑的才能删除 -->
          <el-button
            type="danger"
            :disabled="row.status !== 0 && row.status !== 3 && row.status !== 5"
            @click="onDelete(row)"
          >
            删除
          </el-button>
          <el-button type="primary" @click="onDetail(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="form.current"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="form.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
    <el-dialog title="申请单详情" :visible.sync="detailShow">
      <applyRecordDetail :info="detailObj" />
      <div slot="footer" class="dialog-footer">
        <!-- <el-button @click="detailShow = false">取 消</el-button> -->
        <el-button type="primary" @click="detailShow = false">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="`${editObj.id ? '编辑' : '新建'}扣费申请单`"
      :visible.sync="editShow"
      width="500px"
    >
      <el-form
        ref="ruleForm"
        label-width="100px"
        :model="editObj"
        :rules="rules"
        style="width: 300px"
      >
        <el-form-item label="用户ID" prop="idCode">
          <el-input v-model="editObj.idCode" :disabled="editObj.id" />
        </el-form-item>
        <el-form-item v-if="editObj.id" label="注册手机号">
          <el-input v-model="editObj.registerMobile" :disabled="editObj.id" />
        </el-form-item>
        <el-form-item v-if="editObj.id" label="名称">
          <el-input v-model="editObj.name" :disabled="editObj.id" />
        </el-form-item>
        <el-form-item v-if="editObj.id" label="级别">
          <el-input v-model="editObj.level" :disabled="editObj.id" />
        </el-form-item>
        <el-form-item v-if="editObj.id" label="用户身份">
          <el-input v-model="editObj.role" :disabled="editObj.id" />
        </el-form-item>
        <el-form-item label="处理类型" prop="dealId">
          <el-select v-model="editObj.dealId" clearable>
            <el-option
              v-for="item in dealList"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="违规内容">
          <el-input v-model="editObj.violateValue" :maxlength="255" />
        </el-form-item>
        <el-form-item label="扣除金额" prop="money">
          <el-input
            v-model="editObj.money"
            type="number"
            @input="onMoneyChange"
          />
        </el-form-item>
        <el-form-item label="扣除期间" prop="deductDate">
          <el-date-picker
            v-model="editObj.deductDate"
            type="month"
            placeholder="选择月"
            value-format="yyyy-MM"
          ></el-date-picker>
          <!-- <el-input v-model="editObj.deductDate" /> -->
        </el-form-item>
        <el-form-item label="扣除平台" prop="platformCode">
          <el-select v-model="editObj.platformCode" clearable>
            <el-option
              v-for="item in platformList"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="经办人:">
          <el-select v-model="editObj.agentId" clearable>
            <el-option
              v-for="item in agentList"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注:">
          <el-input v-model="editObj.remark" type="textarea" :maxlength="255" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editShow = false">取 消</el-button>
        <el-button
          :loading="btnLoading"
          :disabled="btnLoading"
          type="primary"
          @click="onConfirmAdd"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import applyRecordDetail from './components/applyRecordDetail';
  import {
    getApplyList,
    getFirstCategoryList,
    addApply,
    editApply,
    submitOrDeleteApply,
    deleteApplyById,
    submitApply,
    exportApplyList,
    importApplyList,
  } from '@/api/award';
  import { exportApplyMenu } from '@/api/blob';
  import { APPLY_ORDER_STATUS, APPLY_DEAL_TYPE } from '@/consts/award';
  import { mapGetters } from 'vuex';
  import { generateRandomApplyNo } from '@/utils/index';
  import { getCookie, appCode } from '@/utils/auth';
  import injectHost from '@/utils/injectHost';
  import { downloadFile } from '@/utils';
  import { can } from '@/utils/hasRole';
  export default {
    components: { applyRecordDetail },
    data() {
      return {
        APPLY_ORDER_STATUS,
        APPLY_DEAL_TYPE,
        form: {
          name: '',
          id: '',
          dealId: '', // 处理类型id
          applyNo: '',
          status: '',
          current: 1,
          size: 10,
          pushTime: [],
          createTime: [],
          cancelTime: [],
          finishTime: [],
        },
        tableData: [],
        agentList: [],
        dealList: [],
        total: 0,
        detailObj: {},
        editObj: {},
        detailShow: false,
        editShow: false,
        rules: {
          idCode: [
            { required: true, message: '请输入用户ID', trigger: 'blur' },
          ],
          dealId: [
            {
              required: true,
              message: '选择处理类型',
              trigger: 'change',
            },
          ],
          deductDate: [
            {
              required: true,
              message: '选择扣除期间',
              trigger: 'change',
            },
          ],
          platformCode: [
            {
              required: true,
              message: '选择平台',
              trigger: 'change',
            },
          ],
          money: [
            { required: true, message: '请输入扣除金额', trigger: 'blur' },
          ],
        },
        selectedItems: [],
        platformList: [
          { label: 'ABM', id: 'ABM' },
          { label: 'DT', id: 'DT' },
        ],
        // 导入相关
        headers: {
          token: getCookie(),
          appCode: appCode(),
          appId: 'abmau',
        },
        uploadData: {
          appId: 'abmau',
          timeStamp: Date.now(),
        },
        action: '',
        btnLoading: false,
      };
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
      }),
    },
    created() {
      this.onGetList();
      this.action =
        'https://' + window.ACCESS_HOSTS.apiHost + '/api/abmio/api/v1.0/upload';
      getFirstCategoryList({ status: 1, parentId: 0 }).then(res => {
        const categoryList = res;
        const agentId = categoryList.filter(i => i.title === '经办人')[0].id;
        const dealId = categoryList.filter(i => i.title === '处理类型')[0].id;
        getFirstCategoryList({
          status: 1,
          parentId: agentId,
        }).then(res => {
          this.agentList = res;
        });
        getFirstCategoryList({
          status: 1,
          parentId: dealId,
        }).then(res => {
          this.dealList = res;
        });
      });
    },
    methods: {
      onGetList(boo = false) {
        if (boo) this.form.current = 1;
        let form = this.form;
        const createTime = form.createTime || [];
        const finishTime = form.finishTime || [];
        const cancelTime = form.cancelTime || [];
        const pushTime = form.pushTime || [];
        const params = {
          dealId: form.dealId,
          agentId: form.agentId,
          applyNo: form.applyNo,
          current: form.current,
          size: form.size,
          idCode: form.idCode,
          status: form.status,
          createdStartTime: createTime[0] || '',
          createdEndTime: createTime[1] || '',
          doneEndTime: finishTime[1] || '',
          doneStartTime: finishTime[0] || '',
          revokeStartTime: cancelTime[0] || '',
          revokeEndTime: cancelTime[1] || '',
          submitEndTime: pushTime[0] || '',
          submitStartTime: pushTime[1] || '',
        };
        getApplyList(params).then(res => {
          this.tableData = res.records || [];
          this.total = res.total;
        });
      },
      onAdd(obj = {}) {
        const permissionKey = obj.id ? 'applyRecord-add' : 'applyRecord-edit';
        this.$btnPermission(permissionKey).then(res => {
          this.editObj = JSON.parse(JSON.stringify(obj));
          this.editShow = true;
        });
      },
      onConfirmAdd() {
        this.$refs.ruleForm.validate(valid => {
          if (valid) {
            if (this.editObj.money <= 0) {
              this.$message.error('金额必须大于0');
              return;
            }
            this.btnLoading = true;
            if (this.editObj.id) {
              // 编辑
              editApply(this.editObj)
                .then(() => {
                  this.$message.success('修改成功');
                  this.editShow = false;
                  this.onGetList();
                })
                .finally(() => {
                  this.btnLoading = false;
                });
            } else {
              // 新增
              addApply({
                operate: this.username,
                applyNo: generateRandomApplyNo(),
                profitType: 17, // 写死扣除
                ...this.editObj,
              })
                .then(res => {
                  this.$message.success('新增成功');
                  this.editShow = false;
                  this.onGetList();
                })
                .finally(() => {
                  this.btnLoading = false;
                });
            }
          }
        });
      },
      onImportSuccess(response) {
        const obj = response.data;
        importApplyList({
          applyNo: generateRandomApplyNo(),
          fileName: obj.fileName,
          url: obj.url,
          operate: this.username,
        }).then(res => {
          this.$message.success(res);
          this.onGetList();
        });
      },
      onExport() {
        this.$btnPermission('applyRecord-export').then(res => {
          const {
            name,
            dealId,
            id,
            applyNo,
            status,
            pushTime,
            createTime,
            cancelTime,
            finishTime,
          } = this.form;
          const noValue = !name && !dealId && !id && !applyNo && !status;
          const noTime =
            pushTime &&
            pushTime.length === 0 &&
            createTime &&
            createTime.length === 0 &&
            cancelTime &&
            cancelTime.length === 0 &&
            finishTime &&
            finishTime.length === 0;

          if (noValue && noTime) {
            this.$message.error('请至少选择一个条件才能导出');
            return;
          }
          exportApplyMenu(this.form).then(res => {
            downloadFile(res.data, '申请记录', 'zip');
          });
        });
      },
      onDownload() {
        this.$btnPermission('applyRecord-download').then(res => {
          const url =
            '//oss.danchuangglobal.com/abmau/202012/027d5aa9d2ba4e18194de4669bf52d0d.xlsx';
          // window.open(url)
          window.location.href = url;
        });
      },
      onSelectable(row) {
        return row.status === 0 || row.status === 3 || row.status === 5;
      },
      onDelete(obj = {}, status = '') {
        const permissionKey =
          obj && obj.id
            ? 'applyRecord-delete'
            : status
            ? 'applyRecord-submit'
            : 'applyRecord-batch-delete';
        this.$btnPermission(permissionKey).then(res => {
          const id = (obj && obj.id) || '';
          if (!id && this.selectedItems.length === 0) {
            this.$message.error('请至少选择一项');
            return;
          }
          // 整单删除根据 单号删除，单个删除根据id删除
          const applyList = id
            ? [{ id }]
            : this.selectedItems.map(i => {
                return { applyNo: i.applyNo };
              });
          this.$confirm('是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(() => {
            if (id) {
              // 单个删除
              deleteApplyById({
                operate: this.username,
                applyNo: obj.applyNo,
                id,
              }).then(() => {
                this.$message.success('删除成功');
                this.onGetList();
              });
            } else {
              if (!status) {
                submitOrDeleteApply({
                  operate: this.username,
                  status: status ? 1 : 6, // status: 1-提交 6-删除
                  applyList,
                }).then(() => {
                  this.$message.success('删除成功');
                  this.onGetList();
                });
              } else {
                submitApply({
                  operate: this.username,
                  status: status ? 1 : 6, // status: 1-提交 6-删除
                  applyList,
                }).then(() => {
                  this.$message.success('提交成功');
                  this.onGetList();
                });
              }
            }
          });
        });
      },
      handleSelectionChange(e) {
        this.selectedItems = e;
      },
      onDetail(obj) {
        this.detailObj = obj;
        this.detailObj.dealName = this.formatType(obj.dealId);
        this.detailObj.agentName = this.formatAgent(obj.agentId);
        this.detailObj.platformName = this.formatPlatform(obj.platformCode);
        this.detailObj.statusName = this.formatStatus(obj.status);
        this.detailShow = true;
      },
      onMoneyChange(e) {
        this.editObj.money = e.match(/\d+(\.\d{0,2})?/)[0];
      },
      handleCurrentChange(e) {
        this.form.current = e;
        this.onGetList();
      },
      handleSizeChange(e) {
        this.form.size = e;
        this.onGetList();
      },
      formatType(id) {
        const obj = this.dealList.filter(i => i.id === id)[0];
        return (obj && obj.title) || '- -';
      },
      formatAgent(id) {
        const obj = this.agentList.filter(i => i.id === id)[0];
        return (obj && obj.title) || '- -';
      },
      formatStatus(id) {
        const obj = APPLY_ORDER_STATUS.filter(i => i.value === id)[0];
        return (obj && obj.label) || '- -';
      },
      formatPlatform(id) {
        const obj = this.platformList.filter(i => i.id === id)[0];
        return (obj && obj.title) || '- -';
      },
    },
  };
</script>
<style lang="scss">
  .apply-record {
    .el-input {
      width: 280px !important;
    }
    .el-date-editor {
      width: 280px !important;
    }
    .el-upload-list {
      display: none;
    }
  }
</style>
