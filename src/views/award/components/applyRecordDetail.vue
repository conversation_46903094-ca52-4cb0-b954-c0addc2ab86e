<template>
  <el-form disabled label-width="100px">
    <el-form-item label="申请单号:">
      <el-input v-model="info.applyNo" />
    </el-form-item>
    <!-- <el-form-item label="序号:">
      <el-input v-model="info.orderNo" />
    </el-form-item> -->
    <el-form-item label="用户ID:">
      <el-input v-model="info.idCode" />
    </el-form-item>
    <el-form-item label="注册手机号:">
      <el-input v-model="info.registerMobile" />
    </el-form-item>
    <el-form-item label="名称:">
      <el-input v-model="info.name" />
    </el-form-item>
    <el-form-item label="级别:">
      <el-input v-model="info.level" />
    </el-form-item>
    <el-form-item label="用户身份:">
      <el-input v-model="info.role" />
    </el-form-item>
    <el-form-item label="处理类型:">
      <el-input v-model="info.dealName" />
    </el-form-item>
    <el-form-item label="违规内容:">
      <el-input v-model="info.violateValue" />
    </el-form-item>
    <el-form-item label="扣除金额:">
      <el-input v-model="info.money" />
    </el-form-item>
    <el-form-item label="扣除平台:">
      <el-input v-model="info.platformCode" />
    </el-form-item>
    <el-form-item label="经办人:">
      <el-input v-model="info.agentName" />
    </el-form-item>
    <el-form-item label="创建时间:">
      <el-input v-model="info.createdTime" />
    </el-form-item>
    <el-form-item label="提交时间:">
      <el-input v-model="info.submitTime" />
    </el-form-item>
    <el-form-item label="完成时间:">
      <el-input v-model="info.doneTime" />
    </el-form-item>
    <el-form-item label="取消时间:">
      <el-input v-model="info.revokeTime" />
    </el-form-item>
    <el-form-item label="扣款期间:">
      <el-input v-model="info.deductDate" />
    </el-form-item>
    <el-form-item label="状态:">
      <el-input v-model="info.statusName" />
    </el-form-item>
    <el-form-item label="创建人:">
      <el-input v-model="info.createBy" />
    </el-form-item>
    <el-form-item label="审核人:">
      <el-input v-model="info.dealBy" />
    </el-form-item>
    <el-form-item label="备注:">
      <el-input v-model="info.remark" type="textarea" />
    </el-form-item>
  </el-form>
</template>
<script>
  export default {
    props: {
      info: {
        type: Object,
        default: () => {},
      },
    },
  };
</script>
