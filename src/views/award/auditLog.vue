<template>
  <div>
    <el-form inline>
      <el-form-item label="任务编号:">
        <el-input v-model="form.taskNo" />
      </el-form-item>
      <el-form-item label="任务状态:">
        <el-select v-model="form.taskStatus" clearable>
          <el-option
            v-for="item in statusList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="任务标识:">
        <el-input v-model="form.taskKey" />
      </el-form-item>
      <el-form-item label="创建时间:">
        <el-date-picker
          v-model="time"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="">
        <el-button type="primary" @click="onGetList(true)">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table border :data="tableData">
      <el-table-column label="任务编号" prop="taskNo" min-width="170" />
      <el-table-column label="任务标识" prop="taskKey" min-width="200" />
      <el-table-column label="任务数据总量" prop="taskSize" />
      <el-table-column label="任务成功数据量" prop="successSize" />
      <el-table-column label="任务失败数据量" prop="failSize" />
      <el-table-column label="处理中数据量" prop="dealSize" />
      <el-table-column label="任务状态" prop="taskStatus" />
      <el-table-column label="申请人" prop="taskApplicant" />
      <el-table-column label="创建时间" prop="createTime" min-width="160" />
      <el-table-column label="更新时间" prop="updateTime" min-width="160" />
      <el-table-column label="错误信息" prop="taskErrorMsg" min-width="200" />
    </el-table>
    <el-pagination
      :current-page="form.pageNo"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="form.limit"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>
<script>
  import { getAuditLogList } from '@/api/award';
  export default {
    data() {
      return {
        form: {
          taskStatus: '',
          taskKey: '',
          taskNo: '',
          createStartTime: '',
          createEndTime: '',
          pageNo: 1,
          limit: 10,
        },
        time: [],
        total: 0,
        tableData: [],
        statusList: [
          { label: '处理中', value: 1 },
          { label: '成功', value: 2 },
          { label: '失败', value: 3 },
        ],
      };
    },
    created() {
      this.onGetList();
    },
    methods: {
      onGetList(boo = false) {
        if (boo) {
          this.form.pageNo = 1;
        }
        const time = this.time;
        this.form.createStartTime = (time && time[0]) || '';
        this.form.createEndTime = (time && time[1]) || '';
        getAuditLogList(this.form).then(res => {
          this.total = res.total;
          this.tableData = res.records || [];
        });
      },
      handleSizeChange(e) {
        this.form.limit = e;
        this.onGetList();
      },
      handleCurrentChange(e) {
        this.form.pageNo = e;
        this.onGetList();
      },
    },
  };
</script>
