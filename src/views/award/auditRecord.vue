<template>
  <div>
    <el-form inline label-width="100px" class="apply-record">
      <el-form-item label="经办人">
        <el-select v-model="form.agentId" clearable>
          <el-option
            v-for="item in agentList"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="用户ID">
        <el-input v-model="form.idCode" placeholder="用户ID" :maxlength="20" />
      </el-form-item>
      <el-form-item label="账户">
        <el-input v-model="form.account" placeholder="账户" :maxlength="255" />
      </el-form-item>
      <el-form-item label="申请单号">
        <el-input
          v-model="form.applyNo"
          placeholder="申请单号"
          :maxlength="25"
        />
      </el-form-item>
      <el-form-item label="处理类型:">
        <el-select v-model="form.dealId" clearable>
          <el-option
            v-for="item in dealList"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="单据类型:">
        <el-select v-model="form.type" clearable>
          <el-option
            v-for="item in DOCUMENT_TYPE"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态:">
        <el-select v-model="form.status" clearable>
          <el-option
            v-for="item in APPLY_ORDER_AUDIT_STATUS"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="创建时间:">
        <el-date-picker
          v-model="form.createTime"
          type="daterange"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item> -->

      <el-form-item label="完成时间:">
        <el-date-picker
          v-model="form.finishTime"
          type="daterange"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
      </el-form-item>

      <el-form-item style="width: 100%">
        <el-button type="primary" @click="onGetList(true)">查询</el-button>
        <el-button type="primary" @click="onAuditPass(1, 1)">
          审核通过
        </el-button>

        <el-button type="danger" @click="onAuditPass(1, 0)">驳回</el-button>
        <el-button type="danger" @click="onAuditPass(1, 2)">撤销</el-button>
        <el-button type="primary" @click="onExport">导出</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      border
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column prop="applyNo" label="申请单号" min-width="150" />
      <el-table-column prop="idCode" label="用户ID" />
      <el-table-column prop="registerMobile" label="注册号码" min-width="110" />
      <el-table-column prop="name" label="代理名称" />
      <el-table-column prop="level" label="等级" min-width="100" />
      <el-table-column prop="dealId" label="处理类型">
        <template slot-scope="{ row }">
          {{ formatType(row.dealId) }}
        </template>
      </el-table-column>
      <el-table-column prop="money" label="金额" />
      <el-table-column prop="account" label="账户" min-width="100" />
      <el-table-column prop="beforeMoney" label="审核前账户余额" />
      <el-table-column prop="lastMoney" label="审核后账户余额" />
      <el-table-column prop="agentId" label="经办人">
        <template slot-scope="{ row }">
          {{ formatAgent(row.agentId) }}
        </template>
      </el-table-column>
      <el-table-column prop="doneTime" label="完成时间" min-width="150" />
      <el-table-column prop="revokeTime" label="撤销时间" min-width="150" />
      <el-table-column prop="profitType" label="单据类型">
        <template slot-scope="{ row }">
          {{ formatDocument(row.profitType) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="{ row }">
          {{ formatStatus(row.status) }}
        </template>
      </el-table-column>
      <el-table-column width="200" label="操作" fixed="right">
        <template slot-scope="{ row }">
          <!-- 待审核的才有驳回和通过 -->
          <el-button
            v-if="row.status === 1"
            type="warning"
            @click="onAuditPass(0, 0, row)"
          >
            驳回
          </el-button>
          <!-- <el-button
            v-if="row.status === 1"
            type="warning"
            @click="onAuditPass(0, 1, row)"
          >
            审核通过
          </el-button> -->
          <!-- 已完成的才能撤销 -->
          <el-button
            v-if="row.status == 2"
            type="danger"
            @click="onAuditPass(0, 2, row)"
          >
            撤销
          </el-button>
          <el-button type="primary" @click="onDetail(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="form.current"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="form.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
    <el-dialog title="申请单详情" :visible.sync="detailShow">
      <applyRecordDetail :info="detailObj" />
      <div slot="footer" class="dialog-footer">
        <!-- <el-button @click="detailShow = false">取 消</el-button> -->
        <el-button type="primary" @click="detailShow = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import applyRecordDetail from './components/applyRecordDetail';
  import { mapGetters } from 'vuex';
  import {
    APPLY_ORDER_AUDIT_STATUS,
    APPLY_ORDER_STATUS,
    DOCUMENT_TYPE,
  } from '@/consts/award';
  import { downloadFile } from '@/utils';
  import {
    getAuditDetail,
    getFirstCategoryList,
    auditPass,
    auditReject,
    auditCancel,
    batchAuditReject,
    batchAuditCancel,
    exportAuditList,
  } from '@/api/award';
  import { exportAuditMenu } from '@/api/blob';
  export default {
    components: { applyRecordDetail },
    data() {
      return {
        APPLY_ORDER_AUDIT_STATUS,
        DOCUMENT_TYPE,
        form: {
          name: '',
          idCode: '',
          // profitType: 17, // 单据类型
          type: '', // 单据类型
          applyNo: '', // 申请单号
          dealId: '', // 处理类型
          agentId: '', //经办人id
          account: '', // 账户
          status: '',
          current: 1,
          size: 10,
          finishTime: [], // 完成时间
        },
        tableData: [],
        dealList: [],
        agentList: [],
        total: 0,
        detailObj: {},
        detailShow: false,
        rules: [],
        selectedItems: [],
      };
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
      }),
    },
    created() {
      this.onGetList();
      getFirstCategoryList({ status: 1, parentId: 0 }).then(res => {
        const categoryList = res;
        const agentId = categoryList.filter(i => i.title === '经办人')[0].id;
        const dealId = categoryList.filter(i => i.title === '处理类型')[0].id;
        getFirstCategoryList({
          status: 1,
          parentId: agentId,
        }).then(res => {
          this.agentList = res;
        });
        getFirstCategoryList({
          status: 1,
          parentId: dealId,
        }).then(res => {
          this.dealList = res;
        });
      });
    },
    methods: {
      onGetList(boo) {
        if (boo) this.form.current = 1;
        this.form.doneEndTime = this.form.finishTime && this.form.finishTime[1];
        this.form.doneStartTime =
          this.form.finishTime && this.form.finishTime[0];
        getAuditDetail(this.form).then(res => {
          this.tableData = res.records || [];
          this.total = res.total;
        });
      },
      onAdd(obj = {}) {
        this.editObj = JSON.parse(JSON.stringify(obj));
        this.editShow = true;
      },
      onImport() {},
      onExport() {
        this.$btnPermission('auditRecord-export').then(res => {
          exportAuditMenu(this.form).then(res => {
            downloadFile(res.data, '审核记录', 'zip');
          });
        });
      },
      onDownload() {},
      onPush() {},
      onDelete(id = '') {
        if (!id && this.selectedItems.length === 0) {
          this.$message.error('请至少选择一项');
          return;
        }

        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          this.$message.success('删除成功');
        });
      },
      handleSelectionChange(e) {
        this.selectedItems = e;
      },
      onAuditPass(type, status, row) {
        // type 1-批量操作 0-单个操作
        // status: 0-驳回 1-通过 2-撤销
        // id 批量无id，单个才有
        const permissionKeyArr = [
          'auditRecord-batch-reject',
          'auditRecord-examine',
          'auditRecord-batch-revoke',
        ];
        const permissionKey =
          type === 1
            ? permissionKeyArr[status]
            : status === 0
            ? 'auditRecord-reject'
            : 'auditRecord-revoke';
        this.$btnPermission(permissionKey).then(res => {
          if (type === 1 && this.selectedItems.length === 0) {
            this.$message.error('请至少选择一项进行操作');
            return;
          }
          this.$confirm(
            `此操作将${this.formatOperateText(status)}, 是否继续?`,
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            },
          ).then(() => {
            const dealBy = this.username;
            if (type === 1) {
              const applyNoList = this.selectedItems.map(i => i.applyNo);
              if (status === 0) {
                // 批量驳回
                batchAuditReject({ applyNoList, dealBy }).then(() => {
                  this.$message.success(
                    `${this.formatOperateText(status)}成功`,
                  );
                  this.onGetList();
                });
              } else if (status === 1) {
                // 批量通过
                auditPass({ applyNoList, dealBy }).then(() => {
                  this.$message.success(
                    `${this.formatOperateText(status)}成功`,
                  );
                  this.onGetList();
                });
              } else {
                // 批量撤销
                batchAuditCancel({ applyNoList, dealBy }).then(() => {
                  this.$message.success(
                    `${this.formatOperateText(status)}成功`,
                  );
                  this.onGetList();
                });
              }
            } else {
              const id = row.id;
              const applyNo = row.applyNo;
              if (status === 0) {
                // 驳回
                auditReject({ applyNo, id, dealBy }).then(() => {
                  this.$message.success(
                    `${this.formatOperateText(status)}成功`,
                  );
                  this.onGetList();
                });
              } else if (status === 1) {
                // 批量通过
                auditPass({ applyNoList: [applyNo], id, dealBy }).then(() => {
                  this.$message.success(
                    `${this.formatOperateText(status)}成功`,
                  );
                  this.onGetList();
                });
              } else {
                // 撤销
                auditCancel({ applyNo, id, dealBy }).then(() => {
                  this.$message.success(
                    `${this.formatOperateText(status)}成功`,
                  );
                  this.onGetList();
                });
              }
            }
          });
        });
      },
      onDetail(obj) {
        this.detailObj = obj;
        this.detailObj.dealName = this.formatType(obj.dealId);
        this.detailObj.agentName = this.formatAgent(obj.agentId);
        this.detailObj.platformName = obj.platformCode;
        this.detailObj.statusName = this.formatStatus(obj.status);
        this.detailShow = true;
      },
      handleCurrentChange(e) {
        this.form.current = e;
        this.onGetList();
      },
      handleSizeChange(e) {
        this.form.size = e;
        this.onGetList();
      },
      formatOperateText(type) {
        if (type === 0) return '驳回';
        if (type === 1) return '审核通过';
        if (type === 2) return '撤销';
      },
      formatType(id) {
        const obj = this.dealList.filter(i => i.id === id)[0];
        return (obj && obj.title) || '- -';
      },
      formatAgent(id) {
        const obj = this.agentList.filter(i => i.id === id)[0];
        return (obj && obj.title) || '- -';
      },
      formatStatus(id) {
        const obj = APPLY_ORDER_STATUS.filter(i => i.value === id)[0];
        return (obj && obj.label) || '- -';
      },
      formatDocument(id) {
        const obj = DOCUMENT_TYPE.filter(i => i.value === id)[0];
        return (obj && obj.label) || '- -';
      },
    },
  };
</script>
