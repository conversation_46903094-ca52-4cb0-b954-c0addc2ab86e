<!--
 * @Author: bruce
 * @Date: 2024-01-26 17:43:13
 * @LastEditors: bruce
 * @FilePath: /access-fmis-web/src/views/transferManagement/components/TransferDialog.vue
 * @Description: 
-->
<template>
  <el-form
    ref="ruleForm"
    :model="ruleForm"
    :rules="rules"
    label-width="100px"
    class="demo-ruleForm"
  >
    <el-form-item label="姓名:" prop="name">
      <el-input v-model="ruleForm.name"></el-input>
    </el-form-item>
    <el-form-item label="渠道账号:" prop="name">
      <el-input v-model="ruleForm.name"></el-input>
    </el-form-item>
    <el-form-item label="户名:" prop="name">
      <el-input v-model="ruleForm.name"></el-input>
    </el-form-item>
    <el-form-item label="银行卡号:" prop="name">
      <el-input v-model="ruleForm.name"></el-input>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
      <el-button @click="resetForm('ruleForm')">取消</el-button>
    </el-form-item>
  </el-form>
</template>
<script>
  import { retryTransfer } from '@/api/userFinancialManagement/transferManagement/TransferHandle';

  export default {
    name: 'Form',
    components: {},
    props: {
      rowData: {
        type: Object,
        default: () => {
          return {};
        },
      },
    },
    data() {
      return {
        regionList: [{ label: '区域一', value: 'shanghai' }],
        checkBoxList: [{ label: '美食/餐厅线上活动', value: 1 }],
        radioList: [{ label: '线上品牌商赞助', value: 1 }],
        ruleForm: { time: [], type: [] },
        rules: {
          name: [
            { required: true, message: '请输入活动名称', trigger: 'blur' },
          ],
        },
      };
    },
    mounted() {},
    methods: {
      submitForm(formName) {
        this.$refs[formName].validate(valid => {
          if (valid) {
            alert('submit!');
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      resetForm(formName) {
        this.$refs[formName].resetFields();
        this.$emit('close-dialog');
      },
    },
  };
</script>
<style lang="scss" scoped></style>
