<!--
 * @Author: bruce
 * @Date: 2024-01-17 15:02:30
 * @LastEditors: Please set LastEditors
 * @FilePath: /access-fmis-web/src/views/transferManagement/TransferHandleSouthEast.vue
 * @Description: 转账处理
-->

<template>
  <div class="table">
    <el-form
      :inline="true"
      :model="formInline"
      class="demo-form-inline acg-filter-form"
    >
      <el-form-item label="转账单号">
        <el-input
          v-model="formInline.platformOrderId"
          placeholder="请输入"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="外部业务单号">
        <el-input
          v-model="formInline.tenantOrderId"
          placeholder="请输入"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="姓名">
        <el-input
          v-model="formInline.userName"
          placeholder="请输入"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="渠道">
        <el-select
          v-model="formInline.channelId"
          placeholder="请选择"
          clearable
          filterable
        >
          <el-option
            v-for="(item, index) in channelList"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态">
        <el-select
          v-model="formInline.status"
          placeholder="请选择"
          clearable
          filterable
        >
          <el-option
            v-for="(item, index) in statusList"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label=" ">
        <el-button type="primary" @click="onSearch">
          <i class="el-icon-search"></i>
          查询
        </el-button>
        <el-button @click="onReset">
          <i class="el-icon-refresh"></i>
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        prop="platformOrderId"
        label="转账单号"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="tenantOrderId"
        label="外部业务单号"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="businessApplication"
        label="赔付类型"
        width="80"
      ></el-table-column>
      <el-table-column
        prop="channelText"
        label="渠道"
        width="80"
      ></el-table-column>
      <el-table-column
        prop="userName"
        label="姓名"
        width="80"
      ></el-table-column>
      <el-table-column
        prop="merchantId"
        label="渠道账号"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="bankName"
        label="银行名称"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="cardNo"
        label="银行卡号"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="transAmount"
        label="金额"
        width="80"
      ></el-table-column>
      <el-table-column
        prop="currency"
        label="币种"
        width="80"
      ></el-table-column>
      <el-table-column
        prop="date"
        label="渠道转账流水号"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="statusDesc"
        label="状态"
        width="80"
      ></el-table-column>
      <el-table-column
        prop="channelMessage"
        label="描述"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="transSuccessTime"
        label="转账成功时间"
        width="180"
      ></el-table-column>
      <el-table-column label="操作" fixed="right" width="120">
        <template
          v-if="Number(row.retry) === 1 && row.status === 'failed'"
          slot-scope="{ row }"
        >
          <el-link
            type="primary"
            style="margin-right: 10px"
            @click="onTransfer(row)"
          >
            发起转账
          </el-link>
          <el-link type="danger" @click="onDelete(row)">驳回</el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="pagination.current"
      :page-sizes="pagination.pageSizes"
      :page-size="pagination.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <el-dialog
      v-if="dialogVisible"
      title="转帐"
      :visible.sync="dialogVisible"
      width="500px"
    >
      <transfer-dialog :row-data="rowData" @close-dialog="closeDialog" />
    </el-dialog>
  </div>
</template>
<script>
  import TransferDialog from './components/TransferDialogSouthEast.vue';
  import {
    transferOrderList,
    transferOrderReject,
    retryTransfer,
  } from '@/api/userFinancialManagement/transferManagement/TransferHandle';
  export default {
    name: 'TransferHandle',
    components: {
      TransferDialog,
    },
    props: {},
    data() {
      return {
        formInline: {},
        statusList: [
          { label: '交易成功', value: 'success' },
          { label: '处理中', value: 'process' },
          { label: '失败', value: 'failed' },
          { label: '未知', value: 'unknow' },
        ],
        dialogVisible: false,
        pagination: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
          pageSizes: [10, 20, 50, 100],
        },
        rowData: {},
        tableData: [],
        multipleSelection: [],
        channelList: [
          { label: '银联', value: 'unionpay' },
          { label: '支付宝', value: 'alipay' },
        ],
      };
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        let params = { ...this.formInline, ...this.pagination };
        delete params.pageSizes;
        delete params.total;
        const { res, err } = await transferOrderList(params);
        if (!err) {
          this.tableData = res.records;
          this.tableData.forEach(item => {
            item['channelText'] = this.getLabel(
              item.channelId,
              this.channelList,
            );
          });
          this.$set(this.pagination, 'total', res.total);
        }
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      onTransfer(row) {
        this.rowData = row;
        // this.dialogVisible = true;
        this.onRetryTransfer(row);
      },
      onReset() {
        Object.assign(this.$data, this.$options.data());
        this.initData();
      },
      onSearch() {
        this.$set(this.pagination, 'pageNo', 1);
        this.initData();
      },
      async onTransferOrderReject(row) {
        const { res, err } = await transferOrderReject({
          platformOrderId: row.platformOrderId,
        });
        if (!err) {
          this.$message({ type: 'success', message: '操作成功!' });
          this.initData();
        }
      },
      async onRetryTransfer(row) {
        const { res, err } = await retryTransfer({
          platformOrderId: row.platformOrderId,
        });
        console.log('res:::', res);
        if (!err) {
          this.$message({ type: 'success', message: '操作成功!' });
          this.initData();
        }
      },
      onDelete(row) {
        this.$confirm('此操作将驳回改转账记录, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.onTransferOrderReject(row);
          })
          .catch(() => {});
      },
      handleSizeChange(val) {
        this.pagination.pageSize = val;
        this.initData();
      },
      handleCurrentChange(val) {
        this.pagination.pageNo = val;
        this.initData();
      },
      closeDialog() {
        this.dialogVisible = false;
        this.rowData = {};
      },
      batchTransfer() {
        if (!this.multipleSelection.length) {
          this.$message.warning('请勾选账单');
          return;
        }
      },
      getLabel(value, list) {
        if (!value) return '-';
        return list.find(item => item.value === value).label;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
