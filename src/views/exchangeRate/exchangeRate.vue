<!-- 汇率获取日志 -->
<template>
  <div class="container">
    <el-form :inline="true" size="small" :model="form" class="demo-form-inline">
      <el-form-item label="任务日期">
        <el-date-picker
          v-model="searchDate"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="渠道:">
        <el-select v-model="form.channelId">
          <el-option
            v-for="item in channelList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="initData(true)">
          查询
        </el-button>
        <el-button type="primary" @click="onReset()">重置</el-button>
        <el-button type="primary" @click="getExchangeRateSetters()">
          汇率设置获取
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      :data="exchangeRateList"
      border
      style="width: 100%"
    >
      <el-table-column prop="hopeExecuteTime" label="任务日期" />
      <el-table-column prop="startTime" label="开始时间" />
      <el-table-column prop="endTime" label="结束时间" />
      <el-table-column prop="status" label="获取状态">
        <template slot-scope="scope">
          <span v-if="scope.row.status === 1" style="color: #67c23a">
            下载中
          </span>
          <span v-if="scope.row.status === 2" style="color: #f56c6c">
            下载失败
            <el-button
              type="primary"
              plain
              icon="el-icon-download"
              circle
              style="margin-left: 20px"
              :loading="downloading"
              @click="handleDownload(scope.row)"
            ></el-button>
          </span>
          <span v-if="scope.row.status === 3" style="color: #409eff">
            下载成功
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="channelId" label="渠道">
        <template slot-scope="{ row }">
          {{ formatChannel(row.channelId) }}
        </template>
      </el-table-column>
      <el-table-column prop="downloadNum" label="下载条数" />
    </el-table>
    <el-row style="text-align: end">
      <el-pagination
        class="pagination-block"
        background
        :current-page="form.pageNo"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="form.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </el-row>
  </div>
</template>

<script>
  import { getExchangeLogList, getExchangeChannelList } from '@/api/exchange';
  import { reDownload } from '@/api/exchange';
  export default {
    data() {
      return {
        exchangeRateList: [],
        form: {
          startTime: '',
          endTime: '',
          pageNo: 1,
          limit: 10,
          channelId: '',
        },
        searchDate: [],
        total: 0,
        loading: false,
        downloading: false,
        channelList: [],
      };
    },
    created() {
      this.initData();
      getExchangeChannelList().then(list => {
        this.channelList = list;
      });
    },
    methods: {
      initData(boolean = false) {
        if (boolean) {
          this.form.pageNo = 1;
        }
        if (this.searchDate[0]) {
          let a = this.searchDate[0] + ' 00:00:00';
          let b = this.searchDate[1] + ' 23:59:59';
          this.form.startTime = a;
          this.form.endTime = b;
        } else {
          this.form.startTime = '';
          this.form.endTime = '';
        }
        this.loading = true;
        getExchangeLogList(this.form)
          .then(res => {
            this.exchangeRateList = res.records;
            this.total = res.total;
          })
          .finally(() => {
            this.loading = false;
          });
      },
      onReset() {
        this.form.pageNo = 1;
        this.searchDate = [];
        this.initData();
      },
      handleCurrentChange(val) {
        this.form.pageNo = val;
        this.initData();
      },
      handleSizeChange(e) {
        this.form.limit = e;
        this.initData();
      },
      formatChannel(value) {
        const obj = this.channelList.filter(i => i.value === value)[0];
        return obj ? obj.name : '- -';
      },
      getExchangeRateSetters() {
        this.$router.push(`getExchangeRateSetters`);
      },
      // 下载失败 重新下载
      handleDownload(row, id) {
        this.downloading = true;
        reDownload({ id: row.id })
          .then(res => {
            this.initData();
          })
          .finally(() => {
            this.downloading = false;
          });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
