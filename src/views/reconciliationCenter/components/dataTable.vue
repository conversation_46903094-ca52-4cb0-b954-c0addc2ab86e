<template>
  <div>
    <el-table
      v-loading="loading"
      show-summary
      sum-text="汇总"
      :data="data"
      style="width: 100%"
      :summary-method="summaryMethod"
    >
      <el-table-column prop="id" label="序号" type="index"></el-table-column>
      <el-table-column prop="billDate" label="账单日期"></el-table-column>
      <el-table-column prop="merchantName" label="公司名称"></el-table-column>
      <el-table-column prop="bizType" label="对账类型">
        <template slot-scope="{ row }">
          {{ row.bizType === 0 ? '收单支付渠道' : '福三凭证' }}
        </template>
      </el-table-column>
      <el-table-column prop="channelName" label="机构类型"></el-table-column>
      <el-table-column
        prop="paymentChannelMerchantId"
        label="机构账号"
      ></el-table-column>

      <el-table-column label="单据" align="center">
        <el-table-column
          prop="platformIncomeOriginalCurrency"
          label="收入（原币）"
        >
          <template slot-scope="{ row }">
            <a style="cursor: pointer" @click="handelDetails(row)">
              +{{ row.platformIncomeOriginalCurrency }}
              <br />
              {{ row.platformIncomeOriginalCurrencyCount }} 笔
            </a>
          </template>
        </el-table-column>
        <el-table-column prop="platformIncomeCny" label="收入（人民币）">
          <template slot-scope="{ row }">
            <a
              v-if="
                row.bizType === 0 ||
                row.platformIncomeCn ||
                row.platformIncomeCnyCount
              "
              style="cursor: pointer"
              @click="handelDetails(row)"
            >
              +{{ row.platformIncomeCny }}
              <br />
              {{ row.platformIncomeCnyCount }} 笔
            </a>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="platformExpendOriginalCurrency"
          label="支出（原币）"
        >
          <template slot-scope="{ row }">
            <a style="cursor: pointer" @click="handelDetails(row)">
              +{{ row.platformExpendOriginalCurrency }}
              <br />
              {{ row.platformExpendOriginalCurrencyCount }} 笔
            </a>
          </template>
        </el-table-column>
        <el-table-column prop="platformExpendCny" label="支出（人民币）">
          <template slot-scope="{ row }">
            <a
              v-if="
                row.bizType === 0 ||
                row.platformExpendCny ||
                row.platformExpendCnyCount
              "
              style="cursor: pointer"
              @click="handelDetails(row)"
            >
              +{{ row.platformExpendCny }}
              <br />
              {{ row.platformExpendCnyCount }} 笔
            </a>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="流水" align="center">
        <el-table-column
          prop="channelIncomeOriginalCurrency"
          label="收入（原币）"
          width="120"
        >
          <template slot-scope="{ row }">
            <a style="cursor: pointer" @click="handelDetails(row)">
              +{{ row.channelIncomeOriginalCurrency }}
              <br />
              {{ row.channelIncomeOriginalCurrencyCount }} 笔
            </a>
          </template>
        </el-table-column>

        <el-table-column
          prop="channelExpendOriginalCurrency"
          label="支出（原币）"
          width="120"
        >
          <template slot-scope="{ row }">
            <a style="cursor: pointer" @click="handelDetails(row)">
              +{{ row.channelExpendOriginalCurrency }}
              <br />
              {{ row.channelExpendOriginalCurrencyCount }} 笔
            </a>
          </template>
        </el-table-column>
        <el-table-column prop="channelChargeAmount" label="手续费" width="120">
          <template slot-scope="{ row }">
            <a style="cursor: pointer" @click="handelDetails(row)">
              {{ row.channelChargeAmount }}
            </a>
          </template>
        </el-table-column>
      </el-table-column>

      <el-table-column label="差异" align="center">
        <el-table-column prop="cashShortAmount" label="单据多单">
          <template slot-scope="{ row }">
            <a style="cursor: pointer" @click="handelDetails(row)">
              {{ row.cashShortAmount }}
              <br />
              {{ row.cashShortAmountCount }} 笔
            </a>
          </template>
        </el-table-column>
        <el-table-column prop="cashOverAmount" label="流水多单">
          <template slot-scope="{ row }">
            <a style="cursor: pointer" @click="handelDetails(row)">
              {{ row.cashOverAmount }}
              <br />
              {{ row.cashOverAmountCount }} 笔
            </a>
          </template>
        </el-table-column>
        <el-table-column prop="differenceCount" label="差异数据">
          <template slot-scope="{ row }">
            <a style="cursor: pointer" @click="handelDetails(row)">
              {{
                row.differenceCount != null ? `${row.differenceCount}笔` : ''
              }}
            </a>
          </template>
        </el-table-column>
        <!-- <el-table-column
          v-if="tabType === '1'"
          prop="differenceCount"
          label="汇差差额"
        >
          <template slot-scope="{ row }">
            <a style="cursor: pointer" @click="handelDetails(row)">
              {{ row.differenceCount }}
            </a>
          </template>
        </el-table-column> -->
        <!-- <el-table-column
          v-if="tabType === '2'"
          prop="differenceCount"
          label="税差差额"
        >
          <template slot-scope="{ row }">
            <a style="cursor: pointer" @click="handelDetails(row)">
              {{ row.differenceCount }}
            </a>
          </template>
        </el-table-column> -->
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="100">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleClick(scope.row)">
            明细
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      ref="listPage"
      background
      :current-page.sync="pagination.pageNo"
      :page-size.sync="pagination.limit"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      @current-change="pagination_currentChange"
      @size-change="pagination_sizeChange"
    ></el-pagination>
  </div>
</template>

<script>
  import { changeTwoDecimal } from '@/utils/math';
  export default {
    props: {
      data: {
        type: Array,
        default: () => [],
      },
      pagination: {
        type: Object,
        default: null,
      },
      loading: {
        type: Boolean,
        default: false,
      },
      tabType: {
        type: String,
        default: '1',
      },
    },
    data() {
      return {};
    },
    methods: {
      summaryMethod({ columns, data }) {
        const sums = ['汇总'];
        columns.forEach((item, index) => {
          const values = data.map(item1 => Number(item1[item.property]));
          if ([6, 7, 8, 9, 10, 11, 12, 13, 14, 15].includes(index)) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
          }
        });
        return sums.map(item => changeTwoDecimal(item));
      },
      handleClick(val) {
        this.$emit('handelDetails', val);
      },
      handelDetails(val) {
        this.$emit('handelDetails', val);
      },
      pagination_currentChange(val) {
        this.$emit('handlePaginationChange', val, this.pagination.limit);
      },
      pagination_sizeChange(val) {
        this.$emit('handlePaginationChange', this.pagination.pageNo, val);
      },
    },
  };
</script>
<style lang="scss" scoped>
  a {
    color: #606266;
    cursor: default !important;
  }
</style>
