<template>
  <div>
    <div v-if="!['2', '3'].includes(tabType)">
      <el-table
        v-loading="loading"
        show-summary
        :data="data"
        style="width: 100%"
        sum-text="汇总"
        :summary-method="summaryMethod"
      >
        <el-table-column prop="id" label="ID" type="index"></el-table-column>

        <el-table-column label="单据信息" align="center">
          <el-table-column
            prop="platformInfo.serialNo"
            label="单据编号"
          ></el-table-column>
          <el-table-column
            prop="platformInfo.associateSerialNo"
            label="关联编号"
          ></el-table-column>

          <el-table-column
            prop="platformInfo.ownpartyName"
            label="公司名称"
          ></el-table-column>
          <el-table-column
            prop="platformInfo.counterpartyName"
            label="对方名称"
          ></el-table-column>
          <el-table-column
            prop="platformInfo.organizationName"
            label="机构类型"
          ></el-table-column>
          <el-table-column
            prop="platformInfo.organizationAccount"
            label="机构账号"
          ></el-table-column>
          <el-table-column prop="platformInfo.amountCny" label="金额（人民币）">
            <template slot-scope="scope">
              <span>
                {{
                  scope.row.platformInfo &&
                  scope.row.platformInfo.amountCny !== null
                    ? scope.row.platformInfo.amountCny
                    : '-'
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="platformInfo.currencyUnit"
            label="币种"
          ></el-table-column>
          <el-table-column prop="platformInfo.amount" label="金额（原币）">
            <template slot-scope="scope">
              <span>
                {{
                  scope.row.platformInfo &&
                  scope.row.platformInfo.amount !== null
                    ? scope.row.platformInfo.amount
                    : ''
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="platformInfo.rate" label="汇率">
            <template slot-scope="scope">
              <span>
                {{
                  scope.row.platformInfo && scope.row.platformInfo.rate !== null
                    ? scope.row.flowInfo.rate
                    : '-'
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="transType" label="交易类型">
            <template slot-scope="scope">
              <span>{{ scope.row.transType == 0 ? '收款' : '付款' }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="platformInfo.transTime"
            label="交易时间"
          ></el-table-column>
        </el-table-column>
        <el-table-column label="流水信息" align="center">
          <el-table-column
            prop="flowInfo.organizationSerialNo"
            label="机构流水号"
          ></el-table-column>
          <el-table-column
            prop="flowInfo.associateSerialNo"
            label="关联编号"
          ></el-table-column>
          <el-table-column
            prop="flowInfo.ownpartyName"
            label="公司名称"
          ></el-table-column>
          <el-table-column
            prop="flowInfo.organizationName"
            label="机构类型"
          ></el-table-column>
          <el-table-column
            prop="flowInfo.organizationAccount"
            label="机构账号"
          ></el-table-column>
          <el-table-column
            prop="flowInfo.counterpartyName"
            label="对方名称"
          ></el-table-column>
          <el-table-column
            prop="flowInfo.counterpartyOrganizationName"
            label="对方机构类型"
          ></el-table-column>
          <el-table-column
            prop="flowInfo.counterpartyOrganizationAccount"
            label="对方账号"
          ></el-table-column>

          <el-table-column prop="flowInfo.amountCny" label="金额（人民币）">
            <template slot-scope="scope">
              <span>
                {{
                  scope.row.flowInfo && scope.row.flowInfo.amountCny !== null
                    ? scope.row.flowInfo.amountCny
                    : '-'
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="flowInfo.currencyUnit"
            label="币种"
          ></el-table-column>
          <el-table-column prop="flowInfo.amount" label="金额（原币）">
            <template slot-scope="scope">
              <span>
                {{
                  scope.row.flowInfo && scope.row.flowInfo.amount !== null
                    ? scope.row.flowInfo.amount
                    : ''
                }}
              </span>
            </template>
          </el-table-column>

          <el-table-column prop="flowInfo.rate" label="汇率">
            <template slot-scope="scope">
              <span>
                {{
                  scope.row.flowInfo && scope.row.flowInfo.rate !== null
                    ? scope.row.flowInfo.rate
                    : '-'
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="transType" label="交易类型">
            <template slot-scope="scope">
              <span>
                {{ scope.row.transType == 0 ? '收款' : '付款' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="flowInfo.chargeAmount"
            label="手续费金额"
          ></el-table-column>
          <el-table-column
            prop="flowInfo.transTime"
            label="交易时间"
          ></el-table-column>
        </el-table-column>
        <el-table-column prop="remark" label="备注"></el-table-column>
        <el-table-column prop="reconStatus" label="对账状态">
          <template slot-scope="scope">
            <span>
              {{ reconStatusObj[scope.row.reconStatus] }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="['1'].includes(tabType)"
          prop="reconTime"
          label="匹配时间"
        ></el-table-column>
        <!-- <el-table-column
          v-if="['4', '5'].includes(tabType)"
          prop="name"
          label="差异原因"
        ></el-table-column> -->
      </el-table>
      <el-pagination
        ref="listPage"
        background
        :current-page.sync="pagination.pageNo"
        :page-size.sync="pagination.limit"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @current-change="pagination_currentChange"
        @size-change="pagination_sizeChange"
      ></el-pagination>
    </div>

    <div v-if="['2', '3'].includes(tabType)">
      <dynamictable
        :data-source="data"
        :columns="columns"
        :options="{ loading, index: true, indexName: 'ID', showSummary: true }"
        :pagination="pagination"
        :fetch="getApi"
        :summary-method="summaryMethod"
      ></dynamictable>
    </div>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import { changeTwoDecimal } from '@/utils/math';
  const reconStatusObj = {
    0: '匹配',
    1: '流水多单',
    2: '单据多单',
    3: '差异',
  };
  export default {
    components: {
      dynamictable,
    },
    props: {
      data: {
        type: Array,
        default: () => [],
      },
      pagination: {
        type: Object,
        default: null,
      },
      loading: {
        type: Boolean,
        default: false,
      },
      currentRow: {
        type: Object,
        default: null,
      },
      tabType: {
        type: String,
        default: '1',
      },
    },
    data() {
      return {
        reconStatusObj,
        columns: [],
      };
    },
    watch: {
      tabType(value) {
        if (value === '2' || value === '3') {
          this.getColumns(value);
        }
      },
    },
    methods: {
      summaryMethod({ columns, data }) {
        const sums = ['汇总'];
        const sumsArr = ['2', '3'].includes(this.tabType)
          ? this.tabType === '2'
            ? [9, 11]
            : [7, 9]
          : [7, 9, 21, 23];
        columns.forEach((item, index) => {
          const values = data.map(item1 => {
            if (item.property && item.property.indexOf('.') != '-1') {
              const arr = item.property ? item.property.split('.') : [];
              return item1[arr[0]] && Number(item1[arr[0]][arr[1]]);
            }
            return Number(item1[item.property]);
          });
          if (sumsArr.includes(index)) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
          }
        });
        return sums.map(item => changeTwoDecimal(item));
      },
      getApi() {
        const { limit, total, pageSize } = this.pagination;
        this.$emit('onGet', {
          pageSize,
          pageNo: pageSize,
          limit: limit,
          total: total,
        });
      },
      handelDetails(val) {
        this.$emit('handelDetails', val);
      },
      pagination_currentChange(val) {
        this.$emit('handlePaginationChange', val, this.pagination.limit);
      },
      pagination_sizeChange(val) {
        this.$emit('handlePaginationChange', this.pagination.pageNo, val);
      },
      getColumns(value) {
        const columns = [
          {
            prop:
              value === '3'
                ? 'platformInfo.serialNo'
                : 'flowInfo.organizationSerialNo',
            label: value === '3' ? '单据编号' : '机构流水号',
          },
          {
            prop:
              value === '3'
                ? 'platformInfo.associateSerialNo'
                : 'flowInfo.associateSerialNo',
            label: '关联编号',
          },

          {
            prop:
              value === '3'
                ? 'platformInfo.ownpartyName'
                : 'flowInfo.ownpartyName',
            label: '公司名称',
          },
          {
            prop: 'platformInfo.counterpartyName',
            label: '对方名称',
            hide: value === '2',
          },
          {
            prop:
              value === '3'
                ? 'platformInfo.organizationName'
                : 'flowInfo.organizationName',
            label: '机构类型',
          },
          {
            prop:
              value === '3'
                ? 'platformInfo.organizationAccount'
                : 'flowInfo.organizationAccount',
            label: '机构账号',
          },
          {
            prop: 'platformInfo.counterpartyName',
            label: '对方户名',
            hide: value === '3',
          },
          {
            prop: 'platformInfo.counterpartyOrganizationName',
            label: '对方机构类型',
            hide: value === '3',
          },
          {
            prop: 'platformInfo.counterpartyOrganizationAccount',
            label: '对方账号',
            hide: value === '3',
          },
          {
            prop:
              value === '3' ? 'platformInfo.amountCny' : 'flowInfo.amountCny',
            label: '金额（人民币）',
            render: ({ platformInfo = {}, flowInfo = {} }) => (
              <span>
                {value === '3'
                  ? platformInfo && platformInfo.amountCny !== null
                    ? platformInfo.amountCny
                    : '-'
                  : flowInfo && flowInfo.amountCny !== null
                  ? flowInfo.amountCny
                  : '-'}
              </span>
            ),
          },
          {
            prop:
              value === '3'
                ? 'platformInfo.currencyUnit'
                : 'flowInfo.currencyUnit',
            label: '币种',
          },
          {
            prop: value === '3' ? 'platformInfo.amount' : 'flowInfo.amount',
            label: '金额（原币）',
          },
          {
            prop: value === '3' ? 'platformInfo.rate' : 'flowInfo.rate',
            label: '汇率',
            render: ({ platformInfo = {}, flowInfo = {} }) => (
              <span>
                {value === '3'
                  ? platformInfo && platformInfo.rate !== null
                    ? platformInfo.rate
                    : '-'
                  : flowInfo && flowInfo.rate !== null
                  ? flowInfo.rate
                  : '-'}
              </span>
            ),
          },
          {
            prop: 'transType',
            label: '交易类型',
            render: ({ transType }) => (
              <span>{transType === 0 ? '收款' : '付款'}</span>
            ),
          },
          {
            prop: 'flowInfo.chargeAmount',
            label: '手续费金额',
            hide: value === '3',
          },

          {
            prop:
              value === '3' ? 'platformInfo.transTime' : 'flowInfo.transTime',
            label: '交易时间',
          },
          {
            prop: 'remark',
            label: '备注',
            hide: value === '3',
          },
          {
            prop: 'reconStatus',
            label: '对账状态',
            render: ({ reconStatus }) => (
              <span>{reconStatusObj[reconStatus]}</span>
            ),
          },
        ];

        this.columns = columns;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
