<template>
  <div>
    <el-form inline>
      <el-form-item label="账单日期:">
        <el-date-picker
          v-model="searchDate"
          :clearable="false"
          :type="tabType === '1' ? 'daterange' : 'monthrange'"
          range-separator="至"
          :value-format="tabType === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
          :format="tabType === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="贸易主体:">
        <el-input
          v-model="searchParams.salesEntityName"
          placeholder="请输入贸易主体"
          clearable
        />
      </el-form-item>
      <el-form-item label="资金主体:">
        <el-input
          v-model="searchParams.platformCompanyName"
          placeholder="请输入资金主体"
          clearable
        />
      </el-form-item>
      <el-form-item label="机构类型:">
        <el-select
          v-model="searchParams.platformChannelId"
          placeholder="请选择机构类型"
          clearable
        >
          <el-option
            v-for="item in channelList"
            :key="item.key"
            :label="item.val"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="机构账号:">
        <el-input
          v-model="searchParams.platformChannelMerchant"
          placeholder="请输入机构账号"
          clearable
        />
      </el-form-item>
      <el-form-item label="订单编号:">
        <el-input
          v-model="searchParams.salesOrderSn"
          placeholder="请输入订单编号"
          clearable
        />
      </el-form-item>
      <!-- <el-form-item label="支付单号:">
        <el-input
          v-model="searchParams.platformPaymentOrderId"
          placeholder="请输入支付单号"
        />
      </el-form-item> -->
      <el-form-item label="机构流水号:">
        <el-input
          v-model="searchParams.channelOrderId"
          placeholder="请输入机构流水号"
          clearable
        />
      </el-form-item>
      <el-form-item label="交易类型:">
        <el-select
          v-model="searchParams.transType"
          placeholder="请选择交易类型"
          clearable
        >
          <el-option
            v-for="item in tranTypes"
            :key="item.key"
            :label="item.val"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="对账状态:">
        <el-select
          v-model="searchParams.reconStatus"
          placeholder="请选择对账状态"
          clearable
        >
          <el-option
            v-for="item in reconStatusList"
            :key="item.key"
            :label="item.val"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="差异类型:">
        <el-select
          v-model="searchParams.diffType"
          clearable
          placeholder="请选择差异类型"
        >
          <el-option
            v-for="item in diffTypes"
            :key="item.key"
            :label="item.val"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="业务类型:">
        <el-select
          v-model="searchParams.bizType"
          clearable
          placeholder="请选择业务类型"
        >
          <el-option
            v-for="item in bizTypes"
            :key="item.key"
            :label="item.val"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="search">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button style="margin-left: 10px" type="primary" @click="onExport">
          导出
        </el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="list" style="width: 100%">
      <el-table-column prop="id" label="序号" type="index"></el-table-column>
      <el-table-column prop="billDate" label="账期"></el-table-column>
      <el-table-column
        prop="salesEntityName"
        label="贸易主体"
      ></el-table-column>
      <el-table-column
        prop="platformCompanyName"
        label="资金主体"
      ></el-table-column>

      <el-table-column
        prop="platformChannelName"
        label="机构类型"
      ></el-table-column>
      <el-table-column
        prop="platformChannelMerchant"
        label="机构账号"
      ></el-table-column>
      <el-table-column label="平台" align="center">
        <el-table-column
          prop="salesOrderSn"
          label="订单编号"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="platformOrderId"
          label="支付单号"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="salesOrderAmount"
          label="订单实付金额"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="platformAmount"
          label="金额_原币"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="platformCurrencyUnit"
          label="币种"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="platformRate"
          label="汇率"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="platformRmbAmount"
          label="金额_人民币"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="platTransType"
          label="交易类型"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="platformSuccessTime"
          label="实际完成时间"
          width="120"
        ></el-table-column>
      </el-table-column>
      <el-table-column label="渠道" align="center">
        <el-table-column
          prop="channelOrderId"
          label="渠道流水号"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="channelAmount"
          label="金额（原币）"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="channelCurrency"
          label="币种"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="channelRate"
          label="汇率"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="channelRmbAmount"
          label="金额_人民币"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="channelTransType"
          label="交易类型"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="channelTransTime"
          label="交易时间"
          width="120"
        ></el-table-column>
      </el-table-column>
      <el-table-column prop="reconStatus" label="对账状态"></el-table-column>
      <el-table-column prop="diffType" label="差异类型"></el-table-column>
      <el-table-column prop="reconTime" label="对账时间"></el-table-column>
      <el-table-column prop="flatTime" label="平账时间"></el-table-column>
      <el-table-column prop="bizType" label="业务类型"></el-table-column>
      <el-table-column fixed="right" label="备注" width="200">
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.remark"
            placeholder="请输入内容"
            @change="val => handleChange(scope.row, val)"
          ></el-input>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      ref="listPage"
      background
      :current-page.sync="pagination.pageNo"
      :page-size.sync="pagination.limit"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      @current-change="val => handlePaginationChange(val, pagination.limit)"
      @size-change="val => handlePaginationChange(pagination.pageNo, val)"
    ></el-pagination>
  </div>
</template>

<script>
  import { parseTime, setInitData, debounce } from '@/utils';
  import { newExportExcel } from '@/api/blob';
  import {
    getFundsBillDetailList,
    getFundsBillEditRemark,
    getFundsBillSelector,
    fundsBillDetailExport,
    getFundsBillDetailSkipList,
  } from '@/api/reconciliationCenter';
  import dayjs from 'dayjs';

  export default {
    name: 'ReconciliationInformation',
    data() {
      return {
        querySource: false,
        loading: false,
        list: [],
        tabType: '1',
        searchDate: '', // 页面筛选时间
        searchParams: {
          billDateEnd: '',
          billDateStart: '',
          bizType: '',
          channelOrderId: '',
          currency: '',
          diffType: '',
          pageNum: '',
          pageSize: '',
          platformChannelId: '',
          platformChannelMerchant: '',
          platformCompanyName: '',
          platformPaymentOrderId: '',
          reconStatus: '',
          salesEntityName: '',
          salesOrderSn: '',
          statType: '',
          transType: '',
        },

        // 分页
        pagination: {
          pageNo: 1,
          limit: 10,
          total: null,
        },
        channelList: [],
        diffTypes: [],

        reconStatusList: [],
        bizTypes: [],
        tranTypes: [],
      };
    },
    created() {
      const { billDate, tabType, ...query } = this.$route.query;
      if (billDate) {
        this.querySource = true;
      } else {
        this.querySource = false;
      }

      if (billDate) {
        const start = new Date(billDate);
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
        const startDate = dayjs(start).format('YYYY-MM-DD');
        this.searchDate =
          tabType === '2' ? this.initDate(billDate) : [startDate, billDate];
      } else {
        this.searchDate = setInitData(30);
      }
      this.searchParams = {
        ...this.searchParams,
        ...query,
      };
      getFundsBillSelector({}).then(({ res }) => {
        if (res) {
          this.channelList = res.channelList;
          this.diffTypes = res.diffTypes;
          this.bizTypes = res.bizTypes;
          this.tranTypes = res.tranTypes;
          this.reconStatusList = res.reconStatusList;
        }
      });

      this.getList(true);
    },
    methods: {
      search() {
        this.querySource = false;
        this.getList(true);
      },
      initDate(billDate) {
        const startTime = dayjs(billDate).format('YYYY-MM-01');
        const endTime = dayjs(billDate).endOf('month').format('YYYY-MM-DD');

        return [startTime, endTime];
      },
      async handleChange(row, val) {
        console.log(val, 'val');
        const { res, err } = await getFundsBillEditRemark({
          id: row.id,
          remark: val,
        });
        if (!err) {
          this.$message({
            message: '修改成功',
            type: 'success',
          });
        }
      },
      getParams() {
        const { searchDate } = this;
        this.searchParams.billDateStart = searchDate
          ? parseTime(searchDate[0], '{y}-{m}-{d}')
          : '';
        this.searchParams.billDateEnd = searchDate
          ? parseTime(searchDate[1], '{y}-{m}-{d}')
          : '';

        const params = {
          ...this.searchParams,
          pageNum: this.pagination.pageNo,
          pageSize: this.pagination.limit,
        };
        return params;
      },
      async getList(isSearch = false, type) {
        if (isSearch) {
          this.pagination.pageNo = 1;
        }
        const params = this.getParams();
        this.loading = true;

        const listApi = this.querySource
          ? getFundsBillDetailSkipList
          : getFundsBillDetailList;
        const { res, err } = await listApi(params);
        if (res) {
          this.list = res.records;
          this.pagination.total = res.total;
        }
        this.loading = false;
      },

      handlePaginationChange(pageNo, limit) {
        this.pagination.pageNo = pageNo;
        this.pagination.limit = limit;
        this.getList();
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.searchDate = setInitData(30);
      },
      onExport: debounce(async function () {
        const params = this.getParams();
        const { res, err } = await fundsBillDetailExport(params);
        if (res) {
          this.$message({
            message: res,
            type: 'success',
          });
        }
      }, 1000),
    },
  };
</script>
<style lang="scss" scoped></style>
