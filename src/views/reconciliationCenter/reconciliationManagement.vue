<template>
  <div>
    <div v-if="tabPage === 1">
      <el-tabs :key="1" v-model="tabType" type="card" @tab-click="handleClick">
        <el-tab-pane label="日汇总" name="1" />
        <el-tab-pane label="月汇总" name="2" />
      </el-tabs>
      <el-form inline>
        <el-form-item v-if="tabType === '1'" label="账单日期:">
          <el-date-picker
            v-model="searchDate"
            :clearable="false"
            type="daterange"
            range-separator="至"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          ></el-date-picker>
        </el-form-item>
        <el-form-item v-if="tabType === '2'" label="日期:">
          <el-date-picker
            v-model="searchDate"
            :clearable="false"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            value-format="yyyy-MM"
            format="yyyy-MM"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="公司名称:">
          <el-input
            v-model="searchParams.merchantName"
            placeholder="请输入公司名称"
          />
        </el-form-item>
        <el-form-item label="对账类型:">
          <el-select
            v-model="searchParams.bizType"
            placeholder="请选择对账类型"
            @change="handlebizType"
          >
            <el-option label="全部" value=""></el-option>
            <el-option
              v-for="item in bizTypeList"
              :key="item.bizType"
              :label="item.bizTypeName"
              :value="item.bizType"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="机构类型:">
          <el-select
            v-model="searchParams.channelId"
            placeholder="请选择机构类型"
          >
            <el-option label="全部" value=""></el-option>
            <el-option
              v-for="item in channelList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="机构账号:">
          <el-input
            v-model="searchParams.paymentChannelMerchantId"
            placeholder="请输入渠道账号"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="getList(true)">查询</el-button>
          <el-button type="primary" @click="onReset">重置</el-button>
          <el-popover placement="bottom" trigger="click">
            <p>请选择导出时间</p>
            <el-date-picker
              v-model="exportDate"
              :clearable="false"
              :type="tabType === '1' ? 'daterange' : 'monthrange'"
              range-separator="至"
              :value-format="tabType === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
              :format="tabType === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :picker-options="pickerOptions"
            ></el-date-picker>
            <div style="text-align: right; margin: 10px 0 0">
              <el-button type="primary" size="mini" @click="onExport">
                确定
              </el-button>
            </div>
            <el-button
              slot="reference"
              style="margin-left: 10px"
              type="primary"
            >
              导出
            </el-button>
          </el-popover>
        </el-form-item>
      </el-form>
      <dataTable
        :loading="loading"
        :pagination="pagination"
        :data="list"
        :tab-type="tabType"
        @handlePaginationChange="handlePaginationChange"
        @handelDetails="(val, tabType) => handelJump(2, val, tabType)"
      ></dataTable>
    </div>

    <div v-if="tabPage === 2">
      <div>
        <el-button
          style="font-size: 16px; margin-bottom: 10px"
          type="text"
          @click="handelJump(1)"
        >
          返回
        </el-button>
      </div>

      <el-tabs :key="2" v-model="activeName" type="card" @tab-click="handleTab">
        <el-tab-pane label="对账信息" name="1" />
        <el-tab-pane label="单据多单" name="3" />
        <el-tab-pane label="流水多单" name="2" />
        <el-tab-pane label="差异信息" name="4" />
      </el-tabs>
      <el-form inline>
        <el-form-item
          v-if="['1', '3', '4'].includes(activeName)"
          label="单据编号:"
        >
          <el-input
            v-model="detailsParams.rpSerialNo"
            placeholder="请输入单据编号"
          />
        </el-form-item>
        <el-form-item label="关联编号:">
          <el-input
            v-model="detailsParams.associateSerialNo"
            placeholder="请输入关联编号"
          />
        </el-form-item>
        <el-form-item label="公司名称:">
          <el-input
            v-model="detailsParams.ownpartyName"
            disabled
            placeholder="请输入公司名称"
          />
        </el-form-item>
        <el-form-item label="对方名称:">
          <el-input
            v-model="detailsParams.counterpartyName"
            placeholder="请输入对方名称"
          />
        </el-form-item>
        <el-form-item label="机构类型:">
          <el-select
            v-model="detailsParams.organizationType"
            disabled
            placeholder="请选择机构类型"
          >
            <el-option label="全部" value=""></el-option>
            <el-option
              v-for="item in channelList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="机构账号:">
          <el-input
            v-model="detailsParams.organizationAccount"
            disabled
            placeholder="请输入机构账号"
          />
        </el-form-item>
        <el-form-item
          v-if="['1', '4'].includes(activeName)"
          label="机构流水号:"
        >
          <el-input
            v-model="detailsParams.organizationSerialNo"
            placeholder="请输入机构流水号"
          />
        </el-form-item>
        <el-form-item label="交易类型:">
          <el-select
            v-model="searchParams.transType"
            placeholder="请选择交易类型"
          >
            <el-option label="全部" value=""></el-option>
            <el-option label="收款" :value="0"></el-option>
            <el-option label="付款" :value="1"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="getDetailsList(true)">
            查询
          </el-button>
          <el-button type="primary" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
      <informationTable
        :loading="loading"
        :pagination="pagination"
        :data="list"
        :tab-type="activeName"
        :current-row="currentRow"
        @onGet="onGet"
        @handlePaginationChange="handlePaginationChange"
      ></informationTable>
    </div>
  </div>
</template>

<script>
  import dataTable from './components/dataTable';
  import informationTable from './components/informationTable';
  import {
    parseTime,
    initSearchParams,
    setInitData,
    downloadFile,
    debounce,
    getMonthFirstOrLaseDay,
  } from '@/utils';
  import { exportExcel } from '@/api/blob';
  import {
    getMonthPageList,
    getDayPageList,
    payChannelList,
    reconFlatPage,
    reconDifferencePage,
    reconCashShortPage,
    reconCashOverPage,
    reconFusanPage,
  } from '@/api/receivableManagement';

  export default {
    components: {
      dataTable,
      informationTable,
    },
    data() {
      return {
        tabPage: 1, // 页面类型
        loading: false,
        list: [],
        tabType: '1',
        activeName: '1',
        searchDate: '', // 页面筛选时间
        exportDate: '', // 导出时间

        choiceDate0: '',
        pickerOptions: {
          onPick: ({ maxDate, minDate }) => {
            this.choiceDate0 = minDate.getTime();
            if (maxDate) {
              this.choiceDate0 = '';
            }
          },
          disabledDate: time => {
            if (this.choiceDate0 !== '') {
              const one = 30 * 24 * 3600 * 1000;
              const minTime = this.choiceDate0 - one;
              const maxTime = this.choiceDate0 + one;
              return time.getTime() < minTime || time.getTime() > maxTime;
            }
          },
        },

        searchParams: {
          bizType: '',
          beginBillDate: '',
          endBillDate: '',
          channelId: '',
          paymentChannelMerchantId: '',
          merchantName: '',
        },
        detailsParams: {
          associateSerialNo: '',
          counterpartyName: '',
          organizationAccount: '',
          organizationSerialNo: '',
          organizationType: '',
          ownpartyName: '',
          reconStatus: 0,
          bizType: 0,
          rpSerialNo: '',
          transType: '',
        },
        currentRow: null, // 当前数据列
        pageNo: 1,
        // 分页
        pagination: {
          pageNo: 1,
          limit: 10,
          total: null,
        },
        channelList: [],
        bizTypeList: [],
      };
    },
    created() {
      this.searchDate = setInitData(30);
      this.exportDate = setInitData(30);
      payChannelList({}).then(res => {
        if (res) {
          this.bizTypeList = res;
        }
      });
      this.getList(true);
    },
    methods: {
      onGet(pagination) {
        this.pagination = pagination;
        this.getDetailsList();
      },
      handlebizType(val) {
        if (typeof val !== 'undefined') {
          this.channelList = this.bizTypeList[val]?.selectorVOS;
        }
      },
      handelJump(val, row) {
        // 月总汇跳转到日
        if (this.tabType === '2') {
          const dateArr = row.billDate ? row.billDate.split('-') : [];
          const searchDate = getMonthFirstOrLaseDay(dateArr[1]);
          this.searchDate = [searchDate.firstDay, searchDate.lastDay];
          this.tabType = '1';
          this.searchParams.bizType = row.bizType;
          this.searchParams.channelId = row.channelId;
          this.searchParams.paymentChannelMerchantId =
            row.paymentChannelMerchantId;
          this.handlebizType(row.bizType);
          this.getList();
          return;
        }
        this.tabPage = val;
        this.currentRow = row;
        this.handlebizType(row ? row.bizType : this.searchParams.bizType);
        if (val === 1) {
          // 返回记住页数
          this.pagination.pageNo = this.pageNo;
          this.getList();
        } else {
          this.init();
          this.activeName = '1';
          const detailsParams = {
            bizType: row.bizType,
            billDate: row.billDate,
            ownpartyName: row.merchantName,
            organizationType: row.channelId,
            organizationAccount: row.paymentChannelMerchantId,
          };
          this.detailsParams = {
            ...this.detailsParams,
            ...detailsParams,
          };
          this.detailsParams = detailsParams;
          this.getDetailsList();
        }
      },
      getchannelId(val) {
        let channelId = '';
        this.channelList.map(item => {
          if (val === item.name) {
            channelId = item.id;
          }
        });
        return channelId;
      },
      init() {
        this.pageNo = this.pagination.pageNo;
        Object.assign(this.$data.pagination, this.$options.data().pagination);
      },
      getParams(flag) {
        const time = flag ? this.exportDate : this.searchDate;
        this.searchParams.beginBillDate = time
          ? parseTime(time[0], this.tabType === '1' ? '{y}-{m}-{d}' : '{y}-{m}')
          : '';
        this.searchParams.endBillDate = time
          ? parseTime(time[1], this.tabType === '1' ? '{y}-{m}-{d}' : '{y}-{m}')
          : '';

        const params = {
          ...this.searchParams,
          current: this.pagination.pageNo,
          size: this.pagination.limit,
        };
        return params;
      },
      async getList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageNo = 1;
        }
        const params = this.getParams();
        this.loading = true;
        const api = this.tabType === '1' ? getDayPageList : getMonthPageList;
        const res = await api(params);
        if (res) {
          this.list = res.list;
          this.pagination.total = res.total;
        }
        this.loading = false;
      },
      async getDetailsList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageNo = 1;
        }
        let params = {
          ...this.detailsParams,
          reconStatus: Number(this.activeName) - 1,
          current: this.pagination.pageNo,
          size: this.pagination.limit,
        };
        this.loading = true;

        const res = await reconFlatPage(initSearchParams(params));
        if (res) {
          this.list = res.records;
          this.pagination.total = res.total;
        }
        this.loading = false;
      },
      handlePaginationChange(pageNo, limit) {
        this.pagination.pageNo = pageNo;
        this.pagination.limit = limit;
        if (this.tabPage === 1) {
          this.getList();
        } else {
          this.getDetailsList();
        }
      },
      onReset() {
        if (this.tabPage === 1) {
          Object.assign(
            this.$data.searchParams,
            this.$options.data().searchParams,
          );
          this.setInit(this.tabType);
          // this.getList(true);
        } else {
          Object.assign(
            this.$data.detailsParams,
            this.$options.data().detailsParams,
          );
          // this.getDetailsList(true);
        }
      },
      setInit(val) {
        const data = setInitData(30);
        if (val == '1') {
          this.exportDate = data;
          this.searchDate = data;
        } else {
          this.exportDate = [data[0], data[0]];
          this.searchDate = [data[0], data[0]];
        }
      },
      handleClick(val) {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );

        this.setInit(val.name);

        this.tabType = val.name;
        this.channelList = [];

        this.getList(true);
      },
      handleTab(val) {
        // Object.assign(
        //   this.$data.detailsParams,
        //   this.$options.data().detailsParams,
        // );
        this.activeName = val.name;
        this.getDetailsList(true);
      },
      onExport: debounce(function () {
        const exportApi =
          this.tabType === '1'
            ? '/reconStatistics/dayPage/export'
            : '/reconStatistics/monthPage/export';
        const params = this.getParams(true);
        exportExcel(params, `/api/receivable${exportApi}`, 'get').then(res => {
          downloadFile(res.data, '渠道对账管理列表');
        });
      }, 1000),
    },
  };
</script>
<style lang="scss" scoped></style>
