<template>
  <div>
    <el-tabs :key="1" v-model="tabType" type="card" @tab-click="handleClick">
      <el-tab-pane label="日汇总" name="1" />
      <el-tab-pane label="月汇总" name="2" />
    </el-tabs>
    <el-form inline>
      <el-form-item label="账单日期:">
        <el-date-picker
          v-model="searchDate"
          :clearable="false"
          :type="tabType === '1' ? 'daterange' : 'monthrange'"
          range-separator="至"
          :value-format="tabType === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
          :format="tabType === '1' ? 'yyyy-MM-dd' : 'yyyy-MM'"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="贸易主体:">
        <el-input
          v-model="searchParams.salesEntityName"
          placeholder="请输入贸易主体"
          clearable
        />
      </el-form-item>
      <el-form-item label="资金主体:">
        <el-input
          v-model="searchParams.platformCompanyName"
          placeholder="请输入资金主体"
          clearable
        />
      </el-form-item>
      <el-form-item label="机构类型:">
        <el-select
          v-model="searchParams.platformChannelId"
          placeholder="请选择机构类型"
          clearable
        >
          <el-option
            v-for="item in channelList"
            :key="item.key"
            :label="item.val"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="机构账号:">
        <el-input
          v-model="searchParams.platformChannelMerchant"
          placeholder="请输入机构账号"
          clearable
        />
      </el-form-item>
      <el-form-item label="币种:">
        <el-select
          v-model="searchParams.currency"
          clearable
          placeholder="请选择币种"
        >
          <el-option
            v-for="item in currencyList"
            :key="item.key"
            :label="item.val"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button type="primary" @click="onExport">导出</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="list" style="width: 100%">
      <el-table-column prop="id" label="序号" type="index"></el-table-column>
      <el-table-column prop="billDate" label="账单日期"></el-table-column>
      <el-table-column
        prop="salesEntityName"
        label="贸易主体"
      ></el-table-column>
      <el-table-column
        prop="platformCompanyName"
        label="资金主体"
      ></el-table-column>

      <el-table-column
        prop="platformChannelName"
        label="机构类型"
      ></el-table-column>
      <el-table-column
        prop="platformChannelMerchant"
        label="机构账号"
      ></el-table-column>
      <el-table-column prop="currencyUnit" label="币种"></el-table-column>
      <el-table-column label="平台" align="center">
        <el-table-column
          prop="platInRmbAmount"
          label="收入_人民币"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="platOutRmbAmount"
          label="支出_人民币"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="platInSourceAmount"
          label="收入_原币"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="platOutSourceAmount"
          label="支出_原币"
          width="120"
        ></el-table-column>
      </el-table-column>
      <el-table-column label="渠道" align="center">
        <el-table-column
          prop="channelInSourceAmount"
          label="收入_原币"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="channelOutSourceAmount"
          label="支出_原币"
          width="120"
        ></el-table-column>
      </el-table-column>
      <el-table-column
        :label="tabType === '1' ? '当日平账' : '当月平账'"
        align="center"
      >
        <el-table-column
          prop="curFlatInAmount"
          label="收入_原币"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="curFlatOutAmount"
          label="支出_原币"
          width="120"
        ></el-table-column>
      </el-table-column>
      <el-table-column
        :label="tabType === '1' ? '当日差异' : '当月差异'"
        align="center"
      >
        <el-table-column
          prop="curUnflatInAmount"
          label="收入_原币"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="curUnflatOutAmount"
          label="支出_原币"
          width="120"
        ></el-table-column>
      </el-table-column>
      <el-table-column label="累计平账" align="center">
        <el-table-column
          prop="allFlatInAmount"
          label="收入_原币"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="allFlatOutAmount"
          label="支出_原币"
          width="120"
        ></el-table-column>
      </el-table-column>
      <el-table-column label="累计差异" align="center">
        <el-table-column
          prop="allUnflatInAmount"
          label="收入_原币"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="allUnflatOutAmount"
          label="支出_原币"
          width="120"
        ></el-table-column>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="100">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handelJump(scope.row)">
            明细
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      ref="listPage"
      background
      :current-page.sync="pagination.pageNo"
      :page-size.sync="pagination.limit"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      @current-change="val => handlePaginationChange(val, pagination.limit)"
      @size-change="val => handlePaginationChange(pagination.pageNo, val)"
    ></el-pagination>
  </div>
</template>

<script>
  import { parseTime, setInitData, downloadFile, debounce } from '@/utils';
  import { newExportExcel } from '@/api/blob';
  import {
    getFundsBillList,
    getFundsBillSelector,
  } from '@/api/reconciliationCenter';

  export default {
    name: 'OrderChannelReconciliationMgt',
    data() {
      return {
        loading: false,
        list: [],
        tabType: '1',
        searchDate: '', // 页面筛选时间
        searchParams: {
          billDateEnd: '',
          billDateStart: '',
          bizType: '',
          channelOrderId: '',
          currency: '',
          diffType: '',
          pageNum: '',
          pageSize: '',
          platformChannelId: '',
          platformChannelMerchant: '',
          platformCompanyName: '',
          platformPaymentOrderId: '',
          reconStatus: '',
          salesEntityName: '',
          salesOrderSn: '',
          statType: '',
          transType: '',
        },

        // 分页
        pagination: {
          pageNo: 1,
          limit: 10,
          total: null,
        },
        channelList: [],
        currencyList: [],
      };
    },
    created() {
      this.searchDate = setInitData(30);

      getFundsBillSelector({}).then(({ res }) => {
        if (res) {
          this.channelList = res.channelList;
          this.currencyList = res.currencyList;
        }
      });
      this.getList(true);
    },
    methods: {
      handelJump(row) {
        const {
          billDate,
          salesEntityName,
          platformCompanyName,
          platformChannelId,
          platformChannelMerchant,

          currencyUnit,
        } = row;
        this.$router.push({
          path: 'reconciliationInformation',
          query: {
            billDate,
            salesEntityName,
            platformCompanyName,
            platformChannelId,
            platformChannelMerchant,
            currency: currencyUnit,
            tabType: this.tabType,
          },
        });
      },

      getParams(flag) {
        const time = this.searchDate;
        this.searchParams.billDateStart = time
          ? parseTime(time[0], this.tabType === '1' ? '{y}-{m}-{d}' : '{y}-{m}')
          : '';
        this.searchParams.billDateEnd = time
          ? parseTime(time[1], this.tabType === '1' ? '{y}-{m}-{d}' : '{y}-{m}')
          : '';

        const params = {
          ...this.searchParams,
          statType: this.tabType === '1' ? 'D' : 'M',
          pageNum: this.pagination.pageNo,
          pageSize: this.pagination.limit,
        };
        return params;
      },
      async getList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageNo = 1;
        }
        const params = this.getParams();
        this.loading = true;
        const { res, err } = await getFundsBillList(params);
        if (res && !err) {
          this.list = res.records;
          this.pagination.total = res.total;
        }
        this.loading = false;
      },

      handlePaginationChange(pageNo, limit) {
        this.pagination.pageNo = pageNo;
        this.pagination.limit = limit;
        this.getList();
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.setInit(this.tabType);
      },
      setInit(val) {
        const data = setInitData(30);
        if (val == '1') {
          this.searchDate = data;
        } else {
          this.searchDate = [data[0], data[0]];
        }
      },
      handleClick(val) {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.setInit(val.name);
        this.tabType = val.name;
        this.getList(true);
      },

      onExport: debounce(function () {
        const params = this.getParams(true);
        newExportExcel(
          params,
          `/api/recon-core/sales/funds/bill/stat/export`,
          'post',
        ).then(res => {
          downloadFile(res.data, '渠道对账管理列表');
        });
      }, 1000),
    },
  };
</script>
<style lang="scss" scoped></style>
