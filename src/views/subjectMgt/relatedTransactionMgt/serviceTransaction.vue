<template>
  <div class="serviceTransaction">
    <serviceTransactionSearch @onSearch="onSearch"></serviceTransactionSearch>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <el-button
          slot="reference"
          type="text"
          size="small"
          @click="handleViewLog(scope.row)"
        >
          查看日志
        </el-button>
      </template>
    </dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import serviceTransactionSearch from './components/serviceTransactionSearch';
  import { getQueryLinkView } from '@/api/subjectMgt';
  export default {
    components: {
      dynamictable,
      serviceTransactionSearch,
    },
    data() {
      return {
        search: {},
        list: [],
        columns: [
          {
            prop: 'tradeType2Desc',
            label: '二级交易',
          },
          {
            prop: 'tradeType3Desc',
            label: '三级',
          },
          {
            prop: 'providerCompanyName',
            label: '服务提供方',
          },
          {
            prop: 'consumerCompanyName',
            label: '服务接收方',
          },
          {
            prop: 'settlementDate',
            label: '结算发起时间',
          },
          {
            prop: 'paymentDate',
            label: '付款日期',
          },
          {
            prop: 'entryDate',
            label: '入账日期',
          },
          {
            prop: 'expireTime',
            label: '失效日期',
          },
          {
            prop: 'period',
            label: '结算周期',
          },
          {
            prop: 'statusDesc',
            label: '状态',
          },
          {
            prop: 'remark',
            label: '备注',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '200',
            scopedSlots: { customRender: 'operation' },
          },
        ],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
      };
    },
    // created() {
    //   this.getList(true)
    // },
    methods: {
      handleViewLog(val) {
        this.$router.push({
          name: 'serviceTransactionLog',
          query: {
            tradeType2: val.tradeType2,
            tradeType3: val.tradeType3,
            providerCompanyCode: val.providerCompanyCode,
            consumerCompanyCode: val.consumerCompanyCode,
            logic: this.search.logic || 1,
            status: val.status,
          },
        });
      },
      onSearch(e) {
        this.search = e;
        this.getList(true);
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = {
          logic: 1,
          ...this.search,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        this.options.loading = true;
        const res = await getQueryLinkView(params);
        this.options.loading = false;
        this.list = res ? res.records : [];
        this.pagination.total = res ? res.total : 0;
      },
    },
  };
</script>
<style lang="scss"></style>
