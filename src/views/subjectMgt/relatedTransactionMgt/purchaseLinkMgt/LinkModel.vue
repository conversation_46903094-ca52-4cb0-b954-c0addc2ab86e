<template>
  <div class="LinkModel">
    <el-form inline>
      <el-row>
        <el-col :span="8">
          <el-form-item label="状态:">
            <el-select
              v-model="searchParams.linkModelStatus"
              placeholder="请选择状态"
            >
              <el-option
                v-for="item in statusList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4" :offset="12">
          <el-form-item>
            <el-button type="primary" @click="getList(true)">查询</el-button>
            <!-- <el-button type="primary" @click="showDialog = true">
              新建
            </el-button> -->
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div></div>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <!-- <template slot="operation" slot-scope="scope">
        <el-button
          slot="reference"
          type="text"
          size="small"
          @click="
            showDialog = true
            currentRecord = scope.row
          "
        >
          编辑
        </el-button>
        <el-button slot="reference" type="text" size="small" @click="submit">
          提交
        </el-button>
      </template> -->
    </dynamictable>
    <addLinkModel
      v-model="showDialog"
      :record="currentRecord"
      :type-list="typeList"
      :subject-positioning-list="subjectPositioningList"
    ></addLinkModel>
    <!-- <el-dialog width="400px" :visible.sync="uploadDialog">
      <div style="text-align: center">
        <uploadImg
          ref="uploadFile"
          btn-text="上传文件"
          list-type="text"
          :init-file-list="initFile"
          :type-file="[]"
          @changeImage="changeFile"
          @onRemove="(a, b) => onRemove(a, b, 1)"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="uploadDialog = false">取 消</el-button>
        <el-button type="primary">确 认</el-button>
      </div>
    </el-dialog> -->
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import uploadImg from '@/components/uploadImg';
  import addLinkModel from '../components/addLinkModel';
  import {
    getDataDictAll,
    getCategoryList,
    submitLinkModel,
    linkCompanyList,
    getLinkModelList,
  } from '@/api/subjectMgt';
  export default {
    components: {
      dynamictable,
      addLinkModel,
      // uploadImg,
    },
    data() {
      return {
        searchParams: {
          linkModelStatus: '',
        },
        uploadDialog: false,
        showDialog: false,
        currentRecord: null,
        initFile: [],
        statusList: [],
        typeList: [],
        subjectPositioningList: [],
        list: [],
        columns: [
          {
            prop: 'id',
            label: '编码',
          },
          {
            prop: 'name',
            label: '链路名称',
          },
          {
            prop: 'typeDesc',
            label: '类型',
          },
          {
            prop: 'companyPositionDesc',
            label: '链路(销-购)',
            render: ({ companyPositionDesc }) => {
              return (companyPositionDesc || []).map((item, index) => (
                <span>
                  <span> {item}</span>
                  {index != companyPositionDesc.length - 1 ? (
                    <span>-</span>
                  ) : (
                    ''
                  )}
                </span>
              ));
            },
          },
          {
            prop: 'statusDesc',
            label: '状态',
          },
          {
            prop: 'attachmentsDesc',
            label: '附件',
            render: ({ attachmentsDesc }) => {
              return (attachmentsDesc || []).map(item => <span> {item}</span>);
            },
          },
          // {
          //   prop: 'operation',
          //   label: '操作',
          //   fixed: 'right',
          //   width: '200',
          //   scopedSlots: { customRender: 'operation' },
          // },
        ],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
      };
    },
    created() {
      getDataDictAll().then(res => {
        if (res) {
          this.statusList = this.getTypeList(res.LINK_MODE_STATUS);
          this.typeList = this.getTypeList(res.LINK_MODE_TYPE);
          this.subjectPositioningList = this.getTypeList(res.LINK_TYPE);
        }
      });
      this.getList(true);
    },
    methods: {
      getTypeList(obj) {
        if (!obj) return [];
        return Object.keys(obj).map(item => {
          return {
            id: Number(item) ? Number(item) : item,
            name: obj[item],
          };
        });
      },
      changeFile() {},
      onRemove() {},
      submit({ id }) {
        this.$confirm('是否确认提交新链路模型，系统将会自动创建OA审批流程', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          await submitLinkModel({ id });
          this.$message({
            type: 'success',
            message: '提交成功!',
          });
        });
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = {
          ...this.searchParams,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        this.options.loading = true;
        const res = await getLinkModelList(params);
        this.options.loading = false;
        this.list = res ? res.records : [];
        this.pagination.total = res ? res.total : 0;
      },
    },
  };
</script>
<style lang="scss"></style>
