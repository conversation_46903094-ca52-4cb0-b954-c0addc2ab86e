<template>
  <div class="linkPricingConfiguration">
    <div class="top">
      <div>
        <span style="margin-right: 20px">
          品牌: {{ linkInfo.brandName || '--' }}
        </span>
        <span style="margin-right: 20px">
          品类: {{ linkInfo.categoryName || '--' }}
        </span>
        <span>链路类型: {{ linkInfo.linkTypeStr || '--' }}</span>
      </div>
      <div>
        <ac-permission-button
          type="primary"
          btn-text="新建"
          permission-key="linkPricingConfiguration-add"
          @click="handleAddEdit(false)"
        ></ac-permission-button>
        <el-button @click="goBack">返回</el-button>
      </div>
    </div>
    <!-- <div class="date-line">
      <div
        v-for="(item, index) in dataLine"
        :key="index"
        :style="styles"
        class="line-item"
      >
        <span class="date">{{ item.time }}</span>
      </div>
    </div> -->

    <el-table v-loading="loading" :data="list" style="width: 100%">
      <el-table-column prop="effectiveDate" label="生效日期"></el-table-column>
      <el-table-column prop="priceMethod" label="定价方法"></el-table-column>
      <el-table-column :render-header="renderHeader">
        <template v-for="(column, index) in tableCol">
          <el-table-column
            v-if="index != tableCol.length - 1"
            :key="index"
            width="300"
            :prop="column.prop"
          >
            <template slot-scope="scope">
              {{ getTableText(scope.row.priceNodeResps, index) }}
            </template>
          </el-table-column>
        </template>
      </el-table-column>
      <el-table-column prop="responsible" label="责任人"></el-table-column>
      <el-table-column prop="effectiveDate" label="操作日期"></el-table-column>
      <el-table-column prop="attachmentUrl" label="附件">
        <template slot-scope="{ row }">
          <a
            v-if="row.attachmentUrl"
            :href="row.attachmentUrl"
            rel="noopener noreferrer"
            download=""
            target="_blank"
          >
            附件
          </a>
          <a v-else>未上传</a>
        </template>
      </el-table-column>

      <el-table-column label="操作" fixed="right" width="120">
        <template slot-scope="scope">
          <ac-permission-button
            type="text"
            size="small"
            :disabled="isDis(scope.row)"
            btn-text="编辑"
            permission-key="linkPricingConfiguration-edit"
            @click="handleAddEdit(scope.row)"
          ></ac-permission-button>
        </template>
      </el-table-column>
    </el-table>

    <editLinkPricingConfiguration
      ref="editLinkPricingConfiguration"
      :purchase-sale-id="Number(searchParams.purchaseSaleId)"
      @onGet="getList"
    ></editLinkPricingConfiguration>
  </div>
</template>

<script>
  import {
    listPriceConfigByPurchaseSaleId,
    getLinkPriceNodeList,
  } from '@/api/subjectMgt';
  import editLinkPricingConfiguration from '../components/editLinkPricingConfiguration';
  import { tenBitTimestamp } from '@/utils';
  import { LOCAL_STORAGE_KEY } from '@/consts/subjectMgt';
  export default {
    components: {
      editLinkPricingConfiguration,
    },
    data() {
      const linkInfo =
        JSON.parse(localStorage.getItem(LOCAL_STORAGE_KEY.linkInfo)) || {};
      return {
        searchParams: {
          purchaseSaleId: '',
        },
        tableCol: [],
        linkInfo,
        saveform: {
          value1: '',
          value2: '',
        },
        styles: {
          width: '20%',
        },
        dataLine: [],
        list: [],
        loading: false,
      };
    },
    created() {
      this.searchParams.purchaseSaleId = this.$route.query.id;
      this.getList();
    },

    methods: {
      isDis({ effectiveDate }) {
        if (effectiveDate) {
          const selectTime = new Date(
            Date.parse(effectiveDate.replace(/-/g, '/')),
          );
          return selectTime < Date.now();
        }
        return false;
      },
      goBack() {
        localStorage.removeItem(LOCAL_STORAGE_KEY.linkInfo);
        this.$router.go(-1);
      },
      getTableText(arr, index) {
        let text;
        arr.map((item, inx) => {
          if (inx !== 0 && inx == index + 1) {
            text = `实际加成率：${item.actualBonusRate}% 理论加成率 ${item.theoryBonusRate}%`;
          }
        });
        return text;
      },
      renderHeader() {
        return (
          <div class="customize-header">
            {this.tableCol.map((item, inx) => {
              return (
                <span class={inx % 2 !== 0 ? 'table-border' : ''}>
                  {item.companyName}
                </span>
              );
            })}
          </div>
        );
      },
      handleAddEdit(val) {
        this.getLinkPriceNodeList(val).then(res => {
          this.$refs['editLinkPricingConfiguration'].open(
            val,
            res.transactionVOS,
          );
        });
      },
      async getLinkPriceNodeList({ id }) {
        let params = {
          ...this.searchParams,
        };
        if (id) {
          params.linkPriceConfigId = id;
        }
        return await getLinkPriceNodeList(params);
      },
      getMaxPriceNodeResps(arr) {
        const lenArr = arr.map(item =>
          Array.isArray(item.priceNodeResps) ? item.priceNodeResps.length : 0,
        );
        const maxNum = Math.max.apply(null, lenArr);

        const obj = arr.find((item, index) => {
          if (Array.isArray(item.priceNodeResps)) {
            return item.priceNodeResps.length === maxNum;
          }
          return index === 0;
        });
        return obj;
      },
      async getList() {
        this.loading = true;
        const res = await listPriceConfigByPurchaseSaleId(this.searchParams);
        if (res) {
          if (Array.isArray(res) && res.length > 0) {
            // const obj = res[0]
            const obj = this.getMaxPriceNodeResps(res);
            if (Array.isArray(obj.priceNodeResps)) {
              this.tableCol = obj.priceNodeResps;
              this.renderHeader();
            }
          }
          this.list = res;
        } else {
          this.list = [];
        }
        this.loading = false;
        // const date = [
        //   {
        //     time: '2019-08-01',
        //   },
        //   {
        //     time: '2020-01-01',
        //   },
        //   {
        //     time: '2020-05-01',
        //   },
        //   {
        //     time: '2020-07-01',
        //   },
        //   {
        //     time: '2021-05-01',
        //   },
        // ]
        // this.dataLine = this.getDateLine(res)
        // this.list = res ? res : []
      },
      getDateLine(list) {
        // const allTimestamp = list.reduce((prev, cur) => {
        //   let date = new Date(cur.time)
        //   let timestamp = date.getTime()
        //   return prev + timestamp
        // }, 0)

        const timestampArr = list.map(item => {
          let date = new Date(item.time);
          let timestamp = date.getTime();
          return timestamp;
        });

        const maxNum = Math.max.apply(null, timestampArr);
        const minNum = Math.min.apply(null, timestampArr);

        const arr = list.map(item => {
          let date = new Date(item.time);
          let timestamp = date.getTime();

          const proportion = timestamp / (maxNum - minNum);

          return {
            ...item,
            proportion,
          };
        });
        return arr;
        // console.log(arr, 'arr;;;;')
      },
    },
  };
</script>
<style lang="scss">
  .linkPricingConfiguration {
    .customize-header {
      display: flex;
      .table-border {
        position: relative;
        height: 100%;
        padding: 7.5px 0;
        border-left: 1px solid #ebeef5;
        border-right: 1px solid #ebeef5;
      }
      span {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
      }
    }
    .el-table .is-group tr:nth-child(2) {
      display: none;
    }
    .el-table th:nth-child(3) {
      padding: 0;
      .cell {
        padding: 0;
      }
    }
    .date-line {
      width: 100%;
      height: 50px;
      background: #1890ff;
      margin-bottom: 50px;
      display: flex;
      .line-item {
        height: 50px;
        position: relative;
        .date {
          position: absolute;
          right: -30px;
          bottom: -30px;
        }
        &::after {
          content: '';
          position: absolute;
          top: 0px;
          right: 0px;
          width: 1px;
          height: 55px;
          background: #1890ff;
        }
      }
    }
    .top {
      padding: 20px;
      margin-bottom: 10px;
      border: 1px solid #dcdfe6;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
</style>
