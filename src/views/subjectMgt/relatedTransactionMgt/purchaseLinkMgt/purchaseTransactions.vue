<template>
  <div class="purchaseTransactions">
    <el-form inline>
      <el-row>
        <el-col :span="8">
          <el-form-item label="二级交易:">
            <el-select v-model="searchParams.tradeType2" placeholder="请选择">
              <el-option
                v-for="item in BRAND"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="三级交易:">
            <el-select v-model="searchParams.tradeType3" placeholder="请选择">
              <el-option
                v-for="item in CATEGORY"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="状态:">
            <el-select v-model="searchParams.status" placeholder="请选择">
              <el-option
                v-for="item in AFF_TRANS_STATUS"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-form-item :label="searchTab === '1' ? '销/购方:' : '销方:'">
          <el-select
            v-model="searchParams.sellerCompanyCode"
            filterable
            placeholder="请选择"
          >
            <el-option
              v-for="item in COMPANY_MAIN"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-radio-group v-model="searchTab" style="margin: 0 10px">
          <el-radio-button label="0">并且</el-radio-button>
          <el-radio-button label="1">或者</el-radio-button>
        </el-radio-group>

        <el-form-item v-if="searchTab === '0'" label="购方:">
          <el-select
            v-model="searchParams.buyerCompanyCode"
            filterable
            placeholder="请选择"
          >
            <el-option
              v-for="item in COMPANY_MAIN"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            :disabled="gxId ? true : false"
            type="primary"
            @click="getList(true)"
          >
            查询
          </el-button>
          <el-button
            :disabled="gxId ? true : false"
            type="primary"
            @click="onReset"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>

    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="gxId ? null : pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <div></div>
        <ac-permission-button
          v-if="[1].includes(scope.row.status)"
          slot="reference"
          type="text"
          size="small"
          btn-text="检查链路模型"
          permission-key="purchaseTransactions-review-model"
          @click="checkLinkModel(scope.row)"
        ></ac-permission-button>
        <el-button
          v-if="[5].includes(scope.row.status)"
          slot="reference"
          type="text"
          size="small"
          @click="viewPricingPlan(scope.row)"
        >
          查看定价方案
        </el-button>
        <ac-permission-button
          v-if="[99, 4].includes(scope.row.status)"
          slot="reference"
          type="text"
          size="small"
          btn-text="定价方案"
          permission-key="purchaseTransactions-pricing-plan"
          @click="viewPricingPlan(scope.row)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="[4].includes(scope.row.status)"
          slot-btn="reference"
          node-type="popconfirm"
          title="确定启用当前交易吗？"
          type="text"
          size="small"
          btn-text="启用"
          permission-key="purchaseTransactions-allow"
          @click="handleConfirm(scope.row, '0')"
        ></ac-permission-button>
        <el-button
          v-if="[1].includes(scope.row.status)"
          slot="reference"
          type="text"
          size="small"
          @click="jump"
        >
          创建链路模型
        </el-button>
        <ac-permission-button
          v-if="[2].includes(scope.row.status)"
          slot="reference"
          type="text"
          size="small"
          btn-text="选择定价方案"
          permission-key="purchaseTransactions-select-pricing-plan"
          @click="viewPricingPlan(scope.row)"
        ></ac-permission-button>
        <ac-permission-button
          v-if="[3].includes(scope.row.status)"
          slot-btn="reference"
          node-type="popconfirm"
          title="当前合同模版：购销合同模版"
          type="text"
          size="small"
          btn-text="生成合同"
          permission-key="purchaseTransactions-generate-contract"
          @click="handleConfirm(scope.row, '1')"
        ></ac-permission-button>
      </template>
    </dynamictable>
    <pricingPlandiaLog
      v-model="showDialog"
      :record="currentRecord"
      :is-hide-operating="currentRecord && currentRecord.status === 5"
      :pricing-plan-select="pricingPlanSelect"
      @onSelect="onSelect"
    ></pricingPlandiaLog>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import pricingPlandiaLog from '../components/pricingPlandiaLog';
  import {
    addContract,
    enable,
    getAffTransList,
    getDataDictAll,
    listByGxId,
    selectPricingByAffTrans,
    checkLinkModel,
    addselectPricing,
  } from '@/api/subjectMgt';
  import { initSearchParams } from '@/utils';
  export default {
    components: {
      dynamictable,
      pricingPlandiaLog,
    },
    data() {
      return {
        searchTab: '0',
        showDialog: false,
        currentRecord: null,
        gxId: '',
        tableData: [],
        searchParams: {
          buyerCompanyCode: '',
          or: '',
          sellerCompanyCode: '',
          status: '',
          tradeType2: '',
          tradeType3: '',
        },
        COMPANY_MAIN: [],
        allSelectList: {},
        BRAND: [],
        CATEGORY: [],
        AFF_TRANS_STATUS: [],
        pricingPlanSelect: [],
        list: [],
        columns: [
          {
            prop: 'contractNo',
            label: '合同编号',
          },
          {
            prop: 'tradeType2Desc',
            label: '二级交易',
          },
          {
            prop: 'tradeType3Desc',
            label: '三级交易',
          },
          {
            prop: 'sellerCompanyName',
            label: '销方',
          },
          {
            prop: 'buyerCompanyName',
            label: '购方',
          },
          {
            prop: 'pricingModelDesc',
            label: '利润率指标',
          },
          {
            prop: 'recommendation',
            label: '建议四分位区间',
            render: ({ pricingModel, recommendation }) => (
              <span>{pricingModel ? recommendation : ''}</span>
            ),
          },
          {
            prop: 'statusDesc',
            label: '状态',
          },
          {
            prop: 'executeNum',
            label: '执行中交易',
            render: ({ executeNum }) => (
              <span onClick={() => this.go(val)}>{executeNum}</span>
            ),
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '200',
            scopedSlots: { customRender: 'operation' },
          },
        ],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
      };
    },
    created() {
      const { id, companyCode } = this.$route.query;
      if (id) {
        this.gxId = id;
      }
      if (companyCode) {
        this.searchTab = '1';
        this.searchParams.sellerCompanyCode = Number(companyCode)
          ? Number(companyCode)
          : companyCode;
      }

      // 下拉状态
      getDataDictAll({}).then(res => {
        if (res) {
          this.allSelectList = res;
          this.COMPANY_MAIN = this.getTypeList(res.COMPANY_MAIN);
          this.AFF_TRANS_STATUS = this.getTypeList(res.AFF_TRANS_STATUS);
          this.BRAND = this.getTypeList(res.BRAND);
          this.CATEGORY = this.getTypeList(res.CATEGORY);
        }
      });
      this.getList(true);
    },
    methods: {
      go(val) {
        this.$router.push({
          name: 'transactionLinkLog',
          query: { atId: val.id },
        });
      },
      jump() {
        this.$router.push({
          name: 'LinkModel',
        });
      },
      async checkLinkModel({ id }) {
        await checkLinkModel({ affTransId: id });
        this.$message({
          message: '检查成功',
          type: 'success',
        });
        this.getList();
      },
      viewPricingPlan(val, flag) {
        this.getSelectPricingByAffTrans(val.id);
        this.showDialog = true;
        this.currentRecord = { ...val, tradeType1Desc: '购销交易' };
      },
      getTypeList(obj) {
        if (!obj) return [];
        return Object.keys(obj).map(item => {
          return {
            id: Number(item) ? Number(item) : item,
            name: obj[item],
          };
        });
      },
      async onSelect(val) {
        this.showDialog = false;
        await addselectPricing({
          affTransId: this.currentRecord.id,
          pricingModel: val.rateOfProfit,
        });
        this.$message({
          message: '选择成功',
          type: 'success',
        });
        this.getList();
      },
      async handleConfirm({ id }, index) {
        const apiList = [enable, addContract];
        const messageText = ['启用成功', '生成合同成功'];
        await apiList[index]({ affTransId: id });
        this.$message({
          message: messageText[index],
          type: 'success',
        });
        this.getList();
      },
      async getList(isSearch) {
        const { gxId, searchParams, pagination } = this;
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = {
          ...searchParams,
          or: this.searchTab === '1',
          current: pagination.pageSize,
          size: pagination.pageLimit,
        };
        this.options.loading = true;
        const getApi = gxId ? listByGxId : getAffTransList;
        const res = await getApi(gxId ? { gxId } : initSearchParams(params));
        this.options.loading = false;
        if (gxId) {
          this.list = res ? res : [];
        } else {
          this.list = res ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        }
      },
      // 选择定价方案列表
      async getSelectPricingByAffTrans(affTransId, flag) {
        const res = await selectPricingByAffTrans({ affTransId });
        if (res && res.length) {
          this.pricingPlanSelect = res;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.getList(true);
      },
    },
  };
</script>
<style lang="scss">
  .pricing-modal {
    .modal-top {
      padding: 20px;
      margin-bottom: 10px;
      border: 1px solid #dcdfe6;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .el-table .is-group tr:nth-child(2) {
      display: none;
    }
  }
</style>
