<template>
  <div>
    <el-form inline>
      <el-form-item label="主体名称:">
        <el-select
          v-model="searchParams.companyCode"
          filterable
          placeholder="请选择主体"
        >
          <el-option
            v-for="item in COMPANY_MAIN"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="品牌:">
        <el-select v-model="searchParams.brandCode" placeholder="请选择品牌">
          <el-option
            v-for="item in BRAND"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="品类:">
        <el-select v-model="searchParams.categoryCode" placeholder="请选择品类">
          <el-option
            v-for="item in CATEGORY"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="链路类型:">
        <el-select v-model="searchParams.linkType" placeholder="请选择链路类型">
          <el-option
            v-for="item in LINK_TYPE"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态:">
        <el-select v-model="searchParams.status" placeholder="请选择状态">
          <el-option
            v-for="item in PURCHASE_SALE_STATUS"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <el-button
          slot="reference"
          type="text"
          size="small"
          :disabled="![2].includes(scope.row.status)"
          @click="handleOperation(scope.row, 'transactionLinkLog')"
        >
          查看
        </el-button>
        <el-button
          slot="reference"
          type="text"
          size="small"
          :disabled="![1, 2].includes(scope.row.status)"
          @click="handleOperation(scope.row, 'linkPricingConfiguration')"
        >
          定价配置
        </el-button>
        <el-button
          slot="reference"
          type="text"
          size="small"
          :disabled="![0, 1, 2].includes(scope.row.status)"
          @click="handleOperation(scope.row, 'purchaseTransactions')"
        >
          关联交易
        </el-button>
      </template>
    </dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import { getDataDictAll, listPurchaseSaleList } from '@/api/subjectMgt';
  import { LOCAL_STORAGE_KEY } from '@/consts/subjectMgt';
  export default {
    components: {
      dynamictable,
    },
    data() {
      return {
        searchParams: {
          brandCode: '',
          categoryCode: '',
          companyCode: '',
          linkType: '',
          status: '',
        },
        COMPANY_MAIN: [],
        PURCHASE_SALE_STATUS: [],
        BRAND: [],
        CATEGORY: [],
        LINK_TYPE: [],
        list: [],
        columns: [
          {
            prop: 'createTime',
            label: '申请日期',
          },
          {
            prop: 'brandName',
            label: '品牌',
          },
          {
            prop: 'linkTypeStr',
            label: '链路类型',
          },
          {
            prop: 'a',
            label: '节点-1',
            render: ({ companyMainRespVOS = [] }) => (
              <span>
                {companyMainRespVOS[0] ? companyMainRespVOS[0].companyName : ''}
              </span>
            ),
          },
          {
            prop: 'b',
            label: '节点-2',
            render: ({ companyMainRespVOS = [] }) => (
              <span>
                {companyMainRespVOS[1] ? companyMainRespVOS[1].companyName : ''}
              </span>
            ),
          },
          {
            prop: 'c',
            label: '节点-3',
            render: ({ companyMainRespVOS = [] }) => (
              <span>
                {companyMainRespVOS[2] ? companyMainRespVOS[2].companyName : ''}
              </span>
            ),
          },
          {
            prop: 'd',
            label: '节点-4',
            render: ({ companyMainRespVOS = [] }) => (
              <span>
                {companyMainRespVOS[3] ? companyMainRespVOS[3].companyName : ''}
              </span>
            ),
          },
          {
            prop: 'e',
            label: '节点-5',
            render: ({ companyMainRespVOS = [] }) => (
              <span>
                {companyMainRespVOS[4] ? companyMainRespVOS[4].companyName : ''}
              </span>
            ),
          },
          {
            prop: 'f',
            label: '节点-6',
            render: ({ companyMainRespVOS = [] }) => (
              <span>
                {companyMainRespVOS[5] ? companyMainRespVOS[5].companyName : ''}
              </span>
            ),
          },
          {
            prop: 'statusStr',
            label: '状态',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '200',
            scopedSlots: { customRender: 'operation' },
          },
        ],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
      };
    },
    created() {
      getDataDictAll({}).then(res => {
        if (res) {
          this.COMPANY_MAIN = this.getTypeList(res.COMPANY_MAIN);
          this.PURCHASE_SALE_STATUS = this.getTypeList(
            res.PURCHASE_SALE_STATUS,
          );
          this.BRAND = this.getTypeList(res.BRAND);
          this.CATEGORY = this.getTypeList(res.CATEGORY);
          this.LINK_TYPE = this.getTypeList(res.LINK_TYPE);
        }
      });
      this.getList(true);
    },
    methods: {
      getTypeList(obj) {
        if (!obj) return [];
        return Object.keys(obj).map(item => {
          return {
            id: Number(item) ? Number(item) : item,
            name: obj[item],
          };
        });
      },
      handleOperation(val, router) {
        if (router === 'linkPricingConfiguration') {
          localStorage.setItem(LOCAL_STORAGE_KEY.linkInfo, JSON.stringify(val));
        }
        this.$router.push({
          name: router,
          query: { id: val.id },
        });
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = {
          ...this.searchParams,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        this.options.loading = true;
        const res = await listPurchaseSaleList(params);
        this.options.loading = false;
        this.list = res ? res.records : [];
        this.pagination.total = res ? res.total : 0;
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.getList(true);
      },
    },
  };
</script>
<style lang="scss" scoped></style>
