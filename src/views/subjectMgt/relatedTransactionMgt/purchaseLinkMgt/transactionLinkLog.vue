<template>
  <div>
    <el-form inline>
      <el-form-item label="日期:">
        <!-- <el-date-picker
          v-model="searchParams.beginTime"
          type="date"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          placeholder="请选择起始日期"
        ></el-date-picker> -->
        <el-date-picker
          v-model="searchDate"
          type="daterange"
          range-separator="至"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          start-placeholder="起始日期"
          end-placeholder="截止日期"
        ></el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="截止日期:">
        <el-date-picker
          v-model="searchParams.endTime"
          type="date"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          placeholder="请选择截止日期"
        ></el-date-picker>
      </el-form-item> -->
      <!-- <el-form-item label="品牌:">
        <el-select v-model="searchParams.tenantId" placeholder="请选择品牌">
          <el-option
            v-for="item in statusList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button @click="() => $router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <el-button
          slot="reference"
          type="text"
          size="small"
          @click="hanldeDetails(scope.row)"
        >
          查看交易明细
        </el-button>
      </template>
    </dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    listTradeLinkLogList,
    listTradeLinkLogByAtId,
  } from '@/api/subjectMgt';
  import { parseTime } from '@/utils';
  export default {
    components: {
      dynamictable,
    },
    data() {
      return {
        searchDate: '',
        searchParams: {
          beginTime: '',
          endTime: '',
        },
        purchaseSaleId: '',
        atId: '',
        list: [],
        columns: [
          {
            prop: 'mainOrderCode',
            label: '采购单号',
          },
          {
            prop: 'brandName',
            label: '品牌',
          },
          {
            prop: 'categoryName',
            label: '品类',
            render: ({ categoryName }) => <span>{''}</span>,
          },
          {
            prop: 'purchaseNum',
            label: '采购数量',
          },
          {
            prop: 'sourceCreate',
            label: '发起日期',
            render: ({ sourceCreate }) => (
              <span>{sourceCreate ? parseTime(sourceCreate) : ''}</span>
            ),
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '200',
            scopedSlots: { customRender: 'operation' },
          },
        ],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
      };
    },
    created() {
      const { id, atId } = this.$route.query;
      if (id) {
        this.purchaseSaleId = id;
      } else {
        this.atId = atId;
      }

      this.getList();
    },
    methods: {
      hanldeDetails(val) {
        this.$router.push({
          name: 'purchaseTransactionLog',
          query: { id: val.id },
        });
      },
      getTableText(arr, index) {
        let node;
        arr.map((item, inx) => {
          if (inx == index) {
            node = (
              <span>
                {item.purchaseAmount}
                <span style="color: red">[{item.percent}]</span>
              </span>
            );
          }
        });
        return node;
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const { purchaseSaleId, atId } = this;

        this.searchParams.beginTime = this.searchDate ? this.searchDate[0] : '';
        this.searchParams.endTime = this.searchDate ? this.searchDate[1] : '';

        let params = {
          ...this.searchParams,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        if (atId) {
          params.atId = atId;
        } else {
          params.purchaseSaleId = purchaseSaleId;
        }
        this.options.loading = true;
        const ApiGet = atId ? listTradeLinkLogByAtId : listTradeLinkLogList;
        const res = await ApiGet(params);
        if (res) {
          if (
            Array.isArray(res.records) &&
            res.records.length > 0 &&
            !isSearch
          ) {
            const obj = res.records[0];

            if (Array.isArray(obj.nodeRespList)) {
              obj.nodeRespList.forEach((element, index) => {
                this.columns.splice(4, 0, {
                  prop: 'categoryName',
                  label: `采购价值 - ${element.companyName}`,
                  render: row => this.getTableText(row.nodeRespList, index),
                });
              });
            }
          }
          this.list = res.records;
          this.pagination.total = res.total;
        } else {
          this.list = [];
          this.pagination.total = 0;
        }
        this.options.loading = false;
      },
      onReset() {
        this.searchDate = '';
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.getList(true);
      },
    },
  };
</script>
<style lang="scss" scoped></style>
