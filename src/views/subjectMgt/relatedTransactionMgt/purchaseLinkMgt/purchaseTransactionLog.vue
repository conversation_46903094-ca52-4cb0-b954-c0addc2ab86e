<template>
  <div>
    <el-form inline>
      <el-form-item label="起始日期:">
        <el-date-picker
          v-model="searchParams.beginTime"
          type="date"
          placeholder="请选择起始日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="截止日期:">
        <el-date-picker
          v-model="searchParams.endTime"
          type="date"
          placeholder="请选择截止日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button @click="() => $router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :fetch="getList"
    ></dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import { listPurchaseSaleLinkLog } from '@/api/subjectMgt';
  import { parseTime } from '@/utils';
  export default {
    components: {
      dynamictable,
    },
    data() {
      return {
        searchParams: {
          mainOrderCode: '',
        },
        list: [],
        mainOrderCode: '',
        columns: [
          {
            prop: 'orderCode',
            label: '采购单号',
          },
          {
            prop: 'brandName',
            label: '品牌',
          },
          {
            prop: 'categoryName',
            label: '品类',
          },
          {
            prop: 'purchaseNum',
            label: '收货数量',
          },
          {
            prop: 'purchaseAmount',
            label: '采购金额',
          },
          {
            prop: 'saleCompanyName',
            label: '销方',
          },
          {
            prop: 'purchaseCompanyName',
            label: '购方',
          },
          {
            prop: 'sourceCreate',
            label: '交易日期',
            render: ({ sourceCreate }) => (
              <span>{sourceCreate ? parseTime(sourceCreate) : ''}</span>
            ),
          },
        ],
        options: {
          loading: false,
          border: true,
        },
      };
    },
    created() {
      this.searchParams.mainOrderCode = this.$route.query.id;
      this.getList();
    },
    methods: {
      async getList() {
        this.options.loading = true;
        const res = await listPurchaseSaleLinkLog(this.searchParams);
        this.options.loading = false;
        this.list = res ? res : [];
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.getList();
      },
    },
  };
</script>
<style lang="scss" scoped></style>
