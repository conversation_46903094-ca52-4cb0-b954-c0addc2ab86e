<template>
  <div>
    <el-form inline>
      <el-form-item label="主体名称:">
        <el-select
          v-model="searchParams.companyCode"
          filterable
          placeholder="请选择主体"
        >
          <el-option
            v-for="item in COMPANY_LIST"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态:">
        <el-select v-model="searchParams.status" placeholder="请选择状态">
          <el-option
            v-for="item in COMPANY_STATUS"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item style="float: right">
        <router-link :to="{ name: 'licenseQuery' }">
          <el-button type="text">证照信息查询</el-button>
        </router-link>
        <el-button
          style="margin-left: 10px"
          type="primary"
          @click="getList(true)"
        >
          查询
        </el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="编辑"
          permission-key="subjectInformationMgt-edit"
          @click="handleEdit(scope.row)"
        ></ac-permission-button>
        <ac-permission-button
          node-type="popconfirm"
          title="请确认主体信息正确，已注册完成。点击“确认”将提交税务组确认公司定位"
          btn-text="审核"
          permission-key="subjectInformationMgt-audit"
          slot-btn="reference"
          :disabled="scope.row.status !== 0"
          type="text"
          size="mini"
          @click="handleConfirm(scope.row, 1)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    statusOperation,
    getlistCompanyMain,
    getDataDictAll,
  } from '@/api/subjectMgt';
  import { parseTime } from '@/utils';
  import { LOCAL_STORAGE_KEY } from '@/consts/subjectMgt';
  export default {
    components: {
      dynamictable,
    },
    data() {
      return {
        searchParams: {
          companyCode: '',
          status: '',
        },
        COMPANY_LIST: [],
        COMPANY_STATUS: [],
        statusList: [],
        list: [],
        columns: [
          {
            prop: 'companyName',
            label: '公司名称',
          },
          {
            prop: 'registration',
            label: '注册地',
          },
          {
            prop: 'socialCreditCode',
            label: '统一社会信用代码',
            render: ({ companyTaxInfoRespVO = {} }) => (
              <span>
                {companyTaxInfoRespVO
                  ? companyTaxInfoRespVO.socialCreditCode
                  : ''}
              </span>
            ),
          },
          {
            prop: 'legalRepresentative',
            label: '法人',
          },
          {
            prop: 'director',
            label: '董事',
          },
          {
            prop: 'registeredCapital',
            label: '注册资本',
          },
          // {
          //   prop: 'paymentChannelMerchantId',
          //   label: '股东名称',
          // },
          {
            prop: 'establishTime',
            label: '成立时间',
            render: ({ establishTime }) => (
              <span>{establishTime ? parseTime(establishTime) : ''}</span>
            ),
          },
          {
            prop: 'statusStr',
            label: '状态',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '200',
            scopedSlots: { customRender: 'operation' },
          },
        ],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
      };
    },
    created() {
      getDataDictAll({}).then(res => {
        if (res) {
          this.COMPANY_LIST = this.getTypeList(res.COMPANY_MAIN);
          this.COMPANY_STATUS = this.getTypeList(res.COMPANY_STATUS);
        }
      });
      this.getList(true);
    },
    methods: {
      getTypeList(obj) {
        if (!obj) return [];
        return Object.keys(obj).map(item => {
          return {
            id: Number(item) ? Number(item) : item,
            name: obj[item],
          };
        });
      },
      handleEdit(val) {
        localStorage.setItem(
          LOCAL_STORAGE_KEY.editSubjectInformation,
          JSON.stringify(val),
        );
        this.$router.push({
          name: 'editSubjectInformation',
          query: { id: val.id },
        });
      },
      async handleConfirm({ id }, key) {
        await statusOperation({ id, status: key });
        this.getList();
        this.$message.success(key === 1 ? '审核成功' : '停用成功');
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = {
          ...this.searchParams,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        this.options.loading = true;
        try {
          const res = await getlistCompanyMain(params);
          this.options.loading = false;
          this.list = res ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        } catch (err) {
          this.options.loading = false;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.getList();
      },
    },
  };
</script>
<style lang="scss" scoped></style>
