<template>
  <div class="editSubjectInformation">
    <el-alert type="success" :closable="false" effect="dark">
      <div class="el-alert-content">
        <div>
          <span style="margin-right: 30px">基本信息</span>
          <el-radio-group v-model="tab">
            <el-radio-button v-if="tab === 0" :label="0">境内</el-radio-button>
            <el-radio-button v-if="tab === 1" :label="1">境外</el-radio-button>
          </el-radio-group>
        </div>
        <div>
          <el-button @click="handleEdit">
            {{ isEdit ? '保存' : '编辑' }}
          </el-button>
          <el-button @click="goBack">返回</el-button>
        </div>
      </div>
    </el-alert>
    <el-form
      ref="saveForm"
      label-position="right"
      label-width="100px"
      :inline="true"
      :model="saveForm"
      class="demo-form-inline"
    >
      <el-form-item
        label="公司编码:"
        prop="companyCode"
        :rules="[
          {
            required: true,
            message: '请输入公司编码',
            trigger: 'change',
          },
        ]"
      >
        <el-input
          v-model="saveForm.companyCode"
          :disabled="true"
          placeholder="请输入公司编码"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="公司名称:"
        prop="companyName"
        :rules="[
          {
            required: true,
            message: '请输入公司名称',
            trigger: 'change',
          },
        ]"
      >
        <el-input
          v-model="saveForm.companyName"
          :disabled="!isEdit"
          placeholder="请输入公司名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="董事:">
        <el-input
          v-model="saveForm.director"
          :disabled="!isEdit"
          placeholder="请输入董事"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="成立时间:"
        prop="establishTime"
        :rules="[
          {
            required: true,
            message: '请输入成立时间',
            trigger: 'change',
          },
        ]"
      >
        <!-- <el-input
          v-model="saveForm.establishTime"
          :disabled="!isEdit"
          placeholder="请输入成立时间"
        ></el-input> -->
        <el-date-picker
          v-model="saveForm.establishTime"
          :disabled="!isEdit"
          type="datetime"
          placeholder="选择日期时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
        ></el-date-picker>
      </el-form-item>
      <el-form-item
        label="英文名称:"
        prop="englishName"
        :rules="[
          {
            required: true,
            message: '请输入英文名称',
            trigger: 'change',
          },
        ]"
      >
        <el-input
          v-model="saveForm.englishName"
          :disabled="!isEdit"
          placeholder="请输入英文名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="总经理:">
        <el-input
          v-model="saveForm.generalManager"
          :disabled="!isEdit"
          placeholder="请输入总经理"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="注册地:"
        prop="registration"
        :rules="[
          {
            required: true,
            message: '请输入注册地',
            trigger: 'change',
          },
        ]"
      >
        <el-input
          v-model="saveForm.registration"
          :disabled="!isEdit"
          placeholder="请输入注册地"
        ></el-input>
      </el-form-item>
      <el-form-item label="邮政编码:">
        <el-input
          v-model="saveForm.postalCode"
          :disabled="!isEdit"
          placeholder="请输入邮政编码"
        ></el-input>
      </el-form-item>
      <el-form-item label="监事:">
        <el-input
          v-model="saveForm.supervisor"
          :disabled="!isEdit"
          placeholder="请输入监事"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="企业类型:"
        prop="enterpriseType"
        :rules="[
          {
            required: true,
            message: '请输入企业类型',
            trigger: 'change',
          },
        ]"
      >
        <el-input
          v-model="saveForm.enterpriseType"
          :disabled="!isEdit"
          placeholder="请输入企业类型"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="经营状态:"
        prop="operatingState"
        :rules="[
          {
            required: true,
            message: '请输入经营状态',
            trigger: 'change',
          },
        ]"
      >
        <el-input
          v-model="saveForm.operatingState"
          :disabled="!isEdit"
          placeholder="请输入经营状态"
        ></el-input>
      </el-form-item>
      <el-form-item label="法定代表人:">
        <el-input
          v-model="saveForm.legalRepresentative"
          :disabled="!isEdit"
          placeholder="请输入法定代表人"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="定位描述:"
        prop="positioningDescription"
        :rules="[
          {
            required: true,
            message: '请输入定位描述',
            trigger: 'change',
          },
        ]"
      >
        <el-input
          v-model="saveForm.positioningDescription"
          :disabled="!isEdit"
          placeholder="请输入定位描述"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="经营范围:"
        prop="businessScope"
        :rules="[
          {
            required: true,
            message: '请输入经营范围',
            trigger: 'change',
          },
        ]"
      >
        <el-input
          v-model="saveForm.businessScope"
          :disabled="!isEdit"
          placeholder="请输入经营范围"
        ></el-input>
      </el-form-item>
      <el-form-item label="财务负责人:">
        <el-input
          v-model="saveForm.financialManager"
          :disabled="!isEdit"
          placeholder="请输入财务负责人"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="注册资本:"
        prop="registeredCapital"
        :rules="[
          {
            required: true,
            message: '请输入注册资本',
            trigger: 'change',
          },
        ]"
      >
        <el-input
          v-model="saveForm.registeredCapital"
          :disabled="!isEdit"
          placeholder="请输入注册资本"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="公司电话:"
        prop="companyPhone"
        :rules="[
          {
            required: true,
            message: '请输入公司电话',
            trigger: 'change',
          },
        ]"
      >
        <el-input
          v-model="saveForm.companyPhone"
          :disabled="!isEdit"
          placeholder="请输入公司电话"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="公司地址:"
        prop="companyAddress"
        :rules="[
          {
            required: true,
            message: '请输入公司地址',
            trigger: 'change',
          },
        ]"
      >
        <el-input
          v-model="saveForm.companyAddress"
          :disabled="!isEdit"
          placeholder="请输入公司地址"
        ></el-input>
      </el-form-item>

      <el-alert type="success" :closable="false" effect="dark">
        税务信息
      </el-alert>
      <el-form-item label="税收管辖区:">
        <el-input
          v-model="companyTaxInfoReqVO.taxJurisdiction"
          :disabled="!isEdit"
          placeholder="请输入税收管辖区"
        ></el-input>
      </el-form-item>
      <el-form-item label="主税务机关:">
        <el-input
          v-model="companyTaxInfoReqVO.principalTaxAuthority"
          :disabled="!isEdit"
          placeholder="请输入主税务机关"
        ></el-input>
      </el-form-item>
      <el-form-item label="社会信用代码:">
        <el-input
          v-model="companyTaxInfoReqVO.socialCreditCode"
          :disabled="!isEdit"
          placeholder="请输入社会信用代码"
        ></el-input>
      </el-form-item>
      <el-form-item label="纳税人类型:">
        <el-input
          v-model="companyTaxInfoReqVO.taxpayerType"
          :disabled="!isEdit"
          placeholder="请输入纳税人类型"
        ></el-input>
      </el-form-item>
      <el-form-item label="所得税说率:">
        <el-input
          v-model="companyTaxInfoReqVO.incomeTaxRate"
          :disabled="!isEdit"
          placeholder="请输入所得税说率"
        ></el-input>
      </el-form-item>
      <el-form-item label="税收优惠待遇:">
        <el-input
          v-model="companyTaxInfoReqVO.preferentialTaxTreatment"
          :disabled="!isEdit"
          placeholder="请输入税收优惠待遇"
        ></el-input>
      </el-form-item>
      <el-form-item label="税务负责人:">
        <el-input
          v-model="companyTaxInfoReqVO.taxOfficer"
          :disabled="!isEdit"
          placeholder="请输入税务负责人"
        ></el-input>
      </el-form-item>

      <el-form-item label="银行:">
        <el-input
          v-model="saveForm.bankName"
          :disabled="!isEdit"
          placeholder="请输入银行"
        ></el-input>
      </el-form-item>

      <el-form-item label="银行账号:">
        <el-input
          v-model="saveForm.bankAccount"
          :disabled="!isEdit"
          placeholder="请输入银行账号"
        ></el-input>
      </el-form-item>

      <el-alert type="success" :closable="false" effect="dark">
        <div class="el-alert-content">
          <div>
            <span style="margin-right: 30px">股权信息</span>
          </div>
          <el-button :disabled="!isEdit" @click="handleAdd('1')">
            新增股权信息
          </el-button>
        </div>
      </el-alert>
      <el-form-item
        v-if="tab === 1"
        label="股本总额:"
        prop="totalAmountEquity"
        :rules="[
          {
            required: true,
            message: '请输入股本总额',
            trigger: 'change',
          },
        ]"
      >
        <el-input
          v-model="saveForm.totalAmountEquity"
          :disabled="!isEdit"
          placeholder="请输入股本总额"
        ></el-input>
      </el-form-item>
      <el-form-item
        v-if="tab === 1"
        label="股本总数:"
        prop="totalNumberEquity"
        :rules="[
          {
            required: true,
            message: '请输入股本总数',
            trigger: 'change',
          },
        ]"
      >
        <el-input
          v-model="saveForm.totalNumberEquity"
          :disabled="!isEdit"
          placeholder="请输入股本总数"
        ></el-input>
      </el-form-item>
      <el-form-item class="table-from" :label="tab === 1 ? '股权信息:' : ''">
        <el-table :data="equityInformationList" style="width: 100%">
          <el-table-column prop="shareholderName" label="股东名称" width="200">
            <template slot-scope="scope">
              <el-autocomplete
                v-model="scope.row.shareholderName"
                value-key="name"
                class="inline-input"
                :disabled="scope.row.isEdit"
                :fetch-suggestions="querySearch"
                placeholder="选择已经存在的主体或输入"
              ></el-autocomplete>
              <!-- <el-select
                v-model="scope.row.shareholderName"
                :disabled="scope.row.isEdit"
                placeholder="选择已经存在的主体或输入"
                filterable
                @blur="e => selectBlur(e, scope.row)"
              >
                <el-option
                  v-for="item in COMPANY_MAIN"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select> -->
              <!-- <el-input
                v-model="scope.row.shareholderName"
                :disabled="scope.row.isEdit"
                placeholder="请输入"
              ></el-input> -->
            </template>
          </el-table-column>

          <el-table-column
            v-if="tab === 1"
            prop="totalHold"
            label="持有总数"
            width="200"
          >
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.totalHold"
                :disabled="scope.row.isEdit"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column
            v-if="tab === 1"
            prop="totalRatio"
            label="持有占比"
            width="200"
          >
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.totalRatio"
                :disabled="scope.row.isEdit"
              ></el-input>
            </template>
          </el-table-column>

          <el-table-column
            v-if="tab === 0"
            prop="capitalContribution"
            label="出资额"
            width="200"
          >
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.capitalContribution"
                :disabled="scope.row.isEdit"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column
            v-if="tab === 0"
            prop="fundedRatio"
            label="出资比例"
            width="200"
          >
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.fundedRatio"
                :disabled="scope.row.isEdit"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column
            v-if="tab === 0"
            prop="paidIn"
            label="实缴"
            width="200"
          >
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.paidIn"
                :disabled="scope.row.isEdit"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column
            v-if="tab === 0"
            prop="fundedTime"
            label="出资日期"
            width="200"
          >
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.fundedTime"
                :disabled="scope.row.isEdit"
                type="date"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
              ></el-date-picker>
            </template>
          </el-table-column>

          <el-table-column width="200" label="操作">
            <template slot-scope="scope">
              <el-button
                size="mini"
                :disabled="!isEdit"
                @click="handleSubmit(scope.row, '1')"
              >
                {{ scope.row.isEdit ? '编辑' : '提交' }}
              </el-button>
              <el-button
                size="mini"
                type="danger"
                :disabled="!isEdit"
                @click="handleDelete(scope.row, '1')"
              >
                {{ scope.row.isEdit ? '删除' : '取消' }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-alert type="success" :closable="false" effect="dark">
        <div class="el-alert-content">
          <div>
            <span style="margin-right: 30px">证照信息</span>
          </div>
          <el-button :disabled="!isEdit" @click="handleAdd('2')">
            新增证照
          </el-button>
        </div>
      </el-alert>

      <el-table :data="licenseList" style="width: 100%">
        <el-table-column prop="licenseName" label="证照名称" width="200">
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.licenseName"
              :disabled="scope.row.isEdit"
              placeholder="请选择证照"
            >
              <el-option
                v-for="item in LICENSE"
                :key="item.name"
                :label="item.name"
                :value="item.name"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="competentAuthority" label="主管机关" width="200">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.competentAuthority"
              :disabled="scope.row.isEdit"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="issueDate" label="颁发日期" width="230">
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.issueDate"
              :disabled="scope.row.isEdit"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
            ></el-date-picker>
          </template>
        </el-table-column>
        <el-table-column prop="updateDate" label="更新日期" width="230">
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.updateDate"
              :disabled="scope.row.isEdit"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
            ></el-date-picker>
          </template>
        </el-table-column>
        <el-table-column prop="validityPeriod" label="有效期" width="200">
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.validityPeriod"
              :disabled="scope.row.isEdit"
              placeholder="请选择有效期"
            >
              <el-option
                v-for="item in VALIDITY_PERIOD"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="idCode" label="证件编码" width="200">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.idCode"
              :disabled="scope.row.isEdit"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="attachmentUrl" label="附件" width="200">
          <template slot-scope="scope">
            <!-- <a @click="uploadFile(scope.row)">
              {{ scope.row.attachmentUrl ? '附件' : '未上传' }}
            </a> -->
            <el-button
              type="text"
              size="mini"
              :disabled="scope.row.isEdit"
              @click="uploadFile(scope.row)"
            >
              {{ scope.row.attachmentUrl ? '附件' : '未上传' }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column width="200" label="操作">
          <template slot-scope="scope">
            <el-button
              size="mini"
              :disabled="!isEdit"
              @click="handleSubmit(scope.row, '2')"
            >
              {{ scope.row.isEdit ? '编辑' : '提交' }}
            </el-button>
            <el-button
              size="mini"
              type="danger"
              :disabled="!isEdit"
              @click="handleDelete(scope.row, '2')"
            >
              {{ scope.row.isEdit ? '删除' : '取消' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <postAttachment
      v-model="showDialog"
      :record="currentRecord"
      @onOK="onOK"
    ></postAttachment>
  </div>
</template>

<script>
  import postAttachment from './components/postAttachment';
  import { editCompanyMain, getDataDictAll } from '@/api/subjectMgt';
  import { LOCAL_STORAGE_KEY } from '@/consts/subjectMgt';
  import { parseTime } from '@/utils';

  export default {
    components: {
      postAttachment,
    },
    data() {
      return {
        tab: 0,
        isEdit: false,
        showDialog: false,
        currentRecord: null,
        saveForm: { id: '' }, // 基本信息
        companyTaxInfoReqVO: {}, // 税务信息
        VALIDITY_PERIOD: [],
        LICENSE: [],
        COMPANY_MAIN: [],
        equityInformationList: [],
        licenseList: [],
      };
    },
    created() {
      getDataDictAll({}).then(res => {
        if (res) {
          this.LICENSE = this.getTypeList(res.LICENSE);
          this.COMPANY_MAIN = this.getTypeList(res.COMPANY_MAIN);
          this.VALIDITY_PERIOD = this.getTypeList(res.VALIDITY_PERIOD);
          const info =
            JSON.parse(
              localStorage.getItem(LOCAL_STORAGE_KEY.editSubjectInformation),
            ) || {};
          this.initform(info);
        }
      });
    },
    methods: {
      createFilter(queryString) {
        return restaurant => {
          return (
            restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) ===
            0
          );
        };
      },
      querySearch(queryString, cb) {
        const results = queryString
          ? this.COMPANY_MAIN.filter(this.createFilter(queryString))
          : this.COMPANY_MAIN;
        // 调用 callback 返回建议列表的数据
        cb(results);
      },
      initform(res) {
        this.tab = res.type;
        this.saveForm = {
          id: res.id,
          companyCode: res.companyCode,
          companyName: res.companyName,
          director: res.director,
          establishTime: res.establishTime,
          englishName: res.englishName,
          generalManager: res.generalManager,
          registration: res.registration,
          postalCode: res.postalCode,
          supervisor: res.supervisor,
          enterpriseType: res.enterpriseType,
          operatingState: res.operatingState,
          legalRepresentative: res.legalRepresentative,
          positioningDescription: res.positioningDescription,
          businessScope: res.businessScope,
          financialManager: res.financialManager,
          registeredCapital: res.registeredCapital,
          companyPhone: res.companyPhone,
          companyAddress: res.companyAddress,
          totalAmountEquity: res.totalAmountEquity,
          totalNumberEquity: res.totalNumberEquity,
        };
        const obj = res.companyTaxInfoRespVO || {};
        this.companyTaxInfoReqVO = {
          taxJurisdiction: obj.taxJurisdiction,
          principalTaxAuthority: obj.principalTaxAuthority,
          socialCreditCode: obj.socialCreditCode,
          taxpayerType: obj.taxpayerType,
          incomeTaxRate: obj.incomeTaxRate,
          preferentialTaxTreatment: obj.preferentialTaxTreatment,
          taxOfficer: obj.taxOfficer,
        };
        this.equityInformationList = (res.companyShareholderRespVOS || []).map(
          ({
            capitalContribution,
            fundedRatio,
            fundedTime,
            shareholderName,
            paidIn,
            totalHold,
            totalRatio,
            id,
          }) => ({
            capitalContribution,
            fundedRatio,
            fundedTime: fundedTime ? parseTime(fundedTime, '{y}-{m}-{d}') : '',
            shareholderName: this.getName(shareholderName, this.COMPANY_MAIN),
            paidIn,
            totalHold,
            totalRatio,
            isEdit: true,
            id,
            key: Date.now(),
          }),
        );
        this.licenseList = (res.companyLicenseRespVOS || []).map(
          ({
            licenseName,
            competentAuthority,
            issueDate,
            validityPeriod,
            attachmentUrl,
            urlName,
            idCode,
            updateDate,
            id,
          }) => ({
            licenseName,
            licenseCode: this.getCode(licenseName, this.LICENSE),
            competentAuthority,
            issueDate: issueDate ? parseTime(issueDate, '{y}-{m}-{d}') : '',
            validityPeriod: this.getCode(validityPeriod, this.VALIDITY_PERIOD), // 1短期 2长期
            attachmentUrl,
            urlName,
            idCode,
            updateDate: updateDate ? parseTime(updateDate, '{y}-{m}-{d}') : '',
            id,
            isEdit: true,
            key: Date.now(),
          }),
        );
      },

      getCode(name, arr) {
        let code = '';

        arr.forEach(item => {
          if (item.name === name) {
            code = item.id;
          }
        });

        return code;
      },
      getName(id, arr) {
        let name = id;

        arr.forEach(item => {
          if (item.id == id) {
            name = item.name;
          }
        });

        return name;
      },
      goBack() {
        localStorage.removeItem(LOCAL_STORAGE_KEY.editSubjectInformation);
        this.$router.go(-1);
      },
      getTypeList(obj) {
        if (!obj) return [];
        return Object.keys(obj).map(item => {
          return {
            id: Number(item) ? Number(item) : item,
            name: obj[item],
          };
        });
      },
      selectBlur(e, v) {
        this.equityInformationList = this.equityInformationList.map(item => {
          if (item.key === v.key) {
            return {
              ...item,
              shareholderName: e.target.value,
            };
          }
          return item;
        });
      },
      uploadFile(val) {
        this.showDialog = true;
        this.currentRecord = val;
      },
      onOK(val) {
        this.licenseList = this.licenseList.map(item => {
          if (this.currentRecord.key === item.key) {
            return {
              ...item,
              attachmentUrl: val ? val.file_url : '',
              urlName: val ? val.name : '',
            };
          }
          return item;
        });
      },
      isRequire(obj) {
        let falg = false;
        const arr = [
          obj.shareholderName || '',
          obj.totalHold || '',
          obj.totalRatio || '',
        ];
        const arr1 = [
          obj.shareholderName || '',
          obj.capitalContribution || '',
          obj.fundedRatio || '',
          obj.fundedTime || '',
          obj.paidIn || '',
        ];

        if (this.tab === 0) {
          if (arr1.includes('')) {
            falg = true;
          }
          return falg;
        }
        if (this.tab === 1) {
          if (arr.includes('')) {
            falg = true;
          }
          return falg;
        }
      },
      handleSubmit(val, key) {
        if (key === '1' && this.isRequire(val) && !val.isEdit) {
          return this.$message.error('全都必填');
        }
        if (key === '2' && this.isAllFill(val, key) && !val.isEdit) {
          return this.$message.error('除“更新日期”以外都是必填项');
        }
        let list = key === '1' ? this.equityInformationList : this.licenseList;
        list = list.map(item => {
          // 有id 用id判断  没有id 用key
          if (val.id && item.id === val.id) {
            return {
              ...item,
              isEdit: !val.isEdit,
            };
          } else if (!val.id && item.key === val.key) {
            return {
              ...item,
              isEdit: !val.isEdit,
            };
          }
          return item;
        });
        if (key === '1') {
          this.equityInformationList = list;
        } else this.licenseList = list;
      },
      isAllFill(obj, key) {
        let newObj = {
          ...obj,
        };
        if (key === '2') {
          delete newObj['updateDate'];
        }
        const arr = Object.keys(newObj).map(item => {
          return newObj[item];
        });
        return arr.includes('');
      },
      handleEdit() {
        if (!this.isEdit) return (this.isEdit = true);

        this.$refs.saveForm.validate(async valid => {
          if (!valid) return;
          const {
            saveForm,
            companyTaxInfoReqVO,
            equityInformationList,
            licenseList,
          } = this;
          const isEquityInformation = this.isSave(equityInformationList);
          const isLicenseList = this.isSave(licenseList);
          if (isEquityInformation)
            return this.$message.error('股权信息有未保存项');
          if (isLicenseList) return this.$message.error('证照信息有未保存项');
          const saveParams = {
            ...saveForm,
            type: this.tab,
            companyTaxInfoReqVO,
            companyLicenseReqVOS: this.getVo(licenseList, 1),
            companyShareholderReqVOS: this.getVo(equityInformationList, 2),
          };
          await editCompanyMain(saveParams);
          this.$message.success('编辑成功');
          this.$router.go(-1);
        });
      },
      getVo(arr = [], type) {
        if (!Array.isArray(arr)) return [];
        return arr.map(item => {
          delete item['isEdit'];
          delete item['key'];
          if (type === 1) {
            return {
              ...item,
              licenseCode: this.getCode(item.licenseName, this.LICENSE),
            };
          } else if (type === 2) {
            return {
              ...item,
              shareholderName:
                this.getCode(item.shareholderName, this.COMPANY_MAIN) ||
                item.shareholderName,
            };
          } else {
            return item;
          }
        });
      },
      isSave(arr = []) {
        return arr
          .map(item => {
            return item.isEdit;
          })
          .includes(false);
      },
      // 新增股权信息 and 证照信息
      handleAdd(key) {
        const list =
          key === '1' ? this.equityInformationList : this.licenseList;
        if (key === '1') {
          const obj = {
            shareholderName: '',
            capitalContribution: '',
            fundedRatio: '',
            fundedTime: '',
            paidIn: '',
            isEdit: false,
            key: Date.now(),
          };
          const obj1 = {
            shareholderName: '',
            totalHold: '',
            totalRatio: '',
            isEdit: false,
            key: Date.now(),
          };
          if (this.tab === 0) {
            list.push({
              ...obj,
            });
          } else {
            list.push({
              ...obj1,
            });
          }
        } else {
          list.push({
            licenseName: '',
            competentAuthority: '',
            issueDate: '',
            validityPeriod: '',
            attachmentUrl: '',
            urlName: '',
            idCode: '',
            updateDate: '',
            isEdit: false,
            key: Date.now(),
          });
        }
      },
      // 删除项
      handleDelete(row, key) {
        const list =
          key === '1' ? this.equityInformationList : this.licenseList;
        const index = list.indexOf(row);
        if (row.id && !row.isEdit) {
          const arr = list.map(item => {
            if (row.id === item.id) {
              return {
                ...item,
                isEdit: !item.isEdit,
              };
            }
            return item;
          });
          if (key === '1') {
            this.equityInformationList = arr;
          } else {
            this.licenseList = arr;
          }
          return;
        }
        if (index !== -1) {
          list.splice(index, 1);
        }
      },
    },
  };
</script>
<style lang="scss">
  .editSubjectInformation {
    .el-alert__content {
      width: 100%;
      .el-alert-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }
    .el-form-item--small.el-form-item {
      width: 30%;
    }
    .table-from.el-form-item {
      display: block;
      width: 100%;
    }
  }
</style>
