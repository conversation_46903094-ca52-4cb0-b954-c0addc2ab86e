<template>
  <div class="pricingModelMgt">
    <el-form inline>
      <!-- <el-form-item label="一级交易:">
        <el-select
          v-model="searchParams.tradeType1"
          placeholder="请选择证照类型"
          @change="handleSelect1"
        >
          <el-option
            v-for="item in tradeType1List"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item> -->

      <!-- <el-form-item label="三级交易:">
        <el-select
          v-model="searchParams.tradeType3"
          placeholder="请选择证照类型"
        >
          <el-option
            v-for="item in tradeType3List"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item> -->
      <transactionType
        :form="searchParams"
        :is-required-obj="{
          tradeType1: false,
          tradeType2: false,
          tradeType3: false,
        }"
      ></transactionType>

      <el-form-item label="区域:">
        <el-select
          v-model="searchParams.areaScope"
          placeholder="请选择证照类型"
        >
          <el-option
            v-for="item in areaList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="利润率指标:">
        <el-select
          v-model="searchParams.rateOfProfit"
          placeholder="请选择证照类型"
        >
          <el-option
            v-for="item in profitMarginList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <ac-permission-button
          type="primary"
          btn-text="新建"
          permission-key="pricingModelMgt-add"
          @click="
            showDialog = true;
            currentRecord = null;
          "
        ></ac-permission-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="list" style="width: 100%">
      <el-table-column prop="id" label="规则编号"></el-table-column>
      <el-table-column prop="tradeType1Desc" label="一级交易"></el-table-column>
      <el-table-column prop="tradeType2Desc" label="二级交易"></el-table-column>
      <el-table-column prop="tradeType3Desc" label="三级交易"></el-table-column>
      <el-table-column
        prop="areaScopeDesc"
        label="可比分析地域范围"
      ></el-table-column>
      <el-table-column
        prop="rateOfProfitDesc"
        label="利润率指标"
      ></el-table-column>
      <el-table-column
        label="基准分析结果（最小值-下四分位-中位值-上四分位-最大值）"
        align="center"
      >
        <el-table-column prop="analysisMin"></el-table-column>
        <el-table-column prop="analysisLowerQuartile"></el-table-column>
        <el-table-column prop="analysisMedian"></el-table-column>
        <el-table-column prop="analysisUpperQuartile"></el-table-column>
        <el-table-column prop="analysisMax"></el-table-column>
      </el-table-column>
      <el-table-column prop="effectiveDate" label="起效日期"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <ac-permission-button
            type="text"
            size="small"
            btn-text="编辑"
            permission-key="pricingModelMgt-edit"
            @click="
              currentRecord = scope.row;
              showDialog = true;
            "
          ></ac-permission-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      ref="listPage"
      background
      :current-page.sync="pagination.current"
      :page-size.sync="pagination.size"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @current-change="pagination_currentChange"
      @size-change="pagination_sizeChange"
    ></el-pagination>
    <addPricingModel
      v-model="showDialog"
      :record="currentRecord"
      :area-list="areaList"
      :profit-margin-list="profitMarginList"
      @onGet="getList(true)"
    ></addPricingModel>
  </div>
</template>

<script>
  import { getDataDictAll, getPricingModelList } from '@/api/subjectMgt';
  import addPricingModel from './components/addPricingModel';
  import transactionType from './components/transactionType';
  export default {
    components: {
      addPricingModel,
      transactionType,
    },
    data() {
      return {
        showDialog: false,
        currentRecord: null,
        searchParams: {
          rateOfProfit: '',
          areaScope: '',
          tradeType1: '',
          tradeType2: '',
          tradeType3: '',
        },
        tradeType1List: [],
        tradeType3List: [],
        areaList: [],
        allSelectList: {},
        profitMarginList: [],
        list: [],
        loading: false,
        total: null,
        buyType: '',
        serviceType: '',
        pagination: {
          current: 1,
          size: 10,
        },
      };
    },
    created() {
      getDataDictAll().then(res => {
        if (res) {
          this.allSelectList = res;
          this.tradeType1List = this.getTypeList(res.AT_TRADE_TYPE_1);
          this.areaList = this.getTypeList(res.AREA_SCOPE);
          this.profitMarginList = this.getTypeList(res.PM_RATE_OF_PROFIT);
          // this.tradeType3List = this.getTradeType3List(res.SERVICE_TRADE_TYPE_3)
          this.tradeType1List.map(item => {
            if (item.name === '购销交易') {
              this.buyType = item.id;
            }
            if (item.name === '服务交易') {
              this.serviceType = item.id;
            }
          });
        }
      });
      this.getList(true);
    },
    methods: {
      getTradeType3List(obj) {
        let arr = [];
        Object.keys(obj).forEach(item1 => {
          Object.keys(obj[item1]).forEach(item2 => {
            arr.push({
              id: Number(item2) ? Number(item2) : item2,
              name: obj[item1][item2],
            });
          });
        });
        return arr;
      },
      handleSelect1(val) {
        this.tradeType3List = [];
        // 选择购销交易类型
        if (val == this.buyType) {
          this.tradeType3List = this.getTypeList(this.allSelectList.CATEGORY);
        }
        // 选择服务交易类型
        if (val == this.serviceType) {
          this.tradeType3List = this.getTradeType3List(
            this.allSelectList.SERVICE_TRADE_TYPE_3,
          );
          // this.tradeType3List = this.getTypeList(
          //   this.allSelectList.COMPANY_MAIN,
          // )
        }
      },
      getTypeList(obj) {
        if (!obj) return [];
        return Object.keys(obj).map(item => {
          return {
            id: Number(item) ? Number(item) : item,
            name: obj[item],
          };
        });
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.current = 1;
        }
        const params = {
          ...this.searchParams,
          ...this.pagination,
        };
        this.loading = true;
        const res = await getPricingModelList(params);
        this.loading = false;
        this.list = res ? res.records : [];
        this.total = res ? res.total : 0;
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.getList();
      },
      pagination_currentChange(current) {
        this.pagination.current = current;
        this.getList();
      },
      pagination_sizeChange(size) {
        this.pagination.current = 1;
        this.pagination.size = size;
        this.getList();
      },
    },
  };
</script>
<style lang="scss">
  .pricingModelMgt {
    .el-table .is-group tr:nth-child(2) {
      display: none;
    }
  }
</style>
