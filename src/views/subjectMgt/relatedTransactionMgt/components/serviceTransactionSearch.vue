<template>
  <div>
    <el-form inline>
      <el-row>
        <el-col :span="8">
          <el-form-item label="二级交易:">
            <el-select
              v-model="searchParams.tradeType2"
              placeholder="请选择"
              @change="handleSelect2"
            >
              <el-option
                v-for="item in SERVICE_TRADE_TYPE_2"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="三级交易:">
            <el-select v-model="searchParams.tradeType3" placeholder="请选择">
              <el-option
                v-for="item in SERVICE_TRADE_TYPE_3"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="状态:">
            <el-select v-model="searchParams.status" placeholder="请选择">
              <el-option
                v-for="item in LINK_VIEW_STATUS"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-form-item
          :label="searchParams.logic === 2 ? '提供/接收方:' : '提供方:'"
        >
          <el-select
            v-model="searchParams.providerCompanyCode"
            placeholder="请选择"
          >
            <el-option
              v-for="item in COMPANY_MAIN"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-radio-group v-model="searchParams.logic" style="margin: 0 10px">
          <el-radio-button :label="1">并且</el-radio-button>
          <el-radio-button :label="2">或者</el-radio-button>
        </el-radio-group>

        <el-form-item v-if="searchParams.logic === 1" label="接收方:">
          <el-select
            v-model="searchParams.consumerCompanyCode"
            placeholder="请选择"
          >
            <el-option
              v-for="item in COMPANY_MAIN"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="primary" @click="onReset">重置</el-button>
          <span
            v-if="routerType === 'serviceTransactionLog'"
            style="margin-left: 10px"
          >
            <ac-permission-button
              type="primary"
              btn-text="复核"
              permission-key="serviceTransactionLog-review"
              @click="handleOperating(0)"
            ></ac-permission-button>
            <ac-permission-button
              type="primary"
              btn-text="发起开票"
              permission-key="serviceTransactionLog-invoicing"
              @click="handleOperating(1)"
            ></ac-permission-button>
            <ac-permission-button
              type="primary"
              btn-text="发起审批"
              permission-key="serviceTransactionLog-trial"
              @click="handleOperating(2)"
            ></ac-permission-button>
          </span>
          <el-button style="margin-left: 10px" @click="() => $router.go(-1)">
            返回
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
  </div>
</template>

<script>
  import { getDataDictAll } from '@/api/subjectMgt';
  export default {
    props: {
      routerType: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        allSelectList: {},
        SERVICE_TRADE_TYPE_2: [],
        SERVICE_TRADE_TYPE_3: [],
        COMPANY_MAIN: [],
        LINK_VIEW_STATUS: [],
        searchParams: {
          logic: 1,
          consumerCompanyCode: '',
          providerCompanyCode: '',
          status: '',
          tradeType2: '',
          tradeType3: '',
        },
      };
    },
    created() {
      const query = this.$route.query;
      const {
        tradeType2,
        tradeType3,
        providerCompanyCode,
        consumerCompanyCode,
        logic = 1,
        status,
      } = query;
      this.searchParams = {
        ...this.searchParams,
        tradeType2: Number(tradeType2) ? Number(tradeType2) : tradeType2,
        tradeType3: Number(tradeType3) ? Number(tradeType3) : tradeType3,
        providerCompanyCode: Number(providerCompanyCode)
          ? Number(providerCompanyCode)
          : providerCompanyCode,
        consumerCompanyCode: Number(consumerCompanyCode)
          ? Number(consumerCompanyCode)
          : consumerCompanyCode,
        logic: logic,
        status: status,
      };
      getDataDictAll({}).then(res => {
        if (res) {
          this.allSelectList = res;
          this.COMPANY_MAIN = this.getTypeList(res.COMPANY_MAIN);
          this.LINK_VIEW_STATUS = this.getTypeList(
            this.routerType === 'serviceTransactionLog'
              ? res.SETTLEMENT_LOG_STATUS
              : res.LINK_VIEW_STATUS,
          );
          this.SERVICE_TRADE_TYPE_2 = this.getTypeList(
            res.SERVICE_TRADE_TYPE_2_ALL,
          );
          if (this.searchParams.tradeType2) {
            this.handleSelect2(this.searchParams.tradeType2);
          }
        }
      });
      this.onSearch();
    },
    methods: {
      getTypeList(obj) {
        if (!obj) return [];
        return Object.keys(obj).map(item => {
          return {
            id: Number(item) ? Number(item) : item,
            name: obj[item],
          };
        });
      },
      handleSelect2(val) {
        this.SERVICE_TRADE_TYPE_3 = this.getTypeList(
          this.allSelectList.SERVICE_TRADE_TYPE_3_ALL[val],
        );
      },
      onSearch() {
        this.$emit('onSearch', this.searchParams);
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.onSearch();
      },
      handleOperating(val) {
        this.$emit('handleOperating', val);
      },
    },
  };
</script>
<style lang="scss" scoped></style>
