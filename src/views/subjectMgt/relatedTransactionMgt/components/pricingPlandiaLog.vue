<template>
  <el-dialog
    class="pricing-modal"
    width="1000px"
    :visible.sync="showDialog"
    :show-close="false"
  >
    <el-row class="modal-top">
      <el-col :span="5">
        <span>一级交易: {{ record ? record.tradeType1Desc : '' }}</span>
      </el-col>
      <el-col :span="5">
        <span>二级交易: {{ record ? record.tradeType2Desc : '' }}</span>
      </el-col>
      <el-col :span="5">
        <span>三级交易: {{ record ? record.tradeType3Desc : '' }}</span>
      </el-col>
      <el-col :span="5">
        <span>
          区域: {{ getAreaScopeDesc(record) }}
          <!-- {{
            record
              ? record.areaScopeDesc || pricingPlanSelect[0]
                ? pricingPlanSelect[0].areaScopeDesc
                : ''
              : ''
          }} -->
        </span>
      </el-col>
      <el-col :span="4">
        <el-button type="primary" @click="showDialog = false">关闭</el-button>
      </el-col>
    </el-row>
    <el-table :data="pricingPlanSelect" style="width: 100%">
      <el-table-column
        prop="rateOfProfitDesc"
        label="利润率指标"
        width="150"
      ></el-table-column>
      <el-table-column
        label="基准分析结果（最小值-下四分位-中位值-上四分位-最大值）"
        align="center"
      >
        <el-table-column prop="analysisMin"></el-table-column>
        <el-table-column prop="analysisLowerQuartile"></el-table-column>
        <el-table-column prop="analysisMedian"></el-table-column>
        <el-table-column prop="analysisUpperQuartile"></el-table-column>
        <el-table-column prop="analysisMax"></el-table-column>
      </el-table-column>
      <el-table-column v-if="!isHideOperating" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleSelect(scope.row)">
            选择
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script>
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      isHideOperating: {
        type: Boolean,
        default: false,
      },
      record: {
        type: Object,
        default: null,
      },
      pricingPlanSelect: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        tableData: [],
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {
        if (value) {
          this.getList();
          // if (this.record) {
          //   this.getList()
          // }
        }
      },
    },
    methods: {
      getAreaScopeDesc(val) {
        if (val && val.areaScopeDesc) {
          return val.areaScopeDesc;
        }
        if (this.pricingPlanSelect.length) {
          return this.pricingPlanSelect[0].areaScopeDesc;
        }
      },
      getList() {
        this.tableData = [
          { rateOfProfitDesc: '完全成本加成率', pricingModelId: '22' },
          { rateOfProfitDesc: '息税前利润率', pricingModelId: '333' },
        ];
      },
      handleSelect(val) {
        this.$emit('onSelect', val);
      },
    },
  };
</script>
<style lang="scss">
  .pricing-modal {
    .modal-top {
      padding: 20px;
      margin-bottom: 10px;
      border: 1px solid #dcdfe6;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .el-table .is-group tr:nth-child(2) {
      display: none;
    }
  }
</style>
