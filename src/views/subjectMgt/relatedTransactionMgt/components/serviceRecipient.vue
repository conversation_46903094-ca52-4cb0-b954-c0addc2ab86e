<template>
  <el-dialog
    :width="id ? '400px' : '800px'"
    :visible.sync="showDialog"
    @closed="showDialog = false"
  >
    <el-row>
      <el-col v-if="!id" :span="12">
        <div class="list" style="overflow: auto; max-height: 400px">
          <div
            v-for="(i, inx) in list"
            :key="i.id"
            :class="{ active: selectIds.includes(i.id) }"
            @click="handelSelect(i, inx)"
          >
            {{ i.name }}
          </div>
        </div>
      </el-col>
      <el-col :span="id ? 24 : 12">
        <el-form ref="formData" label-width="100px" :model="form">
          <el-form-item
            label="起效日期:"
            prop="effectTime"
            :rules="[
              {
                required: true,
                message: '请选择',
                trigger: 'change',
              },
            ]"
          >
            <el-date-picker
              v-model="form.effectTime"
              type="month"
              placeholder="选择日期"
              format="yyyy 年 MM 月"
              value-format="yyyy-MM"
            ></el-date-picker>
          </el-form-item>
          <el-form-item
            label="失效日期:"
            prop="expireTime"
            :rules="[
              {
                required: true,
                message: '请选择',
                trigger: 'change',
              },
            ]"
          >
            <el-date-picker
              v-model="form.expireTime"
              type="month"
              placeholder="选择日期"
              format="yyyy 年 MM 月"
              value-format="yyyy-MM"
            ></el-date-picker>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

    <div slot="footer" class="dialog-footer">
      <el-button @click="showDialog = false">取 消</el-button>
      <el-button type="primary" @click="onOK">保 存</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { serviceConsumerAdd, serviceConsumerEdit } from '@/api/subjectMgt';
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      record: {
        type: Object,
        default: null,
      },
      providerId: {
        type: Number,
        default: null,
      },
      list: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        form: {
          effectTime: '',
          expireTime: '',
        },
        selectIds: [],
        id: null, // 编辑状态的id
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {
        if (value) {
          if (this.record) {
            const { consumerId, effectDate, expireDate } = this.record;
            this.id = consumerId;
            this.form.effectTime = effectDate;
            this.form.expireTime = expireDate;
          } else {
            this.id = null;
          }
        } else {
          Object.assign(this.$data.form, this.$options.data().form);
          this.$nextTick(function () {
            this.$refs.formData.clearValidate();
          });
          this.selectIds = [];
        }
      },
    },
    methods: {
      handelSelect(i, inx) {
        const index = this.selectIds.indexOf(i.id);
        if (index !== -1) {
          this.selectIds.splice(index, 1);
        } else {
          this.selectIds.push(i.id);
        }
      },
      bjDate(date, date1) {
        var date = new Date(date);
        var date1 = new Date(date1);
        if (date.getTime() - date1.getTime() < 0) {
          return false;
        } else {
          return true;
        }
      },
      onOK() {
        this.$refs.formData.validate(async valid => {
          if (!valid) return;
          if (this.selectIds.length === 0 && !this.id) {
            this.$message.error('请选择主体');
            return;
          }
          const api = this.id ? serviceConsumerEdit : serviceConsumerAdd;
          let params = {
            ...this.form,
            providerId: this.providerId,
            companyCode: this.selectIds,
          };
          const editParams = {
            effectDate: this.form.effectTime,
            expireDate: this.form.expireTime,
            id: this.id,
          };
          if (
            this.id &&
            this.bjDate(editParams.effectDate, editParams.expireDate)
          ) {
            this.$message.error('失效日期必须大于起效日期');
            return;
          }
          if (this.bjDate(params.effectTime, params.expireTime)) {
            this.$message.error('失效日期必须大于起效日期');
            return;
          }

          await api(this.id ? editParams : params);
          this.$message.success(this.id ? '编辑成功' : '新建成功');
          this.showDialog = false;
          this.$emit('onGet');
        });
      },
    },
  };
</script>
<style lang="scss" scoped>
  .list div {
    padding: 4px;
    margin: 5px;
  }
  .garden {
    margin-left: 8px;
    font-size: 18px;
    border: 1px solid #dcdfe6;
    border-radius: 50%;
  }
  .active {
    background: #edf6ff;
  }
</style>
