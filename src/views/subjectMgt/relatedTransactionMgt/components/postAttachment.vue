<template>
  <el-dialog :visible.sync="showDialog">
    <div style="display: flex">
      <span>文件附件：</span>
      <div style="margin-left: 10px">
        <uploadImg
          btn-text="上传附件"
          list-type="text"
          :type-file="[]"
          :max="1"
          :init-file-list="fileList"
          @onRemove="onRemove"
          @changeImage="changeImage"
        />
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onOK">保 存</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import uploadImg from '@/components/uploadImg';
  export default {
    components: {
      uploadImg,
    },
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      record: {
        type: Object,
        default: null,
      },
    },
    data() {
      return {
        fileList: [],
        files: null,
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {
        if (value) {
          if (this.record) {
            this.fileList = this.record.attachmentUrl
              ? [
                  {
                    name: this.record.urlName,
                    url: this.record.attachmentUrl,
                  },
                ]
              : [];
          }
        } else {
          this.fileList = [];
        }
      },
    },
    methods: {
      onRemove(files, fileList) {
        this.files = null;
      },
      changeImage(files, fileList) {
        this.files = files;
      },
      onOK() {
        this.$emit('onOK', this.files);
        this.showDialog = false;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
