<template>
  <el-dialog width="500px" :visible.sync="showDialog" @closed="onClose">
    <el-form ref="formData" label-width="100px" :model="form">
      <el-form-item
        label="名称"
        prop="name"
        :rules="[
          {
            required: true,
            message: '请输入',
            trigger: 'change',
          },
        ]"
      >
        <el-input
          v-model="form.name"
          placeholder="请输入名称"
          style="width: 220px"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="类型:"
        prop="type"
        :rules="[
          {
            required: true,
            message: '请选择',
            trigger: 'change',
          },
        ]"
      >
        <el-select
          v-model="form.type"
          style="width: 220px"
          placeholder="请选择类型"
        >
          <el-option
            v-for="item in typeList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        v-for="(i, index) in companyPositionList"
        :key="i.key"
        label="主体定位:"
        required
        :error="i.errorMsg"
      >
        <el-select
          v-model="i.value"
          style="width: 220px"
          placeholder="请选择主体定位"
          @change="checkCompanyPosition(i)"
        >
          <el-option
            v-for="item in subjectPositioningList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
        <span>
          <i
            class="el-icon-top garden"
            @click="birthOrder(companyPositionList, index)"
          ></i>
          <i
            class="el-icon-bottom garden"
            @click="Descending(companyPositionList, index)"
          ></i>
        </span>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="showDialog = false">取 消</el-button>
      <el-button type="primary" @click="onSave">保 存</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { saveOrUpdateLinkModel } from '@/api/subjectMgt';
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      record: {
        type: Object,
        default: null,
      },
      typeList: {
        type: Array,
        default: () => [],
      },
      subjectPositioningList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        form: {
          companyPositionIds: [],
          id: '',
          name: '',
          operator: '',
          type: '',
        },
        companyPositionList: [
          { key: 1, value: '' },
          { key: 2, value: '' },
          { key: 3, value: '' },
          { key: 4, value: '' },
        ],
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {
        if (value && this.record) {
          const { id, name, type } = this.record;
          this.form = {
            ...this.form,
            id,
            name,
            type,
          };
        }
      },
    },
    methods: {
      onClose() {
        Object.assign(this.$data.form, this.$options.data().form);
        Object.assign(
          this.$data.companyPositionList,
          this.$options.data().companyPositionList,
        );
        this.$nextTick(function () {
          this.$refs.formData.clearValidate();
        });
      },
      swapItems(arr, index1, index2) {
        arr[index1] = arr.splice(index2, 1, arr[index1])[0];
        return arr;
      },
      birthOrder(arr, $index) {
        if ($index == 0) {
          return;
        }
        this.swapItems(arr, $index, $index - 1);
      },
      Descending(arr, $index) {
        if ($index == arr.length - 1) {
          return;
        }
        this.swapItems(arr, $index, $index + 1);
      },
      onSave() {
        this.$refs.formData.validate(async valid => {
          this.checkCompanyPosition();
          const companyPositionIds = this.getCompanyPositionIds();
          if (!valid) return;
          if (companyPositionIds.length !== 4) return;
          await saveOrUpdateLinkModel({
            ...this.form,
            companyPositionIds,
          });
          this.showDialog = false;
          this.$emit('onGet');
        });
      },
      getCompanyPositionIds() {
        return this.companyPositionList
          .map(item => item.value)
          .filter(i => i !== '');
      },
      checkCompanyPosition(i) {
        this.companyPositionList = this.companyPositionList.map(item => {
          if (i) {
            if (i.key === item.key && item.value === '') {
              return {
                ...item,
                errorMsg: '请选择',
              };
            } else {
              return {
                ...item,
                errorMsg: '',
              };
            }
          } else {
            if (item.value === '') {
              return {
                ...item,
                errorMsg: '请选择',
              };
            } else {
              return {
                ...item,
                errorMsg: '',
              };
            }
          }
        });
      },
    },
  };
</script>
<style lang="scss" scoped>
  .garden {
    margin-left: 8px;
    font-size: 18px;
    border: 1px solid #dcdfe6;
    border-radius: 50%;
  }
</style>
