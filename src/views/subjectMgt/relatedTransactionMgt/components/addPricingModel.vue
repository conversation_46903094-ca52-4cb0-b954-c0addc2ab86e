<template>
  <el-dialog
    class="addPricingModel"
    width="1240px"
    :visible.sync="showDialog"
    @closed="onClose"
  >
    <el-form ref="formData" class="form" :model="form" inline>
      <!-- <el-form-item
        label="一级交易类型:"
        prop="tradeType1"
        :rules="[
          {
            required: true,
            message: '请选择',
            trigger: 'change',
          },
        ]"
      >
        <el-select v-model="form.tradeType1" placeholder="请选择">
          <el-option
            v-for="item in statusList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="二级交易类型:"
        prop="tradeType2"
        :rules="[
          {
            required: true,
            message: '请选择',
            trigger: 'change',
          },
        ]"
      >
        <el-select v-model="form.tradeType2" placeholder="请选择">
          <el-option
            v-for="item in statusList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="三级交易类型:"
        prop="tradeType3"
        :rules="[
          {
            required: true,
            message: '请选择',
            trigger: 'change',
          },
        ]"
      >
        <el-select v-model="form.tradeType3" placeholder="请选择">
          <el-option
            v-for="item in statusList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item> -->
      <transactionType
        :form="form"
        :id-disabled="id ? true : false"
      ></transactionType>
      <el-form-item
        label="可比分析地域:"
        prop="areaScope"
        :rules="[
          {
            required: true,
            message: '请选择',
            trigger: 'change',
          },
        ]"
      >
        <el-select
          v-model="form.areaScope"
          :disabled="id ? true : false"
          placeholder="请选择"
        >
          <el-option
            v-for="item in areaList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="利润率指标:"
        prop="rateOfProfit"
        :rules="[
          {
            required: true,
            message: '请选择',
            trigger: 'change',
          },
        ]"
      >
        <el-select
          v-model="form.rateOfProfit"
          :disabled="id ? true : false"
          placeholder="请选择"
        >
          <el-option
            v-for="item in profitMarginList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item style="float: right">
        <el-button type="primary" @click="handelAdd">添加</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" style="width: 100%; margin-top: 10px">
      <el-table-column prop="effectiveDate" label="起效日期" width="200px">
        <template slot-scope="{ row }">
          <el-date-picker
            v-if="!row.isEdit"
            v-model="row.effectiveDate"
            style="width: 200px"
            type="date"
            placeholder="选择日期"
            format="yyyy 年 MM 月 dd 日"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
          ></el-date-picker>
          <span v-else>{{ row.effectiveDate }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="基准分析结果（最小值-下四分位-中位值-上四分位-最大值）"
        align="center"
      >
        <el-table-column prop="analysisMin">
          <template slot-scope="{ row }">
            <el-input v-if="!row.isEdit" v-model="row.analysisMin"></el-input>
            <span v-else>{{ row.analysisMin }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="analysisLowerQuartile">
          <template slot-scope="{ row }">
            <el-input
              v-if="!row.isEdit"
              v-model="row.analysisLowerQuartile"
            ></el-input>
            <span v-else>{{ row.analysisLowerQuartile }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="analysisMedian">
          <template slot-scope="{ row }">
            <el-input
              v-if="!row.isEdit"
              v-model="row.analysisMedian"
            ></el-input>
            <span v-else>{{ row.analysisMedian }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="analysisUpperQuartile">
          <template slot-scope="{ row }">
            <el-input
              v-if="!row.isEdit"
              v-model="row.analysisUpperQuartile"
            ></el-input>
            <span v-else>{{ row.analysisUpperQuartile }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="analysisMax">
          <template slot-scope="{ row }">
            <el-input v-if="!row.isEdit" v-model="row.analysisMax"></el-input>
            <span v-else>{{ row.analysisMax }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="可比公司财务数据年度" align="center">
        <el-table-column prop="comparableYear1">
          <template slot-scope="{ row }">
            <el-date-picker
              v-if="!row.isEdit"
              v-model="row.comparableYear1"
              style="width: 150px"
              type="year"
              placeholder="选择年"
              format="yyyy"
              value-format="yyyy"
            ></el-date-picker>
            <span v-else>{{ row.comparableYear1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="comparableYear2">
          <template slot-scope="{ row }">
            <el-date-picker
              v-if="!row.isEdit"
              v-model="row.comparableYear2"
              style="width: 150px"
              type="year"
              placeholder="选择年"
              format="yyyy"
              value-format="yyyy"
            ></el-date-picker>
            <span v-else>{{ row.comparableYear2 }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            :disabled="isDis(scope.row)"
            type="text"
            size="small"
            @click="handleEdit(scope.row, '1', scope.row.isEdit)"
          >
            {{ scope.row.isEdit ? '编辑' : '提交' }}
          </el-button>
          <el-button
            v-if="!scope.row.id"
            type="text"
            size="small"
            @click="handleEdit(scope.row, '2', scope.$index)"
          >
            取消
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="showDialog = false">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import {
    pricingModelAdd,
    pricingModelEdit,
    getPricingModelList,
  } from '@/api/subjectMgt';
  import transactionType from './transactionType';

  export default {
    components: {
      transactionType,
    },
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      areaList: {
        type: Array,
        default: () => [],
      },
      record: {
        type: Object,
        default: null,
      },
      profitMarginList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        form: {
          tradeType1: '',
          tradeType2: '',
          tradeType3: '',
          rateOfProfit: '',
          areaScope: '',
        },
        id: null,
        curDate: new Date(),
        list: [],
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now();
          },
        },
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {
        if (value) {
          if (this.record) {
            const {
              id,
              tradeType1,
              tradeType2,
              tradeType3,
              rateOfProfit,
              areaScope,
            } = this.record;
            this.form = {
              tradeType1: Number(tradeType1) ? Number(tradeType1) : tradeType1,
              tradeType2: Number(tradeType2) ? Number(tradeType2) : tradeType2,
              tradeType3: Number(tradeType3) ? Number(tradeType3) : tradeType3,
              rateOfProfit: Number(rateOfProfit)
                ? Number(rateOfProfit)
                : rateOfProfit,
              areaScope: Number(areaScope) ? Number(areaScope) : areaScope,
            };
            this.getList();
            this.id = id;
          } else {
            this.id = null;
          }
        }
      },
    },
    methods: {
      isDis({ effectiveDate }) {
        if (effectiveDate) {
          const selectTime = new Date(
            Date.parse(effectiveDate.replace(/-/g, '/')),
          );
          return selectTime < Date.now();
        }
        return false;
      },
      onClose() {
        Object.assign(this.$data.form, this.$options.data().form);
        this.list = [];
        this.$nextTick(function () {
          this.$refs.formData.clearValidate();
        });
      },
      handelAdd() {
        this.$refs.formData.validate(async valid => {
          if (!valid) return;
          this.list.push({
            ...this.form,
            isEdit: false,
            key: Date.now(),
          });
          // Object.assign(this.$data.form, this.$options.data().form)
          // this.$nextTick(function () {
          //   this.$refs.formData.clearValidate()
          // })
        });
      },
      check(params) {
        const checkData = [
          'effectiveDate',
          'analysisMin',
          'analysisLowerQuartile',
          'analysisMedian',
          'analysisUpperQuartile',
          'analysisMax',
          'comparableYear1',
          'comparableYear2',
        ];
        let flag = false;
        checkData.forEach(item => {
          if (!params[item]) {
            flag = true;
          }
        });
        return flag;
      },
      async handleEdit(val, key, index) {
        if (key === '2') {
          this.list = this.list.splice(index + 1, 1);
          return;
        }
        if (!index) {
          let params = { ...this.form, ...val };
          delete params.key;
          delete params.isEdit;
          if (this.check(params)) {
            this.$message.error('全部必填');
            return;
          }

          const saveApi = params.id ? pricingModelEdit : pricingModelAdd;
          await saveApi(params);
          // this.showDialog = false
          this.$message.success(params.id ? '编辑' : '新建成功');
          this.getList();
          this.$emit('onGet');
          return;
        }
        this.list = this.list.map(item => {
          if (item.id === val.id) {
            return {
              ...item,
              isEdit: !val.isEdit,
            };
          }
          return item;
        });
      },
      async getList() {
        const res = await getPricingModelList({
          ...this.form,
          all: true,
          current: 1,
          size: 100,
        });
        if (res) {
          this.list = res.records.map(item => ({ ...item, isEdit: true }));
        } else this.list = [];
        // this.list = [
        //   {
        //     ...this.record,
        //     isEdit: true,
        //   },
        // ]
      },
    },
  };
</script>
<style lang="scss" scoped>
  .addPricingModel {
    .form {
      align-items: center;
      padding: 10px 10px 0;
      border: 1px solid #dcdfe6;
    }
  }
</style>
