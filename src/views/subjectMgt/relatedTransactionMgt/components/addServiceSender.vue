<template>
  <el-dialog width="400px" :visible.sync="showDialog" @closed="onClose">
    <el-form ref="formData" label-width="100px" :model="form">
      <el-form-item label="服务发送方:">
        <el-select
          v-model="form.companyCode"
          style="width: 220px"
          placeholder="请选择服务提供方"
        >
          <el-option
            v-for="item in companyList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="一级交易:">
        <el-select
          v-model="form.tradeType1"
          style="width: 220px"
          placeholder="请选择一级交易"
        >
          <el-option label="服务交易" value="SERVICE"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="二级交易:">
        <el-select
          v-model="form.tradeType2"
          style="width: 220px"
          placeholder="请选择二级交易"
          @change="handleSelect2"
        >
          <el-option
            v-for="item in tradeType2List"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="三级交易:">
        <el-select
          v-model="form.tradeType3"
          style="width: 220px"
          placeholder="请选择三级交易"
        >
          <el-option
            v-for="item in tradeType3List"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="showDialog = false">取 消</el-button>
      <el-button type="primary" @click="onSave">保 存</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { serviceProviderAdd } from '@/api/subjectMgt';
  export default {
    model: {
      prop: 'show',
      event: 'change',
    },
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      companyList: {
        type: Array,
        default: () => [],
      },
      allSelectObj: {
        type: Object,
        default: null,
      },
    },
    data() {
      return {
        form: {
          companyCode: '',
          tradeType1: '',
          tradeType2: '',
          tradeType3: '',
        },
        tradeType2List: [],
        tradeType3List: [],
      };
    },
    computed: {
      showDialog: {
        get() {
          return this.show;
        },
        set(value) {
          this.$emit('change', value);
        },
      },
    },
    watch: {
      show(value) {
        if (value) {
          if (this.allSelectObj) {
            this.tradeType2List = this.getTypeList(
              this.allSelectObj['SERVICE_TRADE_TYPE_2'],
            );
          }
        }
      },
    },

    methods: {
      handleSelect2(val) {
        if (val) {
          this.tradeType3List = this.getTypeList(
            this.allSelectObj.SERVICE_TRADE_TYPE_3[val],
          );
        }
      },
      onClose() {
        Object.assign(this.$data.form, this.$options.data().form);
        this.$nextTick(function () {
          this.$refs.formData.clearValidate();
        });
      },
      getTypeList(obj) {
        if (!obj) return [];
        return Object.keys(obj).map(item => {
          return {
            id: Number(item) ? Number(item) : item,
            name: obj[item],
          };
        });
      },
      onSave() {
        this.$refs.formData.validate(async valid => {
          if (!valid) return;
          await serviceProviderAdd(this.form);
          this.showDialog = false;
          this.$message.success('新增成功');
          this.$emit('onGet');
        });
      },
    },
  };
</script>
<style lang="scss" scoped>
  .garden {
    margin-left: 8px;
    font-size: 18px;
    border: 1px solid #dcdfe6;
    border-radius: 50%;
  }
</style>
