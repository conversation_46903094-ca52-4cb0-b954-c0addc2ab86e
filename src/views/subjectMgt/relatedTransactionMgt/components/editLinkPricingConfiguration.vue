<template>
  <div>
    <el-dialog width="1000px" :visible.sync="visible" @closed="onClose">
      <el-form ref="formData" label-width="100px" :model="form">
        <el-form-item
          label="生效日期："
          prop="effectiveDate"
          :rules="[
            {
              required: true,
              message: '请选择',
              trigger: 'change',
            },
          ]"
        >
          <el-date-picker
            v-model="form.effectiveDate"
            type="date"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            :picker-options="pickerOptions"
          ></el-date-picker>
        </el-form-item>
        <el-table :data="list" style="width: 100%">
          <el-table-column
            prop="buyerCompanyName"
            label="购方"
          ></el-table-column>
          <el-table-column
            prop="sellerCompanyName"
            label="销方"
          ></el-table-column>
          <el-table-column
            prop="pricingModelDesc"
            label="利润率指标"
          ></el-table-column>
          <el-table-column
            prop="recommendation"
            label="建议四分位区间"
          ></el-table-column>
          <el-table-column
            prop="actualBonusRate"
            label="实际成本加成率"
            width="150"
          >
            <template slot-scope="scope">
              <el-form-item
                required
                :error="scope.row.actualBonusRateMsg"
                label-width="0px"
              >
                <el-input
                  v-model="scope.row.actualBonusRate"
                  onkeyup="value=value.replace(/[^\d.]/g,'')"
                ></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column
            prop="theoryBonusRate"
            label="理论成本加成率"
            width="150"
          >
            <template slot-scope="scope">
              <el-form-item
                required
                :error="scope.row.theoryBonusRateMsg"
                label-width="0px"
              >
                <el-input v-model="scope.row.theoryBonusRate"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
        <div style="display: flex; margin-top: 20px">
          <el-form-item
            label="责任人："
            prop="responsible"
            :rules="[
              {
                required: true,
                message: '请选择',
                trigger: 'change',
              },
            ]"
          >
            <el-select
              v-model="form.responsible"
              style="width: 200px"
              placeholder="请选择"
            >
              <el-option
                v-for="item in RESPONSIBLE"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item required :error="errorTip">
            <uploadImg
              ref="uploadImg"
              btn-text="上传附件"
              list-type="text"
              :init-file-list="initFileList"
              :max="1"
              @onRemove="onRemove"
              @changeImage="changeImage"
            />
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
        <el-button type="primary" @click="handleOk">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import uploadImg from '@/components/uploadImg';
  import {
    savePurchaseSalePriceConfig,
    getDataDictAll,
  } from '@/api/subjectMgt';
  import { parseTime } from '@/utils';
  export default {
    components: {
      uploadImg,
    },
    props: {
      purchaseSaleId: {
        type: Number,
        default: null,
      },
    },
    data() {
      return {
        form: {
          effectiveDate: '',
          attachmentUrl: '',
          responsible: '',
          purchaseSaleId: '',
        },
        errorTip: '',
        visible: false,
        list: [],
        initFileList: [],
        id: '',
        RESPONSIBLE: [],
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now();
          },
        },
      };
    },
    created() {
      getDataDictAll().then(res => {
        if (res) {
          this.RESPONSIBLE = res.RESPONSIBLE;
        }
      });
    },
    methods: {
      onRemove() {
        this.form.attachmentUrl = '';
        if (this.form.attachmentUrl === '') {
          this.errorTip = '请上传附件';
        }
      },
      changeImage(files, fileList) {
        if (files) {
          this.form.attachmentUrl = files.file_url;
          this.errorTip = '';
        }
      },
      getNodeReqVOList(arr) {
        const newArr = arr.map(item => {
          let newItem = {
            ...item,
          };
          if (newItem.actualBonusRate == '') {
            newItem.actualBonusRateMsg = '请输入';
          } else {
            newItem.actualBonusRateMsg = '';
          }
          if (newItem.actualBonusRate == '') {
            newItem.theoryBonusRateMsg = '请输入';
          } else {
            newItem.theoryBonusRateMsg = '';
          }
          return newItem;
        });
        this.list = newArr;
      },
      getNodeReqVOListParams(arr) {
        return arr.map(({ actualBonusRate, atId, theoryBonusRate }) => ({
          actualBonusRate: Number(actualBonusRate),
          atId,
          theoryBonusRate: Number(theoryBonusRate),
        }));
      },
      isRequire(arr) {
        let falg = false;
        arr.map(item => {
          if (!item.actualBonusRate || !item.theoryBonusRate) {
            falg = true;
          }
        });
        return falg;
      },
      handleOk() {
        this.$refs.formData.validate(async valid => {
          // this.getNodeReqVOList(this.list)
          if (this.form.attachmentUrl === '') {
            this.errorTip = '请上传附件';
            return;
          }

          if (this.isRequire(this.getNodeReqVOListParams(this.list))) {
            this.$message.error('实际成本加成率/理论成本加成率 必填');
            return;
          }

          if (!valid) return;
          let params = {
            ...this.form,
            purchaseSaleId: Number(this.purchaseSaleId),
            nodeReqVOList: this.getNodeReqVOListParams(this.list),
          };
          if (this.id) {
            params.linkPriceConfigId = this.id;
          }

          await savePurchaseSalePriceConfig(params);
          this.visible = false;
          this.$emit('onGet');
        });
      },
      onClose() {
        Object.assign(this.$data.form, this.$options.data().form);
        this.$nextTick(function () {
          this.errorTip = '';
          this.initFileList = [];
          this.$refs.formData.clearValidate();
        });
      },
      open(val, res) {
        if (val) {
          this.id = val.id;
          this.initValue(val);
        } else {
          this.id = '';
        }
        if (Array.isArray(res)) {
          this.list = res;
        }
        this.visible = true;
      },
      initValue(val) {
        this.form = {
          effectiveDate: val.effectiveDate
            ? parseTime(val.effectiveDate, '{y}-{m}-{d}')
            : '',
          attachmentUrl: val.attachmentUrl,
          responsible: val.responsible,
          purchaseSaleId: val.purchaseSaleId,
        };
        if (val.attachmentUrl) {
          this.initFileList = [
            {
              url: val.screenshots,
              name: '文件',
            },
          ];
        }
      },
    },
  };
</script>
<style lang="scss" scoped></style>
