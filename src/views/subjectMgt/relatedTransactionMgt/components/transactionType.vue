<template>
  <div>
    <el-form-item
      label="一级交易类型:"
      prop="tradeType1"
      :rules="[
        {
          required: isRequiredObj ? isRequiredObj.tradeType1 : true,
          message: '请选择',
          trigger: 'change',
        },
      ]"
    >
      <el-select
        v-model="form.tradeType1"
        placeholder="请选择"
        :disabled="idDisabled"
        @change="handleSelect1"
      >
        <el-option
          v-for="item in tradeType1List"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item
      label="二级交易类型:"
      prop="tradeType2"
      :rules="[
        {
          required: isRequiredObj ? isRequiredObj.tradeType2 : true,
          message: '请选择',
          trigger: 'change',
        },
      ]"
    >
      <el-select
        v-model="form.tradeType2"
        placeholder="请选择"
        :disabled="idDisabled"
        @change="handleSelect2"
      >
        <el-option
          v-for="item in tradeType2List"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item
      label="三级交易类型:"
      prop="tradeType3"
      :rules="[
        {
          required: isRequiredObj ? isRequiredObj.tradeType3 : true,
          message: '请选择',
          trigger: 'change',
        },
      ]"
    >
      <el-select
        v-model="form.tradeType3"
        :disabled="idDisabled"
        placeholder="请选择"
      >
        <el-option
          v-for="item in tradeType3List"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        ></el-option>
      </el-select>
    </el-form-item>
  </div>
</template>

<script>
  import { getDataDictAll } from '@/api/subjectMgt';
  export default {
    props: {
      form: {
        type: Object,
        default: null,
      },
      isRequiredObj: {
        type: Object,
        default: null,
      },
      idDisabled: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        // form: {
        //   tradeType1: '',
        //   tradeType2: '',
        //   tradeType3: '',
        // },
        buyType: '',
        serviceType: '',
        allSelectList: [],
        tradeType1List: [],
        tradeType2List: [],
        tradeType3List: [],
      };
    },
    created() {
      getDataDictAll().then(res => {
        if (res) {
          this.allSelectList = res;
          this.tradeType1List = this.getTypeList(res.AT_TRADE_TYPE_1);
          this.tradeType1List.map(item => {
            if (item.name === '购销交易') {
              this.buyType = item.id;
            }
            if (item.name === '服务交易') {
              this.serviceType = item.id;
            }
          });
          if (this.form.tradeType1) {
            this.handleSelect1(this.form.tradeType1);
          }
          if (this.form.tradeType2) {
            this.handleSelect2(this.form.tradeType2);
          }
        }
      });
    },
    methods: {
      getTypeList(obj) {
        if (!obj) return [];
        return Object.keys(obj).map(item => {
          return {
            id: Number(item) ? Number(item) : item,
            name: obj[item],
          };
        });
      },
      handleSelect1(val, name) {
        this.tradeType2List = [];
        this.tradeType3List = [];
        // 选择购销交易类型
        if (val == this.buyType) {
          this.tradeType2List = this.getTypeList(this.allSelectList.BRAND);
          this.tradeType3List = this.getTypeList(this.allSelectList.CATEGORY);
        }
        // 选择服务交易类型
        if (val == this.serviceType) {
          this.tradeType2List = this.getTypeList(
            this.allSelectList.SERVICE_TRADE_TYPE_2,
          );
        }
      },
      handleSelect2(val) {
        if (this.form.tradeType1 === this.serviceType) {
          this.tradeType3List = this.getTypeList(
            this.allSelectList.SERVICE_TRADE_TYPE_3[val],
          );
        }
      },
    },
  };
</script>
<style lang="scss" scoped></style>
