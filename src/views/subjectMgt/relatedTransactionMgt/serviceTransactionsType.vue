<template>
  <div class="serviceTransactionsType">
    <el-form ref="form" class="form" inline :model="searchParams">
      <el-col :span="18">
        <el-form-item
          label="二级交易类型:"
          prop="tradeType2"
          :rules="[
            {
              required: true,
              message: '请选择',
              trigger: 'change',
            },
          ]"
        >
          <el-select
            v-model="searchParams.tradeType2"
            placeholder="请选择"
            @change="handleSelect2"
          >
            <el-option
              v-for="item in SERVICE_TRADE_TYPE_2"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="三级交易类型:"
          prop="tradeType2"
          :rules="[
            {
              required: true,
              message: '请选择',
              trigger: 'change',
            },
          ]"
        >
          <el-select v-model="searchParams.tradeType3" placeholder="请选择">
            <el-option
              v-for="item in SERVICE_TRADE_TYPE_3"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态:">{{ statusDesc }}</el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item style="float: right">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <ac-permission-button
            type="primary"
            btn-text="保存"
            permission-key="serviceTransactionsType-save"
            @click="onSave"
          ></ac-permission-button>
          <ac-permission-button
            type="primary"
            btn-text="生效"
            permission-key="serviceTransactionsType-take-effect"
            @click="takeEffect"
          ></ac-permission-button>
          <el-button style="margin-left: 10px" @click="() => $router.go(-1)">
            返回
          </el-button>
        </el-form-item>
      </el-col>
    </el-form>
    <div v-if="form.modelId">
      <el-form
        ref="savaForm"
        :model="form"
        label-position="right"
        label-width="100px"
      >
        <div style="display: flex; margin: 20px 0">
          <el-form-item label="合同模版:">
            <uploadImg
              ref="uploadImg"
              btn-text="上传合同模版"
              list-type="text"
              :type-file="[]"
              :max="1"
              :init-file-list="initFileList"
              @onRemove="onRemove"
              @changeImage="changeImage"
            />
          </el-form-item>
        </div>

        <el-tabs v-model="activeName" @tab-click="handelTabClick">
          <el-tab-pane label="服务提供方" name="0"></el-tab-pane>
          <el-tab-pane label="服务接收方" name="1"></el-tab-pane>
        </el-tabs>
        <div class="form-top">
          <el-form-item label="会计主体:">
            {{ ['服务提供方', '服务接收方'][activeName] }}
          </el-form-item>
          <el-form-item v-if="activeName === '0'" label="成本中心:">
            <el-select
              v-model="form.costCenter"
              multiple
              filterable
              placeholder="请选择"
              style="width: 300px"
            >
              <el-option
                v-for="item in DEPARTMENT"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>

        <el-form-item label="会计期间:">当前结算期间</el-form-item>
        <el-form-item label="借方:">
          <el-select
            v-model="
              form[
                activeName === '0' ? 'providerBorrowType' : 'consumerBorrowType'
              ]
            "
            placeholder="请选择"
          >
            <el-option label="正向-正数" :value="1"></el-option>
            <el-option label="反向-负数" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="贷方:">
          <el-select
            v-model="
              form[activeName === '0' ? 'providerLoanType' : 'consumerLoanType']
            "
            placeholder="请选择"
          >
            <el-option label="正向-正数" :value="1"></el-option>
            <el-option label="反向-负数" :value="2"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="科目选择："></el-form-item>

        <div class="checkbox-content">
          <div class="checkbox-content-item">
            <div class="checkbox-content-label">一级科目：</div>
            <div class="checkbox-content-value">
              <div
                v-for="i in subjectList[activeName]"
                :key="i.id"
                :class="{
                  'checkbox-content-tab': true,
                  active: i.id == currentId,
                }"
                @click="handelTab(i, '1')"
              >
                {{ i.name }}
                <div v-if="i.selectNum > 0" class="tag">
                  {{ i.selectNum }}
                </div>
              </div>
            </div>
            <el-button type="primary" @click="handelEdit">编辑</el-button>
          </div>

          <div
            v-for="item in currentId ? groupObject[activeName][currentId] : []"
            :key="item.id"
            class="checkbox-content-item"
          >
            <div class="checkbox-content-label">
              <el-checkbox
                v-model="item.checkAll"
                :indeterminate="item.indeterminate"
                @change="val => handleCheckAllChange(val, item.id)"
              >
                全选
              </el-checkbox>
              <div>{{ item.name }}：</div>
            </div>
            <div class="checkbox-box">
              <el-checkbox-group
                v-model="item.checkGroup"
                @change="
                  val => handleCheckedCitiesChange(item.child, item.id, val)
                "
              >
                <el-checkbox
                  v-for="item1 in item.child"
                  :key="item1.id"
                  :label="item1.id"
                >
                  {{ item1.name }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
      </el-form>

      <el-dialog
        width="400px"
        :visible.sync="showDialog"
        @closed="currentSubjectId = []"
      >
        <div class="infinite-list">
          <div
            v-for="i in FIRST_SUBJECT"
            :key="i.id"
            :class="{ active: currentSubjectId.includes(i.id) }"
            @click="handelTab(i, '2')"
          >
            {{ i.name }}
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="showDialog = false">取 消</el-button>
          <el-button type="primary" @click="onOK">确 定</el-button>
        </div>
      </el-dialog>

      <el-dialog
        width="400px"
        :visible.sync="showContractTemplate"
        @closed="currentSubjectId = []"
      >
        <el-form style="text-align: center">
          <el-radio-group
            v-model="contractTemplateType"
            style="margin-bottom: 10px"
          >
            <el-radio-button label="0">选择合同模版</el-radio-button>
            <el-radio-button label="1">上传合同模版</el-radio-button>
          </el-radio-group>
          <el-select
            v-if="contractTemplateType === '0'"
            v-model="form.tenantId"
            placeholder="请选择"
            style="width: 240px"
          >
            <el-option
              v-for="item in statusList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
          <div v-if="contractTemplateType === '1'" class="upload-temp">
            <uploadImg
              ref="uploadImg"
              btn-text="选择文件"
              list-type="text"
              :max="1"
              @onRemove="onRemove"
              @changeImage="changeImage"
            />
            <a style="margin-left: 10px">技术支持合同模版.pdf</a>
          </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="showContractTemplate = false">取 消</el-button>
          <el-button type="primary" @click="showContractTemplate = false">
            确 定
          </el-button>
        </div>
      </el-dialog>
    </div>
    <div v-else class="blank">暂无数据</div>
  </div>
</template>

<script>
  import {
    getDataDictAll,
    saveSettlementRule,
    serviceModelGet,
    serviceModelTurnNo,
  } from '@/api/subjectMgt';
  import uploadImg from '@/components/uploadImg';
  export default {
    components: {
      uploadImg,
    },
    data() {
      return {
        contractTemplateType: '0',
        contractTemplate: '',
        showDialog: false,
        showContractTemplate: false,
        currentId: '', // 当前选中的一级科目
        activeName: '0',
        statusDesc: '',
        currentSubjectId: [], // 选择的一级科目数量
        SERVICE_TRADE_TYPE_2: [],
        SERVICE_TRADE_TYPE_3: [],
        DEPARTMENT: [],
        FIRST_SUBJECT: [],
        SECOND_SUBJECT: [], // 二级科目
        providerSubjects: [],
        consumerSubjects: [],
        SUB_SUBJECT: [], // 三级科目
        allSelectList: {},
        subjectList: {}, // 科目确认的数据
        groupObject: {}, // 科目下的二级 三级科目
        copyGroupObject: {},
        selectList: {}, // 科目选择的数据
        statusList: [],
        searchParams: {},
        initFileList: [], // 默认文件
        form: {
          consumerBorrowType: '',
          consumerLoanType: '',
          consumerSubjects: '',
          contractUrl: '',
          costCenter: [],
          modelId: '',
          providerBorrowType: '',
          providerLoanType: '',
          providerSubjects: '',
        },
      };
    },
    created() {
      getDataDictAll({}).then(res => {
        if (res) {
          this.allSelectList = res;
          this.SERVICE_TRADE_TYPE_2 = this.getTypeList(
            res.SERVICE_TRADE_TYPE_2_ALL,
          );
          this.DEPARTMENT = this.getTypeList(res.DEPARTMENT);
          this.FIRST_SUBJECT = this.getTypeList(res.FIRST_SUBJECT);
        }
      });
    },
    methods: {
      handelTabClick() {
        this.getCheckNum();
        this.initSelect();
      },
      // 添加一级科目
      handelEdit() {
        this.showDialog = true;
        const list = this.subjectList[this.activeName] || [];

        this.currentSubjectId = list.map(item => item.id);
      },
      onSearch() {
        this.$refs.form.validate(valid => {
          if (!valid) return;
          this.serviceModelGet({});
        });
      },
      async serviceModelGet() {
        const res = await serviceModelGet(this.searchParams);
        if (res) {
          this.statusDesc = res.statusDesc;
          // 默认文件
          this.initFileList = res.contractUrl
            ? [
                {
                  url: res.contractUrl,
                  name: '文件',
                },
              ]
            : [];
          const consumer = res.consumer;
          const provider = res.provider;
          this.form = {
            modelId: res.modelId,
            contractUrl: res.contractUrl,
            consumerBorrowType: consumer ? consumer.borrowType : '',
            consumerLoanType: consumer ? consumer.loanType : '',
            providerBorrowType: provider ? provider.borrowType : '',
            providerLoanType: provider ? provider.loanType : '',
            costCenter: provider.costCenterInt,
          };
          this.getDetailsSubjects(provider, consumer);
        }
      },
      getSubjectList(arr) {
        return this.FIRST_SUBJECT.map(item => {
          if (Object.keys(arr).includes(String(item.id))) {
            return item;
          }
        }).filter(i => !!i);
      },
      getDetailsSubjects(provider, consumer) {
        const providerSubjects = provider.subjectMap;
        const consumerSubjects = consumer.subjectMap;
        const arr = this.getSubjectList(providerSubjects); // 服务提供方科目
        const arr1 = this.getSubjectList(consumerSubjects); // 服务接收方科目

        this.selectList = {
          0: [...arr],
          1: [...arr1],
        };

        // 科目默认选择第一个
        this.currentId = arr.length ? arr[0].id : '';

        // 科目选择数据
        this.subjectList = {
          0: this.getGroupList(arr),
          1: this.getGroupList(arr1),
        };

        // 科目下面 二级 三级数据
        this.groupObject = {
          0: this.getTreeObj(this.getGroupList(arr)),
          1: this.getTreeObj(this.getGroupList(arr1)),
        };

        // copy一份
        this.copyGroupObject = JSON.parse(JSON.stringify(this.groupObject));

        // console.log(this.groupObject[0], providerSubjects, '**********')
        // Object.keys(this.groupObject[0]).forEach(item => {
        //   console.log(item, '**********')
        //   Object.keys(providerSubjects).forEach(item1 => {
        //     console.log(item1, '222222222222')
        //     if (item1 == item) {
        //       Object.keys(providerSubjects[item1]).forEach(item2 => {
        //         console.log(item2, '3333333333333')
        //         const checkGroup = providerSubjects[item1][item2].split(',')
        //         const len = this.getLevel3Subject(item2).length
        //         const checkedCount = checkGroup.length
        //         // console.log(item1, item2, 'checkGroupcheckGroupcheckGroup')
        //         this.groupObject[0][item1] = this.groupObject[0][item1].map(
        //           item3 => {
        //             console.log(item3, '444444444444')
        //             return {
        //               ...item3,
        //               checkGroup:
        //                 item3.id == item2
        //                   ? checkGroup.map(i => (Number(i) ? Number(i) : i))
        //                   : [],
        //               checkAll: item3.id == item2 && checkedCount === len,
        //               indeterminate: checkedCount > 0 && checkedCount < len,
        //             }
        //           },
        //         )
        //       })
        //     }
        //   })
        // })

        Object.keys(providerSubjects).forEach(item1 => {
          Object.keys(providerSubjects[item1]).forEach(item2 => {
            this.groupObject[0][item1] = this.groupObject[0][item1].map(
              item3 => {
                const checkGroup = providerSubjects[item1][item2].split(',');
                const len = this.getLevel3Subject(item2).length;
                const checkedCount = checkGroup.length;
                return {
                  ...item3,
                  checkGroup:
                    item3.id == item2
                      ? checkGroup.map(i => (Number(i) ? Number(i) : i))
                      : item3.checkGroup,
                  checkAll:
                    item3.id == item2 ? checkedCount === len : item3.checkAll,
                  indeterminate:
                    item3.id == item2
                      ? checkedCount > 0 && checkedCount < len
                      : item3.indeterminate,
                };
              },
            );
          });
        });

        Object.keys(consumerSubjects).forEach(item1 => {
          Object.keys(consumerSubjects[item1]).forEach(item2 => {
            this.groupObject[1][item1] = this.groupObject[1][item1].map(
              item3 => {
                const checkGroup = consumerSubjects[item1][item2].split(',');
                const len = this.getLevel3Subject(item2).length;
                const checkedCount = checkGroup.length;
                return {
                  ...item3,
                  checkGroup:
                    item2 == item3.id
                      ? checkGroup.map(i => (Number(i) ? Number(i) : i))
                      : item3.checkGroup,
                  checkAll:
                    item2 == item3.id ? checkedCount === len : item3.checkAll,
                  indeterminate:
                    item3.id == item2
                      ? checkedCount > 0 && checkedCount < len
                      : item3.indeterminate,
                };
              },
            );
          });
        });

        // Object.keys(this.groupObject[1]).forEach(item => {
        //   Object.keys(consumerSubjects).forEach(item1 => {
        //     if (item == item1) {
        //       Object.keys(consumerSubjects[item1]).forEach(item2 => {
        //         const checkGroup = consumerSubjects[item1][item2].split(',')
        //         const len = this.getLevel3Subject(item2).length
        //         const checkedCount = checkGroup.length
        //         this.groupObject[1][item1] = this.groupObject[1][item1].map(
        //           item3 => {
        //             return {
        //               ...item3,
        //               checkGroup:
        //                 item3.id == item2
        //                   ? checkGroup.map(i => (Number(i) ? Number(i) : i))
        //                   : [],
        //               checkAll: item3.id == item2 && checkedCount === len,
        //               indeterminate: checkedCount > 0 && checkedCount < len,
        //             }
        //           },
        //         )
        //       })
        //     }
        //   })
        // })

        this.getCheckNum();
      },
      handleSelect2(val) {
        this.SERVICE_TRADE_TYPE_3 = this.getTypeList(
          this.allSelectList.SERVICE_TRADE_TYPE_3_ALL[val],
        );
      },
      getTypeList(obj, ischeck) {
        if (!obj) return [];
        return Object.keys(obj).map(item => {
          let itemObj = {
            id: Number(item) ? Number(item) : item,
            name: obj[item],
          };
          if (ischeck) {
            itemObj.indeterminate = false;
            itemObj.checkAll = false;
            itemObj.checkGroup = [];
          }
          return itemObj;
        });
      },
      handelTab(value, key) {
        const { activeName } = this;
        if (key === '1') {
          this.currentId = value.id;
        } else {
          const index = this.currentSubjectId.indexOf(value.id);
          if (index !== -1) {
            this.currentSubjectId.splice(index, 1);
            this.selectList[activeName].splice(index, 1);
          } else {
            this.currentSubjectId.push(value.id);
            this.selectList[activeName].push(value);
          }
        }
      },
      onOK() {
        this.showDialog = false;
        const { activeName } = this;

        const list = this.getGroupList(this.selectList[activeName]);

        // 重新选择科目的时候，保留之前选择的
        const oldObj = this.groupObject[activeName];
        const newObj = this.getTreeObj(list);
        const ids = Object.keys(oldObj).map(item => item);
        const selectObj = {};
        Object.keys(newObj).forEach(item => {
          if (ids.includes(item)) {
            selectObj[item] = oldObj[item];
          } else {
            selectObj[item] = newObj[item];
          }
        });

        this.subjectList[activeName] = list;
        // this.groupObject[activeName] = this.getTreeObj(list)
        this.groupObject[activeName] = selectObj;

        this.copyGroupObject = JSON.parse(JSON.stringify(this.groupObject));

        // 默认选中
        this.initSelect();
        // 显示选中的个数
        this.getCheckNum();
      },
      initSelect() {
        const { subjectList, activeName, currentId } = this;
        const subjectIDs = subjectList[activeName].length
          ? subjectList[activeName].map(item => item.id)
          : [];

        if (!subjectIDs.includes(currentId)) {
          this.currentId = subjectIDs[0];
        }
      },

      getTreeObj(arr) {
        const obj = {};
        arr.forEach(item => {
          obj[item.id] = item.child;
        });
        return obj;
      },
      getGroupList(arr) {
        return arr.map(item => {
          return {
            ...item,
            child: this.getGroupList2(item.id),
          };
        });
      },
      getGroupList2(id) {
        const arr = this.getTypeList(
          this.allSelectList.SECOND_SUBJECT[id],
          true,
          this.currentId,
        );
        return arr.map(item => {
          return {
            ...item,
            child: this.getGroupList3(item.id),
          };
        });
      },
      getGroupList3(id) {
        return this.getTypeList(
          this.allSelectList.SUB_SUBJECT[id],
          true,
          this.currentId,
        );
      },
      getCheckNum() {
        const { groupObject, subjectList, activeName } = this;
        Object.keys(subjectList[activeName]).forEach(item => {
          Object.keys(groupObject[activeName]).forEach(item1 => {
            if (this.subjectList[activeName][item].id == item1) {
              const checkData = groupObject[activeName][item1] || [];
              const num = checkData.reduce((prev, cur) => {
                return prev + cur.checkGroup.length;
              }, 0);
              this.subjectList[activeName][item]['selectNum'] = num;
            }
          });
        });
      },
      handleCheckAllChange(val, id) {
        const { groupObject, activeName, currentId, copyGroupObject } = this;

        const list = groupObject[activeName][currentId];
        const checkList = val ? this.checkAllGroupIds(id) : [];

        const arr = list.map(item => {
          if (item.id == id) {
            return {
              ...item,
              indeterminate: false,
              checkGroup: checkList,
            };
          } else {
            return item;
          }
        });
        this.groupObject[activeName][currentId] = arr;
        this.getCheckNum();
      },
      handleCheckedCitiesChange(val, id, data) {
        const { groupObject, activeName, currentId, copyGroupObject } = this;
        const list = groupObject[activeName][currentId];
        let checkedCount = data.length;
        const arr = list.map(item => {
          if (id == item.id) {
            const len = this.getLevel3Subject(item.id).length;
            return {
              ...item,
              checkAll: checkedCount === len,
              indeterminate: checkedCount > 0 && checkedCount < len,
            };
          }
          return item;
        });
        this.groupObject[activeName][currentId] = arr;
        // if (activeName === '0') {
        //   this.groupObject['0'][currentId] = arr
        //   this.groupObject[1][currentId] = copyGroupObject[1][currentId]
        // } else {
        //   this.groupObject['1'][currentId] = arr
        //   this.groupObject['0'][currentId] = copyGroupObject[0][currentId]
        // }

        this.getCheckNum();
      },

      // 获取三级科目
      getLevel3Subject(id) {
        return this.getTypeList(this.allSelectList.SUB_SUBJECT[id]);
      },
      // 全选ids
      checkAllGroupIds(id) {
        const arr = this.getTypeList(this.allSelectList.SUB_SUBJECT[id]);
        return arr.map(item => item.id);
      },
      // 科目提交参数
      getSubjectIds(obj) {
        let formObj = {};
        Object.keys(obj).map(item => {
          formObj[item] = '';
          let selectIds = [];
          obj[item].map(item1 => {
            if (item1.checkGroup && item1.checkGroup.length) {
              item1.checkGroup.map(item2 => {
                selectIds.push(item2);
              });
              formObj[item] = selectIds.join(',');
            }
          });
        });

        return JSON.stringify(formObj);
      },
      async onSave() {
        if (!this.form.modelId) {
          return this.$message.error('请选择服务交易类型');
        }
        const { form } = this;
        const params = {
          ...form,
          costCenter: form.costCenter.join(','),
          consumerSubjects: this.getSubjectIds(this.groupObject['1']),
          providerSubjects: this.getSubjectIds(this.groupObject['0']),
        };
        await saveSettlementRule(params);
        this.$message.success('保存成功');
        this.serviceModelGet();
      },
      async takeEffect() {
        if (!this.form.modelId) {
          return this.$message.error('请选择服务交易类型');
        }
        await serviceModelTurnNo({
          modelId: this.form.modelId,
        });
        this.$message.success('生效成功');
        this.serviceModelGet();
      },

      changeImage(files, fileList) {
        if (files) {
          this.form.contractUrl = files.file_url;
        }
      },
      onRemove() {
        this.form.contractUrl = '';
      },
    },
  };
</script>
<style lang="scss" scoped>
  .serviceTransactionsType {
    .blank {
      text-align: center;
      margin-top: 200px;
    }
    .upload-temp {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .infinite-list {
      max-height: 400px;
      overflow: auto;
      .active {
        background: #1890ff;
        color: #fff;
      }
      div {
        padding: 5px;
        margin-bottom: 5px;
      }
    }
    .form {
      display: flex;
      align-items: center;
      padding: 10px 10px 0;
      border: 1px solid #dcdfe6;
    }
    .form-top {
      display: flex;
      .el-form-item {
        flex: 1;
      }
    }
    .checkbox-content {
      border: 1px solid #dcdfe6;
      max-height: 600px;
      overflow-y: scroll;
      .checkbox-content-item {
        display: flex;
      }
      .checkbox-content-value {
        flex: 1;
        display: flex;
        align-items: center;
        margin: 0 10px;
        .checkbox-content-tab {
          position: relative;
        }
        .tag {
          // padding: 5px;
          color: #fff;
          background: red;
          border-radius: 50%;
          position: absolute;
          top: -15px;
          right: -24px;
        }
        .active {
          background: #1890ff;
          color: #fff;
        }
        div {
          padding: 6px 10px;
          margin: 0 10px;
        }
      }
      .checkbox-content-label {
        width: 120px;
        padding: 24px 10px 24px 0;
        border-bottom: 1px solid #fff;
        text-align: right;
        background: #dcdfe6;
      }
      .checkbox-box {
        flex: 1;
        padding: 10px;
        border-top: 1px solid #dcdfe6;
        // max-height: 160px;
        // overflow-y: scroll;
        // border-bottom: 1px solid #dcdfe6;
      }
    }
  }
</style>
