<template>
  <div>
    <el-form inline>
      <el-form-item label="主体名称:">
        <el-select
          v-model="searchParams.companyCode"
          filterable
          placeholder="请选择主体名称"
        >
          <el-option
            v-for="item in COMPANY_LIST"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态:">
        <el-select v-model="searchParams.status" placeholder="请选择状态">
          <el-option
            v-for="item in COMPANY_STATUS"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="链路状态:">
        <el-select
          v-model="searchParams.linkStatus"
          placeholder="请选择链路状态"
        >
          <el-option
            v-for="item in LINK_STATUS"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          :disabled="scope.row.statusStr !== '可执行'"
          type="text"
          size="small"
          btn-text="编辑定位"
          permission-key="locationInformationMgt-edit"
          @click="handleEdit(scope.row)"
        ></ac-permission-button>
        <ac-permission-button
          slot="reference"
          :disabled="scope.row.statusStr !== '可执行'"
          type="text"
          size="small"
          btn-text="执行"
          permission-key="locationInformationMgt-carried-out"
          @click="handleCarriedOut(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>

    <el-dialog
      width="400px"
      title="编辑定位"
      :visible.sync="visible"
      @closed="onClose"
    >
      <el-form ref="formData" :model="saveform">
        <el-form-item label="是否为关联方:">
          <el-switch v-model="saveform.relatedPartFlag"></el-switch>
        </el-form-item>
        <el-form-item label="是否有定价权:">
          <el-switch v-model="saveform.pricePowerFlag"></el-switch>
        </el-form-item>
        <el-form-item label="公司定位:">
          <el-select
            v-model="saveform.companyPosition"
            placeholder="请选择公司定位"
          >
            <el-option
              v-for="item in COMPANY_POSITION"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="地域范围:">
          <el-select v-model="saveform.areaScope" placeholder="请选择地域范围">
            <el-option
              v-for="item in AREA_SCOPE_STATUS"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
        <el-button type="primary" @click="handleOk">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    getListCompanyPosition,
    editCompanyPosition,
    statusOperation,
    getDataDictAll,
  } from '@/api/subjectMgt';
  const linkStatusText = ['', '购销', '服务', '购销、服务'];
  export default {
    components: {
      dynamictable,
    },
    data() {
      return {
        visible: false,
        searchParams: {
          companyCode: '',
          linkStatus: '',
          status: '',
        },
        saveform: {
          areaScope: '',
          companyPosition: '',
          id: '',
          pricePowerFlag: true,
          relatedPartFlag: true,
        },
        COMPANY_POSITION: [],
        AREA_SCOPE_STATUS: [],
        LINK_STATUS: [],
        COMPANY_LIST: [],
        COMPANY_STATUS: [],
        list: [],
        columns: [
          {
            prop: 'companyCode',
            label: '公司编码',
          },
          {
            prop: 'companyName',
            label: '公司名称',
          },
          {
            prop: 'areaScopeDesc',
            label: '地区',
          },
          {
            prop: 'companyPositionStr',
            label: '公司定位',
          },
          {
            prop: 'relatedPartFlagDesc',
            label: '是否为关联方',
          },
          {
            prop: 'pricePowerFlagDesc',
            label: '是否有定价权',
          },
          {
            prop: 'linkStatus',
            label: '链路状态',
            render: val => (
              <a>
                {val.linkStatus === 3 ? (
                  <div>
                    <a onClick={() => this.jump(val, 'purchaseTransactions')}>
                      购销
                    </a>
                    <a onClick={() => this.jump(val, 'serviceTransaction')}>
                      服务
                    </a>
                  </div>
                ) : (
                  <a
                    onClick={() =>
                      this.jump(
                        val,
                        val.linkStatus === 1
                          ? 'purchaseTransactions'
                          : 'serviceTransaction',
                      )
                    }
                  >
                    {linkStatusText[val.linkStatus]}
                  </a>
                )}
              </a>
            ),
          },
          {
            prop: 'statusStr',
            label: '状态',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '200',
            scopedSlots: { customRender: 'operation' },
          },
        ],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
      };
    },
    created() {
      getDataDictAll({}).then(res => {
        if (res) {
          this.COMPANY_LIST = this.getTypeList(res.COMPANY_MAIN);
          this.COMPANY_STATUS = this.getTypeList(res.COMPANY_STATUS);
          this.AREA_SCOPE_STATUS = this.getTypeList(res.AREA_SCOPE);
          this.LINK_STATUS = this.getTypeList(res.COMPANY_LINK_STATUS);
          this.COMPANY_POSITION = this.getTypeList(res.COMPANY_POSITION);
        }
      });
      this.getList(true);
    },
    methods: {
      jump(val, router) {
        if (val.linkStatus === 0) {
          return;
        }
        this.$router.push({
          name: router,
          query: {
            [router !== 'serviceTransaction'
              ? 'companyCode'
              : 'providerCompanyCode']: val.companyCode,
          },
        });
      },
      getTypeList(obj) {
        if (!obj) return [];
        return Object.keys(obj).map(item => {
          return {
            id: Number(item) ? Number(item) : item,
            name: obj[item],
          };
        });
      },
      onClose() {
        Object.assign(this.$data.saveform, this.$options.data().saveform);
        this.$nextTick(function () {
          this.$refs.formData.clearValidate();
        });
      },
      handleEdit(val) {
        this.visible = true;
        this.saveform = {
          areaScope: this.getAreaScope(val.areaScopeDesc),
          companyPosition: val.companyPosition,
          id: val.id,
          pricePowerFlag: val.pricePowerFlag === 1 ? true : false,
          relatedPartFlag: val.relatedPartFlag === 1 ? true : false,
        };
      },
      getAreaScope(val) {
        let areaScope = '';
        if (Array.isArray(this.AREA_SCOPE_STATUS)) {
          this.AREA_SCOPE_STATUS.map(item => {
            if (item.name === val) {
              areaScope = item.id;
            }
          });
        }
        return areaScope;
      },
      async handleOk() {
        const { pricePowerFlag, relatedPartFlag, ...params } = this.saveform;

        await editCompanyPosition({
          ...params,

          pricePowerFlag: pricePowerFlag ? 1 : 0,
          relatedPartFlag: relatedPartFlag ? 1 : 0,
        });
        this.getList();
        this.visible = false;
        this.$message.success('编辑成功');
      },
      async handleCarriedOut({ id }) {
        await statusOperation({ id, status: 2 });
        this.$message.success('执行成功');
        this.getList();
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = {
          ...this.searchParams,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        this.options.loading = true;
        const res = await getListCompanyPosition(params);
        this.options.loading = false;
        this.list = res ? res.records : [];
        this.pagination.total = res ? res.total : 0;
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.getList(true);
      },
    },
  };
</script>
<style lang="scss" scoped></style>
