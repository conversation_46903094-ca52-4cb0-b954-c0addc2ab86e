<template>
  <div class="serviceTransactionsMgt">
    <el-row>
      <el-col :span="8" class="left">
        <div class="left-top">
          <ac-permission-button
            type="primary"
            btn-text="新建服务提供方"
            permission-key="serviceTransactionsMgt-add-service-provider"
            @click="showDialog = true"
          ></ac-permission-button>
          <span>服务发送方</span>
          <el-button type="text" @click="jump({}, 'serviceTransactionsType')">
            服务交易类型管理
          </el-button>
        </div>
        <div class="tree-content">
          <el-tree
            ref="tree"
            :data="data"
            :default-expand-all="true"
            :current-node-key="checkedTreeKeys"
            :highlight-current="true"
            node-key="id"
            :props="defaultProps"
            @node-click="handleNodeClick"
          ></el-tree>
        </div>
      </el-col>
      <el-col :span="16">
        <div class="right-form">
          <el-form
            ref="saveForm"
            :inline="true"
            :model="billingPlanForm"
            class="demo-form-inline"
          >
            <el-form-item label="结算周期">
              <el-select
                v-model="billingPlanForm.period"
                placeholder="请选择结算周期"
              >
                <el-option label="月度" :value="1"></el-option>
                <!-- <el-option label="季度" :value="2"></el-option> -->
              </el-select>
            </el-form-item>
            <el-form-item label="结算日">
              <el-select
                v-model="billingPlanForm.settlementDay"
                placeholder="请选择结算日"
              >
                <el-option
                  v-for="item of 28"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="结算币种">
              <el-select
                v-model="billingPlanForm.currency"
                placeholder="请选择结算币种"
              >
                <el-option
                  v-for="item of CURRENCY"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="自动结算">
              <el-radio-group v-model="billingPlanForm.automatic">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="结算单价"
              prop="unitPrice"
              :rules="[
                {
                  required: false,
                  message: '请输入结算单价',
                  trigger: 'change',
                },
              ]"
            >
              <el-input
                v-model="billingPlanForm.unitPrice"
                placeholder="请输入结算单价"
              />
            </el-form-item>

            <el-form-item class="table-from" label="定价方案">
              <el-table :data="pricingPlanList" style="width: 100%">
                <el-table-column
                  prop="rateOfProfitDesc"
                  label="定价方法"
                  width="150"
                ></el-table-column>
                <el-table-column
                  prop="recommendation"
                  label="建议四分位区间"
                  width="200"
                >
                  <template slot-scope="{ row }">
                    {{
                      row.rateOfProfitDesc
                        ? `${row.analysisLowerQuartile}% ~ ${row.analysisMedian}% ~ ${row.analysisUpperQuartile}%`
                        : ''
                    }}
                  </template>
                </el-table-column>
                <el-table-column prop="addition" label="加成" width="200">
                  <template slot-scope="{ row }">
                    <el-form-item required :error="errorTip" label="">
                      <el-input v-model="row.addition" @input="handleInput">
                        <template slot="append">%</template>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80">
                  <template>
                    <ac-permission-button
                      size="mini"
                      type="text"
                      btn-text="选择"
                      permission-key="serviceTransactionsMgt-choose-pricing-plan"
                      @click="showPricingPlan = true"
                    ></ac-permission-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>

            <el-form-item style="width: 100%; text-align: center">
              <ac-permission-button
                type="primary"
                btn-text="编辑"
                permission-key="serviceTransactionsMgt-edit"
                @click="onSubmit"
              ></ac-permission-button>
              <el-button type="primary" @click="onRest">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="left-top ml10 mt10">
          <span>
            <ac-permission-button
              style="margin-right: 10px"
              type="primary"
              btn-text="新建服务接收方"
              permission-key="serviceTransactionsMgt-add-service-recipient"
              @click="
                showServiceRecipient = true;
                currentRecord = null;
              "
            ></ac-permission-button>
            <ac-permission-button
              slot="reference"
              type="primary"
              btn-text="发起结算"
              permission-key="serviceTransactionsMgt-initiate-protest"
              @click="handleSend"
            ></ac-permission-button>
          </span>
          <span>服务接收方</span>
          <span>
            <el-button type="text" @click="jump({}, 'serviceTransaction')">
              服务交易关系明细
            </el-button>
            <el-button type="text" @click="jump({}, 'serviceTransactionLog')">
              服务交易日志
            </el-button>
          </span>
        </div>
        <div class="ml10 mt10">
          <dynamictable
            :data-source="list"
            :columns="columns"
            :options="options"
          >
            <template slot="operation" slot-scope="scope">
              <el-button
                v-if="[21, 2, 23].includes(scope.row.status)"
                slot="reference"
                type="text"
                size="small"
                @click="
                  showServiceRecipient = true;
                  currentRecord = scope.row;
                "
              >
                编辑
              </el-button>
              <el-popconfirm
                v-if="[2].includes(scope.row.status)"
                title="确认废弃此条交易吗？"
                @confirm="handleConfirm(scope.row, '0')"
              >
                <el-button
                  v-if="[2].includes(scope.row.status)"
                  slot="reference"
                  type="text"
                  size="small"
                >
                  废弃
                </el-button>
              </el-popconfirm>
              <el-popconfirm
                v-if="[2].includes(scope.row.status)"
                title="确认要提交法务审核吗？"
                @confirm="handleConfirm(scope.row, '1')"
              >
                <el-button
                  v-if="[2].includes(scope.row.status)"
                  slot="reference"
                  type="text"
                  size="small"
                >
                  提交
                </el-button>
              </el-popconfirm>
              <el-popconfirm
                v-if="[3].includes(scope.row.status)"
                title="确认要生成合同吗？系统将根据交易类型提供的模版自动创建合同。"
                @confirm="handleConfirm(scope.row, '2')"
              >
                <el-button slot="reference" type="text" size="small">
                  创建合同
                </el-button>
              </el-popconfirm>
              <el-popconfirm
                v-if="[4, 6].includes(scope.row.status)"
                title="确认要启用此条交易吗？"
                @confirm="handleConfirm(scope.row, '3')"
              >
                <el-button slot="reference" type="text" size="small">
                  启用
                </el-button>
              </el-popconfirm>
            </template>
          </dynamictable>
        </div>
      </el-col>
    </el-row>
    <addServiceSender
      v-model="showDialog"
      :company-list="COMPANY_LIST"
      :all-select-obj="allSelectObj"
      @onGet="getTreeData"
    />
    <pricingPlandiaLog
      v-model="showPricingPlan"
      :record="transactionTypeInfo"
      :pricing-plan-select="pricingPlanSelect"
      @onSelect="onSelect"
    />
    <serviceRecipient
      v-model="showServiceRecipient"
      :record="currentRecord"
      :provider-id="providerId"
      :list="COMPANY_LIST"
      @onGet="getServiceConsumerList"
    />
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import addServiceSender from './components/addServiceSender';
  import pricingPlandiaLog from './components/pricingPlandiaLog';
  import serviceRecipient from './components/serviceRecipient';
  import {
    getDataDictAll,
    serviceProviderList,
    settlementManual,
    serviceConsumerDiscard,
    serviceConsumerEnable,
    serviceConsumerList,
    settlementRegulationEdit,
    selectPricingByCoverage,
    getSettlementRegulation,
    serviceConsumerAddContract,
    serviceConsumerSubmitOa,
    companyMainMap,
  } from '@/api/subjectMgt';
  export default {
    components: {
      dynamictable,
      addServiceSender,
      pricingPlandiaLog,
      serviceRecipient,
    },
    data() {
      const validateNum = (rule, value, callback) => {
        const reg = /^[0-9]*$/g;
        if (!reg.test(value) && value) {
          callback(new Error('只能输入数字'));
        } else {
          callback();
        }
      };
      return {
        rules: {
          validateNum,
        },
        defaultValueObj: {
          tradeType2: '',
          tradeType3: '',
          providerCompanyCode: '',
        },
        showDialog: false,
        showPricingPlan: false,
        showServiceRecipient: false,
        checkedTreeKeys: '',
        providerId: null,
        currentRecord: null,
        COMPANY_LIST: [],
        pricingPlanList: [{ key: 1 }],
        errorTip: '',
        CURRENCY: [],
        pricingPlanSelect: [],
        allSelectObj: null,
        billingPlanForm: {
          automatic: 1,
          addition: '',
          currency: '',
          period: 1,
          providerId: '',
          pricingModel: '',
          settlementDay: '',
          unitPrice: '',
        },
        transactionTypeInfo: {},
        pricingPlan: {}, // 默认定价方案
        columns: [
          {
            prop: 'consumerCompanyName',
            label: '服务接收方',
          },
          {
            prop: 'effectDate',
            label: '起效日期',
          },
          {
            prop: 'expireDate',
            label: '失效日期',
          },
          {
            prop: 'currentSettlementDate',
            label: '当前结算期间',
          },
          {
            prop: 'statusDesc',
            label: '状态',
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '100',
            scopedSlots: { customRender: 'operation' },
          },
        ],
        list: [],
        options: {
          loading: false,
          border: true,
        },
        data: [],
        defaultProps: {
          children: 'children',
          label: 'desc',
          value: 'code',
        },
      };
    },
    watch: {
      // pricingPlanList(val) {
      //   console.log(val, ';')
      // },
    },
    created() {
      // 下拉状态
      getDataDictAll({}).then(res => {
        if (res) {
          this.allSelectObj = res;
          // this.COMPANY_LIST = this.getCompanyList(res.COMPANY_MAIN)
          this.CURRENCY = this.getCompanyList(res.CURRENCY);
        }
      });
      // 树形列表
      this.getTreeData(() => {
        this.getSettlementRegulation(this.providerId, () => {
          this.getSelectPricingByCoverage(this.providerId);
        });
        this.getServiceConsumerList(this.providerId);
      });
      // 公司主体列表
      companyMainMap({}).then(res => {
        if (res) {
          this.COMPANY_LIST = this.getCompanyList(res);
        }
      });
    },
    methods: {
      getCompanyList(obj) {
        if (!obj) return [];
        return Object.keys(obj).map(item => {
          return {
            id: Number(item) ? Number(item) : item,
            name: obj[item],
          };
        });
      },
      // 结算方案详情
      async getSettlementRegulation(serviceProviderId, cb) {
        const res = await getSettlementRegulation({ serviceProviderId });
        if (res) {
          this.billingPlanForm = {
            ...res,
            automatic: res.automatic ? 1 : 0,
          };
        } else {
          this.billingPlanForm.automatic = 1;
          this.billingPlanForm.currency = 'CNY';
          this.billingPlanForm.period = 1;
          this.billingPlanForm.settlementDay = 20;
        }
        cb && cb();
      },
      async getServiceConsumerList(serviceProviderId) {
        serviceProviderId = this.providerId;
        const res = await serviceConsumerList({ serviceProviderId });
        if (res) {
          this.list = res;
        }
      },
      // 选择定价方案列表
      async getSelectPricingByCoverage(serviceProviderId) {
        const res = await selectPricingByCoverage({ serviceProviderId });
        if (res && res.length && res[0].id) {
          this.pricingPlanSelect = res;
        }
        this.getInitValue(res);
      },
      getInitValue(arr = []) {
        let pricingPlan = {};
        arr.forEach(item => {
          if (item.rateOfProfitDesc === '成本加成率') {
            pricingPlan = item;
          }
        });
        this.billingPlanForm.pricingModel = pricingPlan.rateOfProfit;
        // 添加默认方案
        this.pricingPlanList = [
          {
            ...pricingPlan,
            addition: this.billingPlanForm.addition
              ? this.billingPlanForm.addition
              : '5.82',
          },
        ];

        this.transactionTypeInfo = Object.keys(pricingPlan).length
          ? pricingPlan
          : arr[0];
      },
      async getTreeData(cb) {
        const res = await serviceProviderList({});
        if (res) {
          this.data = [res];
          const data = res.children;
          if (Array.isArray(data) && data.length) {
            this.defaultValueObj.tradeType2 = data[0].code;
            if (Array.isArray(data[0].children) && data[0].children.length) {
              this.defaultValueObj.tradeType3 = data[0].children[0].code;
            }
            this.getDefaultId([res.children[0]]);
          }

          this.$nextTick(() => {
            this.$refs.tree.setCurrentKey(this.checkedTreeKeys);
            this.providerId = this.checkedTreeKeys;
            cb && cb();
          });
        }
      },
      // 默认第一个
      getDefaultId(arr) {
        arr.forEach(item => {
          if (item.children && item.children.length > 0) {
            this.getDefaultId([item.children[0]]);
            return;
          }
          if (this.checkedTreeKeys) {
            return;
          } else {
            this.defaultValueObj.providerCompanyCode = item.code;
            this.checkedTreeKeys = item.id;
          }
        });
      },
      jump(val, router) {
        this.$router.push({
          name: router,
          query: { id: val.id, ...this.defaultValueObj },
        });
      },
      handleSend() {
        this.$confirm(
          '确认要手动发起结算吗？只有在当月结算日之后才能发起；如果当前有执行中的交易不能手动发起；只有当前月的结算为废弃和被OA拒绝后才能手动发起。',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          },
        ).then(async () => {
          const { providerId } = this;
          await settlementManual({ providerId });
          this.$message({
            type: 'success',
            message: '发起成功!',
          });
        });
      },
      onSelect(val) {
        this.billingPlanForm.pricingModel = val.rateOfProfit;
        this.pricingPlanList = [val];
        this.showPricingPlan = false;
      },
      async handleConfirm(val, key) {
        const apiObj = {
          0: serviceConsumerDiscard,
          1: serviceConsumerSubmitOa,
          2: serviceConsumerAddContract,
          3: serviceConsumerEnable,
        };
        await apiObj[key]({ consumerId: val.consumerId });
        this.$message({
          type: 'success',
          message: '操作成功',
        });
        this.getServiceConsumerList(this.providerId);
      },
      // 加成校验
      handleInput(val) {
        if (val) {
          if (Number(val)) {
            this.errorTip = '';
          } else {
            this.errorTip = '只能输入数字';
          }
        } else {
          this.errorTip = '请输入';
        }
      },
      async onSubmit() {
        this.$refs.saveForm.validate(async valid => {
          const addition = this.pricingPlanList[0].addition;
          if (this.errorTip) {
            return;
          }
          if (!addition) {
            this.errorTip = '请输入';
            return;
          }
          if (!valid) return;
          const params = {
            ...this.billingPlanForm,
            providerId: this.providerId,
            addition,
          };
          await settlementRegulationEdit(params);
          this.$message({
            type: 'success',
            message: '编辑成功!',
          });
          this.getSettlementRegulation(this.providerId);
        });
      },
      onRest() {
        Object.assign(
          this.$data.billingPlanForm,
          this.$options.data().billingPlanForm,
        );
        this.$nextTick(function () {
          this.$refs.saveForm.clearValidate();
        });
      },
      // 切换树节点
      handleNodeClick(data, node) {
        const { parent } = node;
        this.providerId = data.id;
        this.defaultValueObj.tradeType2 = parent.parent.data.code;
        this.defaultValueObj.tradeType3 = parent.data.code;
        this.defaultValueObj.providerCompanyCode = data.code;
        if (Array.isArray(node.childNodes) && node.childNodes.length === 0) {
          this.pricingPlanSelect = []; // 每次切换清空定价方案列表
          Object.assign(
            this.$data.billingPlanForm,
            this.$options.data().billingPlanForm,
          );
          this.$nextTick(function () {
            this.$refs.saveForm.clearValidate();
          });
          this.getSettlementRegulation(data.id, () => {
            this.getSelectPricingByCoverage(data.id);
          });
          this.getServiceConsumerList(data.id);
        }
      },
    },
  };
</script>
<style lang="scss" scoped>
  .serviceTransactionsMgt {
    .left-top {
      padding: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border: 1px solid #dcdfe6;
    }
    .tree-content {
      border: 1px solid #dcdfe6;
      margin-top: 10px;
      height: 66vh;
      overflow: scroll;
    }
    .right-form {
      padding: 10px;
      border: 1px solid #dcdfe6;
      margin-left: 10px;
    }
    .table-from.el-form-item {
      display: block;
      width: 100%;
    }
    .ml10 {
      margin-left: 10px;
    }
    .mt10 {
      margin-top: 10px;
    }
  }
</style>
