<template>
  <div class="serviceTransaction">
    <serviceTransactionSearch
      router-type="serviceTransactionLog"
      @onSearch="onSearch"
      @handleOperating="handleOperating"
    ></serviceTransactionSearch>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
      @selection-change="handleSelectionChange"
    ></dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import serviceTransactionSearch from './components/serviceTransactionSearch';
  import {
    getQuerySettlementLog,
    settlementLogApprove,
    settlementLogInvoicing,
    settlementLogVerify,
  } from '@/api/subjectMgt';
  export default {
    components: {
      dynamictable,
      serviceTransactionSearch,
    },
    data() {
      return {
        search: {},
        list: [],
        settlementLogIds: [],
        columns: [
          {
            prop: 'settlementNo',
            label: '结算单号',
          },
          {
            prop: 'tradeType2Desc',
            label: '二级交易',
          },
          {
            prop: 'tradeType3Desc',
            label: '三级交易',
          },
          {
            prop: 'providerCompanyName',
            label: '服务发送方',
          },
          {
            prop: 'consumerCompanyName',
            label: '服务接收方',
          },
          {
            prop: 'settlementDate',
            label: '发起时间',
          },
          {
            prop: 'paymentDate',
            label: '付款日期',
          },
          {
            prop: 'entryDate',
            label: '入账日期',
          },
          {
            prop: 'currentPeriod',
            label: '结算期间',
          },
          {
            prop: 'currency',
            label: '币种',
          },
          {
            prop: 'amount',
            label: '结算金额',
          },
          {
            prop: 'taxAmount',
            label: '税费',
          },
          {
            prop: 'status',
            label: '状态',
          },
        ],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
          mutiSelect: true,
        },
      };
    },
    // created() {
    //   this.getList(true)
    // },
    methods: {
      onSearch(e) {
        this.search = e;
        this.getList(true);
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = {
          logic: 1,
          ...this.search,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        this.options.loading = true;
        const res = await getQuerySettlementLog(params);
        this.options.loading = false;
        this.list = res ? res.records : [];
        this.pagination.total = res ? res.total : 0;
      },
      handleSelectionChange(val) {
        let arr = [];
        val.forEach(item => {
          if (!arr.includes(item.settlementNo)) {
            arr.push(item.settlementNo);
          }
        });
        this.settlementLogIds = arr;
      },
      async handleOperating(val) {
        const { settlementLogIds } = this;
        if (settlementLogIds.length === 0) {
          return this.$message.error('请选择数据');
        }
        const apiList = [
          settlementLogVerify,
          settlementLogInvoicing,
          settlementLogApprove,
        ];
        let saveParams = {
          settlementNoList: settlementLogIds,
        };
        if (val === 0) {
          this.$confirm('复核', {
            confirmButtonText: '通过',
            cancelButtonText: '不通过',
            type: 'warning',
          })
            .then(async () => {
              saveParams.verifyRes = 1;
              await apiList[val](saveParams);
              this.$message.success('操作成功');
              this.getList();
            })
            .catch(async err => {
              if (err === 'cancel') {
                saveParams.verifyRes = 0;
                await apiList[val](saveParams);
                this.$message.success('操作成功');
                this.getList();
              }
            });
          return;
        }
        const res = await apiList[val](saveParams);
        this.$message.success('操作成功');
        this.getList();
      },
    },
  };
</script>
<style lang="scss"></style>
