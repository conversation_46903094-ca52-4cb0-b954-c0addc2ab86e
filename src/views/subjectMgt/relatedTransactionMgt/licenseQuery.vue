<template>
  <div>
    <el-form inline>
      <el-form-item label="主体名称:">
        <el-select
          v-model="searchParams.companyCode"
          filterable
          placeholder="请选择主体名称"
        >
          <el-option
            v-for="item in COMPANY_LIST"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="证照类型:">
        <el-select
          v-model="searchParams.licenseName"
          placeholder="请选择证照类型"
        >
          <el-option
            v-for="item in LICENSE"
            :key="item.id"
            :label="item.name"
            :value="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="primary" @click="getList(true)">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button @click="() => $router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>
    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
    >
      <template slot="operation" slot-scope="scope">
        <ac-permission-button
          slot="reference"
          type="text"
          size="small"
          btn-text="查看"
          permission-key="licenseQuery-see"
          @click="handleSee(scope.row)"
        ></ac-permission-button>
      </template>
    </dynamictable>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import {
    getDataDictAll,
    getlistCompanyLicense,
    getlistCompanyMain,
  } from '@/api/subjectMgt';
  import { LOCAL_STORAGE_KEY } from '@/consts/subjectMgt';
  export default {
    components: {
      dynamictable,
    },
    data() {
      return {
        searchParams: {
          companyCode: '',
          licenseName: '',
        },
        COMPANY_LIST: [],
        LICENSE: [],
        list: [],
        columns: [
          {
            prop: 'companyName',
            label: '主体名称',
          },
          {
            prop: 'licenseName',
            label: '证照名称',
          },
          {
            prop: 'competentAuthority',
            label: '主管机关',
          },
          {
            prop: 'issueDate',
            label: '颁发日期',
          },
          {
            prop: 'updateDate',
            label: '更新日期',
          },
          {
            prop: 'validityPeriod',
            label: '有效期',
          },
          {
            prop: 'idCode',
            label: '证件编码',
          },
          {
            prop: 'attachmentUrl',
            label: '附件',
            render: ({ attachmentUrl }) => {
              return attachmentUrl ? (
                <a
                  href={attachmentUrl}
                  rel="noopener noreferrer"
                  download=""
                  target="_blank"
                >
                  附件
                </a>
              ) : (
                <a>未上传</a>
              );
            },
          },
          {
            prop: 'operation',
            label: '操作',
            fixed: 'right',
            width: '200',
            scopedSlots: { customRender: 'operation' },
          },
        ],
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        options: {
          loading: false,
          border: true,
        },
      };
    },
    created() {
      getDataDictAll({}).then(res => {
        if (res) {
          this.COMPANY_LIST = this.getTypeList(res.COMPANY_MAIN);
          this.LICENSE = this.getTypeList(res.LICENSE);
        }
      });
      this.getList(true);
    },
    methods: {
      getTypeList(obj) {
        if (!obj) return [];
        return Object.keys(obj).map(item => {
          return {
            id: Number(item) ? Number(item) : item,
            name: obj[item],
          };
        });
      },
      async getList(isSearch) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = {
          ...this.searchParams,
          current: this.pagination.pageSize,
          size: this.pagination.pageLimit,
        };
        this.options.loading = true;
        try {
          const res = await getlistCompanyLicense(params);
          this.options.loading = false;
          this.list = res ? res.records : [];
          this.pagination.total = res ? res.total : 0;
        } catch (err) {
          this.options.loading = false;
        }
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.getList();
      },
      async handleSee(val) {
        const res = await getlistCompanyMain({
          companyCode: val.companyCode,
          current: 1,
          size: 10,
        });
        if (res && res.records) {
          localStorage.setItem(
            LOCAL_STORAGE_KEY.editSubjectInformation,
            JSON.stringify(res.records[0]),
          );
        }

        this.$router.push({
          name: 'editSubjectInformation',
          query: { id: val.id },
        });
      },
    },
  };
</script>
<style lang="scss" scoped></style>
