<template>
  <div class="cost-ratio-setting-container">
    <!-- 顶部卡片标题栏 -->
    <div class="page-header">
      <h2 class="page-title">
        <i class="el-icon-s-operation"></i>
        分账配置
      </h2>
      <div class="header-actions">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="openDialog('add')"
        >
          新增
        </el-button>
        <!-- <el-button icon="el-icon-upload" @click="handleImport">导入</el-button> -->
        <el-button icon="el-icon-download" @click="handleExport">
          导出
        </el-button>
        <el-button
          icon="el-icon-refresh"
          :loading="options.loading"
          @click="handleRefresh"
        >
          刷新
        </el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <el-card class="table-card" shadow="hover">
      <div
        style="
          display: flex;
          align-items: center;
          gap: 16px;
          margin-bottom: 8px;
        "
      >
        <div>
          <div style="display: flex; align-items: center">
            <span style="font-weight: 600; margin-right: 16px">
              分账配置列表
            </span>
            <el-tag
              :type="filterInvalidTab === 'all' ? 'primary' : 'info'"
              effect="plain"
              style="
                cursor: pointer;
                margin-right: 8px;
                font-size: 14px;
                padding: 6px 18px;
                border-radius: 16px;
                border: none;
                box-shadow: none;
              "
              :hit="filterInvalidTab === 'all'"
              @click="filterInvalidTab = 'all'"
            >
              <i
                v-if="filterInvalidTab === 'all'"
                class="el-icon-menu"
                style="margin-right: 4px"
              ></i>
              全部
              <span style="margin-left: 6px; color: #909399; font-size: 13px">
                {{ allList.length }}
              </span>
            </el-tag>
            <el-tag
              :type="filterInvalidTab === 'valid' ? 'success' : 'info'"
              effect="plain"
              style="
                cursor: pointer;
                margin-right: 8px;
                font-size: 14px;
                padding: 6px 18px;
                border-radius: 16px;
                border: none;
                box-shadow: none;
              "
              :hit="filterInvalidTab === 'valid'"
              @click="filterInvalidTab = 'valid'"
            >
              <i
                v-if="filterInvalidTab === 'valid'"
                class="el-icon-check"
                style="margin-right: 4px"
              ></i>
              生效
              <span style="margin-left: 6px; color: #67c23a; font-size: 13px">
                {{ allList.filter(item => item.invalid == 0).length }}
              </span>
            </el-tag>
            <el-tag
              :type="filterInvalidTab === 'invalid' ? 'danger' : 'info'"
              effect="plain"
              style="
                cursor: pointer;
                font-size: 14px;
                padding: 6px 18px;
                border-radius: 16px;
                border: none;
                box-shadow: none;
              "
              :hit="filterInvalidTab === 'invalid'"
              @click="filterInvalidTab = 'invalid'"
            >
              <i
                v-if="filterInvalidTab === 'invalid'"
                class="el-icon-close"
                style="margin-right: 4px"
              ></i>
              失效
              <span style="margin-left: 6px; color: #f56c6c; font-size: 13px">
                {{ allList.filter(item => item.invalid == 1).length }}
              </span>
            </el-tag>
          </div>
        </div>
        <el-input
          v-model="searchKeyword"
          placeholder="搜索贸易主体/费用类型/结算主体"
          prefix-icon="el-icon-search"
          clearable
          style="width: 250px; margin-left: auto"
        />
      </div>
      <el-table
        v-loading="options.loading"
        :data="filteredList"
        border
        stripe
        highlight-current-row
        style="width: 100%"
        :header-cell-style="{
          background: '#f5f7fa',
          color: '#606266',
          fontWeight: '600',
        }"
        :cell-style="{ padding: '12px 0' }"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column
          prop="outAccountName"
          label="贸易主体"
          min-width="140"
        />
        <el-table-column prop="feeName" label="费用类型" min-width="140" />
        <el-table-column label="费用比例" min-width="100">
          <template #default="{ row }">
            <span>{{ formatPercent(row.feeRate) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="是否失效" min-width="100">
          <template #default="{ row }">
            <span
              v-if="row.invalid == 0"
              style="color: #67c23a; font-weight: bold"
            >
              是
            </span>
            <span
              v-else-if="row.invalid == 1"
              style="color: #f56c6c; font-weight: bold"
            >
              否
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="inAccountName"
          label="费用结算主体"
          min-width="140"
        />

        <el-table-column prop="creator" label="申请人" min-width="100" />
        <el-table-column prop="createTime" label="生效时间" min-width="160" />
        <el-table-column
          prop="updateTime"
          label="最后更新时间"
          min-width="160"
        />
        <el-table-column label="操作" width="140">
          <template #default="{ row }">
            <el-button
              type="text"
              size="small"
              @click="openDialog('edit', row)"
            >
              编辑
            </el-button>
            <span
              v-if="row.invalid == 0"
              style="
                color: #f56c6c;
                cursor: pointer;
                font-size: 12px;
                font-weight: 500;
                padding: 0 8px;
              "
              @click="handleInvalidChange(row, 1)"
            >
              作废
            </span>
            <span
              v-else-if="row.invalid == 1"
              style="
                color: #67c23a;
                cursor: pointer;
                font-size: 12px;
                font-weight: 500;
                padding: 0 8px;
              "
              @click="handleInvalidChange(row, 0)"
            >
              启用
            </span>
          </template>
        </el-table-column>
      </el-table>
      <!-- 移除分页控件 -->
    </el-card>

    <!-- 新增/编辑弹窗（后续补充表单） -->
    <el-dialog
      :title="dialogMode === 'add' ? '新增分账配置' : '编辑分账配置'"
      :visible.sync="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="dialogForm"
        :model="dialogForm"
        :rules="dialogRules"
        label-width="110px"
        :disabled="dialogLoading"
      >
        <el-form-item label="贸易主体" prop="outAccountName">
          <el-select
            v-model="dialogForm.outAccountName"
            placeholder="请选择贸易主体"
            :disabled="dialogMode === 'edit'"
            filterable
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in subAccountNameSelectorVOList"
              :key="item.dictValue"
              :label="item.dictDesc"
              :value="item.dictDesc"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="费用类型" prop="feeName">
          <el-select
            v-model="dialogForm.feeType"
            placeholder="请选择费用类型"
            :disabled="dialogMode === 'edit'"
            filterable
            clearable
            style="width: 100%"
            @change="handleFeeTypeChange"
          >
            <el-option
              v-for="item in splitFeeTypeSelectorVOList"
              :key="item.dictValue"
              :label="item.dictDesc"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="费用比例" prop="feeRate">
          <el-input
            v-model="dialogForm.feeRate"
            placeholder="请输入费用比例"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="费用结算主体" prop="inAccountName">
          <el-select
            v-model="dialogForm.inAccountName"
            placeholder="请选择费用结算主体"
            :disabled="dialogMode === 'edit'"
            filterable
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in subAccountNameSelectorVOList"
              :key="item.dictValue + '_in'"
              :label="item.dictDesc"
              :value="item.dictDesc"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          :loading="dialogLoading"
          @click="handleDialogSubmit"
        >
          确 定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import {
    selector,
    splitConfigQuery,
    splitConfigAdd,
    splitConfigUpdate,
  } from '@/api/split';
  import { debounce } from '@/utils';
  import { Message } from 'element-ui';

  export default {
    name: 'CostRatioSetting',
    data() {
      return {
        searchParams: {
          accountNo: '',
        },
        subAccountNameSelectorVOList: [],
        splitFeeTypeSelectorVOList: [],
        list: [],
        allList: [], // 全量数据缓存
        allListLoading: false, // 下拉loading
        searchKeyword: '', // 搜索关键词
        subjectAccount: [],
        filterInvalidTab: 'all', // 标签筛选
        // 移除分页相关数据
        options: {
          loading: false,
          border: true,
        },
        dialogVisible: false,
        dialogMode: 'add', // 'add' or 'edit'
        dialogForm: {
          feeName: '',
          feeRate: '',
          feeType: '',
          inAccountName: '',
          createTime: '',
        }, // 表单数据
        dialogLoading: false,
        dialogRules: {
          outAccountName: [
            {
              required: true,
              message: '请输入贸易主体',
              trigger: 'blur',
              validator: (rule, value, callback) => {
                if (this.dialogMode === 'add' && !value) {
                  callback(new Error('请输入贸易主体'));
                } else {
                  callback();
                }
              },
            },
          ],
          feeType: [
            { required: true, message: '请选择费用类型', trigger: 'blur' },
          ],
          feeRate: [
            { required: true, message: '请输入费用比例', trigger: 'blur' },
            {
              pattern: /^\d+(\.\d{1,4})?$/,
              message: '最多4位小数',
              trigger: 'blur',
            },
          ],
          inAccountName: [
            { required: true, message: '请输入费用结算主体', trigger: 'blur' },
          ],
          createTime: [
            { required: true, message: '请选择生效期间', trigger: 'change' },
          ],
        },
      };
    },
    computed: {
      filteredList() {
        // 先按 invalid 标签过滤
        let arr = this.list;
        if (this.filterInvalidTab === 'valid') {
          arr = arr.filter(item => item.invalid == 0);
        } else if (this.filterInvalidTab === 'invalid') {
          arr = arr.filter(item => item.invalid == 1);
        }
        // 再按关键词过滤
        if (!this.searchKeyword) return arr;
        const kw = this.searchKeyword.trim();
        return arr.filter(
          item =>
            (item.outAccountName && item.outAccountName.includes(kw)) ||
            (item.feeName && item.feeName.includes(kw)) ||
            (item.inAccountName && item.inAccountName.includes(kw)),
        );
      },
    },
    created() {
      this.getList();
      // 获取下拉数据
      selector().then(res => {
        if (res) {
          this.subAccountNameSelectorVOList =
            res.subAccountNameSelectorVOList || [];
          this.splitFeeTypeSelectorVOList =
            res.splitFeeTypeSelectorVOList || [];
        }
      });
    },
    methods: {
      getParams() {
        // 只返回筛选参数，无分页参数
        return {
          ...this.searchParams,
        };
      },
      async getList() {
        // 首次加载时获取全量数据
        if (!this.allList.length) {
          this.options.loading = true;
          this.allListLoading = true;
          const params = {}; // 不传筛选参数，获取全量
          const res = await splitConfigQuery(params);
          this.allList = Array.isArray(res) ? res : res.records || [];
          this.options.loading = false;
          this.allListLoading = false;
        }
        // 本地过滤
        this.list = this.allList.filter(item => {
          if (
            this.searchParams.feeName &&
            !item.feeName.includes(this.searchParams.feeName)
          ) {
            return false;
          }
          return true;
        });
      },
      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.getList();
      },
      // 移除分页相关方法
      handleImport: debounce(async function () {
        const { res, err } = await secondaryTemplateImport();
        if (res && !err) {
          this.$message.success('导出成功');
          window.location.href = res;
        }
      }, 1000),
      handleExport() {
        // 导出当前 filteredList 为 CSV
        const data = this.filteredList;
        if (!data.length) {
          this.$message.warning('无可导出数据');
          return;
        }
        // 取表头
        const columns = [
          { label: 'ID', prop: 'id' },
          { label: '贸易主体', prop: 'outAccountName' },
          { label: '费用类型', prop: 'feeName' },
          { label: '费用比例', prop: 'feeRate' },
          { label: '费用结算主体', prop: 'inAccountName' },
          { label: '最后更新时间', prop: 'updateTime' },
          { label: '更新人', prop: 'updater' },
          { label: '申请人', prop: 'creator' },
          { label: '生效期间', prop: 'createTime' },
        ];
        const csvRows = [];
        // 表头
        csvRows.push(columns.map(col => col.label).join(','));
        // 数据
        data.forEach(row => {
          csvRows.push(
            columns
              .map(col => {
                let val = row[col.prop];
                if (col.prop === 'feeRate') {
                  // 百分比格式
                  val = this.formatPercent(val);
                }
                // 处理逗号、换行
                if (typeof val === 'string') {
                  val = '"' + val.replace(/"/g, '""') + '"';
                }
                return val == null ? '' : val;
              })
              .join(','),
          );
        });
        const csvContent = '\uFEFF' + csvRows.join('\n');
        const blob = new Blob([csvContent], {
          type: 'text/csv;charset=utf-8;',
        });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.setAttribute('download', '分账配置列表.csv');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      },
      openDialog(mode, row) {
        this.dialogMode = mode;
        if (mode === 'edit' && row) {
          this.dialogForm = { ...row };
          // 在编辑模式下，根据 feeName 找到对应的 feeType
          if (row.feeName) {
            const feeTypeItem = this.splitFeeTypeSelectorVOList.find(
              item => item.dictDesc === row.feeName,
            );
            if (feeTypeItem) {
              this.dialogForm.feeType = feeTypeItem.dictValue;
            }
          }
        } else {
          this.dialogForm = {
            outAccountName: '',
            feeName: '',
            feeRate: '',
            feeType: '',
            inAccountName: '',
            createTime: '',
          };
        }
        this.dialogVisible = true;
      },
      async handleDialogSubmit() {
        this.$refs.dialogForm.validate(async valid => {
          if (!valid) return;
          this.dialogLoading = true;
          try {
            if (this.dialogMode === 'add') {
              await splitConfigAdd(this.dialogForm);
              Message.success('新增成功');
            } else {
              await splitConfigUpdate(this.dialogForm);
              Message.success('更新成功');
            }
            this.dialogVisible = false;
            this.dialogLoading = false;
            // 刷新本地数据
            this.allList = [];
            await this.getList();
          } catch (e) {
            this.dialogLoading = false;
          }
        });
      },
      formatPercent(val) {
        if (val === null || val === undefined || val === '') return '';
        const num = Number(val);
        if (isNaN(num)) return val;
        return (num * 100).toFixed(2) + '%';
      },
      handleRefresh() {
        this.allList = [];
        this.getList();
      },
      handleStatusChange(row, newStatus) {
        // TODO: 实现状态变更逻辑，如调用后端接口
        this.$message.info(`将[${row.outAccountName}]状态改为：${newStatus}`);
      },
      async handleInvalidChange(row, newVal) {
        const actionText = newVal === 1 ? '作废' : '启用';
        this.$confirm(`确定要${actionText}该分账配置吗？`, '二次确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(async () => {
            const updateData = { ...row, invalid: newVal };
            try {
              await splitConfigUpdate(updateData);
              this.$message.success(newVal === 1 ? '作废成功' : '启用成功');
              this.allList = [];
              await this.getList();
            } catch (e) {
              this.$message.error('操作失败');
            }
          })
          .catch(() => {
            // 用户取消，无需处理
          });
      },
      handleFeeTypeChange(value) {
        // 根据选择的 dictValue 找到对应的 dictDesc
        const selectedItem = this.splitFeeTypeSelectorVOList.find(
          item => item.dictValue === value,
        );
        if (selectedItem) {
          this.dialogForm.feeName = selectedItem.dictDesc;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .cost-ratio-setting-container {
    padding: 20px;
    background: #f5f7fa;
    min-height: 100vh;

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 12px 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      .page-title {
        margin: 0;
        color: #303133;
        font-size: 20px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;

        i {
          color: #409eff;
          font-size: 24px;
        }
      }
      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
    .search-form {
      margin-bottom: 12px;
      background: white;
      padding: 12px 20px 0 20px;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    }
    .table-card {
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      background: white;
      margin-top: 8px;
      .pagination-bar {
        margin: 16px 0 0 0;
        text-align: right;
      }
    }
  }
</style>
