<template>
  <div>
    <el-form inline>
      <el-form-item label="系统编号:">
        <el-input v-model="searchParams.id" placeholder="请输入系统编号" />
      </el-form-item>

      <el-form-item label="用户ID:">
        <el-input v-model="searchParams.idCode" placeholder="请输入用户ID" />
      </el-form-item>

      <el-form-item label="手机号码:">
        <el-input v-model="searchParams.mobile" placeholder="请输入手机号码" />
      </el-form-item>

      <el-form-item
        v-if="['withdrawal'].includes(type)"
        label="平台注册手机号:"
      >
        <el-input
          v-model="searchParams.regMobile"
          placeholder="请输入平台注册手机号"
        />
      </el-form-item>
      <!-- <el-form-item label="申请人:">
        <el-input
          v-model="searchParams.proposerName"
          placeholder="请输入申请人"
        />
      </el-form-item> -->
      <el-form-item :label="'申请时间:'">
        <el-date-picker
          v-model="searchDate"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="账户类型:">
        <el-select v-model="searchParams.accountType" placeholder="请选择">
          <el-option
            v-for="item in businessList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item> -->

      <el-form-item
        v-if="['refund', 'refundApply'].includes(type)"
        label="业务类型:"
      >
        <el-select v-model="searchParams.type" placeholder="请选择">
          <el-option
            v-for="item in typeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="状态:">
        <el-select v-model="searchParams.status" placeholder="请选择">
          <el-option
            v-for="item in reviewList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch()">查询</el-button>
        <el-button type="primary" @click="onReset">重置</el-button>
        <el-button
          v-if="['refundApply'].includes(type)"
          type="primary"
          @click="onAdd"
        >
          新增退款申请
        </el-button>

        <el-button
          v-if="['withdrawal'].includes(type)"
          type="primary"
          :permission-key="exportPermissionKey"
          @click="onExport"
        >
          导出
        </el-button>

        <el-upload
          style="display: inline-block; margin: 0 10px"
          :action="action"
          :data="uploadData"
          :headers="headers"
          accept="[xlsx]"
          :on-success="onImport"
        ></el-upload>
      </el-form-item>
    </el-form>
    <div v-if="['withdrawal'].includes(type)">
      提现总金额：{{ batchInfo.allmoney }}元, 批量需处理剩余数量：{{
        batchInfo.num
      }}
      <el-button
        v-if="['withdrawal'].includes(type)"
        type="primary"
        :permission-key="exportPermissionKey"
        @click="clickBatch('all')"
      >
        批量处理
      </el-button>
      失败批量需处理剩余数量：{{ batchInfo.failNum }}
      <el-button
        v-if="['withdrawal'].includes(type)"
        type="primary"
        :permission-key="exportPermissionKey"
        @click="clickBatch('allFail')"
      >
        失败批量处理
      </el-button>
    </div>
    <el-dialog title="提示" :visible.sync="batchDialogVisible" width="30%">
      <span>确认审核通过{{ batchNum }}条申请吗？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="batchDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onBatch">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { newExportExcel } from '@/api/blob';
  import { downloadFile, setInitData, initSearchParams } from '@/utils';

  import { getCookie, appCode } from '@/utils/auth';
  import { generateRandomApplyNo } from '@/utils/index';

  export default {
    props: {
      dialog: {
        type: Boolean,
        default: false,
      },
      type: {
        type: String,
        default: '',
      },
      exportUrl: {
        type: String,
        default: '',
      },
      reviewList: {
        type: Array,
        default: () => [],
      },
      countryCodes: {
        type: Array,
        default: () => [],
      },
      batchInfo: {
        type: Object,
        default: null,
      },
      typeList: {
        type: Array,
        default: () => [],
      },
      exportMethod: {
        type: String,
        default: 'post',
      },
      businessList: {
        type: Array,
        default: () => [],
      },
      exportPermissionKey: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        dialogFlag: false,
        statusTypeList: [{ label: '提现处理中', value: 1 }],
        // 导入相关
        headers: {
          token: getCookie(),
          appCode: appCode(),
          appId: 'abmau',
        },
        uploadData: {
          appId: 'abmau',
          timeStamp: Date.now(),
        },
        action: '',
        btnLoading: false,
        batchDialogVisible: false,
        searchParams: {
          amount: '',
          proposerName: '',
          mobile: '',
          idCode: '',
          businessType: '',
          type: '',
          accountType: '', //awx //默认写死账户类型
          endAt: '',
          startAt: '',
          id: '',
          status: '',
          userQuery: '',
          reviewEndAt: '',
          reviewStartAt: '',
          exportStatus: '',
          regMobile: '',
        },
        batchNum: '',
        key: '',
        reviewDate: '',
        searchDate: '',
      };
    },
    created() {
      this.onReset();
      this.action =
        'https://' + window.ACCESS_HOSTS.apiHost + '/api/abmio/api/v1.0/upload';
    },
    methods: {
      getParams() {
        this.searchParams.startTimeStrFor = this.searchDate
          ? this.searchDate[0]
          : '';
        this.searchParams.endTimeStrFor = this.searchDate
          ? this.searchDate[1]
          : '';
        this.searchParams.createStartTime = this.searchDate
          ? this.searchDate[0]
          : '';
        this.searchParams.createEndTime = this.searchDate
          ? this.searchDate[1]
          : '';
        this.searchParams.createdAtStart = this.searchDate
          ? this.searchDate[0]
          : '';
        this.searchParams.createdAtEnd = this.searchDate
          ? this.searchDate[1]
          : '';
        const params = {
          ...this.searchParams,
        };
        // 提现充值， 审核列表不需要 草稿状态
        params.status == this.searchParams.status;
        params.countryCode == this.searchParams.countryCode;

        return initSearchParams(params);
      },
      onSearch() {
        console.log('[][][][][]');
        this.$emit('onSearch', this.getParams());
      },
      onAdd() {
        this.$emit('onAdd');
      },

      onReset() {
        Object.assign(
          this.$data.searchParams,
          this.$options.data().searchParams,
        );
        this.searchDate = setInitData(30);
        this.onSearch();
      },
      clickBatch(key) {
        this.key = key;
        if (key == 'all') {
          if (!this.batchInfo.num) {
            this.$message({ message: '数量为0，不能提交', type: 'warning' });
            return;
          }
          this.batchNum = this.batchInfo.num;
        } else {
          if (!this.batchInfo.failNum) {
            this.$message({ message: '数量为0，不能提交', type: 'warning' });
            return;
          }
          this.batchNum = this.batchInfo.failNum;
        }
        this.batchDialogVisible = true;
      },
      onImport(response) {
        let obj = response.data;
        if ('xlsx' == obj.extName || 'xls' == obj.extName) {
          this.$emit('onImport', obj);
        } else {
          this.$message({
            message: '文件格式不对,请删除，重新上传',
            type: 'warning',
          });
        }
      },

      onBatch() {
        if (this.key == 'all') {
          console.log(this.batchInfo.idsStr, 'aaaaaaaaa');
          this.$emit('onBatch', this.batchInfo.idsStr);
        } else {
          console.log(this.batchInfo.failIdsStr, 'bbbbbbbbbb');

          this.$emit('onBatch', this.batchInfo.failIdsStr);
        }
        this.batchDialogVisible = false;
      },

      onExport() {
        let params = this.getParams();
        newExportExcel(params, `/api${this.exportUrl}`, this.exportMethod).then(
          res => {
            downloadFile(res.data, '提现列表', 'csv');
          },
        );
        this.dialogFlag = false;
      },

      handleClose(done) {
        this.$confirm('审核通过这条申请吗？')
          .then(_ => {})
          .catch(_ => {});
      },

      onSubmit() {
        this.dialogFlag = true;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
