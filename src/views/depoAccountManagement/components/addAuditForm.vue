<!--
 * @Description: 
 * @Author: 陈雪磊
 * @Date: 2021-12-18 16:45:35
 * @LastEditTime: 2022-08-10 18:25:08
 * @LastEditors: xuxiang
 * @Reference: 
-->
<template>
  <div class="saleSubjectDialog">
    <el-form ref="saveParams" :model="saveParams" label-width="120px">
      <el-form-item
        prop="userMobile"
        :rules="[
          {
            required: true,
            message: '请输入手机号',
            trigger: 'change',
          },
        ]"
        label="代理手机号:"
      >
        <el-input
          v-model="saveParams.userMobile"
          placeholder="请输入代理手机号"
        />
        <el-button type="primary" @click="getAmount(saveParams.userMobile)">
          查看余额
        </el-button>
        <span ref="yue" style="color: #f00">
          存管账户余额: ￥{{ depoBalance }}; 保证金余额: ￥{{ marginBalance }}
        </span>
      </el-form-item>

      <el-form-item
        label="账户类型:"
        prop="accountType"
        :rules="[
          {
            required: true,
            message: '请选择账户类型',
            trigger: 'change',
          },
        ]"
      >
        <el-select
          v-model="saveParams.accountType"
          placeholder="请选择账户类型"
        >
          <el-option
            v-for="item in accountTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item
        label="业务类型"
        prop="type"
        :rules="[
          {
            required: true,
            message: '请选择业务类型',
            trigger: 'change',
          },
        ]"
      >
        <el-select v-model="saveParams.type" placeholder="请选择业务类型">
          <el-option
            v-for="(item, index) in typeList"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <p style="color: #ff0a00">
        退款金额：用户实际到账金额，平台扣除金额：平台实际到账金额
      </p>

      <el-form-item
        label="退款金额"
        prop="amount"
        :rules="[
          {
            required: true,
            message: '请填写平台扣收金额',
            trigger: 'change',
          },
          {
            validator: rules.validateAmount,
            trigger: 'change',
          },
        ]"
      >
        <el-input v-model="saveParams.amount">
          <template slot="append">元</template>
        </el-input>
      </el-form-item>
      <el-form-item
        label="平台扣收金额"
        prop="deductAmount"
        :rules="[
          {
            required: true,
            message: '请填写平台扣收金额',
            trigger: 'change',
          },
          {
            validator: rules.validateNumber,
            trigger: 'change',
          },
        ]"
      >
        <el-input v-model="saveParams.deductAmount">
          <template slot="append">元</template>
        </el-input>
      </el-form-item>

      <el-form-item required :error="errorTip" label="退款凭证:">
        <uploadImg
          ref="uploadImg"
          :max="9"
          :init-file-list="initFileList"
          @onRemove="(a, b) => onRemove(a, b, 2)"
          @changeImage="changeImage"
        />
        不重新上传则不更新
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="saveParams.remark"
          type="text"
          maxlength="490"
          show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item style="margin-left: 20px">
        <el-button @click="resetForm">取消</el-button>
        <el-button type="primary" @click="saveForm('saveParams')">
          保存
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import { tenBitTimestamp } from '@/utils';
  import uploadImg from '@/components/uploadImg';
  import { getAmount } from '@/api/depoAccountManagement';
  export default {
    components: {
      uploadImg,
    },
    props: {
      formData: {
        type: Object,
        default: () => {
          return {};
        },
      },
      selectOptions: {
        type: Object,
        default: () => {
          return {};
        },
      },
    },
    data() {
      //这里是自定义的开始时间规则
      var begindateRule = (rule, value, callback) => {
        if (!this.saveParams.status || this.saveParams.status == 0) {
          if (!value || Date.parse(value) > Date.parse(tenBitTimestamp())) {
            callback();
          } else {
            return callback(new Error('开始时间必须大于当前时间'));
          }
        } else {
          callback();
        }
      };

      const validateBsb = (rule, value, callback) => {
        const str = value.replace(/\s/g, '');
        if (str.length !== 6 && value) {
          callback(new Error('bsb长度必须为6位'));
        } else {
          callback();
        }
      };
      const validateAmount = (rule, value, callback) => {
        const reg = /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/;
        if (value && (!reg.test(value) || value == 0)) {
          callback(new Error('金额不得为空且金额需大于0.01'));
        } else {
          callback();
        }
      };

      const validateNumber = (rule, value, callback) => {
        if (value >= 0) {
          callback();
        } else {
          callback(new Error('填写金额有误！'));
        }
      };

      //这里是自定义的失效时间规则
      var enddateRule = (rule, value, callback) => {
        if (this.saveParams.beginTime && value) {
          if (Date.parse(this.saveParams.beginTime) < Date.parse(value)) {
            callback();
          } else {
            return callback(new Error('失效时间必须大于开始时间'));
          }
        } else {
          callback();
        }
      };
      return {
        rules: {
          validateBsb,
          validateAmount,
          validateNumber,
        },
        initFileList: [],
        errorTip: '',
        saveParams: this.formData,
        accountTypeList: [
          {
            label: '厦门国际银行存管',
            value: 'xm',
          },
        ],
        typeList: [
          {
            label: '只退货款',
            value: 'OM',
          },
          {
            label: '只退保证金',
            value: 'SF',
          },
        ],
        rulesForm: {
          userMobile: [
            { required: true, message: '手机号不能为空', trigger: 'change' },
          ],
          accountType: [
            { required: true, message: '账户类型不能为空', trigger: 'change' },
          ],
          type: [
            { required: true, message: '业务类型不能为空', trigger: 'change' },
          ],
          amount: [
            { required: true, message: '金额不能为空', trigger: 'change' },
          ],
          deductAmount: [
            {
              required: true,
              message: '平台扣收金额不能为空',
              trigger: 'change',
            },
          ],
          userLevelList: [
            {
              required: true,
              message: '下单用户等级不能为空',
              trigger: 'change',
            },
          ],
          endTime: [
            { required: true, validator: enddateRule, trigger: 'blur' },
          ],
        },
        depoBalance: '',
        marginBalance: '',
      };
    },
    watch: {
      formData: {
        handler(newObject, oldObject) {
          if (newObject) {
            this.saveParams = newObject;
          }
        },
        deep: true,
        immediate: true,
      },
    },
    mounted() {
      this.$nextTick(() => {
        this.$on('initData', e => {
          this.$refs.saveParams.resetFields();
          this.saveParams = e;
        });
      });
    },
    methods: {
      changeImage(files, fileList) {
        if (fileList.length) {
          this.saveParams.proof = fileList[0].file_url;
        }
      },
      onRemove(files, fileList, key) {
        if (key === 1) {
          this.saveParams.imageId = '';
          return;
        }
        this.saveParams.album = fileList.map(item => item.id);
        if (this.saveParams.album.length === 0) {
          this.errorTip = '至少上传一张凭证图';
        }
      },
      async handleSelectDept(val) {
        if (this.saveParams.depotId != val.id) {
          this.saveParams.depotId = val.id;
          this.saveParams.countryIdList = [];
        }
        this.$emit('handleSelectDept', val, () => {
          // const { deptList = [], warehouseCode } = this.selectOptions;
          // const { depotId } = this.saveParams;
          // deptList.forEach(item => {
          //   if (item.id === depotId) {
          //     if (item.depotCode != warehouseCode) {
          //       this.saveParams.countryIdList = [];
          //     }
          //   }
          // });
        });
      },
      async handleSelectChannel(val) {
        if (this.saveParams.channelCode != val.channelCode) {
          this.saveParams.channelCode = val.channelCode;
          this.saveParams.shopCode = '';
        }
        this.$emit('handleSelectChannel', val, () => {
          // const { storeList = [] } = this.selectOptions;
          // const { shopCode } = this.saveParams;
          // const storeCodeList = storeList.map(item => item.storeCode);
          // if (!storeCodeList.includes(shopCode)) {
          //   this.saveParams.shopCode = '';
          // }
        });
      },
      getAmount(userMobile) {
        if (userMobile) {
          getAmount({ mobile: userMobile }).then(data => {
            if (!data.err) {
              this.depoBalance = data.res.depoBalance;
              this.marginBalance = data.res.marginBalance;
            }
          });
        } else {
          this.$message({
            message: '手机号不能为空',
            type: 'fail',
          });
        }
      },
      saveForm(formName) {
        this.saveParams.id = this.formData.id;
        this.$refs[formName].validate(valid => {
          if (valid) {
            this.$emit('saveForm', JSON.parse(JSON.stringify(this.saveParams)));
          }
        });
      },
      resetForm() {
        this.saveParams = {};
        this.$emit('close');
      },
    },
  };
</script>

<style lang="scss" scoped>
  .saleSubjectDialog {
    .subTitle {
      font-weight: bold;
      font-size: 16px !important;
    }
    .timeItem {
      /deep/.el-form-item__label:before {
        content: '' !important;
      }
    }
    .el-select,
    .el-date-editor {
      width: 280px;
    }
  }
</style>
