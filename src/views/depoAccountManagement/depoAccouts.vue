<!--
 * @Description: 
 * @Author: wuqingsong
 * @Date: 2022-08-09 16:30:38
 * @LastEditTime: 2022-11-02 15:27:39
 * @LastEditors: Please set LastEditors
 * @Reference: 
-->
<template>
  <div>
    <el-form :inline="true" :model="params">
      <el-form-item label="手机号码:">
        <el-input v-model="params.mobile" placeholder="请输入手机号码" />
      </el-form-item>
      <el-form-item label="开户状态:">
        <el-select v-model="params.status" clearable placeholder="请选择">
          <el-option
            v-for="(item, index) in statusList"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <ac-permission-button
          btn-text="查询"
          permission-key=""
          @click="search(true)"
        ></ac-permission-button>
        <el-button type="primary" @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column
        align="center"
        prop="memberId"
        label="用户id"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="mobile"
        label="手机号"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="cashBalance"
        label="可提现货款"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="cashFreezeBalance"
        label="可提现货款(冻结)"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="depoBalance"
        label="可用货款"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="depoFreezeBalance"
        label="可用货款(冻结)"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="totalBalance"
        label="货款总额"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="thirdName"
        label="绑卡姓名"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="idCardNo"
        label="身份证"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="bankName"
        label="所属银行"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="thirdAccountId"
        label="银行卡号"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="reserveMobile"
        label="银行预留手机"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="virtualNo"
        label="厦门银行电子卡号"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="type"
        label="开户渠道"
        min-width="100"
      >
        <template slot-scope="{ row }">
          {{ channelType(row.type) }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="abroad"
        label="账户类型"
        min-width="250"
      >
        <template slot-scope="{ row }">
          {{ abroadType(row.abroad) }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="status"
        label="状态"
        min-width="150"
      ></el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page="pageNum"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>
<script>
  import { setInitData } from '@/utils';
  import {
    getAccountStatusList,
    getAccounts,
  } from '@/api/depoAccountManagement';

  export default {
    data() {
      return {
        params: {
          mobile: '',
          type: '',
          status: '',
        },

        pageNum: 1,
        pageSize: 20,
        statusList: [], // 状态下拉框
        tableData: [],
        loading: false, // loading表格加载层
        pageNo: 1,
        limit: 10,
        total: 0,
      };
    },
    created() {
      this.initParams();
      this.fetch();
      this.getEnums();
    },
    methods: {
      initParams() {
        if (!this.$route.query) {
          return;
        }
        const { mobile = '' } = this.$route.query;
        this.params.mobile = mobile;
        if (mobile) {
          this.fetch();
        }
      },
      async getEnums() {
        const { res } = await getAccountStatusList();
        if (res) {
          this.statusList = res || [];
        }
      },
      async fetch() {
        const params = {
          limit: this.pageSize,
          pageNo: this.pageNum,
          ...this.params,
        };
        delete params.detailTime;

        this.loading = true;
        try {
          const { res } = await getAccounts(params);
          if (res) {
            this.tableData = res.records || [];
            this.total = res.total || 0;
            this.loading = false;
          } else {
            this.loading = false;
            this.tableData = [];
            this.total = 0;
          }
        } catch (error) {
          this.loading = false;
        }
      },
      channelType(type) {
        return type == 'awx' ? '空中云汇' : '厦门';
      },
      abroadType(type) {
        return type === 0 ? '境内' : '海外';
      },
      search() {
        this.pageNum = 1;
        this.fetch();
      },
      reset() {
        Object.assign(this.params, this.$options.data.call(this).params);
        this.pageNum = 1;
        this.pageSize = 20;
        this.params.detailTime = setInitData(90);
      },
      handleSizeChange(val) {
        this.pageNum = 1;
        this.pageSize = val;
        this.fetch();
      },
      handleCurrentChange(val) {
        this.pageNum = val;
        this.fetch();
      },
    },
  };
</script>
<style lang="scss"></style>
