<template>
  <div>
    <searchForm
      type="refundApply"
      :review-list="statusList"
      :business-list="accountType"
      :type-list="typeList"
      export-permission-key="refundApply-export"
      @onSearch="onSearch"
      @onAdd="handleAddEdit"
    ></searchForm>

    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
      @selection-change="handleSelectionChange"
    >
      <template slot="operation" slot-scope="scope">
        <el-button
          v-if="scope.row.button.showView"
          slot="reference"
          type="text"
          size="mini"
          @click="handleSee(scope.row, 1)"
        >
          查看
        </el-button>
        <el-button
          v-if="scope.row.button.showSubmit"
          slot="reference"
          type="text"
          size="mini"
          @click="handleSee(scope.row, 2)"
        >
          提交
        </el-button>
        <el-button
          v-if="scope.row.button.showEdit"
          slot="reference"
          type="text"
          size="mini"
          @click="handleSee(scope.row, 3)"
        >
          编辑
        </el-button>
        <el-button
          v-if="scope.row.button.showUndo"
          slot="reference"
          type="text"
          size="mini"
          @click="handleSee(scope.row, 4)"
        >
          撤销
        </el-button>
      </template>
    </dynamictable>
    <seeModal ref="seeModal" :show-btn="showBtn" @onOk="onOK"></seeModal>

    <!-- //新增申请 -->
    <!-- 新增销售主体/编辑销售主体 -->
    <el-dialog
      width="40%"
      :title="title"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
      @close="onClose"
    >
      <addAuditForm
        ref="form"
        :form-data="formData"
        :select-options="selectoptions"
        @close="showDialog = false"
        @saveForm="saveForm"
      ></addAuditForm>
    </el-dialog>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import seeModal from './components/seeModal';
  import searchForm from './components/searchForm';
  import addAuditForm from './components/addAuditForm';
  import { formatDetail } from './js/index';
  import { parseTime } from '@/utils';
  import store from '@/store';

  import {
    refundApplyList,
    createApply,
    submitInfo,
    showUserBalance,
    editApply,
    undoApply,
    editItemApply,
  } from '@/api/depoAccountManagement';

  export default {
    components: {
      seeModal,
      dynamictable,
      searchForm,
      addAuditForm,
    },

    data() {
      return {
        title: '新增退款申请',
        showDialog: false,
        formData: {},
        selectoptions: {},
        statusList: [
          { value: 0, label: '草稿' },
          { value: 2, label: '待初审' },
          { value: 6, label: '已驳回' },
          { value: 1, label: '已撤销' },
          { value: 7, label: '退款成功' },
          { value: 8, label: '退款失败' },
        ],
        accountType: [
          {
            label: '厦门银行存管',
            value: 'xm',
          },
        ],
        typeList: [
          {
            label: '只退货款',
            value: 'OM',
          },
          {
            label: '只退保证金',
            value: 'SF',
          },
        ],
        search: {},
        showBtn: false,
        isFirst: true,
        options: {
          loading: false,
          border: true,
          mutiSelect: false,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        selectArr: [], // 选择的列表数据
        list: [],
        columns: [
          {
            prop: 'id',
            label: '编号',
          },
          {
            prop: 'memberId',
            label: '用户ID',
          },
          {
            prop: 'userMobile',
            label: '用户手机号',
          },
          {
            prop: 'applicant',
            label: '申请人',
          },
          {
            prop: 'accountTypeText',
            label: '账户类型',
          },
          {
            prop: 'typeText',
            label: '业务类型',
          },
          {
            prop: 'amount',
            label: '退款金额',
          },
          {
            prop: 'thirdName',
            label: '开户人',
          },
          {
            prop: 'bankName',
            label: '开户行',
          },
          {
            prop: 'bankNo',
            label: '银行卡号',
          },
          {
            prop: 'reservedMobile',
            label: '预留手机',
          },
          {
            prop: 'statusText',
            label: '状态',
          },
          {
            prop: 'statusUpdateTime',
            label: '状态更新时间',
            render: ({ statusUpdateTime }) => (
              <span>{statusUpdateTime ? parseTime(statusUpdateTime) : ''}</span>
            ),
          },
          {
            prop: 'reviewer',
            label: '审核人',
          },
          {
            prop: 'createTime',
            label: '创建时间',
            render: ({ createTime }) => (
              <span> {createTime ? parseTime(createTime) : ''} </span>
            ),
          },
          {
            prop: 'operation',
            label: '操作',
            width: '100px',
            fixed: 'right',
            scopedSlots: { customRender: 'operation' },
          },
        ],
        createForm: {
          id: '',
          mobile: '',
          accountType: '',
          type: '',
          proof: '',
          attachment: '',
          amount: '',
          poundage: '',
          deductAmount: '',
          remark: '',
          adminName: '',
        },
      };
    },

    methods: {
      saveForm(data) {
        let param = data;
        this.param = data;
        this.param.mobile = data.userMobile;
        this.param.adminName = store.state.user.username;
        if (data.id) {
          editItemApply(this.param).then(data => {
            if (!data.err) {
              this.$message({
                message: '操作成功',
                type: 'success',
              });
              this.showDialog = false;
              this.formData = {};
              this.getList();
            }
          });
        } else {
          //新创建申请单子
          let param = data;
          this.param = data;
          this.param.mobile = data.userMobile;
          this.param.adminName = store.state.user.username;
          createApply(this.param).then(data => {
            if (!data.err) {
              this.$message({
                message: '操作成功',
                type: 'success',
              });
              this.showDialog = false;
              this.formData = {};
              this.getList();
            }
          });
        }
      },
      handleAddEdit() {
        this.formData = {};
        this.showDialog = true;
      },
      // 打开申请弹框
      async openDialog(e) {
        this.showDialog = true;
      },

      onSearch(e) {
        console.log('ppppp');
        this.search = e;
        this.getList(true);
      },

      onClose() {
        this.showDialog = false;
        this.formData = {};
      },
      getParams() {
        const status = this.search.status;
        const params = {
          ...this.search,
          status: status !== undefined ? status : '',
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
          systemId: this.search.id,
        };
        if (this.search.idCode) {
          params.memberId = this.search.idCode;
        }
        return params;
      },
      getList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;

        try {
          refundApplyList(params).then(data => {
            let result = data.res;
            this.options.loading = false;
            this.list = result && result.records ? result.records : [];
            this.pagination.total = result ? result.total : 0;
          });
        } catch (err) {
          this.options.loading = false;
        }
      },
      handleSee(val, key) {
        if (key == 1) {
          const obj = {
            ...val,
            accountType:
              val.accountType === 'awx'
                ? '空中云汇'
                : val.accountType === 'xm'
                ? '厦门存管'
                : '其他',
          };
          this.showBtn = false;
          this.id = val.id;
          let arr;
          arr = formatDetail(obj, false).applyAuditDetails;
          this.$refs['seeModal'].open({
            arr,
            title: '退款申请查看',
          });
        } else if (key == 2) {
          submitInfo({ id: val.id }).then(data => {
            if (!data.err) {
              this.$message({
                message: '操作成功',
                type: 'success',
              });
              this.getList();
            }
          });
        } else if (key == 3) {
          editApply({ id: val.id }).then(data => {
            if (!data.err) {
              if (data.res.data) {
                this.formData = data.res.data.depositoryRefundApply;
                this.formData.userMobile = data.res.data.mobile;
                this.showDialog = true;
              } else {
                this.$message.error(data.res.msg);
              }
            }
          });
        } else if (key == 4) {
          undoApply({ id: val.id }).then(data => {
            if (!data.err) {
              this.$message({
                message: '操作成功',
                type: 'success',
              });
              this.getList();
            }
          });
        }
      },
      async onOK(params) {},
      getStatusArr() {
        return {
          isOperation: status => {
            return this.selectArr.every(item => {
              return item.status === status;
            });
          },
          getIds: status => {
            return this.selectArr
              .map(item => {
                if (item.status !== status) {
                  return item.id;
                }
              })
              .filter(i => !!i);
          },
        };
      },
      handleSelectionChange(val) {
        this.selectArr = val;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
