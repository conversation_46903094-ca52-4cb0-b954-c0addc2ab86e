<template>
  <div>
    <searchForm
      type="refund"
      export-url="/cust-bus/returnPaymentApplicant/export"
      export-method="post"
      :review-list="statusList"
      :business-list="accountType"
      :type-list="typeList"
      export-permission-key="refundPaymentCheck-export"
      @onBatch="handleBatch"
      @onSearch="onSearch"
    ></searchForm>

    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
      @selection-change="handleSelectionChange"
    >
      <template slot="operation" slot-scope="scope">
        <el-button
          v-if="scope.row.button.showView"
          slot="reference"
          type="info"
          size="mini"
          @click="handleSee(scope.row)"
        >
          查看
        </el-button>
        <el-button
          v-if="scope.row.button.showTrial"
          slot="reference"
          type="warning"
          size="mini"
          @click="handleSee(scope.row, 1)"
        >
          审核
        </el-button>
      </template>
    </dynamictable>
    <seeModal ref="seeModal" :show-btn="showBtn" @onOk="onOK"></seeModal>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import seeModal from './components/seeModal';
  import searchForm from './components/searchForm';
  import { parseTime } from '@/utils';
  import {
    refundAuditList,
    updateApplyStatus,
    editApply,
    abroadRefund,
  } from '@/api/accountPay';
  import { formatDetail } from './js/index';
  import store from '@/store';

  export default {
    components: {
      seeModal,
      dynamictable,
      searchForm,
    },

    data() {
      return {
        statusList: [
          { value: 2, label: '待初审' },
          { value: 3, label: '待复审' },
          { value: 5, label: '已通过' },
          { value: 7, label: '退款成功' },
          { value: 8, label: '退款失败' },
        ],
        accountType: [
          {
            label: '厦门银行存管',
            value: 'xm',
          },
        ],
        typeList: [
          {
            label: '只退货款',
            value: 'OM',
          },
          {
            label: '只退保证金',
            value: 'SF',
          },
        ],
        search: {},
        showBtn: false,
        isFirst: true,
        options: {
          loading: false,
          border: true,
          mutiSelect: false,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        selectArr: [], // 选择的列表数据
        list: [],
        columns: [
          {
            prop: 'id',
            label: '编号',
          },
          {
            prop: 'memberId',
            label: '用户ID',
          },
          {
            prop: 'userMobile',
            label: '用户手机号',
          },
          {
            prop: 'applicant',
            label: '申请人',
          },
          {
            prop: 'accountTypeText',
            label: '账户类型',
          },
          {
            prop: 'typeText',
            label: '业务类型',
          },
          {
            prop: 'amount',
            label: '退款金额',
          },
          {
            prop: 'thirdName',
            label: '开户人',
          },
          {
            prop: 'statusText',
            label: '状态',
          },
          {
            prop: 'reviewer',
            label: '审核人',
          },
          {
            prop: 'createTime',
            label: '创建时间',
            render: ({ createTime }) => (
              <span> {createTime ? parseTime(createTime) : ''} </span>
            ),
          },
          {
            prop: 'operation',
            label: '操作',
            width: '100px',
            fixed: 'right',
            scopedSlots: { customRender: 'operation' },
          },
        ],
      };
    },

    methods: {
      onSearch(e) {
        this.search = e;
        this.getList(true);
      },
      getParams() {
        const status = this.search.status;
        const allStatus = [0, 1, 2, 3, 5]; // 申请列表全部状态
        const params = {
          ...this.search,
          status: status !== undefined ? status : '',
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        if (this.search.idCode) {
          params.memberId = this.search.idCode;
        }
        return params;
      },
      getList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        try {
          refundAuditList(params).then(data => {
            let result = data.res;
            this.options.loading = false;
            this.list = result && result.records ? result.records : [];
            this.pagination.total = result ? result.total : 0;
          });
        } catch (err) {
          this.options.loading = false;
        }
      },
      handleSee(val, key) {
        if (key == 1) {
          editApply({ id: val.id }).then(data => {
            if (!data.err) {
              let result = data.res.data;
              const obj = {
                ...result.depositoryRefundApply,
                userMobile: result.mobile,
                accountType: result.accountTypeText,
                typeText: result.typeText,
                statusText: result.statusText,
              };
              this.showBtn = false;
              this.isFirst = key === 1 ? false : true;
              this.id = val.id;
              let arr = formatDetail(obj, key ? true : false).applyAuditDetails;
              if (key) {
                this.showBtn = true;
              }
              this.$refs['seeModal'].open({
                arr,
                title: '退货款初审',
              });
            }
          });
        } else {
          const obj = {
            ...val,
          };
          this.showBtn = false;
          this.isFirst = key === 1 ? false : true;
          this.id = val.id;
          let arr = formatDetail(obj, key ? true : false).applyAuditDetails;
          if (key) {
            this.showBtn = true;
          }

          this.$refs['seeModal'].open({
            arr,
            title: '查看退货款审核',
          });
        }
      },
      async onOK(params) {
        if (params.type == 1) {
          status = 7;
        } else {
          //复审拒绝
          status = 4;
        }
        const saveParams = {
          id: this.id,
          refusalCause: params.refuseToReason,
          reviewer: store.state.user.username,
          status: status,
        };
        abroadRefund(saveParams).then(data => {
          if (!data.err) {
            this.$message({
              message: '操作成功',
              type: 'success',
            });
            this.showBtn = false;
            this.getList();
          }
        });
      },
      getStatusArr() {
        return {
          isOperation: status => {
            return this.selectArr.every(item => {
              return item.status === status;
            });
          },
          getIds: status => {
            return this.selectArr
              .map(item => {
                if (item.status !== status) {
                  return item.id;
                }
              })
              .filter(i => !!i);
          },
        };
      },
      async handleBatch(key, id) {
        if (!id && this.selectArr.length === 0) {
          return this.$message.error('请选择订单');
        }
        const getStatusArr = this.getStatusArr();

        if (
          (key === 'first' && !getStatusArr.isOperation(0)) ||
          (key === 'tow' && !getStatusArr.isOperation(5))
        ) {
          const errArr =
            key === 'first' ? getStatusArr.getIds(0) : getStatusArr.getIds(5);
          this.$message.error(
            `${errArr}该笔退款申请记录状态已经改变，目前无需审核`,
          );
          return;
        }
        const ids = this.selectArr.map(item => item.id);
        const params = {
          status: key === 'first' ? 5 : 1,
          ids,
        };
        await returnPaymentApplicantBatchAuditPass(params);
        this.$message({
          message: '操作成功',
          type: 'success',
        });
        this.getList();
      },
      handleSelectionChange(val) {
        this.selectArr = val;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
