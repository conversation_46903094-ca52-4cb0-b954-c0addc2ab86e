/*
 * @Description:
 * @Author: 陈雪磊
 * @Date: 2021-12-18 18:10:26
 * @LastEditTime: 2023-03-14 19:30:07
 * @LastEditors: Please set LastEditors
 * @Reference:
 */
export function formatDetail(res = {}, input, key) {
  console.log(res, 'formatDetail');

  const applyAuditDetails = [
    {
      name: '用户手机号',
      value: res.userMobile,
    },
    {
      name: '账户类型',
      value: res.accountType,
    },
    {
      name: '业务类型',
      value: res.typeText,
    },
    {
      name: `退款金额`,
      value: res.amount + '元',
    },
    {
      name: '手续费',
      value: res.poundage + '%',
    },
    {
      name: '平台扣收金额',
      value: res.deductAmount + '元',
    },
    {
      name: '状态',
      value: res.statusText,
    },
    {
      name: '备注',
      value: res.remark,
    },

    {
      name: '拒绝原因',
      value: '',
      type: 'input',
      required: false,
      ishide: input ? false : true,
    },
    {
      name: '支付凭证:(点击图片下载)',
      value: res.proof,
      type: 'img',
    },
  ];

  const toExamine = [
    {
      name: '申请人',
      value: res.applyAdminName,
    },
    {
      name: '会员手机号',
      value: res.mobile,
    },
    {
      name: '账户类型',
      value: res.moneyType,
    },
    {
      name: '充值金额',
      value: res.amount,
    },
    {
      name: `当前${res.moneyType}账户余额`,
      value: res.balance,
    },
    {
      name: '支付方式',
      value: res.payment,
    },
    {
      name: '支付流水号',
      value: res.payTransactionId,
    },
    {
      name: '状态',
      value: res.status,
    },
    {
      name: '备注',
      value: res.remarks,
    },
    {
      name: '拒绝原因',
      value: '',
      type: 'input',
      required: true,
    },
  ];

  let refundAudit = [
    {
      name: '代理手机号',
      value: res.mobile,
    },
    {
      name: '账户类型',
      value: res.accountTypeText,
    },
    {
      name: '业务类型',
      value: res.typeText,
    },
    {
      name: '充值金额',
      value: res.amount + '元',
    },
    {
      name: '状态',
      value: res.statusText,
    },
    {
      name: '初审人',
      value: res.firstCheckAdminName,
      ishide: !['待复审', '已通过', '复审驳回'].includes(res.status),
    },
    {
      name: '审核人',
      value: res.checkAdminName,
      ishide: !['已审核', '已驳回'].includes(res.status),
    },
    {
      name: '复审人',
      value: res.checkAdminName,
      ishide: !['待复审', '已通过', '复审驳回'].includes(res.status),
    },
    {
      name: '拒绝原因',
      value: res.reasons,
      ishide: !['已驳回', '初审驳回', '复审驳回'].includes(res.status),
    },
    {
      name: '备注',
      value: res.remarks,
    },
    {
      name: '附件:(点击下载)',
      value: res.file,
      type: 'file',
      ishide: res.file ? false : true,
    },
    {
      name: '拒绝原因',
      value: '',
      type: 'input',
      required: false,
      ishide: input ? false : true,
    },
    {
      name: '支付凭证:(点击图片下载)',
      value:
        res.proofRefund || res.payVoucher ? JSON.parse(res.payVoucher) : [],
      type: 'img',
    },
  ];

  return {
    applyAuditDetails,
  };
}
