<template>
  <div>
    <searchForm
      type="withdrawal"
      export-url="/account-core/withdrawal/exportPaymentFile"
      :review-list="withdrawalAuditStatus"
      :business-list="accountType"
      :country-codes="countryCodes"
      export-permission-key="withdrawal-export"
      export-method="post"
      @onBatch="handleBatch"
      @onSearch="onSearch"
      @onImport="onImport"
    ></searchForm>

    <dynamictable
      :data-source="list"
      :columns="columns"
      :options="options"
      :pagination="pagination"
      :fetch="getList"
      @selection-change="handleSelectionChange"
    >
      <template slot="operation" slot-scope="scope">
        <el-button
          slot="reference"
          type="text"
          size="mini"
          :disabled="scope.row.status == 0 ? false : true"
          @click="dealAction(true, scope.row)"
        >
          处理
        </el-button>
        <el-button
          slot="reference"
          type="text"
          size="mini"
          :disabled="scope.row.status == 0 ? false : true"
          @click="dealAction(false, scope.row)"
        >
          驳回
        </el-button>
      </template>
    </dynamictable>
    <seeModal ref="seeModal" :show-btn="showBtn" @onOk="onOK"></seeModal>

    <el-dialog title="提示" :visible.sync="dialogVisible" width="30%">
      <span>确认通过这条申请吗？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="auditInfo(true)">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import dynamictable from '@/components/dynamic-table';
  import seeModal from './components/seeModal';
  import searchForm from './components/searchForm';
  import { parseTime } from '@/utils';
  import store from '@/store';
  import {
    returnPaymentApplicantList,
    withdrawalAuditApplicantList,
    auditWithdrawl,
    rejectWithdrawl,
    importStatus,
    exportPaymentFile,
    getSelectors,
  } from '@/api/accountPay';

  export default {
    components: {
      seeModal,
      dynamictable,
      searchForm,
    },

    data() {
      return {
        countryCodes: [],
        dialogFlag: false,
        atitle: '',
        dialogVisible: false,
        visible: false,
        refundPaymentBusinessTypeList: [],
        withdrawalAuditStatus: [
          { value: 0, label: '申请中' },
          { value: 1, label: '提现处理中' },
          { value: 2, label: '拒绝' },
          { value: 3, label: '提现成功' },
          { value: 4, label: '提现申请失败' },
          { value: 5, label: '打款中' },
        ],
        accountType: [
          {
            label: '厦门银行存管',
            value: 'xm',
          },
        ],
        search: {},
        showBtn: false,
        isFirst: true,
        options: {
          loading: false,
          border: true,
          mutiSelect: false,
        },
        pagination: {
          pageSize: 1,
          pageLimit: 10,
          total: null,
        },
        selectArr: [], // 选择的列表数据
        list: [],
        id: '',
        name: 'chenxuelei',
        type: '',
        auditType: '',
        columns: [
          {
            prop: 'id',
            label: '编号',
          },
          {
            prop: 'memberId',
            label: '用户ID',
          },
          {
            prop: 'applyOrderNo',
            label: '平台订单号',
          },
          {
            prop: 'umobile',
            label: '开户手机号',
          },
          {
            prop: 'cardHolder',
            label: '申请人',
          },
          {
            prop: 'amount',
            label: '提现金额',
          },
          {
            prop: 'deposit',
            label: '开户行',
          },
          {
            prop: 'bankNo',
            label: '银行卡号',
          },
          {
            prop: 'countryCode',
            label: '国家',
          },
          {
            prop: 'statusMsg',
            label: '提现状态',
          },
          {
            prop: 'approvers',
            label: '审核人',
          },
          {
            prop: 'createdAt',
            label: '创建时间',
          },
          {
            prop: 'operation',
            label: '操作',
            width: '100px',
            fixed: 'right',
            scopedSlots: { customRender: 'operation' },
          },
        ],
      };
    },

    created() {
      this.getSelectorsList();
    },
    methods: {
      getSelectorsList() {
        getSelectors({}).then(data => {
          if (!data.err) {
            let result = data.res;
            for (let index = 0; index < result.length; index++) {
              let element = result[index];
              if (element.bizType == 'countryCodes') {
                this.countryCodes = [{ key: '全部', value: '' }].concat(
                  element.selectors,
                );
              }
            }
          }
        });
      },
      dealAction(flag, item) {
        this.dialogVisible = true;
        this.id = item.id;
        if (flag) {
          this.atitle = '确定驳回该条申请吗？';
          this.auditType = 'audit';
        } else {
          this.atitle = '确定驳回吗';
          this.auditType = 'reject';
        }
      },

      auditInfo(falg) {
        if (this.auditType == 'audit') {
          let params = {};
          params.ids = this.id;
          params.name = store.state.user.username;
          auditWithdrawl(params).then(data => {
            let result = data;
            if (!result.err) {
              this.$message({
                message: result.res,
                type: 'success',
              });
              this.getList(true);
            } else {
              this.$message({
                message: '操作失败' + result.msg,
                type: 'warning',
              });
            }
          });
        } else {
          let params = {};
          params.id = this.id;
          rejectWithdrawl(params).then(data => {
            let result = data.res;
            if (!data.err) {
              this.$message({
                message: '操作成功',
                type: 'success',
              });
            }
          });
        }
        this.auditType = '';
        this.dialogVisible = false;
        this.getList();
      },

      handleClose(done) {
        this.$confirm('确定驳回这条申请吗？')
          .then(_ => {})
          .catch(_ => {});
      },
      onSearch(e) {
        this.search = e;
        this.getList(true);
      },
      getParams() {
        const status = this.search.status;
        const allStatus = [0, 1, 2, 3, 5]; // 申请列表全部状态
        const params = {
          ...this.search,
          status: status !== undefined ? status : '',
          pageNo: this.pagination.pageSize,
          limit: this.pagination.pageLimit,
        };
        if (this.search.idCode) {
          params.memberId = this.search.idCode;
        }
        return params;
      },
      getList(isSearch = false) {
        if (isSearch) {
          this.pagination.pageSize = 1;
        }
        const params = this.getParams();
        this.options.loading = true;
        try {
          withdrawalAuditApplicantList(params).then(data => {
            this.options.loading = false;
            if (!data.err) {
              let result = data.res.data;
              this.options.loading = false;
              this.list = result && result.records ? result.records : [];
              this.pagination.total = result ? result.total : 0;
            }
          });
        } catch (err) {
          this.options.loading = false;
        }
      },
      handleSee(val, key) {
        const obj = {
          ...val,
          accountType:
            val.accountType === 1
              ? '人民币'
              : val.accountType === 2
              ? '澳币'
              : '人民币老货款',
          businessType: businessTypeText[val.businessType - 1],
          status:
            val.status === 2
              ? val.reviewAt
                ? '复审驳回'
                : '初审驳回'
              : statusText[val.status],
        };
        this.showBtn = false;
        this.isFirst = key === 1 ? false : true;
        this.id = val.id;
        let arr;
        if (val.businessType === 1) {
          arr = formatDetail(obj, key ? true : false).refundPaymentDetails;
        } else {
          arr = formatDetail(obj, key ? true : false)[
            `refundPaymentDetails${val.businessType - 1}`
          ];
        }

        if (key) {
          this.showBtn = true;
        }

        this.$refs['seeModal'].open({
          arr,
          title: '退货款审核',
        });
      },
      async onOK(params) {
        const saveParams = {
          id: this.id,
          reason: params.refuseToReason,
          status: this.isFirst
            ? params.type == 1
              ? 5
              : 2
            : params.type == 1
            ? 1
            : 2,
        };
        await returnPaymentApplicantAudit(saveParams);
        this.$message({
          message: '操作成功',
          type: 'success',
        });
        this.getList();
      },
      getStatusArr() {
        return {
          isOperation: status => {
            return this.selectArr.every(item => {
              return item.status === status;
            });
          },
          getIds: status => {
            return this.selectArr
              .map(item => {
                if (item.status !== status) {
                  return item.id;
                }
              })
              .filter(i => !!i);
          },
        };
      },

      onImport(response) {
        importStatus({ fileId: response.id }).then(data => {
          console.log(data);
          if (!data.err) {
            this.$message({
              message: '操作成功',
              type: 'success',
            });
          }
        });
      },
      async handleBatch(key, id) {
        if (!id && this.selectArr.length === 0) {
          return this.$message.error('请选择订单');
        }
        const getStatusArr = this.getStatusArr();

        if (
          (key === 'first' && !getStatusArr.isOperation(0)) ||
          (key === 'tow' && !getStatusArr.isOperation(5))
        ) {
          const errArr =
            key === 'first' ? getStatusArr.getIds(0) : getStatusArr.getIds(5);
          this.$message.error(
            `${errArr}该笔退款申请记录状态已经改变，目前无需审核`,
          );
          return;
        }
        const ids = this.selectArr.map(item => item.id);
        const params = {
          status: key === 'first' ? 5 : 1,
          ids,
        };
        await returnPaymentApplicantBatchAuditPass(params);
        this.$message({
          message: '操作成功',
          type: 'success',
        });
        this.getList();
      },
      handleSelectionChange(val) {
        this.selectArr = val;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
