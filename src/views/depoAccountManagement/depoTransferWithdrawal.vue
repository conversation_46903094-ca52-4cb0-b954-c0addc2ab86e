<!--
 * @Description: 
 * @Author: wuqingsong
 * @Date: 2022-08-09 16:30:38
 * @LastEditTime: 2022-11-01 14:27:21
 * @LastEditors: Please set LastEditors
 * @Reference: 
-->
<template>
  <div>
    <div>
      <el-form ref="form" :model="form" label-width="80px" inline>
        <el-form-item label="手机号:" prop="mobile">
          <el-input v-model="form.mobile" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div style="float: right; margin-bottom: 15px">
      <ac-permission-button
        btn-text="查询"
        permission-key="overseasWithdrawalPay-search"
        @click="search(1)"
      ></ac-permission-button>
      <el-button type="primary" @click="reset">重置</el-button>
    </div>
    <div>
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
      >
        <el-table-column
          align="center"
          prop="memberId"
          label="用户id"
          min-width="100"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="depoBalance"
          label="货款户余额"
          min-width="100"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="cashBalance"
          label="可提现户余额"
          min-width="100"
        ></el-table-column>
        <el-table-column
          min-width="100"
          max-width="300"
          fixed="right"
          label="操作"
        >
          <template slot-scope="{ row }">
            <ac-permission-button
              slot="reference"
              type="text"
              size="small"
              btn-text="可提现转货款余额"
              permission-key="cashTransferDepo-make-payment"
              @click="cashTransferDepo(row, 1)"
            ></ac-permission-button>
          </template>
        </el-table-column>
      </el-table>
      <el-dialog
        title="可提现转货款"
        :visible.sync="cashTransferDepoDialog"
        width="400px"
        @closed="onClose"
      >
        <el-form
          ref="cashTransferForm"
          label-width="120px"
          :model="cashTransferForm"
          :rules="cashTransferRules"
        >
          <el-form-item label="转账金额" prop="amount">
            <el-input v-model.number="cashTransferForm.amount">
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
          <el-form-item label="备注信息" prop="remark">
            <el-input
              v-model="cashTransferForm.remark"
              placeholder="备注信息"
            />
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button @click="cashTransferDepoDialog = false">取 消</el-button>
          <el-button :loading="loading" type="primary" @click="confirm">
            确 定
          </el-button>
        </div>
      </el-dialog>
      <el-pagination
        :current-page="pageNum"
        :page-sizes="[10, 20, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>
<script>
  import { getAccounts, cashTransferDepo } from '@/api/depoAccountManagement';

  export default {
    data() {
      return {
        form: {
          mobile: null,
        },
        cashTransferForm: {
          accountId: '',
          amount: '',
          remark: '',
        },
        cashTransferRules: {
          amount: [
            { required: true, message: '金额不能为空' },
            { type: 'number', message: '金额必须为数字' },
          ],
          remark: [{ required: true, message: '请输入', trigger: 'blur' }],
        },
        cashTransferDepoDialog: false,
        tradeTypeList: [], // 状态下拉框
        tableData: [],
        loading: false, // loading表格加载层
        pageNo: 1,
        pageNum: 1,
        pageSize: 10,
        total: 1,
      };
    },
    created() {},
    methods: {
      async fetch() {
        if (!this.form.mobile) {
          return this.$message.error('请输入手机号');
        }
        const data = {
          mobile: this.form.mobile,
        };
        this.loading = true;
        try {
          const { res } = await getAccounts(data);
          if (res) {
            this.tableData = res.records;
            this.total = res.total;
            this.loading = false;
          } else {
            this.loading = false;
          }
        } catch (error) {
          this.loading = false;
        }
      },
      search() {
        this.pageNum = 1;
        this.fetch();
      },
      //重置
      reset() {
        this.$refs['form'].resetFields();
      },

      cashTransferDepo(row) {
        this.cashTransferDepoDialog = true;
        this.cashTransferForm.accountId = row.id;
      },
      handleSizeChange(val) {
        this.pageNum = 1;
        this.pageSize = val;
        this.fetch();
      },
      handleCurrentChange(val) {
        this.pageNum = val;
        this.fetch();
      },
      onClose() {
        this.cashTransferDepoDialog = false;
        this.$refs.cashTransferForm.resetFields();
        this.cashTransferForm.accountId = null;
      },
      confirm() {
        this.$refs['cashTransferForm'].validate(async valid => {
          if (valid) {
            let data = { ...this.cashTransferForm };
            const { res, err } = await cashTransferDepo(data);
            if (!err) {
              this.$message.success('操作成功');
              this.cashTransferDepoDialog = false;
              this.fetch();
              this.$refs.cashTransferForm.resetFields();
              this.cashTransferForm.accountId = null;
            }
          }
        });
      },
    },
  };
</script>
