<!--
 * @Description: 
 * @Author: wuqingsong
 * @Date: 2022-08-09 16:30:38
 * @LastEditTime: 2022-09-13 19:30:56
 * @LastEditors: Please set LastEditors
 * @Reference: 
-->
<template>
  <div>
    <el-form :inline="true" :model="params">
      <el-form-item label="手机号码:">
        <el-input v-model="params.mobile" placeholder="请输入手机号码" />
      </el-form-item>
      <el-form-item label="业务类型:">
        <el-select v-model="params.type" clearable placeholder="请选择">
          <el-option
            v-for="(item, index) in tradeTypeList"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间:">
        <el-date-picker
          v-model="params.detailTime"
          type="datetimerange"
          range-separator="至"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <ac-permission-button
          btn-text="查询"
          permission-key=""
          @click="search(true)"
        ></ac-permission-button>
        <el-button type="primary" @click="reset">重置</el-button>
        <ac-permission-button
          btn-text="导出"
          permission-key=""
          @click="doExport()"
        ></ac-permission-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column
        align="center"
        prop="id"
        label="流水id"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="memberId"
        label="用户id"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="money"
        label="金额"
        min-width="100"
      ></el-table-column>

      <el-table-column
        align="center"
        prop="preBalance"
        label="操作前余额"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="balance"
        label="操作后余额"
        min-width="100"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="goodsInfo"
        label="其它信息"
        min-width="250"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="tradeName"
        label="类型"
        min-width="150"
      ></el-table-column>
      <el-table-column
        align="center"
        prop="createdAt"
        label="操作时间"
        min-width="160"
      ></el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page="pageNum"
      :page-size="pageSize"
      :page-sizes="[10, 20, 30, 50, 100]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>
<script>
  import { setInitData, downloadFile } from '@/utils';
  import {
    getWithdrawalflowTypeList,
    getWithdrawalflows,
    exportWithdrawalflows,
  } from '@/api/depoAccountManagement';
  import { exportExcel } from '@/api/blob';

  export default {
    data() {
      return {
        params: {
          mobile: '',
          tradeType: '',
          startTimeStrFor: null, // 流水开始时间
          endTimeStrFor: '', // 流水结束时间
        },
        tradeTypeList: [], // 状态下拉框
        tableData: [],
        loading: false, // loading表格加载层
        pageNo: 1,
        limit: 10,
        pageNum: 1,
        pageSize: 10,
        total: 0,
      };
    },
    created() {
      this.initParams();
      this.getEnums();
    },
    methods: {
      initParams() {
        if (!this.$route.query) {
          return;
        }
        const { mobile = '' } = this.$route.query;
        this.params.mobile = mobile;
        this.fetch();
      },
      async getEnums() {
        const result = await getWithdrawalflowTypeList();
        if (result) {
          this.tradeTypeList = result.res || [];
        }
      },
      async fetch() {
        if (this.params.detailTime && this.params.detailTime.length > 0) {
          this.params.startTimeStrFor = this.params.detailTime[0];
          this.params.endTimeStrFor = this.params.detailTime[1];
        } else {
          this.params.startTimeStrFor = null;
          this.params.endTimeStrFor = null;
        }
        let params = { ...this.params };
        const data = {
          ...this.params,
          pageNo: this.pageNum,
          limit: this.pageSize,
        };
        delete data.detailTime;
        this.loading = true;
        try {
          const res = await getWithdrawalflows(data);
          if (res) {
            this.tableData = res.res.records;
            this.total = res.res.total;
            this.loading = false;
          } else {
            this.loading = false;
          }
        } catch (error) {
          this.loading = false;
        }
      },
      search() {
        this.pageNum = 1;
        this.fetch();
      },
      reset() {
        Object.assign(this.params, this.$options.data.call(this).params);
        this.pageNum = 1;
        this.pageSize = 10;
      },
      doExport() {
        if (this.params.detailTime && this.params.detailTime.length > 0) {
          this.params.startTimeStrFor = this.params.detailTime[0];
          this.params.endTimeStrFor = this.params.detailTime[1];
        } else {
          this.params.startTimeStrFor = null;
          this.params.endTime = null;
        }
        let params = { ...this.params };
        params.tradeType = this.params.type;
        delete params.detailTime;
        const exportApi = '/withdrawalflow/export';
        exportExcel(params, `/api/account-core/${exportApi}`, 'get').then(
          res => {
            if (res) {
              downloadFile(res.data, '可提现货款流水', 'csv');
            } else {
              //
              this.$message.error('暂无数据');
            }
          },
        );
      },
      handleSizeChange(val) {
        this.pageNum = 1;
        this.pageSize = val;
        this.fetch();
      },
      handleCurrentChange(val) {
        this.pageNum = val;
        this.fetch();
      },
    },
  };
</script>
<style lang="scss"></style>
