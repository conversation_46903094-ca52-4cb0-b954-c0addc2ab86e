/*
 * @Description:
 * @Author: bruce
 * @Date: 2021-03-10 14:18:00
 * @LastEditTime: 2022-11-18 16:27:52
 * @LastEditors: dddd
 * @Reference:
 */
import Vue from 'vue';
import App from './App';
import permission from './utils/permission';
import i18n from './i18n';
import store from './store';
import router from './router';
import * as lodash from 'lodash';
import '@/amf';
import '@/components';
import req from '@/utils/newRequest';
import { utils } from '@access/core';
import log from '@/utils/log.js';
const { slsSdk } = utils;
const packages = require('../package.json');
slsSdk.slsSdkInit(process.env.VUE_APP_BUILD_MODE, packages.name);

/* 埋点方法 */
Vue.prototype.$slsSdk = slsSdk;
/* 埋点方法 */
Vue.prototype.$log = log;
/* 全局api */
Vue.prototype.$req = req;
/* 全局lodash */
Vue.prototype.$baseLodash = lodash;
/* 全局按钮权限 */
Vue.prototype.$btnPermission = permission.btnPermission;
/* 全局事件总线 */
Vue.prototype.$baseEventBus = new Vue();

import directives from '@/directives/index.js';
Vue.use(directives);

Vue.config.productionTip = false;

new Vue({
  el: '#amf-fe',
  i18n,
  store,
  router,
  render: h => h(App),
});
