{"name": "access-fmis-web", "version": "2.1.3", "author": "Access Frontend Team", "license": "Mozilla Public License Version 2.0", "private": true, "scripts": {"serve": "cross-env BUILD_MODE=dev access-cli serve", "serve:dev": "cross-env BUILD_MODE=dev access-cli serve", "serve:test": "cross-env BUILD_MODE=test access-cli serve", "serve:pre": "cross-env BUILD_MODE=pre access-cli serve", "build": "cross-env BUILD_MODE=pro access-cli build", "build:dev": "cross-env BUILD_MODE=dev access-cli build", "build:test": "cross-env BUILD_MODE=test access-cli build", "build:pre": "cross-env BUILD_MODE=pre access-cli build", "build:report": "access-cli build --report", "lint": "access-cli lint", "lint:style": "stylelint **/*.{vue,scss} --fix", "inspect": "access-cli inspect", "template": "plop", "git": "git add . && git commit -m \"home laptop\" -n && git push -u origin master", "clear": "npm cache clean -f&&rimraf node_modules&&npm i&&cnpm i image-webpack-loader -D"}, "repository": {"type": "git", "url": "git+https://git.acg.team/111/111-pro.git"}, "dependencies": {"@access/amf-icons": "0.0.1", "@access/components": "0.7.57", "@access/core": "1.3.14", "@antv/g6": "4.8.13", "@sentry/tracing": "6.3.6", "@sentry/vue": "6.3.6", "axios": "0.21.1", "clipboard": "2.0.8", "core-js": "3.9.0", "dayjs": "1.10.4", "echarts": "4.9.0", "element-ui": "2.15.6", "file-saver": "2.0.2", "js-base64": "3.6.0", "js-big-decimal": "^2.0.7", "js-cookie": "2.2.1", "jsencrypt": "3.0.1", "jsonlint": "1.6.3", "jsplumb": "2.15.5", "lodash": "4.17.20", "mammoth": "1.4.16", "mockjs": "1.1.0", "nprogress": "0.2.0", "pug": "3.0.0", "pug-plain-loader": "1.0.0", "qs": "6.9.6", "remixicon": "2.5.0", "resize-detector": "0.2.2", "screenfull": "5.1.0", "vue": "2.6.12", "vue-echarts": "4.1.0", "vue-i18n": "8.22.4", "vue-json-viewer": "2.2.22", "vue-router": "3.5.1", "vuedraggable": "2.24.1", "vuex": "3.6.2", "xlsx": "0.16.7"}, "devDependencies": {"@access/cli": "0.0.6", "@babel/eslint-parser": "7.12.17", "@prettier/plugin-pug": "1.16.1", "@vue/cli-plugin-babel": "4.5.13", "@vue/cli-plugin-eslint": "4.5.13", "@vue/cli-plugin-router": "4.5.13", "@vue/cli-plugin-vuex": "4.5.13", "@vue/cli-service": "4.6.13-optimize-1", "@vue/eslint-config-prettier": "6.0.0", "body-parser": "1.19.0", "chalk": "4.1.0", "chokidar": "3.5.1", "compression-webpack-plugin": "7.1.2", "cross-env": "7.0.3", "eslint": "7.20.0", "eslint-plugin-prettier": "3.3.1", "eslint-plugin-vue": "7.6.0", "file-saver": "2.0.5", "filemanager-webpack-plugin": "3.1.0", "lint-staged": "10.5.4", "node-sass": "^4.14.1", "plop": "2.7.4", "prettier": "2.2.1", "raw-loader": "4.0.2", "sass-loader": "10.1.1", "stylelint": "13.10.0", "stylelint-config-prettier": "8.0.2", "stylelint-config-recess-order": "2.3.0", "svg-sprite-loader": "5.2.1", "vue-eslint-parser": "7.5.0", "vue-plugin-rely": "2.6.13", "vue-template-compiler": "2.6.12", "webpackbar": "5.0.0-3"}, "gitHooks": {"pre-commit": "lint-staged", "commit-msg": "node src/config/verifyCommitMsg.js"}, "lint-staged": {"*.{js,jsx,vue}": ["vue-cli-service lint", "git add"]}, "eslintConfig": {"env": {"node": true}, "extends": ["plugin:vue/recommended", "@vue/prettier"], "rules": {"no-console": "off", "no-debugger": "off", "vue/no-v-html": "off", "vue/no-lone-template": "off", "vue/no-mutating-props": "off"}, "parserOptions": {"parser": "@babel/eslint-parser", "ecmaVersion": 2020, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "overrides": [{"files": ["**/__tests__/*.{j,t}s?(x)", "**/tests/unit/**/*.spec.{j,t}s?(x)"], "env": {"jest": true}}]}, "eslintIgnore": ["src/assets", "src/icons", "public", "dist"], "prettier": {"tabWidth": 2, "useTabs": false, "semi": true, "singleQuote": true, "quoteProps": "as-needed", "jsxSingleQuote": false, "trailingComma": "all", "bracketSpacing": true, "jsxBracketSameLine": false, "arrowParens": "avoid", "htmlWhitespaceSensitivity": "ignore", "vueIndentScriptAndStyle": true, "endOfLine": "lf", "printWidth": 80, "pugAttributeSeparator": "none"}, "stylelint": {"extends": ["stylelint-config-recess-order", "stylelint-config-prettier"]}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "babel": {"presets": ["@vue/cli-plugin-babel/preset"], "env": {"development": {"plugins": ["dynamic-import-node"]}}}}