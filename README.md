<div align="center"><img width="200" src="https://material-admin-web.danchuangglobal.com/static/img/logo.367ebb0a.png"/>
<h1> access-ms-frame</h1>
</div>

## 公告

```diff
- 代码泄露者，斩。
```

## 🔗 链接

- 💻 演示地址：[AMF](https://amf-test.danchuangglobal.com/)


### 常规版(master)使用

```bash
# 克隆项目
git clone
# 进入项目目录
cd access-ms-frame
# 安装依赖
npm i
# 本地开发 启动项目
npm run serve
```

### 其他版本下载

`${branch_name}`替换成上表分支名即可

```bash
# 克隆项目
git clone -b ${branch_name}
# 进入项目目录
cd access-ms-frame
# 安装依赖
npm i
# 本地开发 启动项目
npm run serve
```

## webstorm看这里
`WebStorm preferences -> Settings -> Language & Framework -> JavaScript -> Webpack` 选择 `node_modules`文件夹 `@vue/cli-service/webpack.config.js` 文件即可

## ENV 文件
ENV文件已经收敛，相关变量在`src/config/env.config.js`文件中配置。根据环境区分。

## 开启CDN
编辑文件 `src/config/cdn.config.js` 修改 `enabledCDN` 为 `true` 即可。
要启用该配置项目需要在git组`Access Admin`下面。其它组下的项目想要开启需在项目下面配置 `CI/CD - Varables`，在其中添加`WEBPACK_CDN_PLUGIN_ACCESS_KEY_ID`以及`WEBPACK_CDN_PLUGIN_ACCESS_KEY_SECRET`这两个变量，对应OBS的AK和SK。开启后仅在线上环境会上传。